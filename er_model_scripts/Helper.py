import os
import sys
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))
from Imports import *
config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = DataProcessor()

class Helpers():

    def __init__(self):
        self.index_sg_map = config['mappings']['index_sg_map']

        self.schedular_map = config['mappings']['schedular_map']

        self.metrics_period_map  = config['mappings']['metrics_period_map']
                            

    def create_logger(self,log_file_path):
        log_dir = os.path.dirname(log_file_path)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)  # Create the directory if it doesn't exist
        logger = logging.getLogger('my_logger')
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            logger.removeHandler(handler)
        file_handler = logging.FileHandler(log_file_path, mode='w')  # Use mode='w' for write mode
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger
    
    def get_isin_df(self, tag, model_type, schedular, masters_isin_list, logger):
        logger.info("Getting ISIN List for Predictions")
        try:
            schedular_key = self.schedular_map.get(schedular)
            # Construct the key dynamically
            config_key = f"er_folder_{model_type}_{schedular_key}_{tag}"
            
            if config_key not in config['er_model_index_path']:
                raise ValueError(f"Invalid config combination: {config_key}")
            
            folder_path = config['er_model_index_path'][config_key]

            bucket_name = config['er_model_index_path']['bucket_name_lightgbm']

            # Load training ISINs
            training_df = conn.s3_client.read_as_dataframe(
                bucket_name,
                f"{folder_path}/{config['er_model_index_path']['trained_isins_file']}"
            )
            training_list = training_df["ISIN"].values.tolist()

            # Load q_data
            q_data = conn.s3_client.read_as_dataframe(
                bucket_name,
                f"{folder_path}/{config['er_model_index_path']['q_file_path']}"
            )
            q_data = q_data[q_data['status'] == 'completed']
            ts_dict = dict(zip(q_data['isin'], q_data['timestamp']))

            # Final ISIN list
            no_models_list = [i for i in masters_isin_list if i not in training_list ]
            # isin_list = [i for i in masters_isin_list if i in training_list]
            isin_df = pd.DataFrame(masters_isin_list, columns=['isin'])

            logger.info("Everything Done, ISIN List Fetched.")
            return no_models_list, isin_df, ts_dict, q_data

        except Exception as e:
            logger.error(f"Error in get_isin_df: {e}", exc_info=True)
            raise

    def get_adhoc_data(self, last_date_needed, adhoc_date_needed, isin_list,model_type,schedular, logger):
        logger.info("Getting Historical Data for Ffill")
        index_name = config['er_model_index_path'][f'{model_type}_pred_index']
        try:
            adhoc = conn.get_es_data_all(
                last_date_needed, 
                adhoc_date_needed,
                index_name,
                schedular,
                isin_list
            )
            adhoc = adhoc.set_index('isin').groupby(level=0).ffill().reset_index()
            adhoc = adhoc[adhoc['date'] == adhoc_date_needed]
            adhoc['date'] = pd.to_datetime(adhoc['date'])
            logger.info("Historical Data Collected and Returned.")    
            return adhoc

        except Exception as e:
            logger.error(f"Error while getting adhoc data: {e}", exc_info=True)
            return pd.DataFrame()




    def get_information_model_data(self, tag, isin_df, formatted_date_run, columns_info, schedular,model_type, logger):
        logger.info("Data Collection Started for Information Model")
        
        try:
            results = isin_df.parallel_apply(
                lambda x: conn.get_values_from_es(
                    x['isin'],
                    formatted_date_run,
                    f"{config['er_model_index_path']['information_model_index']}",
                    schedular
                ),
                axis=1
            )
            df_info = pd.concat(results.tolist(), axis=0)
            df_info.rename(columns={'predictions':'prediction_info'},inplace=True)

        except Exception as e:
            logger.error(f"Error during parallel apply or concatenation: {e}")
            return
            # return pd.DataFrame(columns=columns_info)

        try:
            df_info.drop(['pos_senti_comp', 'neg_senti_comp'], axis=1, inplace=True)
        except Exception as e:
            logger.warning(f"Drop sentiment columns warning (may not exist): {e}")

        try:
            df_info.rename(columns={
                'overall_pos_sentiment_general': 'pos_senti_comp',
                'overall_neg_sentiment_general': 'neg_senti_comp'
            }, inplace=True)
        except Exception as e:
            logger.warning(f"Renaming general sentiment columns failed: {e}")

        if 'pos_senti_ind' not in df_info.columns:
            try:
                df_info.rename(columns={
                    'pos_sentiment_industry': 'pos_senti_ind',
                    'neg_sentiment_industry': 'neg_senti_ind'
                }, inplace=True)
            except Exception as e:
                logger.warning(f"Renaming industry sentiment columns failed: {e}")

        df_info['date'] = pd.to_datetime(df_info['date'], errors='coerce')

        try:
            df_info = df_info.reindex(columns=columns_info)
        except Exception as e:
            logger.error(f"Reindexing to match columns_info failed: {e}")

        if tag == 'aieq' and model_type!='lgb':
            try:
                df_gserp = conn.s3_client.read_as_dataframe(
                    config['er_model_index_path']['bucket_info'],
                    f"{config['er_model_index_path']['gserp_path']}/{formatted_date_run}.csv"
                )
                df_gserp['date'] = pd.to_datetime(df_gserp['date'], errors='coerce')
                df_info = df_info.merge(df_gserp, on=['isin', 'date'], how='left')
            except Exception as e:
                logger.error(f"Information Model Gserp Exception: {e}")
                df_gserp = pd.DataFrame(columns=['date', 'gserp_pos_senti_comp', 'gserp_neg_senti_comp'])
                df_gserp['date'] = pd.to_datetime([formatted_date_run])
                df_info = df_info.merge(df_gserp, on=['date'], how='left')

        logger.info("Information Data Collected and Returned.")
        return df_info




    def get_financial_model_data(self, tag, isin_df, formatted_date_run, columns_finance,schedular, logger):
        logger.info("Data Collection Started for Financial Model")
        
        try:
            results = isin_df.parallel_apply(
                lambda x: conn.get_values_from_es(
                    x['isin'],
                    formatted_date_run,
                    f"{config['er_model_index_path']['financial_model_index']}",
                    schedular
                ),
                axis=1
            )
            df_finance = pd.concat(results.tolist(), axis=0)
            df_finance.rename(columns={'predictions':'predictions_f'},inplace=True)

        except Exception as e:
            logger.error(f"Error during ES data fetch or concatenation: {e}")
            return 
            # return pd.DataFrame(columns=columns_finance)

        try:
            df_finance['date'] = pd.to_datetime(df_finance['date'], errors='coerce')
        except Exception as e:
            logger.warning(f"Couldn't convert 'date' to datetime: {e}")

        try:
            df_finance = df_finance.reindex(columns=columns_finance)
        except Exception as e:
            logger.error(f"Reindexing to match columns_finance failed: {e}")

        logger.info("Financial Data Collected and Returned.")
        return df_finance




    def get_management_model_data(self, tag, isin_df, formatted_date_run, columns_management, logger):
        logger.info("Data Collection Started for Management Model")
        
        try:
            results = isin_df.parallel_apply(
                lambda x: conn.get_values_from_es(
                    x['isin'],
                    formatted_date_run,
                    f"{config['er_model_index_path']['management_model_index']}",
                    f"{config['er_model_index_path']['schedular_1']}"
                ),
                axis=1
            )
            df_management = pd.concat(results.tolist(), axis=0)
            df_management.rename(columns={'predictions':'predictions_m'},inplace=True)
        except Exception as e:
            logger.error(f"Error during ES data fetch or concatenation: {e}")
            return 
            # return pd.DataFrame(columns=columns_finance)

        try:
            df_management['date'] = pd.to_datetime(df_management['date'], errors='coerce')
        except Exception as e:
            logger.warning(f"Couldn't convert 'date' to datetime: {e}")

        try:
            df_management = df_management.reindex(columns=columns_management)
        except Exception as e:
            logger.error(f"Reindexing to match columns_management failed: {e}")

        logger.info("Management Data Collected and Returned.")
        return df_management


    def fill_closeprice(self, df_finance, masters, formatted_date_run, logger):
        logger.info("Starting to Fetch the Close Price from API")

        try:
            empty_close_price_isins = df_finance[df_finance['closeprice'].isna()]['isin'].unique()
            empty_cp = pd.DataFrame(empty_close_price_isins, columns=['isin'])

            if len(empty_cp) > 0:
                logger.info(f"Found {len(empty_cp)} ISINs with missing closeprice.")
                try:
                    results_empty_cp = empty_cp.parallel_apply(
                        lambda x: conn.get_stock_price(x['isin'], masters, formatted_date_run),
                        axis=1
                    )
                    df_empty_cp = pd.concat(results_empty_cp.tolist(), ignore_index=True)
                except Exception as e:
                    logger.error(f"Error while fetching stock prices: {e}")
                    return 
                    # return df_finance
                if len(df_empty_cp)==0:
                    return df_finance

                try:
                    df_empty_cp['date'] = pd.to_datetime(df_empty_cp['date'], errors='coerce')
                    df_empty_cp.replace(['Data Unavailable', 'NaN'], np.nan, inplace=True)
                    df_empty_cp.reset_index(drop=True, inplace=True)
                    df_finance['date'] = pd.to_datetime(df_finance['date'], errors='coerce')

                    df_finance.set_index(['isin', 'date'], inplace=True)
                    df_empty_cp.set_index(['isin', 'date'], inplace=True)
                    df_finance.update(df_empty_cp, overwrite=True)
                    df_finance.reset_index(inplace=True)

                    df_finance.drop_duplicates(subset=['isin'], keep='first', inplace=True)
                except Exception as e:
                    logger.error(f"Error during DataFrame merge/update: {e}")
            else:
                logger.info("All ISINs already have close price. No update needed.")

        except Exception as e:
            logger.error(f"Unexpected error in fill_closeprice: {e}")

        return df_finance



    def get_lstm_data(self, isin_df, formatted_date_run, schedular, logger):
        logger.info("Data Collection Started for LSTM Model")

        try:
            # Fetch data from ES
            results = isin_df.parallel_apply(
                lambda x: conn.get_values_from_es(
                    x['isin'],
                    formatted_date_run,
                    f"{config['er_model_index_path']['lstm_model_index']}",
                    schedular
                ),
                axis=1
            )
            df_lstm_raw = pd.concat(results.tolist(), axis=0)

            # Ensure date is datetime
            df_lstm_raw['date'] = pd.to_datetime(df_lstm_raw['date'], errors='coerce')

            # Merge with ISIN list to preserve ordering and full list
            df_lstm = isin_df.merge(df_lstm_raw, on='isin', how='left')

            # Extract LSTM predictions
            df_lstm = df_lstm[['isin', 'date', 'predictions']]
            if schedular != config['er_model_index_path']['schedular_2']:
                df_lstm[['lstm_day1', 'lstm_day2', 'lstm_day3', 'lstm_day4',
                        'lstm_day5', 'lstm_day6', 'lstm_day7']] = df_lstm['predictions'].apply(
                    lambda x: pd.Series(x[:7] + [float('nan')] * (7 - len(x))) if isinstance(x, list) else pd.Series([float('nan')] * 7)
                )
            else:
                df_lstm['predictions'] = df_lstm['predictions'].apply(lambda x: eval(str(x)) if not pd.isna(x) else np.nan)
                df_lstm['lstm_day1'] = df_lstm['predictions'].apply(lambda x: x[0] if not pd.isna(x) and len(x) > 0 else np.nan)


            df_lstm.drop(columns=['predictions'], inplace=True)
            logger.info("LSTM Data Collected and Returned.")

        except Exception as e:
            logger.error(f"Error in get_lstm_data: {e}")
            return 

        return df_lstm




    def get_macro_data(self, masters, formatted_date_run, logger):
        logger.info("Data Collection Started for Macro Model")

        try:
            # Read country mapping from S3
            code_mapping = conn.s3_client.read_as_dataframe(
                config['er_model_index_path']['bucket_name_lightgbm'],
                config['er_model_index_path']['macro_mapping_file']
            )
            code_map_dict = dict(zip(code_mapping['country_code'], code_mapping['country_to_use']))
            macro_df = pd.DataFrame(masters['country_code'].unique(), columns=['country_code'])
            macro_df['proxy_code'] = macro_df['country_code'].map(code_map_dict)
            macro_df.rename(columns={'country_code': 'code'}, inplace=True)
        except Exception as e:
            logger.error(f"Error in reading Macro Mapping File: {e}")
            return
            # return pd.DataFrame(columns=['isin', 'date', 'predictions_macro'])

        try:
            results = macro_df.parallel_apply(
                lambda x: conn.get_values_from_es(
                    str(x['proxy_code']) + '_Equity',
                    formatted_date_run,
                    config['er_model_index_path']['macro_model_index'],
                    config['er_model_index_path']['schedular_1'].lower()
                ),
                axis=1
            )
            df_macro_code_pred = pd.concat(results.tolist(), axis=0)
        except Exception as e:
            logger.error(f"Error while fetching macro data from ES: {e}")
            return 
            # return pd.DataFrame(columns=['isin', 'date', 'predictions_macro'])

        try:
            df_macro_code_pred['date'] = pd.to_datetime(df_macro_code_pred['date'], errors='coerce')
            df_macro_code_pred = df_macro_code_pred[['date', 'country_code', 'predictions']]
            df_macro_code_pred.rename(columns={'predictions': 'predictions_macro'}, inplace=True)
            df_macro_code_pred.drop_duplicates(subset=['country_code'], inplace=True)
            df_macro_code_pred.reset_index(drop=True, inplace=True)

            # Merge code mapping with prediction
            df_macro_code_pred = macro_df.merge(
                df_macro_code_pred, left_on='proxy_code', right_on='country_code', how='left'
            )
            df_macro_code_pred = df_macro_code_pred[['code', 'date', 'predictions_macro']]

            # Merge with master ISIN list
            df_macro = pd.merge(
                masters[['isin', 'country_code']],
                df_macro_code_pred,
                left_on='country_code',
                right_on='code',
                how='left'
            )
            df_macro.drop(['code', 'country_code'], axis=1, inplace=True)

        except Exception as e:
            logger.error(f"Error while processing macro data: {e}")
            return 
            # return pd.DataFrame(columns=['isin', 'date', 'predictions_macro'])
        logger.info("Macro Data Collected and Returned.")

        return df_macro



    def get_sector_gain_data(self, tag, start_date, end_date, logger):
        try:
            index_sg = self.index_sg_map.get(tag)
            start_date = pd.to_datetime(start_date).date()
            end_date = pd.to_datetime(end_date).date()
            start_year, end_year = start_date.year, end_date.year

            q_total = f'''{{"query": {{"bool": {{"must": [{{"range": {{"date": {{"gte": "{start_date.strftime('%Y-%m-%d')}", "lte": "{end_date.strftime('%Y-%m-%d')}", "format": "yyyy-MM-dd"}}}}}}]}}}}}}'''
            
            data = []
            for year in range(start_year, end_year + 1):
                try:
                    response, _ = conn.es_client.search_with_pagination(
                        query=json.loads(q_total),
                        index=f"{index_sg}_{year}",
                        paginate=False,
                        strict=False
                    )
                    for hit in response:
                        data.append(hit['_source'])
                except Exception as e:
                    logger.warning(f"Failed to fetch sector gain data for index {index_sg}_{year}: {e}")

            if not data:
                logger.warning("No sector gain data found for the given date range.")
                return 
                # return pd.DataFrame()

            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df.sort_values('date', inplace=True)
            df = df[['ind_code', 'w_weightedAveragePerSector', 'm_weightedAveragePerSector', 'q_weightedAveragePerSector', 'date']]
            df['sector_gain'] = df.apply(
                lambda row: row['w_weightedAveragePerSector'] + row['m_weightedAveragePerSector'] / 4 + row['q_weightedAveragePerSector'] / 12,
                axis=1
            )
            df = df[['date', 'sector_gain', 'ind_code', 'w_weightedAveragePerSector', 'm_weightedAveragePerSector', 'q_weightedAveragePerSector']]
            df = df[df['date'] == pd.to_datetime(end_date)]
            return df

        except Exception as e:
            logger.error(f"Error occurred in get_sector_gain_data: {e}")
            return pd.DataFrame()



    
    def get_sndp_data(self, country, schedular, logger):
        try:
            data = []
            q_total = f'''{{
                "query": {{
                    "bool": {{
                        "must": [{{
                            "bool": {{
                                "should": [{{"match": {{"country_code": "{country}"}}}}]
                            }}
                        }}]
                    }}
                }}
            }}'''

            start_year = datetime.datetime.now().year - 1
            end_year = datetime.datetime.now().year

            for year in range(start_year, end_year + 1):
                try:
                    result = conn.es_client.run_query(query=json.loads(q_total), index=f"eq_market_sentiment_{year}")
                    for rs in result['hits']['hits']:
                        data.append(rs['_source'])
                except Exception as e:
                    logger.warning(f"Failed to fetch SNDP data for {country} in year {year}: {e}")

            if not data:
                logger.warning(f"No SNDP data found for {country}")
                return 
                # return pd.DataFrame()

            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df = df[df['country_code'] == country]
            df.sort_values('date', ascending=True, inplace=True)
            df.reset_index(drop=True, inplace=True)

            try:
                df.drop(['s&p_positive_sentiment', 's&p_negative_sentiment'], axis=1, inplace=True)
            except:
                pass

            df.rename(columns={
                'benchmark_positive_sentiment': 's&p_positive_sentiment',
                'benchmark_negative_sentiment': 's&p_negative_sentiment'
            }, inplace=True)

            df = df[['date', 's&p_positive_sentiment', 's&p_negative_sentiment', 'country_code']]
            
            if schedular == config['er_model_index_path']['schedular_2']:
                rolling_period = config['er_model_index_path'].get('period_weekly')
            else:
                rolling_period = config['er_model_index_path'].get('period')

            df['s_and_p_positive_sentiment_rolling_mean'] = df['s&p_positive_sentiment'].rolling(rolling_period).mean()
            df['s_and_p_negative_sentiment_rolling_mean'] = df['s&p_negative_sentiment'].rolling(rolling_period).mean()

            return df[['date', 's_and_p_positive_sentiment_rolling_mean', 's_and_p_negative_sentiment_rolling_mean', 'country_code']].reset_index(drop=True)

        except Exception as e:
            logger.error(f"Error in Market Sentiment Function for {country}: {e}")
            return
            # return pd.DataFrame()



    def get_market_sentiment_df(self, masters, formatted_date_run, logger):
        try:
            code_mapping = conn.s3_client.read_as_dataframe(
                config['er_model_index_path']['bucket_name_lightgbm'],
                config['er_model_index_path']['macro_mapping_file']
            )
            code_map_dict = dict(zip(code_mapping['country_code'], code_mapping['country_to_use']))
            code_map_dict['MCO'] = 'USA'

            snp_df = pd.DataFrame(masters['country_code'].unique(), columns=['country_code'])
            snp_df['proxy_code'] = snp_df['country_code'].map(code_map_dict)
            snp_df.rename(columns={'country_code': 'code'}, inplace=True)

            results = snp_df.parallel_apply(
                lambda x: self.get_sndp_data(x['proxy_code'], config['er_model_index_path']['schedular_1'], logger),
                axis=1
            )
            df_snp_pred = pd.concat(results.tolist(), ignore_index=True)
            df_snp_pred = df_snp_pred[df_snp_pred['date'] == pd.to_datetime(formatted_date_run)]
            df_snp_pred.drop_duplicates(subset=['country_code'], inplace=True, ignore_index=True)

            df_snp_pred = snp_df.merge(df_snp_pred, left_on='proxy_code', right_on='country_code')
            df_snp_pred = df_snp_pred[['code', 'date', 's_and_p_positive_sentiment_rolling_mean', 's_and_p_negative_sentiment_rolling_mean']]

            df_snp = pd.merge(masters[['isin', 'country_code']], df_snp_pred, left_on='country_code', right_on='code')
            df_snp.drop(['code', 'country_code'], axis=1, inplace=True)

            # Optional fallback logic (currently commented)
            # try:
            #     if tag in ['aieq', 'aigo']:
            #         country_values = df_snp_pred[df_snp_pred['code'] == 'USA']
            #     else:
            #         country_values = df_snp_pred[df_snp_pred['code'] == 'IND']
            #     for col in ['s_and_p_positive_sentiment_rolling_mean', 's_and_p_negative_sentiment_rolling_mean']:
            #         nan_rows = df_snp[col].isna()
            #         df_snp.loc[nan_rows, col] = country_values[col].values[0]
            #         df_snp[col].fillna(country_values[col].values[0], inplace=True)
            # except:
            #     pass

            return df_snp

        except Exception as e:
            logger.error(f"Error in get_market_sentiment_df: {e}")
            return 
            # return pd.DataFrame()



    def get_new_features(self,df,formatted_date_run):
        df['date'] = pd.to_datetime(formatted_date_run)
        # Extracting various market-related date features
        df['date_year'] = df['date'].dt.year - 2000             # Year adjusted by subtracting 2000
        df['date_month'] = df['date'].dt.month                  # Month
        df['date_week'] = df['date'].dt.isocalendar().week      # Week of the year (ISO)
        df['date_day'] = df['date'].dt.day                      # Day of the month
        df['date_dayofweek'] = df['date'].dt.dayofweek          # Day of the week (0=Monday, 6=Sunday)
        df['date_dayofyear'] = df['date'].dt.dayofyear          # Day of the year
        df['date_quarter'] = df['date'].dt.quarter              # Quarter of the year
    
        df['date_year_sin'] = np.sin(2 * np.pi * df['date_year'] / 67)
        df['date_month_sin'] = np.sin(2 * np.pi * df['date_month'] / 13)
        df['date_week_sin'] = np.sin(2 * np.pi * df['date_week'] / 53)
        df['date_day_sin'] =  np.sin(2 * np.pi * df['date_day'] / 31)
        df['date_dayofyear_sin'] = np.sin(2 * np.pi * df['date_dayofyear'] / 367)
        
        return df


    def ffill_data_df(self, final_df, adhoc, formatted_date_run, schedular, model_type, tag, logger):
        original_dtypes = final_df.dtypes.to_dict()
        columns_to_fill = [col for col in final_df.columns if col in adhoc.columns and final_df[col].isnull().any()]

        # Merge adhoc values
        merged_df = pd.merge(final_df, adhoc, on='isin', how='left', suffixes=('', '_adhoc'))

        summary_records = []
        schedular_key = self.schedular_map.get(schedular)
        config_key = f"new_mnemonic_folder_{model_type}_{schedular_key}_{tag}"

        for column in columns_to_fill:
            if column not in ['isin', 'date']:
                adhoc_col = f'{column}_adhoc'

                # Mask: original is missing, adhoc is available
                was_missing = merged_df[column].isna()
                can_fill = merged_df[adhoc_col].notna()
                filled_mask = was_missing & can_fill

                # Fill original column
                final_df[column] = merged_df[column].combine_first(merged_df[adhoc_col])

                # Prepare summary data only where original was missing
                temp_df = merged_df.loc[was_missing, ['isin']].copy()
                temp_df['feature'] = column
                temp_df['was_missing'] = True
                temp_df['filled'] = filled_mask[was_missing].values
                temp_df['still_missing'] = ~can_fill[was_missing].values
                summary_records.append(temp_df)

        final_df['date'] = pd.to_datetime(formatted_date_run)

        # Restore original data types
        for column, dtype in original_dtypes.items():
            try:
                final_df[column] = final_df[column].astype(dtype)
            except ValueError:
                logger.info(f"Warning: Could not cast column {column} to {dtype}")

        # Concatenate summary data for all features
        ffill_summary_df = pd.concat(summary_records, ignore_index=True) if summary_records else pd.DataFrame()
        ffill_summary_df['date'] = pd.to_datetime(formatted_date_run)
        conn.s3_client.write_advanced_as_df(ffill_summary_df,config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][config_key]}/ffiled_data_folder/{formatted_date_run}.csv")

        return final_df, ffill_summary_df



    def get_daily_run_pred_quat(self,isin,ts_dict,failed_isins,current_date,model_type,tag,schedular,data,logger):
        try:
            ts = ts_dict[f'{isin}']
            df=data
            df=df[df['isin']==isin]
            df['date']=(current_date-BDay(1)).date()
            schedular_key = self.schedular_map.get(schedular)

            config_key = f"lightgbm_s3_folder_{model_type}_{schedular_key}_{tag}"
            year_model = config['er_model_index_path'][config_key].split('/')[-2]

            bst = conn.read_pickle(config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][f'{config_key}']}/{isin}/{config['model_file_names']['model_name_3']}_{isin}_{ts}.pkl")
            scaler = conn.read_pickle(config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][f'{config_key}']}/{isin}/{config['model_file_names']['scaler']}_{isin}_{ts}.pkl")
            
            train_features = bst.get_booster().feature_names
            
            # Define features to exclude from scaling
            exclude_scaling = ['date_year_sin', 'date_month_sin', 'date_week_sin', 'date_day_sin', 'date_dayofyear_sin']
            scaling_features = [feature for feature in train_features if feature not in exclude_scaling]
    
            # Prepare the final DataFrame for scaling
            final_df = df[scaling_features]
            final_df_scaled_values = scaler.transform(final_df)
            final_scaled_df = pd.DataFrame(final_df_scaled_values, columns=scaling_features)
    
            # Add back the excluded features to the scaled DataFrame
            for feature in exclude_scaling:
                if feature in train_features:
                    final_scaled_df[feature] = df[feature]
    
            # Ensure the feature order matches `train_features`
            final_scaled_df = final_scaled_df[train_features]
    
            # Generate predictions
            predictions = bst.predict(final_scaled_df)
            df['actual_quarterly_return_predictions'] = predictions
            df['model_identifier'] = f"{isin}_{year_model}_{config['mappings']['quarter_name']}_{ts}"
            return df
    
        except Exception as e:
            df = data[data['isin']==isin]
            df['actual_quarterly_return_predictions'] = np.nan
            failed_isins.append((isin,e))
            return df


    def get_daily_run_pred(self,isin,ts_dict,failed_isins,current_date,model_type,tag,schedular,data,logger):
        schedular_key = self.schedular_map.get(schedular)
        s3_template_feature = config['version_control']['s3_versioning_features_filename_template']
        s3_version_feature_filename = s3_template_feature.format(model_type=model_type,schedular=schedular.lower(),date=(current_date-BDay(1)).date())
        try:
            ts = ts_dict[f'{isin}']
            df=data
            df=df[df['isin']==isin]
            df['date']=(current_date-BDay(1)).date()

            schedular_key = self.schedular_map.get(schedular)
            config_key = f"lightgbm_s3_folder_{model_type}_{schedular_key}_{tag}"
            year_model = config['er_model_index_path'][config_key].split('/')[-2]
            
            overall_imp_features = conn.read_pickle(config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][f'{config_key}']}/{isin}/{config['model_file_names']['imp_features']}_{isin}_{ts}.pkl")
            feature_dict = conn.read_pickle(config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][f'{config_key}']}/{isin}/{config['model_file_names']['feature_dict']}_{isin}_{ts}.pkl")
            if model_type=='lgb':
                bst = conn.read_pickle(config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][f'{config_key}']}/{isin}/{config['model_file_names']['model_name_2']}_{isin}_{ts}.pkl")
            else:
                bst = conn.read_pickle(config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][f'{config_key}']}/{isin}/{config['model_file_names']['model_name_1']}_{isin}_{ts}.pkl")
            scaler = conn.read_pickle(config['er_model_index_path']['bucket_name_lightgbm'],f"{config['er_model_index_path'][f'{config_key}']}/{isin}/{config['model_file_names']['scaler']}_{isin}_{ts}.pkl")
        
            final_df=pd.DataFrame()
            for idx,k in enumerate(overall_imp_features):
                # print(feature_dict[k])
                try:
                    final_df[k]=eval(feature_dict[k])
                except:
                    final_df[k]=np.nan
            try:
                engineered_df = final_df.copy()
                engineered_df['isin'] = isin
                engineered_df.apply(lambda row: conn.save_row(row.name, row, config['version_control']['bucket_name'],s3_version_feature_filename ), axis=1)
            except Exception as e:
                logger.error(f"Error in saving the Engineered Features to S3 versioning path: {e}") 
            final_df_scaled_values=scaler.transform(final_df)
            final_scaled_df=pd.DataFrame(final_df_scaled_values,columns=final_df.columns) 
            predictions=bst.predict(final_scaled_df)
            df[f'actual_{schedular.lower()}_return_predictions']=predictions
            df['model_identifier'] = f"{isin}_{year_model}_{config['mappings']['quarter_name']}_{ts}"
            return df

        except Exception as e:
            df = data[data['isin']==isin]
            df[f'actual_{schedular.lower()}_return_predictions'] = np.nan
            failed_isins.append((isin,e))
            return df

    # accuracy calculations
    def accuracy_function(self,df_series, coff = 500):
        return df_series.apply(lambda x: 97/( 1 + 20 * np.exp((-coff/x))) if x <  100 else max(0, 106.7 - 0.213 * x))

    def calculate_accuracy(self,df_data, prediction_col = 'predictions', target_col = 'close_change_monthly', coff = 500):

        if prediction_col not in df_data.columns:
            raise Exception('Prediction column not in Dataframe')

        if target_col not in df_data.columns:
            raise Exception('Target column not in Dataframe')
        
        # Remove any nan's in prediction or target cols
        df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

        # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
        df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
        df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

        # Calculate RMS of MAPE over a rolling of 14 days
        df_data['accuracy_1_day'] = self.accuracy_function(df_data['daily_ape'], coff = coff)

        # Calculate RMS of MAPE over a rolling of 22 days
        df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
        df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5
        
        df_data.drop(columns=['denominator'], inplace=True)
        
        return df_data


    def directionality_score_calc(self,prediction_direction, close_direction):
        directionality_df = pd.DataFrame()
        directionality_df['prediction_direction'] = prediction_direction
        directionality_df['close_direction'] = close_direction
        correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)] 
        incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)] 
        relaxation_count = len(incorrect_direction_df[(abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)]) 
        try:
            directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
        except ZeroDivisionError:
            directionality_score = 0  # Or any other appropriate value
        return directionality_score


    def get_confidence_score(self,df, period,metrics_to_calculate = config['columns']['all_metrics']):
        metrics_columns = config['columns']['req_columns'] + metrics_to_calculate
        # Confidence score
        if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
            min_confidence = 0.01
            max_values =  df['actual_monthly_returns'].rolling(period * 24, min_periods = period).max()
            min_values =  df['actual_monthly_returns'].rolling(period * 24, min_periods = period).min()
            filt1 = [df.loc[i, 'monthly_predictions'] >= max_values.loc[i] for i in range(len(df))]
            filt2 = [df.loc[i, 'monthly_predictions'] <= min_values.loc[i] for i in range(len(df))]
            filt3 = [df.loc[i, 'actual_monthly_returns'] >= max_values.loc[i] for i in range(len(df))]
            filt4 = [df.loc[i, 'actual_monthly_returns'] <= min_values.loc[i] for i in range(len(df))]

            return [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
            max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_monthly_returns"])/(max_values.loc[i] - df.loc[i, "monthly_predictions"]) if df.loc[i, "actual_monthly_returns"] > df.loc[i, "monthly_predictions"] else (df.loc[i, "actual_monthly_returns"] - min_values.loc[i]) / (df.loc[i, "monthly_predictions"] - min_values.loc[i])) for i in range(len(df))]

    def calculate_metrics(self,df, isin,n_features, confidence_scores,period,prediction_column = 'actual_monthly_return_predictions', actual_column =  'actual_monthly_return', metrics_to_calculate =  config['columns']['all_metrics']):
        df = df.rename(columns = {prediction_column: 'monthly_predictions', actual_column: 'actual_monthly_returns'})
        metrics_columns =  config['columns']['req_columns'] + metrics_to_calculate
        metrics_columns.remove('confidence_score')
        metrics_columns.remove('confidence_score_14_day')
        metrics_columns.remove('avg_confidence_score')

        # Total perc diff
        if 'total_perc_diff' in metrics_columns or 'abs_total_diff' in metrics_columns:
            df['total_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.mean() - prediction.mean()) / prediction.mean(), period, df['actual_monthly_returns'].values, df['monthly_predictions'].values)
            
        # Abs total diff
        if 'abs_total_diff' in metrics_columns:
            df['abs_total_diff'] = abs(df['total_perc_diff'])
        
        # Total variance perc diff
        if 'total_variance_perc_diff' in metrics_columns or 'abs_total_variance_perc_diff' in metrics_columns:
            df['total_variance_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.var() - prediction.var()) / prediction.var(), period, df['actual_monthly_returns'].values, df['monthly_predictions'].values)
            
        # Abs total variance perc diff
        if 'abs_total_variance_perc_diff' in metrics_columns:
            df['abs_total_variance_perc_diff'] = abs(df['total_variance_perc_diff'])
        
        # MAE
        if 'mean_absolute_error' in metrics_columns:
            df['mean_absolute_error'] = (df['actual_monthly_returns'] - df['monthly_predictions']).rolling(period).apply(lambda df_series: abs(df_series).mean())
        
        # MSE
        if 'mean_squared_error' in metrics_columns or 'root_mean_squared_error' in metrics_columns:
            df['mean_squared_error'] = (df['actual_monthly_returns'] - df['monthly_predictions']).rolling(period).apply(lambda df_series: ((df_series ** 2).sum() / period))
        
        # RMSE
        if 'root_mean_squared_error' in metrics_columns:
            df['root_mean_squared_error'] = df['mean_squared_error'] ** 0.5
            
        # R2 score
        if 'r2_score' in metrics_columns or 'adjusted_r2_score' in metrics_columns:
            df['r2_score'] = rolling_apply_ext(r2_score, period, df['actual_monthly_returns'].values, df['monthly_predictions'].values)
                    
        # Adjusted R2 score
        if 'adjusted_r2_score' in metrics_columns:
            df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features - 1))
        
        # Mean directionality
        if 'mean_directionality' in metrics_columns:
            df['mean_directionality'] = (df['actual_monthly_returns'] * df['monthly_predictions']).rolling(period).apply(lambda df_series: 100 * ((df_series) > 0).sum() / period)
        
        # Correlation score
        if 'correlation_score' in metrics_columns:
            df['correlation_score'] = rolling_apply_ext(lambda target, prediction: np.corrcoef(target, prediction)[0][1], period, df['actual_monthly_returns'].values, df['monthly_predictions'].values) 
            
        # Accuracy
        if "accuracy_14_day" in metrics_columns or "accuracy_22_day" in metrics_columns or "accuracy_1_day"  in metrics_columns:
            df = self.calculate_accuracy(df, 'monthly_predictions', 'actual_monthly_returns')
        # # Average confidence score
        # if 'avg_confidence_score' in metrics_columns:
        #     df['avg_confidence_score'] = confidence_scores.rolling(period // 2).mean()
            

        if 'directionality_score' in metrics_columns:
            directionality_df = pd.DataFrame()
            directionality_df["prediction_direction"] = (df["monthly_predictions"] - df['monthly_predictions'].shift(1)) / df['monthly_predictions'].shift(1)
            directionality_df["close_direction"] = (df["actual_monthly_returns"] - df['actual_monthly_returns'].shift(1)) / df['actual_monthly_returns'].shift(1)
            df['directionality_score'] = rolling_apply_ext(self.directionality_score_calc, period, directionality_df['prediction_direction'], directionality_df['close_direction']) 

        # df['confidence_score_14_day'] = confidence_scores.rolling(11,min_periods=1).mean()


        df['isin'] = isin
        return df[metrics_columns][period:].reset_index(drop = True)


    def read_data(self,isin,schedular,model_type,years,period_schedular,formatted_date_run, read_from = 'es', append_data = None): # function to read historical data
        close_col = f'actual_{schedular.lower()}_return'
        pred_col = f'actual_{schedular.lower()}_return_predictions'
            
        columns_not_needed = config['columns']['columns_not_needed_metrics']
        if read_from == 'es': # fetching data from es
            temp = conn.get_es_data(isin,years,config['er_model_index_path'][f'{model_type}_pred_index'])
            temp = temp[temp['schedular']==schedular]
            temp['date'] = pd.to_datetime(temp['date'])
            temp = temp[temp['date']<=pd.to_datetime(formatted_date_run)]
            n_features = []
            for i in temp.columns:
                if i not in columns_not_needed:
                    n_features.append(i)

            temp = temp[['date', close_col, pred_col,'closeprice']]
            # fetching data from es
        temp['date'] = pd.to_datetime(temp['date'])
        temp = temp[(temp['date'] >= pd.to_datetime(f'{years[0]}-01-01')) & (temp['date'] <= pd.to_datetime(f'{years[1]}-12-31'))]
        
        if append_data is not None:
            temp = temp.append(append_data[(append_data['isin'] == isin) & (append_data['date'] > temp['date'].max())].drop(columns = ['isin']))
            
            
        temp.rename(columns = {pred_col:'monthly_predictions', 
                            close_col: 'actual_monthly_returns'}, 
                inplace = True)
        
        temp['actual_monthly_returns'] = (temp['closeprice'].pct_change(periods=period_schedular))*100
        temp['monthly_predictions'] = temp['monthly_predictions'].shift(period_schedular)
        temp = temp.drop_duplicates(subset = 'date').sort_values(by = 'date').reset_index(drop = True)
        temp = temp.ffill().fillna(0)
        temp = temp[(temp['monthly_predictions'] != 0) & \
                (temp['actual_monthly_returns'] != 0) & \
                temp['monthly_predictions'].notna() & \
                temp['actual_monthly_returns'].notna()] 
        # temp = temp[temp['monthly_predictions'].notna() & \
        #     temp['actual_monthly_returns'].notna()] 
        temp.reset_index(drop = True, inplace = True)
        temp['monthly_predictions'] = temp['monthly_predictions'].astype(float, errors="ignore")
        temp['actual_monthly_returns'] = temp['actual_monthly_returns'].astype(float, errors="ignore")
        
        return temp,len(list(set(n_features)))


    def calculate_and_save_metrics(self,isin,formatted_date_run,schedular,failed_metrics_list,model_type,logger):
        period_schedular = self.metrics_period_map.get(schedular, 22)
        period = config['er_model_index_path']['period']
        years = [datetime.datetime.now().date().year-4,datetime.datetime.now().date().year]
        try:
            temp,n_er_features = self.read_data(isin,schedular,model_type,years,period_schedular,formatted_date_run,read_from='es')
        except Exception as e:
            logger.exception(f'exception is str({e})')
            logger.exception(f"Data not available for {isin}.")
            failed_metrics_list.append((isin,e))
            return

        try:
            confidence_scores = self.get_confidence_score(temp[-600:].reset_index(drop=True),period)[-24:]
            rolling_confidence_scores = pd.Series(confidence_scores).rolling(11).mean()
            metrics_df = self.calculate_metrics(temp[-30:].reset_index(drop=True), isin,n_er_features,pd.Series(confidence_scores),period)
            metrics_df['confidence_score'] = confidence_scores[-8:]
            metrics_df['confidence_score_14_day'] = rolling_confidence_scores[-8:].reset_index(drop=True)
            metrics_df['avg_confidence_score'] = rolling_confidence_scores[-8:].reset_index(drop=True)
            metrics_df = metrics_df[metrics_df['date']==formatted_date_run]
            metrics_df.rename(columns={'monthly_predictions':f'actual_{schedular.lower()}_return_predictions','actual_monthly_returns':f'actual_{schedular.lower()}_return'},inplace=True)
            return metrics_df
        except Exception as e:
            logger.info("Metrics Generation Faied for ISIN",isin)
            failed_metrics_list.append((isin,e))


    def get_last_trading_date(self,isin, date,logger):
            url = conn.spglobal_url  
            headers = {'Authorization': conn.head_auth,'Content-type': conn.content_type}                                  
            data = {
                    "inputRequests": [
                    {
                        "function": 'GDSP',
                        "identifier": isin,
                        "mnemonic": 'IQ_PRICEDATE'
                    }
                ]
            }
            data_json = json.dumps(data)
    
            response = requests.post(url, data=data_json, headers=headers)
            try:
                resp_json=json.loads(response.text)
    
                if resp_json == None:
                    return True
    
                else:
                    try:
                        tmp=pd.json_normalize(resp_json['GDSSDKResponse'])
                        data = tmp['Rows'].iloc[0]
                        date_traded = data[0]['Row'][0]
                        if date_traded == 'Data Unavailable':
                            return True
                        else:
                            date_traded = datetime.datetime.strptime(date_traded,'%Y-%m-%d')
                            date_obj = datetime.datetime.strptime(date,'%Y-%m-%d')
                            if date_traded<date_obj:
                                return False
                            else:
                                return True
                    except Exception as e:
                        return True
            except Exception as e:
                logger.info(f"Error in finding the reason for no closeprice, {e}")
                return True

    def save_records_in_es_s3(self,model_type,schedular,df,saved_date,tag,df_type,log_file_path,logger):
        df.replace({np.inf:np.nan},inplace=True)
        df['schedular']= schedular
        df['updated_at']= datetime.datetime.now()

        df['date'] = df['date'].astype(str)
        if not (df_type == 'preds' and model_type == config['mappings']['model_name_3']):
            df['updated_at'] = df['updated_at'].astype(str)
        
        schedular_key = self.schedular_map.get(schedular)
        config_key = f"new_mnemonic_folder_{model_type}_{schedular_key}_{tag}"
        s3_template_pred = config['version_control']['s3_versioning_pred_filename_template']
        s3_template_metric = config['version_control']['s3_versioning_metrics_filename_template']
        s3_version_pred_filename = s3_template_pred.format(model_type=model_type,schedular=schedular.lower(),date=saved_date)
        s3_version_metric_filename = s3_template_metric.format(model_type=model_type,schedular=schedular.lower(),date=saved_date)

 
        if df_type == 'preds':
            print(1)
            conn.s3_client.write_advanced_as_df(df,config['er_model_index_path']['bucket_name_lightgbm'],f'''{config['er_model_index_path'][f'{config_key}']}/daily_run/{pd.to_datetime(saved_date).strftime("%Y-%m-%d")}.csv''')
            
            conn.es_client.save_records_v2(df,config['er_model_index_path'][f'{model_type}_pred_index'])

            try:                      
                df.parallel_apply(lambda row: conn.save_row(row.name, row, config['version_control']['bucket_name'],s3_version_pred_filename ), axis=1)
            except Exception as e:
                logger.error(f"Error in saving the Pred data to S3 versioning path: {e}")  

        else:
            print(2)
            conn.s3_client.write_advanced_as_df(df,config['er_model_index_path']['bucket_name_lightgbm'],f'''{config['er_model_index_path'][f'{config_key}']}/daily_run_metrics/{pd.to_datetime(saved_date).strftime("%Y-%m-%d")}.csv''')
            conn.s3_client.upload_file(log_file_path,config['er_model_index_path']['bucket_name_lightgbm'],f'''{config['er_model_index_path'][f'{config_key}']}/daily_run_logs/{pd.to_datetime(saved_date).strftime("%Y-%m-%d")}.log''')

            conn.es_client.save_records_v2(df,config['er_model_index_path'][f'{model_type}_metric_index'])
            
            try:
                df.parallel_apply(lambda row: conn.save_row(row.name, row, config['version_control']['bucket_name'],s3_version_metric_filename ), axis=1)
            except Exception as e:
                logger.error(f"Error in saving the Metrics data to S3 versioning path: {e}") 

    def generate_backward_nan_report(self, df, logger,feature_cols, date_col='date', isin_col='isin'):
        nan_report = defaultdict(list)
        try:
            if date_col not in df.columns or isin_col not in df.columns:
                logger.error(f"Missing '{date_col}' or '{isin_col}' column in DataFrame.")
                return nan_report

            df = df.sort_values(by=[isin_col, date_col])
            last_date = df[date_col].max()
            grouped = df.groupby(isin_col)

            for isin, group in grouped:
                try:
                    group = group.sort_values(date_col, ascending=False).reset_index(drop=True)
                    last_row = group.iloc[0]

                    for feat in feature_cols:
                        if feat not in group.columns:
                            logger.warning(f"Feature '{feat}' not found for ISIN {isin}. Skipping.")
                            continue

                        if pd.isna(last_row[feat]):
                            nan_streak = 0
                            start_date = None
                            for _, row in group.iterrows():
                                if pd.isna(row[feat]):
                                    nan_streak += 1
                                    start_date = row[date_col]
                                else:
                                    break
                            if nan_streak > 0:
                                nan_report[isin].append({
                                    'feature': feat,
                                    'start_date': start_date,
                                    'end_date': last_date,
                                    'no_of_days_nan': nan_streak
                                })
                except Exception as e:
                    logger.exception(f"Error processing ISIN {isin}: {e}")

        except Exception as e:
            logger.exception(f"Error in generate_backward_nan_report: {e}")
        
        return nan_report


    def flatten_nan_report(self, nan_report,logger):
        records = []
        try:
            for isin, issues in nan_report.items():
                for issue in issues:
                    try:
                        records.append({
                            'isin': isin,
                            'feature': issue['feature'],
                            'start_date': issue['start_date'],
                            'end_date': issue['end_date'],
                            'no_of_days_nan': issue['no_of_days_nan']
                        })
                    except Exception as e:
                        logger.warning(f"Error while flattening issue for ISIN {isin}: {e}")
        except Exception as e:
            logger.exception(f"Error in flatten_nan_report: {e}")

        return pd.DataFrame(records)

    def get_credentials(self):
        SCOPES = config['mailing']['scopes']
        creds = None

        response = conn.s3_client._s3Client.get_object(Bucket=config['mailing']['gmail_cred_bucket'], Key=config['mailing']['gmail_cred_file'])
        credentials_data = json.loads(response['Body'].read().decode("utf-8"))

        # Load into Google Credentials object
        creds = Credentials.from_authorized_user_info(info=credentials_data, scopes=SCOPES)
        return creds



    def SendMessage(self, sender, to, subject, filename, message_text,logger, attachments: list = None):
        credentials = self.get_credentials()
        if credentials == None:
            logger.info("credentials not found, Generate credentials")
            return 
        try:
            service = build('gmail', 'v1', credentials=credentials)
            message = EmailMessage()
            html = message_text
            body = MIMEText(html, 'html')
            message.set_content(body)
            if attachments:
                for i,attachment in enumerate(attachments):
                    with open(attachment, 'rb') as content_file:
                        content = content_file.read()
                        message.add_attachment(content, maintype='application', subtype=(
                            attachment.split('.')[1]), filename=filename[i])
            message['To'] = to
            message['From'] = sender
            message['Subject'] = subject

            # encoded message
            encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
                .decode()

            create_message = {
                'raw': encoded_message
            }
            # pylint: disable=E1101
            send_message = (service.users().messages().send(
                userId="me", body=create_message).execute())
            logger.info(F'Message Id: {send_message["id"]}')
        except HttpError as error:
            logger.info(F'An error occurred: {error}')
            send_message = None 


    def send_daily_run_mail(self,masters_isin_list,schedular,tag,model_type,final_df,pred_col,failed_metrics_list,metrics_df,formatted_date_run,not_traded_isins,logger):
        try:   
            receivers_list = config['mailing']['recipients']  
            subject = f'ER-{model_type.upper()} {tag.upper()} {schedular.capitalize()} Model Daily Run'
            # subject = f'{tag.upper()}-{schedular.capitalize()[0]} {model_type.upper()} ER Model Daily Run'
            message_text = f"""
                                <html>
                                <body>
                                <p>ER-{model_type.upper()} {tag.upper()} {schedular.capitalize()} Model Run for Business Date {str(pd.to_datetime(formatted_date_run).date())} has been successfully completed.</p>

                                <p><strong>Predictions Summary:</strong><br>

                                &nbsp;&nbsp;&nbsp;- Total Number of ISINs: {int(len(masters_isin_list))}<br>

                                &nbsp;&nbsp;&nbsp;- Number of Successful Runs: {final_df[pred_col].notna().sum()}<br>

                                &nbsp;&nbsp;&nbsp;- Number of Failed Runs: {int(len(masters_isin_list)) - int(final_df[pred_col].notna().sum()) - int(len(not_traded_isins))}<br>

                                &nbsp;&nbsp;&nbsp;- Number of ISINs Not Traded: {int(len(not_traded_isins))}<br>

                                &nbsp;&nbsp;&nbsp;- Number of ISINs with 0 as Prediction: {final_df[final_df[pred_col] == 0]['isin'].nunique()}<br>

                                &nbsp;&nbsp;&nbsp;- Number of ISINs with Predictions from Individual Model: {final_df.apply(lambda row: str(row['isin']) in str(row['model_identifier']), axis=1).sum()}<br>

                                &nbsp;&nbsp;&nbsp;- Number of ISINs with Predictions from Sector-Based Model: {final_df.apply(
                                lambda row: str(row['isin']) not in str(row['model_identifier']) and str(row['model_identifier']) != config['mappings']['sector_identifier']
                                if pd.notnull(row['isin']) and pd.notnull(row['model_identifier']) 
                                else False,
                                axis=1
                                ).sum()}<br>

                                &nbsp;&nbsp;&nbsp;- Number of ISINs with Predictions from Sector-Average: {final_df['model_identifier'].apply(lambda x: str(x) == config['mappings']['sector_identifier']).sum()}<br>
                                
                                </p>

                                <p>Records for {final_df['isin'].nunique()} ISINs have been added to Elasticsearch.</p>

                                <p><strong>Metrics Summary:</strong><br>
                                &nbsp;&nbsp;&nbsp;- Total Number of ISINs: {int(len(masters_isin_list))}<br>
                                &nbsp;&nbsp;&nbsp;- Total Number of Successful Run ISINs: {metrics_df['isin'].nunique()}<br>
                                &nbsp;&nbsp;&nbsp;- Number of Failed ISINs: {int(len(masters_isin_list)) - int(len(metrics_df))}<br>
                                <p>Feel free to reach out if you need any help or clarification.<br><br>
                                Best Regards,<br>Aman Kumar Jethani</p>

                                </body>
                                </html>
                                """
            self.SendMessage("Aman Kumar Jethani <<EMAIL>>",receivers_list, subject, None, message_text,logger, None)
        except Exception as e:
            logger.error(f"Error in sending mail regarding run completion: {e}")

    
    def send_trigger_mail(self,schedular,tag,model_type,formatted_date_run,logger):
        try:   
            receivers_list = config['mailing']['trigger_mail_recipients']  
            subject = f'ER-{model_type.upper()} {tag.upper()} {schedular.capitalize()} Model Daily Run Started'
            message_text = f"""
                                <html>
                                <body>
                                <p>ER-{model_type.upper()} {tag.upper()} {schedular.capitalize()} Model Run for Business Date {str(pd.to_datetime(formatted_date_run).date())} has been successfully triggered.</p>
                                <p>Feel free to reach out if you need any help or clarification.<br><br>
                                Best Regards,<br>Aman Kumar Jethani</p>

                                </body>
                                </html>
                                """
            self.SendMessage("Aman Kumar Jethani <<EMAIL>>",receivers_list, subject, None, message_text,logger, None)
        except Exception as e:
            logger.error(f"Error in sending mail regarding run completion: {e}")
                                
    def send_benchamrk_mail(self,formatted_date_run,tag,logger):
        try:   
            receivers_list = config['mailing']['trigger_mail_recipients'][0]  
            subject = f'Benchmark Data {tag} for Sector Gain Completed'
            message_text = f"""
                                <html>
                                <body>
                                <p>Benchamrk Data Run for Tag: {tag} and Business Date {str(pd.to_datetime(formatted_date_run).date())} has been successfully done.</p>
                                <p>Feel free to reach out if you need any help or clarification.<br><br>
                                Best Regards,<br>Aman Kumar Jethani</p>

                                </body>
                                </html>
                                """
            self.SendMessage("Aman Kumar Jethani <<EMAIL>>",receivers_list, subject, None, message_text,logger, None)
        except Exception as e:
            print(f"Error in sending mail regarding run completion: {e}")
            


 


                        


                                

            


 


                        

