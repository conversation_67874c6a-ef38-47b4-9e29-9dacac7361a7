#!/usr/bin/env python
# coding: utf-8

import os
import sys
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))
from Imports import *
from Helper import *
config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = DataProcessor()
helpers = Helpers()


global masters_tag
masters_tag = sys.argv[1]
global schedular
schedular = sys.argv[2]
global model_type
model_type = sys.argv[3]
# model_type = 'xgb'
# masters_tag = 'aieq'
# schedular = 'Quarterly'
tier1_tag = config['mappings']['tag_names'][4]
tag = config['mappings']['tag_mapping'].get(masters_tag)
if schedular != config['er_model_index_path']['schedular_2']:
    data_schedular = config['er_model_index_path']['schedular_1']
else:
    data_schedular = schedular


current_date = datetime.date.today()
# current_date = pd.to_datetime('2025-03-13')
formatted_date_run = (current_date-BDay(1)).date().strftime("%Y-%m-%d")
start_date = (pd.to_datetime(formatted_date_run) - BDay(22)).isoformat()
end_date = (pd.to_datetime(formatted_date_run)-BDay(1)).isoformat()


global logger
log_folder = f'{script_dir}/Log_Files_Ffill_{model_type.capitalize()}_{helpers.schedular_map.get(schedular).capitalize()}_{tag.capitalize()}'
log_file_path = f'{log_folder}/{formatted_date_run}.log'
logger = helpers.create_logger(log_file_path)


try:   
    masters = conn.get_master_df(masters_tag)
    masters_isin_list = masters['isin'].unique()
    tier1 = conn.get_master_df(tier1_tag)
    tier1_isin_list = tier1['isin'].unique()
except Exception as e:
    logger.error(f"Error in getting ISIN list: {e}")   
    raise SystemExit('Stop right there')   


df_old = conn.get_es_data_all(start_date,end_date,config['er_model_index_path'][f'{model_type}_pred_index'],schedular,masters_isin_list)


# Set flags for easier conditions
is_aieq_or_aigo = tag in [config['mappings']['tag_names'][0], config['mappings']['tag_names'][1]]

# Select column groups based on model type and tag
if model_type == config['mappings']['model_name_1']:
    columns_info = config['columns']['columns_info_lgb_aieq'] if is_aieq_or_aigo else config['columns']['columns_info_lgb_india']
    columns_fin = config['columns']['columns_finance_lgb_aieq'] if is_aieq_or_aigo else config['columns']['columns_finance_lgb_india']
    columns_man = config['columns']['columns_man']

elif model_type == config['mappings']['model_name_2']:
    columns_info = config['columns']['columns_info_cat_aieq'] if is_aieq_or_aigo else config['columns']['columns_info_cat_india']
    if is_aieq_or_aigo:
        columns_info += config['columns']['columns_info_ffill_script']
    columns_fin = config['columns']['columns_finance_cat_aieq'] if is_aieq_or_aigo else config['columns']['columns_finance_cat_india']
    columns_man = []

else:  # Fallback model type
    columns_info = config['columns']['columns_info_cat_aieq'] if is_aieq_or_aigo else config['columns']['columns_info_cat_india']
    columns_fin = config['columns']['columns_finance_cat_aieq'] if is_aieq_or_aigo else config['columns']['columns_finance_cat_india']
    columns_man = []

# Add extra sentiment columns to financial columns
columns_fin += config['columns']['columns_fin_ffill_script']

# Additional feature groups
columns_technical_indicators = config['columns']['columns_technical_indicators_ffill_script']
columns_sector = config['columns']['columns_sector_ffill_script']
columns_lstm = config['columns']['columns_lstm_ffill_script']
columns_macro = config['columns']['columns_macro_ffill_script']

# Combine all model features
columns_base_models = list(set(
    columns_info + columns_fin + columns_man +
    columns_technical_indicators + columns_sector +
    columns_lstm + columns_macro
))

# Columns to drop
date_columns_and_extras = config['columns']['date_columns_and_extras_ffill_script']

# Final features
if model_type != config['mappings']['model_name_1']:
    df_final_features = df_old.drop(columns=[col for col in date_columns_and_extras if col in df_old.columns])
else:
    df_final_features = df_old.copy()
er_model_feat_nd_preds = df_final_features.columns.difference(columns_base_models).tolist()
prediction_column = next((col for col in er_model_feat_nd_preds if re.match(config['mappings']['pattern'], col)), None)


# Combine all columns
columns_all = columns_base_models + er_model_feat_nd_preds


feature_owner_mapping = {
                            config['mailing']['amit']: columns_technical_indicators,
                            config['mailing']['sandra']: columns_fin,
                            config['mailing']['shivam']: columns_info,
                            config['mailing']['my_email']: er_model_feat_nd_preds,
                            config['mailing']['abhinav']: columns_lstm,
                            config['mailing']['sachin']: columns_sector,
                            config['mailing']['tanmay']: columns_macro,
                            config['mailing']['my_email']: prediction_column,
                        }
 
model_mapping = {
                    config['mailing']['amit']: "TI",
                    config['mailing']['sandra']: "Finance",
                    config['mailing']['shivam']: "Information",
                    config['mailing']['my_email']: "ER_Model",
                    config['mailing']['abhinav']: "LSTM",
                    config['mailing']['sachin']: "Sector_Gain",
                    config['mailing']['tanmay']: "Macro",
                    config['mailing']['my_email']: "ER_Model_Preds"
                }


st = time.time()
nan_report = helpers.generate_backward_nan_report(df_old, logger, feature_cols=columns_all)
flat_df = helpers.flatten_nan_report(nan_report,logger)
et = time.time()
print(et-st)


schedular_key = helpers.schedular_map.get(schedular)
config_key = f"new_mnemonic_folder_{model_type}_{schedular_key}_{tag}"
all_bdays = pd.bdate_range(start=start_date, end=current_date)
filled_df = conn.bulk_s32df([config['er_model_index_path']['bucket_name_lightgbm']]*len(all_bdays), [f"{config['er_model_index_path'][config_key]}/ffiled_data_folder/{i.date()}.csv" for i in all_bdays])


if not filled_df.empty:
    forward_fill_counts = (
        filled_df[filled_df['still_missing']==False]
        .groupby(['isin', 'feature'])
        .size()
        .reset_index(name='no_of_days_forward_filled')
    )
    updated_flat_df = flat_df.merge(
        forward_fill_counts,
        on=['isin', 'feature'],
        how='left'
    )
    updated_flat_df['no_of_days_forward_filled'] = updated_flat_df['no_of_days_forward_filled'].fillna(updated_flat_df['no_of_days_nan'])

else:
    updated_flat_df['no_of_days_forward_filled'] = updated_flat_df['no_of_days_nan']

updated_flat_df['no_of_days_nan'] = updated_flat_df['no_of_days_nan'].astype(int)
updated_flat_df['no_of_days_forward_filled'] = updated_flat_df['no_of_days_forward_filled'].astype(int)





q_mnems = config['columns']['q_mnems']
daily_mnems = config['columns']['daily_mnems']
mnemonic_df = conn.s3_client.read_advanced_as_df(config['er_model_index_path']['bucket_name_lightgbm'],config['er_model_index_path']['industry_mapping_file'])
mnemonic_df.rename(columns={'mnemonic_list':'new_mnemonics'},inplace=True)
ind_names = mnemonic_df['Industry_Name'].unique()
ind_name_mapping = conn.s3_client.read_advanced_as_df(config['er_model_index_path']['bucket_name_lightgbm'],config['er_model_index_path']['ind_name_mapping_file'])
ind_name_mapping = ind_name_mapping[['Industry_ID','Industry_Name']].dropna()

company_df = masters.copy()
company_df['ind_code'] = company_df['ind_code'].astype('float')
company_df = company_df.merge(ind_name_mapping,left_on='ind_code',right_on='Industry_ID',how='left')
company_df = company_df.drop('Industry_ID',axis=1)

company_df['mnemonic_list_d']=[daily_mnems for i in company_df.index]
company_df['mnemonic_list_q']=[q_mnems for i in company_df.index]
company_df = company_df.merge(mnemonic_df,on='Industry_Name',how='left')
company_df['mnemonic_list_q']=company_df.apply(lambda row: list(set(row['mnemonic_list_q'] + eval(row['new_mnemonics']))) if pd.notna(row['new_mnemonics']) else row['mnemonic_list_q'], axis=1)

company_df['all_mnemonics'] = company_df['mnemonic_list_q']+company_df['mnemonic_list_d']
company_df['all_mnemonics'] = company_df['all_mnemonics'].apply(lambda x: [i.split('IQ_')[1].lower() for i in x])
master_isin_mapping = dict(zip(company_df['isin'],company_df['all_mnemonics']))


flat_df_1 = updated_flat_df[updated_flat_df['feature'].isin(er_model_feat_nd_preds)]
flat_df_2 = updated_flat_df[~updated_flat_df['feature'].isin(er_model_feat_nd_preds)]

flat_df_1['feature_used'] = flat_df_1.apply(lambda x: True if x['feature'] in master_isin_mapping[x['isin']] else False,axis=1)
flat_df_1 = flat_df_1[flat_df_1['feature_used']==True]
final_df = pd.concat([flat_df_1,flat_df_2])
final_df.drop('feature_used',axis=1,inplace=True)


final_df = final_df[
    ~(
        (~final_df['isin'].isin(tier1_isin_list)) &
        (final_df['feature'].isin(config['columns']['columns_info_ffill_script']))
    )
]


feature_to_email = {}
for email, features in feature_owner_mapping.items():
    for feat in features:
        feature_to_email[feat] = email

length_gt_2 = final_df[(final_df['no_of_days_nan'] > 2) & (final_df['no_of_days_nan'] <= 5)].copy()
length_gt_5 = final_df[final_df['no_of_days_nan'] > 5].copy()

length_gt_2['owner'] = length_gt_2['feature'].map(feature_to_email)
length_gt_5['owner'] = length_gt_5['feature'].map(feature_to_email)





os.makedirs(config['mailing']['high_priority_folder'], exist_ok=True)
os.makedirs(config['mailing']['low_priority_folder'], exist_ok=True)
low_priority_files = []
high_priority_files = []
for owner_email, mail_df in length_gt_2.groupby('owner'):
    mail_df = mail_df.drop(columns='owner')
    file_path = os.path.join(
    config['mailing']['low_priority_folder'], f'Low_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(formatted_date_run).date())}.csv'
)
    mail_df.to_csv(file_path, index=False)
    low_priority_files.append(owner_email)
for owner_email, mail_df in length_gt_5.groupby('owner'):
    mail_df = mail_df.drop(columns='owner')
    file_path = os.path.join(
    config['mailing']['high_priority_folder'], f'High_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(formatted_date_run).date())}.csv'
)
    mail_df.to_csv(file_path, index=False)
    high_priority_files.append(owner_email)


for owner_email in model_mapping.keys():
    attachments = []
    message_parts = ["Hi,</p>", f"<p>Please find below the data quality report for {tag.upper()} based on recent observations.</p>", "<ul>"]
    if owner_email in low_priority_files and owner_email in high_priority_files:
        subject = f"[Data Quality Report - {tag.upper()}] Forward Fill Checker – High & Low Priority Flags"
        message_parts.append("<li><strong>High Priority:</strong> Features with more than <strong>5 consecutive missing values</strong>.</li>")
        message_parts.append("<li><strong>Low Priority:</strong> Features with more than <strong>2 consecutive missing values</strong>.</li>")

        attachments.extend([
            os.path.join(config['mailing']['low_priority_folder'], f"Low_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(formatted_date_run).date())}.csv"),
            os.path.join(config['mailing']['high_priority_folder'], f"High_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(formatted_date_run).date())}.csv")
        ])
    elif owner_email in high_priority_files:
        subject = f"[Data Quality Report - {tag.upper()}] Forward Fill Checker – High Priority Flags"
        message_parts.append("<li><strong>High Priority:</strong> Features with more than <strong>5 consecutive missing values</strong>.</li>")
        attachments.append(os.path.join(config['mailing']['high_priority_folder'],f'High_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(formatted_date_run).date())}.csv'))             
    elif owner_email in low_priority_files:
        subject = f"[Data Quality Report - {tag.upper()}] Forward Fill Checker – Low Priority Flags"
        message_parts.append("<li><strong>Low Priority:</strong> Features with more than <strong>2 consecutive missing values</strong>.</li>")
        attachments.append(os.path.join(config['mailing']['low_priority_folder'],f'Low_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(formatted_date_run).date())}.csv'))
    if len(attachments)>0:    
        message_parts.append("<p>Kindly review and take necessary actions.</p>")   
        message_parts.append(
        "<p>Feel free to reach out if you need any help or clarification.<br><br>"
        "Best Regards,<br>Aman Kumar Jethani</p>"
    )
        message_text = "\n".join(message_parts)
        receiver_list = [owner_email]
        # receiver_list = ['<EMAIL>']

        helpers.SendMessage(config['mailing']['sender_name'], receiver_list, subject, [i.split('/')[-1] for i in attachments], message_text, logger, attachments)




conn.s3_client.upload_file(log_file_path,config['er_model_index_path']['bucket_name_lightgbm'],f'''{config['er_model_index_path'][f'{config_key}']}/daily_run_ffiled_logs/{pd.to_datetime(formatted_date_run).strftime("%Y-%m-%d")}.log''')


for folder_path in [config['mailing']['high_priority_folder'], config['mailing']['low_priority_folder'],log_folder]:
    if os.path.exists(f'{script_dir}/folder_path') and os.path.isdir(f'{script_dir}/folder_path'):
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
        os.rmdir(folder_path)




