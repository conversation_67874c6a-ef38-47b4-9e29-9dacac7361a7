import os
import sys
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))
from Imports import *
from Helper import *
config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = DataProcessor()
helpers = Helpers()

tag = sys.argv[1]
# tag = 'tier1'



today = datetime.datetime.now().date()
formatted_date_needed = (today - BDay(1)).strftime('%Y-%m-%d')
old_date_prev = (today - BDay(300)).strftime('%Y-%m-%d')

global logger
log_folder_dir = f'{script_dir}/Log_Files_SG_{tag.capitalize()}'
log_file_path = f'{log_folder_dir}/{formatted_date_needed}_daily_run.log'
logger = helpers.create_logger(log_file_path)

try:
    masters = conn.get_inhouse_master_df(tag)
    if masters.empty:
        print("[ERROR] Master data is empty.")
        sys.exit(1)
except Exception as e:
    logger.error(f"[ERROR] Failed to load master data: {e}")
    sys.exit(1)

try:
    r_mkp = masters.parallel_apply(lambda x: conn.get_cp_mc_values(x['isin'], masters, old_date_prev), axis=1)
    df_all = pd.concat(r_mkp.tolist())
    df_all['date'] = pd.to_datetime(df_all['date'])
    df_all = df_all[df_all['date'] <= formatted_date_needed]
except Exception as e:
    logger.error(f"[ERROR] Error fetching or processing market data: {e}")
    sys.exit(1)

df_mkp = df_all[['isin', 'date', 'market_cap']].copy()
df_mkp.columns = ['isin', 'date', 'marketcap']
df_mkp['date'] = pd.to_datetime(df_mkp['date'])
df_mkp.sort_values(by=['isin', 'date'], inplace=True)
df_mkp.reset_index(drop=True, inplace=True)
df_mkp = df_mkp.loc[df_mkp.groupby("isin")["date"].idxmax()].reset_index(drop=True)

df_cp = df_all[['isin','date','close']]
df_cp.columns = ['isin','date','closeprice']
df_cp.sort_values(by=['isin','date'],inplace=True)
df_cp.reset_index(drop=True)
df_cp['date'] = pd.to_datetime(df_cp['date'])

# Sort and set index
df_cp = df_cp.sort_values(['isin', 'date']).set_index(['isin', 'date'])

# Create shifted close prices
df_cp['closeprice_1d_back'] = df_cp.groupby(level=0)['closeprice'].shift(1)
df_cp['closeprice_5d_back'] = df_cp.groupby(level=0)['closeprice'].shift(4)
df_cp['closeprice_22d_back'] = df_cp.groupby(level=0)['closeprice'].shift(19)
df_cp['closeprice_66d_back'] = df_cp.groupby(level=0)['closeprice'].shift(63)

# Calculate % changes
df_cp['pct_change_daily'] = (df_cp['closeprice'] - df_cp['closeprice_1d_back']) / df_cp['closeprice_1d_back']
df_cp['pct_change_weekly'] = (df_cp['closeprice'] - df_cp['closeprice_5d_back']) / df_cp['closeprice_5d_back'] 
df_cp['pct_change_monthly'] = (df_cp['closeprice'] - df_cp['closeprice_22d_back']) / df_cp['closeprice_22d_back'] 
df_cp['pct_change_quarterly'] = (df_cp['closeprice'] - df_cp['closeprice_66d_back']) / df_cp['closeprice_66d_back'] 


# Keep only necessary columns
df_cp = df_cp[['closeprice','pct_change_daily', 'pct_change_monthly', 'pct_change_weekly', 'pct_change_quarterly']]

# Drop rows with any NaN
df_cp = df_cp.dropna()

# Reset index
df_cp = df_cp.reset_index(drop=False).reset_index(drop=True)


df_cp_with_mkp = df_cp.merge(df_mkp[['isin', 'marketcap']], on='isin', how='left')
cols = ['pct_change_daily', 'pct_change_monthly', 'pct_change_weekly', 'pct_change_quarterly', 'marketcap']
for col in cols:
    df_cp_with_mkp[col] = pd.to_numeric(df_cp_with_mkp[col], errors='coerce')

df_cp_with_mkp['w_pct_change_daily'] = df_cp_with_mkp['pct_change_daily'] * df_cp_with_mkp['marketcap']
df_cp_with_mkp['w_pct_change_monthly'] = df_cp_with_mkp['pct_change_monthly'] * df_cp_with_mkp['marketcap']
df_cp_with_mkp['w_pct_change_weekly'] = df_cp_with_mkp['pct_change_weekly'] * df_cp_with_mkp['marketcap'] 
df_cp_with_mkp['w_pct_change_quarterly'] = df_cp_with_mkp['pct_change_quarterly'] * df_cp_with_mkp['marketcap'] 

df_cp_with_mkp_indcode = df_cp_with_mkp.merge(masters[['isin','ind_code']],on=['isin'],how='left')
df_cp_with_mkp_indcode['date'] = pd.to_datetime(df_cp_with_mkp_indcode['date'])
df_cp_with_mkp_indcode.sort_values(by=['isin', 'date'], inplace=True)
df_cp_with_mkp_indcode.reset_index(drop=True, inplace=True)
df_cp_with_mkp_indcode = df_cp_with_mkp_indcode.loc[df_cp_with_mkp_indcode.groupby("isin")["date"].idxmax()]

# Group by ind_code and compute sums
grouped = df_cp_with_mkp_indcode.groupby(['ind_code']).agg({
    'w_pct_change_daily' : 'sum',
    'w_pct_change_monthly': 'sum',
    'w_pct_change_weekly': 'sum',
    'w_pct_change_quarterly': 'sum',
    'marketcap': 'sum'
}).reset_index()

# Compute normalized values
grouped['d_weightedAveragePerSector'] = grouped['w_pct_change_daily'] / grouped['marketcap']
grouped['m_weightedAveragePerSector'] = grouped['w_pct_change_monthly'] / grouped['marketcap']
grouped['w_weightedAveragePerSector'] = grouped['w_pct_change_weekly'] / grouped['marketcap']
grouped['q_weightedAveragePerSector'] = grouped['w_pct_change_quarterly'] / grouped['marketcap']

# Optional: drop intermediate columns if not needed
grouped = grouped[['ind_code','d_weightedAveragePerSector', 'm_weightedAveragePerSector', 'w_weightedAveragePerSector', 'q_weightedAveragePerSector','marketcap']]
grouped.rename(columns={'marketcap':'totalMCapOfASector'},inplace=True)

grouped['date'] = today
grouped['tag'] = (
    config['mappings']['tag_names'][0] if tag in [config['mappings']['tag_names'][0], config['mappings']['tag_names'][4]]
    else  config['mappings']['tag_names'][1] if tag == config['mappings']['tag_names'][1]
    else  config['mappings']['tag_names'][6] if tag == config['mappings']['tag_names'][5]
    else 'other'
)

tag_category = config['mappings']['tag_mapping'].get(tag, 'default')
benchmark_index = config['mappings']['index_sg_map'].get(tag_category, config['mappings']['index_sg_map'])

try:
    conn.es_client.save_records_v2(grouped, benchmark_index, primary_column='ind_code', unique_columns=['ind_code', 'date'])
    logger.info(f"[INFO] Successfully saved to index: {benchmark_index}")
except Exception as e:
    logger.error(f"[ERROR] Failed to save to index: {e}")
    sys.exit(1)

helpers.send_benchamrk_mail(formatted_date_needed,tag,logger)

for folder_path in [log_folder_dir]:
    if os.path.exists(folder_path) and os.path.isdir(folder_path):
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
        os.rmdir(folder_path)



