#!/usr/bin/env python
# coding: utf-8

import os
import sys
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))
from Imports import *
from Helper import *
config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = DataProcessor()
helpers = Helpers()


global masters_tag
masters_tag = sys.argv[1]
global schedular
schedular = sys.argv[2]
global model_type
model_type = sys.argv[3]
# model_type = 'lgb'
# masters_tag = 'aieq'
# schedular = 'Monthly'
tag = config['mappings']['tag_mapping'].get(masters_tag)
if schedular != config['er_model_index_path']['schedular_2']:
    data_schedular = config['er_model_index_path']['schedular_1']
else:
    data_schedular = schedular


masters = conn.get_master_df(masters_tag)
isin_list = masters['isin'].unique()

if masters_tag == config['mappings']['tag_names'][1]:
    masters_aieq = conn.get_master_df(config['mappings']['tag_names'][0])
    masters_indeq = conn.get_master_df(config['mappings']['tag_names'][3])

    # Combine ISINs from aieq and indeq
    exclude_isins = pd.concat([masters_aieq, masters_indeq])['isin'].unique()

    # Filter out those ISINs from the aigo master
    masters = masters[~masters['isin'].isin(exclude_isins)]
    isin_list = masters['isin'].unique()


current_date = datetime.date.today()
# current_date = pd.to_datetime('2025-03-14')
adhoc_date_needed= (current_date-BDay(2)).date().strftime("%Y-%m-%d")
formatted_date_run = (current_date-BDay(1)).date().strftime("%Y-%m-%d")
last_date_needed = (pd.to_datetime(current_date) - timedelta(days=config['er_model_index_path']['adhoc_data_read_days'])).isoformat()


global logger
log_folder_dir = f'{script_dir}/Log_Files_{model_type.capitalize()}_{helpers.schedular_map.get(schedular).capitalize()}_{tag.capitalize()}'
log_file_path = f'{log_folder_dir}/{formatted_date_run}_daily_run.log'
logger = helpers.create_logger(log_file_path)

helpers.send_trigger_mail(schedular,tag,model_type,formatted_date_run,logger)

no_models_list,isin_df,ts_dict,q_data = helpers.get_isin_df(tag,model_type,schedular,isin_list,logger)


# ### Data Collection Started

try:
    adhoc = helpers.get_adhoc_data(last_date_needed, adhoc_date_needed, isin_list,model_type,schedular, logger)
except Exception as e:
    logger.error(f"Error in reading Adhoc Data: {e}")


columns_info_filename = f"columns_info_{config['mappings']['model_name_2'] if model_type == config['mappings']['model_name_3'] else model_type}_{config['mappings']['tag_names'][0] if tag == config['mappings']['tag_names'][1]  else tag}"
columns_info = config['columns'][columns_info_filename]

try:
    df_info = helpers.get_information_model_data(tag,isin_df,formatted_date_run,columns_info,data_schedular,model_type,logger) 
    df_info = isin_df.merge(df_info,on='isin',how='left')
    df_info['date'] = pd.to_datetime(formatted_date_run)
    df_info.reset_index(drop=True,inplace=True)
except Exception as e:
    logger.error(f"Error in reading Information Data: {e}")
    df_info = pd.DataFrame(columns=columns_info)
    df_info['isin'] = isin_df['isin'].unique()
    df_info['date'] = pd.to_datetime(formatted_date_run)


columns_finance_filename =  f"columns_finance_{config['mappings']['model_name_2'] if model_type == config['mappings']['model_name_3'] else model_type}_{config['mappings']['tag_names'][0] if tag == config['mappings']['tag_names'][1] else tag}"
columns_finance = config['columns'][columns_finance_filename]

try:
    df_finance = helpers.get_financial_model_data(tag,isin_df,formatted_date_run,columns_finance,data_schedular,logger)
    df_finance = isin_df.merge(df_finance,on='isin',how='left')
    df_finance['date'] = pd.to_datetime(formatted_date_run)
except Exception as e:
    logger.error(f"Error in Reading Financial Data: {e}")
    df_finance = pd.DataFrame(columns=columns_finance)
    df_finance['isin'] = isin_df['isin'].unique()
    df_finance['date'] =  pd.to_datetime(formatted_date_run)


df_finance = helpers.fill_closeprice(df_finance,masters,formatted_date_run,logger)
df_isins_with_no_cp = pd.DataFrame(df_finance[df_finance['closeprice'].isna()]['isin'].unique(),columns=['isin'])
df_isins_with_no_cp['traded'] = df_isins_with_no_cp.apply(lambda x: helpers.get_last_trading_date(x['isin'],formatted_date_run,logger),axis=1)
not_traded_isins = df_isins_with_no_cp[df_isins_with_no_cp['traded']==False]['isin'].unique()

columns_management = config['columns']['columns_man']

try:
    df_management = helpers.get_management_model_data(tag,isin_df,formatted_date_run,columns_management,logger)
    df_management = isin_df.merge(df_management,on='isin',how='left')
    df_management['date'] = pd.to_datetime(formatted_date_run)
except Exception as e:
    logger.error(f"Error in Reading Management Data: {e}")
    df_management = pd.DataFrame(columns=columns_management)
    df_management['isin'] = isin_df['isin'].unique()
    df_management['date'] = pd.to_datetime(formatted_date_run)


try:
    df_lstm = helpers.get_lstm_data(isin_df,formatted_date_run,data_schedular,logger)
    df_lstm = isin_df.merge(df_lstm,on='isin',how='left')
    df_lstm['date'] = pd.to_datetime(formatted_date_run)
except Exception as e:
    logger.error(f"Error in reading LSTM Data: {e}")
    df_lstm = pd.DataFrame(columns=['date','isin','lstm_day1', 'lstm_day2', 'lstm_day3', 'lstm_day4', 'lstm_day5', 'lstm_day6', 'lstm_day7'])
    df_lstm['isin'] = isin_df['isin'].unique()
    df_lstm['date'] =  pd.to_datetime(formatted_date_run)


try:
    df_macro = helpers.get_macro_data(masters,formatted_date_run,logger)
    df_macro = isin_df.merge(df_macro, on='isin', how='left')
    df_macro.reset_index(drop=True, inplace=True)
    df_macro['date'] = pd.to_datetime(formatted_date_run)
except Exception as e:
    logger.error(f"Error in reading Macro Data: {e}")
    df_macro = pd.DataFrame(columns=['date', 'isin', 'predictions_macro'])
    df_macro['isin'] = isin_df['isin'].unique()
    df_macro['date'] =  pd.to_datetime(formatted_date_run)


try:
    df_sector_gain = helpers.get_sector_gain_data(tag,formatted_date_run,formatted_date_run,logger)
    df_sector_gain = masters[['ind_code','isin']].merge(df_sector_gain,on='ind_code',how='left')
    df_sector_gain = isin_df.merge(df_sector_gain,on='isin',how='left')
    df_sector_gain['date'] = pd.to_datetime(formatted_date_run)
    df_sector_gain.reset_index(drop=True,inplace=True)
    df_sector_gain.drop('ind_code',axis=1,inplace=True)
except Exception as e:
    logger.error(f"Error in Getting Sector Gain Data: {e}")
    df_sector_gain = pd.DataFrame(columns=['date','isin','sector_gain','w_weightedAveragePerSector','m_weightedAveragePerSector','q_weightedAveragePerSector'])
    # df_sector_gain = pd.DataFrame(columns=['date','isin','sector_gain','ind_code','w_weightedAveragePerSector','m_weightedAveragePerSector','q_weightedAveragePerSector'])
    df_sector_gain['isin'] = isin_df['isin'].unique()
    df_sector_gain['date'] =  pd.to_datetime(formatted_date_run)


try:
    df_snp = helpers.get_market_sentiment_df(masters,formatted_date_run,logger)
    df_snp = isin_df.merge(df_snp,on='isin',how='left')
    df_snp.reset_index(drop=True,inplace=True)
    df_snp['date'] = pd.to_datetime(formatted_date_run)
except Exception as e:  # Catch specific exception for better debugging
    logger.error(f"Error during concatenation Market Sentiment: {e}")
    df_snp = pd.DataFrame(columns=['date','isin','s_and_p_positive_sentiment_rolling_mean', 's_and_p_negative_sentiment_rolling_mean', 'country_code'])
    df_snp['isin'] = isin_df['isin'].unique()
    df_snp['date'] =  pd.to_datetime(formatted_date_run)


try:
    results = isin_df.parallel_apply(lambda x: conn.get_values_from_es(x['isin'],formatted_date_run,config['er_model_index_path']['er_exclusive_index'],config['er_model_index_path']['schedular_1']),axis=1)
    df_portfolio_ex = pd.concat(results.tolist(), axis=0)
    df_portfolio_ex = isin_df.merge(df_portfolio_ex,on='isin',how='left')
    df_portfolio_ex['date'] = pd.to_datetime(formatted_date_run)
    # df_portfolio_ex.drop([col for col in ['ni_margin', 'year_quarter'] if col in df_portfolio_ex.columns], axis=1, inplace=True)
except Exception as e:
	logger.error(f"Error in reading ER Exclusive Data: {e}")


# final_df=pd.DataFrame(isin_list,columns=['isin'])
final_df=pd.DataFrame(zip(isin_list,[pd.to_datetime(formatted_date_run)]*len(isin_list)),columns=['isin','date'])
final_df=final_df.merge(df_info, on=['isin','date'],how='left')
final_df=final_df.merge(df_finance,on=['isin','date'],how='left')
final_df=final_df.merge(df_lstm,on=['isin','date'],how='left')
final_df=final_df.merge(df_macro,on=['isin','date'],how='left')
final_df=final_df.merge(df_snp,on=['isin','date'],how='left')
final_df=final_df.merge(df_sector_gain,on=['isin','date'],how='left')
final_df=final_df.merge(df_portfolio_ex,on=['isin','date'],how='left')
if model_type==config['mappings']['model_name_1']:
    final_df = final_df.merge(df_management,on=['isin','date'],how='left')


columns_to_convert=[x for x in final_df.columns[~final_df.columns.str.contains('date')]]
columns_to_convert.remove('isin')
final_df[columns_to_convert] = final_df[columns_to_convert].apply(pd.to_numeric, errors='coerce')
columns_to_convert.append('isin')
final_df=final_df[columns_to_convert]


final_df = final_df[final_df['isin'].isin(isin_df['isin'].unique())]
old_df = final_df.copy()
old_df.drop(columns=config['columns']['columns_to_drop'], errors='ignore',inplace=True)


final_df,ffill_summary_df = helpers.ffill_data_df(final_df,adhoc,formatted_date_run,schedular,model_type,tag,logger)
final_df = helpers.get_new_features(final_df,formatted_date_run)


# ### Predictions Calculations

manager = multiprocessing.Manager()
failed_isins = manager.list()


if model_type!=config['mappings']['model_name_3']:
    results= isin_df.parallel_apply(lambda x: helpers.get_daily_run_pred(x['isin'],ts_dict,failed_isins,current_date,model_type,tag,schedular,final_df,logger),axis=1)
else:
    results= isin_df.parallel_apply(lambda x: helpers.get_daily_run_pred_quat(x['isin'],ts_dict,failed_isins,current_date,model_type,tag,schedular,final_df,logger),axis=1)


final_predictions = pd.concat(results.tolist(), axis=0)
final_predictions.reset_index(drop=True, inplace=True)


final_predictions  = isin_df.merge(final_predictions,on=['isin'],how='left')
final_predictions = final_predictions.merge(masters[['isin','ind_code']],on=['isin'],how='left')
final_predictions['sector_code'] = final_predictions['ind_code'].astype(str).str[:2]

prediction_column = next((col for col in final_predictions.columns if re.match(config['mappings']['pattern'], col)), None)
sector_preds = final_predictions.groupby(final_predictions['sector_code'])[prediction_column].mean().reset_index()
sector_pred_map = dict(zip(sector_preds['sector_code'], sector_preds[prediction_column]))


mask = final_predictions[prediction_column].isna() & final_predictions['isin'].isin(no_models_list)
final_predictions.loc[mask, prediction_column] = final_predictions.loc[mask, 'sector_code'].map(sector_pred_map)
final_predictions.loc[mask, 'model_identifier'] = config['mappings']['sector_identifier']


no_cp_isins = final_predictions[final_predictions['closeprice'].isna()]
final_predictions = final_predictions[~final_predictions['isin'].isin(not_traded_isins)]
final_predictions = final_predictions[~final_predictions['closeprice'].isna()]
final_predictions['predictions'] = final_predictions[prediction_column]

try:
    conn.s3_client.write_advanced_as_df(final_predictions,config['version_control']['input_bucket_name'],f'''{config['version_control']['s3_versioning_explainability_input']}/{model_type.lower()}/{schedular.lower()}/{masters_tag.lower()}/{pd.to_datetime(formatted_date_run).strftime("%Y-%m-%d")}/er_data.csv''')
except Exception as e:
    logger.error(f"Failed saving data to eq-model-input due to {e}")

columns_added = list(set(final_predictions.columns).difference(set(old_df.columns)))
columns_added += ['isin']
final_predictions_copy = pd.merge(final_predictions[columns_added],old_df,how='left',on='isin')
final_predictions_copy['date'] = pd.to_datetime(formatted_date_run)
final_predictions_copy['date'] = pd.to_datetime(final_predictions_copy['date'])


helpers.save_records_in_es_s3(model_type,schedular,final_predictions_copy,formatted_date_run,tag,'preds',log_file_path,logger)


failed_isins_list = list(failed_isins)
failed_isins_df = pd.DataFrame(failed_isins_list,columns=['isin','error'])
failed_isins_dir = f'{script_dir}/Failed_Isin_{model_type.capitalize()}_{helpers.schedular_map.get(schedular).capitalize()}_{tag.capitalize()}'
if not os.path.exists(failed_isins_dir):
    os.makedirs(failed_isins_dir)  # Create the directory if it doesn't exist
if failed_isins_df.shape[0] > 0:
    failed_isins_df.to_csv(f'{failed_isins_dir}/failed_isins_{formatted_date_run}.csv', index=False)
    logger.info('The failed ISIN are as follows:')
    for i in failed_isins_list:
        logger.info('ISIN Number: {}'.format(i))
else:
	logger.info('All Preds Done Sucessfully')


# ### Metrics Calculation

manager = multiprocessing.Manager()
failed_metrics_list = manager.list()


results_metrics = isin_df.parallel_apply(lambda x: helpers.calculate_and_save_metrics(x['isin'],formatted_date_run,schedular,failed_metrics_list,model_type,logger),axis=1)
metrics_df = pd.concat(results_metrics.tolist())
metrics_df.reset_index(drop=True,inplace=True)


failed_metrics_list = list(failed_metrics_list)
if len(failed_metrics_list)>0:
    logger.info('The failed ISIN are as follows fro Metrics:')
for i in failed_metrics_list:
    logger.info('ISIN Number: {}'.format(i))


helpers.save_records_in_es_s3(model_type,schedular,metrics_df,formatted_date_run,tag,'metrics',log_file_path,logger)


helpers.send_daily_run_mail(isin_list,schedular,tag,model_type,final_predictions_copy,prediction_column,failed_metrics_list,metrics_df,formatted_date_run,not_traded_isins,logger)


for folder_path in [failed_isins_dir, log_folder_dir]:
    if os.path.exists(folder_path) and os.path.isdir(folder_path):
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
        os.rmdir(folder_path)

