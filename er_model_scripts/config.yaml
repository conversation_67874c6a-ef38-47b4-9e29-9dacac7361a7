snowflake:
        
        snowflake_user : <PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
        snowflake_password : Snow@Common01
        snowflake_account : bgb61827.us-east-1
        snowflake_warehouse : COMPUTE_WH
        snowflake_database : SPGLOBALXPRESSCLOUD_SPGLOBALXPRESSCLOUD_AWS_US_EAST_1_XF_EQUBOT
        snowflake_schema : XPRESSFEED

url:    
        masters_url: http://alb-master-**********.us-east-1.elb.amazonaws.com:8080
        master_inhouse_url: http://52.2.217.192:8080
        snp_url: http://52.2.217.192:8080//stockhistory/getstocksbyisin
        spglobal_url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json

version_control:
        bucket_name: eq-model-output
        input_bucket_name: eq-model-input
        s3_versioning_pred_filename_template: er_model_{model_type}/{schedular}/{date}/predictions/isin.csv
        s3_versioning_metrics_filename_template: er_model_{model_type}/{schedular}/{date}/metrics/isin.csv
        s3_versioning_features_filename_template: er_model_{model_type}/{schedular}/{date}/engineered_features/isin.csv

        ## TODO (Remove_S3_Versions)
        s3_versioning_pred_filename_cat_mon_aieq: er_model_cat/monthly/date/predictions/isin.csv
        s3_versioning_pred_filename_lgb_mon_aieq: er_model_lgb/monthly/date/predictions/isin.csv
        s3_versioning_pred_filename_lgb_mon_aigo: er_model_lgb/monthly/date/predictions/isin.csv
        s3_versioning_pred_filename_xgb_quat_aieq: er_model_xgb/quarterly/date/predictions/isin.csv      
        s3_versioning_pred_filename_cat_mon_india: er_model_cat/monthly/date/predictions/isin.csv
        s3_versioning_pred_filename_xgb_quat_india: er_model_xgb/quarterly/date/predictions/isin.csv
        s3_versioning_pred_filename_lgb_mon_india: er_model_lgb/monthly/date/predictions/isin.csv

        s3_versioning_metrics_filename_cat_mon_aieq: er_model_cat/monthly/date/metrics/isin.csv
        s3_versioning_metrics_filename_lgb_mon_aieq: er_model_lgb/monthly/date/metrics/isin.csv
        s3_versioning_metrics_filename_lgb_mon_aigo: er_model_lgb/monthly/date/metrics/isin.csv
        s3_versioning_metrics_filename_xgb_quat_aieq: er_model_xgb/quarterly/date/metrics/isin.csv      
        s3_versioning_metrics_filename_cat_mon_india: er_model_cat/monthly/date/metrics/isin.csv
        s3_versioning_metrics_filename_xgb_quat_india: er_model_xgb/quarterly/date/metrics/isin.csv
        s3_versioning_metrics_filename_lgb_mon_india: er_model_lgb/monthly/date/metrics/isin.csv

        s3_versioning_explainability_input: explainability_inputs

er_model_index_path:
        bucket_name_lightgbm: portfolio-experiments-test
        bucket_name_india: portfolio-construction
        lightgbm_s3_folder_cat_mon_aieq: aieq_historical_catboost/Monthly/2024/predictions
        lightgbm_s3_folder_lgb_mon_aieq: aieq_historical/Monthly/2024/predictions
        lightgbm_s3_folder_lgb_mon_aigo: aigo_historical/Monthly/2024/predictions
        lightgbm_s3_folder_xgb_quat_aieq: aieq_historical_xgboost/Quarterly/2024/predictions
        lightgbm_s3_folder_cat_mon_india: india_historical_catboost/Monthly/2024/predictions
        lightgbm_s3_folder_xgb_quat_india: india_historical_xgboost/Quarterly/2024/predictions
        lightgbm_s3_folder_lgb_mon_india: india_historical/Monthly/2024/predictions
        lightgbm_s3_folder_lgb_daily_aieq: aieq_historical/Daily/2023/predictions
        new_mnemonic_folder_cat_mon_aieq: aieq_training_catboost/Monthly
        new_mnemonic_folder_lgb_mon_aieq: aieq_training/Monthly
        new_mnemonic_folder_lgb_mon_aigo: aigo_training/Monthly
        new_mnemonic_folder_xgb_quat_aieq: aieq_training_xgboost/Quarterly
        new_mnemonic_folder_cat_mon_india: india_training_catboost/Monthly
        new_mnemonic_folder_xgb_quat_india: india_training_xgboost/Quarterly
        new_mnemonic_folder_lgb_mon_india: india_training/Monthly
        new_mnemonic_folder_india_lgb_mon_india: er_model/india_training/Monthly
        new_mnemonic_folder_lgb_daily_aieq: aieq_training/Daily
        er_folder_cat_mon_aieq : aieq_historical_catboost/Monthly/2024
        er_folder_lgb_mon_aieq: aieq_historical/Monthly/2024
        er_folder_lgb_mon_aigo: aigo_historical/Monthly/2024
        er_folder_xgb_quat_aieq: aieq_historical_xgboost/Quarterly/2024
        er_folder_cat_mon_india: india_historical_catboost/Monthly/2024
        er_folder_xgb_quat_india: india_historical_xgboost/Quarterly/2024
        er_folder_lgb_mon_india: india_historical/Monthly/2024
        er_folder_lgb_daily_aieq: aieq_historical/Daily/2023
        trained_isins_file : trained_isins.csv
        q_file_path : queue_data/q_data.csv
        lgb_pred_index: eq_er_model
        lgb_metric_index: eq_er_model_metrics
        cat_pred_index : eq_cat_er_model
        cat_metric_index : eq_cat_er_model_metrics
        xgb_pred_index : eq_xgb_er_model
        xgb_metric_index : eq_xgb_er_model_metrics
        sg_index: eq_benchmark
        schedular_1 : Monthly
        schedular_2 : Daily
        schedular_3: Quarterly
        information_model_index : eq_information_model
        financial_model_index : eq_financial_model
        lstm_model_index : eq_lstm_model
        macro_model_index : eq_macro_model
        er_exclusive_index : eq_er_exclusive
        management_model_index: eq_management_model
        bucket_info : micro-ops-output
        gserp_path : new_info_model/v2/input_data/daily_run/raw/gserp
        macro_mapping_file : aieq_training/Monthly/config_files/Macro_mapping.csv
        industry_mapping_file: aieq_training/Monthly/config_files/ind_mne_mapping.csv
        ind_name_mapping_file: aieq_training/Monthly/config_files/ind_map.csv 
        period: 22
        period_weekly: 5
        adhoc_data_read_days: 22

snp_creds:
        snp_function: GDSHE
        close_mnemonic: IQ_CLOSEPRICE
        mkp_mnemonic: IQ_MARKETCAP
        period_type: IQ_FY
        frequency: Daily
        head_auth: Basic ************************************************
        content_type: application/json

model_file_names:
        imp_features : overall_imp_features
        feature_dict : overall_feature_dict
        scaler: min_max_scaler
        model_name_1 : final_catboost_model
        model_name_2 : final_lightgbm_model
        model_name_3 : final_xgboost_model

mailing:
        gmail_cred_bucket: "etf-predictions"
        gmail_cred_file: "preportfolio/gmail_credentials/credentials.json"
        app_pass: qtbt iurf onqe wpzr
        my_email: <EMAIL>
        domain: '@quantumstreetai.com'
        scopes: ['https://www.googleapis.com/auth/gmail.send']
        recipient_name: ['tanmay.bhatt','devaraja.s', 'balaji.sridharan','sachin.gowda','shashi.jajware','arun.kumar','sanchit.sayala','nikhil.khetan','shivam.jain']
        recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>','<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
        amit: <EMAIL>
        sandra: <EMAIL>
        shivam: <EMAIL>
        abhinav: <EMAIL>
        sachin: <EMAIL>
        tanmay: <EMAIL>
        low_priority_folder: Features_With_Low_Priority
        high_priority_folder: Features_With_High_Priority
        sender_name: Aman Kumar Jethani <<EMAIL>>
        trigger_mail_recipients: ['<EMAIL>','<EMAIL>']


columns:
       ## TODO (Remove Columns_info_Lgb)
       columns_info_lgb: ['date','isin','eps_current_qr','eps_current_yr','eps_next_qr','eps_next_yr','pos_senti_comp','neg_senti_comp','pos_senti_ind','neg_senti_ind','prediction_info']

       columns_info_lgb_aieq: ['date','isin','eps_current_qr','eps_current_yr','eps_next_qr','eps_next_yr','pos_senti_comp','neg_senti_comp','pos_senti_ind','neg_senti_ind','prediction_info']
       columns_info_lgb_india: ['date','isin','eps_current_qr','eps_current_yr','eps_next_qr','eps_next_yr','pos_senti_comp','neg_senti_comp','pos_senti_ind','neg_senti_ind','prediction_info']
       columns_finance_lgb_aieq: ['date','isin','closeprice', 'volume', 'marketcap', 'tev', 'price_vol_hist_5yr', 'price_vol_hist_2yr', 'price_vol_hist_yr', 'price_vol_hist_6mth', 'price_vol_hist_3mth', 'pe_excl', 'peg_fwd_ciq', 'price_cfps_fwd_ciq', 'pe_excl_fwd', 'price_sales', 'pbv', 'dividend_yield', 'inflation_rate', 'cpi', 'opinion_survey', 'unemployment', 'ppi', 'yield_maturity', 'growth_1d', 'sphq_1d', 'vlue_1d', 'mtum_1d', 'growth_7d', 'sphq_7d', 'vlue_7d', 'mtum_7d', 'growth_30d', 'sphq_30d', 'vlue_30d', 'mtum_30d', 'adx', 'apo', 'cci', 'dx', 'mfi', 'mom', 'ppo', 'rsi', 'ultosc', 'willr', 'terminalroe', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'volume_change_yesterday', 'volume_change_weekly', 'volume_change_monthly', 'cogs', 'current_ratio', 'earning_co_margin', 'ebitda', 'ebitda_margin', 'eps_1yr_ann_growth', 'eps_5yr_ann_cagr', 'gross_margin', 'net_debt_ebitda', 'net_operating_income', 'rd_exp', 'sga_margin', 'dps_5yr_ann_cagr', 'benchmark_closeprice_adj', 'benchmark_volume', 'equity_value_per_share', 'iwf_corr_er', 'iwf_er', 'mtum_corr_er', 'mtum_er', 'sphq_corr_er', 'sphq_er', 'vlue_corr_er', 'vlue_er', 'predictions_f']
       columns_finance_lgb_india: ['date','isin','closeprice', 'volume', 'marketcap', 'tev', 'price_vol_hist_5yr', 'price_vol_hist_2yr', 'price_vol_hist_yr', 'price_vol_hist_6mth', 'price_vol_hist_3mth', 'pe_excl', 'peg_fwd_ciq', 'price_cfps_fwd_ciq', 'pe_excl_fwd', 'price_sales', 'pbv', 'dividend_yield', 'inflation_rate', 'cpi', 'unemployment', 'yield_maturity', 'growth_1d', 'sphq_1d', 'vlue_1d', 'mtum_1d', 'growth_7d', 'sphq_7d', 'vlue_7d', 'mtum_7d', 'growth_30d', 'sphq_30d', 'vlue_30d', 'mtum_30d', 'adx', 'apo', 'cci', 'dx', 'mfi', 'mom', 'ppo', 'rsi', 'ultosc', 'willr', 'terminalroe', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'volume_change_yesterday', 'volume_change_weekly', 'volume_change_monthly', 'cogs', 'current_ratio', 'earning_co_margin', 'ebitda', 'ebitda_margin', 'eps_1yr_ann_growth', 'eps_5yr_ann_cagr', 'gross_margin', 'net_debt_ebitda', 'net_operating_income', 'rd_exp', 'sga_margin', 'tic', 'dps_5yr_ann_cagr', 'benchmark_closeprice_adj', 'benchmark_volume', 'equity_value_per_share', 'iwf_corr_er', 'iwf_er', 'mtum_corr_er', 'mtum_er', 'sphq_corr_er', 'sphq_er', 'vlue_corr_er', 'vlue_er', 'predictions_f']
       columns_info_cat_aieq: ['age_year', 'eps_current_qr', 'eps_current_yr', 'eps_next_yr', 'eps_next_qr', 'high_price', 'low_price', 'open_price', 'pos_senti_ind', 'neg_senti_ind', 'overall_pos_sentiment_innovation', 'overall_neg_sentiment_innovation', 'pos_sentiment_innovation', 'neg_sentiment_innovation', 'average_mentions_innovation', 'average_evidence_innovation', 'overall_pos_sentiment_merger', 'overall_neg_sentiment_merger', 'pos_sentiment_merger', 'neg_sentiment_merger', 'average_mentions_merger', 'average_evidence_merger', 'overall_pos_sentiment_sales', 'overall_neg_sentiment_sales', 'pos_sentiment_sales', 'neg_sentiment_sales', 'average_mentions_sales', 'average_evidence_sales', 'overall_pos_sentiment_growth', 'overall_neg_sentiment_growth', 'pos_sentiment_growth', 'neg_sentiment_growth', 'average_mentions_growth', 'average_evidence_growth', 'pos_senti_comp', 'neg_senti_comp', 'pos_sentiment_general', 'neg_sentiment_general', 'average_mentions_general', 'average_evidence_general', 'overall_pos_sentiment_litigation', 'overall_neg_sentiment_litigation', 'pos_sentiment_litigation', 'neg_sentiment_litigation', 'average_mentions_litigation', 'average_evidence_litigation', 'open_change_Monthly', 'high_change_Monthly', 'low_change_Monthly', 'date', 'isin']
        
       columns_finance_cat_aieq: ['mp1d', 'mp1w', 'mp1m', 'mp1q', 'mp6m','mp1y',"date","isin","closeprice","volume","marketcap","tev","sharesoutstanding","price_vol_hist_5yr","price_vol_hist_2yr","price_vol_hist_yr","price_vol_hist_6mth","price_vol_hist_3mth","pe_excl","peg_fwd_ciq","price_cfps_fwd_ciq","pe_excl_fwd","price_sales","pbv","dividend_yield","inflation_rate","cpi","opinion_survey","unemployment","ppi","yield_maturity","growth_1d","sphq_1d","vlue_1d","mtum_1d","growth_7d","sphq_7d","vlue_7d","mtum_7d","growth_30d","sphq_30d","vlue_30d","mtum_30d","adx","apo","cci","dx","mfi","mom","ppo","rsi","ultosc","willr","macd_0","macd_1","macd_2","macdfix_0","macdfix_1","macdfix_2","stoch_0","stoch_1","stochf_0","stochf_1","stochrsi_0","stochrsi_1","terminalroe","valprice","close_change_yesterday","close_change_weekly","close_change_monthly","volume_change_yesterday","volume_change_weekly","volume_change_monthly","average_market_cap","ar_turns","asset_turns","cash_st_invest","cogs","current_ratio","earning_co_margin","ebit_int","ebit_margin","ebitda","ebitda_capex_int","ebitda_margin","effect_tax_rate","eps_1yr_ann_growth","eps_5yr_ann_cagr","est_act_return_assets_ciq","fixed_asset_turns","gross_margin","inventory_turns","lt_debt","lt_debt_equity","minority_interest","net_debt_ebitda","net_operating_income","ni_margin","payout_ratio","pref_equity","quick_ratio","rd_exp","re","return_assets","return_capital","return_equity","sga","sga_margin","tbv_5yr_ann_cagr","total_common_equity","total_debt","total_debt_capital","total_debt_equity","total_rev","total_rev_1yr_ann_growth","total_rev_3yr_ann_cagr","total_rev_5yr_ann_cagr","total_rev_employee","total_rev_share","ufcf_3yr_ann_cagr","ufcf_5yr_ann_cagr","unlevered_fcf","beta_1yr","beta_2yr","beta_5yr","dps_5yr_ann_cagr","curr_foreign_taxes","capex_pct_rev","benchmark_closeprice_adj","benchmark_volume","equity_value_per_share","iwf_corr_er","iwf_er","mtum_corr_er","mtum_er","sphq_corr_er","sphq_er","vlue_corr_er","vlue_er"]
       columns_info_cat_india: ["gserp_pos_senti_comp","gserp_neg_senti_comp","age_year","eps_current_qr","eps_current_yr","eps_next_yr","eps_next_qr","high_price","low_price","open_price","pos_senti_ind","neg_senti_ind","overall_pos_sentiment_innovation","overall_neg_sentiment_innovation","pos_sentiment_innovation","neg_sentiment_innovation","average_mentions_innovation","average_evidence_innovation","overall_pos_sentiment_merger","overall_neg_sentiment_merger","pos_sentiment_merger","neg_sentiment_merger","average_mentions_merger","average_evidence_merger","overall_pos_sentiment_sales","overall_neg_sentiment_sales","pos_sentiment_sales","neg_sentiment_sales","average_mentions_sales","average_evidence_sales","overall_pos_sentiment_growth","overall_neg_sentiment_growth","pos_sentiment_growth","neg_sentiment_growth","average_mentions_growth","average_evidence_growth","pos_senti_comp","neg_senti_comp","pos_sentiment_general","neg_sentiment_general","average_mentions_general","average_evidence_general","overall_pos_sentiment_litigation","overall_neg_sentiment_litigation","pos_sentiment_litigation","neg_sentiment_litigation","average_mentions_litigation","average_evidence_litigation","open_change_Monthly","high_change_Monthly","low_change_Monthly","date","isin"]
       columns_finance_cat_india: ['mp1d', 'mp1w', 'mp1m', 'mp1q', 'mp6m','mp1y',"date","isin","closeprice","volume","marketcap","tev","sharesoutstanding","price_vol_hist_5yr","price_vol_hist_2yr","price_vol_hist_yr","price_vol_hist_6mth","price_vol_hist_3mth","pe_excl","peg_fwd_ciq","price_cfps_fwd_ciq","pe_excl_fwd","price_sales","pbv","dividend_yield","inflation_rate","cpi","unemployment","yield_maturity","growth_1d","sphq_1d","vlue_1d","mtum_1d","growth_7d","sphq_7d","vlue_7d","mtum_7d","growth_30d","sphq_30d","vlue_30d","mtum_30d","adx","apo","cci","dx","mfi","mom","ppo","rsi","ultosc","willr","macd_0","macd_1","macd_2","macdfix_0","macdfix_1","macdfix_2","stoch_0","stoch_1","stochf_0","stochf_1","stochrsi_0","stochrsi_1","terminalroe","valprice","close_change_yesterday","close_change_weekly","close_change_monthly","volume_change_yesterday","volume_change_weekly","volume_change_monthly","average_market_cap","ar_turns","asset_turns","cash_st_invest","cogs","current_ratio","earning_co_margin","ebit_int","ebit_margin","ebitda","ebitda_capex_int","ebitda_margin","effect_tax_rate","eps_1yr_ann_growth","eps_5yr_ann_cagr","est_act_return_assets_ciq","fixed_asset_turns","gross_margin","inventory_turns","lt_debt","lt_debt_equity","minority_interest","net_debt_ebitda","net_operating_income","ni_margin","payout_ratio","pref_equity","quick_ratio","rd_exp","re","return_assets","return_capital","return_equity","sga","sga_margin","tbv_5yr_ann_cagr","total_common_equity","total_debt","total_debt_capital","total_debt_equity","total_rev","total_rev_1yr_ann_growth","total_rev_3yr_ann_cagr","total_rev_5yr_ann_cagr","total_rev_employee","total_rev_share","ufcf_3yr_ann_cagr","ufcf_5yr_ann_cagr","unlevered_fcf","beta_1yr","beta_2yr","beta_5yr","dps_5yr_ann_cagr","curr_foreign_taxes","capex_pct_rev","benchmark_closeprice_adj","benchmark_volume","equity_value_per_share","iwf_corr_er","iwf_er","mtum_corr_er","mtum_er","sphq_corr_er","sphq_er","vlue_corr_er","vlue_er"]
       columns_man: ['date','isin','predictions_m']
       req_columns: ["date", "isin", "actual_monthly_returns", "monthly_predictions"]
       all_metrics: ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "confidence_score", "avg_confidence_score", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day","confidence_score_14_day"]
       columns_not_needed_metrics: ['date','isin','actual_monthly_return','actual_monthly_return_predictions','ind_code','schedular','updated_at','tic']
       columns_to_drop : ['short_interest', 'short_interest_percent']
       q_mnems: ['IQ_REVENUE_EST_CIQ','IQ_EST_EPS_NORM_SURPRISE_PERCENT_CIQ']
       daily_mnems : ['IQ_TEV_UFCF','IQ_TEV_TOTAL_REV','IQ_TEV_TOTAL_REV_FWD','IQ_TEV_EBITDA','IQ_TEV_EBITDA_FWD','IQ_TEV_EBIT','IQ_TEV_EBIT_FWD','IQ_MKTCAP_TOTAL_REV','IQ_MKTCAP_TOTAL_REV_FWD','IQ_MKTCAP_EBT_EXCL','IQ_PE_NORMALIZED']
       tags: ['aieq','aigo','india']
       columns_fin_ffill_script: ['s_and_p_negative_sentiment_rolling_mean','s_and_p_positive_sentiment_rolling_mean']
       columns_technical_indicators_ffill_script: ['CDLBREAKAWAY', 'CDLHANGINGMAN', 'CDLINVERTEDHAMMER', 'CDLSHOOTINGSTAR']
       columns_sector_ffill_script: ['m_weightedAveragePerSector', 'q_weightedAveragePerSector', 'w_weightedAveragePerSector', 'sector_gain']
       columns_lstm_ffill_script: ['lstm_day1', 'lstm_day2', 'lstm_day3', 'lstm_day4', 'lstm_day5', 'lstm_day6', 'lstm_day7']
       columns_macro_ffill_script: ['predictions_macro']
       date_columns_and_extras_ffill_script: ['actual_monthly_return','date_quarter','date_month_sin','date_day','date_dayofyear','date_day_sin','date_week', 'date_week_sin','date_dayofweek', 'date_year', 'date_month', 'date_year_sin','date_dayofyear_sin', 'ind_code', 'identifier', 'date','isin', 'updated_at','schedular']
       columns_info_ffill_script: ['gserp_neg_senti_comp', 'gserp_pos_senti_comp']


mappings:
        sector_dict : {
                            10: "Energy",
                            15: "Materials",
                            20: "Industrials",
                            25: "Consumer Discretionary",
                            30: "Consumer Staples",
                            35: "Health Care",
                            40: "Financials",
                            45: "Information Technology",
                            50: "Telecommunication Services",
                            55: "Utilities",
                            60: "Real Estate"
                        }
        pattern : '^actual_.+?_.+?_prediction.+'
        sector_identifier: 'sector-average'
        tag_mapping: {
                            'aieq':'aieq',
                            'aigo':'aigo',
                            'indt1':'india',
                            'indeq':'india',
                            'indsec':'india'
                     }
        index_sg_map:  {
                            'aieq': 'eq_benchmark_aieq',
                            'aigo': 'eq_benchmark_aigo',
                            'india': 'eq_benchmark_india',
                            'default': 'eq_benchmark'
                        }

        schedular_map : {
                            'Monthly': 'mon',
                            'Daily': 'daily',
                            'Quarterly': 'quat'
                        }

        metrics_period_map : {
                                   'Daily': 1,
                                   'Weekly': 5,
                                   'Quarterly': 66
                              }
        model_name_1: 'lgb'
        model_name_2: 'cat'
        model_name_3: 'xgb'
        tag_names: ['aieq','aigo','indt1','indeq','tier1','indsec','aifint']
        quarter_name: Q4



        

        


       

 