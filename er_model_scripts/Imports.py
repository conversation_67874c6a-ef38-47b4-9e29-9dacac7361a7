import argparse
import configparser
import pandas as pd
import numpy as np
import lightgbm as lgb
import os,ssl
import pickle
import botocore
from tqdm.notebook import tqdm
from io import BytesIO
from multiprocessing import Pool, Manager
import datetime
import ast
import time
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import multiprocessing
from pandarallel import pandarallel
pandarallel.initialize(progress_bar=True,nb_workers=12)
from io import StringIO
import requests
from pandas.tseries.offsets import BDay
import logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
from datetime import date,timedelta
from pathlib import Path
from urllib.request import urlopen, Request
from concurrent.futures import ThreadPoolExecutor
from io import StringIO
import boto3
import json
import warnings
warnings.filterwarnings("ignore")
import concurrent
from multiprocessing.pool import ThreadPool
import traceback
import math
from fredapi import Fred
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from getpass import getpass
from opensearchpy.connection.http_requests import RequestsHttpConnection
from aws_requests_auth.aws_auth import AWSRequestsAuth
from opensearchpy.exceptions import NotFoundError
from eq_common_utils.utils.config.es_config import es_config
from eq_common_utils.utils.config.s3_config import s3_config
from numpy_ext import rolling_apply as rolling_apply_ext
import sys
import subprocess
import retrying
import snowflake.connector
if (not os.environ.get('PYTHONHTTPSVERIFY', '') and getattr(ssl, '_create_unverified_context', None)):
    ssl._create_default_https_context = ssl._create_unverified_context

from ast import literal_eval
from copulas.multivariate import GaussianMultivariate
import catboost
import yaml
import re
import base64
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText
from email.message import EmailMessage 
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))
with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)
from collections import defaultdict
from eq_common_utils.utils.config.metrics_config import metrics_config

# In[ ]:


class DataProcessor:
    def __init__(self):
        self.s3_client = s3_config()
        self.es_client = es_config(env='prod')
        self.masters_url = config['url']['masters_url']
        self.snp_url = config['url']['snp_url']
        self.spglobal_url = config['url']['spglobal_url']
        self.snowflake_user = config['snowflake']['snowflake_user']
        self.snowflake_password = config['snowflake']['snowflake_password']
        self.snowflake_account = config['snowflake']['snowflake_account']
        self.snowflake_warehouse = config['snowflake']['snowflake_warehouse']
        self.snowflake_database = config['snowflake']['snowflake_database']
        self.snowflake_schema =  config['snowflake']['snowflake_schema']
        self.snp_function = config['snp_creds']['snp_function']
        self.close_mnemonic = config['snp_creds']['close_mnemonic']
        self.mkp_mnemonic = config['snp_creds']['mkp_mnemonic']
        self.period_type = config['snp_creds']['period_type']
        self.frequency = config['snp_creds']['frequency']
        self.head_auth = config['snp_creds']['head_auth']
        self.content_type = config['snp_creds']['content_type']
        self.inhouse_master_url =  config['url']['master_inhouse_url']

    def bulk_s32df(self, bucket_list, file_list):
        assert isinstance(bucket_list, list)
        assert isinstance(file_list, list)
        assert len(bucket_list) == len(file_list)
    
        def safe_read(bucket, file):
            try:
                return self.s3_client.read_as_dataframe(bucket, file)
            except Exception as e:
                print(f"Failed to read {file} from {bucket}: {e}")
                return None
    
        try:
            with ThreadPoolExecutor(max_workers=50) as executor:
                results = list(tqdm(
                    executor.map(safe_read, bucket_list, file_list),
                    total=len(bucket_list),
                    desc="S3 bulk file read",
                ))
    
            # Filter out any None values (i.e., failed reads)
            results = [r for r in results if r is not None]
    
            if results:
                return pd.concat(results)
            else:
                return pd.DataFrame()  # Return empty df if all failed
    
        except Exception as e:
            print(traceback.print_exc())
            raise e

    def df2s3(self, df, bucket_name, file_name):
        s3_client = self.s3_client._s3Client
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index = False)
        s3_client.put_object(Bucket=bucket_name,
                            Key=file_name,
                            Body=csv_buffer.getvalue()
                            )
        return True

    def read_pickle(self, bucket_name, key):
        response = self.s3_client._s3Client.get_object(Bucket=bucket_name, Key=key)
        pickle_data = response['Body'].read()
        return pickle.loads(pickle_data)

    def get_es_data(self, isin, dates, index_prefix):
        try:
            data = []
            for year in range(dates[0], dates[1] + 1):
                q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
                try:
                    # Run the query on Elasticsearch for the given year
                    result = self.es_client.run_query(query=json.loads(q_total), index=f"{index_prefix}_{year}")
                    for rs in result.get('hits', {}).get('hits', []):
                        es_data = rs.get('_source', {})
                        if es_data:  # Add data only if _source exists
                            data.append(es_data)
                except Exception as e:
                    print(f"Failed to query Elasticsearch for year {year}: {e}", exc_info=True)

            # Convert the collected data to a DataFrame
            if data:
                df = pd.DataFrame(data)
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'], errors='coerce')  # Handle parsing errors
                    df.sort_values('date', ascending=True, inplace=True)
                    df.reset_index(drop=True, inplace=True)
                else:
                    print("No 'date' column found in the Elasticsearch data.")
                return df
            else:
                return pd.DataFrame()  # Return an empty DataFrame if no data
        except Exception as e:
            print(f"An error occurred in get_es_data: {e}", exc_info=True)
            return pd.DataFrame()  # Return an empty DataFrame on general failure

    def get_es_data_by_date(self, date, index_prefix):
        try:
            data = []
            # Format the date if it's not already in string format
            date_str = pd.to_datetime(date).strftime('%Y-%m-%d')

            # Query to match the specified date only
            q_total = '{"query":{"bool": {"must":[{"match":{"date":"'+date_str+'"}}]}}}'
            
            # Loop through indices, assuming they are formatted as 'index_prefix_YEAR'
            year = pd.to_datetime(date).year
            index_list = [f"{index_prefix}_{year}"]  # Add any other index names if needed

            for index in index_list:
                try:
                    # Run the query on the current index
                    result = self.es_client.run_query(query=json.loads(q_total), index=index)
                    # Collect the data from the result
                    for rs in result['hits']['hits']:
                        es_data = rs['_source']
                        data.append(es_data)
                except Exception as e:
                    print(f"Error querying index {index}: {e}")
                    continue

            # Convert the data to a DataFrame
            df = pd.DataFrame(data)
            if not df.empty:
                df['date'] = pd.to_datetime(df['date'])
                df.sort_values('date', ascending=True, inplace=True)
                df.reset_index(inplace=True, drop=True)

            return df
        except Exception as e:
            print(f"Error in get_es_data: {e}")
            return pd.DataFrame()  # Return empty DataFrame if an error occurs


        
    def get_values_from_es(self, isin, date_needed, index, schedular):
        year = pd.to_datetime(date_needed).date().year
        try:
            df = self.get_es_data(isin, [year, year], f'{index}')
            df = df[df['schedular'] == f'{schedular}']
            df = df[pd.to_datetime(df['date']) == pd.to_datetime(date_needed)]
            return df
        except:
            print("Data not there for ISIN", isin)


    @staticmethod
    def previous_business_day(date):
        date_range = pd.date_range(start=date, periods=2)
        previous = date_range - pd.tseries.offsets.BDay(1)
        return previous[0]

    def load_config(self,filepath):
        config = configparser.ConfigParser()
        config.read(filepath)
        return config


    def get_master_df(self,tag):
        master_json = requests.get(f'{self.masters_url}/masteractivefirms/getmasterbytag?tag={tag}').json()
        master = pd.DataFrame(master_json["data"][f"masteractivefirms_{tag}"])
        return master

    def get_inhouse_master_df(self,tag):
        master_json = requests.get(f"{self.inhouse_master_url}/masteractivefirms/getmasterbytag?tag={tag}").json()
        masters = pd.DataFrame(master_json["data"][f"masteractivefirms_{tag}"])
        return masters
   

    def save_row(self, idx, row, bucket_name, filename):
        # s3_client = s3_config()
        row_df = row.to_frame().T
        isin  = row_df['isin'].iloc[0]
        filename = filename.replace('isin',isin)                             
        # s3_client.write_advanced_as_df(row_df, bucket_name, filename)
        self.df2s3(row_df, bucket_name, filename)

    def get_es_data_all(self,start_date,end_date, index_prefix,schedular,isin_list):
        start_year = (pd.to_datetime(start_date)).year
        end_year = (pd.to_datetime(end_date)).year
        try:
            data = []
            hits = []
            for year in range(start_year, end_year + 1):
                q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date,
                    "lte": end_date}}},{"term": {"schedular.keyword": f"{schedular}"}}]}}}
                try:
                    response,total_docs = self.es_client.search_with_pagination(index=f"{index_prefix}_{year}",query=q_total,paginate=False,strict=False)
                except Exception as e:
                    pass
                for hit in response:
                    es_data=hit['_source']
                    data.append(es_data)
            df=pd.DataFrame(data)
            df['date']=pd.to_datetime(df['date'])
            df.sort_values('date', ascending=True, inplace=True)
            df.reset_index(inplace=True, drop=True)
            return df
        except Exception as e:
            print(e)
        


    def get_stock_price(self,isin,master,start_date, end_date='03/31/2028' , get_from_api=False):
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").strftime("%m/%d/%Y")
        tic = master[master['isin']==isin]['tic'].unique()[0]
        exchange = master[master['isin']==isin]['exchange'].unique()[0]
        
            
        if get_from_api:
            start_date1 = start_date[-4:] + '-'+ start_date[:2] + '-' + start_date[3:5]
            end_date1 = end_date[-4:] + '-' + end_date[:2] + '-' + end_date[3:5]
            print(f'start_date1 and end_date1 are {start_date1} and {end_date1}')

            stock_url = f'{self.snp_url}?isin={isin}&startDate={start_date1}&endDate={end_date1}&country_code="USA"'
            data_api = requests.get(stock_url).json()
            try:
                df_api = pd.DataFrame(data_api["data"]["stocks"])[['date', 'close']]
                df_api.sort_values(by='date', inplace=True)
                df_api.rename(columns={'close':'adj_close'}, inplace=True)
                df_api['adj_close'] = df_api['adj_close'].map(float)
            except Exception as e:
                print(f'for isin: {isin}, error is {e}')
                df_api = pd.DataFrame(columns=['date', 'adj_close'])            
            return df_api
        else:

            data = {    
                "inputRequests": 
                [
                    {
                        "function": self.snp_function,
                        "identifier": f'{tic}:{exchange}',
                        "mnemonic": self.close_mnemonic,
                        "properties": 
                        {
                            "periodType": self.period_type,
                            "startDate":start_date,
                            "endDate":end_date,
                            "frequency":self.frequency
                        }
                    }
                ]
            }
            data_json = json.dumps(data)

            url = self.spglobal_url
            headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
            response = requests.post(url, data=data_json, headers=headers)
            try:
                op=json.loads(response.text)
                if 'Rows' not in op['GDSSDKResponse'][0]:    
        #     if ('GDSSDKResponse' not in op) or ('Rows' not in op['GDSSDKResponse'][0]):
                    data = {    
                        "inputRequests": 
                        [
                            {
                                "function": self.snp_function,
                                "identifier": f'{isin}',
                                "mnemonic": self.close_mnemonic,
                                "properties": 
                                {
                                "periodType": self.period_type,
                                "startDate":start_date,
                                "endDate":end_date,
                                "frequency":self.frequency
                                }
                            }
                        ]
                    }
                    data_json = json.dumps(data)

                    url = self.spglobal_url
                    headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
                    response = requests.post(url, data=data_json, headers=headers)
                    op=json.loads(response.text)

                df_snp = pd.DataFrame([row['Row'] for row in op['GDSSDKResponse'][0]['Rows']], columns=['closeprice', 'date'])
                df_snp['closeprice'] = df_snp['closeprice'].map(float)
                df_snp['date'] = pd.to_datetime(df_snp['date']).dt.strftime('%Y-%m-%d') 
                df_snp['isin'] = isin
                return df_snp
            except Exception as e:
                
                print(f"isin: {isin}, tic:{tic}, exchange:{exchange}, start:{start_date}, end:{end_date}, exception:{e}")
                return pd.DataFrame(columns=['closeprice', 'date'])


    # In[ ]:




    def get_mnemonic_data(self,mnemonic,isin,master,start_date, end_date='03/31/2028'):
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").strftime("%m/%d/%Y")
        tic = master[master['isin']==isin]['tic'].unique()[0]
        exchange = master[master['isin']==isin]['exchange'].unique()[0]

        data = {    
            "inputRequests": 
            [
                {
                    "function": self.snp_function,
                    "identifier": f'{tic}:{exchange}',
                    "mnemonic": mnemonic,
                    "properties": 
                    {
                        "periodType": self.period_type,
                        "startDate":start_date,
                        "endDate":end_date,
                        "frequency":self.frequency
                    }
                }
            ]
        }
        data_json = json.dumps(data)

        url = self.spglobal_url
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
        response = requests.post(url, data=data_json, headers=headers)
        try:
            op=json.loads(response.text)
            if 'Rows' not in op['GDSSDKResponse'][0]:    
    #     if ('GDSSDKResponse' not in op) or ('Rows' not in op['GDSSDKResponse'][0]):
                data = {    
                    "inputRequests": 
                    [
                        {
                            "function": self.snp_function,
                            "identifier": f'{isin}',
                            "mnemonic": mnemonic,
                            "properties": 
                            {
                            "periodType": self.period_type,
                            "startDate":start_date,
                            "endDate":end_date,
                            "frequency":self.frequency
                            }
                        }
                    ]
                }
                data_json = json.dumps(data)

                url = self.spglobal_url
                headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
                response = requests.post(url, data=data_json, headers=headers)
                op=json.loads(response.text)
            column_name = f"{mnemonic.split('_')[-1].lower()}"
            df = pd.DataFrame([row['Row'] for row in op['GDSSDKResponse'][0]['Rows']], columns=[column_name, 'date'])
            df[column_name] = df[column_name].map(float)
            df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d') 
            df['isin'] = isin
            return df
        except Exception as e:
            print(f"isin: {isin}, tic:{tic}, exchange:{exchange}, start:{start_date}, end:{end_date}, exception:{e}")
            return pd.DataFrame(columns=[f"{mnemonic.split('_')[-1].lower()}", 'date'])



    def get_cp_mc_values(self,isin,master,start_date, end_date='03/31/2028'):
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").strftime("%m/%d/%Y")
        country_code = master[master['isin']==isin]['country_code'].unique()[0]
            
        start_date1 = start_date[-4:] + '-'+ start_date[:2] + '-' + start_date[3:5]
        end_date1 = end_date[-4:] + '-' + end_date[:2] + '-' + end_date[3:5]
        # print(f'start_date1 and end_date1 are {start_date1} and {end_date1}')

        stock_url = f'{self.inhouse_master_url}/stockhistory/getstocksbyisin?isin={isin}&startDate={start_date1}&endDate={end_date1}&countrycode={country_code}'
        data_api = requests.get(stock_url).json()
        try:
            df_api = pd.DataFrame(data_api["data"]["stocks"])
            df_api.sort_values(by='date', inplace=True)
        except Exception as e:
            print(f'for isin: {isin}, error is {e}')
            df_api = pd.DataFrame()            
        return df_api
