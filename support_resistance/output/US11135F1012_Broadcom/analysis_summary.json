{"analysis_info": {"csv_file": "US11135F1012.csv", "company_name": "Broadcom Inc", "company_name_clean": "Broadcom", "analysis_date": "2025-07-16T17:47:40.709404", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 128.5, "max": 283.36, "current": 280.94}}, "support_resistance_levels": {"volume_profile": {"support": 165.656173, "resistance": 191.225179}, "kmeans": {"support": 162.92065445544557, "resistance": 241.81142816091955}, "kde": {"support": 171.3151851851852, "resistance": 171.46}}, "fibonacci_levels": {"0.0": 283.36, "0.236": 246.81304, "0.382": 224.20348, "0.5": 205.93, "0.618": 187.65652, "0.786": 161.64004, "1.0": 128.5}, "signal_summaries": {"volume_profile": {"total_buy_signals": 11, "total_sell_signals": 10, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 165.656173, "resistance_level": 191.225179, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 7, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 162.92065445544557, "resistance_level": 241.81142816091955, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 11, "total_sell_signals": 15, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 171.3151851851852, "resistance_level": 171.46, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}