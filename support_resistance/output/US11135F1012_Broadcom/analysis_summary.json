{"analysis_info": {"csv_file": "US11135F1012.csv", "company_name": "Broadcom Inc", "company_name_clean": "Broadcom", "analysis_date": "2025-07-16T16:16:33.854767", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 30.418, "max": 283.36, "current": 280.94}}, "support_resistance_levels": {"volume_profile": {"support": 48.197449, "resistance": 58.198277000000004}, "kmeans": {"support": 56.44803225844004, "resistance": 234.82322971698113}, "kde": {"support": 51.37976376376376, "resistance": 51.63037537537538}}, "fibonacci_levels": {"0.0": 283.36, "0.236": 223.66568800000002, "0.382": 186.736156, "0.5": 156.889, "0.618": 127.041844, "0.786": 84.54758799999999, "1.0": 30.418}, "signal_summaries": {"volume_profile": {"total_buy_signals": 37, "total_sell_signals": 28, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 48.197449, "resistance_level": 58.198277000000004, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 21, "total_sell_signals": 8, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 56.44803225844004, "resistance_level": 234.82322971698113, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 18, "total_sell_signals": 12, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 51.37976376376376, "resistance_level": 51.63037537537538, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}