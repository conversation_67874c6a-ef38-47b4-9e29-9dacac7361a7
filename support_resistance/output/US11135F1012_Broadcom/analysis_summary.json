{"analysis_info": {"csv_file": "US11135F1012.csv", "company_name": "Broadcom Inc", "company_name_clean": "Broadcom", "analysis_date": "2025-07-16T15:38:10.822142", "data_points": 4009, "date_range": {"start": "2009-08-06 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 1.433, "max": 283.36, "current": 280.94}}, "support_resistance_levels": {"volume_profile": {"support": 4.250917, "resistance": 26.591586333333332}, "kmeans": {"support": 13.317741259758698, "resistance": 177.91726318716218}, "kde": {"support": 8.443269269269269, "resistance": 8.72304004004004}}, "fibonacci_levels": {"0.0": 283.36, "0.236": 216.825228, "0.382": 175.663886, "0.5": 142.3965, "0.618": 109.12911400000002, "0.786": 61.765378, "1.0": 1.433}, "signal_summaries": {"volume_profile": {"total_buy_signals": 3, "total_sell_signals": 31, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 4.250917, "resistance_level": 26.591586333333332, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 15, "total_sell_signals": 20, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 13.317741259758698, "resistance_level": 177.91726318716218, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 4, "total_sell_signals": 8, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 8.443269269269269, "resistance_level": 8.72304004004004, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}