{"analysis_info": {"csv_file": "US46625H1005.csv", "company_name": "JPMorgan Chase & Co", "company_name_clean": "JPMorgan_Chase", "analysis_date": "2025-07-16T17:19:37.052882", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 91.38, "max": 296.4, "current": 286.55}}, "support_resistance_levels": {"volume_profile": {"support": 139.18403333333333, "resistance": 155.32456666666667}, "kmeans": {"support": 124.12540813609466, "resistance": 240.0511748069498}, "kde": {"support": 146.39549549549548, "resistance": 146.59903903903904}}, "fibonacci_levels": {"0.0": 296.4, "0.236": 248.01528, "0.382": 218.08236, "0.5": 193.89, "0.618": 169.69763999999998, "0.786": 135.25428, "1.0": 91.38}, "signal_summaries": {"volume_profile": {"total_buy_signals": 35, "total_sell_signals": 29, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 139.18403333333333, "resistance_level": 155.32456666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 124.12540813609466, "resistance_level": 240.0511748069498, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 25, "total_sell_signals": 28, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 146.39549549549548, "resistance_level": 146.59903903903904, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}