{"analysis_info": {"csv_file": "US46625H1005.csv", "company_name": "JPMorgan Chase & Co", "company_name_clean": "JPMorgan_Chase", "analysis_date": "2025-07-16T15:35:09.216066", "data_points": 8462, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 6.166667, "max": 296.4, "current": 286.55}}, "support_resistance_levels": {"volume_profile": {"support": 32.20843898999999, "resistance": 43.739350096666655}, "kmeans": {"support": 38.14116510399458, "resistance": 225.96525872252744}, "kde": {"support": 40.76476476476476, "resistance": 41.0548048048048}}, "fibonacci_levels": {"0.0": 296.4, "0.236": 227.*********, "0.382": 185.*********, "0.5": 151.2833335, "0.618": 117.*********, "0.786": 68.27660026199999, "1.0": 6.166667}, "signal_summaries": {"volume_profile": {"total_buy_signals": 39, "total_sell_signals": 99, "total_breakouts": 9, "total_breakdowns": 4, "support_level": 32.20843898999999, "resistance_level": 43.739350096666655, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 115, "total_sell_signals": 4, "total_breakouts": 1, "total_breakdowns": 17, "support_level": 38.14116510399458, "resistance_level": 225.96525872252744, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 122, "total_sell_signals": 121, "total_breakouts": 12, "total_breakdowns": 10, "support_level": 40.76476476476476, "resistance_level": 41.0548048048048, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}