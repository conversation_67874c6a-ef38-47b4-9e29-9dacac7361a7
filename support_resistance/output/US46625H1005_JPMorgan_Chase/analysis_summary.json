{"analysis_info": {"csv_file": "US46625H1005.csv", "company_name": "JPMorgan Chase & Co", "company_name_clean": "JPMorgan_Chase", "analysis_date": "2025-07-16T17:47:25.307919", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 190.9, "max": 296.4, "current": 286.55}}, "support_resistance_levels": {"volume_profile": {"support": 209.07197166666668, "resistance": 243.2578496666667}, "kmeans": {"support": 214.2090327586207, "resistance": 271.22321575342465}, "kde": {"support": 243.07177177177178, "resistance": 243.17297297297299}}, "fibonacci_levels": {"0.0": 296.4, "0.236": 271.502, "0.382": 256.099, "0.5": 243.64999999999998, "0.618": 231.201, "0.786": 213.47699999999998, "1.0": 190.9}, "signal_summaries": {"volume_profile": {"total_buy_signals": 14, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 209.07197166666668, "resistance_level": 243.2578496666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 11, "total_sell_signals": 2, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 214.2090327586207, "resistance_level": 271.22321575342465, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 21, "total_sell_signals": 19, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 243.07177177177178, "resistance_level": 243.17297297297299, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}