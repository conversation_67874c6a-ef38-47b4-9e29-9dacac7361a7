{"analysis_info": {"csv_file": "US68389X1054.csv", "company_name": "Oracle Corporation", "company_name_clean": "Oracle", "analysis_date": "2025-07-16T15:34:46.617833", "data_points": 8462, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 0.296296, "max": 241.4385, "current": 234.96}}, "support_resistance_levels": {"volume_profile": {"support": 2.660150506666666, "resistance": 16.824757546666667}, "kmeans": {"support": 11.019943328402675, "resistance": 125.16328005039531}, "kde": {"support": 8.363168808808808, "resistance": 8.600429773773772}}, "fibonacci_levels": {"0.0": 241.4385, "0.236": 184.52893985600002, "0.382": 149.32217807199999, "0.5": 120.86739800000001, "0.618": 92.*********, "0.786": 51.900727656000015, "1.0": 0.296296}, "signal_summaries": {"volume_profile": {"total_buy_signals": 5, "total_sell_signals": 51, "total_breakouts": 6, "total_breakdowns": 0, "support_level": 2.660150506666666, "resistance_level": 16.824757546666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 32, "total_sell_signals": 17, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 11.019943328402675, "resistance_level": 125.16328005039531, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 18, "total_sell_signals": 12, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 8.363168808808808, "resistance_level": 8.600429773773772, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}