{"analysis_info": {"csv_file": "US68389X1054.csv", "company_name": "Oracle Corporation", "company_name_clean": "Oracle", "analysis_date": "2025-07-16T16:15:47.791097", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 53.66, "max": 241.4385, "current": 234.96}}, "support_resistance_levels": {"volume_profile": {"support": 77.67804566666668, "resistance": 88.62274366666668}, "kmeans": {"support": 76.63757053445852, "resistance": 175.38322028940215}, "kde": {"support": 80.59945945945947, "resistance": 80.78297297297297}}, "fibonacci_levels": {"0.0": 241.4385, "0.236": 197.122774, "0.382": 169.707113, "0.5": 147.54925, "0.618": 125.391387, "0.786": 93.84459899999999, "1.0": 53.66}, "signal_summaries": {"volume_profile": {"total_buy_signals": 22, "total_sell_signals": 32, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 77.67804566666668, "resistance_level": 88.62274366666668, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 24, "total_sell_signals": 15, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 76.63757053445852, "resistance_level": 175.38322028940215, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 24, "total_sell_signals": 17, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 80.59945945945947, "resistance_level": 80.78297297297297, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}