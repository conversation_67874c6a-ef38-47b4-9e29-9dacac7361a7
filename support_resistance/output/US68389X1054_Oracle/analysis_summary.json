{"analysis_info": {"csv_file": "US68389X1054.csv", "company_name": "Oracle Corporation", "company_name_clean": "Oracle", "analysis_date": "2025-07-16T17:47:23.627828", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 118.86, "max": 241.4385, "current": 234.96}}, "support_resistance_levels": {"volume_profile": {"support": 160.91663499999999, "resistance": 169.92672833333333}, "kmeans": {"support": 141.12738532608697, "resistance": 221.16162619047617}, "kde": {"support": 169.00968968968968, "resistance": 169.1243043043043}}, "fibonacci_levels": {"0.0": 241.4385, "0.236": 212.509974, "0.382": 194.613513, "0.5": 180.14925, "0.618": 165.684987, "0.786": 145.09179899999998, "1.0": 118.86}, "signal_summaries": {"volume_profile": {"total_buy_signals": 9, "total_sell_signals": 17, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 160.91663499999999, "resistance_level": 169.92672833333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 13, "total_sell_signals": 0, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 141.12738532608697, "resistance_level": 221.16162619047617, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 19, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 169.00968968968968, "resistance_level": 169.1243043043043, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}