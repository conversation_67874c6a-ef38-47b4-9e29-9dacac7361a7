{"analysis_info": {"csv_file": "US30231G1022.csv", "company_name": "Exxon Mobil Corporation", "company_name_clean": "Exxon_Mobil", "analysis_date": "2025-07-16T15:35:17.880090", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 13.4375, "max": 126.34, "current": 112.91}}, "support_resistance_levels": {"volume_profile": {"support": 81.89110833333334, "resistance": 86.36620833333333}, "kmeans": {"support": 30.68146020098741, "resistance": 97.01081383479186}, "kde": {"support": 83.78252252252253, "resistance": 83.89431681681683}}, "fibonacci_levels": {"0.0": 126.34, "0.236": 99.69501, "0.382": 83.21124499999999, "0.5": 69.88875, "0.618": 56.566255, "0.786": 37.598635, "1.0": 13.4375}, "signal_summaries": {"volume_profile": {"total_buy_signals": 170, "total_sell_signals": 158, "total_breakouts": 4, "total_breakdowns": 6, "support_level": 81.89110833333334, "resistance_level": 86.36620833333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 26, "total_sell_signals": 31, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 30.68146020098741, "resistance_level": 97.01081383479186, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 151, "total_sell_signals": 131, "total_breakouts": 4, "total_breakdowns": 10, "support_level": 83.78252252252253, "resistance_level": 83.89431681681683, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}