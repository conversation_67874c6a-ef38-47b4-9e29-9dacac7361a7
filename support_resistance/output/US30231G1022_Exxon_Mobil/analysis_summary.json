{"analysis_info": {"csv_file": "US30231G1022.csv", "company_name": "Exxon Mobil Corporation", "company_name_clean": "Exxon_Mobil", "analysis_date": "2025-07-16T17:19:39.030199", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 31.11, "max": 126.34, "current": 112.91}}, "support_resistance_levels": {"volume_profile": {"support": 60.870333333333335, "resistance": 109.59433333333332}, "kmeans": {"support": 52.082272612732076, "resistance": 110.45620183778902}, "kde": {"support": 109.78361361361362, "resistance": 109.87750750750752}}, "fibonacci_levels": {"0.0": 126.34, "0.236": 103.86572000000001, "0.382": 89.96214, "0.5": 78.725, "0.618": 67.48786000000001, "0.786": 51.48922, "1.0": 31.11}, "signal_summaries": {"volume_profile": {"total_buy_signals": 23, "total_sell_signals": 49, "total_breakouts": 1, "total_breakdowns": 4, "support_level": 60.870333333333335, "resistance_level": 109.59433333333332, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 4, "total_sell_signals": 49, "total_breakouts": 3, "total_breakdowns": 0, "support_level": 52.082272612732076, "resistance_level": 110.45620183778902, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 55, "total_sell_signals": 53, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 109.78361361361362, "resistance_level": 109.87750750750752, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}