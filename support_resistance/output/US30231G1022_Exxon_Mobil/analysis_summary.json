{"analysis_info": {"csv_file": "US30231G1022.csv", "company_name": "Exxon Mobil Corporation", "company_name_clean": "Exxon_Mobil", "analysis_date": "2025-07-16T17:47:25.941443", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 97.8, "max": 126.34, "current": 112.91}}, "support_resistance_levels": {"volume_profile": {"support": 108.25953333333334, "resistance": 115.26513333333332}, "kmeans": {"support": 106.75506800773196, "resistance": 119.13512593749999}, "kde": {"support": 108.94477477477479, "resistance": 108.97024024024024}}, "fibonacci_levels": {"0.0": 126.34, "0.236": 119.60456, "0.382": 115.43772, "0.5": 112.07, "0.618": 108.70228, "0.786": 103.90755999999999, "1.0": 97.8}, "signal_summaries": {"volume_profile": {"total_buy_signals": 25, "total_sell_signals": 15, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 108.25953333333334, "resistance_level": 115.26513333333332, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 15, "total_sell_signals": 13, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 106.75506800773196, "resistance_level": 119.13512593749999, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 28, "total_sell_signals": 23, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 108.94477477477479, "resistance_level": 108.97024024024024, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}