{"analysis_info": {"csv_file": "US7181721090.csv", "company_name": "Philip Morris International Inc", "company_name_clean": "Philip<PERSON>Morris_International", "analysis_date": "2025-07-16T17:47:24.154683", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 104.84, "max": 186.69, "current": 180.92}}, "support_resistance_levels": {"volume_profile": {"support": 120.42107366666667, "resistance": 129.92490966666668}, "kmeans": {"support": 122.80390035211266, "resistance": 177.34649583333334}, "kde": {"support": 123.38083083083083, "resistance": 123.46079079079078}}, "fibonacci_levels": {"0.0": 186.69, "0.236": 167.3734, "0.382": 155.4233, "0.5": 145.765, "0.618": 136.1067, "0.786": 122.3559, "1.0": 104.84}, "signal_summaries": {"volume_profile": {"total_buy_signals": 20, "total_sell_signals": 12, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 120.42107366666667, "resistance_level": 129.92490966666668, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 5, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 122.80390035211266, "resistance_level": 177.34649583333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 8, "total_sell_signals": 10, "total_breakouts": 3, "total_breakdowns": 0, "support_level": 123.38083083083083, "resistance_level": 123.46079079079078, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}