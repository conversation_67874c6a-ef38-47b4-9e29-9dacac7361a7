{"analysis_info": {"csv_file": "US7181721090.csv", "company_name": "Philip Morris International Inc", "company_name_clean": "Philip<PERSON>Morris_International", "analysis_date": "2025-07-16T16:15:49.240357", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 68.93, "max": 186.69, "current": 180.92}}, "support_resistance_levels": {"volume_profile": {"support": 94.23917966666667, "resistance": 101.09989766666666}, "kmeans": {"support": 93.63017639581255, "resistance": 165.58181055045875}, "kde": {"support": 96.26570570570571, "resistance": 96.38073073073073}}, "fibonacci_levels": {"0.0": 186.69, "0.236": 158.89864, "0.382": 141.70568, "0.5": 127.81, "0.618": 113.91432, "0.786": 94.13064, "1.0": 68.93}, "signal_summaries": {"volume_profile": {"total_buy_signals": 64, "total_sell_signals": 57, "total_breakouts": 4, "total_breakdowns": 3, "support_level": 94.23917966666667, "resistance_level": 101.09989766666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 65, "total_sell_signals": 1, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 93.63017639581255, "resistance_level": 165.58181055045875, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 67, "total_sell_signals": 51, "total_breakouts": 2, "total_breakdowns": 6, "support_level": 96.26570570570571, "resistance_level": 96.38073073073073, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}