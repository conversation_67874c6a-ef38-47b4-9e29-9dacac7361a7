{"analysis_info": {"csv_file": "US7181721090.csv", "company_name": "Philip Morris International Inc", "company_name_clean": "Philip<PERSON>Morris_International", "analysis_date": "2025-07-16T15:34:51.419078", "data_points": 4359, "date_range": {"start": "2008-03-17 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 32.04, "max": 186.69, "current": 180.92}}, "support_resistance_levels": {"volume_profile": {"support": 82.71501566666666, "resistance": 88.79602766666665}, "kmeans": {"support": 52.9143993523316, "resistance": 132.56250355297158}, "kde": {"support": 86.8763063063063, "resistance": 87.02906906906907}}, "fibonacci_levels": {"0.0": 186.69, "0.236": 150.1926, "0.382": 127.6137, "0.5": 109.365, "0.618": 91.1163, "0.786": 65.1351, "1.0": 32.04}, "signal_summaries": {"volume_profile": {"total_buy_signals": 117, "total_sell_signals": 125, "total_breakouts": 4, "total_breakdowns": 9, "support_level": 82.71501566666666, "resistance_level": 88.79602766666665, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 29, "total_sell_signals": 4, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 52.9143993523316, "resistance_level": 132.56250355297158, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 140, "total_sell_signals": 122, "total_breakouts": 7, "total_breakdowns": 10, "support_level": 86.8763063063063, "resistance_level": 87.02906906906907, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}