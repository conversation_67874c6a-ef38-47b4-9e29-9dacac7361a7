{"analysis_info": {"csv_file": "US4592001014.csv", "company_name": "International Business Machines Corporation", "company_name_clean": "International_Business_Machine", "analysis_date": "2025-07-16T17:47:35.394406", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 181.81, "max": 296.16, "current": 282.7}}, "support_resistance_levels": {"volume_profile": {"support": 224.06151666666668, "resistance": 250.39871666666667}, "kmeans": {"support": 196.71738454545456, "resistance": 264.2989086}, "kde": {"support": 225.1085285285285, "resistance": 225.22054054054053}}, "fibonacci_levels": {"0.0": 296.16, "0.236": 269.1734, "0.382": 252.47830000000002, "0.5": 238.985, "0.618": 225.4917, "0.786": 206.2809, "1.0": 181.81}, "signal_summaries": {"volume_profile": {"total_buy_signals": 18, "total_sell_signals": 11, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 224.06151666666668, "resistance_level": 250.39871666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 3, "total_sell_signals": 4, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 196.71738454545456, "resistance_level": 264.2989086, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 14, "total_sell_signals": 12, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 225.1085285285285, "resistance_level": 225.22054054054053, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}