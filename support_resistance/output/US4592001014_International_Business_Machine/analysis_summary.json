{"analysis_info": {"csv_file": "US4592001014.csv", "company_name": "International Business Machines Corporation", "company_name_clean": "International_Business_Machine", "analysis_date": "2025-07-16T15:37:06.364754", "data_points": 8457, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 10.15625, "max": 296.16, "current": 282.7}}, "support_resistance_levels": {"volume_profile": {"support": 13.110404166666667, "resistance": 92.32672083333333}, "kmeans": {"support": 28.774673664773047, "resistance": 175.21981246552394}, "kde": {"support": 127.87851851851852, "resistance": 128.16333333333333}}, "fibonacci_levels": {"0.0": 296.16, "0.236": 228.663115, "0.382": 186.9065675, "0.5": 153.158125, "0.618": 119.4096825, "0.786": 71.3610525, "1.0": 10.15625}, "signal_summaries": {"volume_profile": {"total_buy_signals": 19, "total_sell_signals": 88, "total_breakouts": 4, "total_breakdowns": 3, "support_level": 13.110404166666667, "resistance_level": 92.32672083333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 13, "total_sell_signals": 33, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 28.774673664773047, "resistance_level": 175.21981246552394, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 138, "total_sell_signals": 113, "total_breakouts": 11, "total_breakdowns": 8, "support_level": 127.87851851851852, "resistance_level": 128.16333333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}