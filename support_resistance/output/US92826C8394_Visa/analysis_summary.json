{"analysis_info": {"csv_file": "US92826C8394.csv", "company_name": "Visa Inc", "company_name_clean": "Visa", "analysis_date": "2025-07-16T17:19:56.101139", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 174.6, "max": 375.51, "current": 347.02}}, "support_resistance_levels": {"volume_profile": {"support": 207.24116666666666, "resistance": 215.02903333333333}, "kmeans": {"support": 215.9882286159601, "resistance": 336.98654750000003}, "kde": {"support": 217.2128828828829, "resistance": 217.**************}}, "fibonacci_levels": {"0.0": 375.51, "0.236": 328.09524, "0.382": 298.76238, "0.5": 275.055, "0.618": 251.34762, "0.786": 217.59473999999997, "1.0": 174.6}, "signal_summaries": {"volume_profile": {"total_buy_signals": 50, "total_sell_signals": 44, "total_breakouts": 3, "total_breakdowns": 6, "support_level": 207.24116666666666, "resistance_level": 215.02903333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 47, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 215.9882286159601, "resistance_level": 336.98654750000003, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 51, "total_sell_signals": 29, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 217.2128828828829, "resistance_level": 217.**************, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}