{"analysis_info": {"csv_file": "US92826C8394.csv", "company_name": "Visa Inc", "company_name_clean": "Visa", "analysis_date": "2025-07-16T15:36:13.771457", "data_points": 4358, "date_range": {"start": "2008-03-19 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 10.445, "max": 375.51, "current": 347.02}}, "support_resistance_levels": {"volume_profile": {"support": 14.***************, "resistance": 50.66829166666667}, "kmeans": {"support": 46.05772490400159, "resistance": 292.61345550213673}, "kde": {"support": 25.85385885885886, "resistance": 26.216926926926927}}, "fibonacci_levels": {"0.0": 375.51, "0.236": 289.35465999999997, "0.382": 236.05516999999998, "0.5": 192.9775, "0.618": 149.89983, "0.786": 88.56890999999996, "1.0": 10.445}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 19, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 14.***************, "resistance_level": 50.66829166666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 11, "total_sell_signals": 2, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 46.05772490400159, "resistance_level": 292.61345550213673, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 6, "total_sell_signals": 0, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 25.85385885885886, "resistance_level": 26.216926926926927, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}