{"analysis_info": {"csv_file": "US92826C8394.csv", "company_name": "Visa Inc", "company_name_clean": "Visa", "analysis_date": "2025-07-16T18:14:36.262469", "data_points": 1004, "data_filter": "Last 5 years only", "date_range": {"start": "2021-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 4.0}, "price_range": {"min": 174.6, "max": 375.51, "current": 347.02}}, "support_resistance_levels": {"volume_profile": {"support": 211.1351, "resistance": 226.71083333333334}, "kmeans": {"support": 218.**************, "resistance": 336.9865475}, "kde": {"support": 222.1092792792793, "resistance": 222.30513513513515}}, "fibonacci_levels": {"0.0": 375.51, "0.236": 328.09524, "0.382": 298.76238, "0.5": 275.055, "0.618": 251.34762, "0.786": 217.59473999999997, "1.0": 174.6}, "signal_summaries": {"volume_profile": {"total_buy_signals": 33, "total_sell_signals": 36, "total_breakouts": 3, "total_breakdowns": 4, "support_level": 211.1351, "resistance_level": 226.71083333333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 35, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 218.**************, "resistance_level": 336.9865475, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 30, "total_sell_signals": 34, "total_breakouts": 2, "total_breakdowns": 3, "support_level": 222.1092792792793, "resistance_level": 222.30513513513515, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}