{"analysis_info": {"csv_file": "US92826C8394.csv", "company_name": "Visa Inc", "company_name_clean": "Visa", "analysis_date": "2025-07-16T18:12:00.374073", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 252.7, "max": 375.51, "current": 347.02}}, "support_resistance_levels": {"volume_profile": {"support": 284.3083333333334, "resistance": 345.6163333333334}, "kmeans": {"support": 274.903525308642, "resistance": 350.5291541262136}, "kde": {"support": 348.29485485485486, "resistance": 348.*************}}, "fibonacci_levels": {"0.0": 375.51, "0.236": 346.52684, "0.382": 328.59658, "0.5": 314.105, "0.618": 299.61342, "0.786": 278.98134, "1.0": 252.7}, "signal_summaries": {"volume_profile": {"total_buy_signals": 6, "total_sell_signals": 13, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 284.3083333333334, "resistance_level": 345.6163333333334, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 12, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 274.903525308642, "resistance_level": 350.5291541262136, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 22, "total_sell_signals": 12, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 348.29485485485486, "resistance_level": 348.*************, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}