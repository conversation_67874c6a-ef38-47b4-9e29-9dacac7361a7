{"analysis_info": {"csv_file": "US92826C8394.csv", "company_name": "Visa Inc", "company_name_clean": "Visa", "analysis_date": "2025-07-16T18:12:01.246883", "data_points": 501, "data_filter": "Last 5 years only", "date_range": {"start": "2023-07-17 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 2.0}, "price_range": {"min": 227.68, "max": 375.51, "current": 347.02}}, "support_resistance_levels": {"volume_profile": {"support": 259.**************, "resistance": 276.6776666666667}, "kmeans": {"support": 245.53020634328357, "resistance": 337.54084161676644}, "kde": {"support": 272.781971971972, "resistance": 272.9266166166166}}, "fibonacci_levels": {"0.0": 375.51, "0.236": 340.62212, "0.382": 319.03894, "0.5": 301.595, "0.618": 284.15106000000003, "0.786": 259.31561999999997, "1.0": 227.68}, "signal_summaries": {"volume_profile": {"total_buy_signals": 13, "total_sell_signals": 31, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 259.**************, "resistance_level": 276.6776666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 15, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 245.53020634328357, "resistance_level": 337.54084161676644, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 27, "total_sell_signals": 26, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 272.781971971972, "resistance_level": 272.9266166166166, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}