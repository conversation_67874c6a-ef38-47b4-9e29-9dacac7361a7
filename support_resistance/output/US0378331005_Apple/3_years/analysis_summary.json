{"analysis_info": {"csv_file": "US0378331005.csv", "company_name": "Apple Inc", "company_name_clean": "Apple", "analysis_date": "2025-07-16T17:57:26.877731", "data_points": 752, "data_filter": "Last 5 years only", "date_range": {"start": "2022-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 3.0}, "price_range": {"min": 124.17, "max": 260.1, "current": 209.11}}, "support_resistance_levels": {"volume_profile": {"support": 172.42333333333335, "resistance": 183.06866666666667}, "kmeans": {"support": 149.39765510752687, "resistance": 226.8570663716814}, "kde": {"support": 179.6125925925926, "resistance": 179.7467267267267}}, "fibonacci_levels": {"0.0": 260.1, "0.236": 228.02052000000003, "0.382": 208.17474, "0.5": 192.13500000000002, "0.618": 176.09526000000002, "0.786": 153.25902000000002, "1.0": 124.17}, "signal_summaries": {"volume_profile": {"total_buy_signals": 30, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 172.42333333333335, "resistance_level": 183.06866666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 18, "total_sell_signals": 27, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 149.39765510752687, "resistance_level": 226.8570663716814, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 20, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 179.6125925925926, "resistance_level": 179.7467267267267, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}