{"analysis_info": {"csv_file": "US0378331005.csv", "company_name": "Apple Inc", "company_name_clean": "Apple", "analysis_date": "2025-07-16T17:19:50.434609", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 89.145, "max": 260.1, "current": 209.11}}, "support_resistance_levels": {"volume_profile": {"support": 146.7988666666667, "resistance": 173.57326666666668}, "kmeans": {"support": 135.08727261792455, "resistance": 225.17394423868316}, "kde": {"support": 147.25047547547547, "resistance": 147.41704704704705}}, "fibonacci_levels": {"0.0": 260.1, "0.236": 219.75462000000002, "0.382": 194.79519, "0.5": 174.6225, "0.618": 154.44981, "0.786": 125.72936999999999, "1.0": 89.145}, "signal_summaries": {"volume_profile": {"total_buy_signals": 39, "total_sell_signals": 32, "total_breakouts": 3, "total_breakdowns": 0, "support_level": 146.7988666666667, "resistance_level": 173.57326666666668, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 21, "total_sell_signals": 21, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 135.08727261792455, "resistance_level": 225.17394423868316, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 40, "total_sell_signals": 38, "total_breakouts": 3, "total_breakdowns": 0, "support_level": 147.25047547547547, "resistance_level": 147.41704704704705, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}