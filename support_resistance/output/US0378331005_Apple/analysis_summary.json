{"analysis_info": {"csv_file": "US0378331005.csv", "company_name": "Apple Inc", "company_name_clean": "Apple", "analysis_date": "2025-07-16T15:35:54.895182", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 0.113839, "max": 260.1, "current": 209.11}}, "support_resistance_levels": {"volume_profile": {"support": 2.7046293766666665, "resistance": 13.056630883333334}, "kmeans": {"support": 3.833813229747829, "resistance": 169.54836835277553}, "kde": {"support": 2.4479858558558556, "resistance": 2.7071495065065063}}, "fibonacci_levels": {"0.0": 260.1, "0.236": 198.74326600400002, "0.382": 160.*********, "0.5": 130.1069195, "0.618": 99.*********, "0.786": 55.750877453999976, "1.0": 0.113839}, "signal_summaries": {"volume_profile": {"total_buy_signals": 11, "total_sell_signals": 7, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 2.7046293766666665, "resistance_level": 13.056630883333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 14, "total_sell_signals": 31, "total_breakouts": 4, "total_breakdowns": 1, "support_level": 3.833813229747829, "resistance_level": 169.54836835277553, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 16, "total_sell_signals": 6, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 2.4479858558558556, "resistance_level": 2.7071495065065063, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}