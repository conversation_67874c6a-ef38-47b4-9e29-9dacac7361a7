{"analysis_info": {"csv_file": "US0378331005.csv", "company_name": "Apple Inc", "company_name_clean": "Apple", "analysis_date": "2025-07-16T17:47:29.405390", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 169.2101, "max": 260.1, "current": 209.11}}, "support_resistance_levels": {"volume_profile": {"support": 222.19893166666668, "resistance": 230.35842833333334}, "kmeans": {"support": 203.27505544871795, "resistance": 242.58464957627118}, "kde": {"support": 226.07905905905903, "resistance": 226.16574574574574}}, "fibonacci_levels": {"0.0": 260.1, "0.236": 238.6499836, "0.382": 225.3800582, "0.5": 214.65505000000002, "0.618": 203.93004180000003, "0.786": 188.6605386, "1.0": 169.2101}, "signal_summaries": {"volume_profile": {"total_buy_signals": 22, "total_sell_signals": 14, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 222.19893166666668, "resistance_level": 230.35842833333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 6, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 203.27505544871795, "resistance_level": 242.58464957627118, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 24, "total_sell_signals": 28, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 226.07905905905903, "resistance_level": 226.16574574574574, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}