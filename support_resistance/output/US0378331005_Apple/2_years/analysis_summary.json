{"analysis_info": {"csv_file": "US0378331005.csv", "company_name": "Apple Inc", "company_name_clean": "Apple", "analysis_date": "2025-07-16T17:57:25.775737", "data_points": 501, "data_filter": "Last 5 years only", "date_range": {"start": "2023-07-17 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 2.0}, "price_range": {"min": 164.075, "max": 260.1, "current": 209.11}}, "support_resistance_levels": {"volume_profile": {"support": 175.47175, "resistance": 194.22341666666665}, "kmeans": {"support": 177.3068155448718, "resistance": 230.94380555555557}, "kde": {"support": 188.62264264264263, "resistance": 188.71675675675675}}, "fibonacci_levels": {"0.0": 260.1, "0.236": 237.43810000000002, "0.382": 223.41845, "0.5": 212.0875, "0.618": 200.75655, "0.786": 184.62435, "1.0": 164.075}, "signal_summaries": {"volume_profile": {"total_buy_signals": 20, "total_sell_signals": 25, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 175.47175, "resistance_level": 194.22341666666665, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 24, "total_sell_signals": 13, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 177.3068155448718, "resistance_level": 230.94380555555557, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 18, "total_sell_signals": 20, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 188.62264264264263, "resistance_level": 188.71675675675675, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}