{"analysis_info": {"csv_file": "US30303M1027.csv", "company_name": "Meta Platforms Inc", "company_name_clean": "Meta_Platforms", "analysis_date": "2025-07-16T15:38:49.804722", "data_points": 3307, "date_range": {"start": "2012-05-18 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 17.55, "max": 747.9, "current": 710.39}}, "support_resistance_levels": {"volume_profile": {"support": 25.07197, "resistance": 68.40579}, "kmeans": {"support": 86.66291228590246, "resistance": 563.8437497933885}, "kde": {"support": 164.82974474474474, "resistance": 165.55082682682684}}, "fibonacci_levels": {"0.0": 747.9, "0.236": 575.5373999999999, "0.382": 468.9063, "0.5": 382.72499999999997, "0.618": 296.54369999999994, "0.786": 173.84489999999994, "1.0": 17.55}, "signal_summaries": {"volume_profile": {"total_buy_signals": 5, "total_sell_signals": 7, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 25.07197, "resistance_level": 68.40579, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 5, "total_sell_signals": 14, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 86.66291228590246, "resistance_level": 563.8437497933885, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 42, "total_sell_signals": 45, "total_breakouts": 7, "total_breakdowns": 8, "support_level": 164.82974474474474, "resistance_level": 165.55082682682684, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}