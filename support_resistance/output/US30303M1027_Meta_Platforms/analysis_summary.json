{"analysis_info": {"csv_file": "US30303M1027.csv", "company_name": "Meta Platforms Inc", "company_name_clean": "Meta_Platforms", "analysis_date": "2025-07-16T17:47:43.675053", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 442.65, "max": 747.9, "current": 710.39}}, "support_resistance_levels": {"volume_profile": {"support": 570.8223733333334, "resistance": 593.7725600000001}, "kmeans": {"support": 511.0970023076923, "resistance": 696.8014169491526}, "kde": {"support": 587.9134634634635, "resistance": 588.1984284284284}}, "fibonacci_levels": {"0.0": 747.9, "0.236": 675.861, "0.382": 631.2945, "0.5": 595.275, "0.618": 559.2555, "0.786": 507.97349999999994, "1.0": 442.65}, "signal_summaries": {"volume_profile": {"total_buy_signals": 12, "total_sell_signals": 17, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 570.8223733333334, "resistance_level": 593.7725600000001, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 7, "total_sell_signals": 6, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 511.0970023076923, "resistance_level": 696.8014169491526, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 17, "total_sell_signals": 18, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 587.9134634634635, "resistance_level": 588.1984284284284, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}