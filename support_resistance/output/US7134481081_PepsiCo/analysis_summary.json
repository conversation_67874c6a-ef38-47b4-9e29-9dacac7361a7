{"analysis_info": {"csv_file": "US7134481081.csv", "company_name": "PepsiCo Inc", "company_name_clean": "PepsiCo", "analysis_date": "2025-07-16T16:16:29.001472", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 127.6, "max": 196.88, "current": 133.81}}, "support_resistance_levels": {"volume_profile": {"support": 166.76556666666667, "resistance": 172.1605}, "kmeans": {"support": 140.9462082169689, "resistance": 178.77625679177217}, "kde": {"support": 170.2160960960961, "resistance": 170.28426426426427}}, "fibonacci_levels": {"0.0": 196.88, "0.236": 180.52992, "0.382": 170.41504, "0.5": 162.24, "0.618": 154.06495999999999, "0.786": 142.42592, "1.0": 127.6}, "signal_summaries": {"volume_profile": {"total_buy_signals": 70, "total_sell_signals": 69, "total_breakouts": 7, "total_breakdowns": 4, "support_level": 166.76556666666667, "resistance_level": 172.1605, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 19, "total_sell_signals": 39, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 140.9462082169689, "resistance_level": 178.77625679177217, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 83, "total_sell_signals": 74, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 170.2160960960961, "resistance_level": 170.28426426426427, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}