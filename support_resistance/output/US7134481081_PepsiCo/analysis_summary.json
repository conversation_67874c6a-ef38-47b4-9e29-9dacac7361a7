{"analysis_info": {"csv_file": "US7134481081.csv", "company_name": "PepsiCo Inc", "company_name_clean": "PepsiCo", "analysis_date": "2025-07-16T15:37:54.170866", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 14.625, "max": 196.88, "current": 133.81}}, "support_resistance_levels": {"volume_profile": {"support": 38.32478093333334, "resistance": 67.27899746666667}, "kmeans": {"support": 44.69201345818986, "resistance": 157.00727295377047}, "kde": {"support": 45.89891891891892, "resistance": 46.08034534534535}}, "fibonacci_levels": {"0.0": 196.88, "0.236": 153.86782, "0.382": 127.25859, "0.5": 105.7525, "0.618": 84.24641, "0.786": 53.62756999999999, "1.0": 14.625}, "signal_summaries": {"volume_profile": {"total_buy_signals": 67, "total_sell_signals": 53, "total_breakouts": 3, "total_breakdowns": 5, "support_level": 38.32478093333334, "resistance_level": 67.27899746666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 76, "total_sell_signals": 30, "total_breakouts": 1, "total_breakdowns": 5, "support_level": 44.69201345818986, "resistance_level": 157.00727295377047, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 55, "total_sell_signals": 44, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 45.89891891891892, "resistance_level": 46.08034534534535, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}