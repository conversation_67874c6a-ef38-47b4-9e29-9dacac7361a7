{"analysis_info": {"csv_file": "US88160R1014.csv", "company_name": "Tesla Inc", "company_name_clean": "Tesla", "analysis_date": "2025-07-16T15:35:46.118358", "data_points": 3784, "date_range": {"start": "2010-06-29 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 0.998666, "max": 488.5399, "current": 310.78}}, "support_resistance_levels": {"volume_profile": {"support": 5.780886449999999, "resistance": 24.690212249999995}, "kmeans": {"support": 15.139791860317189, "resistance": 323.05531250644316}, "kde": {"support": 12.556195870870871, "resistance": 13.035481823823824}}, "fibonacci_levels": {"0.0": 488.5399, "0.236": 373.*********, "0.382": 302.29914861199995, "0.5": 244.769283, "0.618": 187.*********, "0.786": 105.*********, "1.0": 0.998666}, "signal_summaries": {"volume_profile": {"total_buy_signals": 3, "total_sell_signals": 11, "total_breakouts": 4, "total_breakdowns": 1, "support_level": 5.780886449999999, "resistance_level": 24.690212249999995, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 49, "total_sell_signals": 14, "total_breakouts": 3, "total_breakdowns": 12, "support_level": 15.139791860317189, "resistance_level": 323.05531250644316, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 20, "total_sell_signals": 27, "total_breakouts": 5, "total_breakdowns": 3, "support_level": 12.556195870870871, "resistance_level": 13.035481823823824, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}