{"analysis_info": {"csv_file": "US88160R1014.csv", "company_name": "Tesla Inc", "company_name_clean": "Tesla", "analysis_date": "2025-07-16T17:47:28.790211", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 182.0, "max": 488.5399, "current": 310.78}}, "support_resistance_levels": {"volume_profile": {"support": 220.01797266666668, "resistance": 253.48198066666666}, "kmeans": {"support": 239.1869594, "resistance": 412.85433963414636}, "kde": {"support": 239.92086086086084, "resistance": 240.20924924924924}}, "fibonacci_levels": {"0.0": 488.5399, "0.236": 416.19648359999997, "0.382": 371.4416582, "0.5": 335.26995, "0.618": 299.0982418, "0.786": 247.5995386, "1.0": 182.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 10, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 220.01797266666668, "resistance_level": 253.48198066666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 8, "total_sell_signals": 5, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 239.1869594, "resistance_level": 412.85433963414636, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 9, "total_sell_signals": 14, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 239.92086086086084, "resistance_level": 240.20924924924924, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}