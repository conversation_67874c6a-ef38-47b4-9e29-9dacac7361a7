{"analysis_info": {"csv_file": "US88160R1014.csv", "company_name": "Tesla Inc", "company_name_clean": "Tesla", "analysis_date": "2025-07-16T17:19:48.508392", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 91.0, "max": 488.5399, "current": 310.78}}, "support_resistance_levels": {"volume_profile": {"support": 225.90542263333333, "resistance": 248.7867713133333}, "kmeans": {"support": 169.6821680493902, "resistance": 353.27388932545045}, "kde": {"support": 234.2505025025025, "resistance": 234.63912512512513}}, "fibonacci_levels": {"0.0": 488.5399, "0.236": 394.72048359999997, "0.382": 336.67965819999995, "0.5": 289.76995, "0.618": 242.8602418, "0.786": 176.0735386, "1.0": 91.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 40, "total_sell_signals": 41, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 225.90542263333333, "resistance_level": 248.7867713133333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 16, "total_sell_signals": 17, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 169.6821680493902, "resistance_level": 353.27388932545045, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 40, "total_sell_signals": 40, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 234.2505025025025, "resistance_level": 234.63912512512513, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}