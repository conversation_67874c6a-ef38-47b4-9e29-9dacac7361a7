{"analysis_info": {"csv_file": "US7427181091.csv", "company_name": "The Procter & Gamble Company", "company_name_clean": "The_Procter_Gamble", "analysis_date": "2025-07-16T17:47:28.251434", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 151.9, "max": 180.43, "current": 152.68}}, "support_resistance_levels": {"volume_profile": {"support": 168.58738333333332, "resistance": 169.65251666666666}, "kmeans": {"support": 160.46251325757575, "resistance": 174.3298141509434}, "kde": {"support": 168.6648048048048, "resistance": 168.69185185185185}}, "fibonacci_levels": {"0.0": 180.43, "0.236": 173.69692, "0.382": 169.53154, "0.5": 166.16500000000002, "0.618": 162.79846, "0.786": 158.00542000000002, "1.0": 151.9}, "signal_summaries": {"volume_profile": {"total_buy_signals": 50, "total_sell_signals": 36, "total_breakouts": 1, "total_breakdowns": 4, "support_level": 168.58738333333332, "resistance_level": 169.65251666666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 19, "total_sell_signals": 14, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 160.46251325757575, "resistance_level": 174.3298141509434, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 50, "total_sell_signals": 38, "total_breakouts": 1, "total_breakdowns": 4, "support_level": 168.6648048048048, "resistance_level": 168.69185185185185, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}