{"analysis_info": {"csv_file": "US7427181091.csv", "company_name": "The Procter & Gamble Company", "company_name_clean": "The_Procter_Gamble", "analysis_date": "2025-07-16T16:16:00.168680", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 121.54, "max": 180.43, "current": 152.68}}, "support_resistance_levels": {"volume_profile": {"support": 136.99124999999998, "resistance": 146.06085000000002}, "kmeans": {"support": 136.26385289855074, "resistance": 165.5717898414787}, "kde": {"support": 142.25505505505504, "resistance": 142.31266266266266}}, "fibonacci_levels": {"0.0": 180.43, "0.236": 166.53196, "0.382": 157.93402, "0.5": 150.985, "0.618": 144.03598, "0.786": 134.14246, "1.0": 121.54}, "signal_summaries": {"volume_profile": {"total_buy_signals": 64, "total_sell_signals": 51, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 136.99124999999998, "resistance_level": 146.06085000000002, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 53, "total_sell_signals": 47, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 136.26385289855074, "resistance_level": 165.5717898414787, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 55, "total_sell_signals": 56, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 142.25505505505504, "resistance_level": 142.31266266266266, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}