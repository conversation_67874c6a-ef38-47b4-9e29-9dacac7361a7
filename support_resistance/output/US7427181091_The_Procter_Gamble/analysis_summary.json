{"analysis_info": {"csv_file": "US7427181091.csv", "company_name": "The Procter & Gamble Company", "company_name_clean": "The_Procter_Gamble", "analysis_date": "2025-07-16T15:35:42.035060", "data_points": 8462, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 10.0, "max": 180.43, "current": 152.68}}, "support_resistance_levels": {"volume_profile": {"support": 62.68670625, "resistance": 79.61689375}, "kmeans": {"support": 29.898753265929088, "resistance": 144.83799118975116}, "kde": {"support": 59.98167542542543, "resistance": 60.151248123123125}}, "fibonacci_levels": {"0.0": 180.43, "0.236": 140.20852000000002, "0.382": 115.32574, "0.5": 95.215, "0.618": 75.10426000000001, "0.786": 46.472019999999986, "1.0": 10.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 147, "total_sell_signals": 108, "total_breakouts": 4, "total_breakdowns": 7, "support_level": 62.68670625, "resistance_level": 79.61689375, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 18, "total_sell_signals": 57, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 29.898753265929088, "resistance_level": 144.83799118975116, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 64, "total_sell_signals": 67, "total_breakouts": 2, "total_breakdowns": 6, "support_level": 59.98167542542543, "resistance_level": 60.151248123123125, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}