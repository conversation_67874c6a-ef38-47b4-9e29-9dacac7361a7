{"analysis_info": {"csv_file": "US57636Q1040.csv", "company_name": "Mastercard Incorporated", "company_name_clean": "Mastercard_Incorporated", "analysis_date": "2025-07-16T17:47:27.625967", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 428.86, "max": 594.71, "current": 550.36}}, "support_resistance_levels": {"volume_profile": {"support": 525.9376673333334, "resistance": 564.0326913333333}, "kmeans": {"support": 463.732889893617, "resistance": 564.5139989}, "kde": {"support": 525.0903703703704, "resistance": 525.2516716716717}}, "fibonacci_levels": {"0.0": 594.71, "0.236": 555.5694000000001, "0.382": 531.3553, "0.5": 511.785, "0.618": 492.2147, "0.786": 464.3519, "1.0": 428.86}, "signal_summaries": {"volume_profile": {"total_buy_signals": 22, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 525.9376673333334, "resistance_level": 564.0326913333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 5, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 463.732889893617, "resistance_level": 564.5139989, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 19, "total_sell_signals": 20, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 525.0903703703704, "resistance_level": 525.2516716716717, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}