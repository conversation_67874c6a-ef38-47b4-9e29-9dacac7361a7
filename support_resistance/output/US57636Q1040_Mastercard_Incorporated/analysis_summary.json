{"analysis_info": {"csv_file": "US57636Q1040.csv", "company_name": "Mastercard Incorporated", "company_name_clean": "Mastercard_Incorporated", "analysis_date": "2025-07-16T15:35:33.367448", "data_points": 4814, "date_range": {"start": "2006-05-25 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 4.02, "max": 594.71, "current": 550.36}}, "support_resistance_levels": {"volume_profile": {"support": 10.255276666666667, "resistance": 33.72038333333333}, "kmeans": {"support": 55.68387571285808, "resistance": 484.71230771475064}, "kde": {"support": 30.802162162162162, "resistance": 31.3890990990991}}, "fibonacci_levels": {"0.0": 594.71, "0.236": 455.30716000000007, "0.382": 369.06642, "0.5": 299.365, "0.618": 229.66358000000002, "0.786": 130.**************, "1.0": 4.02}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 12, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 10.255276666666667, "resistance_level": 33.72038333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 6, "total_sell_signals": 4, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 55.68387571285808, "resistance_level": 484.71230771475064, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 15, "total_sell_signals": 9, "total_breakouts": 1, "total_breakdowns": 4, "support_level": 30.802162162162162, "resistance_level": 31.3890990990991, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}