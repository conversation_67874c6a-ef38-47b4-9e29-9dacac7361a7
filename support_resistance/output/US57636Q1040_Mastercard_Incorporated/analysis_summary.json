{"analysis_info": {"csv_file": "US57636Q1040.csv", "company_name": "Mastercard Incorporated", "company_name_clean": "Mastercard_Incorporated", "analysis_date": "2025-07-16T17:19:44.659628", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 276.87, "max": 594.71, "current": 550.36}}, "support_resistance_levels": {"volume_profile": {"support": 342.89476666666667, "resistance": 361.2741666666667}, "kmeans": {"support": 352.7619157878412, "resistance": 537.5934005246305}, "kde": {"support": 355.3742342342342, "resistance": 355.6819019019019}}, "fibonacci_levels": {"0.0": 594.71, "0.236": 519.69976, "0.382": 473.29512, "0.5": 435.79, "0.618": 398.28488000000004, "0.786": 344.88776, "1.0": 276.87}, "signal_summaries": {"volume_profile": {"total_buy_signals": 52, "total_sell_signals": 55, "total_breakouts": 5, "total_breakdowns": 4, "support_level": 342.89476666666667, "resistance_level": 361.2741666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 64, "total_sell_signals": 9, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 352.7619157878412, "resistance_level": 537.5934005246305, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 59, "total_sell_signals": 55, "total_breakouts": 4, "total_breakdowns": 3, "support_level": 355.3742342342342, "resistance_level": 355.6819019019019, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}