{"analysis_info": {"csv_file": "US6174464486.csv", "company_name": "Morgan Stanley", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T17:47:20.651458", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 90.94, "max": 145.16, "current": 141.59}}, "support_resistance_levels": {"volume_profile": {"support": 119.51264999999998, "resistance": 130.88041666666666}, "kmeans": {"support": 102.34746298701299, "resistance": 133.54502893518517}, "kde": {"support": 130.01490490490488, "resistance": 130.06702702702702}}, "fibonacci_levels": {"0.0": 145.16, "0.236": 132.36408, "0.382": 124.44796, "0.5": 118.05, "0.618": 111.65204, "0.786": 102.54308, "1.0": 90.94}, "signal_summaries": {"volume_profile": {"total_buy_signals": 17, "total_sell_signals": 20, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 119.51264999999998, "resistance_level": 130.88041666666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 12, "total_sell_signals": 10, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 102.34746298701299, "resistance_level": 133.54502893518517, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 16, "total_sell_signals": 12, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 130.01490490490488, "resistance_level": 130.06702702702702, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}