{"analysis_info": {"csv_file": "US6174464486.csv", "company_name": "Morgan Stanley", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T15:34:09.554855", "data_points": 8152, "date_range": {"start": "1993-02-24 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 6.71, "max": 145.16, "current": 141.59}}, "support_resistance_levels": {"volume_profile": {"support": 17.447758333333333, "resistance": 28.316625}, "kmeans": {"support": 21.262533688512054, "resistance": 92.25244066019417}, "kde": {"support": 47.90686436436436, "resistance": 48.04317192192192}}, "fibonacci_levels": {"0.0": 145.16, "0.236": 112.4858, "0.382": 92.2721, "0.5": 75.935, "0.618": 59.59790000000001, "0.786": 36.338300000000004, "1.0": 6.71}, "signal_summaries": {"volume_profile": {"total_buy_signals": 37, "total_sell_signals": 53, "total_breakouts": 4, "total_breakdowns": 8, "support_level": 17.447758333333333, "resistance_level": 28.316625, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 23, "total_sell_signals": 40, "total_breakouts": 2, "total_breakdowns": 3, "support_level": 21.262533688512054, "resistance_level": 92.25244066019417, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 109, "total_sell_signals": 112, "total_breakouts": 6, "total_breakdowns": 7, "support_level": 47.90686436436436, "resistance_level": 48.04317192192192, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}