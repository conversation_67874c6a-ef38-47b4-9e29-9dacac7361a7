{"analysis_info": {"csv_file": "US6174464486.csv", "company_name": "Morgan Stanley", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T16:15:40.364531", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 45.86, "max": 145.16, "current": 141.59}}, "support_resistance_levels": {"volume_profile": {"support": 84.52565, "resistance": 88.41238333333332}, "kmeans": {"support": 57.07746538461538, "resistance": 127.5064948611111}, "kde": {"support": 86.5253053053053, "resistance": 86.62312312312312}}, "fibonacci_levels": {"0.0": 145.16, "0.236": 121.7252, "0.382": 107.22739999999999, "0.5": 95.50999999999999, "0.618": 83.7926, "0.786": 67.11019999999999, "1.0": 45.86}, "signal_summaries": {"volume_profile": {"total_buy_signals": 54, "total_sell_signals": 40, "total_breakouts": 5, "total_breakdowns": 4, "support_level": 84.52565, "resistance_level": 88.41238333333332, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 2, "total_sell_signals": 15, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 57.07746538461538, "resistance_level": 127.5064948611111, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 63, "total_sell_signals": 57, "total_breakouts": 6, "total_breakdowns": 7, "support_level": 86.5253053053053, "resistance_level": 86.62312312312312, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}