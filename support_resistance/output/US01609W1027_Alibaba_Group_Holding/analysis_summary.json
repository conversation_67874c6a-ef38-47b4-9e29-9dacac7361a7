{"analysis_info": {"csv_file": "US01609W1027.csv", "company_name": "Alibaba Group Holding Limited", "company_name_clean": "Alibaba_Group_Holding", "analysis_date": "2025-07-16T17:19:25.589019", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 58.01, "max": 319.32, "current": 116.97}}, "support_resistance_levels": {"volume_profile": {"support": 74.36683333333333, "resistance": 89.50303333333333}, "kmeans": {"support": 86.24827175141243, "resistance": 247.6033981617647}, "kde": {"support": 87.55744744744744, "resistance": 87.81169169169169}}, "fibonacci_levels": {"0.0": 319.32, "0.236": 257.65084, "0.382": 219.49957999999998, "0.5": 188.665, "0.618": 157.83042, "0.786": 113.93033999999997, "1.0": 58.01}, "signal_summaries": {"volume_profile": {"total_buy_signals": 27, "total_sell_signals": 31, "total_breakouts": 9, "total_breakdowns": 2, "support_level": 74.36683333333333, "resistance_level": 89.50303333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 49, "total_sell_signals": 7, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 86.24827175141243, "resistance_level": 247.6033981617647, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 41, "total_sell_signals": 32, "total_breakouts": 8, "total_breakdowns": 2, "support_level": 87.55744744744744, "resistance_level": 87.81169169169169, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}