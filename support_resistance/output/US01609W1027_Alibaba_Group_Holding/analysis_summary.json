{"analysis_info": {"csv_file": "US01609W1027.csv", "company_name": "Alibaba Group Holding Limited", "company_name_clean": "Alibaba_Group_Holding", "analysis_date": "2025-07-16T15:34:20.909357", "data_points": 2720, "date_range": {"start": "2014-09-19 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 57.2, "max": 319.32, "current": 116.97}}, "support_resistance_levels": {"volume_profile": {"support": 80.942, "resistance": 101.43266666666666}, "kmeans": {"support": 91.02267357110827, "resistance": 242.75728311594207}, "kde": {"support": 85.47108108108108, "resistance": 85.73109109109109}}, "fibonacci_levels": {"0.0": 319.32, "0.236": 257.45968, "0.382": 219.19016, "0.5": 188.26, "0.618": 157.32984, "0.786": 113.29368, "1.0": 57.2}, "signal_summaries": {"volume_profile": {"total_buy_signals": 46, "total_sell_signals": 49, "total_breakouts": 9, "total_breakdowns": 7, "support_level": 80.942, "resistance_level": 101.43266666666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 44, "total_sell_signals": 6, "total_breakouts": 2, "total_breakdowns": 6, "support_level": 91.02267357110827, "resistance_level": 242.75728311594207, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 69, "total_sell_signals": 71, "total_breakouts": 13, "total_breakdowns": 4, "support_level": 85.47108108108108, "resistance_level": 85.73109109109109, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}