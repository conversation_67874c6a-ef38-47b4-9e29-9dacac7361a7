{"analysis_info": {"csv_file": "US01609W1027.csv", "company_name": "Alibaba Group Holding Limited", "company_name_clean": "Alibaba_Group_Holding", "analysis_date": "2025-07-16T17:47:21.833283", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 73.87, "max": 148.43, "current": 116.97}}, "support_resistance_levels": {"volume_profile": {"support": 84.57818333333333, "resistance": 137.66038333333333}, "kmeans": {"support": 84.2787179906542, "resistance": 131.9306423076923}, "kde": {"support": 84.24417417417416, "resistance": 84.31654654654655}}, "fibonacci_levels": {"0.0": 148.43, "0.236": 130.83384, "0.382": 119.94808, "0.5": 111.15, "0.618": 102.35192, "0.786": 89.82584, "1.0": 73.87}, "signal_summaries": {"volume_profile": {"total_buy_signals": 13, "total_sell_signals": 5, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 84.57818333333333, "resistance_level": 137.66038333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 14, "total_sell_signals": 11, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 84.2787179906542, "resistance_level": 131.9306423076923, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 14, "total_sell_signals": 12, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 84.24417417417416, "resistance_level": 84.31654654654655, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}