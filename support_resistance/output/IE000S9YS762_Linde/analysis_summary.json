{"analysis_info": {"csv_file": "IE000S9YS762.csv", "company_name": "Linde plc", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T17:47:41.910868", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 408.65, "max": 487.49, "current": 460.56}}, "support_resistance_levels": {"volume_profile": {"support": 455.2288, "resistance": 463.80960000000005}, "kmeans": {"support": 425.3831075, "resistance": 469.69161887254904}, "kde": {"support": 458.69766766766764, "resistance": 458.7712812812813}}, "fibonacci_levels": {"0.0": 487.49, "0.236": 468.88376, "0.382": 457.37312, "0.5": 448.07, "0.618": 438.76688, "0.786": 425.52176, "1.0": 408.65}, "signal_summaries": {"volume_profile": {"total_buy_signals": 38, "total_sell_signals": 29, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 455.2288, "resistance_level": 463.80960000000005, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 6, "total_sell_signals": 23, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 425.3831075, "resistance_level": 469.69161887254904, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 43, "total_sell_signals": 39, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 458.69766766766764, "resistance_level": 458.7712812812813, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}