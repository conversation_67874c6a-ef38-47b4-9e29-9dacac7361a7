{"analysis_info": {"csv_file": "IE000S9YS762.csv", "company_name": "Linde plc", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T15:38:28.324258", "data_points": 8326, "date_range": {"start": "1992-06-17 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 6.8125, "max": 487.49, "current": 460.56}}, "support_resistance_levels": {"volume_profile": {"support": 11.831016666666667, "resistance": 30.988416666666666}, "kmeans": {"support": 30.289172700072555, "resistance": 357.85991267432314}, "kde": {"support": 23.737237237237238, "resistance": 24.21722972972973}}, "fibonacci_levels": {"0.0": 487.49, "0.236": 374.05011, "0.382": 303.871195, "0.5": 247.15125, "0.618": 190.431305, "0.786": 109.67748499999999, "1.0": 6.8125}, "signal_summaries": {"volume_profile": {"total_buy_signals": 15, "total_sell_signals": 7, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 11.831016666666667, "resistance_level": 30.988416666666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 22, "total_sell_signals": 12, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 30.289172700072555, "resistance_level": 357.85991267432314, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 65, "total_sell_signals": 45, "total_breakouts": 7, "total_breakdowns": 7, "support_level": 23.737237237237238, "resistance_level": 24.21722972972973, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}