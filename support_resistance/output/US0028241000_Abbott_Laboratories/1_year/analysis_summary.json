{"analysis_info": {"csv_file": "US0028241000.csv", "company_name": "Abbott Laboratories", "company_name_clean": "Abbott_Laboratories", "analysis_date": "2025-07-16T18:14:48.904538", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 99.71, "max": 141.23, "current": 131.49}}, "support_resistance_levels": {"volume_profile": {"support": 113.20665, "resistance": 133.93275000000003}, "kmeans": {"support": 106.42984375, "resistance": 131.6288279661017}, "kde": {"support": 114.49827827827828, "resistance": 114.53846846846847}}, "fibonacci_levels": {"0.0": 141.23, "0.236": 131.43128, "0.382": 125.36935999999999, "0.5": 120.47, "0.618": 115.57064, "0.786": 108.59527999999999, "1.0": 99.71}, "signal_summaries": {"volume_profile": {"total_buy_signals": 20, "total_sell_signals": 15, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 113.20665, "resistance_level": 133.93275000000003, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 3, "total_sell_signals": 19, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 106.42984375, "resistance_level": 131.6288279661017, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 20, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 114.49827827827828, "resistance_level": 114.53846846846847, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}