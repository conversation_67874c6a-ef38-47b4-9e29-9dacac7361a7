{"analysis_info": {"csv_file": "US0028241000.csv", "company_name": "Abbott Laboratories", "company_name_clean": "Abbott_Laboratories", "analysis_date": "2025-07-16T15:36:39.129833", "data_points": 8460, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 11.3125, "max": 142.6, "current": 131.49}}, "support_resistance_levels": {"volume_profile": {"support": 38.828050000000005, "resistance": 46.60035}, "kmeans": {"support": 20.324795852478353, "resistance": 108.18576625531571}, "kde": {"support": 44.803063063063064, "resistance": 44.93315315315316}}, "fibonacci_levels": {"0.0": 142.6, "0.236": 111.61615, "0.382": 92.44817499999999, "0.5": 76.95625, "0.618": 61.464325, "0.786": 39.408024999999995, "1.0": 11.3125}, "signal_summaries": {"volume_profile": {"total_buy_signals": 112, "total_sell_signals": 145, "total_breakouts": 6, "total_breakdowns": 15, "support_level": 38.828050000000005, "resistance_level": 46.60035, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 41, "total_sell_signals": 76, "total_breakouts": 6, "total_breakdowns": 1, "support_level": 20.324795852478353, "resistance_level": 108.18576625531571, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 156, "total_sell_signals": 145, "total_breakouts": 8, "total_breakdowns": 10, "support_level": 44.803063063063064, "resistance_level": 44.93315315315316, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}