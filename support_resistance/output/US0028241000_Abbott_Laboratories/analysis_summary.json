{"analysis_info": {"csv_file": "US0028241000.csv", "company_name": "Abbott Laboratories", "company_name_clean": "Abbott_Laboratories", "analysis_date": "2025-07-16T17:20:01.906438", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 89.674, "max": 142.6, "current": 131.49}}, "support_resistance_levels": {"volume_profile": {"support": 106.39883333333334, "resistance": 113.4525}, "kmeans": {"support": 103.86696987304688, "resistance": 129.92810722891565}, "kde": {"support": 108.9736036036036, "resistance": 109.02492492492493}}, "fibonacci_levels": {"0.0": 142.6, "0.236": 130.109464, "0.382": 122.382268, "0.5": 116.137, "0.618": 109.891732, "0.786": 101.00016400000001, "1.0": 89.674}, "signal_summaries": {"volume_profile": {"total_buy_signals": 55, "total_sell_signals": 61, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 106.39883333333334, "resistance_level": 113.4525, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 57, "total_sell_signals": 17, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 103.86696987304688, "resistance_level": 129.92810722891565, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 75, "total_sell_signals": 71, "total_breakouts": 4, "total_breakdowns": 3, "support_level": 108.9736036036036, "resistance_level": 109.02492492492493, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}