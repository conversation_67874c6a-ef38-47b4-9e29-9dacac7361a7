{"analysis_info": {"csv_file": "US0028241000.csv", "company_name": "Abbott Laboratories", "company_name_clean": "Abbott_Laboratories", "analysis_date": "2025-07-16T18:14:52.089535", "data_points": 1004, "data_filter": "Last 5 years only", "date_range": {"start": "2021-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 4.0}, "price_range": {"min": 89.674, "max": 142.6, "current": 131.49}}, "support_resistance_levels": {"volume_profile": {"support": 104.38350000000001, "resistance": 113.4525}, "kmeans": {"support": 103.04934398395721, "resistance": 130.2762419527897}, "kde": {"support": 109.8973873873874, "resistance": 109.94870870870871}}, "fibonacci_levels": {"0.0": 142.6, "0.236": 130.109464, "0.382": 122.382268, "0.5": 116.137, "0.618": 109.891732, "0.786": 101.00016400000001, "1.0": 89.674}, "signal_summaries": {"volume_profile": {"total_buy_signals": 54, "total_sell_signals": 55, "total_breakouts": 2, "total_breakdowns": 3, "support_level": 104.38350000000001, "resistance_level": 113.4525, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 48, "total_sell_signals": 18, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 103.04934398395721, "resistance_level": 130.2762419527897, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 49, "total_sell_signals": 47, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 109.8973873873874, "resistance_level": 109.94870870870871, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}