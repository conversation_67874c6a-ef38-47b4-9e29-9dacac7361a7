{"analysis_info": {"csv_file": "US0028241000.csv", "company_name": "Abbott Laboratories", "company_name_clean": "Abbott_Laboratories", "analysis_date": "2025-07-16T18:14:50.723601", "data_points": 752, "data_filter": "Last 5 years only", "date_range": {"start": "2022-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 3.0}, "price_range": {"min": 89.674, "max": 141.23, "current": 131.49}}, "support_resistance_levels": {"volume_profile": {"support": 103.00750000000002, "resistance": 105.94210000000001}, "kmeans": {"support": 102.00019668874172, "resistance": 131.6288279661017}, "kde": {"support": 108.41914914914915, "resistance": 108.46922922922923}}, "fibonacci_levels": {"0.0": 141.23, "0.236": 129.062784, "0.382": 121.535608, "0.5": 115.452, "0.618": 109.368392, "0.786": 100.706984, "1.0": 89.674}, "signal_summaries": {"volume_profile": {"total_buy_signals": 45, "total_sell_signals": 34, "total_breakouts": 6, "total_breakdowns": 4, "support_level": 103.00750000000002, "resistance_level": 105.94210000000001, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 34, "total_sell_signals": 19, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 102.00019668874172, "resistance_level": 131.6288279661017, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 44, "total_sell_signals": 41, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 108.41914914914915, "resistance_level": 108.46922922922923, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}