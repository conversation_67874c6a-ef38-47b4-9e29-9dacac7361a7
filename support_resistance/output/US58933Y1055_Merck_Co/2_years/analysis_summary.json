{"analysis_info": {"csv_file": "US58933Y1055.csv", "company_name": "Merck & Co Inc", "company_name_clean": "Merck_Co", "analysis_date": "2025-07-16T18:14:39.192677", "data_points": 501, "data_filter": "Last 5 years only", "date_range": {"start": "2023-07-17 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 2.0}, "price_range": {"min": 73.31, "max": 134.63, "current": 81.52}}, "support_resistance_levels": {"volume_profile": {"support": 78.44212433333334, "resistance": 102.07331100000002}, "kmeans": {"support": 82.19604234693878, "resistance": 124.57985988372091}, "kde": {"support": 104.19756756756757, "resistance": 104.25711711711712}}, "fibonacci_levels": {"0.0": 134.63, "0.236": 120.15848, "0.382": 111.20576, "0.5": 103.97, "0.618": 96.73424, "0.786": 86.43248, "1.0": 73.31}, "signal_summaries": {"volume_profile": {"total_buy_signals": 11, "total_sell_signals": 27, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 78.44212433333334, "resistance_level": 102.07331100000002, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 7, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 82.19604234693878, "resistance_level": 124.57985988372091, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 21, "total_sell_signals": 20, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 104.19756756756757, "resistance_level": 104.25711711711712, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}