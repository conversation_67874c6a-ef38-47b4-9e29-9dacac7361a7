{"analysis_info": {"csv_file": "US58933Y1055.csv", "company_name": "Merck & Co Inc", "company_name_clean": "Merck_Co", "analysis_date": "2025-07-16T15:36:22.604550", "data_points": 8459, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 14.0625, "max": 134.63, "current": 81.52}}, "support_resistance_levels": {"volume_profile": {"support": 32.120195, "resistance": 36.88558033333334}, "kmeans": {"support": 29.34245245919919, "resistance": 89.51811363127133}, "kde": {"support": 33.685688188188195, "resistance": 33.80457957957958}}, "fibonacci_levels": {"0.0": 134.63, "0.236": 106.17607, "0.382": 88.573215, "0.5": 74.34625, "0.618": 60.119285000000005, "0.786": 39.863945, "1.0": 14.0625}, "signal_summaries": {"volume_profile": {"total_buy_signals": 91, "total_sell_signals": 63, "total_breakouts": 3, "total_breakdowns": 4, "support_level": 32.120195, "resistance_level": 36.88558033333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 34, "total_sell_signals": 44, "total_breakouts": 2, "total_breakdowns": 3, "support_level": 29.34245245919919, "resistance_level": 89.51811363127133, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 56, "total_sell_signals": 62, "total_breakouts": 1, "total_breakdowns": 4, "support_level": 33.685688188188195, "resistance_level": 33.80457957957958, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}