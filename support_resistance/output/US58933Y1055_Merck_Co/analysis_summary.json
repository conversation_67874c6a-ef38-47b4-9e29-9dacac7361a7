{"analysis_info": {"csv_file": "US58933Y1055.csv", "company_name": "Merck & Co Inc", "company_name_clean": "Merck_Co", "analysis_date": "2025-07-16T16:16:09.633879", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 70.89, "max": 134.63, "current": 81.52}}, "support_resistance_levels": {"volume_profile": {"support": 75.85362433333333, "resistance": 78.32807633333333}, "kmeans": {"support": 81.24174006797583, "resistance": 123.88729722222222}, "kde": {"support": 79.16364364364365, "resistance": 79.22498498498499}}, "fibonacci_levels": {"0.0": 134.63, "0.236": 119.58735999999999, "0.382": 110.28132, "0.5": 102.75999999999999, "0.618": 95.23868, "0.786": 84.53036, "1.0": 70.89}, "signal_summaries": {"volume_profile": {"total_buy_signals": 33, "total_sell_signals": 49, "total_breakouts": 5, "total_breakdowns": 3, "support_level": 75.85362433333333, "resistance_level": 78.32807633333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 44, "total_sell_signals": 11, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 81.24174006797583, "resistance_level": 123.88729722222222, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 53, "total_sell_signals": 39, "total_breakouts": 5, "total_breakdowns": 4, "support_level": 79.16364364364365, "resistance_level": 79.22498498498499, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}