{"analysis_info": {"csv_file": "US58933Y1055.csv", "company_name": "Merck & Co Inc", "company_name_clean": "Merck_Co", "analysis_date": "2025-07-16T18:14:38.363203", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 73.31, "max": 129.03, "current": 81.52}}, "support_resistance_levels": {"volume_profile": {"support": 78.05726666666666, "resistance": 99.48926666666668}, "kmeans": {"support": 81.4766032967033, "resistance": 115.96928678571429}, "kde": {"support": 99.0718018018018, "resistance": 99.1265065065065}}, "fibonacci_levels": {"0.0": 129.03, "0.236": 115.88008, "0.382": 107.74496, "0.5": 101.17, "0.618": 94.59504000000001, "0.786": 85.23408, "1.0": 73.31}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 17, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 78.05726666666666, "resistance_level": 99.48926666666668, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 9, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 81.4766032967033, "resistance_level": 115.96928678571429, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 12, "total_sell_signals": 18, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 99.0718018018018, "resistance_level": 99.1265065065065, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}