{"analysis_info": {"csv_file": "US58933Y1055.csv", "company_name": "Merck & Co Inc", "company_name_clean": "Merck_Co", "analysis_date": "2025-07-16T18:14:40.209369", "data_points": 752, "data_filter": "Last 5 years only", "date_range": {"start": "2022-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 3.0}, "price_range": {"min": 73.31, "max": 134.63, "current": 81.52}}, "support_resistance_levels": {"volume_profile": {"support": 105.617989, "resistance": 110.34422633333335}, "kmeans": {"support": 85.92643274456522, "resistance": 124.63230086206897}, "kde": {"support": 107.59189189189189, "resistance": 107.65144144144145}}, "fibonacci_levels": {"0.0": 134.63, "0.236": 120.15848, "0.382": 111.20576, "0.5": 103.97, "0.618": 96.73424, "0.786": 86.43248, "1.0": 73.31}, "signal_summaries": {"volume_profile": {"total_buy_signals": 31, "total_sell_signals": 29, "total_breakouts": 5, "total_breakdowns": 3, "support_level": 105.617989, "resistance_level": 110.34422633333335, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 7, "total_sell_signals": 20, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 85.92643274456522, "resistance_level": 124.63230086206897, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 34, "total_sell_signals": 29, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 107.59189189189189, "resistance_level": 107.65144144144145, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}