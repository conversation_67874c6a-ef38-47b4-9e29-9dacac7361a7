{"analysis_info": {"csv_file": "US58933Y1055.csv", "company_name": "Merck & Co Inc", "company_name_clean": "Merck_Co", "analysis_date": "2025-07-16T18:14:41.417380", "data_points": 1004, "data_filter": "Last 5 years only", "date_range": {"start": "2021-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 4.0}, "price_range": {"min": 70.89, "max": 134.63, "current": 81.52}}, "support_resistance_levels": {"volume_profile": {"support": 77.09085033333332, "resistance": 109.25872633333334}, "kmeans": {"support": 83.05806093023256, "resistance": 124.58100914285714}, "kde": {"support": 107.50334334334335, "resistance": 107.56468468468469}}, "fibonacci_levels": {"0.0": 134.63, "0.236": 119.58735999999999, "0.382": 110.28132, "0.5": 102.75999999999999, "0.618": 95.23868, "0.786": 84.53036, "1.0": 70.89}, "signal_summaries": {"volume_profile": {"total_buy_signals": 35, "total_sell_signals": 38, "total_breakouts": 6, "total_breakdowns": 3, "support_level": 77.09085033333332, "resistance_level": 109.25872633333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 17, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 83.05806093023256, "resistance_level": 124.58100914285714, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 35, "total_sell_signals": 25, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 107.50334334334335, "resistance_level": 107.56468468468469, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}