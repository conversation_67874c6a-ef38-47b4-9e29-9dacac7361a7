{"analysis_info": {"csv_file": "US1667641005.csv", "company_name": "Chevron Corporation", "company_name_clean": "Chevron", "analysis_date": "2025-07-16T16:15:44.738309", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 65.16, "max": 189.68, "current": 150.69}}, "support_resistance_levels": {"volume_profile": {"support": 154.96875, "resistance": 162.19125000000003}, "kmeans": {"support": 97.29060147453083, "resistance": 163.77631704545453}, "kde": {"support": 156.51426426426428, "resistance": 156.63555555555556}}, "fibonacci_levels": {"0.0": 189.68, "0.236": 160.29328, "0.382": 142.11336, "0.5": 127.42, "0.618": 112.72664, "0.786": 91.80727999999999, "1.0": 65.16}, "signal_summaries": {"volume_profile": {"total_buy_signals": 62, "total_sell_signals": 51, "total_breakouts": 4, "total_breakdowns": 6, "support_level": 154.96875, "resistance_level": 162.19125000000003, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 12, "total_sell_signals": 35, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 97.29060147453083, "resistance_level": 163.77631704545453, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 84, "total_sell_signals": 81, "total_breakouts": 2, "total_breakdowns": 7, "support_level": 156.51426426426428, "resistance_level": 156.63555555555556, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}