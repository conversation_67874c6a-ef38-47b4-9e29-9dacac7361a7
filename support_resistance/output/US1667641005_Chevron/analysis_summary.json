{"analysis_info": {"csv_file": "US1667641005.csv", "company_name": "Chevron Corporation", "company_name_clean": "Chevron", "analysis_date": "2025-07-16T17:47:22.463953", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 132.04, "max": 168.96, "current": 150.69}}, "support_resistance_levels": {"volume_profile": {"support": 144.6391, "resistance": 157.5375666666667}, "kmeans": {"support": 140.21020277777777, "resistance": 158.8988835227273}, "kde": {"support": 146.89, "resistance": 146.9248148148148}}, "fibonacci_levels": {"0.0": 168.96, "0.236": 160.24688, "0.382": 154.85656, "0.5": 150.5, "0.618": 146.14344, "0.786": 139.94088, "1.0": 132.04}, "signal_summaries": {"volume_profile": {"total_buy_signals": 20, "total_sell_signals": 19, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 144.6391, "resistance_level": 157.5375666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 12, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 140.21020277777777, "resistance_level": 158.8988835227273, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 20, "total_sell_signals": 15, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 146.89, "resistance_level": 146.9248148148148, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}