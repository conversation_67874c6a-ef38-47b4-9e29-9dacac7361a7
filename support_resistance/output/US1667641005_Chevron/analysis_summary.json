{"analysis_info": {"csv_file": "US1667641005.csv", "company_name": "Chevron Corporation", "company_name_clean": "Chevron", "analysis_date": "2025-07-16T15:34:29.553955", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 15.03125, "max": 189.68, "current": 150.69}}, "support_resistance_levels": {"volume_profile": {"support": 44.46687916666667, "resistance": 103.04647083333333}, "kmeans": {"support": 39.07378297696551, "resistance": 155.0554537257019}, "kde": {"support": 40.770138888888894, "resistance": 40.94320570570571}}, "fibonacci_levels": {"0.0": 189.68, "0.236": 148.462895, "0.382": 122.9641775, "0.5": 102.355625, "0.618": 81.7470725, "0.786": 52.4060825, "1.0": 15.03125}, "signal_summaries": {"volume_profile": {"total_buy_signals": 95, "total_sell_signals": 117, "total_breakouts": 4, "total_breakdowns": 4, "support_level": 44.46687916666667, "resistance_level": 103.04647083333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 47, "total_sell_signals": 74, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 39.07378297696551, "resistance_level": 155.0554537257019, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 62, "total_sell_signals": 86, "total_breakouts": 5, "total_breakdowns": 2, "support_level": 40.770138888888894, "resistance_level": 40.94320570570571, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}