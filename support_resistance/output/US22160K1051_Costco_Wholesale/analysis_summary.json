{"analysis_info": {"csv_file": "US22160K1051.csv", "company_name": "Costco Wholesale Corporation", "company_name_clean": "Costco_Wholesale", "analysis_date": "2025-07-16T17:47:23.006266", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 793.0, "max": 1078.235, "current": 967.68}}, "support_resistance_levels": {"volume_profile": {"support": 902.2883833333333, "resistance": 989.1251833333333}, "kmeans": {"support": 878.9916591894737, "resistance": 1024.73506779661}, "kde": {"support": 900.113993993994, "resistance": 900.3892992992993}}, "fibonacci_levels": {"0.0": 1078.235, "0.236": 1010.9195399999999, "0.382": 969.27523, "0.5": 935.6175, "0.618": 901.9597699999999, "0.786": 854.0402899999999, "1.0": 793.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 17, "total_sell_signals": 19, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 902.2883833333333, "resistance_level": 989.1251833333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 13, "total_sell_signals": 9, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 878.9916591894737, "resistance_level": 1024.73506779661, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 19, "total_sell_signals": 15, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 900.113993993994, "resistance_level": 900.3892992992993, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}