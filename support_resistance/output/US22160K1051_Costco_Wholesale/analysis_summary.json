{"analysis_info": {"csv_file": "US22160K1051.csv", "company_name": "Costco Wholesale Corporation", "company_name_clean": "Costco_Wholesale", "analysis_date": "2025-07-16T15:34:37.987707", "data_points": 7984, "date_range": {"start": "1993-10-22 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 6.0, "max": 1078.235, "current": 967.68}}, "support_resistance_levels": {"volume_profile": {"support": 16.77560833333333, "resistance": 59.46137499999999}, "kmeans": {"support": 70.79188219864994, "resistance": 874.450372579081}, "kde": {"support": 42.566431431431425, "resistance": 43.63823823823824}}, "fibonacci_levels": {"0.0": 1078.235, "0.236": 825.1875399999999, "0.382": 668.64123, "0.5": 542.1175, "0.618": 415.59376999999995, "0.786": 235.45828999999992, "1.0": 6.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 11, "total_sell_signals": 37, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 16.77560833333333, "resistance_level": 59.46137499999999, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 33, "total_sell_signals": 11, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 70.79188219864994, "resistance_level": 874.450372579081, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 65, "total_sell_signals": 47, "total_breakouts": 4, "total_breakdowns": 4, "support_level": 42.566431431431425, "resistance_level": 43.63823823823824, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}