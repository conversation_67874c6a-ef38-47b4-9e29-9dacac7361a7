{"analysis_info": {"csv_file": "US22160K1051.csv", "company_name": "Costco Wholesale Corporation", "company_name_clean": "Costco_Wholesale", "analysis_date": "2025-07-16T16:15:46.261327", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 307.0, "max": 1078.235, "current": 967.68}}, "support_resistance_levels": {"volume_profile": {"support": 382.55985, "resistance": 503.9995833333333}, "kmeans": {"support": 369.57712482332147, "resistance": 891.928253613889}, "kde": {"support": 508.334994994995, "resistance": 509.1012012012012}}, "fibonacci_levels": {"0.0": 1078.235, "0.236": 896.22354, "0.382": 783.6232299999999, "0.5": 692.6175, "0.618": 601.61177, "0.786": 472.04428999999993, "1.0": 307.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 25, "total_sell_signals": 22, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 382.55985, "resistance_level": 503.9995833333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 18, "total_sell_signals": 23, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 369.57712482332147, "resistance_level": 891.928253613889, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 26, "total_sell_signals": 21, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 508.334994994995, "resistance_level": 509.1012012012012, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}