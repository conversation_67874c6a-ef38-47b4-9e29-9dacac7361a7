{"analysis_info": {"csv_file": "US02079K3059.csv", "company_name": "Alphabet Inc", "company_name_clean": "Alphabet", "analysis_date": "2025-07-16T18:11:50.624076", "data_points": 501, "data_filter": "Last 5 years only", "date_range": {"start": "2023-07-17 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 2.0}, "price_range": {"min": 118.22, "max": 207.05, "current": 182.0}}, "support_resistance_levels": {"volume_profile": {"support": 163.51833333333332, "resistance": 175.48833333333334}, "kmeans": {"support": 135.72444089595376, "resistance": 183.8283812015504}, "kde": {"support": 167.28426426426427, "resistance": 167.37153153153153}}, "fibonacci_levels": {"0.0": 207.05, "0.236": 186.08612, "0.382": 173.11694, "0.5": 162.635, "0.618": 152.15306, "0.786": 137.22962, "1.0": 118.22}, "signal_summaries": {"volume_profile": {"total_buy_signals": 27, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 163.51833333333332, "resistance_level": 175.48833333333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 20, "total_sell_signals": 11, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 135.72444089595376, "resistance_level": 183.8283812015504, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 21, "total_sell_signals": 22, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 167.28426426426427, "resistance_level": 167.37153153153153, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}