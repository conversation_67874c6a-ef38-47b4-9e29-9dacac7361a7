{"analysis_info": {"csv_file": "US02079K3059.csv", "company_name": "Alphabet Inc", "company_name_clean": "Alphabet", "analysis_date": "2025-07-16T18:11:52.980498", "data_points": 1004, "data_filter": "Last 5 years only", "date_range": {"start": "2021-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 4.0}, "price_range": {"min": 83.34, "max": 207.05, "current": 182.0}}, "support_resistance_levels": {"volume_profile": {"support": 136.45283333333333, "resistance": 141.29216666666667}, "kmeans": {"support": 104.40800968416372, "resistance": 173.24355728813558}, "kde": {"support": 137.58215215215216, "resistance": 137.70522522522523}}, "fibonacci_levels": {"0.0": 207.05, "0.236": 177.85444, "0.382": 159.79278, "0.5": 145.195, "0.618": 130.59722, "0.786": 109.81394, "1.0": 83.34}, "signal_summaries": {"volume_profile": {"total_buy_signals": 41, "total_sell_signals": 32, "total_breakouts": 4, "total_breakdowns": 3, "support_level": 136.45283333333333, "resistance_level": 141.29216666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 14, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 104.40800968416372, "resistance_level": 173.24355728813558, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 44, "total_sell_signals": 36, "total_breakouts": 4, "total_breakdowns": 4, "support_level": 137.58215215215216, "resistance_level": 137.70522522522523, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}