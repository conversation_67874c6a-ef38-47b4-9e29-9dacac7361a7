{"analysis_info": {"csv_file": "US02079K3059.csv", "company_name": "Alphabet Inc", "company_name_clean": "Alphabet", "analysis_date": "2025-07-16T15:36:00.449541", "data_points": 5259, "date_range": {"start": "2004-08-19 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 2.399, "max": 207.05, "current": 182.0}}, "support_resistance_levels": {"volume_profile": {"support": 4.532062083333334, "resistance": 12.648477083333333}, "kmeans": {"support": 17.51557363274194, "resistance": 142.22652296262885}, "kde": {"support": 13.72486086086086, "resistance": 13.928944694694694}}, "fibonacci_levels": {"0.0": 207.05, "0.236": 158.752364, "0.382": 128.873318, "0.5": 104.7245, "0.618": 80.575682, "0.786": 46.19431399999999, "1.0": 2.399}, "signal_summaries": {"volume_profile": {"total_buy_signals": 14, "total_sell_signals": 36, "total_breakouts": 4, "total_breakdowns": 0, "support_level": 4.532062083333334, "resistance_level": 12.648477083333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 15, "total_sell_signals": 34, "total_breakouts": 4, "total_breakdowns": 2, "support_level": 17.51557363274194, "resistance_level": 142.22652296262885, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 34, "total_sell_signals": 31, "total_breakouts": 6, "total_breakdowns": 5, "support_level": 13.72486086086086, "resistance_level": 13.928944694694694, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}