{"analysis_info": {"csv_file": "US02079K3059.csv", "company_name": "Alphabet Inc", "company_name_clean": "Alphabet", "analysis_date": "2025-07-16T16:16:04.825326", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 70.1075, "max": 207.05, "current": 182.0}}, "support_resistance_levels": {"volume_profile": {"support": 104.64083333333333, "resistance": 142.26256666666666}, "kmeans": {"support": 94.60996450738918, "resistance": 172.12239984025558}, "kde": {"support": 136.5881216216216, "resistance": 136.72416816816815}}, "fibonacci_levels": {"0.0": 207.05, "0.236": 174.73157, "0.382": 154.737965, "0.5": 138.57875, "0.618": 122.41953500000001, "0.786": 99.41319500000002, "1.0": 70.1075}, "signal_summaries": {"volume_profile": {"total_buy_signals": 29, "total_sell_signals": 35, "total_breakouts": 4, "total_breakdowns": 2, "support_level": 104.64083333333333, "resistance_level": 142.26256666666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 16, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 94.60996450738918, "resistance_level": 172.12239984025558, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 42, "total_sell_signals": 41, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 136.5881216216216, "resistance_level": 136.72416816816815, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}