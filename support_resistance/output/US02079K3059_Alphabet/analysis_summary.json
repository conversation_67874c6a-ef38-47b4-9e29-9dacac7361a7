{"analysis_info": {"csv_file": "US02079K3059.csv", "company_name": "Alphabet Inc", "company_name_clean": "Alphabet", "analysis_date": "2025-07-16T17:47:30.018268", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 140.53, "max": 207.05, "current": 182.0}}, "support_resistance_levels": {"volume_profile": {"support": 162.52096666666665, "resistance": 173.09716666666668}, "kmeans": {"support": 161.10395798319328, "resistance": 192.24826050000001}, "kde": {"support": 165.26, "resistance": 165.32174174174173}}, "fibonacci_levels": {"0.0": 207.05, "0.236": 191.35128, "0.382": 181.63936, "0.5": 173.79000000000002, "0.618": 165.94064, "0.786": 154.76528000000002, "1.0": 140.53}, "signal_summaries": {"volume_profile": {"total_buy_signals": 21, "total_sell_signals": 14, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 162.52096666666665, "resistance_level": 173.09716666666668, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 8, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 161.10395798319328, "resistance_level": 192.24826050000001, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 29, "total_sell_signals": 23, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 165.26, "resistance_level": 165.32174174174173, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}