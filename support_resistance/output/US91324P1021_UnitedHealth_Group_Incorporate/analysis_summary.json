{"analysis_info": {"csv_file": "US91324P1021.csv", "company_name": "UnitedHealth Group Incorporated", "company_name_clean": "UnitedHealth_Group_Incorporate", "analysis_date": "2025-07-16T17:47:38.355174", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 248.88, "max": 630.73, "current": 291.71}}, "support_resistance_levels": {"volume_profile": {"support": 298.7273416666667, "resistance": 312.9943083333334}, "kmeans": {"support": 319.86903942307697, "resistance": 578.2402721311475}, "kde": {"support": 566.5910410410411, "resistance": 566.9422922922922}}, "fibonacci_levels": {"0.0": 630.73, "0.236": 540.6134, "0.382": 484.8633, "0.5": 439.805, "0.618": 394.74670000000003, "0.786": 330.5959, "1.0": 248.88}, "signal_summaries": {"volume_profile": {"total_buy_signals": 6, "total_sell_signals": 5, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 298.7273416666667, "resistance_level": 312.9943083333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 3, "total_sell_signals": 20, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 319.86903942307697, "resistance_level": 578.2402721311475, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 18, "total_sell_signals": 14, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 566.5910410410411, "resistance_level": 566.9422922922922, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}