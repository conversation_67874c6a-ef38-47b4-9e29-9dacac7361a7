{"analysis_info": {"csv_file": "US91324P1021.csv", "company_name": "UnitedHealth Group Incorporated", "company_name_clean": "UnitedHealth_Group_Incorporate", "analysis_date": "2025-07-16T15:37:45.296149", "data_points": 8462, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 1.859375, "max": 630.73, "current": 291.71}}, "support_resistance_levels": {"volume_profile": {"support": 8.113750603333333, "resistance": 32.96979435}, "kmeans": {"support": 33.779011166814215, "resistance": 495.7104209178745}, "kde": {"support": 21.880779700700696, "resistance": 22.50473959759759}}, "fibonacci_levels": {"0.0": 630.73, "0.236": 482.3165325, "0.382": 390.50142125, "0.5": 316.2946875, "0.618": 242.08795375, "0.786": 136.43768875, "1.0": 1.859375}, "signal_summaries": {"volume_profile": {"total_buy_signals": 32, "total_sell_signals": 41, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 8.113750603333333, "resistance_level": 32.96979435, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 27, "total_sell_signals": 49, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 33.779011166814215, "resistance_level": 495.7104209178745, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 34, "total_sell_signals": 36, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 21.880779700700696, "resistance_level": 22.50473959759759, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}