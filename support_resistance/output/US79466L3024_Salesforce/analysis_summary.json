{"analysis_info": {"csv_file": "US79466L3024.csv", "company_name": "Salesforce Inc", "company_name_clean": "Salesforce", "analysis_date": "2025-07-16T16:16:22.452936", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 126.34, "max": 369.0, "current": 257.58}}, "support_resistance_levels": {"volume_profile": {"support": 211.00275333333332, "resistance": 258.2347266666667}, "kmeans": {"support": 170.33394462540718, "resistance": 292.2535792780749}, "kde": {"support": 253.94607607607608, "resistance": 254.18591591591593}}, "fibonacci_levels": {"0.0": 369.0, "0.236": 311.73224, "0.382": 276.30388, "0.5": 247.67000000000002, "0.618": 219.03612, "0.786": 178.26924, "1.0": 126.34}, "signal_summaries": {"volume_profile": {"total_buy_signals": 43, "total_sell_signals": 42, "total_breakouts": 7, "total_breakdowns": 1, "support_level": 211.00275333333332, "resistance_level": 258.2347266666667, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 15, "total_sell_signals": 20, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 170.33394462540718, "resistance_level": 292.2535792780749, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 41, "total_sell_signals": 37, "total_breakouts": 6, "total_breakdowns": 6, "support_level": 253.94607607607608, "resistance_level": 254.18591591591593, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}