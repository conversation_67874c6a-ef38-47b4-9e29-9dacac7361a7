{"analysis_info": {"csv_file": "US79466L3024.csv", "company_name": "Salesforce Inc", "company_name_clean": "Salesforce", "analysis_date": "2025-07-16T17:47:36.629586", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 230.0, "max": 369.0, "current": 257.58}}, "support_resistance_levels": {"volume_profile": {"support": 261.36396666666667, "resistance": 266.4574333333333}, "kmeans": {"support": 257.5467801546392, "resistance": 336.4556357142857}, "kde": {"support": 266.82408408408406, "resistance": 266.9558258258258}}, "fibonacci_levels": {"0.0": 369.0, "0.236": 336.196, "0.382": 315.902, "0.5": 299.5, "0.618": 283.098, "0.786": 259.746, "1.0": 230.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 11, "total_sell_signals": 9, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 261.36396666666667, "resistance_level": 266.4574333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 11, "total_sell_signals": 10, "total_breakouts": 4, "total_breakdowns": 1, "support_level": 257.5467801546392, "resistance_level": 336.4556357142857, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 17, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 266.82408408408406, "resistance_level": 266.9558258258258, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}