{"analysis_info": {"csv_file": "US0605051046.csv", "company_name": "Bank of America Corporation", "company_name_clean": "Bank_of_America", "analysis_date": "2025-07-16T15:37:15.119435", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 2.53, "max": 55.08, "current": 46.15}}, "support_resistance_levels": {"volume_profile": {"support": 12.***************, "resistance": 16.**************}, "kmeans": {"support": 13.***************, "resistance": 43.**************}, "kde": {"support": 13.***************, "resistance": 13.***************}}, "fibonacci_levels": {"0.0": 55.08, "0.236": 42.***************, "0.382": 35.0059, "0.5": 28.805, "0.618": 22.***************, "0.786": 13.7757, "1.0": 2.53}, "signal_summaries": {"volume_profile": {"total_buy_signals": 71, "total_sell_signals": 57, "total_breakouts": 7, "total_breakdowns": 9, "support_level": 12.***************, "resistance_level": 16.**************, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 68, "total_sell_signals": 66, "total_breakouts": 6, "total_breakdowns": 5, "support_level": 13.***************, "resistance_level": 43.**************, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 77, "total_sell_signals": 88, "total_breakouts": 4, "total_breakdowns": 6, "support_level": 13.***************, "resistance_level": 13.***************, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}