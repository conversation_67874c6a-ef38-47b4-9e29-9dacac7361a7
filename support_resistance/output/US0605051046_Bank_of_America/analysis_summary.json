{"analysis_info": {"csv_file": "US0605051046.csv", "company_name": "Bank of America Corporation", "company_name_clean": "Bank_of_America", "analysis_date": "2025-07-16T17:47:36.011254", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 33.065, "max": 49.305, "current": 46.15}}, "support_resistance_levels": {"volume_profile": {"support": 39.***************, "resistance": 46.***************}, "kmeans": {"support": 38.**************, "resistance": 46.**************}, "kde": {"support": 45.**************, "resistance": 45.*************}}, "fibonacci_levels": {"0.0": 49.305, "0.236": 45.47236, "0.382": 43.10132, "0.5": 41.185, "0.618": 39.***************, "0.786": 36.54036, "1.0": 33.065}, "signal_summaries": {"volume_profile": {"total_buy_signals": 15, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 39.***************, "resistance_level": 46.***************, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 8, "total_sell_signals": 23, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 38.**************, "resistance_level": 46.**************, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 13, "total_sell_signals": 18, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 45.**************, "resistance_level": 45.*************, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}