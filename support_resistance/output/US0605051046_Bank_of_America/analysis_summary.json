{"analysis_info": {"csv_file": "US0605051046.csv", "company_name": "Bank of America Corporation", "company_name_clean": "Bank_of_America", "analysis_date": "2025-07-16T16:16:20.751458", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 22.95, "max": 50.11, "current": 46.15}}, "support_resistance_levels": {"volume_profile": {"support": 28.**************, "resistance": 39.7983}, "kmeans": {"support": 28.**************, "resistance": 44.*************}, "kde": {"support": 39.**************, "resistance": 39.**************}}, "fibonacci_levels": {"0.0": 50.11, "0.236": 43.70024, "0.382": 39.73488, "0.5": 36.53, "0.618": 33.32512, "0.786": 28.76224, "1.0": 22.95}, "signal_summaries": {"volume_profile": {"total_buy_signals": 24, "total_sell_signals": 39, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 28.**************, "resistance_level": 39.7983, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 24, "total_sell_signals": 35, "total_breakouts": 4, "total_breakdowns": 1, "support_level": 28.**************, "resistance_level": 44.*************, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 45, "total_sell_signals": 47, "total_breakouts": 1, "total_breakdowns": 6, "support_level": 39.**************, "resistance_level": 39.**************, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}