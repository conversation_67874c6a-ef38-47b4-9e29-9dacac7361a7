{"analysis_info": {"csv_file": "US64110L1061.csv", "company_name": "Netflix Inc", "company_name_clean": "Netflix", "analysis_date": "2025-07-16T17:47:34.196821", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 587.04, "max": 1341.15, "current": 1260.27}}, "support_resistance_levels": {"volume_profile": {"support": 884.1914156666667, "resistance": 972.6449076666667}, "kmeans": {"support": 695.6125831395349, "resistance": 1207.5898098214286}, "kde": {"support": 923.99006006006, "resistance": 924.7313813813814}}, "fibonacci_levels": {"0.0": 1341.15, "0.236": 1163.1800400000002, "0.382": 1053.07998, "0.5": 964.095, "0.618": 875.1100200000001, "0.786": 748.41954, "1.0": 587.04}, "signal_summaries": {"volume_profile": {"total_buy_signals": 7, "total_sell_signals": 12, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 884.1914156666667, "resistance_level": 972.6449076666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 8, "total_sell_signals": 5, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 695.6125831395349, "resistance_level": 1207.5898098214286, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 14, "total_sell_signals": 13, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 923.99006006006, "resistance_level": 924.7313813813814, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}