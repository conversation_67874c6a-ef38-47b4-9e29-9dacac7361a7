{"analysis_info": {"csv_file": "US64110L1061.csv", "company_name": "Netflix Inc", "company_name_clean": "Netflix", "analysis_date": "2025-07-16T16:16:16.095682", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 162.71, "max": 1341.15, "current": 1260.27}}, "support_resistance_levels": {"volume_profile": {"support": 226.25419666666667, "resistance": 506.06282066666677}, "kmeans": {"support": 317.5912372448979, "resistance": 1022.2019486711308}, "kde": {"support": 510.33264264264267, "resistance": 511.5065765765766}}, "fibonacci_levels": {"0.0": 1341.15, "0.236": 1063.03816, "0.382": 890.9859200000001, "0.5": 751.9300000000001, "0.618": 612.87408, "0.786": 414.89616, "1.0": 162.71}, "signal_summaries": {"volume_profile": {"total_buy_signals": 16, "total_sell_signals": 31, "total_breakouts": 5, "total_breakdowns": 1, "support_level": 226.25419666666667, "resistance_level": 506.06282066666677, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 11, "total_sell_signals": 4, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 317.5912372448979, "resistance_level": 1022.2019486711308, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 29, "total_sell_signals": 30, "total_breakouts": 5, "total_breakdowns": 5, "support_level": 510.33264264264267, "resistance_level": 511.5065765765766, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}