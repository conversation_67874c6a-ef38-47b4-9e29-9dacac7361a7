{"analysis_info": {"csv_file": "US9311421039.csv", "company_name": "Walmart Inc", "company_name_clean": "Walmart", "analysis_date": "2025-07-16T17:20:29.002698", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 39.09, "max": 105.3, "current": 95.39}}, "support_resistance_levels": {"volume_profile": {"support": 45.52542201999999, "resistance": 48.122399806666664}, "kmeans": {"support": 48.17207522566372, "resistance": 91.24213095238095}, "kde": {"support": 47.96913913913914, "resistance": 48.034824824824824}}, "fibonacci_levels": {"0.0": 105.3, "0.236": 89.67444, "0.382": 80.00778, "0.5": 72.195, "0.618": 64.38222, "0.786": 53.25894, "1.0": 39.09}, "signal_summaries": {"volume_profile": {"total_buy_signals": 32, "total_sell_signals": 66, "total_breakouts": 6, "total_breakdowns": 3, "support_level": 45.52542201999999, "resistance_level": 48.122399806666664, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 57, "total_sell_signals": 13, "total_breakouts": 0, "total_breakdowns": 6, "support_level": 48.17207522566372, "resistance_level": 91.24213095238095, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 63, "total_sell_signals": 67, "total_breakouts": 5, "total_breakdowns": 6, "support_level": 47.96913913913914, "resistance_level": 48.034824824824824, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}