{"analysis_info": {"csv_file": "US9311421039.csv", "company_name": "Walmart Inc", "company_name_clean": "Walmart", "analysis_date": "2025-07-16T15:38:19.553547", "data_points": 8456, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 3.1875, "max": 105.3, "current": 95.39}}, "support_resistance_levels": {"volume_profile": {"support": 4.284191336666667, "resistance": 18.47120805}, "kmeans": {"support": 15.828237331904958, "resistance": 86.00725044642854}, "kde": {"support": 17.582382096096097, "resistance": 17.68432570670671}}, "fibonacci_levels": {"0.0": 105.3, "0.236": 81.20145, "0.382": 66.293025, "0.5": 54.24375, "0.618": 42.194475, "0.786": 25.039575, "1.0": 3.1875}, "signal_summaries": {"volume_profile": {"total_buy_signals": 53, "total_sell_signals": 135, "total_breakouts": 5, "total_breakdowns": 6, "support_level": 4.284191336666667, "resistance_level": 18.47120805, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 122, "total_sell_signals": 5, "total_breakouts": 2, "total_breakdowns": 7, "support_level": 15.828237331904958, "resistance_level": 86.00725044642854, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 172, "total_sell_signals": 195, "total_breakouts": 9, "total_breakdowns": 10, "support_level": 17.582382096096097, "resistance_level": 17.68432570670671, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}