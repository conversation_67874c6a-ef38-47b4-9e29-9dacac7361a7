{"analysis_info": {"csv_file": "US5949181045.csv", "company_name": "Microsoft Corporation", "company_name_clean": "Microsoft", "analysis_date": "2025-07-16T15:34:01.584406", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 2.0, "max": 508.3, "current": 505.82}}, "support_resistance_levels": {"volume_profile": {"support": 7.110272806666668, "resistance": 37.32399364666667}, "kmeans": {"support": 31.338240924598516, "resistance": 387.8413348256734}, "kde": {"support": 26.289378318318317, "resistance": 26.793616616616614}}, "fibonacci_levels": {"0.0": 508.3, "0.236": 388.8132, "0.382": 314.89340000000004, "0.5": 255.15, "0.618": 195.40660000000003, "0.786": 110.34819999999996, "1.0": 2.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 25, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 7.110272806666668, "resistance_level": 37.32399364666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 71, "total_sell_signals": 12, "total_breakouts": 2, "total_breakdowns": 8, "support_level": 31.338240924598516, "resistance_level": 387.8413348256734, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 147, "total_sell_signals": 174, "total_breakouts": 11, "total_breakdowns": 6, "support_level": 26.289378318318317, "resistance_level": 26.793616616616614, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}