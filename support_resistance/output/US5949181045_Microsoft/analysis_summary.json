{"analysis_info": {"csv_file": "US5949181045.csv", "company_name": "Microsoft Corporation", "company_name_clean": "Microsoft", "analysis_date": "2025-07-16T17:47:20.106008", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 344.79, "max": 508.3, "current": 505.82}}, "support_resistance_levels": {"volume_profile": {"support": 415.507113, "resistance": 424.37231499999996}, "kmeans": {"support": 384.9019505, "resistance": 473.72798191489363}, "kde": {"support": 420.12114114114115, "resistance": 420.27255255255255}}, "fibonacci_levels": {"0.0": 508.3, "0.236": 469.71164, "0.382": 445.83918, "0.5": 426.545, "0.618": 407.25082000000003, "0.786": 379.78114000000005, "1.0": 344.79}, "signal_summaries": {"volume_profile": {"total_buy_signals": 27, "total_sell_signals": 22, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 415.507113, "resistance_level": 424.37231499999996, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 11, "total_sell_signals": 3, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 384.9019505, "resistance_level": 473.72798191489363, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 26, "total_sell_signals": 22, "total_breakouts": 2, "total_breakdowns": 3, "support_level": 420.12114114114115, "resistance_level": 420.27255255255255, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}