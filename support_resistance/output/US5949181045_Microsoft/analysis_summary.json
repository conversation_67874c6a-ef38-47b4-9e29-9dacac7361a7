{"analysis_info": {"csv_file": "US5949181045.csv", "company_name": "Microsoft Corporation", "company_name_clean": "Microsoft", "analysis_date": "2025-07-16T17:19:19.888140", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 196.25, "max": 508.3, "current": 505.82}}, "support_resistance_levels": {"volume_profile": {"support": 215.37366666666668, "resistance": 331.4788666666667}, "kmeans": {"support": 238.0984722222222, "resistance": 419.12746737288137}, "kde": {"support": 249.61345345345345, "resistance": 249.91918918918918}}, "fibonacci_levels": {"0.0": 508.3, "0.236": 434.6562, "0.382": 389.0969, "0.5": 352.275, "0.618": 315.4531, "0.786": 263.02869999999996, "1.0": 196.25}, "signal_summaries": {"volume_profile": {"total_buy_signals": 26, "total_sell_signals": 33, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 215.37366666666668, "resistance_level": 331.4788666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 29, "total_sell_signals": 40, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 238.0984722222222, "resistance_level": 419.12746737288137, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 28, "total_sell_signals": 23, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 249.61345345345345, "resistance_level": 249.91918918918918, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}