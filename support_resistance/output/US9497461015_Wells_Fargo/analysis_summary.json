{"analysis_info": {"csv_file": "US9497461015.csv", "company_name": "Wells Fargo & Company", "company_name_clean": "Wells_Fargo", "analysis_date": "2025-07-16T17:47:43.124642", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 50.15, "max": 83.945, "current": 78.86}}, "support_resistance_levels": {"volume_profile": {"support": 70.31375, "resistance": 73.49291666666667}, "kmeans": {"support": 56.54981911764706, "resistance": 76.51154699074074}, "kde": {"support": 73.66076076076075, "resistance": 73.69282282282282}}, "fibonacci_levels": {"0.0": 83.945, "0.236": 75.96938, "0.382": 71.03531, "0.5": 67.0475, "0.618": 63.059689999999996, "0.786": 57.38213, "1.0": 50.15}, "signal_summaries": {"volume_profile": {"total_buy_signals": 12, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 70.31375, "resistance_level": 73.49291666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 8, "total_sell_signals": 8, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 56.54981911764706, "resistance_level": 76.51154699074074, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 14, "total_sell_signals": 18, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 73.66076076076075, "resistance_level": 73.69282282282282, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}