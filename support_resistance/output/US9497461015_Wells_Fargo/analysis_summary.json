{"analysis_info": {"csv_file": "US9497461015.csv", "company_name": "Wells Fargo & Company", "company_name_clean": "Wells_Fargo", "analysis_date": "2025-07-16T15:38:46.092645", "data_points": 8462, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 3.59375, "max": 83.945, "current": 78.86}}, "support_resistance_levels": {"volume_profile": {"support": 25.2187375, "resistance": 28.40392083333333}, "kmeans": {"support": 8.77225459483309, "resistance": 51.81679242899298}, "kde": {"support": 27.431249999999995, "resistance": 27.511148648648646}}, "fibonacci_levels": {"0.0": 83.945, "0.236": 64.98210499999999, "0.382": 53.2508225, "0.5": 43.769375, "0.618": 34.287927499999995, "0.786": 20.788917499999997, "1.0": 3.59375}, "signal_summaries": {"volume_profile": {"total_buy_signals": 111, "total_sell_signals": 124, "total_breakouts": 8, "total_breakdowns": 7, "support_level": 25.2187375, "resistance_level": 28.40392083333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 20, "total_sell_signals": 99, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 8.77225459483309, "resistance_level": 51.81679242899298, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 98, "total_sell_signals": 100, "total_breakouts": 9, "total_breakdowns": 7, "support_level": 27.431249999999995, "resistance_level": 27.511148648648646, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}