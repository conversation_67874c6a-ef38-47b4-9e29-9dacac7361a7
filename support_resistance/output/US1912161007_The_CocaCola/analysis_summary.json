{"analysis_info": {"csv_file": "US1912161007.csv", "company_name": "The CocaCola Company", "company_name_clean": "The_CocaCola", "analysis_date": "2025-07-16T17:47:37.725020", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 60.615, "max": 74.38, "current": 69.36}}, "support_resistance_levels": {"volume_profile": {"support": 71.06178333333332, "resistance": 71.57065}, "kmeans": {"support": 63.2327453525641, "resistance": 71.53373582474227}, "kde": {"support": 70.83387387387388, "resistance": 70.84697697697698}}, "fibonacci_levels": {"0.0": 74.38, "0.236": 71.13146, "0.382": 69.12177, "0.5": 67.4975, "0.618": 65.87323, "0.786": 63.56071, "1.0": 60.615}, "signal_summaries": {"volume_profile": {"total_buy_signals": 34, "total_sell_signals": 35, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 71.06178333333332, "resistance_level": 71.57065, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 16, "total_sell_signals": 35, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 63.2327453525641, "resistance_level": 71.53373582474227, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 30, "total_sell_signals": 30, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 70.83387387387388, "resistance_level": 70.84697697697698, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}