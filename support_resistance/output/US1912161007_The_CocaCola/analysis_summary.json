{"analysis_info": {"csv_file": "US1912161007.csv", "company_name": "The CocaCola Company", "company_name_clean": "The_CocaCola", "analysis_date": "2025-07-16T15:37:36.477066", "data_points": 8459, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 8.53125, "max": 74.38, "current": 69.36}}, "support_resistance_levels": {"volume_profile": {"support": 21.16855208333333, "resistance": 26.360135416666665}, "kmeans": {"support": 21.537849257812447, "resistance": 59.774900227107565}, "kde": {"support": 23.69874874874875, "resistance": 23.76377627627628}}, "fibonacci_levels": {"0.0": 74.38, "0.236": 58.839695, "0.382": 49.22577749999999, "0.5": 41.455625, "0.618": 33.685472499999996, "0.786": 22.622882499999996, "1.0": 8.53125}, "signal_summaries": {"volume_profile": {"total_buy_signals": 81, "total_sell_signals": 81, "total_breakouts": 7, "total_breakdowns": 5, "support_level": 21.16855208333333, "resistance_level": 26.360135416666665, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 87, "total_sell_signals": 85, "total_breakouts": 4, "total_breakdowns": 3, "support_level": 21.537849257812447, "resistance_level": 59.774900227107565, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 88, "total_sell_signals": 84, "total_breakouts": 9, "total_breakdowns": 5, "support_level": 23.69874874874875, "resistance_level": 23.76377627627628, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}