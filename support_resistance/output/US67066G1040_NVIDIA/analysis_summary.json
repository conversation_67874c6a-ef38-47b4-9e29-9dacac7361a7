{"analysis_info": {"csv_file": "US67066G1040.csv", "company_name": "NVIDIA Corporation", "company_name_clean": "NVIDIA", "analysis_date": "2025-07-16T15:37:27.673239", "data_points": 6660, "date_range": {"start": "1999-01-22 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 0.033333, "max": 172.4, "current": 170.7}}, "support_resistance_levels": {"volume_profile": {"support": 1.7419386500000003, "resistance": 11.98576055}, "kmeans": {"support": 1.8371242979709645, "resistance": 120.39190475644702}, "kde": {"support": 0.8882976136136136, "resistance": 1.0591343363363361}}, "fibonacci_levels": {"0.0": 172.4, "0.236": 131.*********, "0.382": 106.*********, "0.5": 86.2166665, "0.618": 65.*********, "0.786": 36.919799737999995, "1.0": 0.033333}, "signal_summaries": {"volume_profile": {"total_buy_signals": 4, "total_sell_signals": 5, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 1.7419386500000003, "resistance_level": 11.98576055, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 1, "total_sell_signals": 17, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 1.8371242979709645, "resistance_level": 120.39190475644702, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 16, "total_sell_signals": 0, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 0.8882976136136136, "resistance_level": 1.0591343363363361, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}