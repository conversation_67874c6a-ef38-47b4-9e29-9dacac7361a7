{"analysis_info": {"csv_file": "US67066G1040.csv", "company_name": "NVIDIA Corporation", "company_name_clean": "NVIDIA", "analysis_date": "2025-07-16T17:47:37.169387", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 86.62, "max": 172.4, "current": 170.7}}, "support_resistance_levels": {"volume_profile": {"support": 115.70231666666666, "resistance": 135.31368333333336}, "kmeans": {"support": 113.09148461538462, "resistance": 148.964166}, "kde": {"support": 137.51355355355355, "resistance": 137.59002002002}}, "fibonacci_levels": {"0.0": 172.4, "0.236": 152.15592, "0.382": 139.63204000000002, "0.5": 129.51, "0.618": 119.38796, "0.786": 104.97692, "1.0": 86.62}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 17, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 115.70231666666666, "resistance_level": 135.31368333333336, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 2, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 113.09148461538462, "resistance_level": 148.964166, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 15, "total_sell_signals": 15, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 137.51355355355355, "resistance_level": 137.59002002002, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}