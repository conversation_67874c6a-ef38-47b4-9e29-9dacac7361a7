{"analysis_info": {"csv_file": "US67066G1040.csv", "company_name": "NVIDIA Corporation", "company_name_clean": "NVIDIA", "analysis_date": "2025-07-16T16:16:24.185709", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 9.777, "max": 172.4, "current": 170.7}}, "support_resistance_levels": {"volume_profile": {"support": 11.68585901, "resistance": 18.113299050000002}, "kmeans": {"support": 18.67787340221914, "resistance": 124.75573122186492}, "kde": {"support": 18.64849199199199, "resistance": 18.80922297297297}}, "fibonacci_levels": {"0.0": 172.4, "0.236": 134.020972, "0.382": 110.278014, "0.5": 91.0885, "0.618": 71.898986, "0.786": 44.578321999999986, "1.0": 9.777}, "signal_summaries": {"volume_profile": {"total_buy_signals": 5, "total_sell_signals": 8, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 11.68585901, "resistance_level": 18.113299050000002, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 13, "total_sell_signals": 14, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 18.67787340221914, "resistance_level": 124.75573122186492, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 13, "total_sell_signals": 14, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 18.64849199199199, "resistance_level": 18.80922297297297, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}