{"analysis_info": {"csv_file": "US69608A1088.csv", "company_name": "Palantir Technologies Inc", "company_name_clean": "Palantir_Technologies", "analysis_date": "2025-07-16T16:15:56.904247", "data_points": 1202, "data_filter": "Last 5 years only", "date_range": {"start": "2020-09-30 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 4.8}, "price_range": {"min": 5.84, "max": 150.62, "current": 148.58}}, "support_resistance_levels": {"volume_profile": {"support": 7.474733666666667, "resistance": 24.61153766666667}, "kmeans": {"support": 18.228183026188166, "resistance": 126.2541481617647}, "kde": {"support": 17.033583583583585, "resistance": 17.176876876876875}}, "fibonacci_levels": {"0.0": 150.62, "0.236": 116.45192, "0.382": 95.31404, "0.5": 78.23, "0.618": 61.14596, "0.786": 36.822919999999996, "1.0": 5.84}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 36, "total_breakouts": 7, "total_breakdowns": 2, "support_level": 7.474733666666667, "resistance_level": 24.61153766666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 12, "total_sell_signals": 2, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 18.228183026188166, "resistance_level": 126.2541481617647, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 10, "total_sell_signals": 10, "total_breakouts": 6, "total_breakdowns": 1, "support_level": 17.033583583583585, "resistance_level": 17.176876876876875, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}