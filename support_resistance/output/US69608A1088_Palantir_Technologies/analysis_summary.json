{"analysis_info": {"csv_file": "US69608A1088.csv", "company_name": "Palantir Technologies Inc", "company_name_clean": "Palantir_Technologies", "analysis_date": "2025-07-16T17:47:27.085828", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 21.23, "max": 150.62, "current": 148.58}}, "support_resistance_levels": {"volume_profile": {"support": 37.06340366666667, "resistance": 87.3060836666667}, "kmeans": {"support": 35.23085963855422, "resistance": 126.25414816176469}, "kde": {"support": 78.42037037037036, "resistance": 78.54555555555555}}, "fibonacci_levels": {"0.0": 150.62, "0.236": 120.08396, "0.382": 101.19301999999999, "0.5": 85.925, "0.618": 70.65697999999999, "0.786": 48.91945999999999, "1.0": 21.23}, "signal_summaries": {"volume_profile": {"total_buy_signals": 4, "total_sell_signals": 7, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 37.06340366666667, "resistance_level": 87.3060836666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 2, "total_sell_signals": 2, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 35.23085963855422, "resistance_level": 126.25414816176469, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 8, "total_sell_signals": 7, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 78.42037037037036, "resistance_level": 78.54555555555555, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}