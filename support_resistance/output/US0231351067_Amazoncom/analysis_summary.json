{"analysis_info": {"csv_file": "US0231351067.csv", "company_name": "Amazoncom Inc", "company_name_clean": "Amazoncom", "analysis_date": "2025-07-16T15:33:53.231206", "data_points": 7085, "date_range": {"start": "1997-05-15 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 0.065625, "max": 242.52, "current": 226.35}}, "support_resistance_levels": {"volume_profile": {"support": 2.47727796, "resistance": 12.109307133333335}, "kmeans": {"support": 8.952760817638499, "resistance": 172.44432619388832}, "kde": {"support": 4.429974945945946, "resistance": 4.672207387387387}}, "fibonacci_levels": {"0.0": 242.52, "0.236": 185.3007675, "0.382": 149.90242875, "0.5": 121.29281250000001, "0.618": 92.68319625000001, "0.786": 51.95086125, "1.0": 0.065625}, "signal_summaries": {"volume_profile": {"total_buy_signals": 47, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 2.47727796, "resistance_level": 12.109307133333335, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 27, "total_sell_signals": 42, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 8.952760817638499, "resistance_level": 172.44432619388832, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 26, "total_sell_signals": 20, "total_breakouts": 6, "total_breakdowns": 5, "support_level": 4.429974945945946, "resistance_level": 4.672207387387387, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}