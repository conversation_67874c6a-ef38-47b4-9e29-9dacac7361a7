{"analysis_info": {"csv_file": "US0231351067.csv", "company_name": "Amazoncom Inc", "company_name_clean": "Amazoncom", "analysis_date": "2025-07-16T17:47:19.489318", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 151.61, "max": 242.52, "current": 226.35}}, "support_resistance_levels": {"volume_profile": {"support": 182.40859999999998, "resistance": 188.9958}, "kmeans": {"support": 182.2584488598131, "resistance": 226.57916439393938}, "kde": {"support": 187.3843643643644, "resistance": 187.4654854854855}}, "fibonacci_levels": {"0.0": 242.52, "0.236": 221.06524000000002, "0.382": 207.79238, "0.5": 197.065, "0.618": 186.33762000000002, "0.786": 171.06474000000003, "1.0": 151.61}, "signal_summaries": {"volume_profile": {"total_buy_signals": 9, "total_sell_signals": 15, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 182.40859999999998, "resistance_level": 188.9958, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 9, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 182.2584488598131, "resistance_level": 226.57916439393938, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 20, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 187.3843643643644, "resistance_level": 187.4654854854855, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}