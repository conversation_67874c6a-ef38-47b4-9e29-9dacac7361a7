{"analysis_info": {"csv_file": "US0231351067.csv", "company_name": "Amazoncom Inc", "company_name_clean": "Amazoncom", "analysis_date": "2025-07-16T17:19:17.993525", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 81.43, "max": 242.52, "current": 226.35}}, "support_resistance_levels": {"volume_profile": {"support": 156.8438, "resistance": 166.3562}, "kmeans": {"support": 112.67893448005698, "resistance": 203.41459422254903}, "kde": {"support": 164.2658058058058, "resistance": 164.4262062062062}}, "fibonacci_levels": {"0.0": 242.52, "0.236": 204.50276000000002, "0.382": 180.98362, "0.5": 161.97500000000002, "0.618": 142.96638000000002, "0.786": 115.90326, "1.0": 81.43}, "signal_summaries": {"volume_profile": {"total_buy_signals": 42, "total_sell_signals": 39, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 156.8438, "resistance_level": 166.3562, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 13, "total_sell_signals": 10, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 112.67893448005698, "resistance_level": 203.41459422254903, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 38, "total_sell_signals": 50, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 164.2658058058058, "resistance_level": 164.4262062062062, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}