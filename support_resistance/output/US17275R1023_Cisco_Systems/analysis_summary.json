{"analysis_info": {"csv_file": "US17275R1023.csv", "company_name": "Cisco Systems Inc", "company_name_clean": "Cisco_Systems", "analysis_date": "2025-07-16T17:47:24.765169", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 44.5, "max": 69.78, "current": 67.18}}, "support_resistance_levels": {"volume_profile": {"support": 58.38183333333333, "resistance": 59.83803333333333}, "kmeans": {"support": 49.513817187499995, "resistance": 64.95545831716417}, "kde": {"support": 59.36614614614614, "resistance": 59.390810810810805}}, "fibonacci_levels": {"0.0": 69.78, "0.236": 63.81392, "0.382": 60.12304, "0.5": 57.14, "0.618": 54.15696, "0.786": 49.90992, "1.0": 44.5}, "signal_summaries": {"volume_profile": {"total_buy_signals": 13, "total_sell_signals": 14, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 58.38183333333333, "resistance_level": 59.83803333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 5, "total_sell_signals": 7, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 49.513817187499995, "resistance_level": 64.95545831716417, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 19, "total_sell_signals": 15, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 59.36614614614614, "resistance_level": 59.390810810810805, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}