{"analysis_info": {"csv_file": "US17275R1023.csv", "company_name": "Cisco Systems Inc", "company_name_clean": "Cisco_Systems", "analysis_date": "2025-07-16T16:15:50.815193", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 35.28, "max": 69.78, "current": 67.18}}, "support_resistance_levels": {"volume_profile": {"support": 47.448166666666665, "resistance": 50.13803333333333}, "kmeans": {"support": 43.55682287066246, "resistance": 58.912745853099736}, "kde": {"support": 48.606186186186186, "resistance": 48.6399099099099}}, "fibonacci_levels": {"0.0": 69.78, "0.236": 61.638000000000005, "0.382": 56.601, "0.5": 52.53, "0.618": 48.459, "0.786": 42.663, "1.0": 35.28}, "signal_summaries": {"volume_profile": {"total_buy_signals": 50, "total_sell_signals": 50, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 47.448166666666665, "resistance_level": 50.13803333333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 19, "total_sell_signals": 24, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 43.55682287066246, "resistance_level": 58.912745853099736, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 60, "total_sell_signals": 47, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 48.606186186186186, "resistance_level": 48.6399099099099, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}