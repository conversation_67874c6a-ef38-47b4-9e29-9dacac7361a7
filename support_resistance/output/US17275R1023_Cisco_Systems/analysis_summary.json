{"analysis_info": {"csv_file": "US17275R1023.csv", "company_name": "Cisco Systems Inc", "company_name_clean": "Cisco_Systems", "analysis_date": "2025-07-16T15:35:00.181225", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 0.336806, "max": 82.0, "current": 67.18}}, "support_resistance_levels": {"volume_profile": {"support": 1.15380212, "resistance": 18.75851276}, "kmeans": {"support": 4.643332633666091, "resistance": 50.722708978771465}, "kde": {"support": 20.065753253253252, "resistance": 20.14553616116116}}, "fibonacci_levels": {"0.0": 82.0, "0.236": 62.*********, "0.382": 50.*********, "0.5": 41.168403, "0.618": 31.*********, "0.786": 17.81272951599999, "1.0": 0.336806}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 103, "total_breakouts": 10, "total_breakdowns": 2, "support_level": 1.15380212, "resistance_level": 18.75851276, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 8, "total_sell_signals": 49, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 4.643332633666091, "resistance_level": 50.722708978771465, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 86, "total_sell_signals": 63, "total_breakouts": 6, "total_breakdowns": 6, "support_level": 20.065753253253252, "resistance_level": 20.14553616116116, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}