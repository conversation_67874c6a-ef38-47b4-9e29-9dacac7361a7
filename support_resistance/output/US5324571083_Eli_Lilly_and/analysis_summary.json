{"analysis_info": {"csv_file": "US5324571083.csv", "company_name": "Eli Lilly and Company", "company_name_clean": "<PERSON>_<PERSON>_and", "analysis_date": "2025-07-16T15:38:37.178532", "data_points": 8462, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 10.90625, "max": 972.53, "current": 771.75}}, "support_resistance_levels": {"volume_profile": {"support": 39.386975, "resistance": 77.31960833333333}, "kmeans": {"support": 59.23306248738042, "resistance": 761.6339725520834}, "kde": {"support": 55.55924924924925, "resistance": 56.50931306306306}}, "fibonacci_levels": {"0.0": 972.53, "0.236": 745.5867949999999, "0.382": 605.1897275, "0.5": 491.718125, "0.618": 378.24652249999997, "0.786": 216.6937325, "1.0": 10.90625}, "signal_summaries": {"volume_profile": {"total_buy_signals": 28, "total_sell_signals": 106, "total_breakouts": 5, "total_breakdowns": 2, "support_level": 39.386975, "resistance_level": 77.31960833333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 91, "total_sell_signals": 23, "total_breakouts": 3, "total_breakdowns": 6, "support_level": 59.23306248738042, "resistance_level": 761.6339725520834, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 109, "total_sell_signals": 140, "total_breakouts": 5, "total_breakdowns": 11, "support_level": 55.55924924924925, "resistance_level": 56.50931306306306, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}