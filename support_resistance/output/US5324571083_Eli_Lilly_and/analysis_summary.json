{"analysis_info": {"csv_file": "US5324571083.csv", "company_name": "Eli Lilly and Company", "company_name_clean": "<PERSON>_<PERSON>_and", "analysis_date": "2025-07-16T16:16:38.680197", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 129.21, "max": 972.53, "current": 771.75}}, "support_resistance_levels": {"volume_profile": {"support": 155.77485, "resistance": 238.60768333333334}, "kmeans": {"support": 255.41940502136737, "resistance": 819.4016737534625}, "kde": {"support": 262.49207207207206, "resistance": 263.32246246246245}}, "fibonacci_levels": {"0.0": 972.53, "0.236": 773.50648, "0.382": 650.38176, "0.5": 550.87, "0.618": 451.35824, "0.786": 309.68048, "1.0": 129.21}, "signal_summaries": {"volume_profile": {"total_buy_signals": 3, "total_sell_signals": 13, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 155.77485, "resistance_level": 238.60768333333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 20, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 255.41940502136737, "resistance_level": 819.4016737534625, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 19, "total_sell_signals": 12, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 262.49207207207206, "resistance_level": 263.32246246246245, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}