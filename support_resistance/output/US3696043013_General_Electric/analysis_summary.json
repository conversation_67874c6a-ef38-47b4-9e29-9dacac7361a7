{"analysis_info": {"csv_file": "US3696043013.csv", "company_name": "General Electric Company", "company_name_clean": "General_Electric", "analysis_date": "2025-07-16T15:36:57.530308", "data_points": 8462, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 41.333336, "max": 484.0, "current": 264.67}}, "support_resistance_levels": {"volume_profile": {"support": 54.946390613333335, "resistance": 107.50972373333333}, "kmeans": {"support": 91.78272824826442, "resistance": 314.2382527689789}, "kde": {"support": 73.18093340540541, "resistance": 73.61978892492493}}, "fibonacci_levels": {"0.0": 484.0, "0.236": 379.53066729600005, "0.382": 314.90133435200005, "0.5": 262.666668, "0.618": 210.43200164800004, "0.786": 136.06400209600002, "1.0": 41.333336}, "signal_summaries": {"volume_profile": {"total_buy_signals": 26, "total_sell_signals": 57, "total_breakouts": 5, "total_breakdowns": 3, "support_level": 54.946390613333335, "resistance_level": 107.50972373333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 48, "total_sell_signals": 34, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 91.78272824826442, "resistance_level": 314.2382527689789, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 54, "total_sell_signals": 31, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 73.18093340540541, "resistance_level": 73.61978892492493, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}