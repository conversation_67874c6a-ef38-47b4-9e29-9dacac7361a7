{"analysis_info": {"csv_file": "US3696043013.csv", "company_name": "General Electric Company", "company_name_clean": "General_Electric", "analysis_date": "2025-07-16T17:20:07.683676", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 47.44, "max": 265.06, "current": 264.67}}, "support_resistance_levels": {"volume_profile": {"support": 93.17508333333335, "resistance": 106.13558333333334}, "kmeans": {"support": 71.14063466076698, "resistance": 184.78757713675213}, "kde": {"support": 100.18352352352352, "resistance": 100.40081081081081}}, "fibonacci_levels": {"0.0": 265.06, "0.236": 213.70168, "0.382": 181.92916, "0.5": 156.25, "0.618": 130.57084, "0.786": 94.01067999999998, "1.0": 47.44}, "signal_summaries": {"volume_profile": {"total_buy_signals": 23, "total_sell_signals": 36, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 93.17508333333335, "resistance_level": 106.13558333333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 7, "total_sell_signals": 9, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 71.14063466076698, "resistance_level": 184.78757713675213, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 24, "total_sell_signals": 26, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 100.18352352352352, "resistance_level": 100.40081081081081, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}