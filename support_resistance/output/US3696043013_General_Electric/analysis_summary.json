{"analysis_info": {"csv_file": "US3696043013.csv", "company_name": "General Electric Company", "company_name_clean": "General_Electric", "analysis_date": "2025-07-16T17:47:34.750533", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 150.2001, "max": 265.06, "current": 264.67}}, "support_resistance_levels": {"volume_profile": {"support": 170.15024566666668, "resistance": 180.91707566666668}, "kmeans": {"support": 173.82157557692307, "resistance": 244.66157987804877}, "kde": {"support": 175.58067067067066, "resistance": 175.6887887887888}}, "fibonacci_levels": {"0.0": 265.06, "0.236": 237.9530636, "0.382": 221.18351819999998, "0.5": 207.63004999999998, "0.618": 194.07658179999999, "0.786": 174.78011859999998, "1.0": 150.2001}, "signal_summaries": {"volume_profile": {"total_buy_signals": 18, "total_sell_signals": 17, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 170.15024566666668, "resistance_level": 180.91707566666668, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 11, "total_sell_signals": 5, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 173.82157557692307, "resistance_level": 244.66157987804877, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 9, "total_sell_signals": 5, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 175.58067067067066, "resistance_level": 175.6887887887888, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}