{"analysis_info": {"csv_file": "US0846707026.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T17:47:32.395720", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 406.11, "max": 542.07, "current": 470.13}}, "support_resistance_levels": {"volume_profile": {"support": 451.762737, "resistance": 530.1882110000001}, "kmeans": {"support": 450.8636846957547, "resistance": 517.1078946721311}, "kde": {"support": 460.2900900900901, "resistance": 460.4162962962963}}, "fibonacci_levels": {"0.0": 542.07, "0.236": 509.98344000000003, "0.382": 490.13328, "0.5": 474.09000000000003, "0.618": 458.04672000000005, "0.786": 435.20544, "1.0": 406.11}, "signal_summaries": {"volume_profile": {"total_buy_signals": 18, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 451.762737, "resistance_level": 530.1882110000001, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 16, "total_sell_signals": 11, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 450.8636846957547, "resistance_level": 517.1078946721311, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 19, "total_sell_signals": 23, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 460.2900900900901, "resistance_level": 460.4162962962963, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}