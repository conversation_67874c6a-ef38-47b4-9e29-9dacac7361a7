{"analysis_info": {"csv_file": "US0846707026.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T17:19:59.957586", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 188.32, "max": 542.07, "current": 470.13}}, "support_resistance_levels": {"volume_profile": {"support": 277.558175, "resistance": 312.4227783333333}, "kmeans": {"support": 258.6015662087913, "resistance": 455.96887250069636}, "kde": {"support": 294.6815615615615, "resistance": 295.03123123123123}}, "fibonacci_levels": {"0.0": 542.07, "0.236": 458.58500000000004, "0.382": 406.9375, "0.5": 365.19500000000005, "0.618": 323.4525, "0.786": 264.0225, "1.0": 188.32}, "signal_summaries": {"volume_profile": {"total_buy_signals": 43, "total_sell_signals": 33, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 277.558175, "resistance_level": 312.4227783333333, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 6, "total_sell_signals": 29, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 258.6015662087913, "resistance_level": 455.96887250069636, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 17, "total_sell_signals": 21, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 294.6815615615615, "resistance_level": 295.03123123123123, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}