{"analysis_info": {"csv_file": "US0846707026.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T15:36:30.328462", "data_points": 7340, "date_range": {"start": "1996-05-13 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 19.8, "max": 542.07, "current": 470.13}}, "support_resistance_levels": {"volume_profile": {"support": 77.27269700000001, "resistance": 201.79494500000004}, "kmeans": {"support": 60.45373897925498, "resistance": 362.1086676454762}, "kde": {"support": 55.995195195195194, "resistance": 56.51541541541541}}, "fibonacci_levels": {"0.0": 542.07, "0.236": 418.81428000000005, "0.382": 342.56286, "0.5": 280.935, "0.618": 219.30714, "0.786": 131.56577999999996, "1.0": 19.8}, "signal_summaries": {"volume_profile": {"total_buy_signals": 62, "total_sell_signals": 54, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 77.27269700000001, "resistance_level": 201.79494500000004, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 67, "total_sell_signals": 15, "total_breakouts": 1, "total_breakdowns": 5, "support_level": 60.45373897925498, "resistance_level": 362.1086676454762, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 68, "total_sell_signals": 66, "total_breakouts": 5, "total_breakdowns": 4, "support_level": 55.995195195195194, "resistance_level": 56.51541541541541, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}