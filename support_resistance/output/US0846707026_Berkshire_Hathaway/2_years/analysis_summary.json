{"analysis_info": {"csv_file": "US0846707026.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T18:14:44.344923", "data_points": 501, "data_filter": "Last 5 years only", "date_range": {"start": "2023-07-17 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 2.0}, "price_range": {"min": 330.58, "max": 542.07, "current": 470.13}}, "support_resistance_levels": {"volume_profile": {"support": 359.634751, "resistance": 454.4941263333334}, "kmeans": {"support": 356.17279873188403, "resistance": 482.42261480619266}, "kde": {"support": 462.7296296296296, "resistance": 462.9379279279279}}, "fibonacci_levels": {"0.0": 542.07, "0.236": 492.15836, "0.382": 461.28082, "0.5": 436.32500000000005, "0.618": 411.36918000000003, "0.786": 375.83885999999995, "1.0": 330.58}, "signal_summaries": {"volume_profile": {"total_buy_signals": 29, "total_sell_signals": 27, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 359.634751, "resistance_level": 454.4941263333334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 19, "total_sell_signals": 14, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 356.17279873188403, "resistance_level": 482.42261480619266, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 18, "total_sell_signals": 18, "total_breakouts": 2, "total_breakdowns": 0, "support_level": 462.7296296296296, "resistance_level": 462.9379279279279, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}