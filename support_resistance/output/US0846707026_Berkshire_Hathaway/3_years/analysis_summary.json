{"analysis_info": {"csv_file": "US0846707026.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T18:14:45.394895", "data_points": 752, "data_filter": "Last 5 years only", "date_range": {"start": "2022-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 3.0}, "price_range": {"min": 259.85, "max": 542.07, "current": 470.13}}, "support_resistance_levels": {"volume_profile": {"support": 306.0922383333334, "resistance": 410.23479766666674}, "kmeans": {"support": 309.4693941335739, "resistance": 477.9147498063524}, "kde": {"support": 316.73053053053053, "resistance": 317.0066066066066}}, "fibonacci_levels": {"0.0": 542.07, "0.236": 475.46608000000003, "0.382": 434.26196000000004, "0.5": 400.96000000000004, "0.618": 367.65804, "0.786": 320.24508000000003, "1.0": 259.85}, "signal_summaries": {"volume_profile": {"total_buy_signals": 21, "total_sell_signals": 29, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 306.0922383333334, "resistance_level": 410.23479766666674, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 30, "total_sell_signals": 11, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 309.4693941335739, "resistance_level": 477.9147498063524, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 16, "total_sell_signals": 10, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 316.73053053053053, "resistance_level": 317.0066066066066, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}