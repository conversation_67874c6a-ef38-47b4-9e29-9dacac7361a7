{"analysis_info": {"csv_file": "US0846701086.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T17:19:54.183467", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 282560.0, "max": 812855.0, "current": 705000.0}}, "support_resistance_levels": {"volume_profile": {"support": 552175.1295033334, "resistance": 625370.8068833334}, "kmeans": {"support": 389954.11030163046, "resistance": 685061.1369157381}, "kde": {"support": 442301.95195195195, "resistance": 442826.3063063063}}, "fibonacci_levels": {"0.0": 812855.0, "0.236": 687705.38, "0.382": 610282.31, "0.5": 547707.5, "0.618": 485132.69, "0.786": 396043.13, "1.0": 282560.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 24, "total_sell_signals": 18, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 552175.1295033334, "resistance_level": 625370.8068833334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 5, "total_sell_signals": 31, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 389954.11030163046, "resistance_level": 685061.1369157381, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 16, "total_sell_signals": 22, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 442301.95195195195, "resistance_level": 442826.3063063063, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}