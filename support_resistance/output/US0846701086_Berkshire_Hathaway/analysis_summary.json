{"analysis_info": {"csv_file": "US0846701086.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T15:36:09.002912", "data_points": 8245, "date_range": {"start": "1991-12-05 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 8375.0, "max": 812855.0, "current": 705000.0}}, "support_resistance_levels": {"volume_profile": {"support": 112380.30004333332, "resistance": 624365.3669233334}, "kmeans": {"support": 75354.8463884872, "resistance": 543315.*********}, "kde": {"support": 79733.13313313313, "resistance": 80534.90990990991}}, "fibonacci_levels": {"0.0": 812855.0, "0.236": 622997.72, "0.382": 505543.64, "0.5": 410615.0, "0.618": 315686.36, "0.786": 180533.71999999997, "1.0": 8375.0}, "signal_summaries": {"volume_profile": {"total_buy_signals": 40, "total_sell_signals": 19, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 112380.30004333332, "resistance_level": 624365.3669233334, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 53, "total_sell_signals": 26, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 75354.8463884872, "resistance_level": 543315.*********, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 11, "total_sell_signals": 6, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 79733.13313313313, "resistance_level": 80534.90990990991, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}