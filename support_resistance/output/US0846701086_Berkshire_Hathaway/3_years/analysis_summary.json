{"analysis_info": {"csv_file": "US0846701086.csv", "company_name": "Berkshire Hathaway Inc", "company_name_clean": "Berkshire_Hathaway", "analysis_date": "2025-07-16T18:11:56.981310", "data_points": 752, "data_filter": "Last 5 years only", "date_range": {"start": "2022-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 3.0}, "price_range": {"min": 393012.25, "max": 812855.0, "current": 705000.0}}, "support_resistance_levels": {"volume_profile": {"support": 543075.9871833334, "resistance": 616539.2523633335}, "kmeans": {"support": 464495.1334392157, "resistance": 715793.5827318549}, "kde": {"support": 480433.0608108108, "resistance": 480843.6936936937}}, "fibonacci_levels": {"0.0": 812855.0, "0.236": 713772.111, "0.382": 652475.0695, "0.5": 602933.625, "0.618": 553392.1805, "0.786": 482858.59849999996, "1.0": 393012.25}, "signal_summaries": {"volume_profile": {"total_buy_signals": 22, "total_sell_signals": 29, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 543075.9871833334, "resistance_level": 616539.2523633335, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 21, "total_sell_signals": 13, "total_breakouts": 1, "total_breakdowns": 0, "support_level": 464495.1334392157, "resistance_level": 715793.5827318549, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 13, "total_sell_signals": 9, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 480433.0608108108, "resistance_level": 480843.6936936937, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}