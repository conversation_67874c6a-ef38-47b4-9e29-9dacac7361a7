{"analysis_info": {"csv_file": "US00287Y1091.csv", "company_name": "AbbVie Inc", "company_name_clean": "Abb<PERSON>ie", "analysis_date": "2025-07-16T16:16:32.294800", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 79.1101, "max": 218.66, "current": 186.39}}, "support_resistance_levels": {"volume_profile": {"support": 105.930046, "resistance": 148.95620066666666}, "kmeans": {"support": 106.37036796657384, "resistance": 185.42253330721002}, "kde": {"support": 149.05207207207206, "resistance": 149.1883783783784}}, "fibonacci_levels": {"0.0": 218.66, "0.236": 185.7262236, "0.382": 165.3519382, "0.5": 148.88505, "0.618": 132.4181618, "0.786": 108.9737786, "1.0": 79.1101}, "signal_summaries": {"volume_profile": {"total_buy_signals": 22, "total_sell_signals": 41, "total_breakouts": 0, "total_breakdowns": 4, "support_level": 105.930046, "resistance_level": 148.95620066666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 24, "total_sell_signals": 19, "total_breakouts": 1, "total_breakdowns": 4, "support_level": 106.37036796657384, "resistance_level": 185.42253330721002, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 53, "total_sell_signals": 41, "total_breakouts": 0, "total_breakdowns": 5, "support_level": 149.05207207207206, "resistance_level": 149.1883783783784, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}