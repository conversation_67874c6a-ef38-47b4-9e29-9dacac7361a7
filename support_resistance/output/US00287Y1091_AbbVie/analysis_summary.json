{"analysis_info": {"csv_file": "US00287Y1091.csv", "company_name": "AbbVie Inc", "company_name_clean": "Abb<PERSON>ie", "analysis_date": "2025-07-16T17:47:40.079118", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 163.81, "max": 218.66, "current": 186.39}}, "support_resistance_levels": {"volume_profile": {"support": 175.13920000000002, "resistance": 189.84319999999997}, "kmeans": {"support": 175.5324734939759, "resistance": 207.01413355263156}, "kde": {"support": 191.05774774774775, "resistance": 191.10946946946947}}, "fibonacci_levels": {"0.0": 218.66, "0.236": 205.7154, "0.382": 197.7073, "0.5": 191.235, "0.618": 184.7627, "0.786": 175.5479, "1.0": 163.81}, "signal_summaries": {"volume_profile": {"total_buy_signals": 18, "total_sell_signals": 20, "total_breakouts": 2, "total_breakdowns": 3, "support_level": 175.13920000000002, "resistance_level": 189.84319999999997, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 17, "total_sell_signals": 4, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 175.5324734939759, "resistance_level": 207.01413355263156, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 25, "total_sell_signals": 21, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 191.05774774774775, "resistance_level": 191.10946946946947, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}