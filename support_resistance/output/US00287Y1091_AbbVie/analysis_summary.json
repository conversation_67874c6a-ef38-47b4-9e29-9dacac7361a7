{"analysis_info": {"csv_file": "US00287Y1091.csv", "company_name": "AbbVie Inc", "company_name_clean": "Abb<PERSON>ie", "analysis_date": "2025-07-16T15:38:06.535531", "data_points": 3167, "date_range": {"start": "2012-12-10 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 32.51, "max": 218.66, "current": 186.39}}, "support_resistance_levels": {"volume_profile": {"support": 56.850899999999996, "resistance": 64.11476666666667}, "kmeans": {"support": 58.03678566232769, "resistance": 162.3463357421875}, "kde": {"support": 61.12810810810811, "resistance": 61.311951951951954}}, "fibonacci_levels": {"0.0": 218.66, "0.236": 174.7286, "0.382": 147.5507, "0.5": 125.585, "0.618": 103.6193, "0.786": 72.34609999999998, "1.0": 32.51}, "signal_summaries": {"volume_profile": {"total_buy_signals": 40, "total_sell_signals": 40, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 56.850899999999996, "resistance_level": 64.11476666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 33, "total_sell_signals": 37, "total_breakouts": 0, "total_breakdowns": 5, "support_level": 58.03678566232769, "resistance_level": 162.3463357421875, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 49, "total_sell_signals": 41, "total_breakouts": 2, "total_breakdowns": 7, "support_level": 61.12810810810811, "resistance_level": 61.311951951951954, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}