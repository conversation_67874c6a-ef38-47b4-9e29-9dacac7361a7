{"analysis_info": {"csv_file": "US8725901040.csv", "company_name": "TMobile US Inc", "company_name_clean": "TMobile_US", "analysis_date": "2025-07-16T15:36:42.563388", "data_points": 3070, "date_range": {"start": "2013-05-01 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 16.01, "max": 276.49, "current": 226.02}}, "support_resistance_levels": {"volume_profile": {"support": 29.324833333333327, "resistance": 60.08843333333332}, "kmeans": {"support": 50.83652534129693, "resistance": 231.52265787815134}, "kde": {"support": 36.53219219219219, "resistance": 36.78875875875876}}, "fibonacci_levels": {"0.0": 276.49, "0.236": 215.01672000000002, "0.382": 176.98664, "0.5": 146.25, "0.618": 115.51336, "0.786": 71.75271999999998, "1.0": 16.01}, "signal_summaries": {"volume_profile": {"total_buy_signals": 20, "total_sell_signals": 33, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 29.324833333333327, "resistance_level": 60.08843333333332, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 2, "total_sell_signals": 12, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 50.83652534129693, "resistance_level": 231.52265787815134, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 14, "total_sell_signals": 16, "total_breakouts": 2, "total_breakdowns": 2, "support_level": 36.53219219219219, "resistance_level": 36.78875875875876, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}