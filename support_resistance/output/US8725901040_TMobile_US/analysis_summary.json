{"analysis_info": {"csv_file": "US8725901040.csv", "company_name": "TMobile US Inc", "company_name_clean": "TMobile_US", "analysis_date": "2025-07-16T17:20:03.825788", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 101.51, "max": 276.49, "current": 226.02}}, "support_resistance_levels": {"volume_profile": {"support": 131.70913333333334, "resistance": 145.315}, "kmeans": {"support": 132.6229022274326, "resistance": 238.76509196428574}, "kde": {"support": 137.26732732732734, "resistance": 137.4387087087087}}, "fibonacci_levels": {"0.0": 276.49, "0.236": 235.19472000000002, "0.382": 209.64764, "0.5": 189.0, "0.618": 168.35236, "0.786": 138.95571999999999, "1.0": 101.51}, "signal_summaries": {"volume_profile": {"total_buy_signals": 46, "total_sell_signals": 43, "total_breakouts": 2, "total_breakdowns": 5, "support_level": 131.70913333333334, "resistance_level": 145.315, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 48, "total_sell_signals": 11, "total_breakouts": 1, "total_breakdowns": 4, "support_level": 132.6229022274326, "resistance_level": 238.76509196428574, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 43, "total_sell_signals": 39, "total_breakouts": 2, "total_breakdowns": 3, "support_level": 137.26732732732734, "resistance_level": 137.4387087087087, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}