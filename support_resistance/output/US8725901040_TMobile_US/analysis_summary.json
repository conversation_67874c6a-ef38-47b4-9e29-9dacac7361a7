{"analysis_info": {"csv_file": "US8725901040.csv", "company_name": "TMobile US Inc", "company_name_clean": "TMobile_US", "analysis_date": "2025-07-16T17:47:33.569726", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 173.7413, "max": 276.49, "current": 226.02}}, "support_resistance_levels": {"volume_profile": {"support": 221.1587833333333, "resistance": 240.67244999999997}, "kmeans": {"support": 196.8422365942029, "resistance": 257.6197373188406}, "kde": {"support": 236.8440940940941, "resistance": 236.94188188188187}}, "fibonacci_levels": {"0.0": 276.49, "0.236": 252.24130680000002, "0.382": 237.2399966, "0.5": 225.11565000000002, "0.618": 212.9913034, "0.786": 195.7295218, "1.0": 173.7413}, "signal_summaries": {"volume_profile": {"total_buy_signals": 19, "total_sell_signals": 14, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 221.1587833333333, "resistance_level": 240.67244999999997, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 10, "total_sell_signals": 10, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 196.8422365942029, "resistance_level": 257.6197373188406, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 15, "total_sell_signals": 15, "total_breakouts": 0, "total_breakdowns": 3, "support_level": 236.8440940940941, "resistance_level": 236.94188188188187, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}