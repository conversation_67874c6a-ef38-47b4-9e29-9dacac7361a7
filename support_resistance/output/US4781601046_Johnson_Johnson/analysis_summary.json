{"analysis_info": {"csv_file": "US4781601046.csv", "company_name": "Johnson & Johnson", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T15:38:02.994575", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 8.90625, "max": 186.69, "current": 155.17}}, "support_resistance_levels": {"volume_profile": {"support": 60.05945650000001, "resistance": 67.10645050000001}, "kmeans": {"support": 21.779853159704274, "resistance": 146.12453077163065}, "kde": {"support": 60.140467967967965, "resistance": 60.317748998998994}}, "fibonacci_levels": {"0.0": 186.69, "0.236": 144.733035, "0.382": 118.7766075, "0.5": 97.798125, "0.618": 76.8196425, "0.786": 46.95197249999998, "1.0": 8.90625}, "signal_summaries": {"volume_profile": {"total_buy_signals": 105, "total_sell_signals": 84, "total_breakouts": 5, "total_breakdowns": 1, "support_level": 60.05945650000001, "resistance_level": 67.10645050000001, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 11, "total_sell_signals": 80, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 21.779853159704274, "resistance_level": 146.12453077163065, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 113, "total_sell_signals": 136, "total_breakouts": 5, "total_breakdowns": 2, "support_level": 60.140467967967965, "resistance_level": 60.317748998998994, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}