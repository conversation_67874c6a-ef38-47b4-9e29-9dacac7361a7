{"analysis_info": {"csv_file": "US4781601046.csv", "company_name": "Johnson & Johnson", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T17:47:39.539642", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 140.68, "max": 169.99, "current": 155.17}}, "support_resistance_levels": {"volume_profile": {"support": 154.91586666666666, "resistance": 163.26146666666668}, "kmeans": {"support": 146.67718722222222, "resistance": 162.73985883838384}, "kde": {"support": 154.71317317317317, "resistance": 154.73883883883883}}, "fibonacci_levels": {"0.0": 169.99, "0.236": 163.07284, "0.382": 158.79358000000002, "0.5": 155.335, "0.618": 151.87642, "0.786": 146.95234, "1.0": 140.68}, "signal_summaries": {"volume_profile": {"total_buy_signals": 34, "total_sell_signals": 19, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 154.91586666666666, "resistance_level": 163.26146666666668, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 12, "total_sell_signals": 21, "total_breakouts": 1, "total_breakdowns": 2, "support_level": 146.67718722222222, "resistance_level": 162.73985883838384, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 34, "total_sell_signals": 35, "total_breakouts": 1, "total_breakdowns": 1, "support_level": 154.71317317317317, "resistance_level": 154.73883883883883, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}