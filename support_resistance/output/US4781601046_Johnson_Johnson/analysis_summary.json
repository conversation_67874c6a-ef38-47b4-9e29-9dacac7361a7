{"analysis_info": {"csv_file": "US4781601046.csv", "company_name": "Johnson & Johnson", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T16:16:30.647786", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 133.65, "max": 186.69, "current": 155.17}}, "support_resistance_levels": {"volume_profile": {"support": 163.07398, "resistance": 172.8827}, "kmeans": {"support": 150.38518403614458, "resistance": 174.15091635273973}, "kde": {"support": 162.85714714714715, "resistance": 162.9060960960961}}, "fibonacci_levels": {"0.0": 186.69, "0.236": 174.17256, "0.382": 166.42872, "0.5": 160.17000000000002, "0.618": 153.91128, "0.786": 145.00056, "1.0": 133.65}, "signal_summaries": {"volume_profile": {"total_buy_signals": 102, "total_sell_signals": 44, "total_breakouts": 1, "total_breakdowns": 6, "support_level": 163.07398, "resistance_level": 172.8827, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 43, "total_sell_signals": 42, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 150.38518403614458, "resistance_level": 174.15091635273973, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 103, "total_sell_signals": 112, "total_breakouts": 5, "total_breakdowns": 5, "support_level": 162.85714714714715, "resistance_level": 162.9060960960961, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}