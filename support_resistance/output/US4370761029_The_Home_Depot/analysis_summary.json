{"analysis_info": {"csv_file": "US4370761029.csv", "company_name": "The Home Depot Inc", "company_name_clean": "The_Home_Depot", "analysis_date": "2025-07-16T17:19:40.956861", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 246.59, "max": 439.37, "current": 358.64}}, "support_resistance_levels": {"volume_profile": {"support": 285.6252666666667, "resistance": 325.2120666666667}, "kmeans": {"support": 286.1749399253731, "resistance": 387.0519006410256}, "kde": {"support": 321.55266266266267, "resistance": 321.7332832832833}}, "fibonacci_levels": {"0.0": 439.37, "0.236": 393.87392, "0.382": 365.72804, "0.5": 342.98, "0.618": 320.23196, "0.786": 287.84492, "1.0": 246.59}, "signal_summaries": {"volume_profile": {"total_buy_signals": 46, "total_sell_signals": 47, "total_breakouts": 1, "total_breakdowns": 3, "support_level": 285.6252666666667, "resistance_level": 325.2120666666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 41, "total_sell_signals": 23, "total_breakouts": 3, "total_breakdowns": 3, "support_level": 286.1749399253731, "resistance_level": 387.0519006410256, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 48, "total_sell_signals": 43, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 321.55266266266267, "resistance_level": 321.7332832832833, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}