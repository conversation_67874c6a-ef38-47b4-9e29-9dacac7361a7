{"analysis_info": {"csv_file": "US4370761029.csv", "company_name": "The Home Depot Inc", "company_name_clean": "The_Home_Depot", "analysis_date": "2025-07-16T17:47:26.477942", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 326.31, "max": 439.37, "current": 358.64}}, "support_resistance_levels": {"volume_profile": {"support": 349.7488333333334, "resistance": 368.0921666666667}, "kmeans": {"support": 361.0570095528455, "resistance": 413.9176761029412}, "kde": {"support": 363.8191891891892, "resistance": 363.9154154154154}}, "fibonacci_levels": {"0.0": 439.37, "0.236": 412.68784, "0.382": 396.18108, "0.5": 382.84000000000003, "0.618": 369.49892, "0.786": 350.50484, "1.0": 326.31}, "signal_summaries": {"volume_profile": {"total_buy_signals": 10, "total_sell_signals": 23, "total_breakouts": 0, "total_breakdowns": 2, "support_level": 349.7488333333334, "resistance_level": 368.0921666666667, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 24, "total_sell_signals": 16, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 361.0570095528455, "resistance_level": 413.9176761029412, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 27, "total_sell_signals": 21, "total_breakouts": 0, "total_breakdowns": 1, "support_level": 363.8191891891892, "resistance_level": 363.9154154154154, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}