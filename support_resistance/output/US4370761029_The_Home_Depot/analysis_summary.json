{"analysis_info": {"csv_file": "US4370761029.csv", "company_name": "The Home Depot Inc", "company_name_clean": "The_Home_Depot", "analysis_date": "2025-07-16T15:35:26.712737", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 6.611128, "max": 439.37, "current": 358.64}}, "support_resistance_levels": {"volume_profile": {"support": 10.977950346666667, "resistance": 36.45666042666667}, "kmeans": {"support": 33.56458192734257, "resistance": 326.35646524012407}, "kde": {"support": 30.114369201201203, "resistance": 30.539428132132134}}, "fibonacci_levels": {"0.0": 439.37, "0.236": 337.*********, "0.382": 274.*********, "0.5": 222.990564, "0.618": 171.*********, "0.786": 99.22152660799998, "1.0": 6.611128}, "signal_summaries": {"volume_profile": {"total_buy_signals": 18, "total_sell_signals": 96, "total_breakouts": 8, "total_breakdowns": 3, "support_level": 10.977950346666667, "resistance_level": 36.45666042666667, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 67, "total_sell_signals": 47, "total_breakouts": 1, "total_breakdowns": 8, "support_level": 33.56458192734257, "resistance_level": 326.35646524012407, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 37, "total_sell_signals": 32, "total_breakouts": 3, "total_breakdowns": 2, "support_level": 30.114369201201203, "resistance_level": 30.539428132132134, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}