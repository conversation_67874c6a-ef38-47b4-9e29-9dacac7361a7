{"analysis_info": {"csv_file": "US5801351017.csv", "company_name": "McDonald's Corporation", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T17:47:21.262599", "data_points": 251, "data_filter": "Last 5 years only", "date_range": {"start": "2024-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 1.0}, "price_range": {"min": 246.12, "max": 326.32, "current": 299.62}}, "support_resistance_levels": {"volume_profile": {"support": 293.37350000000004, "resistance": 300.2751666666667}, "kmeans": {"support": 263.716185, "resistance": 309.6494899509804}, "kde": {"support": 294.23873873873873, "resistance": 294.3086386386386}}, "fibonacci_levels": {"0.0": 326.32, "0.236": 307.3928, "0.382": 295.6836, "0.5": 286.22, "0.618": 276.7564, "0.786": 263.2828, "1.0": 246.12}, "signal_summaries": {"volume_profile": {"total_buy_signals": 27, "total_sell_signals": 16, "total_breakouts": 2, "total_breakdowns": 1, "support_level": 293.37350000000004, "resistance_level": 300.2751666666667, "latest_signals": {"buy": false, "sell": true, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 2, "total_sell_signals": 22, "total_breakouts": 0, "total_breakdowns": 0, "support_level": 263.716185, "resistance_level": 309.6494899509804, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 27, "total_sell_signals": 28, "total_breakouts": 3, "total_breakdowns": 1, "support_level": 294.23873873873873, "resistance_level": 294.3086386386386, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}