{"analysis_info": {"csv_file": "US5801351017.csv", "company_name": "McDonald's Corporation", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T15:34:17.884398", "data_points": 8463, "date_range": {"start": "1991-12-02 00:00:00", "end": "2025-07-15 00:00:00"}, "price_range": {"min": 8.28125, "max": 326.32, "current": 299.62}}, "support_resistance_levels": {"volume_profile": {"support": 23.980645833333334, "resistance": 55.171104166666666}, "kmeans": {"support": 31.424205153158837, "resistance": 247.04046820685437}, "kde": {"support": 26.258475975975976, "resistance": 26.57167292292292}}, "fibonacci_levels": {"0.0": 326.32, "0.236": 251.262855, "0.382": 204.8291975, "0.5": 167.300625, "0.618": 129.7720525, "0.786": 76.34154249999997, "1.0": 8.28125}, "signal_summaries": {"volume_profile": {"total_buy_signals": 75, "total_sell_signals": 50, "total_breakouts": 4, "total_breakdowns": 3, "support_level": 23.980645833333334, "resistance_level": 55.171104166666666, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 53, "total_sell_signals": 37, "total_breakouts": 0, "total_breakdowns": 5, "support_level": 31.424205153158837, "resistance_level": 247.04046820685437, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 62, "total_sell_signals": 59, "total_breakouts": 1, "total_breakdowns": 6, "support_level": 26.258475975975976, "resistance_level": 26.57167292292292, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json"]}}