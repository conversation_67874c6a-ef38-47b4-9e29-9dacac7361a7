{"analysis_info": {"csv_file": "US5801351017.csv", "company_name": "McDonald's Corporation", "company_name_clean": "<PERSON><PERSON>", "analysis_date": "2025-07-16T17:19:23.670555", "data_points": 1256, "data_filter": "Last 5 years only", "date_range": {"start": "2020-07-15 00:00:00", "end": "2025-07-15 00:00:00", "years_covered": 5.0}, "price_range": {"min": 189.88, "max": 326.32, "current": 299.62}}, "support_resistance_levels": {"volume_profile": {"support": 290.5924, "resistance": 295.7572}, "kmeans": {"support": 223.61467485337243, "resistance": 294.7956973295454}, "kde": {"support": 291.5358858858859, "resistance": 291.6663863863864}}, "fibonacci_levels": {"0.0": 326.32, "0.236": 294.12016, "0.382": 274.19992, "0.5": 258.1, "0.618": 242.00008, "0.786": 219.07816, "1.0": 189.88}, "signal_summaries": {"volume_profile": {"total_buy_signals": 63, "total_sell_signals": 60, "total_breakouts": 4, "total_breakdowns": 4, "support_level": 290.5924, "resistance_level": 295.7572, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kmeans": {"total_buy_signals": 12, "total_sell_signals": 69, "total_breakouts": 5, "total_breakdowns": 0, "support_level": 223.61467485337243, "resistance_level": 294.7956973295454, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}, "kde": {"total_buy_signals": 63, "total_sell_signals": 75, "total_breakouts": 2, "total_breakdowns": 4, "support_level": 291.5358858858859, "resistance_level": 291.6663863863864, "latest_signals": {"buy": false, "sell": false, "breakout": false, "breakdown": false}}}, "files_generated": {"charts": ["volume_profile_sr.png", "kmeans_sr.png", "kde_sr.png", "fibonacci_levels.png"], "interactive_charts": ["volume_profile_sr_interactive.html", "kmeans_sr_interactive.html", "kde_sr_interactive.html", "fibonacci_levels_interactive.html", "all_methods_combined_interactive.html"], "multi_timeframe_charts": ["volume_profile_multi_timeframe.html", "kmeans_multi_timeframe.html", "kde_multi_timeframe.html"], "data_files": ["swing_highs.csv", "swing_lows.csv", "swings_enhanced.csv"], "signal_files": ["volume_profile_signals.csv", "kmeans_signals.csv", "kde_signals.csv"], "summary_files": ["sr_levels_summary.json", "fibonacci_levels.json", "signal_summaries.json", "multi_timeframe_sr_levels.json", "multi_timeframe_summary.csv"]}}