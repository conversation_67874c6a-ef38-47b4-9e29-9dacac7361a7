import mplfinance as mpf
import pandas as pd

def plot_candlestick(df, title='Candlestick Chart', volume=True, style='yahoo', figsize=(12,6)):
    """
    Plots a candlestick chart from a DataFrame with OHLCV data.

    Parameters:
        df (pd.DataFrame): Must contain columns ['Open', 'High', 'Low', 'Close'].
                           Index should be datetime or convertible to datetime.
        title (str): Chart title
        volume (bool): Show volume bars below chart
        style (str): mplfinance style (e.g., 'yahoo', 'charles', 'binance', etc.)
        figsize (tuple): Size of the plot
    """
    df = df.copy()

    # Ensure datetime index
    if not pd.api.types.is_datetime64_any_dtype(df.index):
        if 'Date' in df.columns:
            df.set_index(pd.to_datetime(df['Date']), inplace=True)
        else:
            df.index = pd.to_datetime(df.index)

    # Required format for mplfinance
    df = df[['Open', 'High', 'Low', 'Close', 'Volume']]

    # Plot
    mpf.plot(
        df,
        type='candle',
        volume=volume,
        style=style,
        title=title,
        ylabel='Price',
        ylabel_lower='Volume',
        figratio=(figsize[0], figsize[1]),
        figscale=1.1
    )

def plot_candlestick_with_sr(df, sr_levels, title='Candlestick with S/R', volume=True, style='yahoo', figsize=(12,6)):
    """
    Plots a candlestick chart with annotated support and resistance levels.

    Parameters:
        df (pd.DataFrame): Must contain 'Open', 'High', 'Low', 'Close', 'Volume'
        sr_levels (list, dict, or tuple): Levels to draw. Can be:
            - List of floats
            - Dict with {'support': [...], 'resistance': [...]}
            - Tuple of (support, resistance)
        title (str): Chart title
        volume (bool): Show volume bar plot
        style (str): mplfinance style
        figsize (tuple): Plot dimensions
    """
    df = df.copy()

    # Ensure datetime index
    if not pd.api.types.is_datetime64_any_dtype(df.index):
        if 'Date' in df.columns:
            df.set_index(pd.to_datetime(df['Date']), inplace=True)
        else:
            df.index = pd.to_datetime(df.index)

    # Extract S/R levels
    support_levels = []
    resistance_levels = []

    if isinstance(sr_levels, dict):
        support_levels = sr_levels.get('support', [])
        resistance_levels = sr_levels.get('resistance', [])
    elif isinstance(sr_levels, tuple):
        support_levels = [sr_levels[0]]
        resistance_levels = [sr_levels[1]]
    elif isinstance(sr_levels, list):
        support_levels = [x for x in sr_levels if x < df['Close'].max()]
        resistance_levels = [x for x in sr_levels if x >= df['Close'].min()]

    # Create horizontal lines
    hlines = support_levels + resistance_levels
    hl_colors = ['green'] * len(support_levels) + ['red'] * len(resistance_levels)

    mpf.plot(
        df[['Open', 'High', 'Low', 'Close', 'Volume']],
        type='candle',
        volume=volume,
        style=style,
        title=title,
        ylabel='Price',
        ylabel_lower='Volume',
        figratio=(figsize[0], figsize[1]),
        figscale=1.1,
        hlines=dict(hlines=hlines, colors=hl_colors, linewidths=1.0, linestyle='dashed')
    )

df = pd.read_csv("./mega_cap/IE000S9" \
"" \
"YS762.csv", parse_dates=["date"], index_col="date")
# plot_candlestick(df, style='yahoo', volume=True)
# plot_candlestick_with_sr(df, sr_levels, title='Candlestick with S/R', volume=True, style='yahoo', figsize=(12,6))

# Example with a tuple
# plot_candlestick_with_sr(df, sr_levels=(support, resistance))

# Example with dict
# plot_candlestick_with_sr(df, sr_levels={'support': [19200, 19350], 'resistance': [19800, 19950]})

# Example with list
plot_candlestick_with_sr(df, sr_levels=[487.49, 465.91488,452.56756, 441.78, 430.99244, 415.63388, 396.07])