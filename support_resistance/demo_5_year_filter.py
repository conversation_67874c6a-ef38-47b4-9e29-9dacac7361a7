#!/usr/bin/env python3
"""
Demonstration script showing the 5-year data filtering functionality
in the Master Support & Resistance Analyzer.
"""

import json
import pandas as pd
from pathlib import Path
from master_support_resistance_analyzer import SupportResistanceAnalyzer


def demo_data_filtering():
    """Demonstrate the 5-year data filtering"""
    print("📅 5-Year Data Filtering Demo")
    print("=" * 50)
    
    # Test files to demonstrate filtering
    test_files = [
        "IE000S9YS762.csv",  # Linde plc
        "US0231351067.csv",  # Amazon.com Inc
        "US0378331005.csv",  # Apple Inc
    ]
    
    analyzer = SupportResistanceAnalyzer()
    
    for csv_file in test_files:
        csv_path = Path("mega_cap") / csv_file
        if not csv_path.exists():
            print(f"⚠️  {csv_file} not found, skipping...")
            continue
        
        print(f"\n📄 Processing: {csv_file}")
        
        try:
            # Load raw data first to show original size
            raw_df = pd.read_csv(csv_path, parse_dates=["date"], index_col="date")
            raw_df = raw_df.dropna()
            
            print(f"   📊 Original data: {len(raw_df)} records")
            print(f"   📅 Original range: {raw_df.index.min().strftime('%Y-%m-%d')} to {raw_df.index.max().strftime('%Y-%m-%d')}")
            print(f"   ⏱️  Total years: {round((raw_df.index.max() - raw_df.index.min()).days / 365.25, 1)} years")
            
            # Now load with analyzer (which applies 5-year filter)
            analyzer.load_data(csv_file)
            
            filtered_df = analyzer.df
            reduction_pct = round((1 - len(filtered_df) / len(raw_df)) * 100, 1)
            
            print(f"   ✂️  After filtering: {len(filtered_df)} records ({reduction_pct}% reduction)")
            print(f"   📅 Filtered range: {filtered_df.index.min().strftime('%Y-%m-%d')} to {filtered_df.index.max().strftime('%Y-%m-%d')}")
            print(f"   ⏱️  Years covered: {round((filtered_df.index.max() - filtered_df.index.min()).days / 365.25, 1)} years")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")


def demo_analysis_comparison():
    """Compare analysis results with and without filtering"""
    print("\n📊 Analysis Impact Demo")
    print("=" * 50)
    
    csv_file = "IE000S9YS762.csv"
    csv_path = Path("mega_cap") / csv_file
    
    if not csv_path.exists():
        print(f"❌ {csv_file} not found")
        return
    
    print(f"📄 Analyzing: {csv_file}")
    
    # Load raw data
    raw_df = pd.read_csv(csv_path, parse_dates=["date"], index_col="date")
    raw_df = raw_df.dropna()
    
    # Create analyzer and load filtered data
    analyzer = SupportResistanceAnalyzer()
    analyzer.load_data(csv_file)
    filtered_df = analyzer.df
    
    print(f"\n📈 Price Analysis Comparison:")
    print(f"   Original Data ({len(raw_df)} records):")
    print(f"     💰 Price Range: ${raw_df['Low'].min():.2f} - ${raw_df['High'].max():.2f}")
    print(f"     📊 Current Price: ${raw_df['Close'].iloc[-1]:.2f}")
    print(f"     📈 Price Change: {((raw_df['Close'].iloc[-1] / raw_df['Close'].iloc[0]) - 1) * 100:.1f}%")
    
    print(f"\n   Filtered Data (Last 5 Years - {len(filtered_df)} records):")
    print(f"     💰 Price Range: ${filtered_df['Low'].min():.2f} - ${filtered_df['High'].max():.2f}")
    print(f"     📊 Current Price: ${filtered_df['Close'].iloc[-1]:.2f}")
    print(f"     📈 Price Change: {((filtered_df['Close'].iloc[-1] / filtered_df['Close'].iloc[0]) - 1) * 100:.1f}%")
    
    # Show volume comparison
    print(f"\n📊 Volume Analysis:")
    print(f"   Original Data:")
    print(f"     📊 Avg Volume: {raw_df['Volume'].mean():,.0f}")
    print(f"     📈 Max Volume: {raw_df['Volume'].max():,.0f}")
    
    print(f"   Filtered Data:")
    print(f"     📊 Avg Volume: {filtered_df['Volume'].mean():,.0f}")
    print(f"     📈 Max Volume: {filtered_df['Volume'].max():,.0f}")


def demo_performance_benefits():
    """Show performance benefits of 5-year filtering"""
    print("\n⚡ Performance Benefits Demo")
    print("=" * 50)
    
    # Check existing analysis results
    output_dir = Path("output")
    if not output_dir.exists():
        print("No analysis results found. Run an analysis first!")
        return
    
    # Find a company folder with analysis results
    company_folder = None
    for folder in output_dir.iterdir():
        if folder.is_dir() and (folder / "analysis_summary.json").exists():
            company_folder = folder
            break
    
    if not company_folder:
        print("No analysis results found!")
        return
    
    # Load analysis summary
    with open(company_folder / "analysis_summary.json", 'r') as f:
        summary = json.load(f)
    
    analysis_info = summary['analysis_info']
    
    print(f"📁 Analysis Results: {company_folder.name}")
    print(f"🏢 Company: {analysis_info.get('company_name', 'N/A')}")
    print(f"📊 Data Points: {analysis_info.get('data_points', 'N/A'):,}")
    print(f"📅 Date Range: {analysis_info.get('date_range', {}).get('start', 'N/A')[:10]} to {analysis_info.get('date_range', {}).get('end', 'N/A')[:10]}")
    print(f"⏱️  Years Covered: {analysis_info.get('date_range', {}).get('years_covered', 'N/A')} years")
    print(f"🔍 Data Filter: {analysis_info.get('data_filter', 'None')}")
    
    # Show file sizes
    print(f"\n📁 Generated Files:")
    
    # Check charts
    charts_dir = company_folder / "charts"
    if charts_dir.exists():
        chart_files = list(charts_dir.glob("*.png"))
        total_chart_size = sum(f.stat().st_size for f in chart_files) / (1024 * 1024)  # MB
        print(f"   📈 Static Charts: {len(chart_files)} files ({total_chart_size:.1f} MB)")
    
    # Check interactive charts
    interactive_dir = company_folder / "interactive_charts"
    if interactive_dir.exists():
        interactive_files = list(interactive_dir.glob("*.html"))
        total_interactive_size = sum(f.stat().st_size for f in interactive_files) / (1024 * 1024)  # MB
        print(f"   🖱️  Interactive Charts: {len(interactive_files)} files ({total_interactive_size:.1f} MB)")
    
    # Check signals
    signals_dir = company_folder / "signals"
    if signals_dir.exists():
        signal_files = list(signals_dir.glob("*.csv"))
        total_signal_size = sum(f.stat().st_size for f in signal_files) / (1024 * 1024)  # MB
        print(f"   🎯 Signal Files: {len(signal_files)} files ({total_signal_size:.1f} MB)")
    
    print(f"\n✅ Benefits of 5-Year Filtering:")
    print(f"   ⚡ Faster processing (less data to analyze)")
    print(f"   📊 More relevant analysis (recent market behavior)")
    print(f"   💾 Smaller file sizes (charts and data)")
    print(f"   🖱️  Better interactive chart performance")
    print(f"   🎯 More accurate signals (current market conditions)")


def show_filtering_logic():
    """Explain the filtering logic"""
    print("\n🔧 Filtering Logic Explanation")
    print("=" * 50)
    
    print("📅 How 5-Year Filtering Works:")
    print("   1. Load complete dataset from CSV")
    print("   2. Find the latest date in the dataset")
    print("   3. Calculate date 5 years ago from latest date")
    print("   4. Filter data to include only records >= 5 years ago")
    print("   5. Proceed with analysis on filtered data")
    
    print(f"\n💡 Example:")
    print(f"   📊 Original dataset: 2010-01-01 to 2025-07-15 (15+ years)")
    print(f"   📅 Latest date: 2025-07-15")
    print(f"   ✂️  5 years ago: 2020-07-15")
    print(f"   📈 Filtered data: 2020-07-15 to 2025-07-15 (5 years)")
    
    print(f"\n🎯 Why 5 Years?")
    print(f"   📊 Captures recent market behavior and trends")
    print(f"   ⚡ Balances data richness with processing speed")
    print(f"   🎯 More relevant for current trading decisions")
    print(f"   📈 Includes major market events (COVID, recovery, etc.)")
    print(f"   💾 Manageable data size for interactive charts")


def main():
    """Main demonstration function"""
    print("🚀 Master Support & Resistance Analyzer")
    print("📅 5-Year Data Filtering Demo")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("mega_cap").exists():
        print("❌ Please run this script from the support_resistance directory")
        return 1
    
    try:
        # Demo 1: Data filtering
        demo_data_filtering()
        
        # Demo 2: Analysis comparison
        demo_analysis_comparison()
        
        # Demo 3: Performance benefits
        demo_performance_benefits()
        
        # Demo 4: Filtering logic
        show_filtering_logic()
        
        print("\n" + "=" * 60)
        print("🎉 5-Year Filtering Demo Complete!")
        
        print("\n💡 Key Benefits:")
        print("   📅 Focus on recent 5 years of market data")
        print("   ⚡ Faster processing and analysis")
        print("   📊 More relevant support/resistance levels")
        print("   🖱️  Better interactive chart performance")
        print("   💾 Smaller file sizes and memory usage")
        
        print("\n🚀 The analysis now automatically filters to the last 5 years!")
        print("   This provides more relevant and actionable insights")
        print("   while maintaining excellent performance.")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
