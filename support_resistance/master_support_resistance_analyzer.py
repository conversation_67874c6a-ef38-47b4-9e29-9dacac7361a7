#!/usr/bin/env python3
"""
Master Support & Resistance Analyzer

This script orchestrates the complete support and resistance analysis pipeline:
1. Identifies support and resistance levels using multiple methods
2. Plots candlestick charts with S/R levels
3. Generates trading signals based on S/R levels

Usage:
    python master_support_resistance_analyzer.py <csv_filename>
    python master_support_resistance_analyzer.py IE000S9YS762.csv
    python master_support_resistance_analyzer.py --all  # Process all CSV files
"""

import os
import sys
import json
import argparse
import pandas as pd
import numpy as np
import mplfinance as mpf
import plotly.graph_objects as go
import plotly.subplots as sp
from plotly.offline import plot
from pathlib import Path
from datetime import datetime

# Import functions from the three analysis scripts
from scipy.signal import argrelextrema
from sklearn.cluster import KMeans
from scipy.stats import gaussian_kde


class SupportResistanceAnalyzer:
    """Complete Support & Resistance Analysis Pipeline"""
    
    def __init__(self, data_dir="mega_cap", output_base_dir="output"):
        self.data_dir = Path(data_dir)
        self.output_base_dir = Path(output_base_dir)
        self.current_output_dir = None
        self.df = None
        self.csv_name = None
        
    def load_data(self, csv_filename):
        """Load and prepare data from CSV file"""
        csv_path = self.data_dir / csv_filename
        if not csv_path.exists():
            raise FileNotFoundError(f"CSV file not found: {csv_path}")

        self.csv_name = csv_filename.replace('.csv', '')

        # Load data
        df = pd.read_csv(csv_path, parse_dates=["date"], index_col="date")
        df = df.dropna()

        # Filter to last 5 years only
        df = self._filter_last_5_years(df)

        # Extract company name if available
        self.company_name = None
        if 'conm' in df.columns:
            # Get the most common company name (in case of variations)
            company_names = df['conm'].dropna().value_counts()
            if len(company_names) > 0:
                self.company_name = company_names.index[0]
                # Clean company name for folder naming
                self.company_name_clean = self._clean_company_name(self.company_name)

        # Ensure required columns exist
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")

        self.df = df
        company_info = f" ({self.company_name})" if self.company_name else ""
        date_range = f" ({df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')})"
        print(f"✅ Loaded data for {self.csv_name}{company_info}: {len(df)} records{date_range}")
        return df

    def _filter_last_5_years(self, df):
        """Filter dataframe to include only the last 5 years of data"""
        if len(df) == 0:
            return df

        # Get the latest date in the dataset
        latest_date = df.index.max()

        # Calculate 5 years ago from the latest date
        five_years_ago = latest_date - pd.DateOffset(years=5)

        # Filter the dataframe
        filtered_df = df[df.index >= five_years_ago]

        original_count = len(df)
        filtered_count = len(filtered_df)

        if filtered_count < original_count:
            print(f"📅 Filtered to last 5 years: {original_count} → {filtered_count} records")
            print(f"   Date range: {filtered_df.index.min().strftime('%Y-%m-%d')} to {filtered_df.index.max().strftime('%Y-%m-%d')}")

        return filtered_df

    def _clean_company_name(self, company_name):
        """Clean company name for use in folder names"""
        if not company_name:
            return ""

        # Remove common suffixes and clean for filesystem
        import re

        # Remove common corporate suffixes
        suffixes = [' Inc', ' Corp', ' Corporation', ' Ltd', ' Limited', ' plc', ' PLC',
                   ' LLC', ' Co', ' Company', ' Group', ' Holdings', ' AG', ' SA', ' SE']

        cleaned = company_name
        for suffix in suffixes:
            if cleaned.endswith(suffix):
                cleaned = cleaned[:-len(suffix)]
                break

        # Replace problematic characters for folder names
        cleaned = re.sub(r'[<>:"/\\|?*]', '_', cleaned)
        cleaned = re.sub(r'[^\w\s-]', '', cleaned)
        cleaned = re.sub(r'\s+', '_', cleaned.strip())

        # Limit length
        if len(cleaned) > 30:
            cleaned = cleaned[:30]

        return cleaned
    
    def setup_output_directory(self):
        """Create output directory structure"""
        # Create folder name with company name if available
        if self.company_name_clean:
            folder_name = f"{self.csv_name}_{self.company_name_clean}"
        else:
            folder_name = self.csv_name

        self.current_output_dir = self.output_base_dir / folder_name
        self.current_output_dir.mkdir(parents=True, exist_ok=True)

        print(f"📁 Output directory: {self.current_output_dir}")
        if self.company_name:
            print(f"📊 Company: {self.company_name}")
    
    # ==================== SUPPORT & RESISTANCE IDENTIFICATION ====================
    
    def get_swing_points(self, order=3):
        """Identify swing highs and lows"""
        highs = self.df['High'].values
        lows = self.df['Low'].values
        
        swing_highs_idx = argrelextrema(highs, np.greater_equal, order=order)[0]
        swing_lows_idx = argrelextrema(lows, np.less_equal, order=order)[0]

        swing_highs = self.df.iloc[swing_highs_idx][['High']]
        swing_lows = self.df.iloc[swing_lows_idx][['Low']]

        return swing_highs, swing_lows
    
    def compute_vwpp(self):
        """Compute Volume Weighted Price Position"""
        vwpp = ((2 * self.df['Close'] - self.df['Low'] - self.df['High']) / 
                (self.df['High'] - self.df['Low']).replace(0, np.nan)) * self.df['Volume']
        return vwpp
    
    def get_swing_points_enhanced(self, order=3, vwpp_threshold=0.0):
        """Enhanced swing points with VWPP filtering"""
        df_copy = self.df.copy()
        df_copy['vwpp'] = self.compute_vwpp()

        highs = df_copy['High'].values
        lows = df_copy['Low'].values

        swing_highs_idx = argrelextrema(highs, np.greater_equal, order=order)[0]
        swing_lows_idx = argrelextrema(lows, np.less_equal, order=order)[0]

        swing_highs = df_copy.iloc[swing_highs_idx][['High', 'vwpp']].copy()
        swing_highs = swing_highs[swing_highs['vwpp'] >= vwpp_threshold]
        swing_highs['type'] = 'resistance'
        swing_highs.rename(columns={'High': 'Price'}, inplace=True)

        swing_lows = df_copy.iloc[swing_lows_idx][['Low', 'vwpp']].copy()
        swing_lows = swing_lows[swing_lows['vwpp'] <= -vwpp_threshold]
        swing_lows['type'] = 'support'
        swing_lows.rename(columns={'Low': 'Price'}, inplace=True)

        swings = pd.concat([swing_highs, swing_lows]).sort_index()
        return swings
    
    def get_volume_profile_levels(self, bins=50):
        """Calculate volume profile support and resistance"""
        price = ((self.df['High'] + self.df['Low'] + self.df['Close']) / 3).values
        hist, bin_edges = np.histogram(price, bins=bins, weights=self.df['Volume'])
        
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        top_indices = np.argsort(hist)[-3:]
        top_levels = bin_centers[top_indices]
        top_levels.sort()
        
        return top_levels[0], top_levels[-1]
    
    def get_kmeans_sr(self, k=3):
        """KMeans clustering for support/resistance"""
        prices = self.df[['Open', 'High', 'Low', 'Close']].mean(axis=1).values.reshape(-1, 1)
        kmeans = KMeans(n_clusters=k, n_init=10, random_state=42).fit(prices)
        centers = sorted(kmeans.cluster_centers_.flatten())
        return centers[0], centers[-1]
    
    def get_kde_sr(self, bw_method='scott', num_peaks=2):
        """KDE-based support/resistance"""
        prices = self.df['Close'].values
        kde = gaussian_kde(prices, bw_method=bw_method)
        
        price_range = np.linspace(prices.min(), prices.max(), 1000)
        density = kde(price_range)

        peak_indices = np.argsort(density)[-num_peaks:]
        peak_levels = price_range[peak_indices]
        sorted_levels = sorted(peak_levels)
        
        return sorted_levels[0], sorted_levels[-1]
    
    def get_fibonacci_levels(self):
        """Calculate Fibonacci retracement levels"""
        max_price = self.df['High'].max()
        min_price = self.df['Low'].min()
        diff = max_price - min_price

        levels = {
            '0.0': max_price,
            '0.236': max_price - 0.236 * diff,
            '0.382': max_price - 0.382 * diff,
            '0.5': max_price - 0.5 * diff,
            '0.618': max_price - 0.618 * diff,
            '0.786': max_price - 0.786 * diff,
            '1.0': min_price,
        }
        return levels
    
    def identify_all_levels(self):
        """Run all support/resistance identification methods"""
        print("🔍 Identifying support and resistance levels...")
        
        # Basic swing points
        swing_highs, swing_lows = self.get_swing_points()
        
        # Enhanced swing points
        vwpp_threshold = self.df['Volume'].quantile(0.5)
        swings_enhanced = self.get_swing_points_enhanced(order=5, vwpp_threshold=vwpp_threshold)
        
        # Volume profile
        volume_support, volume_resistance = self.get_volume_profile_levels()
        
        # KMeans clustering
        kmeans_support, kmeans_resistance = self.get_kmeans_sr()
        
        # KDE analysis
        kde_support, kde_resistance = self.get_kde_sr()
        
        # Fibonacci levels
        fib_levels = self.get_fibonacci_levels()
        
        # Store results
        self.results = {
            'swing_highs': swing_highs,
            'swing_lows': swing_lows,
            'swings_enhanced': swings_enhanced,
            'volume_support': volume_support,
            'volume_resistance': volume_resistance,
            'kmeans_support': kmeans_support,
            'kmeans_resistance': kmeans_resistance,
            'kde_support': kde_support,
            'kde_resistance': kde_resistance,
            'fibonacci_levels': fib_levels
        }
        
        print(f"✅ Identified support and resistance levels using 6 methods")
        return self.results
    
    def save_sr_results(self):
        """Save support/resistance analysis results"""
        print("💾 Saving support/resistance results...")
        
        # Save swing points
        self.results['swing_highs'].to_csv(self.current_output_dir / "swing_highs.csv")
        self.results['swing_lows'].to_csv(self.current_output_dir / "swing_lows.csv")
        self.results['swings_enhanced'].to_csv(self.current_output_dir / "swings_enhanced.csv")
        
        # Save level pairs
        levels_summary = {
            'volume_profile': {
                'support': float(self.results['volume_support']),
                'resistance': float(self.results['volume_resistance'])
            },
            'kmeans': {
                'support': float(self.results['kmeans_support']),
                'resistance': float(self.results['kmeans_resistance'])
            },
            'kde': {
                'support': float(self.results['kde_support']),
                'resistance': float(self.results['kde_resistance'])
            }
        }
        
        with open(self.current_output_dir / "sr_levels_summary.json", "w") as f:
            json.dump(levels_summary, f, indent=4)
        
        # Save Fibonacci levels
        with open(self.current_output_dir / "fibonacci_levels.json", "w") as f:
            json.dump(self.results['fibonacci_levels'], f, indent=4)
        
        print(f"✅ Saved support/resistance results to {self.current_output_dir}")
    
    # ==================== CANDLESTICK PLOTTING ====================
    
    def plot_candlestick_with_sr(self, sr_levels, title=None, save_path=None):
        """Plot candlestick chart with support/resistance levels"""
        if title is None:
            company_part = f" ({self.company_name})" if self.company_name else ""
            title = f'{self.csv_name}{company_part} - Candlestick with Support/Resistance'
        
        df_plot = self.df.copy()
        
        # Ensure datetime index
        if not pd.api.types.is_datetime64_any_dtype(df_plot.index):
            df_plot.index = pd.to_datetime(df_plot.index)

        # Extract S/R levels for plotting
        support_levels = []
        resistance_levels = []

        if isinstance(sr_levels, dict):
            support_levels = sr_levels.get('support', [])
            resistance_levels = sr_levels.get('resistance', [])
        elif isinstance(sr_levels, tuple):
            support_levels = [sr_levels[0]]
            resistance_levels = [sr_levels[1]]
        elif isinstance(sr_levels, list):
            # Split levels based on price position
            median_price = df_plot['Close'].median()
            support_levels = [x for x in sr_levels if x < median_price]
            resistance_levels = [x for x in sr_levels if x >= median_price]

        # Create horizontal lines
        hlines = support_levels + resistance_levels
        hl_colors = ['green'] * len(support_levels) + ['red'] * len(resistance_levels)

        # Plot configuration
        plot_config = {
            'type': 'candle',
            'volume': True,
            'style': 'yahoo',
            'title': title,
            'ylabel': 'Price',
            'ylabel_lower': 'Volume',
            'figratio': (12, 8),
            'figscale': 1.1,
            'warn_too_much_data': 2000  # Suppress warning for up to 2000 data points
        }
        
        if hlines:
            plot_config['hlines'] = dict(hlines=hlines, colors=hl_colors, linewidths=1.0, linestyle='dashed')
        
        if save_path:
            plot_config['savefig'] = save_path
        
        mpf.plot(df_plot[['Open', 'High', 'Low', 'Close', 'Volume']], **plot_config)
    
    def generate_all_charts(self):
        """Generate all candlestick charts with different S/R methods"""
        print("📊 Generating candlestick charts...")
        
        charts_dir = self.current_output_dir / "charts"
        charts_dir.mkdir(exist_ok=True)
        
        # Prepare company name for titles
        company_part = f" ({self.company_name})" if self.company_name else ""

        # Chart 1: Volume Profile levels
        volume_levels = {
            'support': [self.results['volume_support']],
            'resistance': [self.results['volume_resistance']]
        }
        self.plot_candlestick_with_sr(
            volume_levels,
            title=f'{self.csv_name}{company_part} - Volume Profile S/R',
            save_path=charts_dir / "volume_profile_sr.png"
        )

        # Chart 2: KMeans levels
        kmeans_levels = {
            'support': [self.results['kmeans_support']],
            'resistance': [self.results['kmeans_resistance']]
        }
        self.plot_candlestick_with_sr(
            kmeans_levels,
            title=f'{self.csv_name}{company_part} - KMeans S/R',
            save_path=charts_dir / "kmeans_sr.png"
        )

        # Chart 3: KDE levels
        kde_levels = {
            'support': [self.results['kde_support']],
            'resistance': [self.results['kde_resistance']]
        }
        self.plot_candlestick_with_sr(
            kde_levels,
            title=f'{self.csv_name}{company_part} - KDE S/R',
            save_path=charts_dir / "kde_sr.png"
        )

        # Chart 4: Fibonacci levels
        fib_values = list(self.results['fibonacci_levels'].values())
        self.plot_candlestick_with_sr(
            fib_values,
            title=f'{self.csv_name}{company_part} - Fibonacci Levels',
            save_path=charts_dir / "fibonacci_levels.png"
        )
        
        print(f"✅ Generated 4 candlestick charts in {charts_dir}")

    # ==================== INTERACTIVE PLOTLY CHARTS ====================

    def create_interactive_candlestick(self, sr_levels, title=None, filename=None):
        """Create interactive Plotly candlestick chart with hover data"""
        df_plot = self.df.copy()

        # Ensure datetime index
        if not pd.api.types.is_datetime64_any_dtype(df_plot.index):
            df_plot.index = pd.to_datetime(df_plot.index)

        # Create subplot with secondary y-axis for volume
        fig = sp.make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=(title or 'Candlestick Chart', 'Volume'),
            row_width=[0.7, 0.3]
        )

        # Add candlestick chart
        candlestick = go.Candlestick(
            x=df_plot.index,
            open=df_plot['Open'],
            high=df_plot['High'],
            low=df_plot['Low'],
            close=df_plot['Close'],
            name='Price',
            hoverinfo='x+text',
            text=[f'Open: ${o:.2f}<br>High: ${h:.2f}<br>Low: ${l:.2f}<br>Close: ${c:.2f}'
                  for o, h, l, c in zip(df_plot['Open'], df_plot['High'],
                                       df_plot['Low'], df_plot['Close'])]
        )
        fig.add_trace(candlestick, row=1, col=1)

        # Add volume bars
        colors = ['red' if close < open else 'green'
                 for close, open in zip(df_plot['Close'], df_plot['Open'])]

        volume_bar = go.Bar(
            x=df_plot.index,
            y=df_plot['Volume'],
            name='Volume',
            marker_color=colors,
            opacity=0.7,
            hoverinfo='x+y+text',
            text=[f'Volume: {v:,.0f}' for v in df_plot['Volume']]
        )
        fig.add_trace(volume_bar, row=2, col=1)

        # Add support and resistance lines
        if isinstance(sr_levels, dict):
            support_levels = sr_levels.get('support', [])
            resistance_levels = sr_levels.get('resistance', [])
        elif isinstance(sr_levels, tuple):
            support_levels = [sr_levels[0]]
            resistance_levels = [sr_levels[1]]
        elif isinstance(sr_levels, list):
            # Split levels based on price position
            median_price = df_plot['Close'].median()
            support_levels = [x for x in sr_levels if x < median_price]
            resistance_levels = [x for x in sr_levels if x >= median_price]
        else:
            support_levels = []
            resistance_levels = []

        # Add support lines
        for level in support_levels:
            fig.add_hline(
                y=level,
                line=dict(color='green', width=2, dash='dash'),
                annotation_text=f'Support: ${level:.2f}',
                annotation_position='bottom right',
                row=1, col=1
            )

        # Add resistance lines
        for level in resistance_levels:
            fig.add_hline(
                y=level,
                line=dict(color='red', width=2, dash='dash'),
                annotation_text=f'Resistance: ${level:.2f}',
                annotation_position='top right',
                row=1, col=1
            )

        # Update layout
        fig.update_layout(
            title=dict(
                text=title or 'Interactive Candlestick Chart',
                x=0.5,
                font=dict(size=16)
            ),
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True,
            hovermode='x unified',
            template='plotly_white'
        )

        # Update axes
        fig.update_xaxes(title_text='Date', row=2, col=1)
        fig.update_yaxes(title_text='Price ($)', row=1, col=1)
        fig.update_yaxes(title_text='Volume', row=2, col=1)

        # Save as HTML file
        if filename:
            fig.write_html(filename)

        return fig

    def generate_interactive_charts(self):
        """Generate interactive Plotly charts with hover data"""
        print("🖱️  Generating interactive charts...")

        charts_dir = self.current_output_dir / "interactive_charts"
        charts_dir.mkdir(exist_ok=True)

        # Prepare company name for titles
        company_part = f" ({self.company_name})" if self.company_name else ""

        # Interactive Chart 1: Volume Profile levels
        volume_levels = {
            'support': [self.results['volume_support']],
            'resistance': [self.results['volume_resistance']]
        }
        self.create_interactive_candlestick(
            volume_levels,
            title=f'{self.csv_name}{company_part} - Volume Profile S/R (Interactive)',
            filename=charts_dir / "volume_profile_sr_interactive.html"
        )

        # Interactive Chart 2: KMeans levels
        kmeans_levels = {
            'support': [self.results['kmeans_support']],
            'resistance': [self.results['kmeans_resistance']]
        }
        self.create_interactive_candlestick(
            kmeans_levels,
            title=f'{self.csv_name}{company_part} - KMeans S/R (Interactive)',
            filename=charts_dir / "kmeans_sr_interactive.html"
        )

        # Interactive Chart 3: KDE levels
        kde_levels = {
            'support': [self.results['kde_support']],
            'resistance': [self.results['kde_resistance']]
        }
        self.create_interactive_candlestick(
            kde_levels,
            title=f'{self.csv_name}{company_part} - KDE S/R (Interactive)',
            filename=charts_dir / "kde_sr_interactive.html"
        )

        # Interactive Chart 4: Fibonacci levels
        fib_values = list(self.results['fibonacci_levels'].values())
        self.create_interactive_candlestick(
            fib_values,
            title=f'{self.csv_name}{company_part} - Fibonacci Levels (Interactive)',
            filename=charts_dir / "fibonacci_levels_interactive.html"
        )

        # Interactive Chart 5: All levels combined
        all_levels = {
            'support': [
                self.results['volume_support'],
                self.results['kmeans_support'],
                self.results['kde_support']
            ],
            'resistance': [
                self.results['volume_resistance'],
                self.results['kmeans_resistance'],
                self.results['kde_resistance']
            ]
        }
        self.create_interactive_candlestick(
            all_levels,
            title=f'{self.csv_name}{company_part} - All S/R Methods Combined (Interactive)',
            filename=charts_dir / "all_methods_combined_interactive.html"
        )

        print(f"✅ Generated 5 interactive charts in {charts_dir}")

    # ==================== SIGNAL GENERATION ====================

    def generate_sr_features(self, support_level, resistance_level):
        """Generate trading signals based on support/resistance levels"""
        df_signals = self.df.copy()

        # Add S/R levels to dataframe
        df_signals['support_level'] = support_level
        df_signals['resistance_level'] = resistance_level

        # --- Band and distance features ---
        df_signals['sr_bandwidth'] = df_signals['resistance_level'] - df_signals['support_level']
        df_signals['dist_to_support'] = df_signals['Close'] - df_signals['support_level']
        df_signals['dist_to_resistance'] = df_signals['resistance_level'] - df_signals['Close']
        df_signals['pct_to_support'] = (df_signals['Close'] - df_signals['support_level']) / df_signals['Close']
        df_signals['pct_to_resistance'] = (df_signals['resistance_level'] - df_signals['Close']) / df_signals['Close']
        df_signals['pct_through_band'] = (df_signals['Close'] - df_signals['support_level']) / df_signals['sr_bandwidth']

        # --- Zone classification ---
        df_signals['sr_zone'] = np.select(
            [
                df_signals['Close'] < df_signals['support_level'],
                (df_signals['Close'] >= df_signals['support_level']) & (df_signals['Close'] <= df_signals['resistance_level']),
                df_signals['Close'] > df_signals['resistance_level']
            ],
            [-1, 0, 1]
        )

        # --- Cross detection ---
        df_signals['crossed_support'] = ((df_signals['Close'] < df_signals['support_level']) &
                                       (df_signals['Close'].shift(1) >= df_signals['support_level'])).astype(int)
        df_signals['crossed_resistance'] = ((df_signals['Close'] > df_signals['resistance_level']) &
                                          (df_signals['Close'].shift(1) <= df_signals['resistance_level'])).astype(int)

        # --- Bounce detection ---
        df_signals['bounce_from_support'] = ((df_signals['Close'] > df_signals['support_level']) &
                                           (df_signals['Close'].shift(1) <= df_signals['support_level'])).astype(int)
        df_signals['bounce_from_resistance'] = ((df_signals['Close'] < df_signals['resistance_level']) &
                                               (df_signals['Close'].shift(1) >= df_signals['resistance_level'])).astype(int)

        # --- Breakout / breakdown ---
        df_signals['breakout'] = ((df_signals['Close'] > df_signals['resistance_level']) &
                                (df_signals['Close'].shift(1) <= df_signals['resistance_level'])).astype(int)
        df_signals['breakdown'] = ((df_signals['Close'] < df_signals['support_level']) &
                                 (df_signals['Close'].shift(1) >= df_signals['support_level'])).astype(int)

        # --- Volume confirmation ---
        df_signals['volume_spike'] = df_signals['Volume'] > df_signals['Volume'].rolling(10).mean() * 1.5
        df_signals['confirmed_breakout'] = df_signals['breakout'] & df_signals['volume_spike']
        df_signals['confirmed_breakdown'] = df_signals['breakdown'] & df_signals['volume_spike']

        # --- Near support/resistance ---
        df_signals['near_support'] = (abs(df_signals['Close'] - df_signals['support_level']) / df_signals['Close'] < 0.01).astype(int)
        df_signals['near_resistance'] = (abs(df_signals['Close'] - df_signals['resistance_level']) / df_signals['Close'] < 0.01).astype(int)

        # --- Trading signals ---
        df_signals['buy_signal'] = (df_signals['bounce_from_support'] |
                                  (df_signals['near_support'] & (df_signals['Close'] > df_signals['Open']))).astype(int)
        df_signals['sell_signal'] = (df_signals['bounce_from_resistance'] |
                                   (df_signals['near_resistance'] & (df_signals['Close'] < df_signals['Open']))).astype(int)
        df_signals['breakout_signal'] = df_signals['confirmed_breakout']
        df_signals['breakdown_signal'] = df_signals['confirmed_breakdown']

        return df_signals

    def generate_all_signals(self):
        """Generate signals for all S/R methods"""
        print("🎯 Generating trading signals...")

        signals_dir = self.current_output_dir / "signals"
        signals_dir.mkdir(exist_ok=True)

        # Generate signals for each method
        methods = {
            'volume_profile': (self.results['volume_support'], self.results['volume_resistance']),
            'kmeans': (self.results['kmeans_support'], self.results['kmeans_resistance']),
            'kde': (self.results['kde_support'], self.results['kde_resistance'])
        }

        signal_summaries = {}

        for method_name, (support, resistance) in methods.items():
            df_signals = self.generate_sr_features(support, resistance)

            # Save detailed signals
            df_signals.to_csv(signals_dir / f"{method_name}_signals.csv")

            # Create signal summary
            signal_summary = {
                'total_buy_signals': int(df_signals['buy_signal'].sum()),
                'total_sell_signals': int(df_signals['sell_signal'].sum()),
                'total_breakouts': int(df_signals['breakout_signal'].sum()),
                'total_breakdowns': int(df_signals['breakdown_signal'].sum()),
                'support_level': float(support),
                'resistance_level': float(resistance),
                'latest_signals': {
                    'buy': bool(df_signals['buy_signal'].iloc[-1]) if len(df_signals) > 0 else False,
                    'sell': bool(df_signals['sell_signal'].iloc[-1]) if len(df_signals) > 0 else False,
                    'breakout': bool(df_signals['breakout_signal'].iloc[-1]) if len(df_signals) > 0 else False,
                    'breakdown': bool(df_signals['breakdown_signal'].iloc[-1]) if len(df_signals) > 0 else False
                }
            }
            signal_summaries[method_name] = signal_summary

        # Save signal summaries
        with open(signals_dir / "signal_summaries.json", "w") as f:
            json.dump(signal_summaries, f, indent=4)

        print(f"✅ Generated signals for 3 methods in {signals_dir}")
        return signal_summaries

    # ==================== MAIN EXECUTION ====================

    def run_complete_analysis(self, csv_filename):
        """Run the complete support/resistance analysis pipeline"""
        print(f"\n🚀 Starting complete analysis for {csv_filename}")
        print("=" * 60)

        try:
            # Step 1: Load data
            self.load_data(csv_filename)

            # Step 2: Setup output directory
            self.setup_output_directory()

            # Step 3: Identify support/resistance levels
            self.identify_all_levels()
            self.save_sr_results()

            # Step 4: Generate candlestick charts
            self.generate_all_charts()

            # Step 4.5: Generate interactive charts
            self.generate_interactive_charts()

            # Step 5: Generate trading signals
            signal_summaries = self.generate_all_signals()

            # Step 6: Create analysis summary
            self.create_analysis_summary(signal_summaries)

            print(f"\n✅ Complete analysis finished for {csv_filename}")
            print(f"📁 Results saved in: {self.current_output_dir}")

            return True

        except Exception as e:
            print(f"\n❌ Analysis failed for {csv_filename}: {str(e)}")
            return False

    def create_analysis_summary(self, signal_summaries):
        """Create a comprehensive analysis summary"""
        summary = {
            'analysis_info': {
                'csv_file': self.csv_name + '.csv',
                'company_name': self.company_name,
                'company_name_clean': self.company_name_clean,
                'analysis_date': datetime.now().isoformat(),
                'data_points': len(self.df),
                'data_filter': 'Last 5 years only',
                'date_range': {
                    'start': str(self.df.index.min()),
                    'end': str(self.df.index.max()),
                    'years_covered': round((self.df.index.max() - self.df.index.min()).days / 365.25, 1)
                },
                'price_range': {
                    'min': float(self.df['Low'].min()),
                    'max': float(self.df['High'].max()),
                    'current': float(self.df['Close'].iloc[-1])
                }
            },
            'support_resistance_levels': {
                'volume_profile': {
                    'support': float(self.results['volume_support']),
                    'resistance': float(self.results['volume_resistance'])
                },
                'kmeans': {
                    'support': float(self.results['kmeans_support']),
                    'resistance': float(self.results['kmeans_resistance'])
                },
                'kde': {
                    'support': float(self.results['kde_support']),
                    'resistance': float(self.results['kde_resistance'])
                }
            },
            'fibonacci_levels': self.results['fibonacci_levels'],
            'signal_summaries': signal_summaries,
            'files_generated': {
                'charts': ['volume_profile_sr.png', 'kmeans_sr.png', 'kde_sr.png', 'fibonacci_levels.png'],
                'interactive_charts': [
                    'volume_profile_sr_interactive.html', 'kmeans_sr_interactive.html',
                    'kde_sr_interactive.html', 'fibonacci_levels_interactive.html',
                    'all_methods_combined_interactive.html'
                ],
                'data_files': ['swing_highs.csv', 'swing_lows.csv', 'swings_enhanced.csv'],
                'signal_files': ['volume_profile_signals.csv', 'kmeans_signals.csv', 'kde_signals.csv'],
                'summary_files': ['sr_levels_summary.json', 'fibonacci_levels.json', 'signal_summaries.json']
            }
        }

        with open(self.current_output_dir / "analysis_summary.json", "w") as f:
            json.dump(summary, f, indent=4)

        print(f"📋 Created comprehensive analysis summary")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Support & Resistance Analysis Master Script')
    parser.add_argument('csv_file', nargs='?', help='CSV filename to analyze (e.g., IE000S9YS762.csv)')
    parser.add_argument('--all', action='store_true', help='Process all CSV files in mega_cap folder')
    parser.add_argument('--data-dir', default='mega_cap', help='Directory containing CSV files')
    parser.add_argument('--output-dir', default='output', help='Base output directory')

    args = parser.parse_args()

    # Initialize analyzer
    analyzer = SupportResistanceAnalyzer(data_dir=args.data_dir, output_base_dir=args.output_dir)

    if args.all:
        # Process all CSV files
        csv_files = list(Path(args.data_dir).glob('*.csv'))
        if not csv_files:
            print(f"❌ No CSV files found in {args.data_dir}")
            return 1

        print(f"🔄 Processing {len(csv_files)} CSV files...")
        successful = 0
        failed = 0

        for csv_file in csv_files:
            if analyzer.run_complete_analysis(csv_file.name):
                successful += 1
            else:
                failed += 1

        print(f"\n📊 Batch processing complete:")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")

    elif args.csv_file:
        # Process single file
        if not args.csv_file.endswith('.csv'):
            args.csv_file += '.csv'

        success = analyzer.run_complete_analysis(args.csv_file)
        return 0 if success else 1

    else:
        # Show usage and available files
        print("📋 Support & Resistance Analysis Master Script")
        print("\nUsage:")
        print("  python master_support_resistance_analyzer.py <csv_filename>")
        print("  python master_support_resistance_analyzer.py --all")

        # List available CSV files
        csv_files = list(Path(args.data_dir).glob('*.csv'))
        if csv_files:
            print(f"\n📁 Available CSV files in {args.data_dir}:")
            for csv_file in sorted(csv_files)[:10]:  # Show first 10
                print(f"   - {csv_file.name}")
            if len(csv_files) > 10:
                print(f"   ... and {len(csv_files) - 10} more files")
        else:
            print(f"\n❌ No CSV files found in {args.data_dir}")

        return 1


if __name__ == "__main__":
    exit(main())
