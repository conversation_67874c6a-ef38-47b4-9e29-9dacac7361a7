#!/usr/bin/env python3
"""
Master Support & Resistance Analyzer

This script orchestrates the complete support and resistance analysis pipeline:
1. Identifies support and resistance levels using multiple methods
2. Plots candlestick charts with S/R levels
3. Generates trading signals based on S/R levels

Usage:
    python master_support_resistance_analyzer.py <csv_filename>
    python master_support_resistance_analyzer.py IE000S9YS762.csv
    python master_support_resistance_analyzer.py --all  # Process all CSV files
"""

import os
import sys
import json
import argparse
import pandas as pd
import numpy as np

import plotly.graph_objects as go
import plotly.subplots as sp
from plotly.offline import plot
from pathlib import Path
from datetime import datetime

# Import functions from the three analysis scripts
from scipy.signal import argrelextrema
from sklearn.cluster import KMeans
from scipy.stats import gaussian_kde


class SupportResistanceAnalyzer:
    """Complete Support & Resistance Analysis Pipeline"""
    
    def __init__(self, data_dir="mega_cap", output_base_dir="output"):
        self.data_dir = Path(data_dir)
        self.output_base_dir = Path(output_base_dir)
        self.current_output_dir = None
        self.df = None
        self.csv_name = None
        
    def load_data(self, csv_filename):
        """Load and prepare data from CSV file"""
        csv_path = self.data_dir / csv_filename
        if not csv_path.exists():
            raise FileNotFoundError(f"CSV file not found: {csv_path}")

        self.csv_name = csv_filename.replace('.csv', '')

        # Load data
        df = pd.read_csv(csv_path, parse_dates=["date"], index_col="date")
        df = df.dropna()

        # Filter to last 5 years only
        df = self._filter_last_n_years(df, 5)

        # Extract company name if available
        self.company_name = None
        if 'conm' in df.columns:
            # Get the most common company name (in case of variations)
            company_names = df['conm'].dropna().value_counts()
            if len(company_names) > 0:
                self.company_name = company_names.index[0]
                # Clean company name for folder naming
                self.company_name_clean = self._clean_company_name(self.company_name)

        # Ensure required columns exist
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")

        # Calculate future returns
        df = self._calculate_future_returns(df)

        self.df = df
        company_info = f" ({self.company_name})" if self.company_name else ""
        date_range = f" ({df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')})"
        print(f"✅ Loaded data for {self.csv_name}{company_info}: {len(df)} records{date_range}")
        return df

    def _filter_last_n_years(self, df, years):
        """Filter dataframe to include only the last N years of data"""
        if len(df) == 0:
            return df

        # Get the latest date in the dataset
        latest_date = df.index.max()

        # Calculate 5 years ago from the latest date
        n_years_ago = latest_date - pd.DateOffset(years=years)

        # Filter the dataframe
        filtered_df = df[df.index >= n_years_ago]

        original_count = len(df)
        filtered_count = len(filtered_df)

        if filtered_count < original_count:
            print(f"📅 Filtered to last {years} years: {original_count} → {filtered_count} records")
            print(f"   Date range: {filtered_df.index.min().strftime('%Y-%m-%d')} to {filtered_df.index.max().strftime('%Y-%m-%d')}")

        return filtered_df

    def _clean_company_name(self, company_name):
        """Clean company name for use in folder names"""
        if not company_name:
            return ""

        # Remove common suffixes and clean for filesystem
        import re

        # Remove common corporate suffixes
        suffixes = [' Inc', ' Corp', ' Corporation', ' Ltd', ' Limited', ' plc', ' PLC',
                   ' LLC', ' Co', ' Company', ' Group', ' Holdings', ' AG', ' SA', ' SE']

        cleaned = company_name
        for suffix in suffixes:
            if cleaned.endswith(suffix):
                cleaned = cleaned[:-len(suffix)]
                break

        # Replace problematic characters for folder names
        cleaned = re.sub(r'[<>:"/\\|?*]', '_', cleaned)
        cleaned = re.sub(r'[^\w\s-]', '', cleaned)
        cleaned = re.sub(r'\s+', '_', cleaned.strip())

        # Limit length
        if len(cleaned) > 30:
            cleaned = cleaned[:30]

        return cleaned

    def _calculate_future_returns(self, df):
        """Calculate actual monthly and quarterly returns (forward-looking)"""
        print("📈 Calculating future returns (monthly and quarterly)...")

        # Sort by date to ensure proper chronological order
        df = df.sort_index()

        # Calculate future returns
        df['actual_monthly_return'] = None
        df['actual_quarterly_return'] = None

        # Convert to numeric to handle any data type issues
        df['Close'] = pd.to_numeric(df['Close'], errors='coerce')

        for i in range(len(df)):
            current_date = df.index[i]
            current_close = df['Close'].iloc[i]

            if pd.isna(current_close):
                continue

            # Calculate 1 month future date (approximately 30 calendar days)
            monthly_future_date = current_date + pd.DateOffset(days=30)
            # Find the closest trading day to 1 month future
            monthly_mask = df.index >= monthly_future_date
            if monthly_mask.any():
                monthly_future_idx = df.index[monthly_mask][0]
                monthly_future_close = df.loc[monthly_future_idx, 'Close']
                if not pd.isna(monthly_future_close):
                    monthly_return = ((monthly_future_close - current_close) / current_close) * 100
                    df.loc[current_date, 'actual_monthly_return'] = monthly_return

            # Calculate 1 quarter future date (approximately 90 calendar days)
            quarterly_future_date = current_date + pd.DateOffset(days=90)
            # Find the closest trading day to 1 quarter future
            quarterly_mask = df.index >= quarterly_future_date
            if quarterly_mask.any():
                quarterly_future_idx = df.index[quarterly_mask][0]
                quarterly_future_close = df.loc[quarterly_future_idx, 'Close']
                if not pd.isna(quarterly_future_close):
                    quarterly_return = ((quarterly_future_close - current_close) / current_close) * 100
                    df.loc[current_date, 'actual_quarterly_return'] = quarterly_return

        # Convert return columns to numeric
        df['actual_monthly_return'] = pd.to_numeric(df['actual_monthly_return'], errors='coerce')
        df['actual_quarterly_return'] = pd.to_numeric(df['actual_quarterly_return'], errors='coerce')

        # Count non-null values
        monthly_count = df['actual_monthly_return'].notna().sum()
        quarterly_count = df['actual_quarterly_return'].notna().sum()

        print(f"   📊 Monthly returns calculated: {monthly_count}/{len(df)} records")
        print(f"   📊 Quarterly returns calculated: {quarterly_count}/{len(df)} records")

        return df

    def setup_output_directory(self, time_horizon_years=None):
        """Create output directory structure with optional time horizon subfolder"""
        # Create folder name with company name if available
        if self.company_name_clean:
            folder_name = f"{self.csv_name}_{self.company_name_clean}"
        else:
            folder_name = self.csv_name

        # Create base company directory
        base_output_dir = self.output_base_dir / folder_name
        base_output_dir.mkdir(parents=True, exist_ok=True)

        # Create time horizon subfolder if specified
        if time_horizon_years:
            time_horizon_folder = f"{time_horizon_years}_year{'s' if time_horizon_years != 1 else ''}"
            self.current_output_dir = base_output_dir / time_horizon_folder
            self.current_output_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 Output directory: {self.current_output_dir}")
            print(f"⏱️  Time horizon: {time_horizon_years} year{'s' if time_horizon_years != 1 else ''}")
        else:
            self.current_output_dir = base_output_dir
            print(f"📁 Output directory: {self.current_output_dir}")

        if self.company_name:
            print(f"📊 Company: {self.company_name}")
    
    # ==================== MULTI-TIME HORIZON ANALYSIS ====================

    def analyze_multiple_time_horizons(self, time_horizons=[1, 2, 3, 4, 5]):
        """Analyze support and resistance levels for multiple time horizons"""
        print(f"📊 Analyzing multiple time horizons: {time_horizons} years")

        # Load original unfiltered data
        csv_path = self.data_dir / f"{self.csv_name}.csv"
        original_df = pd.read_csv(csv_path, parse_dates=["date"], index_col="date")
        original_df = original_df.dropna()

        # Calculate future returns on full dataset first
        original_df = self._calculate_future_returns(original_df)

        for years in time_horizons:
            print(f"\n🔍 Analyzing {years}-year time horizon...")

            # Filter data for this time horizon
            filtered_df = self._filter_last_n_years(original_df, years)

            if len(filtered_df) < 50:  # Need minimum data points
                print(f"   ⚠️  Insufficient data for {years}-year analysis ({len(filtered_df)} records)")
                continue

            # Temporarily set filtered data
            self.df = filtered_df

            # Setup output directory for this time horizon
            self.setup_output_directory(time_horizon_years=years)

            try:
                # Run complete analysis for this time horizon
                print(f"   📈 Running analysis for {years}-year data ({len(filtered_df)} records)...")

                # Identify S/R levels
                self.identify_all_levels()
                self.save_sr_results()

                # Generate interactive charts
                self.generate_interactive_charts()

                # Generate signals
                signal_summaries = self.generate_all_signals()

                # Create summary
                self.create_analysis_summary(signal_summaries)

                print(f"   ✅ {years}-year analysis complete")

            except Exception as e:
                print(f"   ❌ Error in {years}-year analysis: {str(e)}")
                continue

        # Restore original dataframe
        self.df = original_df

        print(f"\n✅ Multi-time horizon analysis complete for {len(time_horizons)} periods")

    def run_single_time_horizon_analysis(self, csv_filename, years=5):
        """Run analysis for a single time horizon"""
        try:
            print(f"🚀 Starting {years}-year analysis for {csv_filename}")
            print("=" * 60)

            # Step 1: Load original unfiltered data
            csv_path = self.data_dir / csv_filename
            if not csv_path.exists():
                raise FileNotFoundError(f"CSV file not found: {csv_path}")

            self.csv_name = csv_filename.replace('.csv', '')

            # Load unfiltered data
            original_df = pd.read_csv(csv_path, parse_dates=["date"], index_col="date")
            original_df = original_df.dropna()

            # Extract company name
            self.company_name = None
            if 'conm' in original_df.columns:
                company_names = original_df['conm'].dropna().value_counts()
                if len(company_names) > 0:
                    self.company_name = company_names.index[0]
                    self.company_name_clean = self._clean_company_name(self.company_name)

            # Calculate future returns on full dataset first
            original_df = self._calculate_future_returns(original_df)

            # Filter to specified time horizon
            self.df = self._filter_last_n_years(original_df, years)
            print(f"📅 Filtered to last {years} years: {len(original_df)} → {len(self.df)} records")
            print(f"   Date range: {self.df.index.min().strftime('%Y-%m-%d')} to {self.df.index.max().strftime('%Y-%m-%d')}")

            company_info = f" ({self.company_name})" if self.company_name else ""
            print(f"✅ Loaded data for {self.csv_name}{company_info}: {len(self.df)} records")

            # Step 2: Setup output directory with time horizon
            self.setup_output_directory(time_horizon_years=years)

            # Step 3: Identify support/resistance levels
            print("🔍 Identifying support and resistance levels...")
            self.identify_all_levels()
            self.save_sr_results()

            # Step 4: Generate interactive charts
            self.generate_interactive_charts()

            # Step 5: Generate trading signals
            signal_summaries = self.generate_all_signals()

            # Step 6: Create analysis summary
            self.create_analysis_summary(signal_summaries)

            print(f"\n✅ Complete {years}-year analysis finished for {csv_filename}")
            print(f"📁 Results saved in: {self.current_output_dir}")

            return True

        except Exception as e:
            print(f"\n❌ Analysis failed for {csv_filename}: {str(e)}")
            return False

    # ==================== SUPPORT & RESISTANCE IDENTIFICATION ====================
    
    def get_swing_points(self, order=3):
        """Identify swing highs and lows"""
        highs = self.df['High'].values
        lows = self.df['Low'].values
        
        swing_highs_idx = argrelextrema(highs, np.greater_equal, order=order)[0]
        swing_lows_idx = argrelextrema(lows, np.less_equal, order=order)[0]

        swing_highs = self.df.iloc[swing_highs_idx][['High']]
        swing_lows = self.df.iloc[swing_lows_idx][['Low']]

        return swing_highs, swing_lows
    
    def compute_vwpp(self):
        """Compute Volume Weighted Price Position"""
        vwpp = ((2 * self.df['Close'] - self.df['Low'] - self.df['High']) / 
                (self.df['High'] - self.df['Low']).replace(0, np.nan)) * self.df['Volume']
        return vwpp
    
    def get_swing_points_enhanced(self, order=3, vwpp_threshold=0.0):
        """Enhanced swing points with VWPP filtering"""
        df_copy = self.df.copy()
        df_copy['vwpp'] = self.compute_vwpp()

        highs = df_copy['High'].values
        lows = df_copy['Low'].values

        swing_highs_idx = argrelextrema(highs, np.greater_equal, order=order)[0]
        swing_lows_idx = argrelextrema(lows, np.less_equal, order=order)[0]

        swing_highs = df_copy.iloc[swing_highs_idx][['High', 'vwpp']].copy()
        swing_highs = swing_highs[swing_highs['vwpp'] >= vwpp_threshold]
        swing_highs['type'] = 'resistance'
        swing_highs.rename(columns={'High': 'Price'}, inplace=True)

        swing_lows = df_copy.iloc[swing_lows_idx][['Low', 'vwpp']].copy()
        swing_lows = swing_lows[swing_lows['vwpp'] <= -vwpp_threshold]
        swing_lows['type'] = 'support'
        swing_lows.rename(columns={'Low': 'Price'}, inplace=True)

        swings = pd.concat([swing_highs, swing_lows]).sort_index()
        return swings
    
    def get_pivot_points(self):
        """Calculate pivot points and support/resistance levels"""
        df_copy = self.df.copy()
        pivot = (df_copy['High'] + df_copy['Low'] + df_copy['Close']) / 3
        r1 = (2 * pivot) - df_copy['Low']
        s1 = (2 * pivot) - df_copy['High']
        r2 = pivot + (df_copy['High'] - df_copy['Low'])
        s2 = pivot - (df_copy['High'] - df_copy['Low'])
        r3 = df_copy['High'] + 2 * (pivot - df_copy['Low'])
        s3 = df_copy['Low'] - 2 * (df_copy['High'] - pivot)

        return pd.DataFrame({
            'pivot': pivot,
            'resistance1': r1,
            'support1': s1,
            'resistance2': r2,
            'support2': s2,
            'resistance3': r3,
            'support3': s3
        }, index=df_copy.index)

    def get_volume_profile_levels(self, bins=50):
        """Calculate volume profile support and resistance"""
        price = ((self.df['High'] + self.df['Low'] + self.df['Close']) / 3).values
        hist, bin_edges = np.histogram(price, bins=bins, weights=self.df['Volume'])
        
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        top_indices = np.argsort(hist)[-3:]
        top_levels = bin_centers[top_indices]
        top_levels.sort()
        
        return top_levels[0], top_levels[-1]
    
    def get_kmeans_sr(self, k=3):
        """KMeans clustering for support/resistance"""
        prices = self.df[['Open', 'High', 'Low', 'Close']].mean(axis=1).values.reshape(-1, 1)
        kmeans = KMeans(n_clusters=k, n_init=10, random_state=42).fit(prices)
        centers = sorted(kmeans.cluster_centers_.flatten())
        return centers[0], centers[-1]
    
    def get_kde_sr(self, bw_method='scott', num_peaks=2, use_ohlc=True, return_all=True):
        """
        Estimate support and resistance using KDE on price distribution.

        Parameters:
            bw_method (str or float): KDE bandwidth method ('scott', 'silverman', or float)
            num_peaks (int): Number of peak density levels to extract
            use_ohlc (bool): Use average of OHLC instead of Close
            return_all (bool): Return list of all detected price levels instead of just support/resistance

        Returns:
            If return_all:
                Sorted list of high-density price levels
            Else:
                Tuple: (support, resistance)
        """

        if use_ohlc:
            prices = self.df[['Open', 'High', 'Low', 'Close']].mean(axis=1).values
        else:
            prices = self.df['Close'].values

        if len(prices) < num_peaks:
            raise ValueError("Not enough data points to extract requested number of KDE peaks.")

        kde = gaussian_kde(prices, bw_method=bw_method)

        price_range = np.linspace(prices.min(), prices.max(), 1000)
        density = kde(price_range)

        # Get indices of top peaks (sorted by actual density)
        peak_indices = np.argsort(density)[-num_peaks:]
        peak_levels = price_range[peak_indices]
        peak_densities = density[peak_indices]

        # Sort by price for support/resistance logic
        sorted_levels = sorted(peak_levels)

        if return_all:
            return sorted_levels
        else:
            return sorted_levels[0], sorted_levels[-1]
    
    def get_fibonacci_levels(self, extension=True, as_df=False):
        """
        Calculate Fibonacci retracement (and optional extension) levels.

        Parameters:
            extension (bool): Include extensions like 1.272, 1.618, 2.0
            as_df (bool): Return as DataFrame instead of dict

        Returns:
            dict or pd.DataFrame: Fibonacci levels
        """
        max_price = self.df['High'].max()
        min_price = self.df['Low'].min()
        diff = max_price - min_price

        # Detect direction
        is_uptrend = self.df['Close'].iloc[-1] >= self.df['Open'].iloc[0]
        high = max_price if is_uptrend else min_price
        low = min_price if is_uptrend else max_price
        diff = abs(high - low)

        # Basic retracement levels
        levels = {
            '0.0': high,
            '0.236': high - 0.236 * diff,
            '0.382': high - 0.382 * diff,
            '0.5': high - 0.5 * diff,
            '0.618': high - 0.618 * diff,
            '0.786': high - 0.786 * diff,
            '1.0': low,
        }

        # Optional extension levels
        if extension:
            levels.update({
                '1.272': high - 1.272 * diff,
                '1.618': high - 1.618 * diff,
                '2.0': high - 2.0 * diff,
            })

        if as_df:
            return pd.DataFrame(list(levels.items()), columns=['Level', 'Price']).sort_values(by='Price', ascending=False)

        return levels
    
    def identify_all_levels(self):
        """Run all support/resistance identification methods"""
        print("🔍 Identifying support and resistance levels...")
        
        # Basic swing points
        swing_highs, swing_lows = self.get_swing_points()
        
        # Enhanced swing points
        vwpp_threshold = self.df['Volume'].quantile(0.5)
        swings_enhanced = self.get_swing_points_enhanced(order=5, vwpp_threshold=vwpp_threshold)
        
        # Pivot points
        pivot_levels = self.get_pivot_points()

        # Volume profile
        volume_support, volume_resistance = self.get_volume_profile_levels()
        
        # KMeans clustering
        kmeans_support, kmeans_resistance = self.get_kmeans_sr()
        
        # KDE analysis
        kde_support, kde_resistance = self.get_kde_sr()
        
        # Fibonacci levels
        fib_levels = self.get_fibonacci_levels()
        
        # Store results
        self.results = {
            'swing_highs': swing_highs,
            'swing_lows': swing_lows,
            'swings_enhanced': swings_enhanced,
            'pivot_levels': pivot_levels,
            'volume_support': volume_support,
            'volume_resistance': volume_resistance,
            'kmeans_support': kmeans_support,
            'kmeans_resistance': kmeans_resistance,
            'kde_support': kde_support,
            'kde_resistance': kde_resistance,
            'fibonacci_levels': fib_levels
        }
        
        print(f"✅ Identified support and resistance levels using 6 methods")
        return self.results
    
    def save_sr_results(self):
        """Save support/resistance analysis results"""
        print("💾 Saving support/resistance results...")
        
        # Save swing points
        self.results['swing_highs'].to_csv(self.current_output_dir / "swing_highs.csv")
        self.results['swing_lows'].to_csv(self.current_output_dir / "swing_lows.csv")
        self.results['swings_enhanced'].to_csv(self.current_output_dir / "swings_enhanced.csv")
        
        # Save pivot points
        self.results['pivot_levels'].to_csv(self.current_output_dir / "pivot_levels.csv")

        # Save level pairs
        levels_summary = {
            'volume_profile': {
                'support': float(self.results['volume_support']),
                'resistance': float(self.results['volume_resistance'])
            },
            'kmeans': {
                'support': float(self.results['kmeans_support']),
                'resistance': float(self.results['kmeans_resistance'])
            },
            'kde': {
                'support': float(self.results['kde_support']),
                'resistance': float(self.results['kde_resistance'])
            }
        }
        
        with open(self.current_output_dir / "sr_levels_summary.json", "w") as f:
            json.dump(levels_summary, f, indent=4)
        
        # Save Fibonacci levels
        with open(self.current_output_dir / "fibonacci_levels.json", "w") as f:
            json.dump(self.results['fibonacci_levels'], f, indent=4)
        
        print(f"✅ Saved support/resistance results to {self.current_output_dir}")


    # ==================== CANDLESTICK PLOTTING ====================
    

    # ==================== INTERACTIVE PLOTLY CHARTS ====================

    def create_interactive_candlestick(self, sr_levels, title=None, filename=None):
        """Create interactive Plotly candlestick chart with hover data"""
        df_plot = self.df.copy()

        # Ensure datetime index
        if not pd.api.types.is_datetime64_any_dtype(df_plot.index):
            df_plot.index = pd.to_datetime(df_plot.index)

        # Create subplot with secondary y-axis for volume
        fig = sp.make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=(title or 'Candlestick Chart', 'Volume'),
            row_width=[0.7, 0.3]
        )

        # Add candlestick chart
        candlestick = go.Candlestick(
            x=df_plot.index,
            open=df_plot['Open'],
            high=df_plot['High'],
            low=df_plot['Low'],
            close=df_plot['Close'],
            name='Price',
            hoverinfo='x+text',
            text=[f'Open: ${o:.2f}<br>High: ${h:.2f}<br>Low: ${l:.2f}<br>Close: ${c:.2f}'
                  for o, h, l, c in zip(df_plot['Open'], df_plot['High'],
                                       df_plot['Low'], df_plot['Close'])]
        )
        fig.add_trace(candlestick, row=1, col=1)

        # Add volume bars
        colors = ['red' if close < open else 'green'
                 for close, open in zip(df_plot['Close'], df_plot['Open'])]

        volume_bar = go.Bar(
            x=df_plot.index,
            y=df_plot['Volume'],
            name='Volume',
            marker_color=colors,
            opacity=0.7,
            hoverinfo='x+y+text',
            text=[f'Volume: {v:,.0f}' for v in df_plot['Volume']]
        )
        fig.add_trace(volume_bar, row=2, col=1)

        # Add support and resistance lines
        if isinstance(sr_levels, dict):
            support_levels = sr_levels.get('support', [])
            resistance_levels = sr_levels.get('resistance', [])
        elif isinstance(sr_levels, tuple):
            support_levels = [sr_levels[0]]
            resistance_levels = [sr_levels[1]]
        elif isinstance(sr_levels, list):
            # Split levels based on price position
            median_price = df_plot['Close'].median()
            support_levels = [x for x in sr_levels if x < median_price]
            resistance_levels = [x for x in sr_levels if x >= median_price]
        else:
            support_levels = []
            resistance_levels = []

        # Add support lines
        for level in support_levels:
            fig.add_hline(
                y=level,
                line=dict(color='green', width=2, dash='dash'),
                annotation_text=f'Support: ${level:.2f}',
                annotation_position='bottom right',
                row=1, col=1
            )

        # Add resistance lines
        for level in resistance_levels:
            fig.add_hline(
                y=level,
                line=dict(color='red', width=2, dash='dash'),
                annotation_text=f'Resistance: ${level:.2f}',
                annotation_position='top right',
                row=1, col=1
            )

        # Update layout
        fig.update_layout(
            title=dict(
                text=title or 'Interactive Candlestick Chart',
                x=0.5,
                font=dict(size=16)
            ),
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True,
            hovermode='x unified',
            template='plotly_white'
        )

        # Update axes
        fig.update_xaxes(title_text='Date', row=2, col=1)
        fig.update_yaxes(title_text='Price ($)', row=1, col=1)
        fig.update_yaxes(title_text='Volume', row=2, col=1)

        # Save as HTML file
        if filename:
            fig.write_html(filename)

        return fig

    def generate_interactive_charts(self):
        """Generate interactive Plotly charts with hover data"""
        print("🖱️  Generating interactive charts...")

        charts_dir = self.current_output_dir / "interactive_charts"
        charts_dir.mkdir(exist_ok=True)

        # Prepare company name for titles
        company_part = f" ({self.company_name})" if self.company_name else ""

        # Interactive Chart 1: Volume Profile levels
        volume_levels = {
            'support': [self.results['volume_support']],
            'resistance': [self.results['volume_resistance']]
        }
        self.create_interactive_candlestick(
            volume_levels,
            title=f'{self.csv_name}{company_part} - Volume Profile S/R (Interactive)',
            filename=charts_dir / "volume_profile_sr_interactive.html"
        )

        # Interactive Chart 2: KMeans levels
        kmeans_levels = {
            'support': [self.results['kmeans_support']],
            'resistance': [self.results['kmeans_resistance']]
        }
        self.create_interactive_candlestick(
            kmeans_levels,
            title=f'{self.csv_name}{company_part} - KMeans S/R (Interactive)',
            filename=charts_dir / "kmeans_sr_interactive.html"
        )

        # Interactive Chart 3: KDE levels
        kde_levels = {
            'support': [self.results['kde_support']],
            'resistance': [self.results['kde_resistance']]
        }
        self.create_interactive_candlestick(
            kde_levels,
            title=f'{self.csv_name}{company_part} - KDE S/R (Interactive)',
            filename=charts_dir / "kde_sr_interactive.html"
        )

        # Interactive Chart 4: Fibonacci levels
        fib_values = list(self.results['fibonacci_levels'].values())
        self.create_interactive_candlestick(
            fib_values,
            title=f'{self.csv_name}{company_part} - Fibonacci Levels (Interactive)',
            filename=charts_dir / "fibonacci_levels_interactive.html"
        )

        # Interactive Chart 5: All levels combined
        all_levels = {
            'support': [
                self.results['volume_support'],
                self.results['kmeans_support'],
                self.results['kde_support']
            ],
            'resistance': [
                self.results['volume_resistance'],
                self.results['kmeans_resistance'],
                self.results['kde_resistance']
            ]
        }
        self.create_interactive_candlestick(
            all_levels,
            title=f'{self.csv_name}{company_part} - All S/R Methods Combined (Interactive)',
            filename=charts_dir / "all_methods_combined_interactive.html"
        )

        print(f"✅ Generated 5 interactive charts in {charts_dir}")


    # ==================== SIGNAL GENERATION ====================

    def generate_sr_features(self, support_level, resistance_level):
        """Generate trading signals based on support/resistance levels"""
        df_signals = self.df.copy()

        # Add S/R levels to dataframe
        df_signals['support_level'] = support_level
        df_signals['resistance_level'] = resistance_level

        # --- Band and distance features ---
        df_signals['sr_bandwidth'] = df_signals['resistance_level'] - df_signals['support_level']
        df_signals['dist_to_support'] = df_signals['Close'] - df_signals['support_level']
        df_signals['dist_to_resistance'] = df_signals['resistance_level'] - df_signals['Close']
        df_signals['pct_to_support'] = (df_signals['Close'] - df_signals['support_level']) / df_signals['Close']
        df_signals['pct_to_resistance'] = (df_signals['resistance_level'] - df_signals['Close']) / df_signals['Close']
        df_signals['pct_through_band'] = (df_signals['Close'] - df_signals['support_level']) / df_signals['sr_bandwidth']

        # --- Zone classification ---
        df_signals['sr_zone'] = np.select(
            [
                df_signals['Close'] < df_signals['support_level'],
                (df_signals['Close'] >= df_signals['support_level']) & (df_signals['Close'] <= df_signals['resistance_level']),
                df_signals['Close'] > df_signals['resistance_level']
            ],
            [-1, 0, 1]
        )

        # --- Cross detection ---
        df_signals['crossed_support'] = ((df_signals['Close'] < df_signals['support_level']) &
                                       (df_signals['Close'].shift(1) >= df_signals['support_level'])).astype(int)
        df_signals['crossed_resistance'] = ((df_signals['Close'] > df_signals['resistance_level']) &
                                          (df_signals['Close'].shift(1) <= df_signals['resistance_level'])).astype(int)

        # --- Bounce detection ---
        df_signals['bounce_from_support'] = ((df_signals['Close'] > df_signals['support_level']) &
                                           (df_signals['Close'].shift(1) <= df_signals['support_level'])).astype(int)
        df_signals['bounce_from_resistance'] = ((df_signals['Close'] < df_signals['resistance_level']) &
                                               (df_signals['Close'].shift(1) >= df_signals['resistance_level'])).astype(int)

        # --- Breakout / breakdown ---
        df_signals['breakout'] = ((df_signals['Close'] > df_signals['resistance_level']) &
                                (df_signals['Close'].shift(1) <= df_signals['resistance_level'])).astype(int)
        df_signals['breakdown'] = ((df_signals['Close'] < df_signals['support_level']) &
                                 (df_signals['Close'].shift(1) >= df_signals['support_level'])).astype(int)

        # --- Volume confirmation ---
        df_signals['volume_spike'] = df_signals['Volume'] > df_signals['Volume'].rolling(10).mean() * 1.5
        df_signals['confirmed_breakout'] = df_signals['breakout'] & df_signals['volume_spike']
        df_signals['confirmed_breakdown'] = df_signals['breakdown'] & df_signals['volume_spike']

        # --- Near support/resistance ---
        df_signals['near_support'] = (abs(df_signals['Close'] - df_signals['support_level']) / df_signals['Close'] < 0.01).astype(int)
        df_signals['near_resistance'] = (abs(df_signals['Close'] - df_signals['resistance_level']) / df_signals['Close'] < 0.01).astype(int)

        # --- Trading signals ---
        df_signals['buy_signal'] = (df_signals['bounce_from_support'] |
                                  (df_signals['near_support'] & (df_signals['Close'] > df_signals['Open']))).astype(int)
        df_signals['sell_signal'] = (df_signals['bounce_from_resistance'] |
                                   (df_signals['near_resistance'] & (df_signals['Close'] < df_signals['Open']))).astype(int)
        df_signals['breakout_signal'] = df_signals['confirmed_breakout']
        df_signals['breakdown_signal'] = df_signals['confirmed_breakdown']

        return df_signals

    def generate_all_signals(self):
        """Generate signals for all S/R methods"""
        print("🎯 Generating trading signals...")

        signals_dir = self.current_output_dir / "signals"
        signals_dir.mkdir(exist_ok=True)

        # Generate signals for each method
        methods = {
            'volume_profile': (self.results['volume_support'], self.results['volume_resistance']),
            'kmeans': (self.results['kmeans_support'], self.results['kmeans_resistance']),
            'kde': (self.results['kde_support'], self.results['kde_resistance'])
        }

        signal_summaries = {}

        for method_name, (support, resistance) in methods.items():
            df_signals = self.generate_sr_features(support, resistance)

            # Save detailed signals
            df_signals.to_csv(signals_dir / f"{method_name}_signals.csv")

            # Create signal summary
            signal_summary = {
                'total_buy_signals': int(df_signals['buy_signal'].sum()),
                'total_sell_signals': int(df_signals['sell_signal'].sum()),
                'total_breakouts': int(df_signals['breakout_signal'].sum()),
                'total_breakdowns': int(df_signals['breakdown_signal'].sum()),
                'support_level': float(support),
                'resistance_level': float(resistance),
                'latest_signals': {
                    'buy': bool(df_signals['buy_signal'].iloc[-1]) if len(df_signals) > 0 else False,
                    'sell': bool(df_signals['sell_signal'].iloc[-1]) if len(df_signals) > 0 else False,
                    'breakout': bool(df_signals['breakout_signal'].iloc[-1]) if len(df_signals) > 0 else False,
                    'breakdown': bool(df_signals['breakdown_signal'].iloc[-1]) if len(df_signals) > 0 else False
                }
            }
            signal_summaries[method_name] = signal_summary

        # Save signal summaries
        with open(signals_dir / "signal_summaries.json", "w") as f:
            json.dump(signal_summaries, f, indent=4)

        print(f"✅ Generated signals for 3 methods in {signals_dir}")
        return signal_summaries

    # ==================== MAIN EXECUTION ====================

    def run_complete_analysis(self, csv_filename):
        """Run the complete support/resistance analysis pipeline"""
        print(f"\n🚀 Starting complete analysis for {csv_filename}")
        print("=" * 60)

        try:
            # Step 1: Load data
            self.load_data(csv_filename)

            # Step 2: Setup output directory
            self.setup_output_directory()

            # Step 3: Identify support/resistance levels
            print("🔍 Identifying support and resistance levels...")
            self.identify_all_levels()
            self.save_sr_results()

            # Step 4: Generate interactive charts
            self.generate_interactive_charts()

            # Step 5: Generate trading signals
            signal_summaries = self.generate_all_signals()

            # Step 6: Create analysis summary
            self.create_analysis_summary(signal_summaries)

            print(f"\n✅ Complete analysis finished for {csv_filename}")
            print(f"📁 Results saved in: {self.current_output_dir}")

            return True

        except Exception as e:
            print(f"\n❌ Analysis failed for {csv_filename}: {str(e)}")
            return False

    def create_analysis_summary(self, signal_summaries):
        """Create a comprehensive analysis summary"""
        summary = {
            'analysis_info': {
                'csv_file': self.csv_name + '.csv',
                'company_name': self.company_name,
                'company_name_clean': self.company_name_clean,
                'analysis_date': datetime.now().isoformat(),
                'data_points': len(self.df),
                'data_filter': 'Last 5 years only',
                'date_range': {
                    'start': str(self.df.index.min()),
                    'end': str(self.df.index.max()),
                    'years_covered': round((self.df.index.max() - self.df.index.min()).days / 365.25, 1)
                },
                'price_range': {
                    'min': float(self.df['Low'].min()),
                    'max': float(self.df['High'].max()),
                    'current': float(self.df['Close'].iloc[-1])
                }
            },
            'support_resistance_levels': {
                'volume_profile': {
                    'support': float(self.results['volume_support']),
                    'resistance': float(self.results['volume_resistance'])
                },
                'kmeans': {
                    'support': float(self.results['kmeans_support']),
                    'resistance': float(self.results['kmeans_resistance'])
                },
                'kde': {
                    'support': float(self.results['kde_support']),
                    'resistance': float(self.results['kde_resistance'])
                }
            },
            'fibonacci_levels': self.results['fibonacci_levels'],
            'future_returns_statistics': self._calculate_future_returns_stats(),
            'signal_summaries': signal_summaries,
            'files_generated': {
                'interactive_charts': [
                    'volume_profile_sr_interactive.html', 'kmeans_sr_interactive.html',
                    'kde_sr_interactive.html', 'fibonacci_levels_interactive.html',
                    'all_methods_combined_interactive.html'
                ],
                'data_files': ['swing_highs.csv', 'swing_lows.csv', 'swings_enhanced.csv'],
                'signal_files': ['volume_profile_signals.csv', 'kmeans_signals.csv', 'kde_signals.csv'],
                'summary_files': ['sr_levels_summary.json', 'fibonacci_levels.json', 'signal_summaries.json']
            }
        }

        with open(self.current_output_dir / "analysis_summary.json", "w") as f:
            json.dump(summary, f, indent=4)

        print(f"📋 Created comprehensive analysis summary")

    def _calculate_future_returns_stats(self):
        """Calculate statistics for future returns"""
        stats = {}

        # Monthly returns statistics
        monthly_returns = self.df['actual_monthly_return'].dropna()
        if len(monthly_returns) > 0:
            stats['monthly_returns'] = {
                'count': len(monthly_returns),
                'mean': float(monthly_returns.mean()),
                'median': float(monthly_returns.median()),
                'std': float(monthly_returns.std()),
                'min': float(monthly_returns.min()),
                'max': float(monthly_returns.max()),
                'positive_count': int((monthly_returns > 0).sum()),
                'negative_count': int((monthly_returns < 0).sum()),
                'win_rate': float((monthly_returns > 0).mean() * 100)
            }
        else:
            stats['monthly_returns'] = {'count': 0, 'note': 'No monthly return data available'}

        # Quarterly returns statistics
        quarterly_returns = self.df['actual_quarterly_return'].dropna()
        if len(quarterly_returns) > 0:
            stats['quarterly_returns'] = {
                'count': len(quarterly_returns),
                'mean': float(quarterly_returns.mean()),
                'median': float(quarterly_returns.median()),
                'std': float(quarterly_returns.std()),
                'min': float(quarterly_returns.min()),
                'max': float(quarterly_returns.max()),
                'positive_count': int((quarterly_returns > 0).sum()),
                'negative_count': int((quarterly_returns < 0).sum()),
                'win_rate': float((quarterly_returns > 0).mean() * 100)
            }
        else:
            stats['quarterly_returns'] = {'count': 0, 'note': 'No quarterly return data available'}

        return stats

    @staticmethod
    def create_portfolio_performance_summary(output_base_dir="output"):
        """Create comprehensive performance summary across all companies and methods"""
        print("\n📊 Creating Portfolio Performance Summary...")
        print("=" * 60)

        output_dir = Path(output_base_dir)
        if not output_dir.exists():
            print("❌ No output directory found. Run some analyses first!")
            return

        # Collect data from all companies and time horizons
        portfolio_data = []

        for company_folder in output_dir.iterdir():
            if not company_folder.is_dir():
                continue

            company_name = company_folder.name
            print(f"📂 Processing: {company_name}")

            # Process each time horizon folder
            for time_folder in company_folder.iterdir():
                if not time_folder.is_dir() or 'year' not in time_folder.name:
                    continue

                time_horizon = time_folder.name

                # Load signal files for each method
                signals_dir = time_folder / "signals"
                if not signals_dir.exists():
                    continue

                # Process each signal method
                signal_files = {
                    'volume_profile': signals_dir / "volume_profile_signals.csv",
                    'kmeans': signals_dir / "kmeans_signals.csv",
                    'kde': signals_dir / "kde_signals.csv"
                }

                for method_name, signal_file in signal_files.items():
                    if not signal_file.exists():
                        continue

                    try:
                        # Load signal data
                        df = pd.read_csv(signal_file, parse_dates=['date'], index_col='date')

                        # Check if future returns columns exist
                        if 'actual_monthly_return' not in df.columns or 'actual_quarterly_return' not in df.columns:
                            continue

                        # Analyze buy signals
                        buy_signals = df[df['buy_signal'] == 1]
                        if len(buy_signals) > 0:
                            monthly_returns_buy = buy_signals['actual_monthly_return'].dropna()
                            quarterly_returns_buy = buy_signals['actual_quarterly_return'].dropna()

                            if len(monthly_returns_buy) > 0:
                                portfolio_data.append({
                                    'company': company_name,
                                    'time_horizon': time_horizon,
                                    'method': method_name,
                                    'signal_type': 'buy',
                                    'return_period': 'monthly',
                                    'total_signals': len(buy_signals),
                                    'signals_with_returns': len(monthly_returns_buy),
                                    'avg_return': monthly_returns_buy.mean(),
                                    'median_return': monthly_returns_buy.median(),
                                    'win_rate': (monthly_returns_buy > 0).mean() * 100,
                                    'best_return': monthly_returns_buy.max(),
                                    'worst_return': monthly_returns_buy.min(),
                                    'std_return': monthly_returns_buy.std()
                                })

                            if len(quarterly_returns_buy) > 0:
                                portfolio_data.append({
                                    'company': company_name,
                                    'time_horizon': time_horizon,
                                    'method': method_name,
                                    'signal_type': 'buy',
                                    'return_period': 'quarterly',
                                    'total_signals': len(buy_signals),
                                    'signals_with_returns': len(quarterly_returns_buy),
                                    'avg_return': quarterly_returns_buy.mean(),
                                    'median_return': quarterly_returns_buy.median(),
                                    'win_rate': (quarterly_returns_buy > 0).mean() * 100,
                                    'best_return': quarterly_returns_buy.max(),
                                    'worst_return': quarterly_returns_buy.min(),
                                    'std_return': quarterly_returns_buy.std()
                                })

                        # Analyze sell signals (success = negative returns)
                        sell_signals = df[df['sell_signal'] == 1]
                        if len(sell_signals) > 0:
                            monthly_returns_sell = sell_signals['actual_monthly_return'].dropna()
                            quarterly_returns_sell = sell_signals['actual_quarterly_return'].dropna()

                            if len(monthly_returns_sell) > 0:
                                portfolio_data.append({
                                    'company': company_name,
                                    'time_horizon': time_horizon,
                                    'method': method_name,
                                    'signal_type': 'sell',
                                    'return_period': 'monthly',
                                    'total_signals': len(sell_signals),
                                    'signals_with_returns': len(monthly_returns_sell),
                                    'avg_return': monthly_returns_sell.mean(),
                                    'median_return': monthly_returns_sell.median(),
                                    'success_rate': (monthly_returns_sell < 0).mean() * 100,  # Success = negative returns
                                    'best_return': monthly_returns_sell.min(),  # Best = most negative
                                    'worst_return': monthly_returns_sell.max(),  # Worst = most positive
                                    'std_return': monthly_returns_sell.std()
                                })

                            if len(quarterly_returns_sell) > 0:
                                portfolio_data.append({
                                    'company': company_name,
                                    'time_horizon': time_horizon,
                                    'method': method_name,
                                    'signal_type': 'sell',
                                    'return_period': 'quarterly',
                                    'total_signals': len(sell_signals),
                                    'signals_with_returns': len(quarterly_returns_sell),
                                    'avg_return': quarterly_returns_sell.mean(),
                                    'median_return': quarterly_returns_sell.median(),
                                    'success_rate': (quarterly_returns_sell < 0).mean() * 100,  # Success = negative returns
                                    'best_return': quarterly_returns_sell.min(),  # Best = most negative
                                    'worst_return': quarterly_returns_sell.max(),  # Worst = most positive
                                    'std_return': quarterly_returns_sell.std()
                                })

                    except Exception as e:
                        print(f"   ⚠️  Error processing {method_name} for {company_name}/{time_horizon}: {e}")
                        continue

        if not portfolio_data:
            print("❌ No signal data found with future returns. Run analyses with future returns first!")
            return

        # Convert to DataFrame for analysis
        portfolio_df = pd.DataFrame(portfolio_data)

        # Create summary report
        summary_file = output_dir / "portfolio_performance_summary.json"
        detailed_file = output_dir / "portfolio_performance_detailed.csv"

        # Save detailed data
        portfolio_df.to_csv(detailed_file, index=False)
        print(f"💾 Saved detailed data: {detailed_file}")

        # Generate summary statistics
        summary = SupportResistanceAnalyzer._generate_performance_summary(portfolio_df)

        # Save summary
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        print(f"📋 Saved summary report: {summary_file}")

        # Print key insights
        SupportResistanceAnalyzer._print_performance_insights(summary)

        return summary, portfolio_df

    @staticmethod
    def _generate_performance_summary(portfolio_df):
        """Generate comprehensive performance summary from portfolio data"""
        summary = {
            'analysis_metadata': {
                'total_companies': portfolio_df['company'].nunique(),
                'total_time_horizons': portfolio_df['time_horizon'].nunique(),
                'methods_analyzed': list(portfolio_df['method'].unique()),
                'time_horizons_analyzed': list(portfolio_df['time_horizon'].unique()),
                'total_signal_records': len(portfolio_df),
                'analysis_date': pd.Timestamp.now().isoformat()
            }
        }

        # Overall method performance
        method_performance = {}
        for method in portfolio_df['method'].unique():
            method_data = portfolio_df[portfolio_df['method'] == method]

            # Buy signals performance
            buy_data = method_data[method_data['signal_type'] == 'buy']
            monthly_buy = buy_data[buy_data['return_period'] == 'monthly']
            quarterly_buy = buy_data[buy_data['return_period'] == 'quarterly']

            # Sell signals performance
            sell_data = method_data[method_data['signal_type'] == 'sell']
            monthly_sell = sell_data[sell_data['return_period'] == 'monthly']
            quarterly_sell = sell_data[sell_data['return_period'] == 'quarterly']

            method_performance[method] = {
                'buy_signals': {
                    'monthly': {
                        'companies': len(monthly_buy),
                        'avg_win_rate': monthly_buy['win_rate'].mean() if len(monthly_buy) > 0 else 0,
                        'avg_return': monthly_buy['avg_return'].mean() if len(monthly_buy) > 0 else 0,
                        'median_return': monthly_buy['median_return'].mean() if len(monthly_buy) > 0 else 0,
                        'total_signals': monthly_buy['total_signals'].sum() if len(monthly_buy) > 0 else 0,
                        'best_avg_return': monthly_buy['avg_return'].max() if len(monthly_buy) > 0 else 0,
                        'worst_avg_return': monthly_buy['avg_return'].min() if len(monthly_buy) > 0 else 0
                    },
                    'quarterly': {
                        'companies': len(quarterly_buy),
                        'avg_win_rate': quarterly_buy['win_rate'].mean() if len(quarterly_buy) > 0 else 0,
                        'avg_return': quarterly_buy['avg_return'].mean() if len(quarterly_buy) > 0 else 0,
                        'median_return': quarterly_buy['median_return'].mean() if len(quarterly_buy) > 0 else 0,
                        'total_signals': quarterly_buy['total_signals'].sum() if len(quarterly_buy) > 0 else 0,
                        'best_avg_return': quarterly_buy['avg_return'].max() if len(quarterly_buy) > 0 else 0,
                        'worst_avg_return': quarterly_buy['avg_return'].min() if len(quarterly_buy) > 0 else 0
                    }
                },
                'sell_signals': {
                    'monthly': {
                        'companies': len(monthly_sell),
                        'avg_success_rate': monthly_sell['success_rate'].mean() if len(monthly_sell) > 0 else 0,
                        'avg_return': monthly_sell['avg_return'].mean() if len(monthly_sell) > 0 else 0,
                        'median_return': monthly_sell['median_return'].mean() if len(monthly_sell) > 0 else 0,
                        'total_signals': monthly_sell['total_signals'].sum() if len(monthly_sell) > 0 else 0,
                        'best_avg_return': monthly_sell['avg_return'].min() if len(monthly_sell) > 0 else 0,  # Most negative
                        'worst_avg_return': monthly_sell['avg_return'].max() if len(monthly_sell) > 0 else 0   # Most positive
                    },
                    'quarterly': {
                        'companies': len(quarterly_sell),
                        'avg_success_rate': quarterly_sell['success_rate'].mean() if len(quarterly_sell) > 0 else 0,
                        'avg_return': quarterly_sell['avg_return'].mean() if len(quarterly_sell) > 0 else 0,
                        'median_return': quarterly_sell['median_return'].mean() if len(quarterly_sell) > 0 else 0,
                        'total_signals': quarterly_sell['total_signals'].sum() if len(quarterly_sell) > 0 else 0,
                        'best_avg_return': quarterly_sell['avg_return'].min() if len(quarterly_sell) > 0 else 0,  # Most negative
                        'worst_avg_return': quarterly_sell['avg_return'].max() if len(quarterly_sell) > 0 else 0   # Most positive
                    }
                }
            }

        summary['method_performance'] = method_performance

        # Time horizon analysis
        horizon_performance = {}
        for horizon in portfolio_df['time_horizon'].unique():
            horizon_data = portfolio_df[portfolio_df['time_horizon'] == horizon]

            horizon_performance[horizon] = {
                'companies_analyzed': horizon_data['company'].nunique(),
                'methods_analyzed': list(horizon_data['method'].unique()),
                'total_buy_signals': horizon_data[horizon_data['signal_type'] == 'buy']['total_signals'].sum(),
                'total_sell_signals': horizon_data[horizon_data['signal_type'] == 'sell']['total_signals'].sum(),
                'avg_monthly_buy_win_rate': horizon_data[
                    (horizon_data['signal_type'] == 'buy') &
                    (horizon_data['return_period'] == 'monthly')
                ]['win_rate'].mean(),
                'avg_quarterly_buy_win_rate': horizon_data[
                    (horizon_data['signal_type'] == 'buy') &
                    (horizon_data['return_period'] == 'quarterly')
                ]['win_rate'].mean(),
                'avg_monthly_sell_success_rate': horizon_data[
                    (horizon_data['signal_type'] == 'sell') &
                    (horizon_data['return_period'] == 'monthly')
                ]['success_rate'].mean(),
                'avg_quarterly_sell_success_rate': horizon_data[
                    (horizon_data['signal_type'] == 'sell') &
                    (horizon_data['return_period'] == 'quarterly')
                ]['success_rate'].mean()
            }

        summary['time_horizon_performance'] = horizon_performance

        # Best performing combinations
        buy_monthly = portfolio_df[
            (portfolio_df['signal_type'] == 'buy') &
            (portfolio_df['return_period'] == 'monthly')
        ].copy()

        if len(buy_monthly) > 0:
            best_buy_monthly = buy_monthly.loc[buy_monthly['win_rate'].idxmax()]
            summary['best_performers'] = {
                'best_monthly_buy_method': {
                    'method': best_buy_monthly['method'],
                    'company': best_buy_monthly['company'],
                    'time_horizon': best_buy_monthly['time_horizon'],
                    'win_rate': best_buy_monthly['win_rate'],
                    'avg_return': best_buy_monthly['avg_return'],
                    'total_signals': best_buy_monthly['total_signals']
                }
            }

        return summary

    @staticmethod
    def _print_performance_insights(summary):
        """Print key performance insights to console"""
        print("\n🎯 Portfolio Performance Insights")
        print("=" * 60)

        metadata = summary['analysis_metadata']
        print(f"📊 Analysis Overview:")
        print(f"   Companies: {metadata['total_companies']}")
        print(f"   Time Horizons: {metadata['time_horizons_analyzed']}")
        print(f"   Methods: {metadata['methods_analyzed']}")
        print(f"   Total Records: {metadata['total_signal_records']}")

        # Method comparison
        print(f"\n🏆 Method Performance Comparison (Buy Signals):")
        print("-" * 80)
        print(f"{'Method':<15} {'Monthly Win%':<12} {'Monthly Avg%':<12} {'Quarterly Win%':<14} {'Quarterly Avg%':<14}")
        print("-" * 80)

        method_perf = summary['method_performance']
        for method, perf in method_perf.items():
            monthly_win = perf['buy_signals']['monthly']['avg_win_rate']
            monthly_ret = perf['buy_signals']['monthly']['avg_return']
            quarterly_win = perf['buy_signals']['quarterly']['avg_win_rate']
            quarterly_ret = perf['buy_signals']['quarterly']['avg_return']

            print(f"{method:<15} {monthly_win:<11.1f}% {monthly_ret:<11.2f}% {quarterly_win:<13.1f}% {quarterly_ret:<13.2f}%")

        print("-" * 80)

        # Sell signals performance
        print(f"\n🔴 Sell Signal Performance (Success = Negative Returns):")
        print("-" * 80)
        print(f"{'Method':<15} {'Monthly Success%':<15} {'Monthly Avg%':<12} {'Quarterly Success%':<17} {'Quarterly Avg%':<14}")
        print("-" * 80)

        for method, perf in method_perf.items():
            monthly_success = perf['sell_signals']['monthly']['avg_success_rate']
            monthly_ret = perf['sell_signals']['monthly']['avg_return']
            quarterly_success = perf['sell_signals']['quarterly']['avg_success_rate']
            quarterly_ret = perf['sell_signals']['quarterly']['avg_return']

            print(f"{method:<15} {monthly_success:<14.1f}% {monthly_ret:<11.2f}% {quarterly_success:<16.1f}% {quarterly_ret:<13.2f}%")

        print("-" * 80)

        # Time horizon comparison
        print(f"\n⏱️  Time Horizon Analysis:")
        horizon_perf = summary['time_horizon_performance']
        for horizon, perf in horizon_perf.items():
            print(f"\n   📅 {horizon}:")
            print(f"      Companies: {perf['companies_analyzed']}")
            print(f"      Buy Signals: {perf['total_buy_signals']:,}")
            print(f"      Sell Signals: {perf['total_sell_signals']:,}")
            print(f"      Monthly Buy Win Rate: {perf['avg_monthly_buy_win_rate']:.1f}%")
            print(f"      Quarterly Buy Win Rate: {perf['avg_quarterly_buy_win_rate']:.1f}%")
            print(f"      Monthly Sell Success Rate: {perf['avg_monthly_sell_success_rate']:.1f}%")
            print(f"      Quarterly Sell Success Rate: {perf['avg_quarterly_sell_success_rate']:.1f}%")

        # Best performers
        if 'best_performers' in summary:
            best = summary['best_performers']['best_monthly_buy_method']
            print(f"\n🥇 Best Monthly Buy Performance:")
            print(f"   Method: {best['method']}")
            print(f"   Company: {best['company']}")
            print(f"   Time Horizon: {best['time_horizon']}")
            print(f"   Win Rate: {best['win_rate']:.1f}%")
            print(f"   Avg Return: {best['avg_return']:.2f}%")
            print(f"   Total Signals: {best['total_signals']}")

        # Key insights
        print(f"\n💡 Key Insights:")

        # Find best method overall
        best_method = None
        best_score = 0
        for method, perf in method_perf.items():
            # Combine monthly and quarterly buy win rates
            score = (perf['buy_signals']['monthly']['avg_win_rate'] +
                    perf['buy_signals']['quarterly']['avg_win_rate']) / 2
            if score > best_score:
                best_score = score
                best_method = method

        if best_method:
            print(f"   🏆 Best Overall Method: {best_method} ({best_score:.1f}% avg win rate)")

        # Find most consistent method (lowest variance in win rates)
        method_consistency = {}
        for method, perf in method_perf.items():
            monthly_win = perf['buy_signals']['monthly']['avg_win_rate']
            quarterly_win = perf['buy_signals']['quarterly']['avg_win_rate']
            if monthly_win > 0 and quarterly_win > 0:
                variance = abs(monthly_win - quarterly_win)
                method_consistency[method] = variance

        if method_consistency:
            most_consistent = min(method_consistency.items(), key=lambda x: x[1])
            print(f"   📊 Most Consistent Method: {most_consistent[0]} ({most_consistent[1]:.1f}% variance)")

        print(f"\n📋 Detailed data saved to: portfolio_performance_detailed.csv")
        print(f"📋 Summary report saved to: portfolio_performance_summary.json")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Support & Resistance Analysis Master Script')
    parser.add_argument('csv_file', nargs='?', help='CSV filename to analyze (e.g., IE000S9YS762.csv)')
    parser.add_argument('--all', action='store_true', help='Process all CSV files in mega_cap folder')
    parser.add_argument('--data-dir', default='mega_cap', help='Directory containing CSV files')
    parser.add_argument('--output-dir', default='output', help='Base output directory')
    parser.add_argument('--years', type=int, help='Number of years to analyze (e.g., 1, 2, 3, 4, 5)')
    parser.add_argument('--multi-horizon', action='store_true', help='Analyze multiple time horizons (1,2,3,4,5 years)')
    parser.add_argument('--horizons', nargs='+', type=int, default=[1,2,3,4,5],
                       help='Specific time horizons to analyze (default: 1 2 3 4 5)')

    args = parser.parse_args()

    # Initialize analyzer
    analyzer = SupportResistanceAnalyzer(data_dir=args.data_dir, output_base_dir=args.output_dir)

    if args.all:
        # Process all CSV files
        csv_files = list(Path(args.data_dir).glob('*.csv'))
        if not csv_files:
            print(f"❌ No CSV files found in {args.data_dir}")
            return 1

        print(f"🔄 Processing {len(csv_files)} CSV files...")
        successful = 0
        failed = 0

        for csv_file in csv_files:
            print(f"\n{'='*60}")
            print(f"📊 Processing: {csv_file.name}")
            print(f"{'='*60}")

            try:
                if args.multi_horizon or (args.horizons and args.horizons != [1,2,3,4,5]):
                    # Multi-horizon analysis for all files
                    analyzer.load_data(csv_file.name)
                    analyzer.analyze_multiple_time_horizons(args.horizons)
                    successful += 1
                elif args.years:
                    # Specific time horizon for all files
                    if analyzer.run_single_time_horizon_analysis(csv_file.name, args.years):
                        successful += 1
                    else:
                        failed += 1
                else:
                    # Default analysis for all files
                    if analyzer.run_complete_analysis(csv_file.name):
                        successful += 1
                    else:
                        failed += 1
            except Exception as e:
                print(f"❌ Failed to process {csv_file.name}: {str(e)}")
                failed += 1

        print(f"\n📊 Batch processing complete:")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")

        if args.multi_horizon or (args.horizons and args.horizons != [1,2,3,4,5]):
            print(f"   📁 Multi-horizon analysis completed for {successful} companies")
            print(f"   ⏱️  Time horizons analyzed: {args.horizons}")
        elif args.years:
            print(f"   ⏱️  {args.years}-year analysis completed for {successful} companies")

    elif args.csv_file:
        # Process single file
        if not args.csv_file.endswith('.csv'):
            args.csv_file += '.csv'

        if args.multi_horizon or (args.horizons and args.horizons != [1,2,3,4,5]):
            # Analyze multiple time horizons
            analyzer.load_data(args.csv_file)
            analyzer.analyze_multiple_time_horizons(args.horizons)
            return 0
        elif args.years:
            # Analyze specific time horizon
            success = analyzer.run_single_time_horizon_analysis(args.csv_file, args.years)
            return 0 if success else 1
        else:
            # Default 5-year analysis
            success = analyzer.run_complete_analysis(args.csv_file)
            return 0 if success else 1

    else:
        # Show usage and available files
        print("📋 Support & Resistance Analysis Master Script")
        print("\nUsage:")
        print("  python master_support_resistance_analyzer.py <csv_filename>                    # Default 5-year analysis")
        print("  python master_support_resistance_analyzer.py <csv_filename> --years 3         # Specific time horizon")
        print("  python master_support_resistance_analyzer.py <csv_filename> --multi-horizon   # Multiple horizons (1,2,3,4,5 years)")
        print("  python master_support_resistance_analyzer.py <csv_filename> --horizons 1 3 5  # Custom horizons")
        print("  python master_support_resistance_analyzer.py --all                             # Process all files (default analysis)")
        print("  python master_support_resistance_analyzer.py --all --multi-horizon            # Multi-horizon for all files")
        print("  python master_support_resistance_analyzer.py --all --years 3                  # 3-year analysis for all files")
        print("  python master_support_resistance_analyzer.py --all --horizons 1 3 5           # Custom horizons for all files")

        # List available CSV files
        csv_files = list(Path(args.data_dir).glob('*.csv'))
        if csv_files:
            print(f"\n📁 Available CSV files in {args.data_dir}:")
            for csv_file in sorted(csv_files)[:10]:  # Show first 10
                print(f"   - {csv_file.name}")
            if len(csv_files) > 10:
                print(f"   ... and {len(csv_files) - 10} more files")
        else:
            print(f"\n❌ No CSV files found in {args.data_dir}")

        return 1


if __name__ == "__main__":
    exit(main())
