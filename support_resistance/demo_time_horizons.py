#!/usr/bin/env python3
"""
Demonstration script showing the multi-time horizon functionality
in the Master Support & Resistance Analyzer.
"""

import json
import pandas as pd
from pathlib import Path


def demo_time_horizon_structure():
    """Demonstrate the time horizon folder structure"""
    print("📁 Multi-Time Horizon Folder Structure Demo")
    print("=" * 50)
    
    # Find a company folder with time horizon subfolders
    output_dir = Path("output")
    company_folder = None
    
    for folder in output_dir.iterdir():
        if folder.is_dir():
            # Check if it has time horizon subfolders
            time_folders = [f for f in folder.iterdir() if f.is_dir() and ('year' in f.name)]
            if time_folders:
                company_folder = folder
                break
    
    if not company_folder:
        print("❌ No multi-time horizon analysis found. Run an analysis first!")
        print("   Example: python master_support_resistance_analyzer.py IE000S9YS762.csv --horizons 1 3 5")
        return
    
    print(f"📂 Company: {company_folder.name}")
    
    # Show folder structure
    print(f"\n📁 Folder Structure:")
    print(f"   📂 {company_folder.name}/")
    
    # List time horizon folders
    time_folders = sorted([f for f in company_folder.iterdir() if f.is_dir() and ('year' in f.name)])
    for time_folder in time_folders:
        print(f"      📂 {time_folder.name}/")
        
        # Show key files in each time horizon folder
        key_files = ['analysis_summary.json', 'sr_levels_summary.json']
        for key_file in key_files:
            file_path = time_folder / key_file
            if file_path.exists():
                size_kb = file_path.stat().st_size / 1024
                print(f"         📄 {key_file} ({size_kb:.1f} KB)")
        
        # Show subdirectories
        subdirs = [d for d in time_folder.iterdir() if d.is_dir()]
        for subdir in subdirs:
            file_count = len(list(subdir.iterdir()))
            print(f"         📁 {subdir.name}/ ({file_count} files)")


def demo_time_horizon_comparison():
    """Compare analysis results across different time horizons"""
    print("\n📊 Time Horizon Comparison Demo")
    print("=" * 50)
    
    # Find a company folder with time horizon analysis
    output_dir = Path("output")
    company_folder = None
    
    for folder in output_dir.iterdir():
        if folder.is_dir():
            time_folders = [f for f in folder.iterdir() if f.is_dir() and ('year' in f.name)]
            if time_folders:
                company_folder = folder
                break
    
    if not company_folder:
        return
    
    print(f"📈 Analyzing: {company_folder.name}")
    
    # Get time horizon folders
    time_folders = sorted([f for f in company_folder.iterdir() if f.is_dir() and ('year' in f.name)])
    
    print(f"\n📋 Time Horizon Comparison:")
    print("-" * 80)
    print(f"{'Horizon':<10} {'Records':<8} {'Date Range':<25} {'Support':<10} {'Resistance':<12}")
    print("-" * 80)
    
    for time_folder in time_folders:
        # Load analysis summary
        summary_file = time_folder / "analysis_summary.json"
        if summary_file.exists():
            with open(summary_file, 'r') as f:
                summary = json.load(f)
            
            analysis_info = summary['analysis_info']
            sr_levels = summary.get('support_resistance_levels', {})
            
            horizon = time_folder.name.replace('_', ' ').title()
            records = f"{analysis_info.get('data_points', 'N/A'):,}"
            date_start = analysis_info.get('date_range', {}).get('start', '')[:10]
            date_end = analysis_info.get('date_range', {}).get('end', '')[:10]
            date_range = f"{date_start} to {date_end[-5:]}"
            
            # Get volume profile levels (most reliable)
            vol_levels = sr_levels.get('volume_profile', {})
            support = f"${vol_levels.get('support', 0):.2f}"
            resistance = f"${vol_levels.get('resistance', 0):.2f}"
            
            print(f"{horizon:<10} {records:<8} {date_range:<25} {support:<10} {resistance:<12}")
    
    print("-" * 80)


def demo_usage_examples():
    """Show usage examples for multi-time horizon analysis"""
    print("\n🚀 Usage Examples")
    print("=" * 50)
    
    print("📋 Multi-Time Horizon Analysis Commands:")
    print()
    
    print("1️⃣  Single Time Horizon:")
    print("   python master_support_resistance_analyzer.py IE000S9YS762.csv --years 3")
    print("   → Creates: output/IE000S9YS762_Linde/3_years/")
    print()
    
    print("2️⃣  Multiple Time Horizons (Default: 1,2,3,4,5 years):")
    print("   python master_support_resistance_analyzer.py IE000S9YS762.csv --multi-horizon")
    print("   → Creates: output/IE000S9YS762_Linde/1_year/, 2_years/, 3_years/, 4_years/, 5_years/")
    print()
    
    print("3️⃣  Custom Time Horizons:")
    print("   python master_support_resistance_analyzer.py IE000S9YS762.csv --horizons 1 3 5")
    print("   → Creates: output/IE000S9YS762_Linde/1_year/, 3_years/, 5_years/")
    print()
    
    print("4️⃣  Default Analysis (5 years, no subfolders):")
    print("   python master_support_resistance_analyzer.py IE000S9YS762.csv")
    print("   → Creates: output/IE000S9YS762_Linde/ (single analysis)")
    print()
    
    print("💡 Benefits of Multi-Time Horizon Analysis:")
    print("   📊 Compare S/R levels across different time periods")
    print("   🎯 Identify consensus levels confirmed across multiple horizons")
    print("   📈 Understand short-term vs long-term market behavior")
    print("   💰 Make better trading decisions with multiple perspectives")
    print("   🔍 See how support/resistance evolves over time")


def main():
    """Main demonstration function"""
    print("🚀 Master Support & Resistance Analyzer")
    print("📊 Multi-Time Horizon Analysis Demo")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("mega_cap").exists():
        print("❌ Please run this script from the support_resistance directory")
        return 1
    
    try:
        # Demo 1: Folder structure
        demo_time_horizon_structure()
        
        # Demo 2: Time horizon comparison
        demo_time_horizon_comparison()
        
        # Demo 3: Usage examples
        demo_usage_examples()
        
        print("\n" + "=" * 60)
        print("🎉 Multi-Time Horizon Analysis Demo Complete!")
        
        print("\n💡 Key Features:")
        print("   📁 Organized subfolders for each time horizon")
        print("   📊 Complete analysis for each time period")
        print("   🎯 Easy comparison across different horizons")
        print("   💰 Trading insights from multiple perspectives")
        print("   🔍 Consensus building across time periods")
        
        print("\n🚀 Try it yourself:")
        print("   python master_support_resistance_analyzer.py YOUR_FILE.csv --horizons 1 3 5")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
