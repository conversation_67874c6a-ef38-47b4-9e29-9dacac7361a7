# 📈 Support & Resistance Analysis Master Script

A comprehensive Python script that orchestrates support and resistance analysis for stock market data using multiple methodologies.

## 🎯 **Overview**

This master script combines three analysis components:
1. **Support/Resistance Identification** - Multiple algorithms to detect key price levels
2. **Candlestick Chart Generation** - Visual representation with S/R levels
3. **Trading Signal Generation** - Automated signals based on S/R interactions

## 📁 **Project Structure**

```
support_resistance/
├── master_support_resistance_analyzer.py  # Main orchestration script
├── test_master_script.py                  # Test and demonstration script
├── identify_support_and_resistance.py     # S/R identification algorithms
├── plot_candlestick.py                   # Chart generation functions
├── generate_signals.py                   # Signal generation logic
├── mega_cap/                             # Input CSV files
│   ├── IE000S9YS762.csv                 # Sample stock data
│   └── ... (other CSV files)
└── output/                               # Generated results
    └── {csv_name}/                       # Results for each stock
        ├── analysis_summary.json
        ├── charts/
        ├── signals/
        └── ... (detailed results)
```

## 🚀 **Quick Start**

### **1. Analyze a Single Stock**
```bash
python master_support_resistance_analyzer.py IE000S9YS762.csv
```

### **2. Analyze All Stocks**
```bash
python master_support_resistance_analyzer.py --all
```

### **3. Test the System**
```bash
python test_master_script.py
```

## 📊 **Analysis Methods**

### **Support/Resistance Identification**
- **Volume Profile** - High-volume price areas
- **KMeans Clustering** - Price cluster analysis
- **KDE (Kernel Density Estimation)** - Statistical price distribution
- **Swing Points** - Local highs and lows
- **Fibonacci Retracements** - Technical analysis levels

### **Chart Generation**
- **Candlestick Charts** with volume
- **Support/Resistance Lines** overlaid
- **Multiple Timeframe Views**
- **High-Quality PNG Output**

### **Signal Generation**
- **Buy/Sell Signals** at S/R levels
- **Breakout/Breakdown Detection**
- **Volume Confirmation**
- **Zone Classification**

## 📋 **Input Data Format**

CSV files should contain these columns:
```
date,Open,High,Low,Close,Volume
2024-01-01,100.0,105.0,99.0,103.0,1000000
2024-01-02,103.0,107.0,102.0,106.0,1200000
...
```

## 📁 **Output Structure**

For each analyzed stock, the script creates a folder with the company name:

```
output/{csv_id}_{company_name}/
├── analysis_summary.json           # Complete analysis overview (includes company info)
├── sr_levels_summary.json         # S/R levels from all methods
├── fibonacci_levels.json          # Fibonacci retracement levels
├── swing_highs.csv                # Detected swing high points
├── swing_lows.csv                 # Detected swing low points
├── swings_enhanced.csv            # Enhanced swing points with VWPP
├── charts/                        # Candlestick charts (with company names in titles)
│   ├── volume_profile_sr.png      # Volume profile S/R chart
│   ├── kmeans_sr.png              # KMeans S/R chart
│   ├── kde_sr.png                 # KDE S/R chart
│   └── fibonacci_levels.png       # Fibonacci levels chart
└── signals/                       # Trading signals
    ├── volume_profile_signals.csv # Detailed signals (volume method)
    ├── kmeans_signals.csv         # Detailed signals (kmeans method)
    ├── kde_signals.csv            # Detailed signals (kde method)
    └── signal_summaries.json      # Signal summaries for all methods
```

### **🏢 Company Name Integration**

The script automatically extracts company names from the CSV files and includes them in:
- **Folder names**: `IE000S9YS762_Linde/` instead of `IE000S9YS762/`
- **Chart titles**: "IE000S9YS762 (Linde plc) - Volume Profile S/R"
- **Analysis summaries**: Company information preserved in JSON files

## 🎛️ **Command Line Options**

```bash
python master_support_resistance_analyzer.py [OPTIONS] [CSV_FILE]

Arguments:
  CSV_FILE                 CSV filename to analyze (e.g., IE000S9YS762.csv)

Options:
  --all                    Process all CSV files in data directory
  --data-dir DIR          Directory containing CSV files (default: mega_cap)
  --output-dir DIR        Base output directory (default: output)
  -h, --help              Show help message
```

## 📈 **Analysis Results**

### **Support/Resistance Levels**
```json
{
  "volume_profile": {
    "support": 405.25,
    "resistance": 412.80
  },
  "kmeans": {
    "support": 404.90,
    "resistance": 413.15
  },
  "kde": {
    "support": 405.10,
    "resistance": 412.95
  }
}
```

### **Trading Signals**
```json
{
  "volume_profile": {
    "total_buy_signals": 15,
    "total_sell_signals": 12,
    "total_breakouts": 3,
    "total_breakdowns": 2,
    "latest_signals": {
      "buy": false,
      "sell": true,
      "breakout": false,
      "breakdown": false
    }
  }
}
```

## 🔧 **Dependencies**

```bash
pip install pandas numpy scipy scikit-learn matplotlib mplfinance
```

## 🧪 **Testing**

Run the test script to verify everything works:

```bash
python test_master_script.py
```

The test script will:
- ✅ Test data loading
- ✅ Test S/R identification
- ✅ Test chart generation
- ✅ Test signal generation
- ✅ Verify output files

## 💡 **Usage Examples**

### **Example 1: Single Stock Analysis**
```bash
# Analyze Linde plc stock data
python master_support_resistance_analyzer.py IE000S9YS762.csv

# Results will be saved in: output/IE000S9YS762_Linde/
# Company name automatically extracted and included in folder name
```

### **Example 2: Batch Processing**
```bash
# Analyze all stocks in mega_cap folder
python master_support_resistance_analyzer.py --all

# Monitor progress and results
```

### **Example 3: Custom Directories**
```bash
# Use custom input and output directories
python master_support_resistance_analyzer.py \
  --data-dir my_data \
  --output-dir my_results \
  AAPL.csv
```

## 📊 **Key Features**

### **🎯 Comprehensive Analysis**
- Multiple S/R identification methods
- Statistical and technical approaches
- Volume-weighted analysis

### **📈 Professional Charts**
- High-quality candlestick charts
- Clear S/R level visualization
- Volume analysis integration

### **🤖 Automated Signals**
- Buy/sell signal generation
- Breakout/breakdown detection
- Volume confirmation logic

### **📁 Organized Output**
- Structured file organization
- JSON summaries for easy parsing
- CSV files for detailed analysis

## 🚨 **Error Handling**

The script includes robust error handling:
- ✅ Missing CSV files
- ✅ Invalid data formats
- ✅ Calculation errors
- ✅ File I/O issues

## 🔄 **Workflow**

1. **Load Data** - Read and validate CSV file
2. **Identify S/R** - Apply multiple detection methods
3. **Generate Charts** - Create candlestick visualizations
4. **Calculate Signals** - Generate trading signals
5. **Save Results** - Organize output files
6. **Create Summary** - Generate analysis overview

## 🏢 **Company Name Features**

The script automatically handles company information:

1. **Automatic Extraction** - Reads company names from CSV 'conm' column
2. **Name Cleaning** - Removes corporate suffixes (Inc, Corp, plc, etc.)
3. **Folder Naming** - Creates folders like `{CSV_ID}_{Company_Name}`
4. **Chart Titles** - Includes company names in all chart titles
5. **Analysis Records** - Preserves full company info in JSON summaries

**Example Output:**
```bash
# Input: IE000S9YS762.csv (contains "Linde plc" in conm column)
# Output: output/IE000S9YS762_Linde/
# Charts: "IE000S9YS762 (Linde plc) - Volume Profile S/R"
```

**Demo the Feature:**
```bash
python demo_company_names.py
```

## 📞 **Support**

For issues or questions:
1. Check the test script output
2. Verify CSV file format
3. Ensure all dependencies are installed
4. Review error messages in console output

---

**🎉 Ready to analyze your stock data with professional-grade support and resistance analysis!**
