#!/usr/bin/env python3
"""
Demonstration script showing the multi-timeframe S/R analysis functionality
in the Master Support & Resistance Analyzer.
"""

import json
import pandas as pd
from pathlib import Path
from master_support_resistance_analyzer import SupportResistanceAnalyzer


def demo_multi_timeframe_analysis():
    """Demonstrate multi-timeframe S/R analysis"""
    print("📊 Multi-Timeframe S/R Analysis Demo")
    print("=" * 50)
    
    # Check if analysis results exist
    output_dir = Path("output")
    company_folder = None
    
    for folder in output_dir.iterdir():
        if folder.is_dir() and (folder / "multi_timeframe_summary.csv").exists():
            company_folder = folder
            break
    
    if not company_folder:
        print("❌ No multi-timeframe analysis found. Run an analysis first!")
        print("   Example: python master_support_resistance_analyzer.py IE000S9YS762.csv")
        return
    
    print(f"📁 Analyzing results from: {company_folder.name}")
    
    # Load multi-timeframe summary
    summary_file = company_folder / "multi_timeframe_summary.csv"
    df_summary = pd.read_csv(summary_file)
    
    print(f"\n📊 Multi-Timeframe S/R Analysis Results:")
    print(f"   🏢 Company: {company_folder.name.split('_', 1)[1] if '_' in company_folder.name else 'Unknown'}")
    print(f"   📈 Timeframes analyzed: {len(df_summary)} periods")
    
    # Display summary table
    print(f"\n📋 Summary Table:")
    print("-" * 120)
    print(f"{'Timeframe':<10} {'Data Points':<12} {'Date Range':<25} {'Price Range':<20} {'Vol Support':<12} {'Vol Resistance':<15}")
    print("-" * 120)
    
    for _, row in df_summary.iterrows():
        timeframe = row['timeframe']
        data_points = f"{row['data_points']:,}"
        date_range = f"{row['date_start']} to {row['date_end'][-5:]}"
        price_range = f"${row['price_min']:.0f} - ${row['price_max']:.0f}"
        vol_support = f"${row['volume_support']:.2f}"
        vol_resistance = f"${row['volume_resistance']:.2f}"
        
        print(f"{timeframe:<10} {data_points:<12} {date_range:<25} {price_range:<20} {vol_support:<12} {vol_resistance:<15}")
    
    print("-" * 120)


def demo_timeframe_comparison():
    """Compare S/R levels across different timeframes"""
    print("\n🔍 Timeframe Comparison Analysis")
    print("=" * 50)
    
    # Find analysis results
    output_dir = Path("output")
    company_folder = None
    
    for folder in output_dir.iterdir():
        if folder.is_dir() and (folder / "multi_timeframe_summary.csv").exists():
            company_folder = folder
            break
    
    if not company_folder:
        return
    
    # Load data
    summary_file = company_folder / "multi_timeframe_summary.csv"
    df_summary = pd.read_csv(summary_file)
    
    print(f"📊 S/R Level Evolution Analysis:")
    
    # Analyze Volume Profile levels
    print(f"\n🔊 Volume Profile Analysis:")
    for _, row in df_summary.iterrows():
        timeframe = row['timeframe']
        support = row['volume_support']
        resistance = row['volume_resistance']
        spread = resistance - support
        
        print(f"   {timeframe:<8}: Support ${support:7.2f} | Resistance ${resistance:7.2f} | Spread ${spread:6.2f}")
    
    # Analyze KMeans levels
    print(f"\n🎯 KMeans Clustering Analysis:")
    for _, row in df_summary.iterrows():
        timeframe = row['timeframe']
        support = row['kmeans_support']
        resistance = row['kmeans_resistance']
        spread = resistance - support
        
        print(f"   {timeframe:<8}: Support ${support:7.2f} | Resistance ${resistance:7.2f} | Spread ${spread:6.2f}")
    
    # Analyze KDE levels
    print(f"\n📈 KDE (Density) Analysis:")
    for _, row in df_summary.iterrows():
        timeframe = row['timeframe']
        support = row['kde_support']
        resistance = row['kde_resistance']
        spread = resistance - support
        
        print(f"   {timeframe:<8}: Support ${support:7.2f} | Resistance ${resistance:7.2f} | Spread ${spread:6.2f}")


def demo_trend_analysis():
    """Analyze trends in S/R levels across timeframes"""
    print("\n📈 S/R Level Trend Analysis")
    print("=" * 50)
    
    # Find analysis results
    output_dir = Path("output")
    company_folder = None
    
    for folder in output_dir.iterdir():
        if folder.is_dir() and (folder / "multi_timeframe_summary.csv").exists():
            company_folder = folder
            break
    
    if not company_folder:
        return
    
    # Load data
    summary_file = company_folder / "multi_timeframe_summary.csv"
    df_summary = pd.read_csv(summary_file)
    
    print(f"📊 Trend Analysis Insights:")
    
    # Analyze support level trends
    vol_supports = df_summary['volume_support'].tolist()
    kmeans_supports = df_summary['kmeans_support'].tolist()
    
    print(f"\n📉 Support Level Trends:")
    print(f"   Volume Profile: ${vol_supports[0]:.2f} (1Y) → ${vol_supports[-1]:.2f} (5Y)")
    vol_trend = "📈 Rising" if vol_supports[-1] > vol_supports[0] else "📉 Falling"
    print(f"   Trend: {vol_trend} (${vol_supports[-1] - vol_supports[0]:+.2f})")
    
    print(f"\n   KMeans: ${kmeans_supports[0]:.2f} (1Y) → ${kmeans_supports[-1]:.2f} (5Y)")
    kmeans_trend = "📈 Rising" if kmeans_supports[-1] > kmeans_supports[0] else "📉 Falling"
    print(f"   Trend: {kmeans_trend} (${kmeans_supports[-1] - kmeans_supports[0]:+.2f})")
    
    # Analyze resistance level trends
    vol_resistances = df_summary['volume_resistance'].tolist()
    kmeans_resistances = df_summary['kmeans_resistance'].tolist()
    
    print(f"\n📈 Resistance Level Trends:")
    print(f"   Volume Profile: ${vol_resistances[0]:.2f} (1Y) → ${vol_resistances[-1]:.2f} (5Y)")
    vol_res_trend = "📈 Rising" if vol_resistances[-1] > vol_resistances[0] else "📉 Falling"
    print(f"   Trend: {vol_res_trend} (${vol_resistances[-1] - vol_resistances[0]:+.2f})")
    
    print(f"\n   KMeans: ${kmeans_resistances[0]:.2f} (1Y) → ${kmeans_resistances[-1]:.2f} (5Y)")
    kmeans_res_trend = "📈 Rising" if kmeans_resistances[-1] > kmeans_resistances[0] else "📉 Falling"
    print(f"   Trend: {kmeans_res_trend} (${kmeans_resistances[-1] - kmeans_resistances[0]:+.2f})")


def demo_trading_insights():
    """Generate trading insights from multi-timeframe analysis"""
    print("\n🎯 Trading Insights from Multi-Timeframe Analysis")
    print("=" * 50)
    
    # Find analysis results
    output_dir = Path("output")
    company_folder = None
    
    for folder in output_dir.iterdir():
        if folder.is_dir() and (folder / "multi_timeframe_summary.csv").exists():
            company_folder = folder
            break
    
    if not company_folder:
        return
    
    # Load data
    summary_file = company_folder / "multi_timeframe_summary.csv"
    df_summary = pd.read_csv(summary_file)
    
    # Get current price
    current_price = df_summary.iloc[0]['price_current']
    
    print(f"💰 Current Price: ${current_price:.2f}")
    print(f"\n🎯 Key S/R Levels by Timeframe:")
    
    # Analyze each timeframe
    for _, row in df_summary.iterrows():
        timeframe = row['timeframe']
        vol_support = row['volume_support']
        vol_resistance = row['volume_resistance']
        
        # Calculate distances
        support_distance = ((current_price - vol_support) / current_price) * 100
        resistance_distance = ((vol_resistance - current_price) / current_price) * 100
        
        print(f"\n   📊 {timeframe}:")
        print(f"      Support: ${vol_support:.2f} ({support_distance:+.1f}%)")
        print(f"      Resistance: ${vol_resistance:.2f} ({resistance_distance:+.1f}%)")
        
        # Trading signals
        if abs(support_distance) < 2:
            print(f"      🟢 NEAR SUPPORT - Potential buy opportunity")
        elif abs(resistance_distance) < 2:
            print(f"      🔴 NEAR RESISTANCE - Potential sell opportunity")
        elif support_distance > 5 and resistance_distance > 5:
            print(f"      🟡 IN MIDDLE ZONE - Wait for clearer signals")
    
    print(f"\n💡 Multi-Timeframe Consensus:")
    
    # Find strongest levels (appearing in multiple timeframes)
    all_supports = df_summary['volume_support'].tolist()
    all_resistances = df_summary['volume_resistance'].tolist()
    
    # Find levels within 5% of each other
    strong_supports = []
    strong_resistances = []
    
    for i, support in enumerate(all_supports):
        count = sum(1 for s in all_supports if abs(s - support) / support < 0.05)
        if count >= 2:  # Appears in at least 2 timeframes
            strong_supports.append((support, count, df_summary.iloc[i]['timeframe']))
    
    for i, resistance in enumerate(all_resistances):
        count = sum(1 for r in all_resistances if abs(r - resistance) / resistance < 0.05)
        if count >= 2:  # Appears in at least 2 timeframes
            strong_resistances.append((resistance, count, df_summary.iloc[i]['timeframe']))
    
    if strong_supports:
        print(f"   🟢 Strong Support Zones:")
        for support, count, timeframe in strong_supports[:3]:  # Top 3
            print(f"      ${support:.2f} (confirmed in {count} timeframes)")
    
    if strong_resistances:
        print(f"   🔴 Strong Resistance Zones:")
        for resistance, count, timeframe in strong_resistances[:3]:  # Top 3
            print(f"      ${resistance:.2f} (confirmed in {count} timeframes)")


def show_file_structure():
    """Show the multi-timeframe output file structure"""
    print("\n📁 Multi-Timeframe Output Structure")
    print("=" * 50)
    
    # Find analysis results
    output_dir = Path("output")
    company_folder = None
    
    for folder in output_dir.iterdir():
        if folder.is_dir() and (folder / "multi_timeframe_summary.csv").exists():
            company_folder = folder
            break
    
    if not company_folder:
        return
    
    print(f"📂 {company_folder.name}/")
    
    # Show multi-timeframe specific files
    multi_files = [
        "multi_timeframe_sr_levels.json",
        "multi_timeframe_summary.csv"
    ]
    
    for file in multi_files:
        if (company_folder / file).exists():
            size = (company_folder / file).stat().st_size / 1024  # KB
            print(f"   📄 {file} ({size:.1f} KB)")
    
    # Show multi-timeframe charts
    charts_dir = company_folder / "multi_timeframe_charts"
    if charts_dir.exists():
        print(f"   📂 multi_timeframe_charts/")
        for chart in charts_dir.glob("*.html"):
            size = chart.stat().st_size / (1024 * 1024)  # MB
            print(f"      🖼️  {chart.name} ({size:.1f} MB)")


def main():
    """Main demonstration function"""
    print("🚀 Master Support & Resistance Analyzer")
    print("📊 Multi-Timeframe Analysis Demo")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("mega_cap").exists():
        print("❌ Please run this script from the support_resistance directory")
        return 1
    
    try:
        # Demo 1: Multi-timeframe analysis overview
        demo_multi_timeframe_analysis()
        
        # Demo 2: Timeframe comparison
        demo_timeframe_comparison()
        
        # Demo 3: Trend analysis
        demo_trend_analysis()
        
        # Demo 4: Trading insights
        demo_trading_insights()
        
        # Demo 5: File structure
        show_file_structure()
        
        print("\n" + "=" * 60)
        print("🎉 Multi-Timeframe Analysis Demo Complete!")
        
        print("\n💡 Key Features:")
        print("   📊 Analyzes 1, 2, 3, 4, and 5-year timeframes")
        print("   🎯 Compares S/R levels across all timeframes")
        print("   📈 Shows evolution of support/resistance over time")
        print("   🖱️  Interactive charts for each timeframe comparison")
        print("   📋 CSV summary for easy analysis")
        print("   💰 Trading insights based on multi-timeframe consensus")
        
        print("\n🚀 Usage:")
        print("   python master_support_resistance_analyzer.py YOUR_FILE.csv")
        print("   → Automatically generates multi-timeframe analysis")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
