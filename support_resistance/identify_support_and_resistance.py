import os
import json
import pandas as pd
import numpy as np
from scipy.signal import argrelextrema
from sklearn.cluster import KMeans
from scipy.stats import gaussian_kde

# Load your monthly OHLCV data
df = pd.read_csv("./mega_cap/IE000S9YS762.csv", parse_dates=["date"], index_col="date")
df = df.dropna()

def get_swing_points(df, order=3):
    highs = df['High'].values
    lows = df['Low'].values
    
    swing_highs_idx = argrelextrema(highs, np.greater_equal, order=order)[0]
    swing_lows_idx = argrelextrema(lows, np.less_equal, order=order)[0]

    swing_highs = df.iloc[swing_highs_idx][['High']]
    swing_lows = df.iloc[swing_lows_idx][['Low']]

    return swing_highs, swing_lows

def compute_vwpp(df):
    # VWPP = (2C - L - H)/(H - L) * Volume
    vwpp = ((2 * df['Close'] - df['Low'] - df['High']) / 
            (df['High'] - df['Low']).replace(0, np.nan)) * df['Volume']
    return vwpp

def get_swing_points_enhanced(df, order=3, vwpp_threshold=0.0):
    """
    Identify swing highs/lows and filter them based on VWPP strength.
    """
    df = df.copy()
    df['vwpp'] = compute_vwpp(df)

    highs = df['High'].values
    lows = df['Low'].values

    swing_highs_idx = argrelextrema(highs, np.greater_equal, order=order)[0]
    swing_lows_idx = argrelextrema(lows, np.less_equal, order=order)[0]

    swing_highs = df.iloc[swing_highs_idx][['High', 'vwpp']].copy()
    swing_highs = swing_highs[swing_highs['vwpp'] >= vwpp_threshold]
    swing_highs['type'] = 'resistance'
    swing_highs.rename(columns={'High': 'Price'}, inplace=True)

    swing_lows = df.iloc[swing_lows_idx][['Low', 'vwpp']].copy()
    swing_lows = swing_lows[swing_lows['vwpp'] <= -vwpp_threshold]
    swing_lows['type'] = 'support'
    swing_lows.rename(columns={'Low': 'Price'}, inplace=True)

    swings = pd.concat([swing_highs, swing_lows]).sort_index()
    return swings

def get_pivot_points(df):
    pivot = (df['High'] + df['Low'] + df['Close']) / 3
    r1 = (2 * pivot) - df['Low']
    s1 = (2 * pivot) - df['High']
    r2 = pivot + (df['High'] - df['Low'])
    s2 = pivot - (df['High'] - df['Low'])
    r3 = df['High'] + 2 * (pivot - df['Low'])
    s3 = df['Low'] - 2 * (df['High'] - pivot)

    return pd.DataFrame({
        'pivot': pivot,
        'resistance1': r1,
        'support1': s1,
        'resistance2': r2,
        'support2': s2,
        'resistance3': r3,
        'support3': s3
    })

def get_volume_profile_levels(df, bins=50):
    price = ((df['High'] + df['Low']) / 2).values
    hist, bin_edges = np.histogram(price, bins=bins, weights=df['Volume'])

    top_bins = bin_edges[np.argsort(hist)[-3:]]  # 3 most active price zones
    top_bins.sort()
    support_level = top_bins[0]
    resistance_level = top_bins[-1]
    
    return support_level, resistance_level

def get_volume_profile_levels_enhanced(df, bins=50, top_n=3, return_profile=False):
    """
    Calculates high-volume price zones (support/resistance) from volume profile.
    
    Parameters:
        df (pd.DataFrame): DataFrame with 'High', 'Low', 'Close', 'Volume'
        bins (int): Number of price bins
        top_n (int): Number of top volume bins to consider
        return_profile (bool): Whether to return full volume histogram
        
    Returns:
        support_level (float): Lower price zone with high volume
        resistance_level (float): Upper price zone with high volume
        (Optional) profile_df (pd.DataFrame): Bin centers and volume per bin
    """
    
    # Use typical price instead of just midpoint
    price = ((df['High'] + df['Low'] + df['Close']) / 3).values
    
    # Volume-weighted histogram
    hist, bin_edges = np.histogram(price, bins=bins, weights=df['Volume'])
    
    # Bin centers (more meaningful than edges)
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

    # Top-N most active price bins
    top_n = min(top_n, len(hist))  # safeguard
    top_indices = np.argsort(hist)[-top_n:]
    top_levels = bin_centers[top_indices]
    top_levels.sort()

    support_level = top_levels[0]
    resistance_level = top_levels[-1]

    if return_profile:
        profile_df = pd.DataFrame({'Price': bin_centers, 'Volume': hist})
        return support_level, resistance_level, profile_df

    return support_level, resistance_level

def get_kmeans_sr(df, k=3):
    prices = df['Close'].values.reshape(-1, 1)
    kmeans = KMeans(n_clusters=k, random_state=42).fit(prices)
    centers = sorted(kmeans.cluster_centers_.flatten())

    support = centers[0]
    resistance = centers[-1]
    
    return support, resistance

def get_kmeans_sr_enhanced(df, k=3, use_ohlc=True, return_all=False):
    """
    Estimate support/resistance levels using KMeans clustering on price data.

    Parameters:
        df (pd.DataFrame): DataFrame with price data (Close, or OHLC)
        k (int): Number of clusters
        use_ohlc (bool): Whether to use OHLC average instead of just Close
        return_all (bool): Return all cluster centers or only support/resistance

    Returns:
        If return_all:
            List of sorted cluster centers (price zones)
        Else:
            Tuple (support, resistance)
    """
    
    if use_ohlc:
        prices = df[['Open', 'High', 'Low', 'Close']].mean(axis=1).values.reshape(-1, 1)
    else:
        prices = df['Close'].values.reshape(-1, 1)
    
    if len(prices) < k:
        raise ValueError("Not enough data points for the requested number of clusters.")
    
    kmeans = KMeans(n_clusters=k, n_init=10, random_state=42).fit(prices)
    centers = sorted(kmeans.cluster_centers_.flatten())

    if return_all:
        return centers
    else:
        return centers[0], centers[-1]

def get_kde_sr(df, bw_method='scott', num_peaks=2):
    prices = df['Close'].values
    kde = gaussian_kde(prices, bw_method=bw_method)
    
    price_range = np.linspace(prices.min(), prices.max(), 1000)
    density = kde(price_range)

    peaks_idx = np.argpartition(density, -num_peaks)[-num_peaks:]
    levels = price_range[peaks_idx]
    levels.sort()
    
    support = levels[0]
    resistance = levels[-1]
    
    return support, resistance

def get_kde_sr_enhanced(df, bw_method='scott', num_peaks=2, use_ohlc=False, return_all=False):
    """
    Estimate support and resistance using KDE on price distribution.

    Parameters:
        df (pd.DataFrame): Must contain Close or OHLC columns
        bw_method (str or float): KDE bandwidth method ('scott', 'silverman', or float)
        num_peaks (int): Number of peak density levels to extract
        use_ohlc (bool): Use average of OHLC instead of Close
        return_all (bool): Return list of all detected price levels instead of just support/resistance

    Returns:
        If return_all:
            Sorted list of high-density price levels
        Else:
            Tuple: (support, resistance)
    """

    if use_ohlc:
        prices = df[['Open', 'High', 'Low', 'Close']].mean(axis=1).values
    else:
        prices = df['Close'].values

    if len(prices) < num_peaks:
        raise ValueError("Not enough data points to extract requested number of KDE peaks.")

    kde = gaussian_kde(prices, bw_method=bw_method)

    price_range = np.linspace(prices.min(), prices.max(), 1000)
    density = kde(price_range)

    # Get indices of top peaks (sorted by actual density)
    peak_indices = np.argsort(density)[-num_peaks:]
    peak_levels = price_range[peak_indices]
    peak_densities = density[peak_indices]

    # Sort by price for support/resistance logic
    sorted_levels = sorted(peak_levels)

    if return_all:
        return sorted_levels
    else:
        return sorted_levels[0], sorted_levels[-1]


def get_fibonacci_levels(df):
    max_price = df['High'].max()
    min_price = df['Low'].min()
    diff = max_price - min_price

    levels = {
        '0.0': max_price,
        '0.236': max_price - 0.236 * diff,
        '0.382': max_price - 0.382 * diff,
        '0.5': max_price - 0.5 * diff,
        '0.618': max_price - 0.618 * diff,
        '0.786': max_price - 0.786 * diff,
        '1.0': min_price,
    }

    return levels

def get_fibonacci_levels_enhanced(df, extension=False, as_df=False):
    """
    Calculate Fibonacci retracement (and optional extension) levels.

    Parameters:
        df (pd.DataFrame): Must contain 'High' and 'Low'
        extension (bool): Include extensions like 1.272, 1.618, 2.0
        as_df (bool): Return as DataFrame instead of dict

    Returns:
        dict or pd.DataFrame: Fibonacci levels
    """
    max_price = df['High'].max()
    min_price = df['Low'].min()
    diff = max_price - min_price

    # Detect direction
    is_uptrend = df['Close'].iloc[-1] >= df['Open'].iloc[0]
    high = max_price if is_uptrend else min_price
    low = min_price if is_uptrend else max_price
    diff = abs(high - low)

    # Basic retracement levels
    levels = {
        '0.0': high,
        '0.236': high - 0.236 * diff,
        '0.382': high - 0.382 * diff,
        '0.5': high - 0.5 * diff,
        '0.618': high - 0.618 * diff,
        '0.786': high - 0.786 * diff,
        '1.0': low,
    }

    # Optional extension levels
    if extension:
        levels.update({
            '1.272': high - 1.272 * diff,
            '1.618': high - 1.618 * diff,
            '2.0': high - 2.0 * diff,
        })

    if as_df:
        return pd.DataFrame(list(levels.items()), columns=['Level', 'Price']).sort_values(by='Price', ascending=False)

    return levels

# Monthly data assumed
swing_highs, swing_lows = get_swing_points(df)
pivot_levels = get_pivot_points(df)
volume_support, volume_resistance = get_volume_profile_levels(df)
kmeans_support, kmeans_resistance = get_kmeans_sr(df)
kde_support, kde_resistance = get_kde_sr(df)
fib_levels = get_fibonacci_levels(df)
swings_enhanced = get_swing_points_enhanced(df, order=5, vwpp_threshold=df['Volume'].quantile(0.5))
enhanced_volume_support, enhanced_volume_resistance = get_volume_profile_levels_enhanced(df)
enhanced_kmeans_support, enhanced_kmeans_resistance = get_kmeans_sr_enhanced(df)
enhanced_kde_support, enhanced_kde_resistance = get_kde_sr(df)
enhanced_fib_levels = get_fibonacci_levels_enhanced(df)


print(">>> Swing Highs:\n", swing_highs)
print(">>> Swing Lows:\n", swing_lows)

print(">>> Pivot Levels (Support & Resistance):\n", pivot_levels)

print(f">>> Volume Profile Support: {volume_support}")
print(f">>> Volume Profile Resistance: {volume_resistance}")

print(f">>> KMeans-Based Support: {kmeans_support}")
print(f">>> KMeans-Based Resistance: {kmeans_resistance}")

print(f">>> KDE-Based Support: {kde_support}")
print(f">>> KDE-Based Resistance: {kde_resistance}")

print(">>> Fibonacci Levels:\n", fib_levels)

print(">>> Enhanced Swings (Filtered Levels):\n", swings_enhanced)

print(f">>> Enhanced Volume Support: {enhanced_volume_support}")
print(f">>> Enhanced Volume Resistance: {enhanced_volume_resistance}")

print(f">>> Enhanced KMeans Support: {enhanced_kmeans_support}")
print(f">>> Enhanced KMeans Resistance: {enhanced_kmeans_resistance}")

print(f">>> Enhanced KDE Support: {enhanced_kde_support}")
print(f">>> Enhanced KDE Resistance: {enhanced_kde_resistance}")

print(">>> Enhanced Fibonacci Levels:\n", enhanced_fib_levels)


# -------------------------------
# ✅ Save all outputs to 'output/' folder
# -------------------------------

# Create output directory
output_dir = os.path.join(os.path.dirname(__file__), "output")
os.makedirs(output_dir, exist_ok=True)

# Save swing highs and lows
swing_highs.to_csv(os.path.join(output_dir, "swing_highs.csv"))
swing_lows.to_csv(os.path.join(output_dir, "swing_lows.csv"))

# Save pivot levels
pivot_levels.to_csv(os.path.join(output_dir, "pivot_levels.csv"))

# Save volume profile support/resistance
with open(os.path.join(output_dir, "volume_profile_levels.txt"), "w") as f:
    f.write(f"Support: {volume_support}\nResistance: {volume_resistance}\n")

# Save KMeans support/resistance
with open(os.path.join(output_dir, "kmeans_levels.txt"), "w") as f:
    f.write(f"Support: {kmeans_support}\nResistance: {kmeans_resistance}\n")

# Save KDE support/resistance
with open(os.path.join(output_dir, "kde_levels.txt"), "w") as f:
    f.write(f"Support: {kde_support}\nResistance: {kde_resistance}\n")

# Save Fibonacci levels
with open(os.path.join(output_dir, "fibonacci_levels.json"), "w") as f:
    json.dump(fib_levels, f, indent=4)

# Save enhanced swing points
swings_enhanced.to_csv(os.path.join(output_dir, "swings_enhanced.csv"))

# Save enhanced volume support/resistance
with open(os.path.join(output_dir, "enhanced_volume_levels.txt"), "w") as f:
    f.write(f"Support: {enhanced_volume_support}\nResistance: {enhanced_volume_resistance}\n")

# Save enhanced KMeans support/resistance
with open(os.path.join(output_dir, "enhanced_kmeans_levels.txt"), "w") as f:
    f.write(f"Support: {enhanced_kmeans_support}\nResistance: {enhanced_kmeans_resistance}\n")

# Save enhanced KDE support/resistance
with open(os.path.join(output_dir, "enhanced_kde_levels.txt"), "w") as f:
    f.write(f"Support: {enhanced_kde_support}\nResistance: {enhanced_kde_resistance}\n")

# Save enhanced Fibonacci levels
with open(os.path.join(output_dir, "enhanced_fibonacci_levels.json"), "w") as f:
    json.dump(enhanced_fib_levels, f, indent=4)

print(f"\n✅ All outputs saved in folder: {output_dir}")
