import pandas as pd
import numpy as np

def generate_sr_features(df):
    """
    Expects a DataFrame with columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'support_level', 'resistance_level']
    Returns the same DataFrame with additional support/resistance features.
    """

    # --- Band and distance features ---
    df['sr_bandwidth'] = df['resistance_level'] - df['support_level']
    df['dist_to_support'] = df['Close'] - df['support_level']
    df['dist_to_resistance'] = df['resistance_level'] - df['Close']
    df['pct_to_support'] = (df['Close'] - df['support_level']) / df['Close']
    df['pct_to_resistance'] = (df['resistance_level'] - df['Close']) / df['Close']
    df['pct_through_band'] = (df['Close'] - df['support_level']) / df['sr_bandwidth']

    # --- Slope features ---
    df['support_slope'] = df['support_level'].diff()
    df['resistance_slope'] = df['resistance_level'].diff()
    df['support_slope_norm'] = df['support_slope'] / df['sr_bandwidth']
    df['resistance_slope_norm'] = df['resistance_slope'] / df['sr_bandwidth']


    # --- Zone classification ---
    df['sr_zone'] = np.select(
        [
            df['Close'] < df['support_level'],
            (df['Close'] >= df['support_level']) & (df['Close'] <= df['resistance_level']),
            df['Close'] > df['resistance_level']
        ],
        [-1, 0, 1]
    )

    # --- Cross detection ---
    df['crossed_support'] = ((df['Close'] < df['support_level']) & (df['Close'].shift(1) >= df['support_level'])).astype(int)
    df['crossed_resistance'] = ((df['Close'] > df['resistance_level']) & (df['Close'].shift(1) <= df['resistance_level'])).astype(int)
    df['support_cross_count_3'] = df['crossed_support'].rolling(3).sum()
    df['resistance_cross_count_3'] = df['crossed_resistance'].rolling(3).sum()

    # --- Bounce detection ---
    df['bounce_from_support'] = (df['Close'] > df['support_level']) & (df['Close'].shift(1) <= df['support_level'])
    df['bounce_from_resistance'] = (df['Close'] < df['resistance_level']) & (df['Close'].shift(1) >= df['resistance_level'])

    # --- Breakout / breakdown ---
    df['breakout'] = ((df['Close'] > df['resistance_level']) & (df['Close'].shift(1) <= df['resistance_level'])).astype(int)
    df['breakdown'] = ((df['Close'] < df['support_level']) & (df['Close'].shift(1) >= df['support_level'])).astype(int)

    # --- Fakeout detection ---
    df['fakeout_up'] = ((df['Close'].shift(1) > df['resistance_level']) & (df['Close'] < df['resistance_level'])).astype(int)
    df['fakeout_down'] = ((df['Close'].shift(1) < df['support_level']) & (df['Close'] > df['support_level'])).astype(int)

    # --- Breakout/Breakdown strength ---
    df['breakout_strength'] = ((df['Close'] - df['resistance_level']) / df['sr_bandwidth']).clip(lower=0)
    df['breakdown_strength'] = ((df['support_level'] - df['Close']) / df['sr_bandwidth']).clip(lower=0)

    # --- Volume confirmation ---
    df['volume_spike'] = df['Volume'] > df['Volume'].rolling(10).mean() * 1.5
    df['confirmed_breakout'] = df['breakout'] & df['volume_spike']
    df['confirmed_breakdown'] = df['breakdown'] & df['volume_spike']

    # --- Band contraction signal ---
    df['sr_band_narrowing'] = df['sr_bandwidth'] / df['sr_bandwidth'].rolling(3).mean()
    df['band_contraction_signal'] = (df['sr_band_narrowing'] < 0.8).astype(int)

    # --- Inside band streak ---
    df['inside_band'] = ((df['Close'] >= df['support_level']) & (df['Close'] <= df['resistance_level'])).astype(int)
    df['inside_band_streak'] = df['inside_band'] * (
        df['inside_band'].groupby((df['inside_band'] != df['inside_band'].shift()).cumsum()).cumcount() + 1
    )

    # --- Time since last breakout/breakdown ---
    df['last_breakout'] = df['breakout'].cumsum()
    df['months_since_breakout'] = df.groupby('last_breakout').cumcount()

    df['last_breakdown'] = df['breakdown'].cumsum()
    df['months_since_breakdown'] = df.groupby('last_breakdown').cumcount()

    # --- Support/Resistance flip ---
    df['sr_flip_up'] = (df['breakout'].shift(1) == 1) & (df['Close'] > df['support_level'])
    df['sr_flip_down'] = (df['breakdown'].shift(1) == 1) & (df['Close'] < df['resistance_level'])
    df['sr_flip_up'] = df['sr_flip_up'].astype(int)
    df['sr_flip_down'] = df['sr_flip_down'].astype(int)

    # --- Bandwidth change ---
    df['bandwidth_change'] = df['sr_bandwidth'].pct_change()
    df['bandwidth_compression'] = (df['bandwidth_change'] < -0.1).astype(int)
    df['bandwidth_expansion'] = (df['bandwidth_change'] > 0.1).astype(int)

    # --- Rejection at support/resistance ---
    df['rejected_at_resistance'] = ((df['High'] > df['resistance_level']) & (df['Close'] < df['resistance_level'])).astype(int)
    df['rejected_at_support'] = ((df['Low'] < df['support_level']) & (df['Close'] > df['support_level'])).astype(int)

    # --- Price above/below support/resistance ---
    df['above_resistance'] = (df['Close'] > df['resistance_level']).astype(int)
    df['below_support'] = (df['Close'] < df['support_level']).astype(int)

    # Rolling duration
    df['above_resistance_streak'] = df['above_resistance'] * (df['above_resistance'].groupby((df['above_resistance'] != df['above_resistance'].shift()).cumsum()).cumcount() + 1)
    df['below_support_streak'] = df['below_support'] * (df['below_support'].groupby((df['below_support'] != df['below_support'].shift()).cumsum()).cumcount() + 1)

    # --- Confluence features ---
    df['sr_confluence'] = (df['sr_bandwidth'] / df['Close'] < 0.03).astype(int)

    # Within 1% of support/resistance
    df['near_support'] = (abs(df['Close'] - df['support_level']) / df['Close'] < 0.01).astype(int)
    df['near_resistance'] = (abs(df['Close'] - df['resistance_level']) / df['Close'] < 0.01).astype(int)

    df['support_hits_6m'] = df['near_support'].rolling(6).sum()
    df['resistance_hits_6m'] = df['near_resistance'].rolling(6).sum()

    # --- Deviation from Mean of S/R Band ---
    df['sr_midpoint'] = (df['support_level'] + df['resistance_level']) / 2
    df['price_to_midpoint'] = (df['Close'] - df['sr_midpoint']) / df['sr_bandwidth']

    # --- Additional features for model training ---
    df['breakout_strength_std'] = (df['breakout_strength'] > 0.1).astype(int)
    df['near_top'] = (df['pct_through_band'] > 0.95).astype(int)
    df['near_bottom'] = (df['pct_through_band'] < 0.05).astype(int)

    
    df['breakout_strength_std'] = (df['breakout_strength'] > 0.1).astype(int)
    df['near_top'] = (df['pct_through_band'] > 0.95).astype(int)
    df['near_bottom'] = (df['pct_through_band'] < 0.05).astype(int)

    return df

df = pd.read_csv("monthly_data.csv", parse_dates=["Date"], index_col="Date")
df = generate_sr_features(df)




df['bullish_engulfing'] = ((df['Open'].shift(1) > df['Close'].shift(1)) & 
                           (df['Close'] > df['Open']) & 
                           (df['Open'] < df['Close'].shift(1)) & 
                           (df['Close'] > df['Open'].shift(1))).astype(int)

df['bearish_engulfing'] = ((df['Open'].shift(1) < df['Close'].shift(1)) & 
                           (df['Close'] < df['Open']) & 
                           (df['Open'] > df['Close'].shift(1)) & 
                           (df['Close'] < df['Open'].shift(1))).astype(int)

# Marubozu candle: no shadows
df['marubozu'] = ((abs(df['Open'] - df['Low']) < 0.01 * df['Close']) & 
                  (abs(df['High'] - df['Close']) < 0.01 * df['Close'])).astype(int)

# On-Balance Volume
df['obv'] = (np.sign(df['Close'].diff()) * df['Volume']).fillna(0).cumsum()

# Volume Rate of Change (VoC)
df['volume_roc'] = df['Volume'].pct_change(periods=3)

# Accumulation/Distribution Index (simplified) VWPP
df['adi'] = ((2 * df['Close'] - df['Low'] - df['High']) / (df['High'] - df['Low']).replace(0, np.nan)) * df['Volume']
df['adi'] = df['adi'].fillna(0).cumsum()
