import os
import json
import subprocess
import sys
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from multiassetdailyrun.helpers import *

s3conn = s3_config()
with open('multiassetdailyrun/config.yaml', 'r') as file:
    config= yaml.safe_load(file)

def send_email(subject ,receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']

    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)


def run_script(script_path : str,  log_dir: str = "script_logs" ) -> bool:
    """
    Run a Python script using subprocess and log its execution.
    
    Args:
        script_path (str): Path to the Python script.
    
    Returns:
        bool: True if the script executed successfully, False otherwise.
    """
    try:
        # Create log directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)

        print(f"Starting script: {script_path}")

        # Get absolute path of the script
        abs_script_path = os.path.abspath(script_path)
        script_dir = os.path.dirname(abs_script_path)
        script_name = os.path.basename(abs_script_path)
        #print(abs_script_path, script_dir,script_name, sep = '\n' )
        script_base = os.path.splitext(script_name)[0]

        # Create timestamped log file
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file_path = os.path.join(log_dir, f"{script_base}.log")

        print(f"Running script: {script_path}")
        print(f"Logging output to: {log_file_path}")


        # Determine how to run the script based on extension
        if script_name.endswith('.py'):
            cmd = [sys.executable, script_name]
        elif script_name.endswith('.sh'):
            # Run shell script using bash
            cmd = ['bash', script_name]
        else:
            # Run as executable directly (make sure it's executable!)
            #cmd = [script_name]
            print('No proper file extension given')

        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=script_dir,
            shell=False  # Do not use shell=True for security and cross-platform reasons
        )

        with open(log_file_path, "w", encoding="utf-8") as log_file:
            log_file.write(f"--- STDOUT ---\n{result.stdout}\n")
            log_file.write(f"\n--- STDERR ---\n{result.stderr}\n")

        # upload log file to s3
        s3conn.upload_file(log_file_path, 'etf-predictions',f'inference_logs/{log_file_path}')
            
        # Log output and errors
        if result.stdout:
            print(f"Output from {script_path} :\n {result.stdout}")
        if result.stderr:
            print(f"Error from {script_path} :\n {result.stderr}")

        # Check exit code
        if result.returncode == 0:
            print(f"Script {script_path} executed successfully.")
            return True
        else:
            print(f"Script {script_path} failed with exit code {result.returncode}.")
            return False
    except Exception as e:
        print(f"Exception occurred while running script {script_path}: {str(e)}")
        return False


def run_parallel(scripts):
    with ThreadPoolExecutor(max_workers=len(scripts)) as executor:
        futures = {executor.submit(run_script, script): script for script in scripts}
        for future in as_completed(futures):
            script = futures[future]
            if not future.result():
                print(f"Error: {script} failed.")
                return False
    return True

def run_sequential(scripts):
    for script in scripts:
        success = run_script(script)
        if not success:
            print(f"Stopping execution due to failure in script: {script}")
            print(f"Error: Script {script} failed. Stopping further execution.")
            return False
        else:
            print(f"Script {script} executed successfully.")

    return True
            
def main():
    """
    Main function to execute multiple Python scripts.
    """
    script_0 = "etfsdailyruns/monthly_bnp_daily_run/bnp_daily_run.py"

    script_1_script_2 =  ["deliverymetrics/delivery_metrics_final.py", ## delivery_metrics script
                         "etfsdailyruns/monthly-etfs_django_run/monthly_etfs_django_autotrigger.py"]
    
    script_3 = "etfsdailyruns/monthly-etfs_data_updation/etfs_data_updation_to_s3.py" ### django data updation to s3

    script_4_script_5 = ["etfsdailyruns/transformation_pipeline_without_context/monthly/monthly_without_context_dailyrun.py",
              "etfsdailyruns/transformation_pipeline_without_context/quarterly/quarterly_without_context_dailyrun.py"]

    script_6_script_7 = ["etfsdailyruns/transformation_pipeline_with_context/predict.sh",
                        "etfsdailyruns/quarterly-file_generation/quarterly_file_generation.py"] 

    scripts_8_sequential = [
        "postprocessingscripts/postprocessed_script_final.py", ## post processing script
        
        "deliveryscripts/monthly_aigo_preportfolio.py",
        "deliveryscripts/monthly_db_preportfolio.py",
        "deliveryscripts/monthly_bnp_preportfolio.py",
        "deliveryscripts/quarterly_aigo_preportfolio.py",

        "multiassetdailyrun/multiasset_dailyrun.py", # multiasset daily run both monthly & quarterly
        
        "deliveryscripts/monthly_multiasset_preportfolio.py",
        "deliveryscripts/quarterly_multiasset_preportfolio.py",
        "clientdelivery/aigobifurcation/aigo_file_bifurcation.py", # AIGO file bifurcation

        "etfsdailyruns/model_metrics_predictions_versioning/model_metrics_predictions_versioning.py" #model metrics & predictions versioning
    ]

    print("\n--- Running Stage: script_0 ---")
    if not run_script(script_0):
        print("Stopping due to failure in script_0")
        return False
    
    print("\n--- Running Stage: script_1 and script_2 ---")
    if not run_parallel(script_1_script_2):
        print("Stopping due to failure in script_1 or script_2")
        return False

    print("\n--- Running Stage: script_3 ---")
    if not run_script(script_3):
        print("Stopping due to failure in script 3")
        return False

    print("\n--- Running Stage: script_4 and script_5 ---")
    if not run_parallel(script_4_script_5):
        print("Stopping due to failure in script_4 or script_5")
        return False

    print("\n--- Running Stage: script_6 and script_7 ---")
    if not run_parallel(script_6_script_7):
        print("Stopping due to failure in script_6 or script_7")
        return False

    print("\n--- Running Stage: script_8 sequential ---")
    if not run_sequential(scripts_8_sequential):
        print("Stopping due to failure in script_6 or script_7")
        return False

    return True

if __name__ == "__main__":
    receiver = config['Gmail_creds']['email_receiver_failed']
    if main():
        send_email(f'ALL Triggers: Run COMPLETED successfully',receiver, 'SUCCESS')
    else:
        send_email(f'ALL Triggers : Run FAILED',receiver, 'FAILED')
