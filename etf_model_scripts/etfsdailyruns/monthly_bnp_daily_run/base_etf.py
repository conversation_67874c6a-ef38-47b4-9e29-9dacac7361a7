from helpers import *

class BaseETF:
    def __init__(self, etf_name,cp_date):
        self.etf_name = etf_name
        self.cp_date = cp_date
        self.data = None
        self.missing_data_df = pd.DataFrame(columns=['feature', 'missing_days'])

    def collect_data(self):
        """Collect ETF data from various sources."""
        self.data = get_etf_raw_data(self.etf_name,self.cp_date)
        return self.data

    # def validate_data(self):
    #     """Validate collected data."""
    #     if self.data is None:
    #         raise ValueError("Data not collected yet")
        
    #     # validation_report = validate_data(self.data)
    #     # print(f"Data validation completed for {self.etf_name}. Report: {validation_report}")

    # def track_missing_data(self):
    #     """Track which features have missing data and for how many days."""
    #     if self.data is None:
    #         raise ValueError("Data not collected yet")
        
    #     # missing_data_info = track_missing_data(self.data)
    #     # self.missing_data_df = pd.concat([self.missing_data_df, missing_data_info], ignore_index=True)
    #     print(f"Missing data tracked for {self.etf_name}.")

    # def save_data(self):
    #     """Save data to S3 and Elasticsearch."""
    #     if self.data is None:
    #         raise ValueError("Data not collected yet")
        
    #     # Save to S3
    #     s3_bucket_name = 'your-s3-bucket-name'
    #     s3_key = f"{self.etf_name}/{self.run_date}_data.csv"
    #     # save_to_s3(self.data, s3_bucket_name, s3_key)
    #     # print(f"Data saved to S3 at {s3_key}")

    #     # Save missing data report to Elasticsearch
    #     # es = Elasticsearch(hosts=["your-elasticsearch-host"])
    #     # save_to_es(self.missing_data_df, es, index_name=f"{self.etf_name}_missing_data")
    #     # print(f"Missing data report saved to Elasticsearch for {self.etf_name}.")

    # def process(self):
    #     """Main function to run the entire data collection process."""
    #     self.collect_data()
    #     self.validate_data()
    #     self.track_missing_data()
    #     self.save_data()

