from base_etf import BaseETF
from helpers import *

class BNPIDSBU(BaseETF):
    def __init__(self, cp_date):
        super().__init__("BNPIDSBU", cp_date)
        
    def collect_data(self):
        """
        Collect etf specific data here.
        Returns a DataFrame.
        """
        super().collect_data()
        self.data['yearhigh'] = self.data['close_price'].rolling(window=252, min_periods=252).max()
        self.data['yearlow'] = self.data['close_price'].rolling(window=252, min_periods=252).min()

        self.data = get_volatility(self.data,252*5, 'volatility_5yr')
        self.data = get_volatility(self.data, 252*2, 'volatility_2yr')
        self.data = get_volatility(self.data, 252*1, 'volatility_1yr')

        self.data = get_macro_data(self.data,self.cp_date,'Usa','Equity','USA')
        self.data = get_bnp_technical_indicators(self.data,self.etf_name)
        
        self.data = get_aluminium(self.data)
        self.data = get_gold(self.data)
        self.data = get_silver(self.data)
        self.data = get_copper(self.data)
        self.data = get_lead(self.data)
        self.data = get_nickel(self.data)
        self.data = get_zinc(self.data)
        
        senti_dat = get_etf_KW_sentiment(self.cp_date,'BCKTXALC_commodity')
        senti_dat = senti_dat[senti_dat.columns].rolling(window = 30).mean()
        self.data = self.data.join(senti_dat)


        self.data = get_snp_gold(self.data)
        self.data = get_snp_aluminium(self.data)
        self.data = get_snp_silver(self.data)
        self.data = get_snp_copper(self.data)

        self.data = get_all_freddata(self.data, self.etf_name)
        
        self.data=get_momentum_data(self.data,self.etf_name,self.cp_date)
        self.data = get_LSTM_data(self.data,self.etf_name,self.cp_date)
        self.data=clean_etf_data(self.data,self.cp_date,self.etf_name)
        save_daily_data(self.data,self.etf_name)
        

        return self.data
