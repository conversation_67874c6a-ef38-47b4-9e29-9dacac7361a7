from base_etf import BaseETF
from helpers import *

class BNPIFUS(BaseETF):
    def __init__(self, cp_date):
        super().__init__("BNPIFUS", cp_date)
        
    def collect_data(self):
        """
        Collect etf specific data here.
        Returns a DataFrame.
        """
        super().collect_data()
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'SPY:ARCA','IQ_VOLUME',self.data,cname='Volume')
        self.data['daily_volume_change'] = self.data['Volume'].pct_change(1)*100
        self.data['weekly_volume_change'] = self.data['Volume'].pct_change(5)*100
        self.data['monthly_volume_change'] = self.data['Volume'].pct_change(22)*100
        self.data['yearly_volume_change'] = self.data['Volume'].pct_change(252)*100

        self.data['yearhigh'] = self.data['close_price'].rolling(window=252, min_periods=252).max()
        self.data['yearlow'] = self.data['close_price'].rolling(window=252, min_periods=252).min()

        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'IQ2668699','IQ_CLOSEPRICE',self.data,cname='index_day_closeprice')
        self.data = get_beta_calculations(self.data)

        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'SPY:ARCA','IQ_MARKETCAP',self.data,cname='market_cap')
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'SPY:ARCA','IQ_SHARESOUTSTANDING',self.data,cname='Share Outstanding')
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'SPY:ARCA','IQ_DIVIDEND_YIELD',self.data,cname='Dividend yield')
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'SPY:ARCA','IQ_RSI_ADJ',self.data, cname='RSI Dividend ADJ')
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'SPY:ARCA','IQ_VALUE_TRADED',self.data,cname='Daily value traded')
        

        self.data = get_volatility(self.data,252*5, 'volatility_5yr')
        self.data = get_volatility(self.data, 252*2, 'volatility_2yr')
        self.data = get_volatility(self.data, 252*1, 'volatility_1yr')
        self.data = get_US_treasury(self.data,self.etf_name)
        self.data = get_rolledup_data(self.data,'SPY',self.cp_date)
        self.data = get_gdp_forecasted_data(self.data)
        self.data = get_future_fedfun_rate(self.data)
        self.data = get_AIGO_sentiment(self.data,'SPY',self.cp_date)
        self.data = get_macro_data(self.data,self.cp_date,'Usa','Equity','USA')
        self.data = get_aigo_technical_indicators(self.data,'spy')
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"),'IQ2667254','IQ_CLOSEPRICE',self.data,cname='DJI_closeprice')
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"),'IQ2668699','IQ_CLOSEPRICE',self.data,cname='SPX_closeprice')
        self.data = get_all_freddata(self.data, self.etf_name)
        self.data = get_self_and_corr_predictions(self.data)
        self.data=get_momentum_data(self.data,self.etf_name,self.cp_date)
        self.data = get_yahoo_snp500(self.data,'ES=F','E_mini_snp_futures')
        self.data = get_LSTM_data(self.data,self.etf_name,self.cp_date)
        self.data=clean_etf_data(self.data,self.cp_date,self.etf_name)
        save_daily_data(self.data,self.etf_name)
        return self.data
