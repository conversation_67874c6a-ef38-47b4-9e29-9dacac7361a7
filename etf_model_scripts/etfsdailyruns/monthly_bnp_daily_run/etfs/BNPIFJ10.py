from base_etf import BaseETF
from helpers import *

class BNPIFJ10(BaseETF):
    def __init__(self, cp_date):
        super().__init__("BNPIFJ10", cp_date)
        
    def collect_data(self):
        """
        Collect etf specific data here.
        Returns a DataFrame.
        """
        super().collect_data()
        self.data['yearhigh'] = self.data['close_price'].rolling(window=252, min_periods=252).max()
        self.data['yearlow'] = self.data['close_price'].rolling(window=252, min_periods=252).min()

        self.data = get_volatility(self.data,252*5, 'volatility_5yr')
        self.data = get_volatility(self.data, 252*2, 'volatility_2yr')
        self.data = get_volatility(self.data, 252*1, 'volatility_1yr')

        self.data = get_japan_10Y_yield(self.data,self.etf_name)
        self.data = get_macro_data(self.data,self.cp_date,'Japan','Bond','JPN')
        self.data = get_bnp_technical_indicators(self.data,self.etf_name)
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"),'IQ2668711','IQ_CLOSEPRICE',self.data,cname='Nikkei_225_closeprice')
        self.data = get_all_freddata(self.data, self.etf_name)
        senti_dat = get_etf_KW_sentiment(self.cp_date,'JP_BNPIFJ10')
        senti_dat = macro_sentiment(senti_dat,self.cp_date,'japan_bond_macro')
        senti_dat = senti_dat[senti_dat.columns].rolling(window = 30).mean()
        self.data = self.data.join(senti_dat)

        self.data=get_momentum_data(self.data,self.etf_name,self.cp_date)
        self.data = get_LSTM_data(self.data,self.etf_name,self.cp_date)
        self.data=clean_etf_data(self.data,self.cp_date,self.etf_name)
        save_daily_data(self.data,self.etf_name)
        return self.data
