from base_etf import BaseETF
from helpers import *

class BNPIFU10(BaseETF):
    def __init__(self, cp_date):
        super().__init__("BNPIFU10", cp_date)
        
    def collect_data(self):
        """
        Collect etf specific data here.
        Returns a DataFrame.
        """
        api_url = config['Inhouse_API']['inhouse_api_url_by_isin']

        startDate = (pd.to_datetime(self.cp_date)-BDay(252*6)).strftime("%Y-%m-%d")
        stock_data = requests.get(api_url.format(identifier = self.etf_name, startdate = startDate, enddate = self.cp_date)).json()
        stock_data = pd.DataFrame(stock_data['data']['stocks'])[['date','adj_close']]
    
        stock_data.rename(columns={'adj_close':'close_price'}, inplace=True)
        stock_data=stock_data[['date','close_price']]
        stock_data['date']=pd.to_datetime(stock_data['date'])
        stock_data.sort_values('date', ascending =True , inplace=True)
        stock_data.reset_index(inplace=True, drop=True)
        stock_data['date'] = stock_data['date'].astype(str)
        stock_data.set_index('date',inplace=True)
        stock_data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"), 'IEF:NasdaqGM','IQ_CLOSEPRICE',stock_data,cname='IEF_close_price')
        # stock_data.reset_index(inplace=True, drop=True)

        stock_data['daily_close_change']=stock_data['IEF_close_price'].pct_change(periods=1)*100
        stock_data['weekly_close_change']=stock_data['IEF_close_price'].pct_change(periods=5)*100
        stock_data['monthly_close_change']=stock_data['IEF_close_price'].pct_change(periods=22)*100
        stock_data['yearly_close_change']=stock_data['IEF_close_price'].pct_change(periods=252)*100
        # stock_data['date'] = stock_data['date'].astype(str)
        # stock_data.set_index('date',inplace=True)
        stock_data['yearhigh'] = stock_data['close_price'].rolling(window=252, min_periods=252).max()
        stock_data['yearlow'] = stock_data['close_price'].rolling(window=252, min_periods=252).min()
        self.data = get_volatility(stock_data,22*3, 'volatility_3m')
        self.data = get_volatility(self.data, 22*6, 'volatility_6m')
        self.data = get_volatility(self.data,252*5, 'volatility_5yr')
        self.data = get_volatility(self.data, 252*2, 'volatility_2yr')
        self.data = get_volatility(self.data, 252*1, 'volatility_1yr')
        
        self.data = get_US_treasury(self.data,self.etf_name)
        self.data = get_AIGO_sentiment(self.data,'MQFIUSTY',self.cp_date)
        self.data = get_macro_data(self.data,self.cp_date,'Usa','Bond','USA')
        self.data = get_bnp_technical_indicators(self.data,self.etf_name)
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"),'IQ2667254','IQ_CLOSEPRICE',self.data,cname='DJI_closeprice')
        self.data = get_snp_data((pd.to_datetime(self.cp_date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(self.cp_date)).strftime("%m/%d/%Y"),'IQ2668699','IQ_CLOSEPRICE',self.data,cname='SPX_closeprice')
        self.data = get_all_freddata(self.data, self.etf_name)
        self.data = get_inflation_breakeven(self.data)
        self.data = get_inflation_expectation(self.data)
        self.data=get_momentum_data(self.data,self.etf_name,self.cp_date)
        self.data = get_LSTM_data(self.data,self.etf_name,self.cp_date)
        self.data=clean_etf_data(self.data,self.cp_date,self.etf_name)
        save_daily_data(self.data,self.etf_name)
        return self.data
