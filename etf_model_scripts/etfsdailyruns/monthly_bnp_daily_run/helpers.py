import sys
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from eq_common_utils.utils.metrics_helper import MetricsHelper
from opensearchpy.connection.http_requests import RequestsHttpConnection
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from opensearchpy.client import OpenSearch
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
import requests 
import pandas as pd
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
import talib
from functools import reduce
import json
from talib import abstract
from talib.abstract import *
from fredapi import Fred
import time
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor, as_completed
import ast
from multiprocessing.pool import ThreadPool
from concurrent.futures import ThreadPoolExecutor
import ibm_watsonx_ai
from ibm_watsonx_ai import APIClient
from ibm_watsonx_ai.deployment import Batch
import traceback

s3conn = s3_config()
ss = s3conn
es = es_config(env='prod')
preprod_es = es_config(env='pre')
metrics_obj = MetricsHelper(OpenSearch)


with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

wml_credentials = ibm_watsonx_ai.Credentials(
                   url = config['IBM_creds']['wml_credentials']['url'],
                   api_key = config['IBM_creds']['wml_credentials']['apikey']
                  )

schedular_dict = config['Metrics_data']['schedular_dict']
req_columns = config['Metrics_data']['req_columns']
all_metrics = config['Metrics_data']['all_metrics']
metrics_period = config['Metrics_data']['metrics_period']

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

try:
    fred = Fred(api_key='57b0fb46e088b7bfcdf4b85ce5606edb')
except Exception as e:
    print(e)


def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def trigger_and_save_technical_indicators(etfs,apiurl,patterns,bucket,path,ss):
    try:
        startDate = (datetime.today()-BDay(260*25)).strftime("%Y-%m-%d")
        endDate = (datetime.today()-BDay(1)).strftime("%Y-%m-%d")
        for etf in etfs:
            print(etf)
            bnp_json = requests.get(apiurl.format(identifier=etf,startdate=startDate,enddate=endDate)).json()
            bnp_daily_data = pd.DataFrame(bnp_json["data"]["stocks"])[['date','close']]
            bnp_daily_data['date'] = pd.to_datetime(bnp_daily_data['date'])
            bnp_daily_data.sort_values(by='date', inplace=True)
            bnp_daily_data.reset_index(drop=True,inplace=True)
            name=etf
            group=bnp_daily_data
            group.dropna(inplace=True)
            close = (group['close'].values).astype(float)
            for pattern in patterns:    
                try:
                    result = getattr(talib, pattern)(close) 
                    
                except Exception as e:
                    print(e)
                    
                try:
                    if len(result) >1 and len(result)<=3:
                        for i in range(len(result)):
                            group[pattern + "_"+str(i)] = result[i]
                    else:
                        group[pattern] = result
                except Exception as e:
                    print(e)
            group["tic"] = name
            group.bfill(inplace=True)
            #group.ffill(inplace=True)
            group.reset_index(inplace=True, drop=True)
            group.drop(columns=['close'], inplace=True)
            ss.write_advanced_as_df(group,bucket, path.format(etf=etf))
        print(f'Technical indicators trigger succesful')
    except Exception as e:
        print(e)
        

def get_es_data_list(es,index_prefix,isin_list,filedatestr_modified,year):
    # es_client = es.__connect_openSearch()
    data=[]
    q_total = {"size": len(isin_list), "query": {
            "bool": {"must": [{"terms": {"isin.keyword": isin_list }}, {"match": {"schedular": "Monthly"}},{"range": {"date": {"gte": filedatestr_modified, "lte": filedatestr_modified}}}]}},
                 "sort": [{"date": {"order": "desc"}}]}
    # try:
    result = es.client.search(index=index_prefix+"_"+year, body=q_total, size=10000,request_timeout=6000)
    for rs in result['hits']['hits']:
        es_data=rs['_source']
        data.append(es_data)
    # except:
    #     print(f'ES data for {index_prefix} not present for {year}.')
    df=pd.DataFrame(data)
    if len(df) == 0:
        return df
    df['date']=pd.to_datetime(df['date'])
    df.sort_values('date', ascending=True, inplace=True)
    df.reset_index(inplace=True, drop=True)
    return df

def trigger_and_save_rolledup_data(etf_list,ss,read_from_bucket, read_from_path,run_date,rolledup_upload_bucket, rolledup_etf_data_path, rolledup_raw_data_path):
    try:
        for etf_name in etf_list:
            print(etf_name)
            df_etf = ss.read_as_dataframe(read_from_bucket,read_from_path.format(etf=etf_name))
            li = df_etf['ISIN'].tolist()
            date_fin = (pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
            s_date = date_fin
            l_date = date_fin
        #     s_date = '2025-01-30'
        #     l_date = '2025-02-03'
            df_etf_n = df_etf[['ISIN','Weight']]
            df_etf_n.rename(columns={'Weight':'weight'},inplace=True)
            date_list = pd.date_range(start=s_date,end=l_date,freq='B').strftime("%Y-%m-%d").tolist()
            print(date_list)
            for date in date_list:
                da = date.split("-")
                year = da[0]
                try:
                    df_fin = get_es_data_list(es,'eq_financial_model',li,date,year)
                    df_fin = df_fin[['isin','pbv','price_sales','pe_excl','total_rev','predictions']]
                    df_fin.rename(columns={'predictions':'f_pred'},inplace=True)
                except Exception as e:  
                    df_fin = pd.DataFrame([],columns=['isin','pbv','price_sales','pe_excl','total_rev','f_pred'])
                try:    
                    df_man = get_es_data_list(es,'eq_management_model',li,date,year)
                    df_man = df_man[['isin','predictions']]
                    df_man['predictions'] = df_man['predictions'] * 100
                    df_man.rename(columns={'predictions':'m_pred'},inplace=True)
                except Exception as e:  
                    df_man = pd.DataFrame([],columns=['isin','m_pred'])  
                try:    
                    df_i = get_es_data_list(es,'eq_information_model',li,date,year)
                    df_i = df_i[['isin','predictions']]
                    df_i['predictions'] = df_i['predictions'] * 100
                    df_i.rename(columns={'predictions':'i_pred'},inplace=True)
                except Exception as e:  
                    df_i = pd.DataFrame([],columns=['isin','i_pred'])   
                try: 
                    df_er_new = get_es_data_list(es,'eq_er_model',li,date,year)
                    df_er_new = df_er_new[['isin','actual_monthly_return_predictions']]
                    df_er_new.rename(columns={'actual_monthly_return_predictions':'er_new'},inplace=True)
                except Exception as e:  
                    df_er_new = pd.DataFrame([],columns=['isin','er_new'])    

                try: 
                    df_cat = get_es_data_list(es,'eq_cat_er_model',li,date,year)
                    df_cat = df_cat[['isin','actual_monthly_return_predictions']]
                    df_cat.rename(columns={'actual_monthly_return_predictions':'er_cat'},inplace=True)
                except Exception as e:  
                    df_cat = pd.DataFrame([],columns=['isin','er_cat'])
                    
                try: 
                    df_cat_conf = get_es_data_list(es,'eq_cat_er_model_metrics',li,date,year)
                    df_cat_conf = df_cat_conf[['isin','avg_confidence_score']]
                    df_cat_conf.rename(columns={'avg_confidence_score':'cat_conf'},inplace=True)
                except Exception as e:  
                    df_cat_conf = pd.DataFrame([],columns=['isin','cat_conf'])
                df_final = df_etf_n
                df_final = df_final.rename(columns={'ISIN':'isin'})
                data_frames = [df_final,df_fin,df_man,df_i,df_er_new,df_cat,df_cat_conf]
                df_merged = reduce(lambda  left,right: pd.merge(left,right,on=['isin'],how='left'), data_frames)
                df_merged['weight'] = df_merged['weight'] / 100
                
                rolldetails = {'Equity' : [etf_name],
                                'pbv' : [0],'price_sales' : [0],'pe_excl' : [0],'total_rev' : [0],'f_score' : [0],'m_score' : [0],
                                'i_score' : [0],'er_old' : [0],'er_new' : [0],'er_cat' : [0],'cat_conf' : [0],'created' : [date]}
                etf_roll = pd.DataFrame(rolldetails)
                etf_roll['Equity'] = etf_name
                df_merged = df_merged.astype({'pbv': 'float64', 'weight': 'float64', 'price_sales': 'float64','pe_excl':'float64',
                                                'pbv': 'float64','total_rev': 'float64','f_pred': 'float64','m_pred': 'float64',
                                                'i_pred': 'float64','er_new': 'float64','er_cat': 'float64','cat_conf': 'float64'})
                etf_roll['pbv'] = (df_merged['pbv'] * df_merged["weight"]).sum()
                etf_roll['price_sales'] = (df_merged['price_sales'] * df_merged["weight"]).sum() 
                etf_roll['pe_excl'] = (df_merged['pe_excl'] * df_merged["weight"]).sum() 
                etf_roll['total_rev'] = (df_merged['total_rev'] * df_merged["weight"]).sum() 
                etf_roll['f_score'] = (df_merged['f_pred'] * df_merged["weight"]).sum() 
                etf_roll['m_score'] = (df_merged['m_pred'] * df_merged["weight"]).sum() 
                etf_roll['i_score'] = (df_merged['i_pred'] * df_merged["weight"]).sum() 
                # etf_roll['er_old'] = (df_merged['er_old'] * df_merged["weight"]).sum() 
                etf_roll['er_new'] = (df_merged['er_new'] * df_merged["weight"]).sum() 
                etf_roll['er_cat'] = (df_merged['er_cat'] * df_merged["weight"]).sum() 
                etf_roll['cat_conf'] = (df_merged['cat_conf'] * df_merged["weight"]).sum() 
                etf_roll['created'] = date 
                ss.write_advanced_as_df(etf_roll,rolledup_upload_bucket, rolledup_etf_data_path.format(etf=etf_name,date=date))
                ss.write_advanced_as_df(df_merged,rolledup_upload_bucket, rolledup_raw_data_path.format(etf=etf_name,date=date))
        print(f'Rolledup data trigger successful for run date :{run_date}')
    except Exception as e:
        print(e)

def get_instockapi_data (api_url, firm ,start_date, end_date):
    isin = firm
    stock_data = requests.get(api_url.format(identifier = isin, startdate = start_date, enddate = end_date)).json()
 
    stock_data = pd.DataFrame(stock_data['data']['stocks'])[['date','adj_close']]
 
    stock_data.rename(columns={'adj_close':'close_price'}, inplace=True)
    stock_data=stock_data[['date','close_price']]
    stock_data['date']=pd.to_datetime(stock_data['date'])
    stock_data.sort_values('date', ascending =True , inplace=True)
    stock_data.reset_index(inplace=True, drop=True)
 
    stock_data['monthly_close_change']=stock_data['close_price'].pct_change(periods=22)*100
    stock_data['actual_monthly_returns']=stock_data['monthly_close_change'].shift(-22)
    stock_data['date'] = stock_data['date'].astype(str)
    return stock_data

def get_beta_calculations(t_dat):
    import numpy as np
    import pandas as pd

    # Ensure the index is not datetime for now
    t_dat.reset_index(inplace=True)

    # Type conversions
    t_dat['close_price'] = pd.to_numeric(t_dat['close_price'], errors='coerce')
    # t_dat.rename(columns={'IQ_CLOSEPRICE': 'index_day_closeprice'}, inplace=True)
    t_dat['index_day_closeprice'] = pd.to_numeric(t_dat['index_day_closeprice'], errors='coerce')

    # Daily returns
    t_dat['X'] = t_dat['close_price'].pct_change()
    t_dat['Y'] = t_dat['index_day_closeprice'].pct_change()

    # Replace missing values with 0 (optional: safer might be dropna or fill only after return calc)
    t_dat.fillna(0, inplace=True)

    # Rolling windows (trading days)
    window_5y = 252 * 5
    window_2y = 252 * 2
    window_1y = 252

    t_dat['beta_5yr'] = np.nan
    t_dat['beta_2yr'] = np.nan
    t_dat['beta_1yr'] = np.nan

    def safe_polyfit(y, x):
        if np.std(x) < 1e-8 or np.std(y) < 1e-8:
            return np.nan
        try:
            return np.polyfit(y, x, 1)[0]
        except np.linalg.LinAlgError:
            return np.nan

    for i in range(window_5y, len(t_dat)):
        y = t_dat['Y'].values[i - window_5y:i]
        x = t_dat['X'].values[i - window_5y:i]
        t_dat.at[i, 'beta_5yr'] = safe_polyfit(y, x)

    for i in range(window_2y, len(t_dat)):
        y = t_dat['Y'].values[i - window_2y:i]
        x = t_dat['X'].values[i - window_2y:i]
        t_dat.at[i, 'beta_2yr'] = safe_polyfit(y, x)

    for i in range(window_1y, len(t_dat)):
        y = t_dat['Y'].values[i - window_1y:i]
        x = t_dat['X'].values[i - window_1y:i]
        t_dat.at[i, 'beta_1yr'] = safe_polyfit(y, x)

    # Drop helper columns
    t_dat.drop(columns=['X', 'Y'], inplace=True)

    # Set index back to date
    t_dat.set_index("date", inplace=True)

    return t_dat

def get_capiq_close(start_date,end_date, identifier,mnemonic): # Function to fetch adjusted closeprices from SnP (for a single isin in the year range)
        url = config['SNP_data']['url']
        headers = config['SNP_data']['headers']
        body = {
            "function": "GDST",
            "identifier": identifier,
            "mnemonic": mnemonic,
            "properties": {
                "self.currencyID":"USD",
                "startDate": start_date, # startdate
                "endDate" : end_date # enddate
            }
        }
        input_list = [body]
        data = {"inputRequests" : input_list}
        data_json = json.dumps(data)
        response = requests.post(url, data=data_json, headers=headers)
        
        try:
            resp_json=json.loads(response.text)
            return resp_json
        except Exception as e:
            print(e)

def get_snp_data(start_date, end_date, identifier,mnemonic,t_dat,cname=None):
    # try:
    single_date_response = get_capiq_close(start_date,end_date, identifier,mnemonic)
    # try:
    snp_closes = pd.DataFrame({'date': pd.to_datetime(single_date_response['GDSSDKResponse'][0]['Headers']), mnemonic: single_date_response['GDSSDKResponse'][0]['Rows'][0]['Row']})
    snp_closes[mnemonic] = pd.to_numeric(snp_closes[mnemonic], errors='coerce')

    snp_closes['date'] = snp_closes['date'].astype(str)
    snp_closes[mnemonic] = snp_closes[mnemonic].astype(float)
    snp_closes.set_index('date',inplace=True)
    t_dat.index = pd.to_datetime(t_dat.index).normalize()
    snp_closes.index = pd.to_datetime(snp_closes.index).normalize()
    t_dat = t_dat.join(snp_closes)
    # except:
    #     print(f"Couldn't fetch {mnemonic} from S&P API.")
    #     t_dat[mnemonic] = np.nan
    if cname!=None:
        t_dat.rename(columns={mnemonic: cname}, inplace=True)
    return t_dat

def get_etf_raw_data(etf,date):
    api_url=config['Inhouse_API']['inhouse_api_url_by_isin']
    startDate = (pd.to_datetime(date)-BDay(252*6)).strftime("%Y-%m-%d")
    stock_data = requests.get(api_url.format(identifier = etf, startdate = startDate, enddate = date)).json()
    stock_data = pd.DataFrame(stock_data['data']['stocks'])[['date','adj_close']]
 
    stock_data.rename(columns={'adj_close':'close_price'}, inplace=True)
    stock_data=stock_data[['date','close_price']]
    stock_data['date']=pd.to_datetime(stock_data['date'])
    stock_data.sort_values('date', ascending =True , inplace=True)
    stock_data.reset_index(inplace=True, drop=True)
 
    stock_data['daily_close_change']=stock_data['close_price'].pct_change(periods=1)*100
    stock_data['weekly_close_change']=stock_data['close_price'].pct_change(periods=5)*100
    stock_data['monthly_close_change']=stock_data['close_price'].pct_change(periods=22)*100
    stock_data['yearly_close_change']=stock_data['close_price'].pct_change(periods=252)*100
    stock_data['date'] = stock_data['date'].astype(str)
    stock_data.set_index('date',inplace=True)
    # snp_mnem_list = config['SNP_data']['snp_mnemonics_identifier_list'][etf][0]
    # if snp_mnem_list:
    #     snpidentifier = config['SNP_data']['snp_mnemonics_identifier_list'][etf][1]
    #     for mnem in snp_mnem_list:
    #         stock_data = get_snp_data((pd.to_datetime(date)-BDay(252*2)).strftime("%m/%d/%Y"),(pd.to_datetime(date)).strftime("%m/%d/%Y"), snpidentifier,mnem,stock_data)
    #     if 'IQ_VOLUME' in stock_data.columns:
    #         stock_data['daily_volume_change'] = stock_data['IQ_VOLUME'].pct_change(1)*100
    #         stock_data['weekly_volume_change'] = stock_data['IQ_VOLUME'].pct_change(5)*100
    #         stock_data['monthly_volume_change'] = stock_data['IQ_VOLUME'].pct_change(22)*100
    #         stock_data['yearly_volume_change'] = stock_data['IQ_VOLUME'].pct_change(252)*100
    
    # stock_data['yearhigh'] = stock_data['close_price'].rolling(window=252, min_periods=252).max()
    # stock_data['yearlow'] = stock_data['close_price'].rolling(window=252, min_periods=252).min()
    return stock_data
 
def get_volatility(t_dat,diff, col):
    df = t_dat[['close_price']]
    df.index = pd.to_datetime(df.index).normalize()
    t_dat.index = pd.to_datetime(t_dat.index).normalize()
    # Filter index to only business days (intersection with bdate_range)
    business_dates = pd.bdate_range(start=df.index.min(), end=df.index.max())
    df = df.loc[df.index.intersection(business_dates)].ffill()
    # Log return squared
    sq_ln_change = df.rolling(2).apply(lambda x: (np.log(x[1] / x[0])), raw=True) ** 2
    # Rolling window volatility
    volatility = sq_ln_change.rolling(diff).mean().apply(np.sqrt)
    # Merge back into original dataframe
    t_dat[col] = volatility

    return t_dat

def get_US_treasury(t_dat,tic):
    bucketName = "etf-predictions"
    filename = "Monthly/new_BNP_Paribas/daily_scraped_data/us_bond.csv"
    df = ss.read_advanced_as_df(bucketName,filename)[['Date','1Mo','2Mo','3Mo','4Mo','6Mo','1Yr','2Yr','3Yr','5Yr','7Yr','10Yr','20Yr','30Yr']]
    df['Date'] = pd.to_datetime(df['Date'].apply(str.strip))
    df.rename(columns={'Date':'date'},inplace=True)
    if tic == 'BNPIFU10':
        df = df[['date','5Yr','7Yr','10Yr','20Yr','30Yr']]
    df = convert_yearly_df(df)
    t_dat = t_dat.join(df)
    return t_dat

def get_monthly_to_daily_data(df):
    start_date = df.index.min() - pd.DateOffset(day=1)
    end_date = df.index.max() + pd.DateOffset(day=31)
    dates = pd.date_range(start_date, end_date, freq='D')
    dates.name = 'date'
    df = df.reindex(dates, method='ffill')
    
    return df


def get_gdp_forecasted_data(t_dat):
    df =pd.DataFrame(data = safe_fred_series('GDPPOT'))
    df.columns = ["gdp_forecasted"]
    df.dropna(how='all',inplace=True)
    df = get_monthly_to_daily_data(df)
    df['gdp_forecasted'] = df['gdp_forecasted'].shift(-252*2, axis = 0)
    t_dat = t_dat.join(df)
    return t_dat

def get_future_fedfun_rate(t_dat):
    df =pd.DataFrame(data = safe_fred_series('FEDTARMD'))
    df.columns = ["future_fedfund_rate"]
    df.dropna(how='all',inplace=True)
    df = get_monthly_to_daily_data(df)
    df['future_fedfund_rate'] = df['future_fedfund_rate'].shift(-252*2, axis = 0)
    t_dat = t_dat.join(df)
    return t_dat

def get_json_data_from_s3(bucket_name, object_key): # object key of the file to read
    s3 = boto3.client('s3', aws_access_key_id=ss._key_id, aws_secret_access_key=ss._secret)
    json_obj = s3.get_object(Bucket=bucket_name, Key=object_key)
    body = json_obj['Body']
    json_string=body.read().decode('utf-8')
    json_data =json.loads(json_string)
    return json_data


def get_AIGO_sentiment(t_dat,etf,latest_date):
    fetched=[]
    for dt in pd.bdate_range(pd.to_datetime(latest_date)-BDay(22), pd.to_datetime(latest_date)):
        dt = pd.to_datetime(dt).strftime('%Y-%m-%d')
        #print(dt)
        try:
            sentiment_json=get_json_data_from_s3(config['S3_paths']['aigo_sentiment_monthly_bucket'],config['S3_paths']['aigo_sentiment_monthly_path'].format(date=dt))
            for item in sentiment_json:
                if item['etf']==etf:
                    item['date']= dt
                    fetched.append(item)
        except:
            continue
    fetched_df=pd.DataFrame(fetched)
    if 'macro_equity_kw_pos' in fetched_df.columns:
        fetched_df.rename(columns={'macro_equity_kw_pos':'macro_kw_pos', 'macro_equity_kw_neg':'macro_kw_neg'}, inplace=True)
    fetched_df['date'] = pd.to_datetime(fetched_df['date'])
    fetched_df.sort_values('date', ascending=True, inplace=True)
    fetched_df.drop_duplicates(subset=['date'], keep='first', inplace=True)
    fetched_df.reset_index(inplace=True, drop=True)
    fetched_df.set_index('date',inplace=True)
    fetched_df.drop(columns=['etf'],inplace=True)
    ##############################################################
    fetched_df['common_kw_pos'] = np.nan
    fetched_df['common_kw_neg'] = np.nan
    ##############################################################
    t_dat = t_dat.join(fetched_df)
    return t_dat

def get_macro_data(t_dat,date,geo,asset_type,c_code):
    cons_macro_df = pd.DataFrame()
    for dt in pd.bdate_range(pd.to_datetime(date)-BDay(22), pd.to_datetime(date)):
        dt = pd.to_datetime(dt).strftime('%Y-%m-%d')
        try:
            df = ss.read_advanced_as_df(config['S3_paths']['macro_data_bucket'],config['S3_paths']['macro_data_path'].format(geo=geo,asset_type=asset_type,date=dt))[['date','predictions','isin']]
            df.rename(columns={'predictions':'macro_pred'},inplace=True)
            df = df[df['isin']==f'{c_code}_{asset_type}']
            df.drop(columns=['isin'],inplace=True)
            df['date'] = pd.to_datetime(df['date'].apply(str.strip))
            df = df.set_index('date')
            cons_macro_df = pd.concat([cons_macro_df, df])
        except:
            continue
    t_dat = t_dat.join(cons_macro_df)
    return t_dat

def get_aigo_technical_indicators(t_dat,etf):
    dff = ss.read_advanced_as_df(config['S3_paths']['aigo_technical_indicators_bucket'], config['S3_paths']['aigo_technical_indicators_path'])[['date', 'ADX', 'APO', 'CCI', 'DX', 'MFI', 'MOM', 'PPO', 'ULTOSC',
       'WILLR', 'MACD_0', 'MACD_1', 'MACD_2', 'MACDFIX_0', 'MACDFIX_1',
       'MACDFIX_2', 'STOCH_0', 'STOCH_1', 'STOCHF_0', 'STOCHF_1', 'STOCHRSI_0',
       'STOCHRSI_1','tic']]
    curr_tic = dff.groupby('tic')
    df = curr_tic.get_group(etf) 
    df['date'] = pd.to_datetime(df['date'].apply(str.strip))
    df = df.set_index('date')
    df = df.drop(['tic'], axis=1)
    t_dat = t_dat.join(df)
    return t_dat

def get_bnp_technical_indicators(t_dat,ticker):
    df = ss.read_advanced_as_df(config['S3_paths']['bnp_technical_indicators_bucket'], config['S3_paths']['bnp_technical_indicators_path'].format(etf=ticker))
    df['date'] = pd.to_datetime(df['date'].apply(str.strip))
    df = df.set_index('date')
    df = df.drop(['tic','index'], axis=1)
    t_dat = t_dat.join(df)
    return t_dat

def get_all_freddata(t_dat,etf):
    cons_fred_data = pd.DataFrame()
    
    for fred_mnem in config['misc']['fred_mnemonic_list'][etf]:
        # try:
        df = pd.DataFrame(data=safe_fred_series(fred_mnem))
        df.reset_index(inplace=True)
        df.columns = ['date', fred_mnem]
        df['date'] = pd.to_datetime(df['date'].astype(str).str.strip())
        df.sort_values('date', inplace=True)
        df.fillna(method='ffill', inplace=True)
        
        if cons_fred_data.empty:
            cons_fred_data = df
        else:
            cons_fred_data = pd.merge(cons_fred_data, df, on='date', how='outer')
        # except:
        #     cons_fred_data[fred_mnem] = np.nan
    
    cons_fred_data.sort_values('date', inplace=True)
    cons_fred_data.fillna(method='ffill', inplace=True)
    cons_fred_data = convert_yearly_df(cons_fred_data)
    # cons_fred_data.set_index('date', inplace=True)
    t_dat = t_dat.join(cons_fred_data)
    return t_dat

def get_momentum_data(t_dat,curr_etf,cdate):
    try:
        year=cdate.split('-')[0]
        q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"Tic":"'+curr_etf+'"}}]}}]}}}'
        result = preprod_es.client.search(index=f"eq_momentum_{year}", body=q_total, size=10000,request_timeout=6000)
        mom_data=result['hits']['hits']
        es_mom_dat = pd.DataFrame.from_dict([x['_source'] for x in mom_data])[['date','MP_1D', 'MP_1W','MP_1M','MP_1Q','MP_6M','MP_1Y']]
        es_mom_dat.date = pd.to_datetime(es_mom_dat.date, format='%Y-%m-%d')
        es_mom_dat.sort_values(by="date", inplace=True)
        es_mom_dat = convert_yearly_df(es_mom_dat)
        # es_mom_dat = es_mom_dat.set_index('date')
        #es_mom_dat = get_monthly_to_daily_data(es_mom_dat)
        t_dat = t_dat.join(es_mom_dat)
    except:
        t_dat['MP_1D'] = np.nan
        t_dat['MP_1W'] = np.nan
        t_dat['MP_1M'] = np.nan
        t_dat['MP_1Q'] = np.nan
        t_dat['MP_6M'] = np.nan
        t_dat['MP_1Y'] = np.nan
    return t_dat

def get_yahoo_snp500(t_dat,mnem,cname):
    df = yf.download(mnem, auto_adjust = False)[['Close','Volume']]
    #df.reset_index(inplace=True)
    #df.rename(columns={'Date':'date'}, inplace=True)
    df.rename(columns = {'Close': f'{cname}_Closeprice'}, inplace=True)
    df.rename(columns = {'Volume': f'{cname}_Volume'} ,inplace=True)
    df.columns=[f'{col1}' for col1,col2 in df.columns]
    #df['Date']=pd.to_datetime(df['Date'])
    t_dat = t_dat.join(df)
    return t_dat

def get_LSTM_data(t_dat,tic,date):
    year=date.split('-')[0]
    combined_df  = ss.read_advanced_as_df('historical-prediction-data-15yrs','lstm/bnpetf/predictions/7_Day/predictions_2005_2024.csv')[['date','tic','predictions']]
    keys = ss.get_objects_in_range('historical-prediction-data-15yrs',f'lstm/bnpetf/predictions/7_Day/{year}/')
    last_date = combined_df['date'].values[-1]
    for key in keys[-30:]:
#         if key.split('/')[-1].split('.')[0][17:].replace('_','-') >last_date:
        df=ss.read_advanced_as_df('historical-prediction-data-15yrs',key['Key'])[['date','tic','predictions']]
        combined_df = pd.concat([combined_df,df])
    combined_df['date'] = pd.to_datetime(combined_df['date'].apply(str.strip))
    #combined_df=combined_df.drop_duplicates(subset='date', keep="last")
    combined_df = combined_df.set_index('date')
    combined_df=combined_df.sort_values('date', ascending=True)
    gk = combined_df.groupby('tic')
    unique_isin_df = gk.first()
    unique_isin_df['tic'] = unique_isin_df.index
    lstm_dat = gk.get_group(tic)
    lstm_dat.reset_index(inplace=True)
    lstm_dat=lstm_dat.drop_duplicates(subset='date', keep="last")
    lstm_dat['predictions'].ffill(inplace=True)
    lstm_dat[['lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7']] = lstm_dat['predictions'].str.split(',',expand=True)
    lstm_dat['lstm_day_1'] = lstm_dat['lstm_day_1'].apply(lambda x:x.split('[')[1])
    lstm_dat['lstm_day_7'] = lstm_dat['lstm_day_7'].apply(lambda x:x.split(']')[0])
#     lstm_dat['predictions'] = pd.to_numeric(lstm_dat['predictions'], errors='coerce')
    lstm_dat['lstm_day_1'] = pd.to_numeric(lstm_dat['lstm_day_1'], errors='coerce')
    lstm_dat['lstm_day_2'] = pd.to_numeric(lstm_dat['lstm_day_2'], errors='coerce')
    lstm_dat['lstm_day_3'] = pd.to_numeric(lstm_dat['lstm_day_3'], errors='coerce')
    lstm_dat['lstm_day_4'] = pd.to_numeric(lstm_dat['lstm_day_4'], errors='coerce')
    lstm_dat['lstm_day_5'] = pd.to_numeric(lstm_dat['lstm_day_5'], errors='coerce')
    lstm_dat['lstm_day_6'] = pd.to_numeric(lstm_dat['lstm_day_6'], errors='coerce')
    lstm_dat['lstm_day_7'] = pd.to_numeric(lstm_dat['lstm_day_7'], errors='coerce')
    lstm_dat=lstm_dat[['date','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7']]
    lstm_dat = lstm_dat.set_index('date')
    t_dat = t_dat.join(lstm_dat)
    return t_dat

def get_rolledup_data(t_dat,tic,date):
    try:
        cons_rollup_df = pd.DataFrame()
        for dt in pd.bdate_range(pd.to_datetime(date)-BDay(22), pd.to_datetime(date)):
            dt = pd.to_datetime(dt).strftime('%Y-%m-%d')
            try:
                df = ss.read_advanced_as_df(config['S3_paths']['rolledup_data_bucket'],config['S3_paths']['rolledup_data_path'].format(etf=tic,date=dt))
                if 'index' in df.columns:
                    df.drop(columns=['index'],inplace=True)
                df.drop(columns=['er_old'],inplace=True)
                try:
                    df.drop(columns=['Equity','er_cat','cat_conf'],inplace=True)
                except:
                    df.drop(columns=['Equity'],inplace=True)
                df.rename(columns={'er_new':'ERscore'},inplace=True)
                df = df.rename(columns={'created':'date'})
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date')
                cons_rollup_df = pd.concat([cons_rollup_df,df])
            except:
                continue
        t_dat = t_dat.join(cons_rollup_df)
    except:
        t_dat['pbv'] = np.nan
        t_dat['price_sales'] = np.nan
        t_dat['pe_excl'] = np.nan
        t_dat['total_rev'] = np.nan
        t_dat['f_score'] = np.nan
        t_dat['m_score'] = np.nan
        t_dat['i_score'] = np.nan
        t_dat['er_new'] = np.nan
    return t_dat

def get_china_treasury(t_dat):
    bucketName = "etf-predictions"
    filename = "Monthly/new_BNP_Paribas/daily_scraped_data/china_bond.csv"
    df = ss.read_advanced_as_df(bucketName,filename)
    df = df.drop(columns=['index'])
    df_group = df.groupby('Yield Curve Name')
    
    df_1 = df_group.get_group('ChinaBond Government Bond Yield Curve')
    df_1.drop(columns=['Yield Curve Name'],inplace=True)
    df_1['Date'] = pd.to_datetime(df_1['Date'].apply(str.strip))
    df_1.rename(columns = {'Date':'date'},inplace=True)
    df_1.set_index('date', inplace=True)
    # df_1 = convert_yearly_df(df_1)
    c_name = 'ChinaBond Government Bond Yield Curve'
    df_1.columns = [f'{c_name}_3M',f'{c_name}_6M',f'{c_name}_1Y',f'{c_name}_3Y',f'{c_name}_5Y',f'{c_name}_7Y',f'{c_name}_10Y',f'{c_name}_30Y']
    
    t_dat = t_dat.join(df_1)
    
    df_2 = df_group.get_group('ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)')
    df_2.drop(columns=['Yield Curve Name'],inplace=True)
    df_2['Date'] = pd.to_datetime(df_2['Date'].apply(str.strip))
    df_2.rename(columns = {'Date':'date'},inplace=True)
    df_2.set_index('date', inplace=True)
    # df_2 = convert_yearly_df(df_2)
    c_name = 'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)'
    df_2.columns = [f'{c_name}_3M',f'{c_name}_6M',f'{c_name}_1Y',f'{c_name}_3Y',f'{c_name}_5Y',f'{c_name}_7Y',f'{c_name}_10Y',f'{c_name}_30Y']
    
    t_dat = t_dat.join(df_2)
    
    df_3 = df_group.get_group('ChinaBond CP&Note Yield Curve (AAA)')
    df_3.drop(columns=['Yield Curve Name'],inplace=True)
    df_3['Date'] = pd.to_datetime(df_3['Date'].apply(str.strip))
    df_3.rename(columns = {'Date':'date'},inplace=True)
    df_3.set_index('date', inplace=True)
    # df_3 = convert_yearly_df(df_3)
    c_name = 'ChinaBond CP&Note Yield Curve (AAA)'
    df_3.columns = [f'{c_name}_3M',f'{c_name}_6M',f'{c_name}_1Y',f'{c_name}_3Y',f'{c_name}_5Y',f'{c_name}_7Y',f'{c_name}_10Y',f'{c_name}_30Y']
    t_dat = t_dat.join(df_3)
    return t_dat

def get_europe_treasury(t_dat,tic):
    bucketName = "etf-predictions"
    filename = "Monthly/new_BNP_Paribas/daily_scraped_data/europe_bond.csv"
    df = ss.read_advanced_as_df(bucketName,filename)
    df['Date'] = pd.to_datetime(df['Date'].apply(str.strip))
    if 'index' in df.columns:
        df.drop(columns=['index'], inplace=True)
    df.rename(columns={'Date':'date'},inplace=True)
    if tic == 'BNPIFE10':
        df = df[['date','10Y Treasury Yield']]
    df = convert_yearly_df(df)
    t_dat = t_dat.join(df)
    return t_dat

def get_japan_10Y_yield(t_dat,tic):
    bucketName = "etf-predictions"
    filename = "Monthly/new_BNP_Paribas/daily_scraped_data/japan_bond.csv"
    df = ss.read_advanced_as_df(bucketName,filename)
    df.columns = df.iloc[0]
    df=df.iloc[1:]
    df = df[['Date',   '1Y',   '2Y',   '3Y',   '4Y',   '5Y',   '6Y',   '7Y',
         '8Y',   '9Y',  '10Y',  '15Y',  '20Y',  '25Y',  '30Y',  '40Y']]
    if tic=='BNPIFJ10':
          df =df[['Date',  '10Y']]
    df.rename(columns={'Date':'date'},inplace=True)
    df['date'] = pd.to_datetime(df['date'], format='%Y/%m/%d').dt.normalize()
    df = convert_yearly_df(df)
    t_dat = t_dat.join(df)
    return t_dat

def get_etf_KW_sentiment(date,ticker):
    df = ss.read_advanced_as_df(config['S3_paths']['etf_KW_sentiment_bucket'],config['S3_paths']['etf_KW_sentiment_path'].format(ticker=ticker,date=date))
    df.rename(columns = {'pos_sentiment':'etf_KW_pos','neg_sentiment':'etf_KW_neg'}, inplace = True)
    df = df[['date','etf_KW_pos','etf_KW_neg']]
    df['date'].values[-1] = (pd.to_datetime(date))
    df['date']=pd.to_datetime(df['date'])
    df = df.set_index('date')
#     t_dat = t_dat.join(df)
    return df

def macro_sentiment(senti_df,date,filename):
    df = ss.read_advanced_as_df(config['S3_paths']['macro_sentiment_bucket'],config['S3_paths']['macro_sentiment_path'].format(filename=filename,date=date))
    df.rename(columns = {'pos_sentiment':'macro_pos','neg_sentiment':'macro_neg'}, inplace = True)
    df = df[['date','macro_pos','macro_neg']]
    df['date'].values[-1] = (pd.to_datetime(date))
    df['date']=pd.to_datetime(df['date'])
    df = df.set_index('date')
    df = senti_df.join(df)
    
    #df = df[df.columns].rolling(window = 30).mean()
    return df

def get_industry_data(senti_df,date,ticker):
    industry_list = config['misc']['etf_industry_mappings'][ticker]
    cons_ind_df = pd.DataFrame()
    for industry in industry_list:
        df = ss.read_advanced_as_df(config['S3_paths']['industry_sentiment_bucket'],config['S3_paths']['industry_sentiment_path'].format(industry=industry,date=date))

        # df['date'] = pd.to_datetime(df['date'].apply(lambda k:'-'.join(k.split('-')[::-1])))
        df.rename(columns = {'pos_sentiment':industry+"_pos",'neg_sentiment':industry+"_neg"}, inplace = True)
        df = df[['date',industry+"_pos",industry+"_neg"]]
        df['date'].values[-1] = (pd.to_datetime(date))
        df['date']=pd.to_datetime(df['date'])
        df = df.set_index('date')
        cons_ind_df = cons_ind_df.join(df, how='outer') if not cons_ind_df.empty else df
    cons_ind_df = senti_df.join(cons_ind_df)            
    return cons_ind_df

def get_common_keywords(senti_df,date,filename):
    df = ss.read_advanced_as_df(config['S3_paths']['common_keywords_bucket'],config['S3_paths']['common_keywords_path'].format(filename=filename,date=date))
    df.rename(columns = {'pos_sentiment':'common_kw_pos','neg_sentiment':'common_kw_neg'}, inplace = True)
    df = df[['date','common_kw_pos','common_kw_neg']]
    df['date'].values[-1] = (pd.to_datetime(date))
    df['date']=pd.to_datetime(df['date'])
    df = df.set_index('date')
    df = senti_df.join(df)
    return df

def get_holdings_data(senti_df,date,filename):
    df = ss.read_advanced_as_df(config['S3_paths']['holdings_sentiment_bucket'],config['S3_paths']['holdings_sentiment_path'].format(filename=filename,date=date))
    # df['date'] = pd.to_datetime(df['date'].apply(lambda k:'-'.join(k.split('-')[::-1])))
    df.rename(columns = {'pos_sentiment':'holdings_pos','neg_sentiment':'holdings_neg'}, inplace = True)
    df = df[['date','holdings_pos','holdings_neg']]
    df['date'].values[-1] = (pd.to_datetime(date))
    df['date']=pd.to_datetime(df['date'])
    df = df.set_index('date')
    df = senti_df.join(df)
    #df = df[df.columns].rolling(window = 30).mean()
    return df

def get_yield_maturity(t_dat):
    df =pd.DataFrame(data = safe_fred_series('BAA10Y'))
    #df.reset_index(inplace=True)
    df.columns = ["yield_maturity"]
    df.dropna(how='all',inplace=True)
    #df.date = pd.to_datetime(cpi.date, format='%d-%m-%Y')
    #df.sort_values(by="date", inplace=True)
    df = get_monthly_to_daily_data(df)
    t_dat = t_dat.join(df)
    return t_dat

def convert_yearly_df(yearly_df):
    yearly_df.rename(columns={'Year':'date'}, inplace=True)
    yearly_df.date = pd.to_datetime(yearly_df.date, format="%Y")
    yearly_df.set_index('date', inplace=True)
    date_range = pd.date_range(start=yearly_df.index.min(), end=pd.Timestamp.today().normalize(), freq='D')

    daily_df = yearly_df.reindex(date_range).ffill()
    daily_df.index.name = 'date'
    return daily_df


def get_LT_borrowing_cost(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['lt_borrowing_cost_bucket'],config['S3_paths']['lt_borrowing_cost_path'])[['DATE','Bank interest rates - loans to households for consumption (new business) - euro area (MIR.M.U2.B.A2B.A.R.A.2250.EUR.N)']]
    df.rename(columns = {'DATE':'Date','Bank interest rates - loans to households for consumption (new business) - euro area (MIR.M.U2.B.A2B.A.R.A.2250.EUR.N)':'LT_borrowing_cost'}, inplace = True)
    df['Date'] = pd.to_datetime(df['Date'].apply(str.strip))
    df = df.set_index('Date')
    df.sort_values(by="Date", inplace=True)
    df = get_monthly_to_daily_data(df)
    t_dat = t_dat.join(df)
    return t_dat

def get_inflation_breakeven(t_dat):
    df =pd.DataFrame(data = safe_fred_series('T5YIE'))
    #df.reset_index(inplace=True)
    df.columns = ["get_inflation_breakeven"]
    df.dropna(how='all',inplace=True)
    #df.date = pd.to_datetime(cpi.date, format='%d-%m-%Y')
    #df.sort_values(by="date", inplace=True)
    df = get_monthly_to_daily_data(df)
    t_dat = t_dat.join(df)
    return t_dat

def get_inflation_expectation(t_dat):
    df =pd.DataFrame(data = safe_fred_series('T5YIFR'))
    #df.reset_index(inplace=True)
    df.columns = ["get_inflation_expectation"]
    df.dropna(how='all',inplace=True)
    #df.date = pd.to_datetime(cpi.date, format='%d-%m-%Y')
    #df.sort_values(by="date", inplace=True)
    df = get_monthly_to_daily_data(df)
    t_dat = t_dat.join(df)
    return t_dat

def get_snp_gold(t_dat):
    df = yf.download('GC=F')[['Close']]
    #df.reset_index(inplace=True)
    #df.rename(columns={'Date':'date'}, inplace=True)
    df.rename(columns = {'Close': 'Gold_Closeprice'}, inplace=True)
    df.columns=[col1 for col1,col2 in df.columns]
    #df.rename(columns = {'Volume': 'E_mini_snp_futures_Volume'} ,inplace=True)
    #df['Date']=pd.to_datetime(df['Date'])
    t_dat = t_dat.join(df)
    return t_dat


def get_snp_aluminium(t_dat):
    df = yf.download('ALI=F')[['Close']]
    #df.reset_index(inplace=True)
    #df.rename(columns={'Date':'date'}, inplace=True)
    df.rename(columns = {'Close': 'Aluminium_Closeprice'}, inplace=True)
    df.columns=[col1 for col1,col2 in df.columns]
    #df.rename(columns = {'Volume': 'E_mini_snp_futures_Volume'} ,inplace=True)
    #df['Date']=pd.to_datetime(df['Date'])
    t_dat = t_dat.join(df)
    return t_dat    

def get_snp_silver(t_dat):
    df = yf.download('SI=F')[['Close']]
    #df.reset_index(inplace=True)
    #df.rename(columns={'Date':'date'}, inplace=True)
    df.rename(columns = {'Close': 'Silver_Closeprice'}, inplace=True)
    df.columns=[col1 for col1,col2 in df.columns]
    #df.rename(columns = {'Volume': 'E_mini_snp_futures_Volume'} ,inplace=True)
    #df['Date']=pd.to_datetime(df['Date'])
    t_dat = t_dat.join(df)
    return t_dat    

def get_snp_copper(t_dat):
    df = yf.download('HG=F')[['Close']]
    #df.reset_index(inplace=True)
    #df.rename(columns={'Date':'date'}, inplace=True)
    df.rename(columns = {'Close': 'Copper_Closeprice'}, inplace=True)
    df.columns=[col1 for col1,col2 in df.columns]
    #df.rename(columns = {'Volume': 'E_mini_snp_futures_Volume'} ,inplace=True)
    #df['Date']=pd.to_datetime(df['Date'])
    t_dat = t_dat.join(df)
    return t_dat    

def get_gold(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['gold_bucket'],config['S3_paths']['gold_path'])
    df = convert_yearly_df(df)
    df = df.iloc[-250:]
    if 'index' in df.columns:
        df = df.drop(columns=['index'])
    c = []
    for i in df.columns:
        i = i+'_gold'
        c.append(i)
    df.columns = c
    t_dat = t_dat.join(df)
    return t_dat

def get_aluminium(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['aluminium_bucket'],config['S3_paths']['aluminium_path'])
    df = convert_yearly_df(df)
    df = df.iloc[-250:]
    if 'index' in df.columns:
        df = df.drop(columns=['index'])
    c = []
    for i in df.columns:
        i = i+'_aluminium'
        c.append(i)
    df.columns = c
    t_dat = t_dat.join(df)
    return t_dat

def get_silver(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['silver_bucket'],config['S3_paths']['silver_path'])
    df = convert_yearly_df(df)
    df = df.iloc[-250:]
    if 'index' in df.columns:
        df = df.drop(columns=['index'])
    c = []
    for i in df.columns:
        i = i+'_silver'
        c.append(i)
    df.columns = c
    t_dat = t_dat.join(df)
    return t_dat

def get_copper(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['copper_bucket'],config['S3_paths']['copper_path'])
    df = convert_yearly_df(df)
    df = df.iloc[-250:]
    if 'index' in df.columns:
        df = df.drop(columns=['index'])
    c = []
    for i in df.columns:
        i = i+'_copper'
        c.append(i)
    df.columns = c
    t_dat = t_dat.join(df)
    return t_dat

def get_nickel(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['nickel_bucket'],config['S3_paths']['nickel_path'])
    df = convert_yearly_df(df)
    df = df.iloc[-250:]
    if 'index' in df.columns:
        df = df.drop(columns=['index'])
    if 'Production: Secondary' in df.columns:
        df = df.drop(columns=['Production: Secondary'])
    if 'Consumption, Apparent, total' in df.columns:
        df = df.drop(columns=['Consumption, Apparent, total'])
    c = []
    for i in df.columns:
        i = i+'_nickel'
        c.append(i)
    df.columns = c
    t_dat = t_dat.join(df)
    return t_dat

def get_zinc(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['zinc_bucket'],config['S3_paths']['zinc_path'])
    df = convert_yearly_df(df)
    df = df.iloc[-250:]
    if 'index' in df.columns:
        df = df.drop(columns=['index'])
    if 'Production: Refined zinc' in df.columns:
        df = df.drop(columns=['Production: Refined zinc'])

    c = []
    for i in df.columns:
        i = i+'_zinc'
        c.append(i)
    df.columns = c
    t_dat = t_dat.join(df)
    return t_dat

def get_lead(t_dat):
    df = ss.read_advanced_as_df(config['S3_paths']['lead_bucket'],config['S3_paths']['lead_path'])
    df = convert_yearly_df(df)
    df = df.iloc[-250:]
    if 'index' in df.columns:
        df = df.drop(columns=['index'])
    c = []
    for i in df.columns:
        i = i+'_lead'
        c.append(i)
    df.columns = c
    t_dat = t_dat.join(df)
    return t_dat

def get_self_and_corr_predictions(t_dat):
    t_dat['self_predictions'] = np.nan
    t_dat['corr_predictions'] = np.nan
    return t_dat

def clean_etf_data(df, date,tic):
    df_cleaned = df.copy()

    for col in df_cleaned.columns:
        df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')
        df_cleaned[col] = df_cleaned[col].astype(float)

    df_cleaned.replace([np.inf, -np.inf], np.nan, inplace=True)
    if df_cleaned.index.name != 'date':
        df_cleaned.index.name = 'date'
    df_cleaned.reset_index(inplace=True)
    df_cleaned = df_cleaned[df_cleaned['date'] == pd.to_datetime(date).normalize()]
    columns = config['misc']['etf_column_mappings'][tic]
    df_cleaned.columns = columns
    if 'index_day_closeprice' in df_cleaned.columns:
        df_cleaned.drop(columns=['index_day_closeprice'], inplace=True)


    return df_cleaned

def save_daily_data(df,tic):
    if len(df) > 0:
        bucketName = config['S3_paths']['upload_input_data_bucket']
        filename = config['S3_paths']['upload_input_data_path'].format(etf=tic)
        df['date'] = df['date'].astype(str)
        try:
            cons_df = ss.read_advanced_as_df(bucketName, filename)
            cons_df['date'] = cons_df['date'].astype(str)
            cons_df = pd.concat([cons_df, df])
            cons_df = cons_df.sort_values('date').drop_duplicates('date', keep='last')

        except Exception as e:
            print(e)
            cons_df = df.copy()
        if 'index' in cons_df.columns:
            cons_df = cons_df.drop(columns=['index'])
        cons_df = cons_df.sort_values('date').reset_index(drop=True)
        ss.write_advanced_as_df(cons_df,bucketName, filename)

def safe_fred_series(series_id, observation_start=None, observation_end=None, retries=5, delay=2):
    for attempt in range(retries):
        try:
            return fred.get_series(series_id, observation_start=observation_start, observation_end=observation_end)
        except ValueError as e:
            if "Too Many Requests" in str(e):
                print(f"[Retry] FRED rate limit hit for {series_id}, attempt {attempt + 1}/{retries}. Retrying...")
                time.sleep(delay * (attempt + 1))  # Exponential backoff
            else:
                print(f"[Error] FRED fetch failed for {series_id}: {e}")
                break
        except Exception as e:
            print(f"[Error] Unexpected error for {series_id}: {e}")
            break
    print(f"[Fail] All retries failed for {series_id}. Returning empty series.")
    return pd.Series(dtype='float64')

def patch_https_connection_pool(**constructor_kwargs):
    """
    This allows to override the default parameters of the
    HTTPConnectionPool constructor.
    For example, to increase the poolsize to fix problems
    with "HttpSConnectionPool is full, discarding connection"
    call this function with maxsize=16 (or whatever size
    you want to give to the connection pool)
    """
    from urllib3 import connectionpool, poolmanager
    class MyHTTPSConnectionPool(connectionpool.HTTPSConnectionPool):
        def __init__(self, *args,**kwargs):
            kwargs.update(constructor_kwargs)
            super(MyHTTPSConnectionPool, self).__init__(*args,**kwargs)
    poolmanager.pool_classes_by_scheme['https'] = MyHTTPSConnectionPool
patch_https_connection_pool(maxsize=100)


class predictionHelper():
    def __init__(self, prediction_batch_size = 120):
        self.data_store_dict = {}
        self.batch_run_size = prediction_batch_size

    def del_jobs(self, job_id, wmlclient):
        try:
            status = wmlclient.deployments.delete_job(job_id, hard_delete=True)
            return f'{job_id}:{status}'
        except Exception as e:
            print(f'job:{job_id}:{str(e)}')


    def create_deployment_jobs(self, deployment_id, year, input_location, wmlclient, service):
        global job_count 
        job_count += 1
        try:
            actuals = []
            if type(input_location) == str or input_location is None:
                if input_location is None or input_location.strip() == '':
                    print(f"Input data not available for: {deployment_id}")
                    raise Exception("Input data not available")
                else:
                    try:
                        if input_location in self.data_store_dict.keys():
                            test_data = self.data_store_dict[input_location]
                        else:
                            test_data = read_data(input_location)
                            self.data_store_dict[input_location] = test_data
                        #test_data.rename(columns={'date':'Date'},inplace=True)
                        test_data['date'] = pd.to_datetime(test_data['date'])
                    except Exception as e:
                        print(f"Input data not available or unable to read for: {deployment_id}")
                        print(e)
                        raise Exception("Input data not available or unable to read")
                    test_data = test_data[(test_data['date'] >= pd.to_datetime(f'01-01-{int(year) + 1}')) & (test_data['date'] <= pd.to_datetime(f'31-12-{int(year) + 1}'))].reset_index(drop = True)
            else:
                test_data = input_location
            test_data['date'] = test_data['date'].astype(str)
            service.get(deployment_id)
            model_id = wmlclient.deployments.get_details(deployment_uid=deployment_id)['entity']['asset']['id']
            input_schema = wmlclient.repository.get_model_details(model_id)['entity']['schemas']['input'][0]['fields']   
            input_columns = list(pd.DataFrame(input_schema)['name'].values)
            test_data = test_data[input_columns]
            test_data = test_data.replace(np.inf, np.nan).replace(-np.inf, np.nan).ffill().fillna(0)
            job_payload_ref = {wmlclient.deployments.ScoringMetaNames.INPUT_DATA: [{'fields': [], 'values': test_data.values.tolist()}]}
            job = wmlclient.deployments.create_job(deployment_id, meta_props=job_payload_ref) # Creates the scoring/prediction job
            job_id = wmlclient.deployments.get_job_uid(job)
            print(f'deployment id: {deployment_id} job id: {job_id}.')
            return [deployment_id, job_id]
        except Exception as e:
            print(f'for deployment_id: {deployment_id}, error is {e}')
            return [deployment_id, '']


    def get_state(self, job_id, wmlclient):
        status = ''
        values=[]
        try:
            # Get only the status and predictions output from get_job_details
            job_details = wmlclient.deployments.get_job_details(job_id)#, include='predictions,status')
            status =  job_details['entity']['scoring']['status']['state']
            if status=='completed':
                values = [value[0] for value in job_details['entity']['scoring']['predictions'][0]['values']]
            return [job_id, status, values]
        except Exception as e:
            return [job_id, 'error', []]


    def trigger_predict(self, deployment_toSubmit, max_run_duration = 60):
        global job_count
        job_count = 0
        delete_pool = ThreadPool(24)
        all_jobs_status = []
        all_jobs_created = []
        all_jobs_deleted = []
        start = time.perf_counter()
        wml_credentials = config['IBM_creds']['wml_credentials']
        for space_id in deployment_toSubmit['deployment_space'].unique().tolist():
            wmlclient = APIClient(wml_credentials)
            wmlclient.set.default_space(space_id)
            service = Batch(source_wml_credentials=wml_credentials, source_space_id=space_id)
            print(f"starting for space: {space_id}")
            delete_results = [] 
            job_status = []     
            jobs_deleted = []   
            run_duration = 0
            space_start = time.perf_counter()
            deployments = deployment_toSubmit[deployment_toSubmit['deployment_space']==space_id]
            print(f"length of deployment list: {deployments.shape[0]}")
            jobs_to_submit=self.batch_run_size
            jobs_submitted=0
            jobs_executing=0
            no_new_jobs_counter = 0 # added
            prev_jobs_executing = 0 # added
            j=0
            while (( jobs_to_submit > 0 ) or ( jobs_executing > 0 )) and ( run_duration < max_run_duration * 60) and no_new_jobs_counter < 50: # changed
                jobs_to_submit=min(deployments.shape[0]-jobs_submitted,jobs_to_submit)
                to_fetch_list=()
                if ( jobs_to_submit > 0 ) :   
                    deployment_jobs = deployments.iloc[jobs_submitted:(jobs_submitted+jobs_to_submit)].copy()
                    deployment_jobs = deployment_jobs[['deployment_id', 'year', 'input_location']].copy()
    #                 print(f'deployment_jobs : {deployment_jobs}')
                    job_created = []
                    job_create_start = time.perf_counter()
                    create_results = []
                    print(f'jobs_to_submit: {jobs_to_submit}, iteration:{j+1}, submitted: {jobs_submitted}, jobs_to_submit_actual: {deployment_jobs.shape[0]}, total_jobs: {deployments.shape[0]}')
                    thread_pool_size = jobs_to_submit if jobs_to_submit <= 100 else 100
                    pool = ThreadPool(thread_pool_size)
                    for deployment in deployment_jobs.values.tolist():
                        create_results.append(pool.apply_async(self.create_deployment_jobs, deployment+[wmlclient, service]))
                    data_store_dict = {}    

                    pool.close()
                    pool.join()
                    job_created = [r.get() for r in create_results]
                    all_jobs_created.extend(job_created)
                    job_create_finish = time.perf_counter()
                    print(f'create jobs finished in {round((job_create_finish-job_create_start), 2)} seconds(s)')
                    print(f'number of jobs created: {len(job_created)}')
                    print(f'number of jobs created with error : {len([job for job in job_created if job is None])}')
                    print(f'number of jobs created with no job_id: {len([job for job in job_created if job[1]==""])}')
                    job_created_df = pd.DataFrame(columns=['deployment_id', 'job_id'], data=job_created)
                    to_fetch_list=job_created_df[['job_id']].values.tolist()            
                wait_time = 5 # 0 if jobs_to_submit > 0 else 5 # changed
                no_new_jobs_counter = no_new_jobs_counter+1 if jobs_executing == prev_jobs_executing else 0 # added
                prev_jobs_executing = jobs_executing #added
                print(f'waiting for {wait_time} sec ...')
                time.sleep(wait_time)
                print('done waiting')
                job_fetch_start = time.perf_counter()
                fetch_results = []
                pool = ThreadPool(24)
                if len(to_fetch_list) > 0  :  
                    to_fetch_list.extend([job[0]] for job in job_status if job[1] in ["queued", "running"])
                else:
                    to_fetch_list = ([job[0]] for job in job_status if job[1] in ["queued", "running"])
                for job in to_fetch_list:
                    fetch_results.append(pool.apply_async(self.get_state, job+[wmlclient]))
                pool.close()
                pool.join()
                job_status = [r.get() for r in fetch_results]
                all_jobs_status.extend([job for job in job_status if job[1] in ["completed", "failed"]])
                job_fetch_finish = time.perf_counter()
                print(f'fetch jobs finished in {round((job_fetch_finish-job_fetch_start), 2)} seconds(s)')
                print(f'number of jobs fetched: {len(job_status)}')
                print(f'number of jobs fetched and completed: {len([job for job in job_status if job[1] in ["completed"]])}')
                print(f'number of jobs fetched and queued: {len([job for job in job_status if job[1] in ["queued"]])}')
                print(f'number of jobs fetched and failed: {len([job for job in job_status if job[1] in ["failed"]])}')
                print(f'number of jobs fetched and running: {len([job for job in job_status if job[1] in ["running"]])}')
                print(f'number of jobs fetched otherwise:{len([job for job in job_status if job[1] not in ["completed", "queued", "failed", "running"]])}')
                job_fetched_df = pd.DataFrame(columns=['job_id', 'status', 'values'], data=job_status)

                # Delete completed and failed jobs
                job_list = [job[0] for job in job_fetched_df[['job_id', 'status']].values.tolist() if (job[1] in ["completed", "failed"])]
                if ( len(job_list) > 0):     
                    for job in job_list: 
                        delete_results.append(delete_pool.apply_async(self.del_jobs,(job, wmlclient,)) )
                job_delete_finish = time.perf_counter()
                jobs_submitted+=jobs_to_submit
                jobs_to_submit=len(job_list)
                jobs_executing=len([job for job in job_status if job[1] in ["queued", "running"]])
                j+=1
                print(f'total time taken in this loop, {j}: {round((job_delete_finish-job_create_start), 2)} sec(s)\n')
                run_duration = ( job_delete_finish - start )
                print(f'current run duration : {run_duration} jobs_executing : {jobs_executing} submitted:{jobs_submitted},total_jobs:{len(deployments)}\n')
            if (jobs_executing > 0):     
                for job in [job for job in job_status if job[1] in ["queued", "running"]]:
                    delete_results.append(delete_pool.apply_async(self.del_jobs,(job[0], wmlclient,)) )        
            jobs_deleted = [res for res in delete_results if res is not None]
            all_jobs_deleted.extend(jobs_deleted)
            print(f'total time taken in this space: {round((job_delete_finish-space_start)/60, 2)} min(s)\n')
            print(f'length of all jobs created:{len(all_jobs_created)}')
            print(f'length of all jobs status:{len(all_jobs_status)}')
            print(f'length of all jobs deleted:{len(all_jobs_deleted)}')
        # Wait for all deletes to finish
        delete_pool.close()
        delete_pool.join()
        print(f"length of all_jobs_status is {len(all_jobs_status)}")
        df_all_jobs_status = pd.DataFrame(all_jobs_status, columns=['job_id', 'status', 'predictions'])
        df_all_jobs_created = pd.DataFrame(all_jobs_created, columns=['deployment_id', 'job_id'])
        result_df = pd.merge(df_all_jobs_created, df_all_jobs_status, how='left', on='job_id')
        result_df = pd.merge(deployment_toSubmit[['deployment_space', 'deployment_id']], result_df, on='deployment_id', how = 'left')
        print(f'total time taken:{round((job_delete_finish-start)/60, 2)} min(s)')
        return result_df
                      
def trigger_prediction_run(prediction_input_deployments, max_allowed_time = 5, max_retries = 2):
    all_final_results = pd.DataFrame()
    #prediction_input_deployments = deployment_toSubmit
    max_retries = 3
    max_allowed_time = 1.5

    print('Deployments to submit shape:', prediction_input_deployments.shape)
    ph = predictionHelper()
    start = time.time()
    prediction_results = ph.trigger_predict(prediction_input_deployments, max_run_duration = max_allowed_time * 60)
    print(prediction_results)
    prediction_results = prediction_results[prediction_results['status'] == 'completed']
    prediction_results['predictions'] = prediction_results['predictions'].apply(lambda x: x[0])
    end = time.time()
    print('Total time taken for the prediction jobs to run', round((end-start)/60, 2),'mins.')
    final_results = pd.merge(prediction_results, prediction_input_deployments,  how='left', left_on=['deployment_space','deployment_id'], right_on = ['deployment_space','deployment_id'])
    print(final_results)
    remaining = prediction_results[[x is None or x != 'completed' or str(x) == 'nan' for x in final_results['status']]]
    # print(remaining)
    
    all_final_results = pd.concat([all_final_results, final_results], ignore_index=True)

    print(len(remaining))
    final_results = all_final_results
    return final_results

def get_es_data(isin, dates, index_prefix, schedular = None):
    if schedular is not None:
        schedular = schedular.capitalize()
    data=[]
    for year in range(dates[0], dates[1] + 1):
        q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
        try:
            result = es.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
            for rs in result['hits']['hits']:
                es_data=rs['_source']
                data.append(es_data)
        except:
            print(f'ES data for {index_prefix} not present for {year}.')
    df=pd.DataFrame(data)
    if (schedular != None) and ('schedular' in df.columns):
        df = df[df['schedular'] == schedular].reset_index(drop = True)
    if len(df) == 0:
        return df
    df['date']=pd.to_datetime(df['date'])
    df.sort_values('date', ascending=True, inplace=True)
    df.reset_index(inplace=True, drop=True)
    return df


def read_data(isin, years, read_from, pred_col, schedular, es_index, bucket_name, path_loc, local_folder):
    default_schedular = 'Monthly'
    try:
        if read_from == 'es': # fetching data from es
            temp = get_es_data(isin, years, config['Metrics_data']['es_index'], config['Metrics_data']['schedular'])[['date',pred_col,'monthly_close_change']] # fetching data from es
        if schedular == None or schedular == '':
            schedular = default_schedular
    except:
        return isin, pd.DataFrame()
#     company = all_df[[x == isin for x in all_df['isin']]][['tic', 'exchange', 'isin', 'ipodate', 'ciq_trading_id']].reset_index(drop = True)
#     if len(company) == 0:
#         print(f'Isin {isin} not in Masters Active Firms')
#         return isin, pd.DataFrame()
#     #try:
#     close_change = get_close_change(company, schedular_dict[schedular])
#     temp = pd.merge(temp, close_change, on = 'date').rename(columns = {'percent_pct': 'close_change'})
    temp.rename(columns = {pred_col:f'{schedular}_predictions', 
                     'monthly_close_change': f'actual_{schedular}_returns'}, 
          inplace = True)
    temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].shift(schedular_dict[schedular])
    temp['date'] = pd.to_datetime(temp['date'])
    temp = temp[(temp['date'] >= pd.to_datetime(f'{years[0]}-01-01')) & (temp['date'] <= pd.to_datetime(f'{years[1]}-12-31'))]    
    temp = temp.drop_duplicates(subset = 'date').sort_values(by = 'date').reset_index(drop = True)
    temp.reset_index(drop = True, inplace = True)
    temp = temp.ffill().fillna(0)
    temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].astype(float, errors="ignore")
    temp[f'actual_{schedular}_returns'] = temp[f'actual_{schedular}_returns'].astype(float, errors="ignore")
    print(f'Data collected for isin {isin}.')
#     except:
#         temp = pd.DataFrame()
    return isin, temp
 
def read_data_parallely(list_of_isins, years, read_from = 'es', pred_col = 'monthly_predictions', n_workers = 100, **kwargs): # function to read historical data
    '''
    'kwargs' keys-
        'es_index': index of Elastic Search to fetch data from (if read_from is 'es')
        'bucket_name': name of S3 bucket where data is stored (if read_from is 's3')
        'path_loc': S3 folder location to fetch data from (if read_from is 's3')
        'local_folder': path for folder to fetch data from (if data needs to be collected from local)
        'schedular': 'daily', 'weekly' or 'monthly'
    '''
    if 'schedular' in kwargs.keys():
        schedular = kwargs['schedular'].lower()
    else:
        schedular = None
    if read_from == 'es': # fetching data from es
        es_index = kwargs['es_index']
        bucket_name = None
        path_loc = None
        local_folder = None
    elif read_from == 's3':
        bucket_name = kwargs['bucket_name']
        path_loc = kwargs['path_loc']
        es_index = None
        local_folder = None
    else:
        local_folder = kwargs['local_folder']
        es_index = None
        bucket_name = None
        path_loc = None
    n_calls = len(list_of_isins)
    with ThreadPoolExecutor(max_workers=n_workers) as exe:
        dfs = exe.map(read_data, 
                     list_of_isins, 
                     [years] * n_calls, 
                     [read_from] * n_calls, 
                     [pred_col] * n_calls, 
                     [schedular] * n_calls, 
                     [es_index] * n_calls, 
                     [bucket_name] * n_calls, 
                     [path_loc] * n_calls, 
                     [local_folder] * n_calls)
    data_dict = {}
    for isin, data in dfs:
        data_dict[isin] = data
    print(len(data_dict))
    return data_dict

def calculate_metrics (etf, temp_df, lastbday, schedular,  prediction_column, actual_column):
    '''
        Function to calculate metrics using Inhouse Library function
    '''
    if schedular == 'Monthly':
        shift_window = 22
    elif schedular == 'Quarterly':
        shift_window = 66
 
    isin = temp_df['isin'].values[0]
    
    temp_df = temp_df[['date',actual_column, prediction_column]].copy()
    temp_df[actual_column] = temp_df[actual_column].shift(shift_window)
    temp_df[prediction_column] = temp_df[prediction_column].shift(shift_window)
    
    temp_df = temp_df.dropna()
 
    temp_df = temp_df[-700:] ### assuming the daily run needs a single row, so no need to iterate over all historical
    temp_df.reset_index(inplace = True, drop = True)
    
    new_metrics = metrics_obj.calculate_metrics_helper(temp_df , isin =isin, prediction_column =prediction_column,
                                                       actual_column = actual_column)
    new_metrics = new_metrics[new_metrics['date']<= lastbday]
    metrics_oneday = new_metrics[-1:]
    metrics_oneday.reset_index(inplace = True, drop = True)
    metrics_oneday.rename(columns={'actual_returns':'actual_monthly_returns','predictions':'monthly_predictions'}, inplace = True)
    # metrics_oneday = metrics_oneday.drop(columns = ['actual_returns','predictions'], axis = 1)
 
    metrics_oneday['tic'] = etf
    return metrics_oneday

def generate_and_save_metrics(cp_date):
    bnpetfs = config['misc']['bnpetfs']
    years = [(int(cp_date.split('-')[0])-3),(int(cp_date.split('-')[0]))]
    data_dict = read_data_parallely(bnpetfs,
                                    years,
                                    read_from = 'es',
                                    n_workers = 120,
                                    es_index = config['Metrics_data']['es_index'],
                                    schedular = config['Metrics_data']['schedular'])
    for etf in bnpetfs:
        df = data_dict[etf]
        df = df[df['date'] <= pd.to_datetime(cp_date)]
        df['isin'] = etf
        metrics_df = calculate_metrics (etf, df, cp_date, config['Metrics_data']['schedular'],  'monthly_predictions', 'actual_monthly_returns')
        # metrics_df['date'] = cp_date
        if pd.to_datetime(metrics_df['date'].values[0]).strftime('%Y-%m-%d') != cp_date:
            continue
        metrics_df['schedular'] = 'Monthly'
        ss.write_advanced_as_df(metrics_df,config['S3_paths']['upload_daily_metrics_bucket'],config['S3_paths']['upload_daily_metrics_path'].format(etf=etf, date=cp_date))
        metrics_df.replace({np.nan:None},inplace=True)
        metrics_df['date'] = metrics_df['date'].apply(lambda x:(pd.to_datetime(x)).strftime('%Y-%m-%d'))
        upload_data_to_elastic_search(es, metrics_df, 'eq_etf_model_metrics' ,'Monthly')
        pred_data = ss.read_advanced_as_df(config['S3_paths']['upload_daily_predictions_bucket'],config['S3_paths']['upload_daily_predictions_path'].format(etf=etf, date=cp_date))
        pred_data['isin'] = etf
        pred_data = pd.merge(pred_data,metrics_df[["isin", "avg_confidence_score", "root_mean_squared_error"]], on = 'isin')
        pred_data.rename(columns={'avg_confidence_score':'confidence_score'},inplace=True)
        consolidated_tic_df = ss.read_as_dataframe(config['S3_paths']['upload_cons_etf_bucket'], config['S3_paths']['upload_cons_etf_path'].format(etf=etf))
        if 'level_0' in consolidated_tic_df.columns:
                consolidated_tic_df = consolidated_tic_df.drop(columns='level_0')
        if 'index' in consolidated_tic_df.columns:
            consolidated_tic_df = consolidated_tic_df.drop(columns='index')
        consolidated_tic_df = pd.concat([consolidated_tic_df,pred_data])
        consolidated_tic_df.ffill(inplace=True)
        consolidated_tic_df.fillna(0, inplace=True)
        consolidated_tic_df['date'] = consolidated_tic_df['date'].apply(lambda x:(pd.to_datetime(x)).strftime('%Y-%m-%d'))
        consolidated_tic_df=consolidated_tic_df.drop_duplicates(subset='date', keep="last")
        consolidated_tic_df = consolidated_tic_df.sort_values('date', ascending=True)
        ss.write_advanced_as_df(consolidated_tic_df,config['S3_paths']['upload_cons_etf_bucket'], config['S3_paths']['upload_cons_etf_path'].format(etf=etf))


def prepare_body_html(body):
    if body:
        receiver = config['Gmail_creds']['email_receiver_failed']
        html = f"""\
                    <html>
                      <head></head>
                      <body>
                       {body}
                      </body>
                    </html>
                    """
        
    else:
        receiver = config['Gmail_creds']['email_receiver_success']
        html  = f'<html><title></title><body>Hi,<br><p><b>BNP prediction and metrics files have been generated in following paths.</b> <br> Daily predictions file path : <a href="https://us-east-1.console.aws.amazon.com/s3/buckets/etf-predictions?region=us-east-1&bucketType=general&prefix=Monthly/new_BNP_Paribas/BNP_retrained/Daily_run_predictions/&showversions=false">Daily Predictions</a><br>Daily metrics :<a href="https://us-east-1.console.aws.amazon.com/s3/buckets/etf-predictions?region=us-east-1&bucketType=general&prefix=Monthly/new_BNP_Paribas/Daily_Run_metrics/&showversions=false">Daily metrics</a></p><br>Thanks</body></html>'
    receiver = [email.strip() for email in receiver.split(',')]
    return html,receiver

def send_email(subject , body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']
    # receiver_passed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_passed')
    # receiver_failed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_failed')
    # receiver = [email.strip() for email in receiver.split(',')]
    body_html,receiver = prepare_body_html(body)
    SendMessage(gmail_creds_bucket , gmail_creds_path, ss, sender, receiver , subject, body_html)

def get_cp_avail_tickers(date):
    cons_cp_df = pd.DataFrame()
    for etf in config['misc']['bnpetfs']:
        try:
            bnp_json = requests.get(config['Inhouse_API']['inhouse_api_url_by_isin'].format(identifier=etf,startdate=(pd.to_datetime(date)-BDay(1)).strftime("%Y-%m-%d"),enddate=date)).json()
            bnp_daily_data = pd.DataFrame(bnp_json["data"]["stocks"])[['date','close']]
            bnp_daily_data['date'] = pd.to_datetime(bnp_daily_data['date'])
            bnp_daily_data.sort_values(by='date', inplace=True)
            bnp_daily_data.reset_index(drop=True,inplace=True)
            bnp_daily_data=bnp_daily_data[bnp_daily_data['date']==date]
            bnp_daily_data['Ticker'] = etf
            cons_cp_df = pd.concat([cons_cp_df,bnp_daily_data], axis=0)
        except:
            print(f"Close Price not available for {etf} for the date {date}")
            continue
    return cons_cp_df['Ticker'].values.tolist()

def upload_data_to_elastic_search(es, df, index_name ,schedular):
    if df.empty:
        return
    documents = []
    for index, row in df.iterrows():
        doc_id = f"{row['isin']}_{row['date']}"
        year = row['date'].split('-')[0]
        document = {"index": {"_index": f'{index_name}_{year}', "_id": doc_id}}
        data = row.to_dict()
        documents.append(document)
        documents.append(data)
    print("len",len(documents))
    #print(documents)
    ##return documents
    response=es.client.bulk(documents)
    print(response)



