from helpers import *
from etfs.BNPIFUS import BNPIFUS
from etfs.BNPIFU10 import BNPIFU10
from etfs.BNPIFEU import BNPIFEU
from etfs.BNPIFEM import BNPIFEM
from etfs.BNPIFCN import BNPIFCN
from etfs.BNPIFJP import BNPIFJP
from etfs.BNPIFE10 import BNPIFE10
from etfs.BNPIFJ10 import BNPIFJ10
from etfs.BNPIDSBU import BNPIDSBU
from etfs.BNPIG0GC import BNPIG0GC

s3conn = s3_config()
ss = s3conn
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

def collect_etf_data(etf_name, etf_class, cp_date):
    obj = etf_class(cp_date)
    df = obj.collect_data()
    time.sleep(1.5)  # Sleep to avoid hitting FRED too fast
    return etf_name, df

def collect_all_etfs(cp_date):
    apiurl = config['Inhouse_API']['inhouse_api_url_by_isin']
    techind_patterns = config['misc']['techind_patterns']
    techind_etfs = config['misc']['techin_etfs']
    upload_techindicators_bucket = config['S3_paths']['upload_techindicators_bucket']
    upload_techindicators_path = config['S3_paths']['upload_techindicators_path']
    rolledup_etfs = config['misc']['rolledup_etfs']
    rolledup_read_bucket = config['S3_paths']['rolledup_etf_holdings_bucket']
    rolledup_read_path = config['S3_paths']['rolledup_etf_holdings_path']

    upload_rolledup_bucket = config['S3_paths']['upload_rolledup_bucket']
    upload_rolledup_etf_data_path = config['S3_paths']['upload_rolledup_etfdata_path']
    upload_rolledup_raw_data_path = config['S3_paths']['upload_rolledup_rawdata_path']
    run_date = (pd.to_datetime(cp_date)+BDay(1)).strftime("%Y-%m-%d")
    trigger_and_save_technical_indicators(techind_etfs,apiurl,techind_patterns,upload_techindicators_bucket,upload_techindicators_path,ss)
    trigger_and_save_rolledup_data(rolledup_etfs,ss,rolledup_read_bucket, rolledup_read_path,run_date,upload_rolledup_bucket,upload_rolledup_etf_data_path,upload_rolledup_raw_data_path)
    results = {}
    for name,cls in ETF_CLASSES.items():
        if name in get_cp_avail_tickers(cp_date):
            etf_name,df = collect_etf_data(name, cls, cp_date)
            results[etf_name] = df
    return results

def generate_and_save_predictions(cp_date):
    bnpetfs = config['misc']['bnpetfs']
    dep_file = ss.read_advanced_as_df(config['S3_paths']['deployment_bucket'], config['S3_paths']['deployment_path'])[['isin','schedular','model_identifier','space_id','deployment_id','input_features','test_end_date']]
    dep_file = dep_file[dep_file['test_end_date'].isna()].reset_index(drop = True)
    dep_file_test = dep_file.copy()
    pred_input = []
    for i, row in dep_file.iterrows():
        etf = row['isin']
        print(etf)
        bnp_daily_data_df = ss.read_advanced_as_df(config['S3_paths']['upload_input_data_bucket'], config['S3_paths']['upload_input_data_path'].format(etf=etf))
        bnp_daily_data_df = bnp_daily_data_df.ffill()
        bnp_daily_data_df.fillna(0,inplace=True)
        bnp_daily_data_df = bnp_daily_data_df[bnp_daily_data_df['date'] == cp_date]
        if 'index' in bnp_daily_data_df.columns:
            bnp_daily_data_df.drop(columns=['index'], inplace=True)
        pred_input.append(bnp_daily_data_df)
    dep_file_test['input_location'] = pred_input
    dep_file_test['year'] = cp_date.split('-')[0]
    dep_file_test.rename(columns = {'space_id': 'deployment_space'}, inplace = True)
    dep_file_test = dep_file_test[['deployment_id', 'deployment_space', 'year', 'input_location']]
    final = trigger_prediction_run(dep_file_test, 3)
    final = final.merge(dep_file[['deployment_id', 'isin', 'schedular', 'model_identifier']], 
                    how='left', 
                    left_on='deployment_id', 
                    right_on='deployment_id')
    for etf in bnpetfs:
        print(etf)
        bnp_pred_data = ss.read_advanced_as_df(config['S3_paths']['upload_input_data_bucket'], config['S3_paths']['upload_input_data_path'].format(etf=etf))
        s3_preds_df = bnp_pred_data.ffill()
        bnp_pred_data = bnp_pred_data[bnp_pred_data['date'] == cp_date]
        s3_preds_df = s3_preds_df[s3_preds_df['date'] == cp_date]
        if bnp_pred_data.empty:
            continue
        if 'index' in bnp_pred_data.columns:
            bnp_pred_data.drop(columns=['index'], inplace=True)
            s3_preds_df.drop(columns=['index'], inplace=True)
        # ffill_df = ss.read_as_dataframe('etf-predictions', config['S3_paths']['upload_cons_etf_path'].format(etf=etf))
        try:
            bnp_pred_data['monthly_predictions'] = final[final['isin']==etf]['predictions'].values[0]
            s3_preds_df['monthly_predictions'] = final[final['isin']==etf]['predictions'].values[0]
        except:
            yest_df = ss.read_as_dataframe(config['S3_paths']['upload_cons_etf_bucket'], config['S3_paths']['upload_cons_etf_path'].format(etf=etf))
            if 'level_0' in yest_df.columns:
                yest_df = yest_df.drop(columns='level_0')
            if 'index' in yest_df.columns:
                yest_df = yest_df.drop(columns='index')
            for i in range(1,5):
                try:
                    past_date = (pd.to_datetime(cp_date) - BDay(i)).strftime("%Y-%m-%d")
                    print(past_date)
                    yest_df = yest_df[yest_df['date'] == past_date]
                    print(yest_df)
                    break
                except:
                    continue
            bnp_pred_data['monthly_predictions'] = yest_df['monthly_predictions'].values[0]
            s3_preds_df['monthly_predictions'] = yest_df['monthly_predictions'].values[0]
        s3_preds_df['isin'] = etf
        bnp_pred_data['isin'] = etf
        s3_preds_df['schedular'] = 'Monthly'
        bnp_pred_data['schedular'] = 'Monthly'
        bnp_pred_data['date'] = bnp_pred_data['date'].apply(lambda x:(pd.to_datetime(x)).strftime('%Y-%m-%d'))
        ss.write_advanced_as_df(s3_preds_df,config['S3_paths']['upload_daily_predictions_bucket'],config['S3_paths']['upload_daily_predictions_path'].format(etf=etf, date=cp_date))
        bnp_pred_data.replace({np.nan:None},inplace=True)
        upload_data_to_elastic_search(es, bnp_pred_data, 'eq_etf_model' ,'monthly')
        #upload bnp_pred_data to es

def bnp_daily_run(run_date):
    cp_date = (pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
    results = collect_all_etfs(cp_date)
    generate_and_save_predictions(cp_date)
    generate_and_save_metrics(cp_date)


ETF_CLASSES = {
    'BNPIFUS': BNPIFUS,
    'BNPIFEM': BNPIFEM,
    'BNPIFEU': BNPIFEU,
    'BNPIFCN': BNPIFCN,
    'BNPIFJP': BNPIFJP,
    'BNPIFU10': BNPIFU10,
    'BNPIFE10': BNPIFE10,
    'BNPIFJ10': BNPIFJ10,
    'BNPIG0GC': BNPIG0GC,
    'BNPIDSBU': BNPIDSBU,
}

if __name__ == "__main__":
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")
    
    # run_date = (datetime.today()-BDay(0)).strftime("%Y-%m-%d")
    # run_date = '2025-04-07'
    try:
        bnp_daily_run(run_date)
        print(f"Monthly BNP Paribas Predictions and metrics file generated for {run_date}")
        send_email(f'Monthly BNP Paribas : Daily run pipeline COMPLETED successfully for {run_date}')
    except Exception as e:
        print(f'Monthly BNP Paribas Preportfolio : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        send_email( f'Monthly BNP Paribas : Daily run pipeline FAILED for run date {run_date}', traceback.format_exc())
        print(e)
        raise
        
