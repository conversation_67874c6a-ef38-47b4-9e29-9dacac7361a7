Gmail_creds:
    gmail_creds_bucket : etf-predictions
    gmail_creds_path: preportfolio/gmail_credentials/credentials.json
    gmail_sender : <EMAIL>
    email_receiver_success : <EMAIL>, <EMAIL>, <EMAIL>
    email_receiver_failed : <EMAIL>, <EMAIL>, <EMAIL>

IBM_creds:
    wml_credentials : { "apikey": M1MZPBWTdIyon3T9RyFJlEo8VNCJ6blusJTYRvuB9k2W,"url": 'https://us-south.ml.cloud.ibm.com'}
    
Master_active_firms:
    api_url : http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all

Inhouse_API:
    inhouse_api_url_by_isin : http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA
    inhouse_api_url_by_tic : http://52.2.217.192:8080/stockhistory/getstocksbytic?tic={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA

Metrics_data:
    schedular_dict : {'monthly': 22,'weekly' : 5,'daily': 1}
    req_columns : ["date", "isin", "actual_monthly_returns", "monthly_predictions"]
    all_metrics : ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "confidence_score", "avg_confidence_score", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day"]
    metrics_period : 22
    es_index : eq_etf_model
    schedular : Monthly


SNP_data:
    url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json
    headers : {'Authorization': 'Basic ************************************************','Content-type': 'application/json'}
    snp_mnemonics_identifier_list: {'BNPIFUS':[['IQ_VOLUME', 'IQ_MARKETCAP', 'IQ_SHARESOUTSTANDING','IQ_DIVIDEND_YIELD','IQ_RSI_ADJ','IQ_VALUE_TRADED'],'SPY:ARCA'],
                                     'BNPIFEM':[['IQ_VOLUME', 'IQ_MARKETCAP', 'IQ_SHARESOUTSTANDING','IQ_DIVIDEND_YIELD','IQ_RSI_ADJ','IQ_VALUE_TRADED'],'EEM:ARCA'],
                                     'BNPIFEU':[['IQ_VOLUME', 'IQ_MARKETCAP', 'IQ_SHARESOUTSTANDING','IQ_DIVIDEND_YIELD','IQ_RSI_ADJ','IQ_VALUE_TRADED'],'EXW1:XTRA'],
                                     'BNPIFJP':[['IQ_VOLUME', 'IQ_MARKETCAP', 'IQ_SHARESOUTSTANDING','IQ_DIVIDEND_YIELD','IQ_RSI_ADJ','IQ_VALUE_TRADED'],'1321:TSE'],
                                     'BNPIFCN':[['IQ_VOLUME', 'IQ_MARKETCAP', 'IQ_SHARESOUTSTANDING','IQ_DIVIDEND_YIELD','IQ_RSI_ADJ','IQ_VALUE_TRADED'],'2828:SEHK'],
                                     'BNPIG0GC':[['IQ_VOLUME', 'IQ_MARKETCAP', 'IQ_SHARESOUTSTANDING','IQ_DIVIDEND_YIELD','IQ_RSI_ADJ','IQ_VALUE_TRADED'],'GLD:ARCA'],
                                    'BNPIFU10':[[],''],
                                    'BNPIFJ10':[[],''],
                                    'BNPIFE10':[[],''],
                                    'BNPIDSBU':[[],'']
                                     }
    
misc:
    bnpetfs : [BNPIFUS,BNPIFEM,BNPIFEU,BNPIFJP,BNPIFCN,BNPIFE10,BNPIFU10,BNPIFJ10,BNPIG0GC,BNPIDSBU]
    techind_patterns : [APO,MOM,PPO,RSI,MACD,MACDFIX,STOCHRSI]
    techin_etfs : [BNPIFU10,BNPIFCN,BNPIFJP,BNPIFE10,BNPIFJ10,BNPIDSBU]
    rolledup_etfs : [XLE,XME,SPY,QQQ,EEM,EFA,EWJ,IWM,IYR,XLB,XLC,XLF,XLI,XLK,XLP,XLRE,XLU,XLV,XLY,BNPIFCN]
    ETF_CLASSES : {'BNPIFUS': BNPIFUS,'BNPIFEM': BNPIFEM,'BNPIFEU': BNPIFEU,'BNPIFCN': BNPIFCN,'BNPIFJP': BNPIFJP,'BNPIFU10': BNPIFU10,'BNPIFE10': BNPIFE10,'BNPIFJ10': BNPIFJ10,'BNPIG0GC': BNPIG0GC,'BNPIDSBU': BNPIDSBU}
    fred_mnemonic_list : {'BNPIFUS':['GDPC1','PPIACO', 'CPIAUCSL', 'UNRATE', 'T10Y2Y','DFF','T5YIE','T5YIFR','BAMLC0A4CBBBEY','USEPUINDXD','GVZCLS','VIXCLS'],
                          'BNPIFEM':['CHNSPASTT01GYM','BSCICP03CNM665S', 'CHNLOLITOAASTSAM', 'CHNBSCICP02STSAM', 'TWNBCAGDPBP6PT','NAEXKP01INQ652S','INDCPIALLMINMEI','SLUEM1524ZSIND','FPCPITOTLZGIND','NAEXKP01KRQ189S','KORCPIALLMINMEI','LRUNTTTTKRM156S','FPCPITOTLZGKOR','MEXGDPNQDSMEI','MEXCPIALLMINMEI','NMUR','FPCPITOTLZGMEX','NAEXKP01BRQ652S','CPALTT01BRM659N','SLUEM1524ZSBRA','FPCPITOTLZGBRA','INDIRLTLT01STM','IRLTLT01KRM156N','IRLTLT01MXM156N','INTGSTBRM193N'],
                          'BNPIFEU':['PIEAMP01EZM661N','EA19CPALTT01GYM', 'CPMNACSCAB1GQEU272020', 'FPCPITOTLZGEUU', 'EUEPUINDXM', 'ECBDFR','BAMLHE00EHYIEY','BSCICP02USM460S','WUIEUROPE','LRUN74TTEUQ156S','SPASTT01EZM657N','OECDMABMM301GPSAM','INTGSBEZM193N','IRSTCI01EZM156N','IR3TIB01EZM156N','ECBMLFR','DTWEXBGS'],
                          'BNPIFJP':['JPNRGDPEXP','JPNCPIALLMINMEI', 'PITGCG01JPM661N', 'JPNPCPIPCPPPT', 'JPNLOCOSIORSTM', 'LRUN64TTJPM156S','SPASTT01JPM661N','NIKKEI225','IRLTLT01JPM156N','RBJPBIS','IRSTCI01JPM156N','CSCICP03JPM665S','IR3TIB01JPM156N','INTGSBJPM193N','JPNFCNODCANUM'],
                          'BNPIFCN':['WUIHKG','CHNSPASTT01GYM', 'BSCICP03CNM665S', 'CHNLOLITOAASTSAM', 'CHNBSCICP02STSAM', 'TWNBCAGDPBP6PT'],
                          'BNPIG0GC':['FPCPITOTLZGUSA','CPIAUCSL', 'CSCICP03USM665S', 'UNRATENSA', 'PPIACO'],
                          'BNPIFU10':['CONSUMER','FEDFUNDS', 'CPIAUCSL', 'PPIACO', 'UNRATE', 'PAYEMS', 'RRPONTSYAWARD','T10Y2Y', 'RESPPALGUOMNXAWXCH1NWW', 'GDPC1','USEPUINDXD','WILLREITIND','GVZCLS'],
                          'BNPIFJ10':['JPNRGDPEXP','JPNCPIALLMINMEI', 'IR3TIB01JPM156N', 'IRSTCI01JPM156N', 'INTGSBJPM193N', 'PITGCG01JPM661N','JPNPCPIPCPPPT', 'JPNLOCOSIORSTM', 'JPNFCNODCANUM','LRUN64TTJPM156S','SPASTT01JPM661N','NIKKEI225','IRLTLT01JPM156N','RBJPBIS','CSCICP03JPM665S'],
                          'BNPIFE10':['IRSTCI01EZM156N','PIEAMP01EZM661N', 'INTGSBEZM193N', 'EA19CPALTT01GYM', 'CPMNACSCAB1GQEU272020', 'IR3TIB01EZM156N','ECBMLFR','FPCPITOTLZGEUU','EUEPUINDXM','ECBDFR','BAMLHE00EHYIEY','BSCICP02USM460S','WUIEUROPE','LRUN74TTEUQ156S','SPASTT01EZM657N','OECDMABMM301GPSAM'],
                          'BNPIDSBU':['WTISPLC','OVXCLS', 'IPG21112S', 'IP2709', 'RIWG21112S', 'ID2710','PCU486110486110P','PCU324191324191','WPU0561','PCU324110324110J','U24STI','IPG32411S']
                         }
    etf_industry_mappings : {'BNPIFUS':[],
                          'BNPIFEM':[],
                          'BNPIFEU':['europe_consumer_discretionary','europe_financials','europe_industrials','europe_information_technology'],
                          'BNPIFJP':['japan_consumer_discretionary','japan_communication_services','japan_information_technology','japan_health_care','japan_industrials'],
                          'BNPIFCN':['china_consumer_discretionary','china_communication_services','china_information_technology','china_consumer_staples','china_financials','china_health_care'],
                          'BNPIG0GC':[],
                          'BNPIFU10':[],
                          'BNPIFJ10':[],
                          'BNPIFE10':[],
                          'BNPIDSBU':[]
                         }
    etf_column_mappings: {'BNPIFUS':['date', 'close_price', 'daily_close_change', 'weekly_close_change',
       'monthly_close_change', 'yearly_close_change', 'Volume',
       'daily_volume_change', 'weekly_volume_change', 'monthly_volume_change',
       'Yearly_volume_change', 'yearhigh', 'yearlow', 'index_day_closeprice',
       'beta_5yr', 'beta_2yr', 'beta_1yr', 'marketcap', 'Share Outstanding',
       'Dividend yield', 'RSI Dividend ADJ', 'Daily value traded', 'vol_5yr',
       'vol_2yr', 'vol_1yr', '1Mo', '2Mo', '3Mo', '4Mo', '6Mo', '1Yr', '2Yr',
       '3Yr', '5Yr', '7Yr', '10Yr', '20Yr', '30Yr', 'pbv rolledup',
       'price_sales rolledup', 'pe_excl rolledup', 'total_rev rolledup','F_score_rolledup','M_score_rolledup','I_score_rolledup','ER_score_rolledup',
       'gdp_forecasted', 'future_fedfund_rate', 'etf_kw_pos', 'etf_kw_neg',
       'macro_kw_pos', 'macro_kw_neg', 'common_kw_pos', 'common_kw_neg',
       'holdings_kw_pos', 'holdings_kw_neg', 'all_inds_kw_pos',
       'all_inds_kw_neg', 'equity_prediction', 'ADX', 'APO', 'CCI', 'DX', 'MFI',
       'MOM', 'PPO', 'ULTOSC', 'WILLR', 'MACD_0', 'MACD_1', 'MACD_2',
       'MACDFIX_0', 'MACDFIX_1', 'MACDFIX_2', 'STOCH_0', 'STOCH_1', 'STOCHF_0',
       'STOCHF_1', 'STOCHRSI_0', 'STOCHRSI_1', 'DJI_closeprice',
       'SPX_closeprice', 'GDP',  'PPI',
       'CPI', 'unemployement_rate', '10-Year Treasury Constant Maturity Minus 2-Year Treasury Constant Maturity', 'Federal Funds Effective Rate', '5-Year Breakeven Inflation Rate', '5-Year, 5-Year Forward Inflation Expectation Rate',
       'ICE BofA BBB US Corporate Index Effective Yield', 'Economic Policy Uncertainty Index for United States', 'CBOE Gold ETF Volatility Index','CBOE Volatility Index','self_predictions', 'corr_predictions', 'mp_1d','mp_1w', 'mp_1m', 'mp_1q', 'mp_6m', 'mp_1y','E_mini_snp_futures_Closeprice','E_mini_snp_futures_Volume','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIFEM':['date','close_price',
                                    'daily_close_change',
                                    'weekly_close_change',
                                    'monthly_close_change',
                                    'yearly_close_change','Volume',
                                    'daily_volume_change',
                                    'weekly_volume_change',
                                    'monthly_volume_change',
                                    'Yearly_volume_change',
                                    'yearhigh',
                                    'yearlow',
                                    'index_day_closeprice',
                                    'beta_5yr',
                                    'beta_2yr',
                                    'beta_1yr',
            'marketcap',
            'Share Outstanding',
            'Dividend yield',
            'RSI Dividend ADJ',
            'Daily value traded',
            'vol_5yr',
            'vol_2yr',
            'vol_1yr',
            'ChinaBond Government Bond Yield Curve_3M',
            'ChinaBond Government Bond Yield Curve_6M',
            'ChinaBond Government Bond Yield Curve_1Y',
            'ChinaBond Government Bond Yield Curve_3Y',
            'ChinaBond Government Bond Yield Curve_5Y',
            'ChinaBond Government Bond Yield Curve_7Y',
            'ChinaBond Government Bond Yield Curve_10Y',
            'ChinaBond Government Bond Yield Curve_30Y',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_3M',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_6M',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_1Y',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_3Y',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_5Y',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_7Y',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_10Y',
            'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_30Y',
            'ChinaBond CP&Note Yield Curve (AAA)_3M',
            'ChinaBond CP&Note Yield Curve (AAA)_6M',
            'ChinaBond CP&Note Yield Curve (AAA)_1Y',
            'ChinaBond CP&Note Yield Curve (AAA)_3Y',
            'ChinaBond CP&Note Yield Curve (AAA)_5Y',
            'ChinaBond CP&Note Yield Curve (AAA)_7Y',
            'ChinaBond CP&Note Yield Curve (AAA)_10Y',
            'ChinaBond CP&Note Yield Curve (AAA)_30Y',
            'pbv rolledup',
            'price_sales rolledup',
            'pe_excl rolledup',
            'total_rev rolledup',
                'F_score_rolledup','M_score_rolledup','I_score_rolledup','ER_rolledup',
            'etf_kw_pos',
            'etf_kw_neg',
            'macro_kw_pos',
            'macro_kw_neg',
            'common_kw_pos',
            'common_kw_neg',
            'holdings_kw_pos',
            'holdings_kw_neg',
            'financials_kw_pos',
            'financials_kw_neg',
            'information_technology_kw_pos',
            'information_technology_kw_neg',
            'consumer_discretionary_kw_pos',
            'consumer_discretionary_kw_neg',
            'macro_prediction',
            'ADX',
            'APO',
            'CCI',
            'DX',
            'MFI',
            'MOM',
            'PPO',
            'ULTOSC',
            'WILLR',
            'MACD_0',
            'MACD_1',
            'MACD_2',
            'MACDFIX_0',
            'MACDFIX_1',
            'MACDFIX_2',
            'STOCH_0',
            'STOCH_1',
            'STOCHF_0',
            'STOCHF_1',
            'STOCHRSI_0',
            'STOCHRSI_1','HIS_closeprice','MXEF_closeprice',
            'Equity_market_china',
            'Confidence index', 'oecd leading indicator',
                'business tendency survey', 'net current account','India GDP','India CPI',
                'India Unemployement','India Inflation','South Korea GDP','South Korea CPI','South Korea Unemployement','South Korea Inflation','Mexico GDP','Mexico CPI','Mexico Unemployement','Mexico Inflation','Brazil GDP','Brazil CPI','Brazil Unemployement','Brazil Inflation','India Bond yield 10yr',
                'South Korea yield 10yr','Mexico Government bond yield','Brazil Government bond yield',
            'mp_1d',
            'mp_1w',
            'mp_1m',
            'mp_1q',
            'mp_6m',
            'mp_1y','MSCI_Emerging_Markets_Index_Futures_Closeprice','MSCI_Emerging_Markets_Index_Futures_Volume','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIFEU':['date', 'close_price', 'daily_close_change', 'weekly_close_change',
       'monthly_close_change', 'yearly_close_change', 'Volume',
       'daily_volume_change', 'weekly_volume_change', 'monthly_volume_change',
       'Yearly_volume_change', 'yearhigh', 'yearlow', 'index_day_closeprice',
       'beta_5yr', 'beta_2yr', 'beta_1yr', 'marketcap', 'Share Outstanding',
       'Dividend yield', 'RSI Dividend ADJ', 'Daily value traded', 'vol_5yr',
       'vol_2yr', 'vol_1yr', '1Y Treasury Yield', '5Y Treasury Yield',
       '10Y Treasury Yield', '20Y Treasury Yield', '30Y Treasury Yield',
       'pbv rolledup', 'price_sales rolledup', 'pe_excl rolledup',
       'total_rev rolledup','F_score_rolledup','M_score_rolledup','I_score_rolledup','ER_rolledup', 'equity_prediction', 'ADX', 'APO', 'CCI', 'DX', 'MFI',
       'MOM', 'PPO', 'ULTOSC', 'WILLR', 'MACD_0', 'MACD_1', 'MACD_2',
       'MACDFIX_0', 'MACDFIX_1', 'MACDFIX_2', 'STOCH_0', 'STOCH_1', 'STOCHF_0',
       'STOCHF_1', 'STOCHRSI_0', 'STOCHRSI_1', 'SX5E_closeprice', 'etf_KW_pos',
       'etf_KW_neg', 'macro_pos', 'macro_neg',
       'europe_consumer_discretionary_pos',
       'europe_consumer_discretionary_neg', 'europe_financials_pos',
       'europe_financials_neg', 'europe_industrials_pos',
       'europe_industrials_neg', 'europe_information_technology_pos',
       'europe_information_technology_neg', 'common_kw_pos', 'common_kw_neg',
       'holdings_pos', 'holdings_neg', 'PPI', 'CPI', 'GDP', 'Inflation',
       'economic_index', 'deposits', 'corporate_funds', 'business_tendency_survey',
       'uncertainty_index', 'unemployement', 'europe_market', 'broad_money',
       'government_security_rates ','interest rate','interbank rate','Marginal Lending rate','Broad dollar usd index','self_predictions', 'corr_predictions', 'MP_1D', 'MP_1W', 'MP_1M', 'MP_1Q', 'MP_6M', 'MP_1Y','ESTX_50_PR.EUR_Futures_Closeprice','ESTX_50_PR.EUR_Futures_Volume','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIFJP':['date', 'close_price', 'daily_close_change', 'weekly_close_change',
       'monthly_close_change', 'yearly_close_change','Volume', 'daily_volume_change',
       'weekly_volume_change', 'monthly_volume_change', 'Yearly_volume_change',
       'yearhigh', 'yearlow', 'index_day_closeprice', 'beta_5yr', 'beta_2yr',
       'beta_1yr', 'marketcap', 'Share Outstanding', 
       'Dividend yield', 'RSI Dividend ADJ', 'Daily value traded', 'vol_5yr',
       'vol_2yr', 'vol_1yr', '1Y', '2Y', '3Y', '4Y', '5Y', '6Y', '7Y', '8Y',
       '9Y', '10Y', '15Y', '20Y', '25Y', '30Y', '40Y', 'pbv rolledup', 'price_sales rolledup',
       'pe_excl rolledup', 'total_rev rolledup','F_score_rolledup','M_score_rolledup','I_score_rolledup','ER_rolledup', 'equity_prediction', 'APO', 'MOM',
       'PPO', 'RSI', 'MACD_0', 'MACD_1', 'MACD_2', 'MACDFIX_0', 'MACDFIX_1',
       'MACDFIX_2', 'STOCHRSI_0', 'STOCHRSI_1', 'Nikkei_225_closeprice',
       'etf_KW_pos', 'etf_KW_neg', 'macro_pos', 'macro_neg',
       'japan_consumer_discretionary_pos', 'japan_consumer_discretionary_neg',
       'japan_communication_services_pos', 'japan_communication_services_neg',
       'japan_information_technology_pos', 'japan_information_technology_neg',
       'japan_health_care_pos', 'japan_health_care_neg',
       'japan_industrials_pos', 'japan_industrials_neg', 'common_kw_pos',
       'holdings_neg', 'GDP', 'CPI', 'PPI',
       'CPI_forecast', 'interest_rate_spread', 'unemployement', 'equity_market',
       'japan_equity', 'long_term_bond', 'effective_rate', 'call_money',
       'opinion_survey','interbank rates','interest rates','Lending data',
       'self_predictions', 'corr_predictions', 'mp_1d', 'mp_1w', 'mp_1m',
       'mp_1q', 'mp_6m', 'mp_1y','Nikkei_Futures_Closeprice','Nikkei_Futures_Volume','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIFCN':['date','close_price', 'daily_close_change', 'weekly_close_change',
       'monthly_close_change', 'yearly_close_change','Volume', 'daily_volume_change',
       'weekly_volume_change', 'monthly_volume_change', 'Yearly_volume_change',
       'yearhigh', 'yearlow', 'index_day_closeprice', 'beta_5yr', 'beta_2yr',
       'beta_1yr', 'marketcap', 'Share Outstanding', 
       'Dividend yield', 'RSI Dividend ADJ', 'Daily value traded', 'vol_5yr',
       'vol_2yr', 'vol_1yr', 'ChinaBond Government Bond Yield Curve_3M',
       'ChinaBond Government Bond Yield Curve_6M',
       'ChinaBond Government Bond Yield Curve_1Y',
       'ChinaBond Government Bond Yield Curve_3Y',
       'ChinaBond Government Bond Yield Curve_5Y',
       'ChinaBond Government Bond Yield Curve_7Y',
       'ChinaBond Government Bond Yield Curve_10Y',
       'ChinaBond Government Bond Yield Curve_30Y',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_3M',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_6M',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_1Y',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_3Y',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_5Y',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_7Y',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_10Y',
       'ChinaBond Financial Bond of Commercial Bank Yield Curve (AAA)_30Y',
       'ChinaBond CP&Note Yield Curve (AAA)_3M',
       'ChinaBond CP&Note Yield Curve (AAA)_6M',
       'ChinaBond CP&Note Yield Curve (AAA)_1Y',
       'ChinaBond CP&Note Yield Curve (AAA)_3Y',
       'ChinaBond CP&Note Yield Curve (AAA)_5Y',
       'ChinaBond CP&Note Yield Curve (AAA)_7Y',
       'ChinaBond CP&Note Yield Curve (AAA)_10Y',
       'ChinaBond CP&Note Yield Curve (AAA)_30Y', 'pbv rolledup',
       'price_sales rolledup', 'pe_excl rolledup', 'total_rev rolledup','F_score_rolledup','M_score_rolledup','I_score_rolledup','ER_rolledup',
       'equity_prediction', 'APO', 'MOM', 'PPO', 'RSI', 'MACD_0', 'MACD_1', 'MACD_2',
       'MACDFIX_0', 'MACDFIX_1', 'MACDFIX_2', 'STOCHRSI_0', 'STOCHRSI_1',
       'HIS_closeprice', 'etf_KW_pos', 'etf_KW_neg', 'macro_pos', 'macro_neg',
       'china_consumer_discretionary_pos', 'china_consumer_discretionary_neg',
       'china_communication_services_pos', 'china_communication_services_neg',
       'china_information_technology_pos', 'china_information_technology_neg',
       'china_consumer_staples_pos', 'china_consumer_staples_neg',
       'china_financials_pos', 'china_financials_neg', 'china_health_care_pos',
       'china_health_care_neg', 'holdings_pos', 'holdings_neg',
       'uncertainity_index',  'Equity_market_china', 'Confidence_index',
       'oecd_leading_indicator', 'business_tendency_survey', 'net_current_account','self_predictions', 'corr_predictions', 'mp_1d', 'mp_1w', 'mp_1m',
       'mp_1q', 'mp_6m', 'mp_1y','HANG_SENG_INDEX_Futures_Closeprice','HANG_SENG_INDEX_Futures_Volume','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIG0GC':['date','close_price', 'daily_close_change', 'weekly_close_change',
       'monthly_close_change', 'yearly_close_change','Volume', 'daily_volume_change',
       'weekly_volume_change', 'monthly_volume_change','yearly_volume_change','yearhigh', 'yearlow', 'index_day_closeprice',
       'beta_5yr', 'beta_2yr', 'beta_1yr', 'marketcap', 'Share Outstanding', 'Dividend yield', 'RSI Dividend ADJ', 'Daily value traded',
       'vol_5yr', 'vol_2yr', 'vol_1yr', '1Mo', '2Mo', '3Mo', '4Mo', '6Mo',
       '1Yr', '2Yr', '3Yr', '5Yr', '7Yr', '10Yr', '20Yr', '30Yr',
       'Production: Mine_gold', 'Production: Refinery: Primary_gold',
       'Production: Refinery: Secondary_gold', 'Imports_gold', 'Exports_gold',
       'Consumption, reported_gold', 'Stocks, yearend, Treasury_gold',
       'Price, dollars per ounce_gold',
       'Employment, mine and mill, number_gold',
       'Net import reliance as a percent of apparent consumption_gold',
       'equity_prediction', 'ADX', 'APO', 'CCI', 'DX', 'MFI', 'MOM', 'PPO', 'ULTOSC',
       'WILLR', 'MACD_0', 'MACD_1', 'MACD_2', 'MACDFIX_0', 'MACDFIX_1',
       'MACDFIX_2', 'STOCH_0', 'STOCH_1', 'STOCHF_0', 'STOCHF_1', 'STOCHRSI_0',
       'STOCHRSI_1', 'etf_kw_pos', 'etf_kw_neg', 'macro_bond_kw_pos',
       'macro_bond_kw_neg', 'common_kw_pos', 'common_kw_neg','inflation_rate', 'cpi', 'opinion_survey', 'unemployment', 'ppi','gold_future_closeprice',
       'yield_maturity', 'mp_1d', 'mp_1w', 'mp_1m', 'mp_1q', 'mp_6m', 'mp_1y','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIFU10':['date','close_price', 'yearhigh', 'yearlow', 'IEF_close_price',
       'daily_close_change', 'weekly_close_change', 'monthly_close_change',
       'yearly_close_change', 'vol_3m', 'vol_6m',
       'vol_5yr', 'vol_2yr', 'vol_1yr', '5Yr', '7Yr',
       '10Yr', '20Yr', '30Yr', 'etf_kw_pos', 'etf_kw_neg', 'macro_bond_kw_pos',
       'macro_bond_kw_neg', 'common_kw_pos', 'common_kw_neg', 'equity_prediction',
       'APO', 'MOM', 'PPO', 'RSI', 'MACD_0', 'MACD_1', 'MACD_2', 'MACDFIX_0',
       'MACDFIX_1', 'MACDFIX_2', 'STOCHRSI_0', 'STOCHRSI_1', 'DJI_closeprice',
       'SPX_closeprice', 'CONSUMER', 'interest_rate', 'CPI', 'PPI', 'unemployement_rate', 'PAYEMS', 'Repo_rate',
       'treasury_spreads', 'treasury_change', 'GDP','Economic Policy Uncertainty Index for United States', 'Wilshire US Real Estate Investment Trust Total Market Index (Wilshire US REIT)','CBOE Gold ETF Volatility Index','inflation_breakeven', 'inflation_expectation', 'MP_1D',
       'MP_1W', 'MP_1M', 'MP_1Q', 'MP_6M', 'MP_1Y','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIFJ10':['date', 'close_price', 'daily_close_change', 'weekly_close_change',
       'monthly_close_change', 'yearly_close_change','yearhigh','yearlow',
       'vol_5yr', 'vol_2yr', 'vol_1yr', '10Y', 'prediction', 'APO', 'MOM',
       'PPO', 'RSI', 'MACD_0', 'MACD_1', 'MACD_2', 'MACDFIX_0', 'MACDFIX_1',
       'MACDFIX_2', 'STOCHRSI_0', 'STOCHRSI_1', 'Nikkei_225_closeprice', 'GDP',
       'CPI', 'interbank_rates',
       'callmoney', 'interest_rates', 'ppi',
       'CPI_estim', 'interest_rate_spread', 'lending_data','unemployement_rate','equity_market','japan equity',
     'long term bond','effective rate','opinion survey',
       'etf_KW_pos', 'etf_KW_neg', 'macro_pos', 'macro_neg', 'MP_1D', 'MP_1W', 'MP_1M',
       'MP_1Q', 'MP_6M', 'MP_1Y','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIFE10':['date', 'close_price', 'daily_close_change', 'weekly_close_change',
       'monthly_close_change', 'yearly_close_change','yearhigh','yearlow',
       'vol_5yr', 'vol_2yr', 'vol_1yr', '10Yr Treasury Yield', 'prediction',
       'APO', 'MOM', 'PPO', 'RSI', 'MACD_0', 'MACD_1', 'MACD_2', 'MACDFIX_0',
       'MACDFIX_1', 'MACDFIX_2', 'STOCHRSI_0', 'STOCHRSI_1', 'SX5E_closeprice',
       'interest_rate_immediate','PPI','interest_rate','CPI','GDP','interbank_rate', 'marginal_lending_rate','Inflation','economic index','deposits','corporate bond','business tendency survey','uncertainty index','unemployement_rate','europe market','broad money','etf_KW_pos', 'etf_KW_neg', 'macro_pos', 'macro_neg',
        'MP_1D', 'MP_1W',
       'MP_1M', 'MP_1Q', 'MP_6M', 'MP_1Y', 'LT_borrowing_cost','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7'],
                          'BNPIDSBU':['date','close_price',
                    'daily_close_change',
                    'weekly_close_change',
                    'monthly_close_change',
                    'yearly_close_change',
                    'yearhigh','yearlow',
                    'vol_5yr',
                    'vol_2yr',
                    'vol_1yr',
                    'equity_prediction',
                    'APO',
                    'MOM',
                    'PPO',
                    'RSI',
                    'MACD_0',
                    'MACD_1',
                    'MACD_2',
                    'MACDFIX_0',
                    'MACDFIX_1',
                    'MACDFIX_2',
                    'STOCHRSI_0',
                    'STOCHRSI_1',
                    'Production: Primary_aluminium',
                    'Production: Secondary (from old scrap)_aluminium',
                    'Production: Secondary (from new scrap)_aluminium',
                    'Imports for consumption_aluminium',
                    'Imports for consumption: Crude and semimanufactures_aluminium',
                    'Imports for consumption: Scrap_aluminium',
                    'Exports_aluminium',
                    'Exports: Crude and semimanufactures_aluminium',
                    'Exports: Scrap_aluminium',
                    'Shipments from Government stockpile excesses_aluminium',
                    'Consumption, apparent_aluminium',
                    'Supply, apparent_aluminium',
                    'Price, ingot, average U.S. market (spot),cents per pound_aluminium',
                    'Stocks: Aluminum industry, yearend_aluminium',
                    'Stocks: LME, U.S. warehouses, yearend_aluminium',
                    'Employment, number_aluminium',
                    'Employment, primary reduction_aluminium',
                    'Emplyment, Secondary smelter_aluminium',
                    'Net import reliance as a percent of apparent consumption_aluminium',
                    'Production: Mine_gold',
                    'Production: Refinery: Primary_gold',
                    'Production: Refinery: Secondary_gold',
                    'Imports_gold',
                    'Exports_gold',
                    'Consumption, reported_gold',
                    'Stocks, yearend, Treasury_gold',
                    'Price, dollars per ounce_gold',
                    'Employment, mine and mill, number_gold',
                    'Net import reliance as a percent of apparent consumption_gold',
                    'Production: Mine_silver',
                    'Production: Refinery: Primary_silver',
                    'Production: Refinery: Secondary_silver',
                    'Imports for consumption_silver',
                    'Exports_silver',
                    'Consumption, apparent_silver',
                    'Stocks, yearend: Treasury Department_silver',
                    'Stocks, yearend: COMEX_silver',
                    'Stocks, yearend: Department of Defense_silver',
                    'Stocks, yearend: Exchange Traded Fund_silver',
                    'Stocks, yearend: Industry_silver',
                    'Price, dollars per troy ounce_silver',
                    'Employment, mine and mill, number_silver',
                    'Net import reliance as a percent of apparent consumption_silver',
                    'Production: Mine_copper',
                    'Production: Refinery: Primary_copper',
                    'Production: Refinery: Secondary_copper',
                    'Copper from all old scrap_copper',
                    'Imports for consumption: Ores and concentrates_copper',
                    'Imports for consumption: Refined_copper',
                    'Imports for consumption: Unmanufactured_copper',
                    'Exports: Ores and concentrates_copper',
                    'Exports: Refined_copper',
                    'Exports: Unmanufactured_copper',
                    'Consumption, reported_copper',
                    'Consumption, apparent_copper',
                    'Price, average, cents per pound: Domestic producer, cathode_copper',
                    'Price, average, cents per pound: London Metal Exchange_copper',
                    'Price, average, cents per pound: U.S. producer, cathode_copper',
                    'Price, average, cents per pound: COMEX_copper',
                    'Stocks, yearend, refined_copper',
                    'Employment, mine and mill, thousands_copper',
                    'Employment, mine and plant, number_copper',
                    'Net import reliance as a percent of apparent consumption_copper',
                    'Production: Mine, lead in concentrates_lead',
                    'Production: Primary refinery_lead',
                    'Production: Secondary refinery_lead',
                    'Imports for consumption: Lead in concentrates_lead',
                    'Imports for consumption: Refined metal_lead',
                    'Exports: Lead in concentrates_lead',
                    'Exports: Refined metal_lead',
                    'Shipments from Government stockpile excesses, metal_lead',
                    'Consumption: Reported_lead',
                    'Consumption: Apparent_lead',
                    'Price, average, cents per pound: North American Producer_lead',
                    'Price, average, cents per pound: North American Market_lead',
                    'Price, average, cents per pound: London Metal Exchange_lead',
                    'Stocks, metal, producers, consumers, yearend_lead',
                    'Employment: Mine and mill, number_lead',
                    'Employment: Smelter primary, number_lead',
                    'Employment: Secondary smelters, refineries_lead',
                    'Net import reliance as a percentage of apparent consumption_lead',
                    'Production: Mine_nickel',
                    'Shipments of purchased scrap_nickel',
                    'Imports: Ores and concentrates_nickel',
                    'Imports: Primary_nickel',
                    'Imports: Secondary_nickel',
                    'Exports: Ores and concentrates_nickel',
                    'Exports: Primary_nickel',
                    'Exports: Secondary_nickel',
                    'Consumption, Reported, primary_nickel',
                    'Consumption, Reported, secondary_nickel',
                    'Consumption, Apparent, primary_nickel',
                    'Price, average annual, London Metal Exchange: Cash, dollars per metric ton_nickel',
                    'Price, average annual, London Metal Exchange: Cash, dollars per pound_nickel',
                    'Stocks: Consumer, yearend_nickel',
                    'Stocks: Producer, yearend_nickel',
                    'Stocks: LME U.S. warehouses_nickel',
                    'Net import reliance as a percent of apparent consumption_nickel',
                    'Production: Mine, zinc in ore and concentrate_zinc',
                    'Production: Primary slab zinc_zinc',
                    'Production: Secondary slab zinc_zinc',
                    'Imports for consumption: Ore and concentrate_zinc',
                    'Imports for consumption: Refined zinc_zinc',
                    'Exports: Ore and concentrate_zinc',
                    'Exports: Refined zinc_zinc',
                    'Shipments from Government stockpile_zinc',
                    'Consumption: Apparent, refined zinc_zinc',
                    'Consumption: Apparent, all forms_zinc',
                    'Price, average, cents per pound: North American_zinc',
                    'Price, average, cents per pound: London Metal Exchange, cash_zinc',
                    'Stocks, slab zinc, yearend_zinc',
                    'Employment: Mine and mill, number_zinc',
                    'Employment: Smelter primary, number_zinc',
                    'Net import reliance as a percentage of apparent consumption: Refined zinc_zinc',
                    'Net import reliance as a percentage of apparent consumption: All forms of zinc_zinc',
                    'etf_KW_pos',
                    'etf_KW_neg',
                    'gold_future_closeprice',
                    'aluminium_future_closeprice',
                    'silver_future_closeprice',
                    'copper_future_closeprice',
                    'WTI_crude_oil',
                    'etf_volatality', 'Industrial Production: Mining, Quarrying, and Oil and Gas Extraction: Crude Oil', 'import',
                        'mining', 'export', 'ppi_crude_oil', 'ppi_petrol',
                    'ppi_fuel', 'ppi_oil', 'manufaturing', 'Industrial Production: Manufacturing: Non-Durable Goods: Petroleum Refineries','MP_1D',
                    'MP_1W',
                    'MP_1M',
                    'MP_1Q',
                    'MP_6M',
                    'MP_1Y','lstm_day_1','lstm_day_2','lstm_day_3','lstm_day_4','lstm_day_5','lstm_day_6','lstm_day_7']}

S3_paths:
    #upload_paths
    upload_techindicators_bucket: etf-predictions
    upload_techindicators_path: Monthly/BNP_Paribas/Technical_indicators/{etf}.csv
    upload_rolledup_bucket : micro-ops-output
    upload_rolledup_etfdata_path : FMI_rollup_new_model/{etf}/{etf}_{date}.csv
    upload_rolledup_rawdata_path : FMI_rollup_new_model/raw/{etf}/{etf}_{date}.csv
    upload_input_data_bucket : etf-predictions
    upload_input_data_path : Monthly/new_BNP_Paribas/new_training_data/{etf}.csv
    upload_daily_predictions_bucket : etf-predictions
    upload_daily_predictions_path : Monthly/new_BNP_Paribas/BNP_retrained/Daily_run_predictions/{etf}/daily_pred_{etf}_{date}.csv
    upload_daily_metrics_bucket : etf-predictions
    upload_daily_metrics_path : Monthly/new_BNP_Paribas/Daily_Run_metrics/{etf}/daily_metrics_{etf}_{date}.csv
    upload_cons_etf_bucket : etf-predictions
    upload_cons_etf_path : Monthly/new_BNP_Paribas/Client/Final_data/{etf}.csv

    #read_from_paths
    rolledup_etf_holdings_bucket : equbot-scraper
    rolledup_etf_holdings_path : etf-holdings/latest_etf_holding/{etf}.xlsx
    aigo_sentiment_monthly_bucket : micro-ops-output
    aigo_sentiment_monthly_path : keywords_sentiment/daily/{date}/{date}_sentiment.json
    macro_data_bucket: micro-ops-output
    macro_data_path : macro-historical-data/final-predictions/daily/{geo}/{asset_type}/daily/{date}.csv
    aigo_technical_indicators_bucket : micro-ops-output
    aigo_technical_indicators_path : technical_indicators/technical_data_etf_modified_latest.csv
    bnp_technical_indicators_bucket : etf-predictions
    bnp_technical_indicators_path : Monthly/BNP_Paribas/Technical_indicators/{etf}.csv
    rolledup_data_bucket : micro-ops-output
    rolledup_data_path : FMI_rollup_new_model/{etf}/{etf}_{date}.csv
    etf_KW_sentiment_bucket : etf-predictions
    etf_KW_sentiment_path : Daily/new_BNP_Paribas/etf_specific_data/{date}_{ticker}.csv
    macro_sentiment_bucket : etf-predictions
    macro_sentiment_path : Daily/new_BNP_Paribas/macro_data/{date}_{filename}.csv
    industry_sentiment_bucket : etf-predictions
    industry_sentiment_path : Daily/new_BNP_Paribas/industry_data/{date}_{industry}.csv
    holdings_sentiment_bucket : etf-predictions
    holdings_sentiment_path : Daily/new_BNP_Paribas/holdings_data/{date}_{filename}.csv
    common_keywords_bucket : etf-predictions
    common_keywords_path : Daily/new_BNP_Paribas/industry_data/{date}_{filename}.csv
    gold_bucket : etf-predictions
    gold_path : Monthly/BNP_Paribas/Commodity_data_processed/Gold_commodity.csv
    lt_borrowing_cost_bucket : etf-predictions
    lt_borrowing_cost_path : Monthly/BNP_Paribas/Scheduled_data/Bond_data/Long_term_cost_of_borrowing.csv
    aluminium_bucket : etf-predictions
    aluminium_path : Monthly/BNP_Paribas/Commodity_data_processed/Aluminium_commodity.csv
    silver_bucket : etf-predictions
    silver_path : Monthly/BNP_Paribas/Commodity_data_processed/Silver_commodity.csv
    copper_bucket : etf-predictions
    copper_path : Monthly/BNP_Paribas/Commodity_data_processed/Copper_commodity.csv
    nickel_bucket : etf-predictions
    nickel_path : Monthly/BNP_Paribas/Commodity_data_processed/Nickel_commodity.csv
    zinc_bucket : etf-predictions
    zinc_path : Monthly/BNP_Paribas/Commodity_data_processed/Zinc_commodity.csv
    lead_bucket : etf-predictions
    lead_path : Monthly/BNP_Paribas/Commodity_data_processed/Lead_commodity.csv
    deployment_bucket : etf-predictions
    deployment_path : Monthly/new_BNP_Paribas/BNP_new_deployment_mapping/BNP_new_deployment_mapping.csv
