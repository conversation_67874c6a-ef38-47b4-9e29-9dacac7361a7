# modules
import os
import yaml
import json
import pandas as pd
from io import StringIO
from datetime import datetime
from eq_common_utils.utils.config.s3_config import s3_config

# function definitions
def read_configuration():

    
    # read the configuration file
    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)

    etfs      = config['init']['etfs'].split(', ')
    src       = config['init']["final_destination"]
    log_folder= config['init']["AutoAI_log_folder"]
    bucket= config['upload']['bucket']
    path  = config['upload']['path']

    # remove etfs, for which I do not have a prediction
    try:
        # read
        absent= None
        with open(f'./{log_folder}/no_predictions.txt', 'r') as f:
            absent= set(f.read().split('\n')[1:]) #ignore the first line, because it is a comment
        # delete
        tmp= []
        for etf in etfs:
            if etf not in absent:
                tmp.append(etf)
    except:
        tmp= etfs

    return (tmp, src, log_folder, bucket, path)

# upload to s3
def upload(df, bucket_name, folder_path, file_prefix, txt, log, error_logs):
    client = s3_config()
    
    try:
        client.write_advanced_as_df(df= df, bucket= bucket_name, path= f'{folder_path}/{file_prefix}')
        log.append(txt)
    except:
        error_logs.append(file_prefix)

# download from s3
def download(bucket_name, object_key):
    """
    Desc.: Read the file at the path f'{bucket_name}/{object_key}' from S3
    returns a DataFrame
    """
    client = s3_config()

    # 1a GET data
    df= client.read_as_dataframe(bucket= bucket_name, path= object_key)
    
    # 2 sort
    df['date'] = df['date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d'))
    #df.sort_values(by= ['date'], inplace= True)
    
    return df
