# modules
import os
import yaml
import json
import pandas as pd
from datetime import datetime

# function definitions
def read_configuration():
    
    # read the configuration file
    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)

    etfs      = config['init']['etfs'].split(', ')
    src_pred  = config['init']["denorm_pred"]
    src_feat  = config['init']["pred_folder"]
    dst       = config['init']["final_destination"]
    target    = config['init']["target"]
    pred_col  = config['init']["pred_col"]
    log_folder= config['init']["AutoAI_log_folder"]
    flag      = config['init']["predictions_for_training_set"]

    # remove etfs, for which I do not have a prediction
    try:
        # read
        absent= None
        with open(f'./{log_folder}/no_predictions.txt', 'r') as f:
            absent= set(f.read().split('\n')[1:]) #ignore the first line, because it is a comment
        # delete
        tmp= []
        for etf in etfs:
            if etf not in absent:
                tmp.append(etf)
    except:
        tmp= etfs

    return (tmp, target, pred_col, src_pred, src_feat, dst, flag)

def read_csv(path):
    df= pd.read_csv(path)
    df['date'] = df['date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d'))
    df.sort_values(by= ['date'], inplace= True)
    return df
