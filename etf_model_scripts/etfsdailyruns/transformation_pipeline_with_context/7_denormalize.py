"""
Desc.: Used to denormalize the predictions (generated by AutoAI); because the target was normalized by me.
Dependencies:
 1. ./4_logs/no_predictions.txt (if it was created, during training)
 2. ./6_pred/*.csv
"""
from utilities_7 import *

# MAIN
(etfs, pred_col, target, src_data, src_stats, dst, flag) = read_configuration()

# 1 read the stats (per ETF)
with open(f'./{src_stats}/stats.txt', 'r') as f:
    stats = {}
    for line in f:
        record = json.loads(line)
        stats[record['file']] = {'mean': record['mean'], 'std': record['std']}

# 2 denormalize (per ETF)
for etf in etfs:

    if flag:
        path= f'./{src_data}/train_pred/{etf}_train_pred.csv'
        if not os.path.isfile(path):
            continue
        df = pd.read_csv( path, usecols= ['date', pred_col, target] )
    else:
        path= f'./{src_data}/{etf}_pred.csv'
        if not os.path.isfile(path):
            continue
        df = pd.read_csv( path, usecols= ['date', pred_col] )    
 
    cols = df.columns.to_list()
    cols.pop(cols.index('date'))
    for col in cols:
        df[col] = df[col].apply(lambda x: x*stats[etf]['std'] + stats[etf]['mean'])
    if target in set(cols):
        _dir_ = f'{dst}/train_pred' 
        if not os.path.isdir(_dir_):
            os.mkdir(_dir_)
        path= f'./{_dir_}/{etf}_train_pred.csv'
    else:
        path= f'./{dst}/{etf}_pred.csv'
    df.to_csv(path, index= False)

