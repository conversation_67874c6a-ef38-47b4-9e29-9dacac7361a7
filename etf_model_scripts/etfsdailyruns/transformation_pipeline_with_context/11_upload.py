"""
Desc.: Appends to the file on S3, which contains the predictions
"""
from utilities_11 import *

# read etfs for which predictions are available
(etfs, src, log_folder, bucket, path)= read_configuration()

# this list shall store the names of etfs, for which an upload fails
error_logs= []
# this list shall store the names of etfs, for which an upload succeeds
log= []

for i, etf in enumerate(etfs):
    # read from storage
    new= pd.read_csv(f'./{src}/{etf}_predictions.csv')
    new['date'] = new['date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d'))
    new_date= new.loc[:, 'date'].iloc[-1]
    #new.sort_values(by= ['date'], inplace= True)

    # download from S3
    file_name= f'{etf}_predictions.csv'
    # if the file exists already, append to it
    # This line will cause an error, if the file does not exist already on S3
    old= download(bucket_name= bucket, object_key= f'{path}/{file_name}')
    
    # appends new rows, to the old dataframe
    last_date= old.loc[:, 'date'].iloc[-1]
    cond= (new.loc[:, 'date'] > last_date)
    tmp= new.loc[cond, :]
    df= pd.concat(objs= [old, tmp], axis= 0, join= 'outer') #inner
    df.sort_values(by= ['date'], inplace= True)

    # pretty printing
    txt= f'{i}) {etf}, \tlast: {last_date.date()}, \tcurrent: {new_date.date()}'
    print(txt)
    
    # upload
    upload(df, bucket, path, file_name, txt, log, error_logs)

if len(error_logs) > 0:
        with open(f'./{log_folder}/failed_uploads.txt', 'w') as f:
            f.write(f'{datetime.now()}\nFailed to upload the predictions for the following ETFs,\n' + '\n'.join(error_logs))
else:
    if os.path.isfile(f'./{log_folder}/failed_uploads.txt'):
        os.remove(f'./{log_folder}/failed_uploads.txt')

if len(log) > 0:
        with open(f'./{log_folder}/lastest_run.txt', 'w') as f:
            f.write(f'{datetime.now()}\nSuccessfully uploaded the predictions for the following ETFs,\n' + '\n'.join(log))
