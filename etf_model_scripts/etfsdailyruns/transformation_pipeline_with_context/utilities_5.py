# MODULES
from eq_common_utils.utils.config.s3_config import s3_config
import ibm_watsonx_ai
from ibm_watsonx_ai import APIClient
from ibm_watsonx_ai.deployment import Batch
from datetime import timedelta, date
from datetime import datetime
import multiprocessing as mp
from time import sleep
import pandas as pd
import numpy as np
import regex as re
import warnings
import time
import json
import yaml
import sys
import os
warnings.filterwarnings('ignore')

# function definitions

def read_configuration():
    
    # read the configuration file
    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)

    ## read config
    src        = config['init']["data_folder"] 
    dst        = config['init']["pred_folder"]
    log_folder = config['init']["AutoAI_log_folder"]
    target     = config['init']["target"]
    pred_col   = config['init']["pred_col"]
    model_name= config['generate_predictions']['model_name']
    
    return (config, src, dst, log_folder, target, pred_col, model_name)


def generate_predictions(Queue, etf, client, wml_credentials, space_id, dataset= 'test'):

    # 1 init
    ## error check
    if not os.path.isfile('./config.yaml'):
        Queue.put( (etf, f'ERROR 404: not found \'config.yaml\'') )
        return None
        
    ## read the configuration file
    (config, src, dst, log_folder, target, pred_col, model_name)= read_configuration()
    
        
    # 2 read dataframe
    path= f'{src}/{etf}_{dataset}.csv'

    ## error check
    if not os.path.isfile(path):
        Queue.put( (etf, f'ERROR 404: not found \'{path}\'') )
        return None
        
    df = pd.read_csv(path)

    # generate predictions for those dates, which are not associated with a prediction
    if 'test' == dataset:
        cond= df.loc[:, target].isna()
    
        ## error check
        if sum(cond) == 0:
            Queue.put( (etf, f'ERROR: No new data in \'{path}\'') )
            return None
    
        ## filter rows
        df= df.loc[cond, :]
    
    
    # 3 read deployment ID
    ## error check
    path= f'{log_folder}/deployment_details.csv'
    if not os.path.isfile(path):
        Queue.put( (etf, f'ERROR 404: not found \'{path}\'') )
        return None

    deployment_details = pd.read_csv(f'{log_folder}/deployment_details.csv')

    cond = deployment_details['deployment_name'] == model_name.format(etf= etf)
    if sum(cond) == 0:
        Queue.put( (etf, f'ERROR 404: not found a deployment ID for \'{etf}\' at path \'{path}\'') )
        return None
        
    deployment_id = deployment_details[cond]['deployment_id'].values[0]

    # 4 generate predictions
    try:
        service = Batch(wml_credentials, source_space_id= space_id)
        service.get(deployment_id)
        df_1= df.drop(columns=['date', target], axis=1)
        scoring_params = service.run_job(df_1, background_mode=False)
        job_id = scoring_params["metadata"].get("id")
        job_details = client.deployments.get_job_details(job_id)
        values = np.array(job_details['entity']['scoring']['predictions'][0]['values']).flatten()
        
    except:
        Queue.put( (etf, f'ERROR: Failed to use IBM to generate predictions for {etf}. Debugging required.') )
        return None

    # 5 store
    df[pred_col] = values
    df.reset_index(drop=True, inplace=True)
    if 'test' == dataset:
        df.drop([target], axis=1, inplace= True)

    path= None
    if dataset == 'test':
        path = f'{dst}/{etf}_pred.csv'
    elif dataset == 'train':
        _dir_ = f'{dst}/train_pred' 
        if not os.path.isdir(_dir_):
            os.mkdir(_dir_)
        path = f'{_dir_}/{etf}_train_pred.csv'
    else:
        path = f'{dst}/{etf}_{dataset}_pred.csv'
        
    try:
        df.to_csv(path, index= False)
    except:
        Queue.put( (etf, f'ERROR: Failed to store predicitons for {etf}') )
        return None
        
    Queue.put( (etf, f'SUCCESS: {etf}') )
    return None

def download_deployment_ids():

    # init
    with open('config.yaml', 'r') as f:
        config= yaml.safe_load(f)

    bucket     = config['generate_predictions']['bucket']
    path       = config['generate_predictions']['path']
    no_of_etfs = len(config['init']['etfs'].split(', '))
    dst        = config['init']['AutoAI_log_folder']

    # download all deployment IDs
    client= s3_config()
    df= client.read_as_dataframe(bucket, path)

    # filter the required IDs
    # select last 'no_of_etfs' IDs
    df_subset = df.iloc[-no_of_etfs:, :]
    
    # error check: are the values in 'test_end_date' NaNs?
    col  = 'test_end_date'
    cond = sum( df_subset.loc[:, col].isna() ) == no_of_etfs
    
    # if not, determine the rows where this column contains NaNs
    if not cond:
        df_subset= df.loc[ df.loc[:, col].isna(), :]
        # error check
        assert len(df_subset) == no_of_etfs
    
    df_subset.to_csv(f'./{dst}/deployment_details.csv', index= False)
    