init:
    etfs : DBEEEMGF, DBEEUNGF, DBEEURGF, EEM, EWJ, IWM, QQQ, TLT, BNPIDSBU, BNPIFCN, BNPIFE10, BNPIFEM, BNPIFEU, BNPIFJ10, BNPIFJP, BNPIFU10, BNPIFUS, BNPIG0GC
    target: actual_monthly_returns
    lag : 5
    pred_col : monthly_predictions
    data_folder : 2a_data
    info_folder : 2b_metadata
    AutoAI_log_folder : 4_logs
    pred_folder : 6_pred
    denorm_pred: 8_denorm_pred
    final_destination : 10_final_output
    date : 2025-04-15
    predictions_for_training_set : False

get_data:
    bucket: etf-predictions
    paths: Monthly/aigo/predictions/{etf}_predictions.csv, Monthly/sectors/predictions/{etf}_predictions.csv, Monthly/db/predictions/{etf}_predictions.csv, Monthly/new_BNP_Paribas/updated_training_data/{etf}.csv

upload:
    bucket: etf-predictions
    path: Monthly/transformation_pipeline/pipeline_with_context

generate_predictions:
    api_key: qLDM4dDNVkXs9Eou1gotvaD8jZcIDRd7qkqTyrRp-kZe
    location: us-south
    wml_url : .ml.cloud.ibm.com

    # for the function create_new_deployment_space()
    storage_type: bmcos_object_storage,
    storage_resource_crn: "crn:v1:bluemix:public:cloud-object-storage:global:a/4f872a4264a50cbd6da50add5eb35fd8:964b66b9-7350-4529-b9d0-9700e30fea75::"
    
    compute_name: Machine Learning-a8,
    compute_crn: "crn:v1:bluemix:public:pm-20:us-south:a/4f872a4264a50cbd6da50add5eb35fd8:8e2ee632-8bd2-4e10-8f3d-17e4f1503b52::"

    space_id : 7772dba2-8371-4aa5-8fde-e3f1227e503f
    bucket_name: histetfdata
    model_storage_path: portfolio_model/Saved_Models/Daily/

    datasource_name : bluemixcloudobjectstorage
    iam_url: https://iam.cloud.ibm.com/identity/token

    model_name : cxt_q1_2025_{etf}_train
    bucket : etf-predictions
    path   : 'Monthly/transformation_pipeline/pipeline_with_context/deployments/pipeline_with_context_deployments.csv'

meta_data:
    bucket     : 'etf-predictions' 
    path       : 'Monthly/transformation_pipeline/pipeline_with_context/meta_data/compressed.zip'
    dir        : 2b_metadata
    stats_file : 2a_data/stats.txt

    
