"""
Desc.: This program performs feature-engineering on the input numerical-dataset.
Dependencies:
    1. utilities.py : it contains function-definitions
    2. config.py    : configuration file

"""
# Estimate of time for execution: 3 min
from time import time
from utilities_1a import *
warnings.filterwarnings("ignore")

s= time()
# 0 initialization
# read the ETFs (list), the target-column's name & the [number of] lags [required per row] (int)
(etfs, target, lag, dst, split_date, info_folder) = read_configuration()

# correction: from index x till 0, equals (x +1) lags, so
lag -= 1 # Now I have x lags


# to log errors
log_mixed_datatypes= []
log_no_data= []
## stationarize
log_make_stationary= {}
drop_cols= {}
## pca
log_stats= []
log_pca= []
## to store each ETF's statistics (to denormalize the predictions)
stats= []
## to store the columns used, from the raw-input
etf_cols= {}


# FE
for etf in etfs:
    
    # 1 read the data, from S3
    print(etf)
    org = read_csv(etf, target, log_no_data)
    
    # error check
    if org.empty:
        log_no_data.append(etf)
        continue
        
    # 2 Check whether each column contains a consistent data-type
    columns = org.columns.to_list()
    _flag_= detect_mixed_datatypes(org, columns, log_mixed_datatypes)
    # error check
    if _flag_:
        log_mixed_datatypes.append(etf)
        continue
    
    # 3a split the data, based on date

    # 3.a.i to include context, I need the 'lag' rows, which precede 'split_data'
    # 3.a.ii '-1' because using first-differencing (to impose stationarity) removes 1 row
    split_idx = org[org.loc[:, 'date'] >= split_date].iloc[0].name - lag -1 
    df_train = org[org.index <  split_idx]
    
    # 3b error check
    if len(df_train) == 0:
        log_no_data.append(etf)
        continue
    
    # 4 denoise data
    df_train = denoise( df_train )
    df_train.reset_index(inplace= True, drop= True)
    
    # 5 Convert MCC to AMR
    df_train[target] = df_train[target].shift(periods= -22)
    
    # ~ Additionaly, set the index as the date
    df_train.set_index(keys= ['date'], inplace= True, drop= True)

    
    # 6 impose-stationarity
    
    # remove the target & date
    columns.pop(columns.index(target))
    columns.pop(columns.index('date'))

    for col in columns:
        # perform the Augmented-Dicky-Fuller test to detect non-stationary-features
        # reference: https://www.youtube.com/watch?v=1opjnegd_hA
        try:
            p= round(adfuller( df_train.loc[:, [col]].dropna(), maxlag= 11, regression= 'ct')[1] , 3)
            if p > 0.05:
                # use first-differencing to make these features stationary
                df_train.loc[:, col]= df_train.loc[:, col].diff(periods= 1)
                # keep a record
                if etf not in log_make_stationary:
                    log_make_stationary[etf]= []
                log_make_stationary[etf].append(col)
        except:
            df_train.drop([col], axis=1, inplace= True)
            columns.pop(columns.index(col))
            if etf not in drop_cols:
                    drop_cols[etf]= []
            drop_cols[etf].append(col)

    """
    store the column-names, which shall be used for FE;
    storing this info. on disk ensures that these columns are retained before FE for inferencing
    """
    etf_cols[etf]= deepcopy(columns)
    etf_cols[etf].extend(['date'])
    
    # the first-row would have become null (due to the above-differencing), so
    df_train.dropna(inplace= True) # the last 22 rows are also null (due to the .shift() under '5 Convert MCC to AMR')
    
    # 7 rectify multi-colinearity
    # 7a detect clusters
    (clusters, correlated_matrix) = detect_multi_colinearity(df1= df_train, cols= columns)
    
    # 7b normalize the clusters before applying PCA
    # this list stores the names, of all the columns, present in 'clusters' (purpose: for simultaneous-normalization)
    cols = []
    for cluster in clusters:
        cols.extend(cluster)
    
    scaler= normalize_features(df_train, cols)
    log_stats.append(('pca', etf, scaler, cols))
    
    # ~ to fix fragmentaxion of the dataframe; caused due to re-assignment
    df_train = df_train.copy(deep= True)
    
    # 7c apply PCA to replace each cluster, by 1 feature
    (pca_results, pcas) = apply_PCA(df_train, clusters)
    log_pca.append((etf, pca_results, pcas))
    
    # load the current columns
    cols = df_train.columns.to_list()
    
    # decrease resolution
    for c in cols:
        df_train[c] = df_train[c].apply(lambda x: round(x, 4))
    
    # remove target from the list of columns
    cols.pop(cols.index(target))
    
    # 8 normalize all the features
    scaler= normalize_features(df_train, cols)
    log_stats.append(('cols',etf, scaler, cols))
    
    # 9 normalize the target
    target_scaler = StandardScaler()
    
    df_train.loc[:, [target]] = target_scaler.fit_transform(df_train.loc[:, [target]].to_numpy())
    log_stats.append(('target', etf, target_scaler, target ))
    
    # 10 convert each submatrix to a row, then store on disk
    l = f'l{str(lag+1)}'
    df = include_lags(df_train, target, lag)
    # store
    df.to_csv(f'./{dst}/{etf}_train.csv', index= True)
    
    
    # 11 keep a record of the statistics, (to be able) to denormalize the predictions
    #
    std = target_scaler.scale_[0]
    mu  = target_scaler.mean_[ 0]
    
    ## store this data
    req_data = {'file' : etf,
                'mean' : mu,
                'std' : std}
    
    stats.append(req_data)

# store the statistics per ETF, to denormalize the predictions
if len(stats) > 0:
    with open(f'{dst}/stats.txt', 'w') as f:
        for _dict_ in stats:
            f.write( f'{json.dumps(_dict_)}\n' )

if len(log_mixed_datatypes) > 0:
    with open(f'{info_folder}/error_mixed_datatypes.txt', 'w') as f:
        f.write('\n'.join(log_mixed_datatypes))

if len(log_no_data) > 0:
    with open(f'{info_folder}/error_no_data.txt', 'w') as f:
        f.write('\n'.join(log_no_data))

if len(log_make_stationary.keys()) > 0:
    with open(f'{info_folder}/first_diff.txt', 'w') as f:
        f.write(json.dumps(log_make_stationary))

if len(drop_cols.keys()) > 0:
    with open(f'{info_folder}/drop_cols.txt', 'w') as f:
        f.write(json.dumps(drop_cols))
 
for (name, etf, scaler, cols) in log_stats:
    tmp = json.dumps({'etf' : etf, 'cols' : cols})
    with open(f'{info_folder}/scaler_info/{name}_standarize.txt', 'a') as f:
        f.write(tmp + '\n')
    
    # store the transformer
    joblib.dump(scaler, f'./{info_folder}/scaler/{etf}_{name}_scaler.pkl')

    

e= time()
print(f'Time req. {round(e-s)} sec')

tmp= []
for (etf, pca_results, pcas) in log_pca:
    _tmp_= {'etf' : etf,
    'pca_results' : json.dumps(pca_results)}
    tmp.append( json.dumps(_tmp_) )

    for _id, pca in pcas.items():
        joblib.dump(pca, f'./{info_folder}/pca/{etf}_{_id}_pca.pkl')
        

with open(f'{info_folder}/pca_info.txt', 'w') as f:
    f.write('\n'.join(tmp))

with open(f'{info_folder}/raw_columns_used.txt', 'w') as f:
    f.write(json.dumps(etf_cols))

# uploads the meta_data to S3
upload_metadata()
