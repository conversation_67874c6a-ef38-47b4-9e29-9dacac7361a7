"""
Desc.: used to generate predictions, from the models on AutoAI
"""
from utilities_5 import *

# read the configuration file
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

etfs = config['init']['etfs'].split(', ')
no_of_etfs = len(etfs)
log_folder = config['init']["AutoAI_log_folder"]
bucket     = config['generate_predictions']['bucket']
path       = config['generate_predictions']['path']
flag       = config['init']['predictions_for_training_set']
dataset= 'test'
if flag:
    dataset= 'train'

# establishes connection with IBM
api_key= config['generate_predictions']['api_key']
location= config['generate_predictions']['location']

wml_credentials = ibm_watsonx_ai.Credentials(
    api_key =  api_key,
    url =  'https://' + location + config['generate_predictions']['wml_url']
)

#Connecting with API Client, --Error getting IAM Token-- Means your apikey or location is wrong
client = APIClient(wml_credentials)
####################################################
space_id = config['generate_predictions']['space_id']
####################################################

#setting the default space_id till 2 minutes
t_end = time.time() + 60 * 2
while(str(client.set.default_space(space_id))!="SUCCESS" and time.time() < t_end):
    print('Waiting for 20 seconds for Trying to set Space id')
    time.sleep(20)

if client.default_space_id!= space_id:
    pass
    print('Failure with Setting default space_id')

# download the deplyment IDs
download_deployment_ids()

#These list shall store, the names of ETFs which fail to generate predictions
error_logs= []
miss_etfs = []

# generate predictions
s = time.time()

if __name__ == '__main__':

    queue= mp.Queue()
    
    # launch 16 processes for the task
    PROCESSORS= os.cpu_count()
    n = len(etfs)
    
    # select 16 etfs at a time
    for i in range(0, n, PROCESSORS):
        subset= etfs[i:i+PROCESSORS]
        
        # start 16 processes
        ps = []
        for etf in subset:
            p= mp.Process( target= generate_predictions, args= (queue, etf, client, wml_credentials, space_id, dataset, ) )
            p.start()
            ps.append( (p, etf) )
        
        # terminate processes
        for (p, etf) in ps:
            # process-timeout occurs after 5 minutes, regardless of success of failure
            p.join(300)

            # refer: https://docs.python.org/3/library/multiprocessing.html
            # if the child-process times-out, then kill it
            if p.is_alive():
                p.terminate()
                p.join()
                miss_etfs.append( etf )
                error_logs.append( f'ERROR: Failed to receive predictions from IBM for {etf}' )
                
            
    ptrn = re.compile(pattern= 'error', flags= re.IGNORECASE)
    while not queue.empty():
        (etf, msg) = queue.get()
        if ptrn.search(msg):
            miss_etfs.append(etf)
            error_logs.append(msg)

e = time.time()
print(f'Testing time: {round(e-s)} sec')

# log errors
if len(error_logs) > 0:
    
    with open(f'{log_folder}/error_log.txt', 'w') as f:
        f.write(f'Failed to generate predictions for the following ETFs,\n{'\n'.join(error_logs)}')
        
    with open(f'{log_folder}/no_predictions.txt', 'w') as f:
        f.write(f'Failed to generate predictions for the following ETFs,\n{'\n'.join(miss_etfs)}')
else:

    if os.path.isfile(f'{log_folder}/error_log.txt'):
        os.remove(f'{log_folder}/error_log.txt')
        
    if os.path.isfile(f'{log_folder}/no_predictions.txt'):
        os.remove(f'{log_folder}/no_predictions.txt')
    
