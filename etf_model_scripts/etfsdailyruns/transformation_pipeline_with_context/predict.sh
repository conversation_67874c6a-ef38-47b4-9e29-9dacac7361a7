#!/bin/bash

# to ensure that this shell-script stops, if any process (in this sequential-execution) fails
set -e

start_time=$EPOCHREALTIME

echo "0 creating folders"
python3 0_create_folders.py
echo "0 done"

#echo "Duplicating existing *_test.csv files; maybe required for debugging"
#cp 2a_data/*_test.csv 14_yesterday_s_test_files/

#echo "1a performing FE on the training-dataset"
#python3 1a_train_feat_eng.py
#echo "done 1a"

echo "1b performing FE on the testing-dataset"
python3 1b_test_feat_eng.py
echo "done 1b"

echo "5 generating predictions via IBM"
python3 5_generate_predictions.py
echo "done 5"

echo "7 denormalizing predictions"
python3 7_denormalize.py
echo "done 7"

echo "9 merging the predictions with the features"
python3 9_merge.py
echo "done 9"

echo "11 uploading data to S3"
python3 11_upload.py
echo "done 11"

end_time=$EPOCHREALTIME

elapsed=$(bc -l <<< "scale=2; ($end_time - $start_time)/60")
echo "Time taken: $elapsed min"