"""
Desc.: Merging the monthly_predictions with the features used for predicting
Dependencies:
 1. ./4_logs/no_predictions.txt (if it was created, during training)
 2. ./8_denorm_pred/*.csv
"""
from utilities_9 import *

# main
(etfs, target, pred_col, src_pred, src_feat, dst, flag)= read_configuration()

# for each etf, merge the predictions, with the features used (for predicting)
for etf in etfs:

    if flag:
        path_1 = f'./{src_pred}/train_pred/{etf}_train_pred.csv'
        path_2 = f'./{src_feat}/train_pred/{etf}_train_pred.csv'
        path_3 = f'./{dst}/train_pred/{etf}_train_predictions.csv'
        if not os.path.isdir(f'./{dst}/train_pred'):
            os.mkdir(f'./{dst}/train_pred')
    else:
        path_1 = f'./{src_pred}/{etf}_pred.csv'
        path_2 = f'./{src_feat}/{etf}_pred.csv'
        path_3 = f'./{dst}/{etf}_predictions.csv'

    if not os.path.isfile(path_1) or not os.path.isfile(path_2):
        continue
        
    df1= read_csv(path_1)
    df2= read_csv(path_2)
    
    df2.drop(labels= [pred_col], axis= 1, inplace= True)
    if flag:
        df2.drop(labels= [target], axis= 1, inplace= True)
        
    df= pd.merge(left= df1, right= df2, on= 'date', how='inner')
    
    df.to_csv(path_3, index= False)
    