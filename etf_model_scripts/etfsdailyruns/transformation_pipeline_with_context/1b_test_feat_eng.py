"""
Desc.: This program performs feature-engineering on the input numerical-dataset.
Dependencies:
    1. utilities.py : it contains function-definitions
    2. config.py    : configuration file

"""
# Estimate of time for execution: 3 min
from utilities_1b import *
warnings.filterwarnings("ignore")

# 0 initialization

# download the meta_data, if it is not available
download_metadata()

# read the ETFs (list), the target-column's name & the [number of] lags [required per row] (int)
(etfs, target, lag, dst, split_date, info_folder, req_cols) = read_configuration()

first_diff = first_differenced(info_folder)
drop_cols  = dropped_columns(  info_folder)
# correction: from index x till 0, equals (x +1) lags, so
lag -= 1 # Now I have x lags

# to skip the ETFs which do not contain data (after splitting)
flag = False


# to log errors
log= []

# FE
for etf in etfs:

    print(etf)
    
    # 1 read the data, from S3
    org = read_csv(etf, target, log)
    
    # error check
    if org.empty:
        continue
        
    # 2 Check whether each column contains a consistent data-type
    columns = org.columns.to_list()

    
    # 3a split the data, based on date

    # 3.a.ii to include context, I need the 'lag' rows, which precede 'split_data'
    # 3.a.iii '-1' because using first-differencing (to impose stationarity) removes 1 row
    split_idx = org[org.loc[:, 'date'] >= split_date].iloc[0].name - lag -1 
    df_test  = org[org.index >= split_idx]
    
    # 4 denoise data
    df_test = denoise( df_test )
    df_test.reset_index(inplace= True, drop= True)

    # 5 Convert MCC to AMR
    df_test[target] = df_test[target].shift(periods= -22)
    
    # ~ Additionaly, set the index as the date
    df_test.set_index(keys= ['date'], inplace= True, drop= True)

    
    # 6 impose-stationarity
    
    # remove the target & date
    columns.pop(columns.index(target))
    columns.pop(columns.index('date'))

    if drop_cols != None and etf in drop_cols:
        
        for col in drop_cols[etf]:
            df_test.drop([col], axis=1, inplace= True)

     # e. delete the columns which were not seen during training
    req_cols[etf].extend([target])
    req_cols[etf].pop(req_cols[etf].index('date'))
    df_test = df_test.loc[:, req_cols[etf]]

    if first_diff != None and etf in first_diff:
        for col in first_diff[etf]:
            df_test.loc[:, col]= df_test.loc[:, col].diff(periods= 1)
        # the first-row would have become null (due to the above-differencing), so
        df_test.drop(labels= [ df_test.index[0] ], axis= 0, inplace= True)

    
    # 7 rectify multi-colinearity
    normalize_features(etf, info_folder, 'pca', df_test)
    
    
    # 7c apply PCA to replace each cluster, by 1 feature
    pca_results = apply_PCA(etf, df_test, info_folder)
    
    # load the new_columns
    #? <-- check columns here
    cols = list(df_test.columns)
    
    # decrease resolution
    for c in cols:
        df_test[c] = df_test[c].apply(lambda x: round(x, 4))
    
    # remove target from the list of columns
    cols.pop(cols.index(target))
    
    # 8 normalize all the features
    normalize_features(etf, info_folder, 'cols', df_test)
    
    # 9 normalize the target
    target_scaler = joblib.load(f'{info_folder}/scaler/{etf}_target_scaler.pkl')
    df_test.loc[:, [target]] = target_scaler.transform(df_test.loc[:, [target]].to_numpy())
    
    # 10 convert each submatrix to a row, then store on disk
    l = f'l{str(lag+1)}'
    df = include_lags(df_test, target, lag)
    df.to_csv(f'./{dst}/{etf}_test.csv', index= True)
