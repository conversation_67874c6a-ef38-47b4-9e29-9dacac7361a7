# modules
import os
import re
import yaml
import json
import numpy as np
import pandas as pd
from datetime import datetime
from sklearn.preprocessing import StandardScaler

# function definitions
def read_configuration():
    
    # read the configuration file
    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)

    etfs      = config['init']['etfs'].split(', ')
    src_stats = config['init']["data_folder"]
    src_data  = config['init']["pred_folder"]
    dst       = config['init']["denorm_pred"]
    log_folder= config['init']["AutoAI_log_folder"]
    pred_col  = config['init']["pred_col"]
    target    = config['init']["target"]
    flag      = config['init']["predictions_for_training_set"]

    # remove etfs, for which I do not have a prediction
    try:
        # read
        absent= None
        with open(f'./{log_folder}/no_predictions.txt', 'r') as f:
            absent= set(f.read().split('\n')[1:]) #ignore the first line, because it is a comment
        # delete
        tmp= []
        for etf in etfs:
            if etf not in absent:
                tmp.append(etf)
    except:
        tmp= etfs

    return (tmp, pred_col, target, src_data, src_stats, dst, flag)
