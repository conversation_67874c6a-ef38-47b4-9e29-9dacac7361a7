# modules
import os
import sys
import json
import warnings
from io import StringIO
from datetime import datetime, timedelta

#import boto3

import numpy as np
import pandas as pd

from pywt import dwt, idwt

from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

from statsmodels.tsa.stattools import adfuller

import joblib
from eq_common_utils.utils.config.s3_config import s3_config
import yaml

import zipfile
import regex as re
from io import BytesIO

from copy import deepcopy

# function definitions
def read_configuration():

    # read the configuration file
    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)
    

    etfs  = config['init']['etfs'].split(', ')
    lag   = config['init']['lag']
    target= config['init']['target']
    dst   = config['init']["data_folder"]
    info_folder  = config['init']['info_folder']
    date  = datetime.strptime(config['init']['date'].strftime('%Y-%m-%d'), '%Y-%m-%d')

    return (etfs, target, lag, dst, date, info_folder)
    
def get_data(etf):
    """
    Desc.: this function is used to get the data[-frame] for an ETF, from S3
    Arguments: etf = str
    returns a DataFrame
    """
    client = s3_config()

    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)
    bucket= config['get_data']['bucket']
    paths= config['get_data']['paths'].split(', ')
    
    # GET data
    for p in paths:
        try:
            return client.read_as_dataframe(bucket= bucket, path= p.format(etf= etf))
        except:
            pass
    
    # if data not found
    print(f'ERROR: data not found for the etf {etf}')
    df = pd.DataFrame()
        
    return df
    
def read_csv(etf, target, log):
    """
    Desc.: Read data from S3
    Arguments: etf = str
    returns a DataFrame
    """
    
    # read
    df = get_data(etf)

    #
    if not df.empty:
        
        # a delete columns
        # i. old:
        """
        cols = ['tic', 'isin', 'actual_monthly_returns', 'monthly_predictions', 'daily_close_change', 'quarterly_close_change', 'weekly_close_change']
        df.drop(cols, axis= 1, inplace= True)
        """

        # ii. new: In response to BNP's data
        ## for BNP:
        cols_1 = ['daily_close_change', 'weekly_close_change']
        df.drop(cols_1, axis= 1, inplace= True)
        cols_2= ['tic', 'isin', 'actual_monthly_returns', 'monthly_predictions', 'quarterly_close_change', 'yearly_close_change']
        for col in cols_2:
            if col in df.columns:
                df.drop(col, axis= 1, inplace= True)
        
        # b delete columns with NaNs
        cols= df.columns
        for col in cols:
            if sum(df.loc[:, col].isna()) > 0:
                df.drop(labels= [col], axis= 1, inplace= True)
    
        # c sort
        ## for BNP:
        if 'Date' in df.columns:
            df.rename(mapper= {'Date':'date'}, axis= 1, inplace= True)
            
        df['date'] = df['date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d'))
        df.sort_values(by= ['date'], inplace= True)

        # for BNP: drop rows which contain the same 'date'
        df.drop_duplicates(subset= 'date', inplace= True)
        
        # d rename mcc to f'{target}'
        """
        I did not use AMR, or shift MCC back by 22, because
            1. the mcc column will undergo '.shift(-22)', after denoising. refer comment '5 Convert MCC to AMR'
            2. denoising requires the absence of NaNs
        """
        ## for BNP:
        if 'close_price' in df.columns:
            df.loc[:, 'monthly_close_change'] = df.loc[:, 'close_price'].pct_change(periods= 22)*100
            df.drop(['close_price'], axis= 1, inplace= True)
        df.rename(mapper= {'monthly_close_change' : target}, axis=1, inplace= True)

        ## for BNP: delete rows with NaNs
        df.dropna(inplace= True)
        
    else:
        log.append(f'ERROR: No data found for the etf {etf}')

    return df

def detect_mixed_datatypes(df, cols, log):
    """
    Desc.: Checks whether the input-dataframe contains columns with inconsistent data-types
    """
    
    flag = False
    for col in cols:
        tmp = df[col].apply( lambda x: type(x))
        if sum(tmp != tmp.iloc[0]):
            flag = True
    
    if flag:
        log.append('ERROR: There are columns with mixed data-types.')
        return True
    else:
        return False

def denoise(df):

    """
    Des.: Uses the db4 wavelet to denoise each column in the dataframe df
    returns the denoised dataframe
    """

    # create a "deep-copy" of the DataFrame
    df1 = df.copy(deep= True)
    
    # error fix : Daubechies-Wavelet-Transform needs an even no. of rows
    flag = False
    if len(df1) % 2 > 0:
        tmp  = pd.DataFrame(df1.head(1).copy())
        df1   = pd.concat([tmp, df1])
        flag = True

    # Use this DWT
    wavelet = 'db4'
    for col in df1.columns:
        if col == 'date':
            continue
        
        # decompose the signal
        a, d= dwt(df1[col], wavelet)
        
        # Clip the outliers via "winsorization"
        ## remove high +ves
        threshold = round( np.quantile(a= d, q= 0.98), 2 )
        d[ d >= threshold ]= threshold
        ## remove low -ves
        threshold = round( np.quantile(a= d, q= 0.02), 2 )
        d[ d <= threshold ]= threshold

        # re-construct the signal
        df1[col]= idwt(a, d, wavelet)


    # remove the duplicate row
    if flag:
        df1.reset_index(inplace= True, drop= True)
        df1.drop(index= 0, inplace= True)
        df1.reset_index(inplace= True, drop= True)

    return df1
        
def detect_multi_colinearity(df1, cols):

    """
    Desc.: Identifies [highly-] linearly-correlated columns in the dataframe 'df1' (frm the set of columns in 'cols')
    """
    
    # 
    df = df1[cols].copy()

    # calculate Pearson's-correlation between each pair of features
    fc = df.corr()
    fc = fc.to_numpy()

    # Retain correaltions which are, either > 0.8, or < -0.8
    r, c = fc.shape
    for i in range(r):
        for j in range(c):
            tmp = abs(fc[i, j])
            if tmp < 0.8:
                fc[i, j] = 0
            else:
                fc[i, j] = tmp

    # This performs a non-linear transformation on the coorelation-matrix, to convert it into an adjacency-matrix
    graph = -1 * np.log(fc)

    # detects clusters
    tmp = Floyd_Warshall(graph)

    # This performs a non-linear transformation on the adjacency-matrix, to convert it into a coorelation-matrix
    ans = np.exp(-1 * tmp)

    # save the columns-name in each cluster
    cols = df.columns
    f2f = pd.DataFrame(ans, columns= cols, index= cols)

    corr_feats = dict()
    visited = set()
    for i in range(r-1):
        corr_feats[ cols[i] ] = []
        for j in range(i+1, r):
            if ans[i, j] > 0:
                if j not in visited:
                    visited.add(j)
                    corr_feats[ cols[i] ].append( cols[j] )

    corr_cols = []
    for k, v in corr_feats.items():
        if len(v) != 0:
            tmp = [k]
            tmp.extend( v )
            corr_cols.append(tmp)
            

    return (corr_cols, ans)

# reference: https://www.geeksforgeeks.org/floyd-warshall-algorithm-dp-16/
def Floyd_Warshall(graph):
    
    r,c = graph.shape
    
    if r != c:
        raise Exception('The (input) adjacency-mtarix is not square!')

    tmp = np.copy(graph)
    
    for k in range(r):
        for i in range(r):
            for j in range(r):
                
                _min_ = min(tmp[i, j], tmp[i, k] + tmp[k, j])
                tmp[i, j] = _min_

    return tmp

def normalize_features(df_train, cols):
    
    # reference: https://scikit-learn.org/dev/modules/generated/sklearn.preprocessing.StandardScaler.html
    scaler = StandardScaler()

    # replace specific columns by standarized data
    df_train.loc[:, cols] = scaler.fit_transform(df_train.loc[:, cols].to_numpy())

    return scaler

def apply_PCA(df_train, clusters):

    """
    Dec.: Applies PCA on a set of columns, to create new features (and delete the older sets)
    Arguments:
        data     : is a dictionary, which contains the training & test sets, as DataFrames
        clusters : is a list of lists, where each list contains [highly-] linearly-correlated columns
    """
    
    
    
    # Apply PCA
    pcas= {}
    pca_results = {}
    for i, cluster in enumerate(clusters):
        # 'y' shall be the new column's name
        y = 'pca_' + str(i+1)
        # reference: https://scikit-learn.org/stable/modules/generated/sklearn.decomposition.PCA.html
        pca = PCA(n_components= 1)
        # Fit on the training-set
        df_train[y] = pca.fit_transform(df_train[cluster]) #/ n
        pca_results[y]   = { 'variance captured' : round(pca.explained_variance_ratio_[0], 2), 'cluster' : cluster, 'weights' : list(pca.components_.reshape((-1, ))), 'id' : i }

        pcas[i]= pca
        del pca
    
    # drop the clusters
    for cluster in clusters:
        df_train.drop(cluster, axis= 1, inplace= True)

    return (pca_results, pcas)

def include_lags(df, target, lag):
    """
    Desc.:
    Input:   DataFrame
    Output:  DataFrame
    Process: Flattens each submatrix, i.e., x[i:i+lag] -> x[i]
    """
    # convert df -> (x, y)
    x = df.drop([target], axis= 1).to_numpy()
    y = df.loc[:, [target]].to_numpy()
    
    # convert each submatrix to a row
    r, c = x.shape
    context = np.empty((r-lag, c*(lag+1)), dtype= 'float')
    for i in range(lag, r):
        j = i-lag # select the row 'lags' units before i
        context[j, :] = x[j:i+1, :].flatten()
    
    # creates names for lagging-columns
    cols = df.columns.to_list()
    cols.pop(cols.index(target))
    names = []
    for i in reversed(range(lag+1)):
        for c in cols:
            names.append(f'lag{str(i)}_{c}')
    
    # create the final DataFrame
    ans = pd.DataFrame(data= context, columns= names, index= df.index[lag:])
    ans[target] = df[target]

    # TEST: check the values of a random-row, from a randomly-selected column
    c = cols[np.random.randint(low= 0, high= len(cols), size= 1)[0]]
    x = np.random.randint(low= -22, high= -1, size= 1)[0]
    
    ## select lagging-values
    c_lags = []
    for i in reversed(range(lag+1)):
        c_lags.append(f'lag{str(i)}_{c}')

    # check the last-values
    if sum(ans[c_lags].iloc[x].values != df[c].iloc[x-lag:x+1].values) > 0:
        raise Exception(f'{etf} : The context is corrupt')

    # check the dates
    start = ans.index[0 ] != df[c].index[lag]
    end   = ans.index[-1] != df[c].index[-1]
    cond  = start & end 
    if cond:
        raise Exception(f'{etf} : The dates are mis-aligned')
        
    return ans

def upload_metadata():
    # init
    ## error check
    if not os.path.isfile('config.yaml'):
        raise Exception('ERROR: file \'config.yaml\' not found')
    with open('config.yaml', 'r') as f:
        config= yaml.safe_load(f)
    bucket = config['meta_data']['bucket']
    pathname= config['meta_data']['path']
    _dir_ = config['meta_data']['dir']

    # walk the directory, i.e., view all files & subdirectories
    ## error check
    if not os.path.isdir(_dir_):
        raise Exception(f'ERROR: directory \'{_dir_}\' not found')
    
    ## remove cached files, stored within '.ipynb_checkpoints'
    ptrn = re.compile('.ipynb_checkpoints')
    gen= os.walk(_dir_)
    ## store all desired paths here
    paths= []
    for (r, sd, fs) in gen:
        if ptrn.search(r):
            continue
        paths.append( (r, fs) )
        
    """
    convert:
    
    FROM:
    (root, paths)
    
    TO:
    for each path in paths:
        f'{root}/{path}'
    '"""
    req_paths= []
    for (root, ps) in paths:
        for p in ps:
            path= os.path.join(root, p)
            req_paths.append(path)
    
    req_paths.append('./2a_data/stats.txt')
    
    # zip files
    buffer = BytesIO()
    with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for path in req_paths:
            zipf.write(path)
    
    # upload to S3
    client = s3_config()
    client.upload_binary_file(buffer.getvalue(), bucket, pathname)
    

