# modules
from eq_common_utils.utils.config.s3_config import s3_config
from sklearn.preprocessing import StandardScaler
from statsmodels.tsa.stattools import adfuller
from datetime import datetime, timedelta
from sklearn.decomposition import PCA
from io import StringIO, BytesIO
from pywt import dwt, idwt
import pandas as pd
import numpy as np
import warnings
import zipfile
import joblib
import shutil
import yaml
import json
import sys
import os

# function definitions
def read_configuration():
    
    # read the configuration file
    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)

    etfs  = config['init']['etfs'].split(', ')
    lag   = config['init']['lag']
    target= config['init']['target']
    dst   = config['init']["data_folder"]
    info_folder  = config['init']['info_folder']
    date  = datetime.strptime(config['init']['date'].strftime('%Y-%m-%d'), '%Y-%m-%d')

    # disregard etfs which I do not have a training set
    if os.path.isfile(f'{info_folder}/error_no_data.txt'):
        with open(f'{info_folder}/error_no_data.txt', 'r') as f:
            absent= set((f.read().split('\n')))
        etfs2= []
        for etf in etfs:
            if etf not in absent:
                etfs2.append(etf)
        etfs= etfs2
    if os.path.isfile(f'{info_folder}/error_mixed_datatypes.txt'):
        with open(f'{info_folder}/error_mixed_datatypes.txt', 'r') as f:
            absent= set((f.read().split('\n')))
        etfs2= []
        for etf in etfs:
            if etf not in absent:
                etfs2.append(etf)
        etfs= etfs2

    #
    with open(f'{info_folder}/raw_columns_used.txt', 'r') as f:
        req_cols= json.loads(f.read())
    

    return (etfs, target, lag, dst, date, info_folder, req_cols)

def get_data(etf):
    """
    Desc.: this function is used to get the data[-frame] for an ETF, from S3
    Arguments: etf = str
    returns a DataFrame
    """
    client = s3_config()

    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)
    bucket= config['get_data']['bucket']
    paths= config['get_data']['paths'].split(', ')
    
    # GET data
    for p in paths:
        try:
            return client.read_as_dataframe(bucket= bucket, path= p.format(etf= etf))
        except:
            pass
    
    # if data not found
    print(f'ERROR: data not found for the etf {etf}')
    df = pd.DataFrame()
        
    return df
    
def read_csv(etf, target, log):
    """
    Desc.: Read data from S3
    Arguments: etf = str
    returns a DataFrame
    """
    
    # read
    df = get_data(etf)

    #
    if not df.empty:
        
        # a delete columns
        cols_1 = ['daily_close_change', 'weekly_close_change']
        df.drop(cols_1, axis= 1, inplace= True)
        cols_2= ['tic', 'isin', 'actual_monthly_returns', 'monthly_predictions', 'quarterly_close_change', 'yearly_close_change']
        for col in cols_2:
            if col in df.columns:
                df.drop(col, axis= 1, inplace= True)
        
        # b sort
        ## for BNP:
        if 'Date' in df.columns:
            df.rename(mapper= {'Date':'date'}, axis= 1, inplace= True)
            
        df['date'] = df['date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d'))
        df.sort_values(by= ['date'], inplace= True)

        # for BNP: drop rows which contain the same 'date'
        df.drop_duplicates(subset= 'date', inplace= True)

        # c delete columns with NaNs
        cols= df.columns
        
        ## read the columns-required during training
        with open('config.yaml', 'r') as file:
            info_dir= yaml.safe_load(file)['init']['info_folder']
        with open(f'{info_dir}/raw_columns_used.txt', 'r') as f:
            req_cols= json.loads(f.read())[etf]
        
        # error check:
        if not set(req_cols).issubset(set(cols)):
            tmp= list(set(req_cols) - set(cols))
            tmp.sort()
            ', '.join(tmp)
            msg= f'ERROR: for the etf {etf}, the current-data (i.e., columns) differs from the training-columns. Missing columns are : {tmp}'    
            raise Exception(msg)

        cols= list(cols)
        for col in cols:
            # if there are NaNs in this column
            if sum(df.loc[:, col].isna()) > 0:
                # if this column is required, then ffill & bfill
                if col in req_cols:
                    df.loc[:, col].ffill(inplace= True)
                    if df.loc[0, col].isna():
                        df.loc[:, col].bfill(inplace= True)
                else:
                    df.drop(labels= [col], axis= 1, inplace= True)
        
        # d rename mcc to f'{target}'
        """
        I did not use AMR, or shift MCC back by 22, because
            1. the mcc column will undergo '.shift(-22)', after denoising. refer comment '5 Convert MCC to AMR'
            2. denoising requires the absence of NaNs
        """
        ## for BNP:
        if 'close_price' in df.columns:
            df.loc[:, 'monthly_close_change'] = df.loc[:, 'close_price'].pct_change(periods= 22)*100
            df.drop(['close_price'], axis= 1, inplace= True)
        df.rename(mapper= {'monthly_close_change' : target}, axis=1, inplace= True)

        ## for BNP: delete rows with NaNs
        df.dropna(inplace= True)
        
    else:
        log.append(f'ERROR: No data found for the etf {etf}')

    return df

def detect_mixed_datatypes(df, cols, log):
    """
    Desc.: Checks whether the input-dataframe contains columns with inconsistent data-types
    """
    
    flag = False
    for col in cols:
        tmp = df[col].apply( lambda x: type(x))
        if sum(tmp != tmp.iloc[0]):
            flag = True
    
    if flag:
        log.append('ERROR: There are columns with mixed data-types.')
        return True
    else:
        return False

def denoise(df):

    """
    Des.: Uses the db4 wavelet to denoise each column in the dataframe df
    returns the denoised dataframe
    """

    # create a "deep-copy" of the DataFrame
    df1 = df.copy(deep= True)
    
    # error fix : Daubechies-Wavelet-Transform needs an even no. of rows
    flag = False
    if len(df1) % 2 > 0:
        tmp  = pd.DataFrame(df1.head(1).copy())
        df1   = pd.concat([tmp, df1])
        flag = True

    # Use this DWT
    wavelet = 'db4'
    for col in df1.columns:
        if col == 'date':
            continue
        
        # decompose the signal
        a, d= dwt(df1[col], wavelet)
        
        # Clip the outliers via "winsorization"
        ## remove high +ves
        threshold = round( np.quantile(a= d, q= 0.98), 2 )
        d[ d >= threshold ]= threshold
        ## remove low -ves
        threshold = round( np.quantile(a= d, q= 0.02), 2 )
        d[ d <= threshold ]= threshold

        # re-construct the signal
        df1[col]= idwt(a, d, wavelet)


    # remove the duplicate row
    if flag:
        df1.reset_index(inplace= True, drop= True)
        df1.drop(index= 0, inplace= True)
        df1.reset_index(inplace= True, drop= True)

    return df1
        
def normalize_features(etf, info_folder, name, df_test):

    cols= None
    with open(f'{info_folder}/scaler_info/{name}_standarize.txt', 'r') as f:
        for line in f:
            x= json.loads(line)
            if x['etf'] == etf:
                cols= x['cols']
                break
    if cols == None:
        raise Exception(f'ERROR: data not found in for the etf {etf} \'{info_folder}/scaler_info/{name}_standarize.txt\'.')
    
    scaler = joblib.load(f'{info_folder}/scaler/{etf}_{name}_scaler.pkl')
    
    # replace specific columns by standarized data
    df_test.loc[:, cols] = scaler.transform(   df_test.loc[:, cols].to_numpy())

def apply_PCA(etf, df_test, info_folder):

    """
    Dec.: Applies PCA on a set of columns, to create new features (and delete the older sets)
    Arguments:
        data     : is a dictionary, which contains the training & test sets, as DataFrames
        clusters : is a list of lists, where each list contains [highly-] linearly-correlated columns
    """
    
    y= None
    with open(f'{info_folder}/pca_info.txt', 'r') as f:
        for line in f:
            x= json.loads(line)
            if x['etf'] == etf:
                y= json.loads(x['pca_results'])
                break
    if y == None:
        raise Exception(f'ERROR: data not found in for the etf {etf} \'{info_folder}/pca_info.txt\'.')
        
    for feat in y.keys():
        cluster= y[feat]['cluster']
        _id = y[feat]['id']

        pca = joblib.load(f'{info_folder}/pca/{etf}_{_id}_pca.pkl')
        
        # Fit on the training-set
        df_test[feat] = pca.transform(df_test[cluster]) #/ n
        df_test.drop(cluster, axis= 1, inplace= True)


def include_lags(df, target, lag):
    """
    Desc.:
    Input:   DataFrame
    Output:  DataFrame
    Process: Flattens each submatrix, i.e., x[i:i+lag] -> x[i]
    """
    # convert df -> (x, y)
    x = df.drop([target], axis= 1).to_numpy()
    y = df.loc[:, [target]].to_numpy()
    
    # convert each submatrix to a row
    r, c = x.shape
    context = np.empty((r-lag, c*(lag+1)), dtype= 'float')
    for i in range(lag, r):
        j = i-lag # select the row 'lags' units before i
        context[j, :] = x[j:i+1, :].flatten()
    
    # creates names for lagging-columns
    cols = df.columns.to_list()
    cols.pop(cols.index(target))
    names = []
    for i in reversed(range(lag+1)):
        for c in cols:
            names.append(f'lag{str(i)}_{c}')
    
    # create the final DataFrame
    ans = pd.DataFrame(data= context, columns= names, index= df.index[lag:])
    ans[target] = df[target]

    # TEST: check the values of a random-row, from a randomly-selected column
    c = cols[np.random.randint(low= 0, high= len(cols), size= 1)[0]]
    x = np.random.randint(low= -len(ans), high= -1, size= 1)[0]
    
    ## select lagging-values
    c_lags = []
    for i in reversed(range(lag+1)):
        c_lags.append(f'lag{str(i)}_{c}')

    # check the last-values
    if sum(ans[c_lags].iloc[x].values != df[c].iloc[x-lag:x+1].values) > 0:
        raise Exception(f'ERROR: {etf} : The lags do not match')

    # check the dates
    start = ans.index[0 ] != df[c].index[lag]
    end   = ans.index[-1] != df[c].index[-1]
    cond  = start & end 
    if cond:
        raise Exception(f'ERROR: {etf} : The dates are mis-aligned')
        
    return ans


def first_differenced(info_folder):

    if os.path.isfile(f'./{info_folder}/first_diff.txt'):
        with open(f'./{info_folder}/first_diff.txt', 'r') as f:
            req_data= json.loads(f.read())
        return req_data
    else:
        return None

def dropped_columns(info_folder):

    if os.path.isfile(f'./{info_folder}/drop_cols.txt'):
        with open(f'./{info_folder}/drop_cols.txt', 'r') as f:
            req_data= json.loads(f.read())
        return req_data
    else:
        return None

def download_metadata():

    delete_metadata()

    with open('./config.yaml', 'r') as f:
        config= yaml.safe_load(f)
    bucket = config['meta_data']['bucket']
    pathname= config['meta_data']['path']

    client = s3_config()
    obj= client.read_as_stream(bucket, pathname)
    
    buffer= BytesIO(obj[0])
    
    unzip_files= util_download_metadata(buffer)
    if len(unzip_files) == 0:
        print('Meta data is available locally')
        return None
        
    with zipfile.ZipFile(buffer, 'r') as zipf:
        files= zipf.namelist()
        if len(files) == len(unzip_files):
            zipf.extractall()
        else:
            for i in unzip_files:
                zipf.extract(files[i])

def util_download_metadata(buffer : BytesIO) -> list:

    files= None
    with zipfile.ZipFile(buffer, 'r') as zipf:
        files= zipf.namelist()

    unzip= []
    for i, f in enumerate(files):
        if not os.path.isfile(f):
            unzip.append(i)

    return unzip

def delete_metadata():
    with open('config.yaml', 'r') as file:
        config= yaml.safe_load(file)
        
    data_dir = config['init']["data_folder"]
    meta_data_dir  = config['init']['info_folder']
    if os.path.isdir(meta_data_dir):
        shutil.rmtree(meta_data_dir)
    if os.path.isfile(f'{data_dir}/stats.txt'):
        os.remove(f'{data_dir}/stats.txt')