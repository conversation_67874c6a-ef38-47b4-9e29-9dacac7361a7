from helpers import *
s3conn = s3_config()
esconn = es_config(env='prod').client
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)


def send_email(subject ,receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']

    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)

def do_input_check(input_check_url):
    input_check_response = requests.get(input_check_url)
    input_check_status = input_check_response.json()['status']
    return input_check_status
    
def do_predictions_trigger(prediction_trigger_url, requests_body):
    try:
        #return True
        prediction_trigger_response = requests.post(prediction_trigger_url, json=requests_body, timeout = 1800)
        prediction_trigger_status = prediction_trigger_response.json()['status']
        return prediction_trigger_status
    except Exception as e:
        print(traceback.format_exc())
        return False


def do_metrics_trigger(metrics_trigger_url, requests_body):
    try:
        #return True
        metrics_trigger_response = requests.post(metrics_trigger_url, json=requests_body, timeout = 240)
        metrics_trigger_status = metrics_trigger_response.json()['status']
        return metrics_trigger_status
    except Exception as e:
        print(traceback.format_exc())
        return False

def check_if_prices_available(isin_list, api_url, target_date):
    new_list = []
    for isin in isin_list:
        try:
            get_instockapi_data (api_url, isin ,target_date, target_date)
            new_list.append(isin)
        except:
            print(f'No Price data for this isin {isin}')
    return new_list


def run_django_runs(run_date):
    lastbday = (pd.to_datetime(run_date) - BDay(1)).strftime('%Y-%m-%d')
    print(run_date, lastbday)
    target_date = lastbday
    print(target_date)
    print(f'Running for Target Date : {target_date}')


    input_check_url = config['Django_Endpoints']['input_check_url']
    data_trigger_url = config['Django_Endpoints']['trigger_data_url']
    
    input_check_url = input_check_url.format(target_date = target_date)
    prediction_trigger_url = data_trigger_url.format(function_to_trigger = 'trigger_predict', target_date = target_date)
    metrics_trigger_url = data_trigger_url.format(function_to_trigger = 'trigger_metrics', target_date = target_date)

    body_requests_all = config['Django_Body']['body_requests']
    all_isins = pd.DataFrame(body_requests_all)['isin'].values.tolist()

    ### starting input check
    start = time.perf_counter()
    input_check_status = do_input_check(input_check_url)
    wait_time = time.perf_counter()-start
    while(not input_check_status and wait_time<= 5*60):
        print(input_check_status)
        time.sleep(60)
        wait_time = time.perf_counter()-start
        input_check_status = do_input_check(input_check_url)

    print(f'Final Input Check Status : {input_check_status}')

    pred_index = config['misc']['es_index_name_pred']
    metrics_index = config['misc']['es_index_name_metrics']
    schedular = config['misc']['schedular']
    curr_year = int(target_date.split('-')[0])

    inhouse_api_url = config['Inhouse_API']['inhouse_api_url_by_isin']

    ### doing trigger predict

    max_tries = 0
    pred_delta_isins = all_isins
    
    while pred_delta_isins and max_tries < 2 :
        max_tries+=1
        body_requests = [item for item in body_requests_all if item['isin'] in pred_delta_isins]
        prediction_trigger_status = do_predictions_trigger(prediction_trigger_url, body_requests)
        
        if not prediction_trigger_status :
            time.sleep(300)
    
        fetched_from_es = get_latest_data_from_es( curr_year, pred_index, esconn, target_date, schedular)
        fetched_from_es_isins = fetched_from_es['isin'].values.tolist()
        pred_delta_isins = list(set(all_isins) - set(fetched_from_es_isins))
        pred_delta_isins = check_if_prices_available(pred_delta_isins, inhouse_api_url, target_date)
    
    print('Prediction Delta Isins: ', pred_delta_isins)

    ### doing trigger metrics
    max_tries = 0
    metrics_delta_isins = all_isins
    
    while metrics_delta_isins and max_tries < 2 :
        max_tries+=1
        body_requests = [item for item in body_requests_all if item['isin'] in metrics_delta_isins]
        metrics_trigger_status = do_metrics_trigger(metrics_trigger_url, body_requests)
        
        if not metrics_trigger_status :
            time.sleep(60)
    
        fetched_from_es = get_latest_data_from_es( curr_year, metrics_index, esconn, target_date, schedular)
        fetched_from_es_isins = fetched_from_es['isin'].values.tolist()
    
        fetched_from_es_pred = get_latest_data_from_es( curr_year, pred_index, esconn, target_date, schedular)
        fetched_from_es_pred_isins = fetched_from_es_pred['isin'].values.tolist()
        
        metrics_delta_isins = list(set(fetched_from_es_pred_isins) - set(fetched_from_es_isins))
    
    print('Metrics Delta Isins: ', pred_delta_isins + metrics_delta_isins)

    return {'Django Delta Predictions': pred_delta_isins, 'Django Delta Metrics' : pred_delta_isins + metrics_delta_isins}


if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    
    try:
        
        summary = run_django_runs(run_date)
        print(f'ETFs Django Runs: Run COMPLETED successfully for run date {run_date}')

        receiver = config['Gmail_creds']['email_receiver_success']
        send_email(f'ETFs Django Runs : Run COMPLETED successfully for run date {run_date}',receiver, summary)
    
    except Exception as e:
        print(f'ETFs Django Runs : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config['Gmail_creds']['email_receiver_failed']
        send_email( f'ETFs Django Runs : Run FAILED for run date {run_date}',receiver, traceback.format_exc())
        raise