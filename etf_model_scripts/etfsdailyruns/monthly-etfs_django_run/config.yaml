Gmail_creds:
    gmail_creds_bucket : etf-predictions
    gmail_creds_path: preportfolio/gmail_credentials/credentials.json
    gmail_sender : <EMAIL>
    email_receiver_success : <EMAIL>, <EMAIL>, <EMAIL>
    email_receiver_failed : <EMAIL>, <EMAIL>, <EMAIL>

Tic_ISIN_map:
    isin_tic_map_bucket : etf-predictions
    isin_tic_map_path : preportfolio/etfs_isin_map/etfs_tic_isin_mapping.csv
    
Master_active_firms:
    api_url : http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all

Inhouse_API:
    inhouse_api_url_by_isin : http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA
    inhouse_api_url_by_tic : http://52.2.217.192:8080/stockhistory/getstocksbytic?tic={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA

S3_paths:


Django_Endpoints:
    input_check_url : http://3.84.115.162:8032/model/input_check?model_key=etf_model&bulk=true&schedular=Monthly&date={target_date}&tag=alletf
    trigger_data_url : http://3.84.115.162:8032/model/{function_to_trigger}?model_key=etf_model&bulk=true&schedular=Monthly&date={target_date}
    #trigger_data_url : http://3.84.115.162:8032/model/trigger_predict?model_key=etf_model&bulk=true&schedular=Monthly&date={target_date}
    #trigger_metrics_url : http://3.84.115.162:8032/model/trigger_metrics?model_key=etf_model&bulk=true&schedular=Monthly&date={target_date}
Django_Body : 
    body_requests: [  
                    {"isin": "US46090E1038", "tic": "QQQ", "exchange": "NasdaqGM"},
                    {"isin": "US4642872349", "tic": "EEM", "exchange": "ARCA"},
                    {"isin": "US4642874659", "tic": "EFA", "exchange": "ARCA"},
                    {"isin": "US4642876555", "tic": "IWM", "exchange": "ARCA"},
                    {"isin": "US4642877397", "tic": "IYR", "exchange": "ARCA"},
                    {"isin": "US46434G8226", "tic": "EWJ", "exchange": "ARCA"},
                    {"isin": "US78462F1030", "tic": "SPY", "exchange": "ARCA"},
                    {"isin": "US78464A7550", "tic": "XME", "exchange": "ARCA"},
                    {"isin": "US81369Y5069", "tic": "XLE", "exchange": "ARCA"},
                    {"isin": "US4642871762", "tic": "TIP", "exchange": "ARCA"},
                    {"isin": "US4642872422", "tic": "LQD", "exchange": "ARCA"}, 
                    {"isin": "US4642874329", "tic": "TLT", "exchange": "NasdaqGM"}, 
                    {"isin": "US4642874576", "tic": "SHY", "exchange": "NasdaqGM"}, 
                    {"isin": "US4642882819", "tic": "EMB", "exchange": "NasdaqGM"}, 
                    {"isin": "US4642885135", "tic": "HYG", "exchange": "ARCA"}, 
                    {"isin": "US78463V1070", "tic": "GLD", "exchange": "ARCA"}, 
                    {"isin": "US92203J4076", "tic": "BNDX", "exchange": "NasdaqGM"},
                    {"isin": "MQFIUSTU", "tic": "MQFIUSTU"},
                    {"isin": "MQFIUSTY", "tic": "MQFIUSTY"},
                    {"isin": "MQFIUSUS", "tic": "MQFIUSUS"},
                    {"isin": "HSMETYSN", "tic": "HSMETYSN"},
                    {"isin": "HSMETYV3", "tic": "HSMETYV3"},
                    {"isin": "US81369Y1001", "tic": "XLB", "exchange": "ARCA"},
                    {"isin": "US81369Y2090", "tic": "XLV", "exchange": "ARCA"}, 
                    {"isin": "US81369Y3080", "tic": "XLP", "exchange": "ARCA"}, 
                    {"isin": "US81369Y4070", "tic": "XLY", "exchange": "ARCA"}, 
                    {"isin": "US81369Y6059", "tic": "XLF", "exchange": "ARCA"}, 
                    {"isin": "US81369Y7040", "tic": "XLI", "exchange": "ARCA"}, 
                    {"isin": "US81369Y8030", "tic": "XLK", "exchange": "ARCA"}, 
                    {"isin": "US81369Y8527", "tic": "XLC", "exchange": "ARCA"}, 
                    {"isin": "US81369Y8600", "tic": "XLRE", "exchange": "ARCA"}, 
                    {"isin": "US81369Y8865", "tic": "XLU", "exchange": "ARCA"},
                    {"isin": "DBEEEMGF", "tic": "DBEEEMGF"},
                    {"isin": "DBRCOYGC", "tic": "DBRCOYGC"},
                    {"isin": "DBEETGFU", "tic": "DBEETGFU"},
                    {"isin": "DBEEURGF", "tic": "DBEEURGF"},
                    {"isin": "DBEEUNGF", "tic": "DBEEUNGF"},
                    {"isin": "DBEEUGFT", "tic": "DBEEUGFT"},
                    {"isin": "DBDRUS10", "tic": "DBDRUS10"},
                    {"isin": "DBDRUS02", "tic": "DBDRUS02"},
                    {"isin": "DBDRUS20", "tic": "DBDRUS20"}
                    ]
    
misc:
    
    aigo_etfs : [QQQ, EEM, EFA, IWM, IYR, EWJ, SPY, XME, XLE,TIP, LQD, TLT, SHY, EMB, HYG, GLD, BNDX,MQFIUSTU, MQFIUSTY, MQFIUSUS, HSMETYSN,
                    HSMETYV3]
    sector_etfs : [ XLB, XLV, XLP, XLY, XLF, XLI, XLK, XLC, XLRE, XLU]
    db_etfs : [DBEEEMGF,DBEETGFU,DBEEURGF,DBEEUNGF,DBEEUGFT,DBRCOYGC,DBDRUS10,DBDRUS02,DBDRUS20]
    bnp_etfs : [BNPIFU10,BNPIFCN, BNPIFJP, BNPIFE10, BNPIFJ10, BNPIDSBU,BNPIFUS, BNPIFEM, BNPIG0GC, BNPIFEU]

    aigo_pub_etfs : [QQQ, EEM, EFA, IWM, IYR, EWJ, SPY, XME, XLE,TIP, LQD, TLT, SHY, EMB, HYG, GLD, BNDX, XLB, XLV, XLP, XLY, XLF, XLI, XLK, XLC,
                        XLRE, XLU]

    es_index_name_pred : eq_etf_model
    es_index_name_metrics : eq_etf_model_metrics
    schedular : Monthly
    