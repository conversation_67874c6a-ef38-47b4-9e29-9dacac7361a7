import sys
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from opensearchpy.connection.http_requests import RequestsHttpConnection
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from itertools import repeat
import ast
from opensearchpy.client import OpenSearch
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
import requests
import pandas as pd
import ibm_watsonx_ai
from ibm_watsonx_ai import APIClient
from ibm_watsonx_ai.deployment import Batch
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
import traceback
from concurrent.futures import ThreadPoolExecutor

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def get_smoothening_gradients():
    gradient_size = 5
    multiplier = 1 / (np.e * (np.e ** gradient_size - 1) / (np.e - 1))
    smoothening_gradient = [multiplier * (np.e ** (i + 1)) for i in range(gradient_size)]
    return smoothening_gradient

def get_instockapi_data (api_url, firm ,start_date, end_date):
    '''
        Function to get price data from Inhouse Stock API
    '''
    isin = firm
    stock_data = requests.get(api_url.format(identifier = isin, startdate = start_date, enddate = end_date)).json()
 
    stock_data = pd.DataFrame(stock_data['data']['stocks'])[['date','adj_close']]
 
    stock_data.rename(columns={'adj_close':'close_price'}, inplace=True)
    stock_data=stock_data[['date','close_price']]
    stock_data['date']=pd.to_datetime(stock_data['date'])
    stock_data.sort_values('date', ascending =True , inplace=True)
    stock_data.reset_index(inplace=True, drop=True)
 
    stock_data['monthly_close_change']=stock_data['close_price'].pct_change(periods=22)*100
    stock_data['actual_monthly_returns']=stock_data['monthly_close_change'].shift(-22)
    stock_data['date'] = stock_data['date'].astype(str)
    return stock_data


def prepare_body_html(body):
    if isinstance(body, pd.DataFrame):
        body = body.to_html(index=False, border=1)
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html
    
def get_data_from_es(etf_list, index_year, index_name, es_client, target_date, schedular, cols_to_remove):
    '''
        Function to get all data from ES within given parameters
    '''
    etf_data=[]
    for tic in etf_list:
        for year in range(index_year,index_year+ 1):
            #print(tic)
            q_total = '{"query":{"bool": {"must":[{"match":{"date":"'+target_date+'"}},{"match":{"tic":"'+tic+'"}}]}}}'
    
            result = es_client.search(index=f"{index_name}_{index_year}", body=q_total, size=10000,request_timeout=6000)
    
            for rs in result['hits']['hits']:
                etf_data_df=rs['_source']
                etf_data.append(etf_data_df)
            #print(tic)
        
    etf_data_fetched=pd.DataFrame(etf_data)
    #print(etf_data)
    if etf_data_fetched.empty:
        return pd.DataFrame(None)
    
    etf_data_fetched['date']=pd.to_datetime(etf_data_fetched['date'])
    
    etf_data_fetched = etf_data_fetched[etf_data_fetched['schedular']==schedular]

    for col in [f'{schedular.lower()}_predictions', f'smoothened_{schedular.lower()}_predictions']:
        if col in cols_to_remove:
            cols_to_remove.remove(col)
    
    for col in cols_to_remove:
        if col in etf_data_fetched.columns:
            etf_data_fetched.drop(columns=[col], axis=1, inplace=True)
      
    etf_data_fetched.reset_index(inplace=True, drop=True)
    return etf_data_fetched


def update_historical_predictions_data(etf_data_fetched, etf_list, product, 
                                       hist_pred_bucket, hist_pred_path, 
                                       upload_hist_pred_bucket, upload_hist_pred_path, s3conn):
    '''
        Function to update the new predictions data to the historical file
    '''
    for etf in etf_list:
        prev_df = pd.DataFrame(None)
        new_df = pd.DataFrame(None)
        
        prev_df = s3conn.read_as_dataframe(hist_pred_bucket, hist_pred_path.format(etf = etf, product = product))
            
        to_add_df = etf_data_fetched[etf_data_fetched['tic']==etf]
        if to_add_df.empty:
            print(f'Hehe Boy, No data available for given date for {etf}')
            continue
        to_add_df.reset_index(inplace=True, drop = True)
        to_add_df = to_add_df.copy()
        to_add_df.dropna(axis = 1, inplace=True)
        if prev_df['isin'].values[0] == to_add_df['isin'].values[0] and prev_df.shape[1] == to_add_df.shape[1] +1:
            prev_df['date'] = pd.to_datetime(prev_df['date'])
            new_df = pd.concat([prev_df,to_add_df], axis= 0)
            new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
            new_df.sort_values('date', ascending=True, inplace=True)
            new_df.reset_index(inplace=True, drop=True)
            n = new_df['actual_monthly_returns'].isna().sum().sum() + 5
            new_df.loc[new_df.index[-n:],'actual_monthly_returns'] = new_df.loc[new_df.index[-n:],'monthly_close_change'].shift(-22)
            print(etf)
            print(new_df.isna().sum().sum())
            print(new_df.shape, new_df.date.values[0], new_df.date.values[-1])

            s3conn.write_advanced_as_df(new_df, upload_hist_pred_bucket, upload_hist_pred_path.format(product = product, etf = etf))
            
            print(f'{etf} done')
        else:
            print(f'Error, shape mismatch for {etf}, {prev_df.shape}, {to_add_df.shape}')
            continue


def update_historical_metrics_data(etf_metrics_data_fetched, etf_list, product, 
                                       hist_metrics_bucket, hist_metrics_path, 
                                       upload_hist_metrics_bucket, upload_hist_metrics_path, s3conn):
    '''
        Function to update the new metrics data to the historical file
    '''
    for etf in etf_list:
        prev_df = pd.DataFrame(None)
        new_df = pd.DataFrame(None)

        prev_df = s3conn.read_as_dataframe(hist_metrics_bucket, hist_metrics_path.format(etf = etf, product = product))
            
        to_add_df = etf_metrics_data_fetched[etf_metrics_data_fetched['tic']==etf]
        if to_add_df.empty:
            print(f'Hehe Boy, No data available for given date for {etf}')
            continue
        to_add_df.reset_index(inplace=True, drop = True)
        to_add_df = to_add_df.copy()
        to_add_df.dropna(axis = 1, inplace=True)
        if prev_df['isin'].values[0] == to_add_df['isin'].values[0] and prev_df.shape[1] == to_add_df.shape[1]:
            prev_df['date'] = pd.to_datetime(prev_df['date'])
            new_df = pd.concat([prev_df,to_add_df], axis= 0)
            new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
            new_df.sort_values('date', ascending=True, inplace=True)
            new_df.reset_index(inplace=True, drop=True)
            print(etf)
            #print(new_df.isna().sum().sum())
            print(new_df.shape, new_df.date.values[0], new_df.date.values[-1])
            
            s3conn.write_advanced_as_df(new_df, upload_hist_metrics_bucket, upload_hist_metrics_path.format(product = product, etf = etf))
            
            print(f'{etf} done')
        else:
            print(f'Error, shape mismatch for {etf}, {prev_df.shape}, {to_add_df.shape}')
            continue

def get_all_latest_predictions_and_metrics(aigo_etfs, sector_etfs, db_etfs, s3conn, target_date,schedular,
                                           hist_pred_bucket, hist_pred_path, 
                                           delivery_metrics_bucket, delivery_metrics_path ):
    '''
        Function to get latest predictions and metrics data
    '''
    all_predictions = []
    for etf in aigo_etfs+ sector_etfs+ db_etfs:
        curr_df = pd.DataFrame(None)
        if etf in aigo_etfs:
            product = 'aigo'
        elif etf in db_etfs:
            product = 'db'
        elif etf in sector_etfs:
            product = 'sector'

        curr_df = s3conn.read_as_dataframe(hist_pred_bucket, hist_pred_path.format(product = product, etf = etf))
        curr_df = curr_df[curr_df['date']<=target_date]
            
        if 'ERscore' in curr_df.columns:
            curr_df  = curr_df[['date','tic','close_price','weekly_close_change','monthly_close_change','ERscore','monthly_predictions']][-1:]
        else:
            curr_df  = curr_df[['date','tic','close_price','weekly_close_change','monthly_close_change','monthly_predictions']][-1:]
        all_predictions.append(curr_df.to_dict('records')[0])

    all_predictions_df  = pd.DataFrame(all_predictions)
    
    all_metrics_df = s3conn.read_as_dataframe(delivery_metrics_bucket, delivery_metrics_path.format(schedular = schedular,
                                                                                                   schedular_lower = schedular.lower(),
                                                                                                   latest_date = target_date))
    all_metrics_df = all_metrics_df[['tic','avg_confidence_score', 'accuracy_22_day']]

    print('all_predictions_df', all_predictions_df)
    print('all_metrics_df' , all_metrics_df)

    merged_all = pd.merge(all_predictions_df, all_metrics_df, on = ['tic'], how = 'left')
    merged_all.columns = ['date','equity', 'close_price' ,'weekly_close_change',
                    'monthly_close_change','er_m','monthly_predictions','confidence_score', 'accuracy']
    
    return merged_all


def do_file_generation(merged_all, aigo_etfs, sector_etfs, db_etfs, s3conn, lastbday, file_generation_bucket, file_generation_path ):
    '''
        Function to generate client file product wise for consumption in delivery scripts
    '''
    #### Aigo File Generation
    aigo_file = pd.DataFrame(None)
    aigo_file = merged_all[merged_all['equity'].isin(aigo_etfs)]
    aigo_file.reset_index(inplace=True, drop = True)
    aigo_file.replace({'MQFIUSTU':'TREASURY_2YR',
                        'MQFIUSTY':'TREASURY_10YR',
                      'MQFIUSUS':'TREASURY_30YR',
                       'HSMETYSN': 'METYS',
                       'HSMETYV3': 'METYSV3'
                     }, inplace=True)
    aigo_file.loc[:, 'date'] = lastbday
    s3conn.write_advanced_as_df(aigo_file, file_generation_bucket,
                                file_generation_path.format(folderpath ='error-rate', product = 'aigo' ,target_date = lastbday) )
    

    #### Sectors File Generation
    sector_file = pd.DataFrame(None)
    sector_file = merged_all[merged_all['equity'].isin(sector_etfs +['XLE','XME'])]
    sector_file.reset_index(inplace=True, drop = True)
    sector_file.loc[:, 'date'] = lastbday
    s3conn.write_advanced_as_df(sector_file, file_generation_bucket,
                                file_generation_path.format(folderpath ='sector/client', product = 'sector' ,target_date = lastbday) )

    #### DB File Generation
    db_file = pd.DataFrame(None)
    db_file = merged_all[merged_all['equity'].isin(db_etfs)]
    db_file.reset_index(inplace=True, drop = True)
    db_file.loc[:, 'date'] = lastbday
    s3conn.write_advanced_as_df(db_file, file_generation_bucket,
                                file_generation_path.format(folderpath ='db/client', product = 'db' ,target_date = lastbday) )

