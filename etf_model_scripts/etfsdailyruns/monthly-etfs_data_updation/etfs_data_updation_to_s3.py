from helpers import *
s3conn = s3_config()
esconn = es_config(env='prod').client
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

def send_email(subject ,receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']

    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)

def run_djangoruns_s3updation(target_date):
    
    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    
    pred_index = config['misc']['es_index_name_pred']
    metrics_index = config['misc']['es_index_name_metrics']
    
    schedular = config['misc']['schedular']
    cols_to_remove_from_es = config['misc']['cols_to_remove_from_es']

    hist_pred_bucket  = config['S3_paths']['hist_pred_bucket']
    hist_pred_path  = config['S3_paths']['hist_pred_path']
    upload_hist_pred_bucket  = config['S3_paths']['upload_hist_pred_bucket']
    upload_hist_pred_path  = config['S3_paths']['upload_hist_pred_path']
    
    hist_metrics_bucket  = config['S3_paths']['hist_metrics_bucket']
    hist_metrics_path  = config['S3_paths']['hist_metrics_path']
    upload_hist_metrics_bucket  = config['S3_paths']['upload_hist_metrics_bucket']
    upload_hist_metrics_path  = config['S3_paths']['upload_hist_metrics_path']

    curr_year = int(target_date.split('-')[0])
    
    etf_data_fetched = get_data_from_es(aigo_etfs + db_etfs + sector_etfs, curr_year, pred_index, esconn, 
                                    target_date, schedular, cols_to_remove_from_es)
    
    etf_metrics_data_fetched = get_data_from_es(aigo_etfs + db_etfs + sector_etfs, curr_year, metrics_index, esconn, target_date, schedular, cols_to_remove_from_es)

    print('Updating the historical predictions file started')
    if not etf_data_fetched.empty:
        update_historical_predictions_data(etf_data_fetched, aigo_etfs, 'aigo', 
                                        hist_pred_bucket, hist_pred_path, upload_hist_pred_bucket, upload_hist_pred_path, s3conn)
        update_historical_predictions_data(etf_data_fetched, sector_etfs, 'sector', 
                                            hist_pred_bucket, hist_pred_path, upload_hist_pred_bucket, upload_hist_pred_path, s3conn)
        update_historical_predictions_data(etf_data_fetched, db_etfs, 'db', 
                                            hist_pred_bucket, hist_pred_path, upload_hist_pred_bucket, upload_hist_pred_path, s3conn)

    print('Updating the historical predictions file Done')

    print('Updating the historical metrics file started')
    if not etf_metrics_data_fetched.empty:
        update_historical_metrics_data(etf_metrics_data_fetched, aigo_etfs, 'aigo', 
                                        hist_metrics_bucket, hist_metrics_path, upload_hist_metrics_bucket, upload_hist_metrics_path, s3conn)
        update_historical_metrics_data(etf_metrics_data_fetched, sector_etfs, 'sector', 
                                            hist_metrics_bucket, hist_metrics_path, upload_hist_metrics_bucket, upload_hist_metrics_path, s3conn)
        update_historical_metrics_data(etf_metrics_data_fetched, db_etfs, 'db', 
                                            hist_metrics_bucket, hist_metrics_path, upload_hist_metrics_bucket, upload_hist_metrics_path, s3conn)
    print('Updating the historical metrics file Done')


def run_file_generation(target_date):

    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    schedular = config['misc']['schedular']

    hist_pred_bucket  = config['S3_paths']['hist_pred_bucket']
    hist_pred_path  = config['S3_paths']['hist_pred_path']
    
    hist_metrics_bucket  = config['S3_paths']['hist_metrics_bucket']
    hist_metrics_path  = config['S3_paths']['hist_metrics_path']

    file_generation_bucket = config['S3_paths']['upload_file_generation_bucket']
    file_generation_path = config['S3_paths']['upload_file_generation_path']

    delivery_metrics_bucket = config['S3_paths']['delivery_metrics_bucket']
    delivery_metrics_path = config['S3_paths']['delivery_metrics_path']

    
    ## getting the merged file
    merged_all_df = get_all_latest_predictions_and_metrics(aigo_etfs, sector_etfs, db_etfs, s3conn, target_date,schedular,
                                           hist_pred_bucket, hist_pred_path,delivery_metrics_bucket, delivery_metrics_path )

    print(f'generated merged data for all ETFs')


    ### filtering and uploading to respective paths
    do_file_generation(merged_all_df, aigo_etfs, sector_etfs, db_etfs, s3conn, target_date, file_generation_bucket, file_generation_path )
    
    print('upload product file to local s3 done')
    
    return merged_all_df

def run_djangoruns_s3updation_and_filegeneration(run_date):
    lastbday = (pd.to_datetime(run_date) - BDay(1)).strftime('%Y-%m-%d')
    print(run_date, lastbday)
    target_date = lastbday
    print(target_date)
    print(f'Running for Target Date : {target_date}')

    print('Django Run S3 updation started')
    run_djangoruns_s3updation(target_date)
    print('Django Run S3 updation Done')

    print('File generation started')
    final_file_df = run_file_generation(target_date)
    print('File generation done')
    return final_file_df

if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    
    try:
        
        final_file_df = run_djangoruns_s3updation_and_filegeneration(run_date)
        print(f'Django Runs updations to S3 & file generation: Run COMPLETED successfully for run date {run_date}')

        receiver = config['Gmail_creds']['email_receiver_success']
        send_email(f'Django Runs updations to S3 & file generation : Run COMPLETED successfully for run date {run_date}',receiver, final_file_df)
    
    except Exception as e:
        print(f'Django Runs updations to S3 & file generation : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config['Gmail_creds']['email_receiver_failed']
        send_email( f'Django Runs updations to S3 & file generation : Run FAILED for run date {run_date}',receiver, traceback.format_exc())
        raise