from helpers import *
s3conn = s3_config()
metrics_obj = MetricsHelper(None)
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

def send_email(subject ,receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']

    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)

def get_product_name(etf):
    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    bnp_etfs = config['misc']['bnp_etfs']

    if etf in aigo_etfs + sector_etfs:
        return 'aigo'
    elif etf in db_etfs:
        return 'db'
    elif etf in bnp_etfs:
        return 'bnp'

def do_s3_versioning( data_df, data_type,  pipeline, schedular, lastbday, upload_bucket, upload_path):

    data_df['date'] = data_df['date'].astype('str')

    data_df_latest = data_df[data_df['date'] == lastbday].copy()
    data_df_latest.reset_index(inplace = True, drop = True)

    for _,row in data_df_latest.iterrows():
        isin = row['isin']
        tic = row['tic']
        curr_df = pd.DataFrame([row])
        curr_df.reset_index(inplace = True, drop = True)
        product = get_product_name(tic)
        s3conn.write_advanced_as_df(curr_df, upload_bucket, 
                                upload_path.format(product =product, lower_schedular =schedular.lower(),latest_date=lastbday, 
                                                   folder = data_type, isin = isin, pipeline_name = pipeline))

    
    #print('s3 versioning of data done')

def fetch_etf_data_raw(etf: str, bucket: str, file_paths: list[str], s3conn) -> pd.DataFrame:
    for path in file_paths:
        try:
            df = s3conn.read_as_dataframe(bucket, path.format(etf=etf))
            if isinstance(df, pd.DataFrame):
                return df
            else:
                print(f"Data fetched from {path} is not a DataFrame.")
                raise ValueError("Invalid data type returned.")
        except Exception as e:
            #print(e)
            continue
    #logger.error(f"Failed to fetch ETF data for {etf}")
    raise FileNotFoundError(f"ETF data not found for {etf}")
    
def get_etf_data_native_monthly(etf: str) -> pd.DataFrame:
    
    raw_data_bucket = config['S3_paths']['all_pred_bucket']
    
    aigo_raw_data_path = config['S3_paths']['monthly_aigo_hist_pred_path']
    db_raw_data_path = config['S3_paths']['monthly_db_hist_pred_path']
    sector_raw_data_path = config['S3_paths']['monthly_sector_hist_pred_path']
    bnp_raw_data_path = config['S3_paths']['monthly_bnp_hist_pred_path']
    multiasset_raw_data_path = config['S3_paths']['monthly_multiasset_hist_pred_path']

    file_paths = [
        aigo_raw_data_path,
        db_raw_data_path,
        sector_raw_data_path,
        bnp_raw_data_path,
        multiasset_raw_data_path
    ]
    return fetch_etf_data_raw(etf, raw_data_bucket, file_paths, s3conn)

def get_etf_data_xform_wo_context_monthly(etf: str) -> pd.DataFrame:
    
    raw_data_bucket = config['S3_paths']['all_pred_bucket']
    
    raw_data_path = config['S3_paths']['monthly_xform_without_context_path']

    file_paths = [
        raw_data_path
    ]
    return fetch_etf_data_raw(etf, raw_data_bucket, file_paths, s3conn)

def get_etf_data_xform_wo_context_quarterly(etf: str) -> pd.DataFrame:
    
    raw_data_bucket = config['S3_paths']['all_pred_bucket']
    
    raw_data_path = config['S3_paths']['quarterly_xform_without_context_path']

    file_paths = [
        raw_data_path
    ]
    return fetch_etf_data_raw(etf, raw_data_bucket, file_paths, s3conn)

def get_etf_data_xform_w_context_monthly(etf: str) -> pd.DataFrame:
    
    raw_data_bucket = config['S3_paths']['all_pred_bucket']
    
    raw_data_path = config['S3_paths']['monthly_xform_with_context_path']

    file_paths = [
        raw_data_path
    ]
    return fetch_etf_data_raw(etf, raw_data_bucket, file_paths, s3conn)


def trigger_metrics_single (etf, pipeline, latest_date, prices_start_date, end_date, schedular, cols_to_choose_inhouse):
    inhouse_api_url_by_tic = config['Inhouse_API']['inhouse_api_url_by_tic']
    metrics_upload_bucket = config['S3_paths']['upload_model_metrics_bucket']
    metrics_upload_path = config['S3_paths']['upload_model_metrics_path']
    
    try:
        if pipeline == 'auto_ai':
            try:
                pred_df = get_etf_data_native_monthly(etf)
            except:
                return
        elif pipeline == 'xform_wo_context':
            try:
                if schedular == 'Monthly':
                    pred_df = get_etf_data_xform_wo_context_monthly(etf)
                elif schedular == 'Quarterly':
                    pred_df = get_etf_data_xform_wo_context_quarterly(etf)
            except:
                return
        elif pipeline == 'xform_w_context':
            try:
                pred_df = get_etf_data_xform_w_context_monthly(etf)
            except:
                return
        
        etf_raw_df = get_instockapi_data(inhouse_api_url_by_tic, etf, prices_start_date, end_date)
        etf_raw_df = etf_raw_df[cols_to_choose_inhouse]
        etf_raw_df['date'] = pd.to_datetime(etf_raw_df['date'])
    
        etf_predictions_df = pred_df[['date',f'{schedular.lower()}_predictions']].copy()
        etf_predictions_df['date'] = pd.to_datetime(etf_predictions_df['date'])
        etf_predictions_df.reset_index(inplace=True, drop=True)
    
        etf_temp = pd.merge(etf_raw_df, etf_predictions_df, on = ['date'], how = 'left')
        etf_temp.sort_values('date', ascending=True, inplace=True)
        etf_temp = etf_temp.ffill()
        etf_temp['date'] = etf_temp['date'].astype('str')
            
        metrics = calculate_metrics (metrics_obj ,etf, etf_temp, latest_date ,schedular,  prediction_column = f'{schedular.lower()}_predictions', 
                                    actual_column = f'actual_{schedular.lower()}_returns')

        if metrics.empty:
            print(f'No data for this etf : {etf}')
            return
            
        to_add_df = metrics[metrics ['date']== latest_date ]
        prev_df = s3conn.read_as_dataframe(metrics_upload_bucket, 
                                           metrics_upload_path.format(schedular = schedular, pipeline = pipeline, etf = metrics['isin'].values[0]))
    
        if to_add_df.empty:
            print(f'Hehe Boy, No data available for given date for {etf}')
            return
    
        to_add_df.reset_index(inplace=True, drop = True)
    
        if prev_df['tic'].values[0] == to_add_df['tic'].values[0] and prev_df.shape[1] == to_add_df.shape[1]:
            prev_df['date'] = pd.to_datetime(prev_df['date'])
            to_add_df['date'] = pd.to_datetime(to_add_df['date'])
            new_df = pd.concat([prev_df,to_add_df], axis= 0)
            new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
            new_df.sort_values('date', ascending=True, inplace=True)
            new_df.reset_index(inplace=True, drop=True)
            new_df['date'] = new_df['date'].astype('str')
            print(etf)
            print(new_df.isna().sum().sum())
            print(prev_df.shape, new_df.shape, new_df.date.values[0], new_df.date.values[-1])
            
            s3conn.write_advanced_as_df(new_df, metrics_upload_bucket, 
                                        metrics_upload_path.format(schedular = schedular, pipeline = pipeline, etf = metrics['isin'].values[0]))
            #break
            print(f'{etf} done')
        else:
            print(f'Error, shape mismatch for {etf}, {prev_df.shape}, {to_add_df.shape}')
        
    except Exception as e:
        print(e)

def run_for_all_pipelines (run_date, schedular):

    ### getting last business date
    latest_date=(pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
    start_date = (pd.to_datetime(latest_date) - timedelta(850)).strftime("%Y-%m-%d")
    prices_start_date = (pd.to_datetime(latest_date) - timedelta(days = 850)).strftime("%Y-%m-%d")
    end_date = (pd.to_datetime(latest_date)).strftime("%Y-%m-%d")
    print(start_date, end_date, prices_start_date)    

    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    bnp_etfs = config['misc']['bnp_etfs']
    etf_list = aigo_etfs + sector_etfs + db_etfs + bnp_etfs
    
    
    cols_to_choose_inhouse = ['date','isin','tic',f'actual_{schedular.lower()}_returns']

    inhouse_api_url_by_tic = config['Inhouse_API']['inhouse_api_url_by_tic']
    s3versioning_upload_bucket = config['S3_paths']['upload_s3versioning_bucket']
    s3versioning_upload_path = config['S3_paths']['upload_s3versioning_path']

    isin_tic_map_bucket = config['Tic_ISIN_map']['isin_tic_map_bucket']
    isin_tic_map_path = config['Tic_ISIN_map']['isin_tic_map_path']

    metrics_upload_bucket = config['S3_paths']['upload_model_metrics_bucket']
    metrics_upload_path = config['S3_paths']['upload_model_metrics_path']
    
    if schedular == 'Monthly':
        pipelines = ['auto_ai','xform_w_context', 'xform_wo_context']
    
    elif schedular == 'Quarterly':
         pipelines = ['xform_wo_context']
    
    for pipeline in pipelines:
        print(f'Starting for {pipeline} for {schedular} schedular')
        metrics_all = []
        predictions_all = []

        ### generate model metrics
        with ThreadPoolExecutor(max_workers=26) as exe:
            exe.map(trigger_metrics_single, etf_list, repeat(pipeline), repeat(latest_date) ,repeat(prices_start_date), repeat(end_date),
                    repeat(schedular), repeat(cols_to_choose_inhouse))
        
        ### s3 versioning part
        for etf in etf_list:
            if pipeline == 'auto_ai':
                try:
                    pred_df = get_etf_data_native_monthly(etf)
                except:
                    continue
            elif pipeline == 'xform_wo_context':
                try:
                    if schedular == 'Monthly':
                        pred_df = get_etf_data_xform_wo_context_monthly(etf)
                    elif schedular == 'Quarterly':
                        pred_df = get_etf_data_xform_wo_context_quarterly(etf)
                except:
                    continue
            elif pipeline == 'xform_w_context':
                try:
                    pred_df = get_etf_data_xform_w_context_monthly(etf)
                except:
                    continue
            
            raw_df = s3conn.read_as_dataframe(isin_tic_map_bucket, isin_tic_map_path)
            raw_df = raw_df[raw_df['tic']==etf]
            
            Isin = raw_df['isin'].values[0]
            
            if 'tic' not in pred_df.columns:
                pred_df['tic'] = etf
                
            if 'isin' not in  pred_df.columns:
                pred_df['isin'] = Isin
    
            pred_df = pred_df[pred_df['date']==latest_date]
            do_s3_versioning(pred_df,'predictions', pipeline , schedular, latest_date, s3versioning_upload_bucket, s3versioning_upload_path)

            metrics_oneday = s3conn.read_as_dataframe(metrics_upload_bucket, 
                                                      metrics_upload_path.format(schedular = schedular, pipeline = pipeline, etf = Isin))
            metrics_oneday = metrics_oneday[metrics_oneday['date']<= latest_date]
            metrics_oneday = metrics_oneday[-1:]
            metrics_oneday.reset_index(inplace = True, drop = True)
            
            metrics_all.append(metrics_oneday)

                
        metrics_all_df = pd.concat(metrics_all, axis = 0)
        metrics_all_df.reset_index(inplace = True, drop = True)
    
        print(f'starting s3 versioning for {schedular} schedular and {pipeline} pipeline')
        do_s3_versioning(metrics_all_df,'metrics', pipeline , schedular, latest_date, s3versioning_upload_bucket, s3versioning_upload_path)
        print(f's3 versioning done for {schedular} schedular and {pipeline} pipeline')
        
    print(f'Done for {schedular}')

if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')

    run_schedular  = config['misc']['run_schedular']

    for schedular in run_schedular:
        if schedular not in ['Monthly','Quarterly']:
            print(f'Invalid schedular : {schedular}, only accept in [Monthly,Quarterly]')
            continue
        
        try:
            run_for_all_pipelines(run_date, schedular)
            print(f'model_metrics_and_s3versioning run part completed for {schedular} schedular')

            print(f'{schedular} model_metrics_and_s3versioning: Run COMPLETED successfully for run date {run_date}')
            
            receiver = config['Gmail_creds']['email_receiver_success']
            send_email(f'{schedular} model_metrics_and_s3versioning : Run COMPLETED successfully for run date {run_date}',receiver, 'SUCCESS')
        
        except Exception as e:
            print(f'{schedular} model_metrics_and_s3versioning : Run FAILED for run date {run_date} due to the Error : {e}')
            print(traceback.format_exc())
            receiver = config['Gmail_creds']['email_receiver_failed']
            send_email( f'model_metrics_and_s3versioning : Run FAILED for run date {run_date}',receiver, traceback.format_exc())
            raise
        