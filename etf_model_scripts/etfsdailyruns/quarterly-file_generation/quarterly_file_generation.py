from helpers import *
s3conn = s3_config()
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)
    
def send_email(subject ,receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']

    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)

def get_product_name(etf):
    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    bnp_etfs = config['misc']['bnp_etfs']

    if etf in aigo_etfs + sector_etfs:
        return 'aigo'
    elif etf in db_etfs:
        return 'db'
    elif etf in bnp_etfs:
        return 'bnp'


def get_all_latest_predictions_and_metrics(aigo_pub_etfs, s3conn, target_date,schedular,
                                           hist_pred_bucket, hist_pred_path, 
                                           hist_metrics_bucket, hist_metrics_path ):
    
    all_predictions = []
    for etf in aigo_pub_etfs:
        curr_df = pd.DataFrame(None)

        curr_df = s3conn.read_as_dataframe(hist_pred_bucket, hist_pred_path.format( etf = etf))
        curr_df = curr_df[curr_df['date']<=target_date]
            
        curr_df  = curr_df[['date','tic','close_price','weekly_close_change','monthly_close_change','quarterly_close_change','smoothened_quarterly_predictions']][-1:]
        
        all_predictions.append(curr_df.to_dict('records')[0])

    all_predictions_df  = pd.DataFrame(all_predictions)
    
    # all_metrics_df = s3conn.read_as_dataframe(delivery_metrics_bucket, delivery_metrics_path.format(schedular = schedular,
    #                                                                                                schedular_lower = schedular.lower(),
    #                                                                                                latest_date = target_date))
    # all_metrics_df = all_metrics_df[['tic','avg_confidence_score', 'accuracy_22_day']]


    all_metrics = []
    for etf in aigo_pub_etfs:
        curr_df = pd.DataFrame(None)
        curr_df = s3conn.read_as_dataframe(hist_metrics_bucket, hist_metrics_path.format( etf = etf))
        #curr_df = get_csv_data_from_s3('etf-predictions',f'Quarterly/public_etfs/transformed_data/metrics/{etf}_metrics.csv')
        curr_df = curr_df[curr_df['date']<= target_date]
        curr_df  = curr_df[[ 'tic','avg_confidence_score', 'accuracy_22_day']][-1:]
        all_metrics.append(curr_df.to_dict('records')[0])

    all_metrics_df  = pd.DataFrame(all_metrics)
    
    print('all_predictions_df', all_predictions_df)
    print('all_metrics_df' , all_metrics_df)

    merged_all = pd.merge(all_predictions_df, all_metrics_df, on = ['tic'], how = 'left')
    merged_all.columns = ['date', 'tic', 'close_price', 'weekly_close_change','monthly_close_change', 'quarterly_close_change', 
                                               'quarterly_predictions','confidence_score','accuracy']
    
    return merged_all

def run_file_generation(target_date):

    aigo_pub_etfs = config['misc']['aigo_pub_etfs']
    schedular = config['misc']['schedular']

    hist_pred_bucket  = config['S3_paths']['hist_pred_bucket']
    hist_pred_path  = config['S3_paths']['hist_pred_path']

    hist_metrics_bucket = config['S3_paths']['hist_metrics_bucket']
    hist_metrics_path = config['S3_paths']['hist_metrics_path']

    file_generation_bucket = config['S3_paths']['upload_file_generation_bucket']
    file_generation_path = config['S3_paths']['upload_file_generation_path']
    
    ## getting the merged file
    merged_all_df = get_all_latest_predictions_and_metrics(aigo_pub_etfs, s3conn, target_date, schedular,
                                           hist_pred_bucket, hist_pred_path,hist_metrics_bucket, hist_metrics_path )

    print(f'generated merged data for all ETFs')

    ### uploading to file generation path
    s3conn.write_advanced_as_df(merged_all_df, file_generation_bucket, file_generation_path.format(target_date = target_date) )
    
    print('upload product file to local s3 done')
    
    return merged_all_df

def do_s3_versioning( data_df, data_type, schedular, lastbday, upload_bucket, upload_path):

    data_df['date'] = data_df['date'].astype('str')

    data_df_latest = data_df[data_df['date'] == lastbday].copy()
    data_df_latest.reset_index(inplace = True, drop = True)

    for _,row in data_df_latest.iterrows():
        isin = row['isin']
        tic = row['tic']
        curr_df = pd.DataFrame([row])
        curr_df.reset_index(inplace = True, drop = True)
        product = get_product_name(tic)
        s3conn.write_advanced_as_df(curr_df, upload_bucket, 
                                upload_path.format(product =product, lower_schedular =schedular.lower(),latest_date=lastbday, 
                                                   folder = data_type, isin = isin))



def run_file_generation_and_s3_versioning(run_date):

    lastbday = (pd.to_datetime(run_date) - BDay(1)).strftime('%Y-%m-%d')
    print(run_date, lastbday)
    target_date = lastbday
    print(target_date)
    print(f'Running for Target Date : {target_date}')

    ## run file generation
    merged_all_df = run_file_generation(target_date)

    ### run s3 versioning

    s3versioning_upload_bucket = config['S3_paths']['upload_s3versioning_bucket']
    s3versioning_upload_path = config['S3_paths']['upload_s3versioning_path']

    aigo_pub_etfs = config['misc']['aigo_pub_etfs']
    schedular = config['misc']['schedular']

    hist_pred_bucket  = config['S3_paths']['hist_pred_bucket']
    hist_pred_path  = config['S3_paths']['hist_pred_path']

    predictions_all = []
    for etf in aigo_pub_etfs:
        pred_df = s3conn.read_as_dataframe(hist_pred_bucket, hist_pred_path.format(etf = etf))
        etf_predictions_df = pred_df[['date','tic','isin',f'smoothened_{schedular.lower()}_predictions']].copy()
        etf_predictions_df['selected_pipeline'] = 'xform_wo_context'

        etf_predictions_df = etf_predictions_df[etf_predictions_df['date']==target_date]
        
        predictions_all.append(etf_predictions_df)
        
    predictions_all_df = pd.concat(predictions_all, axis = 0)
    
    do_s3_versioning(predictions_all_df,'predictions' , schedular, target_date, s3versioning_upload_bucket, s3versioning_upload_path)

    return merged_all_df


if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    
    try:
        
        final_file_df = run_file_generation_and_s3_versioning(run_date)
        print(f'Quarterly file generation: Run COMPLETED successfully for run date {run_date}')

        receiver = config['Gmail_creds']['email_receiver_success']
        send_email(f'Quarterly file generation : Run COMPLETED successfully for run date {run_date}',receiver, final_file_df)
    
    except Exception as e:
        print(f'Quarterly file generation : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config['Gmail_creds']['email_receiver_failed']
        send_email( f'Quarterly file generation : Run FAILED for run date {run_date}',receiver, traceback.format_exc())
        raise