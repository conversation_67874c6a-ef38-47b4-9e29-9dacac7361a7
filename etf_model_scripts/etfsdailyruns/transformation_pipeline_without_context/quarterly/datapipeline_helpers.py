import pandas as pd
import numpy as np
import pywt
import json
import joblib
import shutil
import boto3
import os
from statsmodels.tsa.stattools import adfuller
from sklearn.preprocessing import PowerTransformer
from scipy.stats.mstats import winsorize
from sklearn.decomposition import PCA
from typing import List, Set, Dict
import tempfile
from pandas.tseries.offsets import BDay
def get_training_metadata(trainingidentifier: str, datapipeline_read_bucket , datapipeline_read_path, s3) -> dict:
    """
    Load training metadata from a JSON file.

    Args:
        trainingidentifier (str): Unique experiment identifier.

    Returns:
        dict: Loaded training metadata.
    """
    #s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    s3bucket = datapipeline_read_bucket
    s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = 'training_metadata.json')
    
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        json_load_path = tmp_file.name  # Local path to the downloaded file
        
    print(f"Loading training metadata from {json_load_path}")
    
    with open(json_load_path, "r") as json_file:
        metadata =  json.load(json_file)

    os.remove(json_load_path)
    return metadata

def denoise(
    df: pd.DataFrame, columns_to_denoise: List[str], window_size: int = 50
) -> pd.DataFrame:
    """
    Remove noise from specified columns in a DataFrame using Discrete Wavelet Transform (DWT).

    Args:
        df (pd.DataFrame): Input DataFrame containing the data.
        columns_to_denoise (List[str]): List of column names to denoise.
        window_size (int, optional): Size of the sliding window. Default is 50.

    Returns:
        pd.DataFrame: DataFrame containing denoised data for the specified columns.
    """
    #print(f"Starting denoising process for columns: {columns_to_denoise} with window size: {window_size}")
    denoised_df = pd.DataFrame(index=df.index)

    wavelet = "db4"

    # Check for missing columns at once
    missing_columns = [column for column in columns_to_denoise if column not in df.columns]
    if missing_columns:
        print(f"The following columns were not found in the DataFrame and will be skipped: {missing_columns}")

    for column in columns_to_denoise:
        if column not in df.columns:
            continue

        #logger.debug("Denoising column: %s", column)

        # Create sliding windows over the data (ensure writable)
        windowed_data = np.lib.stride_tricks.sliding_window_view(df[column].values, window_shape=window_size).copy()
        denoised_data = np.zeros(len(df), dtype=np.float64)  # Ensure writable array

        # Vectorized denoising process
        coeffs_list = [pywt.wavedec(window, wavelet, level=1) for window in windowed_data]
        sigma_list = [np.median(np.abs(coeffs[1])) / 0.6745 for coeffs in coeffs_list]
        threshold_list = [sigma * np.sqrt(2 * np.log(window_size)) for sigma in sigma_list]

        denoised_windows = [
            pywt.waverec([
                pywt.threshold(c, threshold, mode="soft") if j > 0 else c
                for j, c in enumerate(coeffs)
            ], wavelet)
            for coeffs, threshold in zip(coeffs_list, threshold_list)
        ]

        # Assign the last value of each denoised window
        denoised_data[window_size - 1 :] = [window[-1] for window in denoised_windows]

        # Fill the initial values (before window_size) with the original data
        denoised_data[:window_size-1] = df[column][:window_size-1].values

        # Store the denoised column in the DataFrame
        denoised_df[column] = denoised_data
        #print(f"Finished denoising column: {column}")

    #print("Denoising process completed for all specified columns.")
    return denoised_df

def apply_stationarity_on_test_data(df: pd.DataFrame, column: str, 
                                    removed_trends: set, removed_heteroscedastic: set, 
                                    trainingidentifier: str, datapipeline_read_bucket , datapipeline_read_path, s3) -> pd.Series:
    """
    Apply stored transformations to test data to make it stationary.

    Args:
        df (pd.DataFrame): Input DataFrame.
        column (str): Column name of the time series.
        removed_trends (set): Set of columns where trends were removed during training.
        removed_heteroscedastic (set): Set of columns where heteroscedasticity was removed.
        trainingidentifier (str): Unique experiment identifier.

    Returns:
        pd.Series: Transformed stationary series.
    """

    #s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    
    #print(f"Applying stationarity transformations to column: {column}")

    if column in removed_trends:
        #logger.debug("Applying differencing for column: %s", column)
        df[f"{column}_differenced"] = df[column].diff().dropna()

        if column in removed_heteroscedastic:
            #logger.debug("Applying Yeo-Johnson transformation to differenced column: %s", column)
            #transformer_path = f"data/experimentfiles/{trainingidentifier}/yeo_johnson/yeo_johnson_transformer_{column}.joblib"
            transformer_path = f"yeo_johnson/yeo_johnson_transformer_{column}.joblib"

            s3bucket = datapipeline_read_bucket
            s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = transformer_path)
            
            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                s3.download_fileobj(s3bucket, s3path, tmp_file)
                transformer_load_path = tmp_file.name
            
            pt_loaded = joblib.load(transformer_load_path)
            os.remove(transformer_load_path)
            transformed = pt_loaded.transform(df[[f"{column}_differenced"]])
            df[f"{column}_yeo_johnson"] = transformed.flatten()
            return df[f"{column}_yeo_johnson"]

        return df[f"{column}_differenced"]

    elif column in removed_heteroscedastic:
        #logger.debug("Applying Yeo-Johnson transformation to column: %s", column)
        #transformer_path = f"data/experimentfiles/{trainingidentifier}/yeo_johnson/yeo_johnson_transformer_{column}.joblib"
        transformer_path = f"yeo_johnson/yeo_johnson_transformer_{column}.joblib"

        s3bucket = datapipeline_read_bucket
        s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = transformer_path)
        
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            s3.download_fileobj(s3bucket, s3path, tmp_file)
            transformer_load_path = tmp_file.name
        
        pt_loaded = joblib.load(transformer_load_path)
        os.remove(transformer_load_path)
        transformed = pt_loaded.transform(df[[column]])
        df[f"{column}_yeo_johnson"] = transformed.flatten()
        return df[f"{column}_yeo_johnson"]

    #logger.debug("No transformations needed for column: %s", column)
    return df[column]

def winsorize_data(df: pd.DataFrame, trainingidentifier: str, datapipeline_read_bucket , datapipeline_read_path, s3) -> pd.DataFrame:
    """
    Apply winsorization to the DataFrame using precomputed limits.

    Args:
        df (pd.DataFrame): Input DataFrame to be winsorized.
        trainingidentifier (str): Unique identifier for the training experiment.

    Returns:
        pd.DataFrame: Winsorized DataFrame.
    """

    #s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    
    #print("Applying winsorization to data.")
    #load_path = f"data/experimentfiles/{trainingidentifier}/winsorization/winsorization_limits.pkl"
    load_path = f"winsorization/winsorization_limits.pkl"

    s3bucket = datapipeline_read_bucket
    s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = load_path)
    
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        win_load_path = tmp_file.name
    
    winsorization_limits = joblib.load(win_load_path)
    os.remove(win_load_path)
    #print("Loaded winsorization limits from: %s", win_load_path)

    for col in df.columns:
        if col not in winsorization_limits:
            print(f"No winsorization limits found for column: {col}")
            raise ValueError(f"No winsorization limits found for column: {col}")
        lower_bound, upper_bound = winsorization_limits[col]
        df[col] = df[col].clip(lower_bound, upper_bound)
        #logger.debug("Winsorized column: %s | Lower Bound: %.5f | Upper Bound: %.5f", col, lower_bound, upper_bound)

    #print("Winsorization process completed.")
    return df

def do_normalization_standardization_test(df: pd.DataFrame, columns: List[str], trainingidentifier: str, datapipeline_read_bucket , datapipeline_read_path, s3) -> pd.DataFrame:
    """
    Apply normalization (standardization) to test data using previously saved mean and standard deviation values.

    Args:
        df (pd.DataFrame): Input DataFrame containing the test data.
        columns (List[str]): List of columns to standardize.
        trainingidentifier (str): Unique identifier for the training experiment.

    Returns:
        pd.DataFrame: DataFrame with standardized test data.
    """
    #s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    
    #print("Starting standardization process for test data.")
    epsilon = 1e-10
    standardization_path = f"standardization/standardization_values.pkl"
    #print(f"Loading standardization values from: {standardization_path}")
    
    s3bucket = datapipeline_read_bucket
    s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = standardization_path)
    
    with tempfile.NamedTemporaryFile(delete = False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        standardization_load_path = tmp_file.name
    
    standardization_values = joblib.load(standardization_load_path)
    os.remove(standardization_load_path)
    for col in columns:
        if col not in standardization_values:
            print(f"No standardization values found for column: {col}")
            raise ValueError(f"No standardization values found for column: {col}")

        mu, sigma = standardization_values[col]
        df[col] = (df[col] - mu) / (sigma + epsilon)  # Normalize
        #logger.debug("Standardized column: %s using Mean: %.5f | Std: %.5f", col, mu, sigma)

    #print("Standardization process completed for test data.")
    return df
def pca_transform_pipeline(df: pd.DataFrame, trainingidentifier: str, datapipeline_read_bucket , datapipeline_read_path, s3) -> pd.DataFrame:
    """
    Apply PCA transformations to the DataFrame based on saved PCA models.

    Args:
        df (pd.DataFrame): Input DataFrame to transform.
        trainingidentifier (str): Unique identifier for the training experiment.

    Returns:
        pd.DataFrame: Transformed DataFrame with PCA components.
    """
    #print("Starting PCA transformation pipeline.")
    #s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    
    json_path = f"pca/collinear_clusters.json"

    s3bucket = datapipeline_read_bucket
    s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = json_path)
    
    with tempfile.NamedTemporaryFile(delete= False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        json_load_path = tmp_file.name

    with open(json_load_path, "r") as json_file:
        colinear_sets_cluster = json.load(json_file)
    os.remove(json_load_path)
        
    colinear_sets = colinear_sets_cluster["clusters"]

    all_pc = []
    for i, cols in enumerate(colinear_sets):
        y = f"pca_{i}"
        model_path = f"pca/{y}.joblib"
        s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = model_path)
    
        with tempfile.NamedTemporaryFile(delete  = False) as tmp_file:
            s3.download_fileobj(s3bucket, s3path, tmp_file)
            model_load_path = tmp_file.name

        pca_loaded = joblib.load(model_load_path)
        os.remove(model_load_path)
        pc = pca_loaded.transform(df[cols])
        all_pc.append(pd.DataFrame(pc, columns=[y]))
        #logger.debug("Applied PCA transformation for cluster %d: %s", i, cols)

    all_pc_df = pd.concat(all_pc, axis=1)
    #print("PCA transformation pipeline completed.")
    return all_pc_df

def inverse_heteroscedastic(df: pd.DataFrame, column: str, trainingidentifier: str, datapipeline_read_bucket , datapipeline_read_path, s3) -> pd.Series:
    """
    Apply inverse Yeo-Johnson transformation to stabilize variance.

    Args:
        df (pd.DataFrame): Input DataFrame.
        column (str): Column name to apply inverse transformation.
        trainingidentifier (str): Unique experiment identifier.

    Returns:
        pd.Series: Inverse transformed series.
    """
    transformer_path = f"yeo_johnson/yeo_johnson_transformer_{column}.joblib"
    #s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    s3bucket = datapipeline_read_bucket
    s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = transformer_path)
    
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        transformer_load_path = tmp_file.name

    pt_loaded = joblib.load(transformer_load_path)
    inversed = pt_loaded.inverse_transform(df[[column]])
    return pd.Series(inversed.flatten(), name=f"{column}_yeo_johnson_inversed")
    
def inverse_transform_target(df: pd.DataFrame, column_in_df: str, target_col: str, 
                             trainingidentifier: str, removed_heteroscedastic: set, datapipeline_read_bucket , 
                             datapipeline_read_path, s3) -> pd.DataFrame:
    """
    Perform inverse transformations (denormalization and variance stabilization) on target data.

    Args:
        df (pd.DataFrame): Input DataFrame.
        column_in_df (str): Column to transform.
        target_col (str): Original target column name.
        trainingidentifier (str): Unique experiment identifier.
        removed_heteroscedastic (set): Set of columns with heteroscedasticity removed.

    Returns:
        pd.DataFrame: DataFrame with inverse transformed target column.
    """
    #print("Performing inverse transformations for target column: %s", column_in_df)
    df = df[['date', column_in_df]].copy()

    # Denormalization
    standardization_path = f"standardization/standardization_values.pkl"
    #print(f"Loading standardization values from: {standardization_path}")
    #s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    s3bucket = datapipeline_read_bucket
    s3path = datapipeline_read_path.format(trainingidentifier = trainingidentifier, dir_with_filename = standardization_path)
    
    with tempfile.NamedTemporaryFile(delete = False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        standardization_load_path = tmp_file.name
        
    standardization_values = joblib.load(standardization_load_path)
    mean, sd = standardization_values[target_col]

    df[target_col] = df[column_in_df] * sd + mean

    # De-heteroscedasticity
    if target_col in removed_heteroscedastic:
        print(f"Applying inverse heteroscedastic transformation for column: {target_col}")
        df[target_col] = inverse_heteroscedastic(df.copy(), target_col, trainingidentifier, datapipeline_read_bucket , 
                             datapipeline_read_path, s3)

    # Rename column and return
    df = df[['date', target_col]]
    df.columns = ['date', column_in_df]
    return df
    