from helpers import *
from datapipeline_helpers import *
s3conn = s3_config()
esconn = es_config(env='prod').client

s3_client = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret, config=botocore.config.Config(max_pool_connections=100))

with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

wml_credentials = ibm_watsonx_ai.Credentials(
                   url = 'https://' + config['IBM_details']['location'] + '.ml.cloud.ibm.com',
                   api_key = config['IBM_details']['api_key']
                  )
smoothening_gradient = get_smoothening_gradients()
metrics_obj = MetricsHelper(None)

def send_email(subject , receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)

def fetch_etf_data_raw(etf: str, bucket: str, file_paths: list[str], s3conn) -> pd.DataFrame:
    for path in file_paths:
        try:
            df = s3conn.read_as_dataframe(bucket, path.format(etf=etf))
            if isinstance(df, pd.DataFrame):
                return df
            else:
                print(f"Data fetched from {path} is not a DataFrame.")
                raise ValueError("Invalid data type returned.")
        except Exception as e:
            #print(e)
            continue
    #logger.error(f"Failed to fetch ETF data for {etf}")
    raise FileNotFoundError(f"ETF data not found for {etf}")
    
def get_etf_data_raw_monthly_source(etf: str) -> pd.DataFrame:

    raw_data_bucket = config['S3_paths']['raw_data_bucket']

    aigo_raw_data_path = config['S3_paths']['aigo_raw_data_path']
    db_raw_data_path = config['S3_paths']['db_raw_data_path']
    sector_raw_data_path = config['S3_paths']['sector_raw_data_path']
    bnp_raw_data_path = config['S3_paths']['bnp_raw_data_path']

    file_paths = [
        aigo_raw_data_path,
        db_raw_data_path,
        sector_raw_data_path,
        bnp_raw_data_path
    ]
    return fetch_etf_data_raw(etf, raw_data_bucket, file_paths, s3conn)

def get_etf_data_raw(etf: str) -> pd.DataFrame:

    raw_data_bucket = config['S3_paths']['pubetf_raw_data_bucket']

    pubetf_raw_data_path = config['S3_paths']['pubetf_raw_data_path']

    file_paths = [
        pubetf_raw_data_path
    ]
    return fetch_etf_data_raw(etf, raw_data_bucket, file_paths, s3conn)

def update_quarterly_data_for_run (public_etfs):

    quarterly_raw_upload_bucket = config['S3_paths']['upload_quarterly_raw_bucket']
    quarterly_raw_upload_path = config['S3_paths']['upload_quarterly_raw_path']
    
    for etf in public_etfs:
        df = get_etf_data_raw_monthly_source(etf)
        df.drop(columns=['monthly_predictions','actual_monthly_returns'], axis = 1, inplace=True)
        df['actual_quarterly_returns'] = df['quarterly_close_change'].shift(-66)
        #upload_csv_data_to_s3(df,'histetfdata','temp/quarterly_training_data/public_etfs/',etf)
        s3conn.write_advanced_as_df(df, quarterly_raw_upload_bucket, quarterly_raw_upload_path.format(etf = etf))
        print(df.date.values[-1])


def process_for_test_data(Isin ,trainingidentifier, train_end_date, datapipeline_read_bucket , datapipeline_read_path, datapipeline_upload_bucket, datapipeline_upload_path)-> None:
    """
    Main pipeline function to process ETF data for test data generation.
    """
    #trainingidentifier = f'{Isin}_{freq}_{period[0]}'
    print(f"Starting test data generation for {trainingidentifier}.")

    try:
    
        startdate_for_test  = pd.to_datetime(train_end_date) - BDay(60)
        startdate_for_test = startdate_for_test.strftime('%Y-%m-%d')
    
        # Fetch training metadata
        training_metadata = get_training_metadata(trainingidentifier, datapipeline_read_bucket , datapipeline_read_path, s3_client)
        master_features = set(training_metadata['removed_trends'] + \
                                training_metadata['removed_heteroscedastic'] + \
                                training_metadata['removed_multicollinearity'] + \
                                training_metadata['final_features'])
    
        master_columns_needed = [feature for feature in master_features if 'pca_' not in feature]
        print(f"{len(master_columns_needed)} master features identified for {trainingidentifier}.")
    
        # Load ETF data
        tic_data = get_etf_data_raw(Isin)
        columns_to_include = [column for column in tic_data.columns if column in master_columns_needed]
        tic_data = tic_data[columns_to_include]
        tic_data = tic_data[tic_data['date'] >= startdate_for_test]
        tic_data.set_index('date', inplace=True)
        tic_data_for_test = tic_data
    
        if tic_data_for_test.empty:
            print("Empty dataframe after date filtering.")
            raise ValueError("Empty dataframe after date filtering.")

        # Fetch pipeline configurations
        training_pipeline = training_metadata['pipeline']
        to_denoise = training_pipeline['to_denoise']
        to_stationarize = training_pipeline['to_stationarize']
        to_winorize = training_pipeline['to_winorize']
        to_normalize = training_pipeline['to_normalize']
        to_remove_multicollinearity = training_pipeline['to_remove_multicollinearity']
    
        new_df = tic_data_for_test.copy()
    
        # Denoise the data
        if to_denoise:
            print(f"Denoising data for {trainingidentifier}.")
            new_df = denoise(new_df.copy(), new_df.columns)

        # Stationarize the data
        if to_stationarize:
            print(f"Stationarizing data for {trainingidentifier}.")
            stationarized_data = []
            for column in new_df.columns:
                stationarized_data.append(apply_stationarity_on_test_data(
                    new_df.copy(), column, training_metadata['removed_trends'],
                    training_metadata['removed_heteroscedastic'], trainingidentifier, datapipeline_read_bucket , datapipeline_read_path, s3_client))
    
            stationarized_df = pd.concat(stationarized_data, axis=1)
            stationarized_df.columns = stationarized_df.columns.str.replace('_yeo_johnson|_differenced', '', regex=True)
            stationarized_df = stationarized_df[1:].copy()
    
            columns_with_nan = stationarized_df.isna().any()
            columns_with_nan = columns_with_nan[columns_with_nan].index.tolist()
            if columns_with_nan:
                columns_to_remove = [col for col in columns_with_nan if col not in ['actual_monthly_returns','actual_quarterly_returns','actual_daily_returns']]
                if columns_to_remove:
                    print(f"Columns with NaN after stationarization: {columns_to_remove}")
                    stationarized_df[columns_to_remove] = stationarized_df[columns_to_remove].fillna(method = 'ffill').fillna(0)
                    #raise ValueError("NaN values present after stationarizing. Check data integrity.")
                
            new_df = stationarized_df
            print("Stationarization completed successfully.")
            print(f"Total NaN values after Stationarization {new_df.isna().sum().sum()}")
    
        # Clip outliers
        if to_winorize:
            print(f"Applying winsorization for {trainingidentifier}.")
            new_df = winsorize_data(new_df.copy(), trainingidentifier, datapipeline_read_bucket , datapipeline_read_path, s3_client)
            print("Winsorization completed successfully.")

        # Normalize the data
        if to_normalize:
            print(f"Normalizing data for {trainingidentifier}.")
            new_df = do_normalization_standardization_test(new_df.copy(), new_df.columns, trainingidentifier, datapipeline_read_bucket , datapipeline_read_path, s3_client)
            print("Normalization completed successfully.")
    
        # Remove multicollinearity
        if to_remove_multicollinearity:
            print(f"Removing multicollinearity for {trainingidentifier}.")
    
            allpcas = pca_transform_pipeline(new_df, trainingidentifier, datapipeline_read_bucket , datapipeline_read_path, s3_client)
            allpcas.index = new_df.index
    
            allpcas_normalized = do_normalization_standardization_test(allpcas.copy(), allpcas.columns, trainingidentifier, datapipeline_read_bucket , datapipeline_read_path, s3_client)
            allpcas_normalized.reset_index(inplace=True)
    
            allcols_used_for_pca = training_metadata['removed_multicollinearity']
    
            new_df = new_df.drop(columns=allcols_used_for_pca , axis = 1 )
            new_df.reset_index(inplace =True)
    
            new_df = pd.merge(allpcas_normalized, new_df, on='date', how='inner')
            new_df.set_index('date', inplace=True)
    
        print(f"Test data preparation completed for {trainingidentifier}.")
        new_df.reset_index(inplace=True)
        
        # Save the generated test data
        new_df = new_df[50:]
        new_df.reset_index(inplace=True, drop=True)
        #new_df.to_csv(f'data/experimentfiles/{trainingidentifier}/test_data.csv', index=False)
        s3conn.write_advanced_as_df(new_df, datapipeline_upload_bucket, datapipeline_upload_path.format(trainingidentifier = trainingidentifier, dir_with_filename = 'test_data.csv'))
        print(f"Test data saved for {trainingidentifier}. Data range: {new_df.date.values[0]} to {new_df.date.values[-1]}")
    except Exception as e:
        print(e)

def trigger_data_pipeline(row):
    trainingidentifier = row['deployment_name']
    train_end_date = row['train_end_date']
    Isin = row['tic']
    print(f'{Isin} started')
    
    datapipeline_upload_bucket = config['S3_paths']['upload_datapipeline_bucket']
    datapipeline_upload_path = config['S3_paths']['upload_datapipeline_path']
    datapipeline_read_bucket = config['S3_paths']['datapipeline_read_bucket']
    datapipeline_read_path = config['S3_paths']['datapipeline_read_path']
    
    process_for_test_data(Isin, trainingidentifier, train_end_date, datapipeline_read_bucket , 
                      datapipeline_read_path, datapipeline_upload_bucket, datapipeline_upload_path)
    print(f'{Isin} finished')

def get_inputs_data_and_generate_predictions(row, target_date):
    try:

        datapipeline_read_bucket = config['S3_paths']['datapipeline_read_bucket']
        datapipeline_read_path = config['S3_paths']['datapipeline_read_path']
        
        predictions_upload_bucket = config['S3_paths']['upload_daily_predictions_bucket']
        predictions_upload_path = config['S3_paths']['upload_daily_predictions_path']
    
        service = Batch(wml_credentials, source_space_id=row['space_id'])
        wmlclient = APIClient(credentials = wml_credentials, space_id = row['space_id'])

        trainingidentifier = row['deployment_name']
        
        df = s3conn.read_as_dataframe(datapipeline_read_bucket, datapipeline_read_path.format(trainingidentifier = trainingidentifier,
                                                                                    dir_with_filename= 'test_data.csv'))
        #print(df)
        df = df[df['date'] == target_date]
        if df.empty:
            print(f'No New data for {row["tic"]}')
            return
        input_features = ast.literal_eval(row['input_features'])
    
        is_subset = set(input_features).issubset(set(df.columns))
        
        if not is_subset:
            print(f"Not all columns are present for {row['tic']}")
            return
    
        temp = df[input_features]
        
        # service.get(row['deployment_id'])
        # scoring_params = service.run_job(temp, background_mode=False)
        # job_id = scoring_params["metadata"].get("id")
        # job_details = client.deployments.get_job_details(job_id)
        # values = np.array(job_details['entity']['scoring']['predictions'][0]['values']).flatten()
        # df["quarterly_predictions"] = values

        for i in range(3): ## number of times predictions job to be created in case of fail
            start = time.perf_counter()
        
            service.get(row['deployment_id'])
            scoring_params = service.run_job(temp, background_mode=True)
            job_id = scoring_params["metadata"].get("id")
        
            infer_time = time.perf_counter()-start
            status = service.get_job_status(job_id)['state']
        
            while(status!='completed' and infer_time<= 8*60):
                if status=='failed':
                    break
                print(status)
                time.sleep(5)
                infer_time = time.perf_counter()-start
                status = service.get_job_status(job_id)['state']
        
            if status== 'completed':
                print(status)
                job_results = service.get_job_result(job_id)
                values = np.array(job_results['prediction'].values).flatten()
                break
        
            if status=='failed':
                print('Job Failed, wait and recreate the job again')
                time.sleep(30)
        
            else:
                print(status)
                try:
                    wmlclient.deployments.delete_job(job_id, hard_delete=True)
                except Exception as e:
                    print(e)
                
        df["quarterly_predictions"] = values
        
        s3conn.write_advanced_as_df(df, predictions_upload_bucket, 
                                    predictions_upload_path.format(target_date = target_date, folder_n = 'skip_quarter_mod',
                                                                   trainingidentifier = trainingidentifier))


        ### Applying Descaling on predictions

        temp = df
        temp.reset_index(inplace=True, drop=True)
        training_metadata = get_training_metadata(trainingidentifier, datapipeline_read_bucket , datapipeline_read_path, s3_client)

        transformed_predictions = inverse_transform_target(temp.copy(), 'quarterly_predictions','actual_quarterly_returns',
                                                           trainingidentifier,training_metadata['removed_heteroscedastic'],
                                                          datapipeline_read_bucket , datapipeline_read_path, s3_client)

        s3conn.write_advanced_as_df(transformed_predictions, predictions_upload_bucket, 
                                    predictions_upload_path.format(target_date = target_date, folder_n = 'skip_quarter_mod_original_scale',
                                                                   trainingidentifier = trainingidentifier))

        data = transformed_predictions
        data['date'] = pd.to_datetime(data['date'])

        print(trainingidentifier, data.date.values[0])
        
        df_raw = get_etf_data_raw(row['tic'])
        if 'quarterly_predictions' in df_raw.columns:
            df_raw.drop(columns = 'quarterly_predictions', axis = 1, inplace = True)
        
        df_raw['date'] = pd.to_datetime(df_raw['date'])
        df_raw = df_raw[df_raw['date']== pd.to_datetime(target_date)]
        
        df_comb = pd.merge(df_raw, data, on='date', how = 'left')

        s3conn.write_advanced_as_df(df_comb, predictions_upload_bucket, 
                                    predictions_upload_path.format(target_date = target_date, 
                                                                   folder_n = 'skip_quarter_mod_original_scale_with_raw_feature',
                                                                   trainingidentifier = trainingidentifier))
        
        print(f"{row['tic']} Done")
        
    except Exception as e:
        print(e)
        
def complete_daily_run_metrics(schedular, public_etfs, latest_date, 
                               hist_pred_bucket, hist_pred_path,
                               hist_metrics_bucket, hist_metrics_path, 
                               upload_hist_metrics_bucket, upload_hist_metrics_path):
    
    for etf in public_etfs:
        
        etf_temp = s3conn.read_as_dataframe(hist_pred_bucket, hist_pred_path.format(etf=etf, folder_path = 'predictions_with_raw_features'))
        etf_temp['date'] = etf_temp['date'].astype('str')
        
        metrics= calculate_metrics (metrics_obj ,etf, etf_temp,latest_date, schedular,  prediction_column = f'{schedular.lower()}_predictions', 
                                    actual_column = f'actual_{schedular.lower()}_returns')
        
        if metrics.empty:
            print(f'No data for this etf : {etf}')
            continue
            
        to_add_df = metrics[metrics ['date']== latest_date ]
        prev_df = s3conn.read_as_dataframe(hist_metrics_bucket, hist_metrics_path.format(etf=etf))
    
        if to_add_df.empty:
            print(f'Hehe Boy, No data available for given date for {etf}')
            continue
    
        to_add_df.reset_index(inplace=True, drop = True)
    
        if prev_df['tic'].values[0] == to_add_df['tic'].values[0] and prev_df.shape[1] == to_add_df.shape[1]:
            prev_df['date'] = pd.to_datetime(prev_df['date'])
            to_add_df['date'] = pd.to_datetime(to_add_df['date'])
            new_df = pd.concat([prev_df,to_add_df], axis= 0)
            new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
            new_df.sort_values('date', ascending=True, inplace=True)
            new_df.reset_index(inplace=True, drop=True)
            new_df['date'] = new_df['date'].astype('str')
            print(etf)
            print(new_df.isna().sum().sum())
            print(prev_df.shape, new_df.shape, new_df.date.values[0], new_df.date.values[-1])
            
            # upload_csv_data_to_s3(new_df,'etf-predictions','Monthly/multiasset/metrics/',f'{etf}_metrics')
            s3conn.write_advanced_as_df(new_df,upload_hist_metrics_bucket, upload_hist_metrics_path.format(etf=etf))
            #break
            print(f'{etf} done')
        else:
            print(f'Error, shape mismatch for {etf}, {prev_df.shape}, {to_add_df.shape}')

def run_daily_runs_transformation_pipeline(run_date):
    lastbday = (pd.to_datetime(run_date) - BDay(1)).strftime('%Y-%m-%d')
    print(run_date, lastbday)
    target_date = lastbday
    print(target_date)
    print(f'Running for Target Date : {target_date}')

    public_etfs = config['misc']['aigo_pub_etfs']
    schedular = config['misc']['schedular']

    ### updating quarterly data for daily runs
    update_quarterly_data_for_run(public_etfs)

    #### Getting deployments data ###
    deployments_list_bucket = config['S3_paths']['quarterly_deployments_bucket']
    deployments_list_path = config['S3_paths']['quarterly_deployments_path']
    deployments_data = s3conn.read_as_dataframe(deployments_list_bucket, deployments_list_path)
    deployments_data = deployments_data[deployments_data.test_end_date.isna()]
    deployments_data.reset_index(inplace = True, drop = True)
    print(deployments_data.shape, f'Total {deployments_data.shape[0]} for todays run')

    
    ### triggering data transformation pipeline 
    with ThreadPoolExecutor(max_workers=28) as exe:
        exe.map(trigger_data_pipeline, [row for _, row in deployments_data.iterrows()])
    print('Data Transformation pipeline done')

    #### Running AutoAI Predictions for last BDAY & rescale back
    with ThreadPoolExecutor(max_workers=28) as exe:
        exe.map(get_inputs_data_and_generate_predictions, [row for _, row in deployments_data.iterrows()], repeat(target_date))    
    
    print('predictions generation done')


    dailyruns_read_bucket=  config['S3_paths']['dailyruns_read_bucket']
    dailyruns_read_path= config['S3_paths']['dailyruns_read_path']
    
    hist_pred_bucket = config['S3_paths']['hist_pred_bucket']
    hist_pred_path= config['S3_paths']['hist_pred_path']
    
    upload_hist_pred_bucket =  config['S3_paths']['upload_hist_pred_bucket']
    upload_hist_pred_path = config['S3_paths']['upload_hist_pred_path']

    ## doing smoothening and updating raw data to historical file
    do_smoothening_update_historical_file_raw (deployments_data, dailyruns_read_bucket , dailyruns_read_path,
                                       hist_pred_bucket, hist_pred_path, s3conn,
                                       upload_hist_pred_bucket, upload_hist_pred_path , target_date, smoothening_gradient)

    #### appending original features to historical file
    do_smoothening_update_historical_file_original (deployments_data, dailyruns_read_bucket , dailyruns_read_path,
                                       hist_pred_bucket, hist_pred_path, s3conn,
                                       upload_hist_pred_bucket, upload_hist_pred_path , target_date, smoothening_gradient)


    hist_metrics_bucket = config['S3_paths']['hist_metrics_bucket']
    hist_metrics_path= config['S3_paths']['hist_metrics_path']
    
    upload_hist_metrics_bucket =  config['S3_paths']['upload_hist_metrics_bucket']
    upload_hist_metrics_path = config['S3_paths']['upload_hist_metrics_path']

    ## calculating metrics and appending to historical file
    complete_daily_run_metrics(schedular, public_etfs, target_date, 
                               hist_pred_bucket, hist_pred_path,
                               hist_metrics_bucket, hist_metrics_path, 
                               upload_hist_metrics_bucket, upload_hist_metrics_path)

    print('metrics Done')

    es_index_name_pred =  config['misc']['es_index_name_pred']
    es_index_name_metrics = config['misc']['es_index_name_metrics']
    
    
    read_and_upload_data_to_es(s3conn, esconn, public_etfs, hist_pred_bucket, hist_pred_path,
                                      hist_metrics_bucket, hist_metrics_path,
                                      es_index_name_pred, es_index_name_metrics, target_date, schedular)
    print('upload to ES done')

if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    
    try:
        
        run_daily_runs_transformation_pipeline(run_date)
        print(f'Quarterly Transformation without Context: Run COMPLETED successfully for run date {run_date}')

        receiver = config['Gmail_creds']['email_receiver_success']
        send_email(f'Quarterly Transformation without Context : Run COMPLETED successfully for run date {run_date}',receiver, 'SUCCESS')
    
    except Exception as e:
        print(f'Quarterly Transformation without Context : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config['Gmail_creds']['email_receiver_failed']
        send_email( f'Quarterly Transformation without Context : Run FAILED for run date {run_date}',receiver, traceback.format_exc())
        raise