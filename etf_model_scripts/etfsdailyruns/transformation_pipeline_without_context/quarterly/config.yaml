Gmail_creds:
    gmail_creds_bucket : etf-predictions
    gmail_creds_path: preportfolio/gmail_credentials/credentials.json
    gmail_sender : <EMAIL>
    email_receiver_success : <EMAIL>, <EMAIL>, <EMAIL>
    email_receiver_failed : <EMAIL>, <EMAIL>, <EMAIL>

Tic_ISIN_map:
    isin_tic_map_bucket : etf-predictions
    isin_tic_map_path : preportfolio/etfs_isin_map/etfs_tic_isin_mapping.csv
    
Master_active_firms:
    api_url : http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all

Inhouse_API:
    inhouse_api_url_by_isin : http://************:8080/stockhistory/getstocksbyisin?isin={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA
    inhouse_api_url_by_tic : http://************:8080/stockhistory/getstocksbytic?tic={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA

S3_paths:
    quarterly_deployments_bucket : etf-predictions
    quarterly_deployments_path : Quarterly/public_etfs/transformed_data/deployments/pipeline_without_context_deployments_quarterly_returns.csv

    upload_quarterly_raw_bucket : histetfdata ### do not change original bucket
    upload_quarterly_raw_path : temp/quarterly_training_data/public_etfs/{etf}.csv

    raw_data_bucket : etf-predictions
    aigo_raw_data_path :   Monthly/aigo/predictions/{etf}_predictions.csv
    db_raw_data_path : Monthly/db/predictions/{etf}_predictions.csv
    sector_raw_data_path : Monthly/sector/predictions/{etf}_predictions.csv
    bnp_raw_data_path : Monthly/new_BNP_Paribas/updated_training_data/{etf}.csv

    pubetf_raw_data_bucket : histetfdata ### do not change original bucket
    pubetf_raw_data_path : temp/quarterly_training_data/public_etfs/{etf}.csv

    upload_datapipeline_bucket : etf-predictions
    upload_datapipeline_path : Quarterly/public_etfs/transformed_data/datapipelinefiles/{trainingidentifier}/{dir_with_filename}

    datapipeline_read_bucket : etf-predictions
    datapipeline_read_path : Quarterly/public_etfs/transformed_data/datapipelinefiles/{trainingidentifier}/{dir_with_filename}

    upload_daily_predictions_bucket : etf-predictions
    upload_daily_predictions_path : Quarterly/public_etfs/transformed_data/daily_runs/{target_date}/{folder_n}/{trainingidentifier}.csv

    dailyruns_read_bucket: etf-predictions
    dailyruns_read_path : Quarterly/public_etfs/transformed_data/daily_runs/{target_date}/{folder_path}/{ti}.csv
    
    hist_pred_bucket :  etf-predictions
    hist_pred_path : Quarterly/public_etfs/transformed_data/{folder_path}/{etf}_predictions.csv

    hist_metrics_bucket :  etf-predictions
    hist_metrics_path : Quarterly/public_etfs/transformed_data/metrics/{etf}_metrics.csv
    
    upload_hist_pred_bucket :  etf-predictions
    upload_hist_pred_path : Quarterly/public_etfs/transformed_data/{folder_path}/{etf}_predictions.csv

    upload_hist_metrics_bucket :  etf-predictions
    upload_hist_metrics_path : Quarterly/public_etfs/transformed_data/metrics/{etf}_metrics.csv

    
misc:
    
    aigo_etfs : [QQQ, EEM, EFA, IWM, IYR, EWJ, SPY, XME, XLE,TIP, LQD, TLT, SHY, EMB, HYG, GLD, BNDX,MQFIUSTU, MQFIUSTY, MQFIUSUS, HSMETYSN,
                    HSMETYV3]
    sector_etfs : [ XLB, XLV, XLP, XLY, XLF, XLI, XLK, XLC, XLRE, XLU]
    db_etfs : [DBEEEMGF,DBEETGFU,DBEEURGF,DBEEUNGF,DBEEUGFT,DBRCOYGC,DBDRUS10,DBDRUS02,DBDRUS20]
    bnp_etfs : [BNPIFU10,BNPIFCN, BNPIFJP, BNPIFE10, BNPIFJ10, BNPIDSBU,BNPIFUS, BNPIFEM, BNPIG0GC, BNPIFEU]

    aigo_pub_etfs : [QQQ, EEM, EFA, IWM, IYR, EWJ, SPY, XME, XLE,TIP, LQD, TLT, SHY, EMB, HYG, GLD, BNDX, XLB, XLV, XLP, XLY, XLF, XLI, XLK, XLC,
                        XLRE, XLU]

    es_index_name_pred : eq_etf_model
    es_index_name_metrics : eq_etf_model_metrics
    schedular : Quarterly

IBM_details :
    api_key : 65x_WGURZjRYyJIvUGWEoJSlNQwCAxl0hyOmlJqCTar8
    location : us-south