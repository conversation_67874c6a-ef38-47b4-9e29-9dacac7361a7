import sys
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from opensearchpy.connection.http_requests import RequestsHttpConnection
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from itertools import repeat
import ast
from opensearchpy.client import OpenSearch
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
import requests
import pandas as pd
import ibm_watsonx_ai
from ibm_watsonx_ai import APIClient
from ibm_watsonx_ai.deployment import Batch
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
import traceback
from concurrent.futures import ThreadPoolExecutor
import botocore
import time

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def get_smoothening_gradients():
    gradient_size = 5
    multiplier = 1 / (np.e * (np.e ** gradient_size - 1) / (np.e - 1))
    smoothening_gradient = [multiplier * (np.e ** (i + 1)) for i in range(gradient_size)]
    return smoothening_gradient

def get_instockapi_data (api_url, firm ,start_date, end_date):
    '''
        Function to get price data from Inhouse Stock API
    '''
    isin = firm
    stock_data = requests.get(api_url.format(identifier = isin, startdate = start_date, enddate = end_date)).json()
 
    stock_data = pd.DataFrame(stock_data['data']['stocks'])[['date','adj_close']]
 
    stock_data.rename(columns={'adj_close':'close_price'}, inplace=True)
    stock_data=stock_data[['date','close_price']]
    stock_data['date']=pd.to_datetime(stock_data['date'])
    stock_data.sort_values('date', ascending =True , inplace=True)
    stock_data.reset_index(inplace=True, drop=True)
 
    stock_data['monthly_close_change']=stock_data['close_price'].pct_change(periods=22)*100
    stock_data['actual_monthly_returns']=stock_data['monthly_close_change'].shift(-22)
    stock_data['date'] = stock_data['date'].astype(str)
    return stock_data


def prepare_body_html(body):
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html

def apply_smoothing(x, gradient):
    if len(x) == len(gradient):  # Apply smoothing only if window size matches
        return np.sum(np.array(x) * gradient)
    else:
        return x.iloc[-1]

def get_bnp_training_data(s3conn, etf, bucket, path):
    df = s3conn.read_as_dataframe(bucket, path.format(etf=etf))
    if 'actual_monthly_change' in df.columns:
        df.rename(columns = {'actual_monthly_change':'actual_monthly_returns'}, inplace = True)
    if 'Date' in df.columns:
        df.rename(columns = {'Date':'date'}, inplace = True)
    if isinstance(df, pd.DataFrame):
        df.reset_index(inplace=True, drop=True)
        df.drop_duplicates(subset='date',keep = 'last', inplace = True)
    return df
def get_bnp_daily_data(s3conn, etf, bucket, path):
    df = s3conn.read_as_dataframe(bucket, path.format(etf=etf))
    if 'actual_monthly_change' in df.columns:
        df.rename(columns = {'actual_monthly_change':'actual_monthly_returns'}, inplace = True)
    if 'Date' in df.columns:
        df.rename(columns = {'Date':'date'}, inplace = True)
    if isinstance(df, pd.DataFrame):
        df.reset_index(inplace=True, drop=True)
        df.drop_duplicates(subset='date',keep = 'last', inplace = True)
    return df

def do_smoothening_update_historical_file_raw (deployments, datapipeline_read_bucket , datapipeline_read_path, hist_bucket, hist_path,
                                           s3conn, upload_bucket, upload_path , target_date, smoothening_gradient):
    '''
        Function to add new data to historical file with raw inputs and update smoothening value
    '''
    for _,row in deployments.iterrows():
        #prev_df = s3conn.read_as_dataframe(hist_bucket,f"Monthly/transformation_pipeline/pipeline_without_context/predictions_with_raw_features/{etf}_predictions.csv")
        prev_df = s3conn.read_as_dataframe(hist_bucket, hist_path.format(folder_path = 'predictions_with_raw_features',etf = row['tic']))
        ti = row['deployment_name']
        try:
            to_add_df = s3conn.read_as_dataframe(datapipeline_read_bucket , 
                                                 datapipeline_read_path.format(target_date = target_date,
                                                                               folder_path = 'skip_quarter_mod_original_scale_with_raw_feature',
                                                                               ti = ti))
            #to_add_df = pd.read_csv(f'data/daily_runs/{target_date}/skip_quarter_mod_original_scale_with_raw_feature/{ti}.csv')
    
        except Exception as e:
            print(e)
            print(f'Hehe Boy, No data available for given date for {row["tic"]}')
            continue
        to_add_df.reset_index(inplace=True, drop = True)

        if prev_df.shape[1] == to_add_df.shape[1]+1:
            prev_df['date'] = pd.to_datetime(prev_df['date'])
            to_add_df['date'] = pd.to_datetime(to_add_df['date'])
            new_df = pd.concat([prev_df,to_add_df], axis= 0)
            new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
            new_df.sort_values('date', ascending=True, inplace=True)
            new_df.reset_index(inplace=True, drop=True)
            n = new_df['actual_monthly_returns'].isna().sum().sum()
            new_df.loc[new_df.index[-n:],'actual_monthly_returns'] = new_df.loc[new_df.index[-n:],'monthly_close_change'].shift(-22)
            
            new_df['smoothened_monthly_predictions'] = new_df['monthly_predictions'].rolling(
    window =len(smoothening_gradient), min_periods=1).apply(lambda x: apply_smoothing(x, smoothening_gradient), raw=False)
            
            new_df['date'] = new_df['date'].astype('str')
            print(row['tic'])
            print(new_df.isna().sum().sum())
            print(prev_df.shape)
            print(new_df.shape, new_df.date.values[0], new_df.date.values[-1])
            #continue
            #print(new_df.iloc[:,-2:])
            s3conn.write_advanced_as_df(new_df, upload_bucket, upload_path.format(folder_path = 'predictions_with_raw_features', etf = row['tic']))
            #upload_csv_data_to_s3(new_df,"etf-predictions",f"Monthly/transformation_pipeline/pipeline_without_context/predictions_with_raw_features/" ,f'{etf}_predictions')
        else:
            print(f'Error, shape mismatch for {row["tic"]}, {prev_df.shape}, {to_add_df.shape}')
            continue    

def do_smoothening_update_historical_file_original (deployments, datapipeline_read_bucket , datapipeline_read_path, hist_bucket, hist_path,
                                           s3conn, upload_bucket, upload_path , target_date, smoothening_gradient):
    '''
        Function to add new data to historical file with original inputs and update smoothening value
    '''
    for _,row in deployments.iterrows():
        #prev_df = s3conn.read_as_dataframe(hist_bucket,f"Monthly/transformation_pipeline/pipeline_without_context/predictions_with_raw_features/{etf}_predictions.csv")
        prev_df = s3conn.read_as_dataframe(hist_bucket, hist_path.format(folder_path = 'predictions_with_actual_features',etf = row['tic']))
        ti = row['deployment_name']
        try:
            to_add_df = s3conn.read_as_dataframe(datapipeline_read_bucket , 
                                                 datapipeline_read_path.format(target_date = target_date, 
                                                                               folder_path = 'skip_quarter_mod',
                                                                               ti = ti))
            to_add_df.drop(columns = 'monthly_predictions', axis = 1, inplace = True)
            to_add_df2 = s3conn.read_as_dataframe(datapipeline_read_bucket , 
                                                 datapipeline_read_path.format(target_date = target_date, 
                                                                               folder_path = 'skip_quarter_mod_original_scale_with_raw_feature'
                                                                               ,ti = ti)
                                                 )[['date','monthly_predictions']]
            to_add_df = pd.merge(to_add_df, to_add_df2, on='date', how= 'left')
    
        except Exception as e:
            print(e)
            print(f'Hehe Boy, No data available for given date for {row["tic"]}')
            continue
        to_add_df.reset_index(inplace=True, drop = True)

        prev_df['date'] = pd.to_datetime(prev_df['date'])
        to_add_df['date'] = pd.to_datetime(to_add_df['date'])
        new_df = pd.concat([prev_df,to_add_df], axis= 0)
        new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
        new_df.sort_values('date', ascending=True, inplace=True)
        new_df.reset_index(inplace=True, drop=True)
        
        new_df['smoothened_monthly_predictions'] = new_df['monthly_predictions'].rolling(
    window =len(smoothening_gradient), min_periods=1).apply(lambda x: apply_smoothing(x, smoothening_gradient), raw=False)
        new_df['date'] = new_df['date'].astype('str')
        print(row['tic'])
        print(new_df.isna().sum().sum())
        print(prev_df.shape)
        print(new_df.shape, new_df.date.values[0], new_df.date.values[-1])

        s3conn.write_advanced_as_df(new_df, upload_bucket, upload_path.format(folder_path = 'predictions_with_actual_features', etf = row['tic']))
        #upload_csv_data_to_s3(new_df,"etf-predictions",f"Monthly/transformation_pipeline/pipeline_without_context/predictions_with_actual_features/" ,f'{etf}_predictions')
