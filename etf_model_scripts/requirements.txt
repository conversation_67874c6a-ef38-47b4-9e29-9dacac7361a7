# Python 3.12.3
absl-py==2.2.2
annotated-types==0.7.0
anyio==4.4.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==2.4.1
astunparse==1.6.3
async-lru==2.0.4
attrs==24.2.0
aws-requests-auth==0.4.3
awscli==1.34.20
babel==2.16.0
beautifulsoup4==4.12.3
bleach==6.1.0
boto==2.49.0
boto3==1.35.20
botocore==1.35.20
cachetools==5.5.2
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
comm==0.2.2
configparser==7.2.0
contourpy==1.3.0
croniter==1.4.1
curl_cffi==0.11.1
cycler==0.12.1
debugpy==1.8.5
decorator==5.1.1
defusedxml==0.7.1
docutils==0.16
elastic-transport==8.15.0
elasticsearch==7.12.1
eq-common-utils@git+https://<EMAIL>/EqubotAI/eq-common-utils.git@v0.14
et-xmlfile==1.1.0
Events==0.5
exchange_calendars==4.10
executing==2.1.0
expiringdict==1.2.2
fastjsonschema==2.20.0
flatbuffers==25.2.10
fonttools==4.54.1
fqdn==1.5.1
fredapi==0.5.2
frozendict==2.4.4
fsspec==2023.6.0
gast==0.6.0
google==3.0.0
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-pasta==0.2.0
googleapis-common-protos==1.69.2
graphviz==0.14
greenlet==3.1.0
grpcio==1.71.0
h11==0.14.0
h5py==3.13.0
html5lib==1.1
httpcore==1.0.5
httplib2==0.22.0
httpx==0.27.2
ibm-cos-sdk==2.13.6
ibm-cos-sdk-core==2.13.6
ibm-cos-sdk-s3transfer==2.13.6
ibm_watsonx_ai==1.1.11
idna==3.10
importlib_metadata==8.5.0
ipykernel==6.29.5
ipython==8.27.0
ipywidgets==8.1.5
isoduration==20.11.0
jedi==0.19.1
Jinja2==3.1.4
jmespath==1.0.1
joblib==1.3.2
json-normalize==1.1.0
json5==0.9.25
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
kiwisolver==1.4.7
korean-lunar-calendar==0.3.1
libclang==18.1.1
lomond==0.3.3
lxml==5.3.0
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
ml_dtypes==0.5.1
multitasking==0.0.11
namex==0.0.9
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
nltk==3.9.1
notebook==7.2.2
notebook_shim==0.2.4
numpy==1.26.4
numpy-ext==0.9.9
openpyxl==3.1.5
opensearch-py==2.7.1
opt_einsum==3.4.0
optree==0.15.0
overrides==7.7.0
packaging==24.1
pandas==2.1.4
pandas_market_calendars==4.6.1
pandocfilters==1.5.1
parso==0.8.4
patsy==1.0.1
peewee==3.17.6
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.3
prometheus_client==0.20.0
prompt_toolkit==3.0.47
proto-plus==1.26.1
protobuf==5.29.4
psutil==5.9.8
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.9.1
pydantic_core==2.23.3
Pygments==2.18.0
pyluach==2.2.0
pyparsing==3.1.4
pysftp==0.2.9
python-dateutil==2.9.0.post0
python-json-logger==2.0.7
pytz==2023.3
PyWavelets==1.8.0
PyYAML==6.0.2
pyzmq==26.2.0
referencing==0.35.1
regex==2024.9.11
requests==2.32.2
requests-aws4auth==1.3.1
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==14.0.0
rpds-py==0.20.0
rsa==4.7.2
s3transfer==0.10.2
scikit-learn==1.6.0
scipy==1.14.1
Send2Trash==1.8.3
setuptools==75.0.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.34
stack-data==0.6.3
statsmodels==0.14.4
TA-Lib==0.4.32
tabulate==0.9.0
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorflow==2.19.0
termcolor==3.1.0
terminado==0.18.1
threadpoolctl==3.5.0
tinycss2==1.3.0
toolz==1.0.0
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
types-python-dateutil==2.9.0.20240906
typing_extensions==4.12.2
tzdata==2024.1
uri-template==1.3.0
uritemplate==4.1.1
urllib3==1.26.20
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wheel==0.45.1
widgetsnbextension==4.0.13
wrapt==1.17.2
xlrd==2.0.1
XlsxWriter==3.2.2
yfinance==0.2.61
zipp==3.20.2