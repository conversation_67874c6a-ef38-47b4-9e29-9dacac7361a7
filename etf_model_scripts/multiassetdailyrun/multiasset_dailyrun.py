from helpers import *

s3conn = s3_config()
metrics_obj = MetricsHelper(None)

with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

asset_weights = {
    'AOK': {
        'NasdaqGM:BNDX' : 69.92,
        'ARCA:SPY': 17.5,
        'ARCA:EFA': 7.97,
        'ARCA:EEM': 3.11,
        'ARCA:IWM': 0.48
    },
    'AOM':{
        'NasdaqGM:BNDX' : 59.42,
        'ARCA:SPY': 23.8,
        'ARCA:EFA': 10.6,
        'ARCA:EEM': 4.15,
        'ARCA:IWM': 0.65
    },
    'AOA':{
        'NasdaqGM:BNDX' : 19.62,
        'ARCA:SPY': 47.14,
        'ARCA:EFA': 21.01,
        'ARCA:EEM': 8.22,
        'ARCA:IWM': 1.28
    },
    'AOR':{
        'NasdaqGM:BNDX' : 39.63,
        'ARCA:SPY': 34.79,
        'CA:EFA': 16.2,
        'ARCA:EEM': 6.19,
        'ARCA:IWM': 1.02
    }
}

def send_email(subject ,receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']

    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)


def get_delivery_data(schedular,start_date,end_date):
    monthly_etf_preportfolio_bucket = config['S3_paths']['monthly_etf_preportfolio_bucket']
    monthly_etf_preportfolio_path = config['S3_paths']['monthly_etf_preportfolio_path']
    quarterly_etf_preportfolio_bucket = config['S3_paths']['quarterly_etf_preportfolio_bucket']
    quarterly_etf_preportfolio_path = config['S3_paths']['quarterly_etf_preportfolio_path']
    pre_port=[]
    #for i in pd.date_range('2024-03-01','2024-06-04',freq='W-FRI'):
    if schedular=='Monthly':
        for date in pd.date_range(start_date,end_date, freq='B'):
            i= date.strftime('%m_%d_%Y')
           #print(i)
            try:
                df=s3conn.read_as_dataframe(monthly_etf_preportfolio_bucket,monthly_etf_preportfolio_path.format(date=i))[['Date','Equity','Avg']]
                pre_port.append(df)
            except Exception as e:
                print(e)
                continue
    else:
        for date in pd.date_range(start_date,end_date, freq='B'):
            k= date.strftime('%m-%d-%Y')
            i = date.strftime('%Y-%m-%d')
            #print(i)
            try:
                df=s3conn.read_as_dataframe(quarterly_etf_preportfolio_bucket,quarterly_etf_preportfolio_path.format(date=k))[['Equity','Avg']]
                df['Date'] = i
                pre_port.append(df)
            except Exception as e:
                print(e)
                continue
    pre_port_df= pd.concat(pre_port)
    pre_port_df = pre_port_df[['Date','Equity','Avg']]
    pre_port_df.columns=['date','tic','delivery_predictions']
    # print(pre_port_df['date'].values[0])
    pre_port_df['date']= pd.to_datetime(pre_port_df['date'])
    pre_port_df.reset_index(inplace=True, drop=True)
    pre_port_df.drop_duplicates(subset=["date", "tic"], keep="first", inplace = True)
    #pre_port_df.replace(etf_dict, inplace=True)
    pre_port_df['delivery_predictions']= pre_port_df['delivery_predictions']*100
    return pre_port_df

def complete_daily_run_prediction(schedular, multiasset_etfs, aietf_pre_port_df, latest_date, prices_start_date, end_date):
    multiasset_predictions_hist_bucket  = config['S3_paths']['multiasset_predictions_hist_bucket']
    multiasset_predictions_hist_path  = config['S3_paths']['multiasset_predictions_hist_path']
    multiasset_predictions_upload_bucket  = config['S3_paths']['upload_multiasset_predictions_hist_bucket']
    multiasset_predictions_upload_path  = config['S3_paths']['upload_multiasset_predictions_hist_path']
    
    inhouse_api_url_by_tic = config['Inhouse_API']['inhouse_api_url_by_tic']
    
    cols_to_choose_inhouse_monthly = ['date', 'isin', 'tic', 'adj_close', 'close_price','weekly_close_change', 'monthly_close_change','actual_monthly_returns']
    cols_to_choose_inhouse_quarterly = ['date', 'isin', 'tic', 'adj_close', 'close_price','weekly_close_change', 'monthly_close_change', 'quarterly_close_change',
                                    'actual_quarterly_returns']
    if schedular == 'Monthly':
        cols_to_choose_inhouse = cols_to_choose_inhouse_monthly
    elif schedular == 'Quarterly':
        cols_to_choose_inhouse = cols_to_choose_inhouse_quarterly
        
    for etf in multiasset_etfs:
        wts = asset_weights[etf]
        all_proxy_etfs = []
        total = 0
        for wt in wts:
            all_proxy_etfs.append(wt.split(':')[1])
            total+=wts[wt]
        print(all_proxy_etfs)
        print('weights total ', total)
        new_wts = {key: value *100/total for key, value in wts.items()}
        print(f'weights normalization done for {etf}')
        print(f'new weights are : ',new_wts)
        total = 0
        for wt in new_wts:
            total+=new_wts[wt]
        print('New Weights Total : ',total)

        etf_raw_temp = get_instockapi_data(inhouse_api_url_by_tic, etf, prices_start_date, end_date)
        etf_raw_temp = etf_raw_temp[cols_to_choose_inhouse]
        etf_raw_temp['date'] = pd.to_datetime(etf_raw_temp['date'])
    
        for proxy_etf in all_proxy_etfs:
            etf_delivery_temp = aietf_pre_port_df[aietf_pre_port_df['tic']==proxy_etf]
            etf_delivery_temp = etf_delivery_temp[['date','delivery_predictions']]
            etf_delivery_temp.reset_index(inplace=True, drop=True)
             
            etf_delivery_temp.columns = ['date',f'{proxy_etf}_{schedular.lower()}_predictions']
            etf_raw_temp = pd.merge(etf_raw_temp,etf_delivery_temp , on='date', how = 'left')
        
            etf_raw_temp[f'{proxy_etf}_{schedular.lower()}_predictions'] = etf_raw_temp[f'{proxy_etf}_{schedular.lower()}_predictions'].ffill()
    
        etf_raw_temp[f'{schedular.lower()}_predictions'] = sum(etf_raw_temp[f'{col.split(":")[1]}_{schedular.lower()}_predictions'] * weight for col, weight in new_wts.items())/100
    
        to_add_df = etf_raw_temp[etf_raw_temp ['date']== latest_date ]
        prev_df = s3conn.read_as_dataframe(multiasset_predictions_hist_bucket, multiasset_predictions_hist_path.format(schedular= schedular, etf = etf))
    
        if to_add_df.empty:
            print(f'Hehe Boy, No data available for given date for {etf}')
            continue
    
        to_add_df.reset_index(inplace=True, drop = True)

        if prev_df['tic'].values[0] == to_add_df['tic'].values[0] and prev_df.shape[1] == to_add_df.shape[1]:
            prev_df['date'] = pd.to_datetime(prev_df['date'])
            new_df = pd.concat([prev_df,to_add_df], axis= 0)
            new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
            new_df.sort_values('date', ascending=True, inplace=True)
            new_df.reset_index(inplace=True, drop=True)
            if schedular =='Monthly':
                n = new_df['actual_monthly_returns'].isna().sum().sum() + 5
                new_df.loc[new_df.index[-n:],'actual_monthly_returns'] = new_df.loc[new_df.index[-n:],'monthly_close_change'].shift(-22)
            elif schedular == 'Quarterly':
                n = new_df['actual_quarterly_returns'].isna().sum().sum() + 5
                new_df.loc[new_df.index[-n:],'actual_quarterly_returns'] = new_df.loc[new_df.index[-n:],'quarterly_close_change'].shift(-66)
                
            new_df['date'] = new_df['date'].astype('str')
            print(etf)
            print(new_df.isna().sum().sum())
            print(prev_df.shape, new_df.shape, new_df.date.values[0], new_df.date.values[-1])
            
            s3conn.write_advanced_as_df(new_df, multiasset_predictions_upload_bucket, multiasset_predictions_upload_path.format(schedular = schedular, etf = etf) )
            #s3conn.write_advanced_as_df(new_df, 'histetfdata', multiasset_predictions_hist_path.format(schedular = schedular, etf = etf) )
        
            print(f'{etf} done')
        else:
            print(f'Error, shape mismatch for {etf}, {prev_df.shape}, {to_add_df.shape}')
            continue

def complete_daily_run_metrics(schedular, multiasset_etfs, latest_date):
    upload_multiasset_metrics_bucket = config['S3_paths']['upload_multiasset_metrics_hist_bucket']
    upload_multiasset_metrics_path = config['S3_paths']['upload_multiasset_metrics_hist_path']
    multiasset_prediction_bucket = config['S3_paths']['multiasset_predictions_hist_bucket']
    multiasset_prediction_path = config['S3_paths']['multiasset_predictions_hist_path']
    multiasset_metrics_bucket = config['S3_paths']['multiasset_metrics_hist_bucket']
    multiasset_metrics_path = config['S3_paths']['multiasset_metrics_hist_path']
    
    for etf in multiasset_etfs:
        
        etf_temp = s3conn.read_as_dataframe(multiasset_prediction_bucket,multiasset_prediction_path.format(etf=etf, schedular = schedular))
        etf_temp['date'] = etf_temp['date'].astype('str')
        
        metrics= calculate_metrics (metrics_obj ,etf, etf_temp,latest_date, schedular,  prediction_column = f'{schedular.lower()}_predictions', 
                                    actual_column = f'actual_{schedular.lower()}_returns')
        
        if metrics.empty:
            print(f'No data for this etf : {etf}')
            continue
            
        to_add_df = metrics[metrics ['date']== latest_date ]
        prev_df = s3conn.read_as_dataframe(multiasset_metrics_bucket, multiasset_metrics_path.format(etf=etf, schedular = schedular))
    
        if to_add_df.empty:
            print(f'Hehe Boy, No data available for given date for {etf}')
            continue
    
        to_add_df.reset_index(inplace=True, drop = True)
    
        if prev_df['tic'].values[0] == to_add_df['tic'].values[0] and prev_df.shape[1] == to_add_df.shape[1]:
            prev_df['date'] = pd.to_datetime(prev_df['date'])
            to_add_df['date'] = pd.to_datetime(to_add_df['date'])
            new_df = pd.concat([prev_df,to_add_df], axis= 0)
            new_df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
            new_df.sort_values('date', ascending=True, inplace=True)
            new_df.reset_index(inplace=True, drop=True)
            new_df['date'] = new_df['date'].astype('str')
            print(etf)
            print(new_df.isna().sum().sum())
            print(prev_df.shape, new_df.shape, new_df.date.values[0], new_df.date.values[-1])
            
            # upload_csv_data_to_s3(new_df,'etf-predictions','Monthly/multiasset/metrics/',f'{etf}_metrics')
            s3conn.write_advanced_as_df(new_df,upload_multiasset_metrics_bucket, upload_multiasset_metrics_path.format(etf=etf, schedular = schedular))
            #break
            print(f'{etf} done')
        else:
            print(f'Error, shape mismatch for {etf}, {prev_df.shape}, {to_add_df.shape}')
            continue

def run_daily_run_multiasset(run_date, schedular):
    print(f'Starting multiasset daily run for run date {run_date} for schedular {schedular}')

    ### getting last business date
    latest_date=(pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
    start_date = (pd.to_datetime(latest_date) - timedelta(80)).strftime("%Y-%m-%d")
    prices_start_date = (pd.to_datetime(latest_date) - timedelta(days = 250)).strftime("%Y-%m-%d")
    end_date = (pd.to_datetime(latest_date)).strftime("%Y-%m-%d")
    print(start_date, end_date, prices_start_date)

    multiasset_etfs = config['misc']['multiasset_etfs']
    inhouse_api_url_by_tic = config['Inhouse_API']['inhouse_api_url_by_tic']
    multiasset_isin_tic_map_bucket = config['Tic_ISIN_map']['multiasset_isin_tic_map_bucket']
    multiasset_isin_tic_map_path = config['Tic_ISIN_map']['multiasset_isin_tic_map_path']
    multiasset_isin_tic_map = s3conn.read_as_dataframe(multiasset_isin_tic_map_bucket, multiasset_isin_tic_map_path)

    ### getting multiasset delivery data 
    aietf_pre_port_df = get_delivery_data(schedular,start_date,end_date)
    print(f'getting multiasset {schedular} delivery data done')

    ### doing multiasset daily prediction
    complete_daily_run_prediction(schedular, multiasset_etfs, aietf_pre_port_df, latest_date, prices_start_date , end_date)
    print(f'daily predictions done for multiasset {schedular}')

    ### doing multiasset daily metrics
    complete_daily_run_metrics(schedular, multiasset_etfs, latest_date)
    print(f'daily metrics done for multiasset {schedular}')


def run_s3versioning_and_file_generation(run_date, schedular):
    
    ## getting the last business day
    lastbday=pd.to_datetime(run_date)- BDay(1)
    lastbday=lastbday.date().strftime('%Y-%m-%d')
    print(lastbday)
    print(f'last business day {lastbday}')

    multiasset_etfs = config['misc']['multiasset_etfs']
    
    multiasset_predictions_hist_bucket  = config['S3_paths']['multiasset_predictions_hist_bucket']
    multiasset_predictions_hist_path  = config['S3_paths']['multiasset_predictions_hist_path']
    multiasset_metrics_hist_bucket  = config['S3_paths']['multiasset_metrics_hist_bucket']
    multiasset_metrics_hist_path  = config['S3_paths']['multiasset_metrics_hist_path']
    multiasset_file_generation_bucket  = config['S3_paths']['upload_multiasset_file_generation_bucket']
    multiasset_file_generation_path  = config['S3_paths']['upload_multiasset_file_generation_path']

    s3versioned_bucket = config['S3_paths']['upload_s3versioned_multiasset_predictions_bucket']
    s3versioned_path = config['S3_paths']['upload_s3versioned_multiasset_predictions_path']

    ### s3 versioning of the predictions data 
    do_s3_versioning(s3conn, schedular, lastbday, multiasset_etfs, 'multiasset',
                                           multiasset_predictions_hist_bucket, multiasset_predictions_hist_path, 
                                           s3versioned_bucket, s3versioned_path)

    print(f's3 versioning of the data into the post processed path done for schedular {schedular}')
    
    ## generating the merged file 
    merged_final_df = do_file_generation_local(s3conn, schedular, lastbday, multiasset_etfs,
                                           multiasset_predictions_hist_bucket, multiasset_predictions_hist_path, 
                                           multiasset_metrics_hist_bucket, multiasset_metrics_hist_path)
    print(f'generating merged file done for multiasset {schedular}')
    print(merged_final_df)
    merged_final_df.loc[:, 'date'] = lastbday

    ### uploading the data to s3 local
    s3conn.write_advanced_as_df(merged_final_df, multiasset_file_generation_bucket,
                                multiasset_file_generation_path.format(schedular =schedular, lastbday = lastbday) )
    print('upload merged file to local s3 done')
    


if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting Multiasset run for run date :{run_date}')
    
    multiasset_schedulars  = config['misc']['multiasset_schedular']

    for schedular in multiasset_schedulars:
        if schedular not in ['Monthly','Quarterly']:
            print(f'Invalid schedular : {schedular}, only accept in [Monthly,Quarterly]')
            continue
        
        try:
            run_daily_run_multiasset(run_date, schedular)
            print(f'daily run part a completed for {schedular} schedular')

            run_s3versioning_and_file_generation(run_date, schedular)
            print(f's3 versioning & daily run file generation completed for {schedular} schedular')

            print(f'{schedular} Multiasset Daily Run: Run COMPLETED successfully for run date {run_date}')
            
            receiver = config['Gmail_creds']['email_receiver_success']
            send_email(f'{schedular} Multiasset Daily Run : Run COMPLETED successfully for run date {run_date}',receiver, 'SUCCESS')
            
        except Exception as e:
            print(f'{schedular} Multiasset Daily Run : Run FAILED for run date {run_date} due to the Error : {e}')
            print(traceback.format_exc())
            receiver = config['Gmail_creds']['email_receiver_failed']
            send_email( f'{schedular} Multiasset Daily Run : Run FAILED for run date {run_date}', receiver, traceback.format_exc())
            raise
