Gmail_creds:
    gmail_creds_bucket : etf-predictions
    gmail_creds_path: preportfolio/gmail_credentials/credentials.json
    gmail_sender : <EMAIL>
    email_receiver_success : <EMAIL>, <EMAIL>, <EMAIL>
    email_receiver_failed : <EMAIL>, <EMAIL>, <EMAIL>

Tic_ISIN_map:
    isin_tic_map_bucket : etf-predictions
    isin_tic_map_path : preportfolio/etfs_isin_map/etfs_tic_isin_mapping.csv
    multiasset_isin_tic_map_bucket : etf-predictions
    multiasset_isin_tic_map_path : preportfolio/etfs_isin_map/multiasset_tic_isin_mapping.csv
    
Master_active_firms:
    api_url : http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all

Inhouse_API:
    inhouse_api_url_by_isin : http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA
    inhouse_api_url_by_tic : http://52.2.217.192:8080/stockhistory/getstocksbytic?tic={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA

S3_paths:

    multiasset_predictions_hist_bucket  : etf-predictions
    multiasset_predictions_hist_path  : "{schedular}/multiasset/predictions/{etf}_predictions.csv"
    multiasset_metrics_hist_bucket  : etf-predictions
    multiasset_metrics_hist_path  : "{schedular}/multiasset/metrics/{etf}_metrics.csv"
    
    monthly_etf_preportfolio_bucket : aimax
    monthly_etf_preportfolio_path : ETF_Pre_Portfolio_Folder/ETF_pre_portfolio_{date}.xlsx 
    quarterly_etf_preportfolio_bucket : aimax
    quarterly_etf_preportfolio_path : ETF_Pre_Portfolio_quaterly/ETF_pre_portfolio_{date}.xlsx
    
    upload_multiasset_file_generation_bucket : histetfdata
    upload_multiasset_file_generation_path : "{schedular}/multiasset/client/multiasset_results_{lastbday}.csv"
    upload_multiasset_predictions_hist_bucket  : histetfdata
    upload_multiasset_predictions_hist_path  : "{schedular}/multiasset/predictions/{etf}_predictions.csv"
    upload_multiasset_metrics_hist_bucket: histetfdata
    upload_multiasset_metrics_hist_path: "{schedular}/multiasset/metrics/{etf}_metrics.csv"
    
    upload_s3versioned_multiasset_predictions_bucket : histetfdata
    upload_s3versioned_multiasset_predictions_path : etf_model/{product}/etf_post_processed/{lower_schedular}/{latest_date}/predictions/{isin}.csv
    

misc:
    multiasset_etfs : [AOK ,AOM, AOA, AOR]
    multiasset_schedular : [Monthly, Quarterly]