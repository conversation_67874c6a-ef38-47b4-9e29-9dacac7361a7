import sys
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from opensearchpy.connection.http_requests import RequestsHttpConnection
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from opensearchpy.client import OpenSearch
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
import requests
import pandas as pd
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
import traceback

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def split_upload_df_product_and_isinwise (df, product_name, etf_list, latest_date, upload_bucket, upload_path, s3conn, cols_to_keep):
    '''
        Function to upload df in product folder and with isin
    '''
    final_results = df[cols_to_keep]
    final_results = final_results[final_results['tic'].isin(etf_list)]
    final_results.reset_index(inplace=True, drop = True)
    
    for index,row in final_results.iterrows():
        isin_df  = pd.DataFrame([row])
        #print(isin_df)
        isin = isin_df['isin'].values[0]
        s3conn.write_advanced_as_df(isin_df, upload_bucket, upload_path.format(product = product_name, latest_date = latest_date, isin = isin))
    print(f'product file {product_name} uploaded at s3 versioned path')

def split_upload_df_productwise(df, product_name, etf_list, latest_date, upload_bucket, upload_path, s3conn):
    '''
        Function to upload df in product folder aggregated
    '''
    final_results = df [['date','tic', 'final_monthly_predictions','final_confidence_score']]
    final_results.columns = ['date','etf', 'final_monthly_predictions','confidence_score']
    
    product_file = pd.DataFrame(None)
    product_file = final_results[final_results['etf'].isin(etf_list)]
    product_file.reset_index(inplace=True, drop = True)
    product_file.loc[:, 'date'] = latest_date
    s3conn.write_advanced_as_df(product_file, upload_bucket, upload_path.format(product = product_name, latest_date = latest_date))
    print(f'product file {product_name} uploaded at this path : {upload_path.format(product = product_name, latest_date = latest_date)}')

def treat_confi(confid):
    if confid >= 0.6:
        return confid
    return (60 + confid*10)/100

def get_smoothening_gradients():
    gradient_size = 5
    multiplier = 1 / (np.e * (np.e ** gradient_size - 1) / (np.e - 1))
    smoothening_gradient = [multiplier * (np.e ** (i + 1)) for i in range(gradient_size)]
    return smoothening_gradient

def get_instockapi_data (api_url, firm ,start_date, end_date):
    '''
        Function to get proces data from inhouse api
    '''
    isin = firm
    stock_data = requests.get(api_url.format(identifier = isin, startdate = start_date, enddate = end_date)).json()
 
    stock_data = pd.DataFrame(stock_data['data']['stocks'])[['date','adj_close']]
 
    stock_data.rename(columns={'adj_close':'close_price'}, inplace=True)
    stock_data=stock_data[['date','close_price']]
    stock_data['date']=pd.to_datetime(stock_data['date'])
    stock_data.sort_values('date', ascending =True , inplace=True)
    stock_data.reset_index(inplace=True, drop=True)
 
    stock_data['monthly_close_change']=stock_data['close_price'].pct_change(periods=22)*100
    stock_data['actual_monthly_returns']=stock_data['monthly_close_change'].shift(-22)
    stock_data['date'] = stock_data['date'].astype(str)
    return stock_data

def select_pred(df, etfs, target = 'actual_monthly_returns', pred= 'monthly_predictions', window= 11):
    """
    Desc:
        determines the best-model in a window (w.r.t the directionality), based on majority voting.
        returns a dataframe with the final predictions
    
    Arguments:
    df     = DataFrame
    etfs   = a list
    target = column's name, for the actual_returns
    pred   = column's name, for the ER
    window = size of window, for averaging the directionality
    
    Returns:
     a DataFrame
     
    """
    
    # 0 copy the DataFrame
    _df_= df.copy()

    # 1a determine the directionality, on each day
    # this dictionary shall store the directionality [per day] for each ETF
    dirs= {}
    for etf in etfs:
        
        # read
        p= _df_.loc[:, f'{pred}_{etf}']
        t= _df_.loc[:, f'{target}_{etf}']
    
        # assess directionality
        _dir_= np.zeros(len(p), dtype= bool)
        for i in range(len(p)):
            x= np.sign(p.iloc[i])
            y= np.sign(t.iloc[i])
            if x == y:
                _dir_[i]= 1
    
        # store
        dirs[etf]= _dir_

    # 1b update the DataFrame
    for etf, _dir_ in dirs.items():
        _df_[f'dir_{etf}']= _dir_.astype(int, copy= False)

    # 2a determine the average directionality in the window
    for etf in etfs:
        # mean-dir in the window
        _df_.loc[:, f'win_dir_{etf}']= _df_.loc[:, f'dir_{etf}'].rolling(window= window).apply(lambda arr: sum(arr) / window)
    
    # 2b discard NaNs
    _df_= _df_.iloc[window-1:, :]

    # 2c converts the fractions into integers
    for etf in etfs:
        _df_.loc[:, f'win_dir_{etf}' ]= _df_.loc[:, f'win_dir_{etf}' ].apply(lambda x: round(100*x))
    
    # 3 determine the best-model (for each day), based on the f'win_dir_{etf}'
    for i, row in _df_.iterrows():

        # determines the maximum-value of f'win_dir_{etf}' on each day
        champion, max_win_dir= None, -1
        for etf in etfs:
            if row[f'win_dir_{etf}']  > max_win_dir:
                max_win_dir= row[f'win_dir_{etf}']
                champion= etf
                
        # store
        _df_.loc[i, 'max_win_dir']= max_win_dir
        _df_.loc[i, 'best_model']= champion
        
    # 4a select a model, based on the past-performance (i.e., 22 days ago)
    period= 22
    
    _df_.reset_index(inplace=True, drop= True)
    for i in range(len(_df_)-period):
        # select the best model, 'period' days ago
        etf= _df_.loc[i, 'best_model']
        
        # store
        j= i+period
        _df_.loc[j, 'model_selected']= etf
        _df_.loc[j, 'final_pred']= _df_.loc[j, f'{pred}_{etf}']
    
    # 4b discard NaNs
    _df_= _df_.iloc[period:, :]

    # 5 reset index
    #_df_.reset_index(inplace= True, drop= True)
    _df_.set_index(keys= ['date'], inplace= True, drop= True)
    
    # 6 return these columns
    cols= ['model_selected', 'best_model', 'final_pred'] #, 'max_win_dir', 'win_dir_SPY', 'win_dir_DBEEUGFT', 'win_dir_BNPIFUS']
    _df_= _df_.loc[:, cols]
    
    return _df_ 

def accuracy_function(df_series, coff = 500):
    '''
    Accuracy conversion metric to convert daily APE into accuracy for ER.
    '''
    return df_series.apply(lambda x: 97/( 1 + 20 * np.exp((-coff/x))) if x <  100 else max(0, 106.7 - 0.213 * x))

def calculate_accuracy(df_data, prediction_col = 'predictions', target_col = 'close_change_monthly', coff = 500):
    '''
    This function calculates the accuracy based on actual er and predicted er for an isin for daily, 14 & 22 trading days rolling average.
    Accuracy calculations:
        - Calculate the modified APE for individual data points (modification: divide by max( abs(actual), abs(pred) )) )
        - Convert it to accuracy using 100/1+20*(exp^-500/x) if x < 100 and we linearly degrade to 0 for ape = 500
        

    Input params (required):
        - date
        - prediction_col : predicted ER value in percentage (prediction for today's date that are generated 1 month back ie. if date is 28 Dec 2023, it'll have the predictions generated on 28 Nov 2023)
        - target_col : target column value in percentage, it's also shifted by 22 days similar to prediction_col
        - coff : coefficient to be used in accuracy function

    Output columns:
        - accuracy_1_day: accuracy calculated for each day
        - accuracy_14_day : accuracy calculated for 14 day rolling window
        - accuracy_22_day : accuracy calculated for 22 day rolling window

    Range of columns:
        - prediction_col : [0, 100]
        - target_col : [0, 100]
        - accuracy: [0, 100]
    '''
    if prediction_col not in df_data.columns:
        raise Exception('Prediction column not in Dataframe')

    if target_col not in df_data.columns:
        raise Exception('Target column not in Dataframe')
    
    # Remove any nan's in prediction or target cols
    df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

    # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
    df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
    df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

    # Calculate RMS of MAPE over a rolling of 14 days
    df_data['accuracy_1_day'] = accuracy_function(df_data['daily_ape'], coff = coff)

    # Calculate RMS of MAPE over a rolling of 22 days
    df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
    df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5
    
    df_data.drop(columns=['denominator'], inplace=True)
    
    return df_data


def calculate_metrics(etf, temp, target_date):
    try:
        metric_data_list = []
        
        for i in range(len(temp)-22-80,len(temp)-22+1):
            ticker = etf
            df = temp[i-22:i]
            df["date"] = pd.to_datetime(df["date"])
            #print(df)
            
            two_year_max = temp[i-min(i,504):i]["actual_monthly_returns"].max()
            two_year_min = temp[i-min(i,504):i]["actual_monthly_returns"].min()
            #print(two_year_min, two_year_max)
            
            merge = df.copy()
            group = df.copy()
            
            df.sort_values(by="date", ascending=True, inplace=True)
            df.drop_duplicates(subset=["date"], inplace=True)
            df["date"] = df["date"].astype(str)
            df.reset_index(drop=True, inplace=True)
            accuracy_df=calculate_accuracy(df,prediction_col='monthly_predictions', target_col='actual_monthly_returns')
            accuracy= accuracy_df[['accuracy_14_day', 'accuracy_22_day', 'accuracy_1_day']].to_dict('records')[-1]
            
            merge["date"] = pd.to_datetime(merge["date"])
            merge.sort_values(by="date",ascending=True, inplace=True)
            merge.reset_index(drop=True, inplace=True)
            merge = merge.ffill().bfill()
            merge.dropna(inplace=True)
            
            directionality_df = pd.DataFrame()
            merge["predictions_prev"] = merge["monthly_predictions"].shift(1)
            merge["actual_prev"] = merge["actual_monthly_returns"].shift(1)
            merge.dropna(inplace=True)
            
            directionality_df["prediction_direction"] = merge["monthly_predictions"].astype(float, errors="ignore") - \
                                                merge["predictions_prev"].astype(float, errors="ignore") / \
                                                merge["predictions_prev"].astype(float, errors="ignore")
            directionality_df["close_direction"] = merge["actual_monthly_returns"].astype(float, errors="ignore") - \
                                                        merge["actual_prev"].astype(float, errors="ignore") / \
                                                        merge["actual_prev"].astype(float, errors="ignore")
            correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) |
                                                    directionality_df.lt(0).all(axis=1)]
            subset_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)]
            relaxation_count = len(subset_df[(abs(abs(subset_df["close_direction"]) -
                                                abs(subset_df["prediction_direction"])) < 0.5)])
            try:
                directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
            except:
                directionality_score =0
            
            
            df_filter = group[["date","actual_monthly_returns", "monthly_predictions"]]
            df_filter.dropna(inplace=True)
            df_filter.reset_index(drop=True, inplace=True)
            date = str(df_filter.iloc[-1]["date"].date())
            
            try:
                mae = mean_absolute_error(df_filter['actual_monthly_returns'], df_filter['monthly_predictions'])
            except Exception as e:
                print(e)
                mae = 0
            try:
                mse = mean_squared_error(df_filter['actual_monthly_returns'], df_filter['monthly_predictions'])
            except:
                mse = 0
            try:
                rmse = np.sqrt(mse)
            except:
                rmse = 0
            try:
                mean_directionality = 100 * ((df_filter['actual_monthly_returns'] * df_filter['monthly_predictions']) > 0).mean()
            except:
                mean_directionality = 0
            try:
                r2 = r2_score(df_filter['actual_monthly_returns'], df_filter['monthly_predictions'])
            except:
                r2 = 0
            try:
                adj_r2 = 1 - (1 - r2) * (df_filter.shape[0] - 1) / (df_filter.shape[0] - df_filter.shape[1] - 1)
            except:
                adj_r2 = 0
            try:
                total_perc_diff = (df_filter['actual_monthly_returns'].mean() - df_filter['monthly_predictions'].mean()) / df_filter[
                    'monthly_predictions'].mean()
                abs_total_diff = abs(total_perc_diff)
            except:
                total_perc_diff = 0
                abs_total_diff = 0
            try:
                total_variance_perc_diff = (df_filter['actual_monthly_returns'].var() - df_filter['monthly_predictions'].var()) / \
                                        df_filter['monthly_predictions'].var()
            except:
                total_variance_perc_diff = 0
            try:
                abs_total_variance_perc_diff = abs(
                    (df_filter['actual_monthly_returns'].var() - df_filter['monthly_predictions'].var()) / df_filter['monthly_predictions'].var())
            except:
                abs_total_variance_perc_diff = 0
            try:
                df['confidence']=df.apply(lambda x: (two_year_max - x["actual_monthly_returns"])/(two_year_max - x["monthly_predictions"]) if x["actual_monthly_returns"]>= x["monthly_predictions"] else (x["actual_monthly_returns"] - two_year_min) / (x["monthly_predictions"] - two_year_min), axis=1)
                
                df['confidence']= df['confidence'].apply(lambda x: 0.1 if x<=0 else x)
                
                confidence_score= df['confidence'].values[-1]
                avg_confidence_score = df['confidence'][-11:].mean()
            except Exception as e:
                print(e)
            
            correlation_score = df_filter.monthly_predictions.corr(df_filter.actual_monthly_returns)
            metric_df = pd.DataFrame([[date,ticker , mae, mse, rmse, r2, adj_r2, total_perc_diff,
                                    abs_total_diff, total_variance_perc_diff,
                                    abs_total_variance_perc_diff, correlation_score,
                                    directionality_score, mean_directionality,confidence_score, 
                                    avg_confidence_score, accuracy["accuracy_14_day"], 
                                    accuracy["accuracy_22_day"], accuracy["accuracy_1_day"]]],
                            columns=["date", "tic", "mean_absolute_error", "mean_squared_error",
                                    "root_mean_squared_error", "r2_score",
                                    "adjusted_r2_score", "total_perc_diff", "abs_total_diff",
                                    'total_variance_perc_diff', "abs_total_variance_perc_diff",
                                    "correlation_score", "directionality_score","mean_directionality", "confidence_score",
                                    "avg_confidence_score","accuracy_14_day",
                                     "accuracy_22_day", "accuracy_1_day"])
            #print(metric_df)
            metric_data_list.append(metric_df)
            
        metrics_final = pd.concat(metric_data_list)
        metrics_final.reset_index(inplace=True, drop=True)
        #print(metrics_final)
        temp= temp[-81:]
        #print(temp)
        temp.reset_index(inplace=True, drop=True)
        metrics_final['date']=temp['date']
        metrics_final['date']=metrics_final['date'].astype('str')
        #print(metrics_final)
        return metrics_final[metrics_final['date']<=str(target_date)][-1:]
    
    except Exception as e:
        print(e)

def s3_versioning(df, date, prod_name,bucket,path,ss):
    df = df[df['date']==date]
    if len(df)!=0:
        isin = df['isin'].values[0]
        ss.write_advanced_as_df(df,bucket, path.format(product=prod_name,schedular = 'monthly',latest_date=date, etf=isin))
        return True
    else:
        return False

def get_product_name(aigo,bnp,db,etf):
    if etf in aigo:
        prod = 'aigo'
    elif etf in bnp:
        prod='bnp'
    elif etf in db:
        prod='db'
    return prod

def prepare_body_html(body):
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html

def apply_smoothing(x, gradient):
    if len(x) == len(gradient):  # Apply smoothing only if window size matches
        return np.sum(np.array(x) * gradient)
    else:
        return x.iloc[-1]

def er_high_func(row):
    return row['smoothened_monthly_predictions'] + (row['max'] - row['smoothened_monthly_predictions']) * (1 - row['confidence_score'])

def get_2yr_max(api_url,etf,end_date):
    start_date  = (pd.to_datetime(end_date) - BDay(600)).strftime("%Y-%m-%d")
    df = get_instockapi_data (api_url,etf,start_date ,end_date)[['date','monthly_close_change']]
    df = df[-252:]
    df.reset_index(inplace = True, drop = True)
    return max(df.monthly_close_change.values)