from helpers import *
s3conn = s3_config()
ss = s3conn
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

smoothening_gradient = get_smoothening_gradients()

def run_metrics(end_date,all_etfs):
    bestpipeline_hist_upload_bucket = config['S3_paths']['upload_bestpipeline_hist_bucket']
    bestpipeline_hist_upload_path = config['S3_paths']['upload_bestpipeline_hist_path']
    start_date  = (pd.to_datetime(end_date) - BDay(600)).strftime("%Y-%m-%d")
    api_url = config['Inhouse_API']['inhouse_api_url_by_tic']
    metrics_all=[]
    for etf in all_etfs:
        # get_instockapi_data (api_url, firm ,start_date, end_date)
        etf_raw_temp = get_instockapi_data(api_url,etf,start_date, end_date)[['date','actual_monthly_returns']]
        etf_delivery_temp = ss.read_as_dataframe(bestpipeline_hist_upload_bucket,bestpipeline_hist_upload_path.format(etf=etf))[['date','monthly_predictions']]
        etf_delivery_temp.reset_index(inplace=True, drop=True)
        etf_temp = pd.merge(etf_raw_temp, etf_delivery_temp, on = ['date'], how = 'outer')
        etf_temp.sort_values('date', ascending=True, inplace=True)
        etf_temp.fillna(method = 'ffill', inplace=True)
        etf_temp['monthly_predictions'].fillna(method = 'ffill', inplace=True)
        etf_temp['date'] = etf_temp['date'].astype('str')
        #print(etf_temp)
        metrics= calculate_metrics( etf, etf_temp.copy(), end_date)
        #print(metrics)
        if metrics.empty:
            print(f'No data for this etf : {etf}')
            continue
        metrics_all.append(metrics)
    
    metrics_all_df=pd.concat(metrics_all, axis=0)
    metrics_all_df.reset_index(inplace=True, drop=True)
    return metrics_all_df

### List s3 files from s3 bucket and path
def get_s3_keys_from_bucket(bucket_name, folder_path, sort_by_timestamp= True , sort_in_ascending = False):
    s3 = boto3.resource('s3', aws_access_key_id= s3conn._key_id , aws_secret_access_key=s3conn._secret)
    s3_bucket_obj = s3.Bucket(bucket_name)
    key_list = []
    time_stamp_list = []
    try:
        for i, object_summary in enumerate(s3_bucket_obj.objects.filter(Prefix = folder_path)):
            try:
                key_list.append(object_summary.key)
                time_stamp_list.append(object_summary.last_modified)
            except Exception as e:
                print(f'exception in for loop {e}')
    except Exception as e:
        print(f'Exception as outer loop{e}')
    keys_df = pd.DataFrame(list(zip(key_list, time_stamp_list)), columns= ['key', 'timestamp'])
    keys_df=keys_df[keys_df['key']!=folder_path]
    
    if sort_by_timestamp:
        keys_df.sort_values(by=["timestamp"], ascending=sort_in_ascending, inplace=True)
    else:
        keys_df.sort_values(by=["key"], ascending= sort_in_ascending, inplace=True)
        
    return keys_df
    
def get_fmi_data(tic,target_date):
    etf= tic
    fmi_data_bucket = config['S3_paths']['fmi_data_bucket']
    fmi_data_path = config['S3_paths']['fmi_data_path']
    
    try:
        #keys_df= get_s3_keys_from_bucket('micro-ops-output', f'FMI_rollup_new_model/{etf}/')'
        keys_df= get_s3_keys_from_bucket(fmi_data_bucket, fmi_data_path.format(etf = etf))

        while(True):
            file_key=f'FMI_rollup_new_model/{etf}/{etf}_{target_date}.csv'
        
            if file_key in keys_df['key'].values:
                fmi_df=s3conn.read_as_dataframe(fmi_data_bucket, file_key)
                
                fmi_df.rename(columns={'created':'date','Equity':'ETF','pbv':'pbv_rolledup',
                           'price_sales':'price_sales_rolledup',
                          'pe_excl':'pe_excl_rolledup','total_rev':'total_rev_rolledup',
                          'f_score':'Fscore','m_score':'Mscore','i_score':'Iscore',
                          'er_old':'ERscore_autoai','er_new':'ERscore','er_cat':'ERscore_cat','cat_conf':'Confi_cat'}, inplace=True)
                
                #fmi_df.drop(columns= 'ERscore_autoai', inplace=True)
                
                fmi_df.replace(0, np.nan, inplace=True)
                #fmi_df = fmi_df[['date','ETF','ERscore']]
                fmi_df = fmi_df[['date','ETF','ERscore_cat','Confi_cat']]
                if fmi_df.isna().sum().sum()==0:
                    return fmi_df.to_dict('records')[0]
                else:
                    target_date=pd.to_datetime(target_date)-timedelta(1)
                    target_date=target_date.date()
                    
            else:
                target_date=pd.to_datetime(target_date)-timedelta(1)
                target_date=target_date.date()

    
    except Exception as e:
        print(e)

def replace_with_rolledup_catboost_er_confi (prev_df, target_date):
    two_corr_etfs = [tuple(pair) for pair in config['misc']['two_corr_etfs']]
    three_corr_etfs = [tuple(pair) for pair in config['misc']['three_corr_etfs']]
    
    sector_etfs = config['misc']['sector_etfs']
    replacement_with_catboost_rollup = ['XLB', 'XLV', 'XLP', 'XLY', 'XLF', 'XLI', 'XLC', 'XLU', 'XLE','XME']
    print('replacement_with_catboost_rollup_etfs',replacement_with_catboost_rollup)
    
    rolledup_df = []
    for etf in replacement_with_catboost_rollup:
        df = get_fmi_data(etf, target_date)
        rolledup_df.append(df)
    
    rolledup_df = pd.DataFrame(rolledup_df)

    pred_col = 'ERscore_cat'
    confi_col = 'Confi_cat'
    
    all_etfs_to_replace = rolledup_df.ETF.values
    
    for etf in all_etfs_to_replace:
        prev_df.loc[prev_df['tic'] == etf, 'final_monthly_predictions'] = rolledup_df[rolledup_df['ETF']== etf][pred_col].values[0]
        prev_df.loc[prev_df['tic'] == etf, 'final_confidence_score'] = rolledup_df[rolledup_df['ETF']== etf][confi_col].values[0]
        prev_df.loc[prev_df['tic'] == etf, 'selected_etf'] = 'Catboost_Rolledup_ER'
        
    
    # for etf1, etf2 in two_corr_etfs:
    #     etfs = [etf1, etf2]
    #     preferred = [etf for etf in etfs if etf in all_etfs_to_replace]
    #     non_preferred = [etf for etf in etfs if etf not in all_etfs_to_replace]
        
    #     if preferred:
    #         # Pick the first preferred ETF as the source of truth
    #         preferred_value = rolledup_df.loc[rolledup_df['ETF'] == preferred[0], pred_col].values[0]
    #         preferred_confi = rolledup_df.loc[rolledup_df['ETF'] == preferred[0], confi_col].values[0]
    #         for etf in non_preferred:
    #             prev_df.loc[prev_df['tic'] == etf, 'final_monthly_predictions'] = preferred_value
    #             prev_df.loc[prev_df['tic'] == etf, 'final_confidence_score'] = preferred_confi
    
    
    # for etf1, etf2, etf3 in three_corr_etfs:
    #     etfs = [etf1, etf2, etf3]
    #     preferred = [etf for etf in etfs if etf in all_etfs_to_replace]
    #     non_preferred = [etf for etf in etfs if etf not in all_etfs_to_replace]
        
    #     if preferred:
    #         # Pick the first preferred ETF as the source of truth
    #         preferred_value = rolledup_df.loc[rolledup_df['ETF'] == preferred[0], pred_col].values[0]
    #         preferred_confi = rolledup_df.loc[rolledup_df['ETF'] == preferred[0], confi_col].values[0]
    #         for etf in non_preferred:
    #             prev_df.loc[prev_df['tic'] == etf, 'final_monthly_predictions'] = preferred_value
    #             prev_df.loc[prev_df['tic'] == etf, 'final_confidence_score'] = preferred_confi
    
    return prev_df

def get_bestpipeline_forall_etfs(transformed_with_context,transformed_without_context,native,date):
    transformed_with_context_bucket = config['S3_paths']['transformed_with_context_bucket']
    transformed_with_context_path = config['S3_paths']['transformed_with_context_path']
    bestpipeline_hist_upload_bucket = config['S3_paths']['upload_bestpipeline_hist_bucket']
    bestpipeline_hist_upload_path = config['S3_paths']['upload_bestpipeline_hist_path']
    aigo_predictions_bucket = config['S3_paths']['aigo_predictions_bucket']
    db_predictions_bucket = config['S3_paths']['db_predictions_bucket']
    bnp_predictions_bucket = config['S3_paths']['bnp_predictions_bucket']
    sector_predictions_bucket = config['S3_paths']['sector_predictions_bucket']
    aigo_predictions_path = config['S3_paths']['aigo_predictions_path']
    db_predictions_path = config['S3_paths']['db_predictions_path']
    bnp_training_data_path = config['S3_paths']['bnp_training_data_path']
    bnp_finalpredictions_path = config['S3_paths']['bnp_finalpredictions_path']
    sector_predictions_path = config['S3_paths']['sector_predictions_path']
    transformed_without_context_bucket = config['S3_paths']['transformed_without_context_bucket']
    transformed_without_context_path = config['S3_paths']['transformed_without_context_path']
    s3_versioning_bucket = config['S3_paths']['upload_s3versioning_best_pipeline_mapped_bucket']
    s3_versioning_path = config['S3_paths']['upload_s3versioning_best_pipeline_mapped_path']
    isin_tic_map_bucket = config['Tic_ISIN_map']['isin_tic_map_bucket']
    isin_tic_map_path = config['Tic_ISIN_map']['isin_tic_map_path']
    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    bnp_etfs = config['misc']['bnp_etfs']
    tic_isin_map_df = ss.read_as_dataframe(isin_tic_map_bucket,isin_tic_map_path)

    #with context
    for etf in transformed_with_context:
        df = ss.read_as_dataframe(transformed_with_context_bucket,transformed_with_context_path.format(etf=etf))
        df['tic'] = etf
        df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        df['smoothened_monthly_predictions'] = df['monthly_predictions'].rolling(
    window =len(smoothening_gradient), min_periods=1).apply(lambda x: apply_smoothing(x, smoothening_gradient), raw=False)
        if 'isin' not in df.columns:
            df = pd.merge(df,tic_isin_map_df, on='tic')
        df['selected_pipeline'] = 'xform_w_context'
        ss.write_advanced_as_df(df,bestpipeline_hist_upload_bucket, bestpipeline_hist_upload_path.format(etf=etf))
        prod_name = get_product_name(aigo_etfs+sector_etfs,bnp_etfs,db_etfs,etf)
        #if s3_versioning(df, date, etf,s3_versioning_bucket,s3_versioning_path.format(product=prod_name,schedular = 'monthly',latest_date=date,etf=etf),ss):
        if s3_versioning(df, date, prod_name,s3_versioning_bucket,s3_versioning_path,ss):
            print(f"s3 versioning for {etf} done, for the date : {date} ")
        else:
            print(f"Data not available for s3 versioning of {etf}, for the date : {date} ")

    #without context
    for etf in transformed_without_context:
        df = ss.read_as_dataframe(transformed_without_context_bucket,
                                  transformed_without_context_path.format(etf=etf))
        df['tic'] = etf
        if 'isin' not in df.columns:
            df = pd.merge(df,tic_isin_map_df, on='tic')
        df['selected_pipeline'] = 'xform_wo_context'
        ss.write_advanced_as_df(df,bestpipeline_hist_upload_bucket, bestpipeline_hist_upload_path.format(etf=etf))
        prod_name = get_product_name(aigo_etfs+sector_etfs,bnp_etfs,db_etfs,etf)
        if s3_versioning(df, date, prod_name,s3_versioning_bucket,s3_versioning_path,ss):
            print(f"s3 versioning for {etf} done, for the date : {date} ")
        else:
            print(f"Data not available for s3 versioning of {etf}, for the date : {date} ")

    #native
    for etf in native:
        # df = ss.read_as_dataframe(native_bucket,native_predictions.format(etf=etf))
        try:
            df = ss.read_as_dataframe(aigo_predictions_bucket,aigo_predictions_path.format(etf=etf))
        except :
            try:
                df = ss.read_as_dataframe(db_predictions_bucket,db_predictions_path.format(etf=etf))
            except:
                try:
                    df = ss.read_as_dataframe(sector_predictions_bucket,sector_predictions_path.format(etf=etf))
                except:
                    try:
                        df = ss.read_as_dataframe(bnp_predictions_bucket,bnp_training_data_path.format(etf=etf))
                        df2 = ss.read_as_dataframe(bnp_predictions_bucket,bnp_finalpredictions_path.format(etf=etf))[['date','monthly_predictions']]
                        df2.drop_duplicates(subset='date',keep = 'last', inplace = True)
                        df = pd.merge(df,df2, on = 'date', how = 'left')
                        df['monthly_predictions'].fillna(method = 'ffill', inplace = True)
                    except Exception as e:
                        print(e)
        print(etf,df.date.values[-1])
        df['tic'] = etf
        df.drop_duplicates(subset ='date', keep = 'last', inplace=True)
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        df['smoothened_monthly_predictions'] = df['monthly_predictions'].rolling(
    window =len(smoothening_gradient), min_periods=1).apply(lambda x: apply_smoothing(x, smoothening_gradient), raw=False)
        if 'isin' not in df.columns:
            df = pd.merge(df,tic_isin_map_df, on='tic')
        df['selected_pipeline'] = 'auto_ai'
        ss.write_advanced_as_df(df,bestpipeline_hist_upload_bucket, bestpipeline_hist_upload_path.format(etf=etf))
        prod_name = get_product_name(aigo_etfs+sector_etfs,bnp_etfs,db_etfs,etf)
        if s3_versioning(df, date, prod_name,s3_versioning_bucket,s3_versioning_path,ss):
            print(f"s3 versioning for {etf} done, for the date : {date} ")
        else:
            print(f"Data not available for s3 versioning of {etf}, for the date : {date} ")

def select_best_pipeline(run_date):
    bestpipeline_hist_upload_bucket = config['S3_paths']['upload_bestpipeline_hist_bucket']
    bestpipeline_hist_upload_path = config['S3_paths']['upload_bestpipeline_hist_path']
    transformed_with_context = config['misc']['transformed_with_context_list']
    best_pipeline_daily_bucket = config['S3_paths']['upload_best_pipeline_daily_bucket']
    best_pipeline_daily_path = config['S3_paths']['upload_best_pipeline_daily_path']
    upload_best_pipeline_metrics_bucket = config['S3_paths']['upload_best_pipeline_metrics_bucket']
    upload_best_pipeline_metrics_path = config['S3_paths']['upload_best_pipeline_metrics_path']
    upload_all_predictions_merged_local_path = config['S3_paths']['upload_all_predictions_merged_local_path']
    to_modify_etfs = config['misc']['to_modify_etfs']
    api_url = config['Inhouse_API']['inhouse_api_url_by_tic']

    native = config['misc']['native_list']
    transformed_without_context = config['misc']['transformed_without_context_list']
    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    bnp_etfs = config['misc']['bnp_etfs']

    date = (pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
    get_bestpipeline_forall_etfs(transformed_with_context,transformed_without_context,native,date)

    all_predictions = []
    for etf in aigo_etfs+sector_etfs+db_etfs+bnp_etfs:
        curr_df = ss.read_as_dataframe(bestpipeline_hist_upload_bucket,bestpipeline_hist_upload_path.format(etf=etf))
        curr_df = curr_df[curr_df['date']<=date]
        curr_df  = curr_df[['date','smoothened_monthly_predictions','tic','isin','selected_pipeline']][-1:]
    
        curr_dict = curr_df.to_dict('records')[0]
        # curr_dict['etf'] = etf
        all_predictions.append(curr_dict)
    all_predictions_df  = pd.DataFrame(all_predictions)
    ss.write_advanced_as_df(all_predictions_df,best_pipeline_daily_bucket, best_pipeline_daily_path.format(date=date))
    all_etfs = aigo_etfs + sector_etfs + db_etfs + bnp_etfs
    all_metrics = run_metrics(date,all_etfs)
    ss.write_advanced_as_df(all_metrics,upload_best_pipeline_metrics_bucket, upload_best_pipeline_metrics_path.format(date=date))

    metrics = ss.read_as_dataframe(upload_best_pipeline_metrics_bucket,upload_best_pipeline_metrics_path.format(date=date))
    # metrics = metrics[['date','tic','avg_confidence_score']]
    # metrics.columns = ['date','tic','confidence_score']
    metrics = metrics[['tic','avg_confidence_score']]
    metrics.columns = ['tic','confidence_score']
    er_df = ss.read_as_dataframe(best_pipeline_daily_bucket,best_pipeline_daily_path.format(date=date))
    #df_merged = pd.merge(metrics,er_df,on=['tic','date'], how = 'left')
    df_merged = pd.merge(metrics,er_df,on=['tic'], how = 'left')
    df_merged['max'] = df_merged['tic'].apply(lambda etf: get_2yr_max(api_url,etf, date))
    df_merged['er_high'] = df_merged['smoothened_monthly_predictions'] + (df_merged['max'] - df_merged['smoothened_monthly_predictions']) * (1 - df_merged['confidence_score'])
    df_merged['modified_monthly_predictions'] = df_merged.apply(
        lambda x: x['smoothened_monthly_predictions'] if x['tic'] not in  to_modify_etfs else x['er_high'], axis=1)
    ss.write_advanced_as_df(df_merged,best_pipeline_daily_bucket, upload_all_predictions_merged_local_path.format(latest_date=date))
    return df_merged
    
    
def send_email(subject , receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)

def run_correlated_etfs_part(run_date):
    
    ## getting last business date
    latest_date= pd.to_datetime(run_date)- BDay(1)
    latest_date = latest_date.strftime("%Y-%m-%d")
    print(latest_date)
    print(f'Last business day for this run : {latest_date}')

    
    ## getting all the paths from config
    inhouse_api_url_by_tic = config['Inhouse_API']['inhouse_api_url_by_tic']
    two_corr_etfs = [tuple(pair) for pair in config['misc']['two_corr_etfs']]
    three_corr_etfs = [tuple(pair) for pair in config['misc']['three_corr_etfs']]
    aigo_etfs = config['misc']['aigo_etfs']
    sector_etfs = config['misc']['sector_etfs']
    db_etfs = config['misc']['db_etfs']
    bnp_etfs = config['misc']['bnp_etfs']
    to_modify_etfs  =  config['misc']['to_modify_etfs']
    
    best_pipeline_local_bucket = config['S3_paths']['upload_bestpipeline_hist_bucket']
    best_pipeline_local_path = config['S3_paths']['upload_bestpipeline_hist_path']
    best_correlated_local_bucket = config['S3_paths']['upload_best_correlated_local_bucket']
    best_correlated_local_path = config['S3_paths']['upload_best_correlated_local_path']
    all_predictions_merged_bucket = config['S3_paths']['upload_all_predictions_merged_local_bucket']
    all_predictions_merged_path = config['S3_paths']['upload_all_predictions_merged_local_path']
    all_predictions_merged_final_bucket = config['S3_paths']['upload_all_predictions_merged_final_local_bucket']
    all_predictions_merged_final_path = config['S3_paths']['upload_all_predictions_merged_final_local_path']
    product_predictions_aggregate_bucket = config['S3_paths']['upload_product_predictions_aggregate_local_bucket']
    product_predictions_aggregate_path = config['S3_paths']['upload_product_predictions_aggregate_local_path']

    s3versioned_etf_correlated_predictions_bucket =  config['S3_paths']['upload_s3versioned_etf_correlated_predictions_bucket']
    s3versioned_etf_correlated_predictions_path = config['S3_paths']['upload_s3versioned_etf_correlated_predictions_path']
    s3versioned_etf_postprocessed_predictions_bucket =  config['S3_paths']['upload_s3versioned_etf_postprocessed_predictions_bucket']
    s3versioned_etf_postprocessed_predictions_path = config['S3_paths']['upload_s3versioned_etf_postprocessed_predictions_path']

    ### getting end_date, start_date for correlated etfs best model 
    end_date= (pd.to_datetime(latest_date).date()).strftime("%Y-%m-%d")
    start_date= (pd.to_datetime(end_date) - timedelta(weeks= 13)).strftime("%Y-%m-%d")
    prices_start_date = (pd.to_datetime(end_date) - timedelta(days = 250)).strftime("%Y-%m-%d")
    print(start_date, end_date, prices_start_date)

    ### getting the best correlated model & its predictions
    best_model_results = []
    for corrsets in two_corr_etfs + three_corr_etfs:
        dfs = []
        isinset = []
        for tic in corrsets:
            isinset.append(tic)
            df = s3conn.read_as_dataframe(best_pipeline_local_bucket , best_pipeline_local_path.format(etf = tic))
            df['date'] = pd.to_datetime(df['date'])
            df = df[['date', 'monthly_predictions']]
            df['tic'] = tic
            df.columns = ['date', f'monthly_predictions_{tic}','tic']
            df = df[(df['date']>= pd.to_datetime(start_date)) & (df['date']<= pd.to_datetime(end_date)) ]
            df.reset_index(inplace = True, drop = True)
            df.drop(columns= ['tic'], axis = 1, inplace = True)
    
            actuals_df = get_instockapi_data(inhouse_api_url_by_tic, tic, prices_start_date, end_date)[['date',f'actual_monthly_returns']]
            actuals_df['date'] = pd.to_datetime(actuals_df['date'])
            actuals_df.columns  = ['date',f'actual_monthly_returns_{tic}']
            actuals_df = actuals_df[(actuals_df['date']>= pd.to_datetime(start_date)) & (actuals_df['date']<= pd.to_datetime(end_date)) ]
    
            df = pd.merge(df, actuals_df, on='date', how  = 'outer')
            
            dfs.append(df)
        
        df1 = dfs[0]
        for _df_ in dfs[1:]:
            df1= pd.merge(left= df1, right= _df_, on='date', how='outer')
        df1.sort_values('date', ascending = True, inplace = True)
        df1.reset_index(inplace=True, drop=True)
        cols_to_ffill = [col for col in df1.columns if 'monthly_predictions_' in col]
        df1[cols_to_ffill] = df1[cols_to_ffill].ffill()
        cols_to_ffill = [col for col in df1.columns if 'actual_monthly_returns' in col]
        df1[cols_to_ffill] =df1[cols_to_ffill].bfill()
        pred   = 'monthly_predictions'
        target = 'actual_monthly_returns'
        # select the predictions, based on the strategy
        ans_df= select_pred(df1, isinset)
        ans_df.reset_index(inplace=True)
        ans_df['smoothened_pred'] = ans_df['final_pred'].rolling(window = len(smoothening_gradient)).apply(lambda x: np.sum(np.array(x) * smoothening_gradient))
        s3conn.write_advanced_as_df(ans_df, best_correlated_local_bucket, best_correlated_local_path.format(latest_date = latest_date ,etf = corrsets[0]))
        print(corrsets[0])
        raw_er = ans_df['final_pred'].values[-1]
        latest_answer = ans_df['smoothened_pred'].values[-1]
        last_date = ans_df.date.values[-1]
        model_selected = ans_df.model_selected.values[-1]
        print(latest_answer, last_date, model_selected)
        for etf in isinset:
            best_model_results.append({'tic': etf, 'monthly_predictions' : raw_er ,'final_smoothened_predictions':latest_answer, 'latest_date':last_date, 'model_selected':model_selected})

    best_model_results_df = pd.DataFrame(best_model_results)
    print(f'calculating the best model for correlated etfs done')
    print(best_model_results_df)
    s3conn.write_advanced_as_df(best_model_results_df, best_correlated_local_bucket, best_correlated_local_path.format(latest_date = latest_date ,etf =f'correlated_best_model_results_{latest_date}'))


    ### getting the merged predictions file
    merged_predictions_df =s3conn.read_as_dataframe(all_predictions_merged_bucket, all_predictions_merged_path.format(latest_date = latest_date))
    merged_predictions_df = merged_predictions_df[['date','tic','isin','selected_pipeline','confidence_score','smoothened_monthly_predictions','modified_monthly_predictions']]
    merged_predictions_df['final_monthly_predictions'] = merged_predictions_df['modified_monthly_predictions']
    merged_predictions_df['final_confidence_score'] = merged_predictions_df['confidence_score']
    merged_predictions_df['selected_etf'] =   merged_predictions_df['tic']


    ### Replacing confidence and predictions for correlated etfs with best model choosen
    merged_all = []
    for index,row in merged_predictions_df.iterrows():
        if row['tic'] in best_model_results_df['tic'].values:
            selected_model = best_model_results_df[best_model_results_df['tic']== row['tic']]['model_selected'].values[0]
            #print(selected_model)
            if row['tic'] not in to_modify_etfs:
                row['final_monthly_predictions'] = best_model_results_df[best_model_results_df['tic']==row['tic']]['final_smoothened_predictions'].values[0]
            else:
                row['final_monthly_predictions'] = merged_predictions_df[merged_predictions_df['tic']==selected_model]['modified_monthly_predictions'].values[0]
            row['final_confidence_score'] = merged_predictions_df[merged_predictions_df['tic']==selected_model]['confidence_score'].values[0]
            row['selected_etf'] = selected_model
        merged_all.append(row)
    merged_predictions_final_df  = pd.DataFrame(merged_all)
    print(f'replacing confidence and predicions for correlated etfs done')
    print(merged_predictions_final_df)

    ### Replacing the data with catboost rollup data for sector etfs
    merged_predictions_final_df = replace_with_rolledup_catboost_er_confi(merged_predictions_final_df, latest_date)
    
    ### clipping the confidence score at >= 60
    merged_predictions_final_df['final_confidence_score'] = merged_predictions_final_df['final_confidence_score'].apply(treat_confi)
    print(f'clipping the confidence score done')
    print(merged_predictions_final_df)

    s3conn.write_advanced_as_df(merged_predictions_final_df, all_predictions_merged_final_bucket, all_predictions_merged_final_path.format(latest_date = latest_date))

    ### uploading the product wise data to loacl path
    split_upload_df_productwise(merged_predictions_final_df, 'aigo', aigo_etfs, latest_date, product_predictions_aggregate_bucket, product_predictions_aggregate_path, s3conn)
    split_upload_df_productwise(merged_predictions_final_df, 'sector', sector_etfs, latest_date, product_predictions_aggregate_bucket, product_predictions_aggregate_path, s3conn)
    split_upload_df_productwise(merged_predictions_final_df, 'db', db_etfs, latest_date, product_predictions_aggregate_bucket, product_predictions_aggregate_path, s3conn)
    split_upload_df_productwise(merged_predictions_final_df, 'bnp', bnp_etfs, latest_date, product_predictions_aggregate_bucket, product_predictions_aggregate_path, s3conn)

    ### s3 versioning uploading to correlated path
    correlated_only_df = merged_predictions_final_df[merged_predictions_final_df['tic'].isin(best_model_results_df['tic'].values)]
    correlated_only_df.reset_index(inplace = True, drop = True)
    cols_to_keep = ['date', 'tic', 'isin', 'selected_pipeline', 'final_monthly_predictions', 'selected_etf']
    split_upload_df_product_and_isinwise (correlated_only_df, 'aigo', aigo_etfs + sector_etfs , latest_date, s3versioned_etf_correlated_predictions_bucket, s3versioned_etf_correlated_predictions_path, s3conn, cols_to_keep)
    split_upload_df_product_and_isinwise (correlated_only_df, 'db', db_etfs , latest_date, s3versioned_etf_correlated_predictions_bucket, s3versioned_etf_correlated_predictions_path, s3conn, cols_to_keep)
    split_upload_df_product_and_isinwise (correlated_only_df, 'bnp', bnp_etfs , latest_date, s3versioned_etf_correlated_predictions_bucket, s3versioned_etf_correlated_predictions_path, s3conn, cols_to_keep)

    ### s3 versioning uploading to postprocessed path
    cols_to_keep = ['date', 'tic', 'isin', 'selected_pipeline', 'final_monthly_predictions', 'selected_etf']
    split_upload_df_product_and_isinwise (merged_predictions_final_df, 'aigo', aigo_etfs + sector_etfs , latest_date, s3versioned_etf_postprocessed_predictions_bucket, s3versioned_etf_postprocessed_predictions_path, s3conn, cols_to_keep)
    split_upload_df_product_and_isinwise (merged_predictions_final_df, 'db', db_etfs , latest_date, s3versioned_etf_postprocessed_predictions_bucket, s3versioned_etf_postprocessed_predictions_path, s3conn, cols_to_keep)
    split_upload_df_product_and_isinwise (merged_predictions_final_df, 'bnp', bnp_etfs , latest_date, s3versioned_etf_postprocessed_predictions_bucket, s3versioned_etf_postprocessed_predictions_path, s3conn, cols_to_keep)
    print('s3 versioning file upload done')


if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    
    try:
        select_best_pipeline(run_date)
        print(f'run_bestpipeline_etfs_part: Run COMPLETED successfully for run date {run_date}')
        
        run_correlated_etfs_part(run_date)
        print(f'run_correlated_etfs_part: Run COMPLETED successfully for run date {run_date}')

        print(f'Monthly ETFs Post Process: Run COMPLETED successfully for run date {run_date}')
        receiver = config['Gmail_creds']['email_receiver_success']
        send_email(f'Monthly ETFs Post Process : Run COMPLETED successfully for run date {run_date}',receiver, 'SUCCESS')
    
    except Exception as e:
        print(f'Monthly ETFs Post Process : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config['Gmail_creds']['email_receiver_failed']
        send_email( f'Monthly ETFs Post Process : Run FAILED for run date {run_date}',receiver, traceback.format_exc())
        raise