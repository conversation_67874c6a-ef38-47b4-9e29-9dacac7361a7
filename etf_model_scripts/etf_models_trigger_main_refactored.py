"""
Refactored ETF Models Trigger Main Script

This is a refactored version of etf_models_trigger_main.py demonstrating
the use of shared utilities for improved code quality and maintainability.
"""

import os
import sys
import subprocess
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import List, bool

# Import shared utilities package (installed from Git)
from shared_utils import (
    load_config, validate_common_config, create_model_logger, create_email_sender,
    create_s3_manager
)


class ETFModelTrigger:
    """ETF Model execution orchestrator with improved error handling and logging."""
    
    def __init__(self, config_path: str = 'multiassetdailyrun/config.yaml'):
        """
        Initialize ETF Model Trigger.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = load_config(config_path)
        self._validate_config()
        
        # Initialize utilities
        self.s3_manager = create_s3_manager()
        self.email_sender = create_email_sender(self.config.to_dict(), self.s3_manager.s3_client)
        
        # Setup logging
        self.logger = create_model_logger(
            model_name='etf_models_trigger',
            tag='all',
            scheduler='daily',
            run_date=datetime.now().strftime('%Y-%m-%d'),
            log_dir='script_logs'
        )
        
        self.logger.log_model_start({
            'config_path': config_path,
            'timestamp': datetime.now().isoformat()
        })
    
    def _validate_config(self) -> None:
        """Validate configuration requirements."""
        if not validate_common_config(self.config):
            raise ValueError("Configuration validation failed")
        
        required_keys = [
            'Gmail_creds.email_receiver_failed',
            'Gmail_creds.email_receiver_success'
        ]
        
        if not self.config.validate_required_keys(required_keys):
            raise ValueError("Missing required configuration keys")
    
    def send_notification(self, subject: str, status: str, body: str = None) -> bool:
        """
        Send email notification.
        
        Args:
            subject: Email subject
            status: 'success' or 'failed'
            body: Optional email body
            
        Returns:
            True if email sent successfully
        """
        try:
            if status.lower() == 'success':
                recipients = self.config.get('Gmail_creds.email_receiver_success', '')
            else:
                recipients = self.config.get('Gmail_creds.email_receiver_failed', '')
            
            if not recipients:
                self.logger.warning(f"No recipients configured for {status} notifications")
                return False
            
            success = self.email_sender.send_email(subject, recipients, body)
            
            if success:
                self.logger.info(f"Notification sent successfully: {subject}")
            else:
                self.logger.error(f"Failed to send notification: {subject}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")
            return False
    
    def run_script(self, script_path: str, log_dir: str = "script_logs") -> bool:
        """
        Run a script using subprocess with proper logging.
        
        Args:
            script_path: Path to the script to run
            log_dir: Directory for log files
            
        Returns:
            True if script executed successfully, False otherwise
        """
        try:
            # Create log directory
            os.makedirs(log_dir, exist_ok=True)
            
            self.logger.info(f"Starting script: {script_path}")
            
            # Get script information
            abs_script_path = os.path.abspath(script_path)
            script_dir = os.path.dirname(abs_script_path)
            script_name = os.path.basename(abs_script_path)
            script_base = os.path.splitext(script_name)[0]
            
            # Create log file
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file_path = os.path.join(log_dir, f"{script_base}.log")
            
            # Determine command based on file extension
            if script_name.endswith('.py'):
                cmd = [sys.executable, script_name]
            elif script_name.endswith('.sh'):
                cmd = ['bash', script_name]
            else:
                self.logger.error(f"Unsupported script type: {script_name}")
                return False
            
            # Execute script
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=script_dir,
                shell=False
            )
            
            # Write logs
            with open(log_file_path, "w", encoding="utf-8") as log_file:
                log_file.write(f"--- STDOUT ---\n{result.stdout}\n")
                log_file.write(f"\n--- STDERR ---\n{result.stderr}\n")
                log_file.write(f"\n--- RETURN CODE ---\n{result.returncode}\n")
            
            # Check result
            if result.returncode == 0:
                self.logger.info(f"Script completed successfully: {script_path}")
                return True
            else:
                self.logger.error(f"Script failed with return code {result.returncode}: {script_path}")
                self.logger.error(f"Error output: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Exception running script {script_path}: {e}")
            return False
    
    def run_parallel(self, scripts: List[str], max_workers: int = 3) -> bool:
        """
        Run multiple scripts in parallel.
        
        Args:
            scripts: List of script paths
            max_workers: Maximum number of parallel workers
            
        Returns:
            True if all scripts succeeded, False otherwise
        """
        self.logger.info(f"Running {len(scripts)} scripts in parallel with {max_workers} workers")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all scripts
            future_to_script = {
                executor.submit(self.run_script, script): script 
                for script in scripts
            }
            
            # Collect results
            all_success = True
            for future in as_completed(future_to_script):
                script = future_to_script[future]
                try:
                    success = future.result()
                    if not success:
                        all_success = False
                        self.logger.error(f"Parallel script failed: {script}")
                except Exception as e:
                    all_success = False
                    self.logger.error(f"Exception in parallel script {script}: {e}")
        
        return all_success
    
    def run_sequential(self, scripts: List[str]) -> bool:
        """
        Run multiple scripts sequentially.
        
        Args:
            scripts: List of script paths
            
        Returns:
            True if all scripts succeeded, False otherwise
        """
        self.logger.info(f"Running {len(scripts)} scripts sequentially")
        
        for script in scripts:
            if not self.run_script(script):
                self.logger.error(f"Sequential execution stopped due to failure in: {script}")
                return False
        
        return True
    
    def execute_pipeline(self) -> bool:
        """
        Execute the complete ETF model pipeline.
        
        Returns:
            True if pipeline completed successfully, False otherwise
        """
        try:
            self.logger.info("Starting ETF model pipeline execution")
            
            # Define script execution stages
            script_0 = "etfsdailyruns/monthly_bnp_daily_run/bnp_daily_run.py"
            
            script_1_script_2 = [
                "deliverymetrics/delivery_metrics_final.py",
                "etfsdailyruns/monthly-etfs_django_run/monthly_etfs_django_autotrigger.py"
            ]
            
            script_3 = "etfsdailyruns/monthly-etfs_data_updation/etfs_data_updation_to_s3.py"
            
            script_4_script_5 = [
                "etfsdailyruns/transformation_pipeline_without_context/monthly/monthly_without_context_dailyrun.py",
                "etfsdailyruns/transformation_pipeline_without_context/quarterly/quarterly_without_context_dailyrun.py"
            ]
            
            script_6_script_7 = [
                "etfsdailyruns/transformation_pipeline_with_context/monthly/monthly_with_context_dailyrun.py",
                "etfsdailyruns/transformation_pipeline_with_context/quarterly/quarterly_with_context_dailyrun.py"
            ]
            
            scripts_8_sequential = [
                "postprocessingscripts/postprocessed_script_final.py",
                "deliveryscripts/monthly_aigo_preportfolio.py",
                "deliveryscripts/monthly_db_preportfolio.py",
                "deliveryscripts/monthly_bnp_preportfolio.py",
                "deliveryscripts/quarterly_aigo_preportfolio.py",
                "multiassetdailyrun/multiasset_dailyrun.py",
                "deliveryscripts/monthly_multiasset_preportfolio.py",
                "deliveryscripts/quarterly_multiasset_preportfolio.py",
                "clientdelivery/aigobifurcation/aigo_file_bifurcation.py",
                "etfsdailyruns/model_metrics_predictions_versioning/model_metrics_predictions_versioning.py"
            ]
            
            # Execute pipeline stages
            stages = [
                ("Stage 0: BNP Daily Run", [script_0]),
                ("Stage 1-2: Delivery Metrics & Django", script_1_script_2),
                ("Stage 3: Data Updation", [script_3]),
                ("Stage 4-5: Transformation Pipeline", script_4_script_5),
                ("Stage 6-7: Context Pipeline", script_6_script_7),
                ("Stage 8: Post-processing", scripts_8_sequential)
            ]
            
            for stage_name, scripts in stages:
                self.logger.info(f"Executing {stage_name}")
                
                if len(scripts) == 1:
                    # Single script
                    if not self.run_script(scripts[0]):
                        self.logger.error(f"Pipeline failed at {stage_name}")
                        return False
                elif stage_name.startswith("Stage 8"):
                    # Sequential execution
                    if not self.run_sequential(scripts):
                        self.logger.error(f"Pipeline failed at {stage_name}")
                        return False
                else:
                    # Parallel execution
                    if not self.run_parallel(scripts):
                        self.logger.error(f"Pipeline failed at {stage_name}")
                        return False
                
                self.logger.info(f"Completed {stage_name}")
            
            self.logger.log_model_end(success=True, additional_info={'total_stages': len(stages)})
            return True
            
        except Exception as e:
            self.logger.error(f"Pipeline execution failed: {e}")
            self.logger.log_model_end(success=False)
            return False


def main():
    """Main execution function."""
    try:
        # Initialize trigger
        trigger = ETFModelTrigger()
        
        # Execute pipeline
        success = trigger.execute_pipeline()
        
        # Send notification
        if success:
            trigger.send_notification(
                'ETF Models: Run COMPLETED successfully',
                'success',
                'All ETF model scripts executed successfully.'
            )
        else:
            trigger.send_notification(
                'ETF Models: Run FAILED',
                'failed',
                'ETF model pipeline execution failed. Check logs for details.'
            )
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"Critical error in main execution: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
