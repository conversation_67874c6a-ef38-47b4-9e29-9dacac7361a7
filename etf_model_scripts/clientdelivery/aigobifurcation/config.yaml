Gmail_creds:
    gmail_creds_bucket : etf-predictions
    gmail_creds_path: preportfolio/gmail_credentials/credentials.json
    gmail_sender : <EMAIL>
    email_receiver_success : <EMAIL>, <EMAIL>, <EMAIL>
    email_receiver_failed : <EMAIL>, <EMAIL>, <EMAIL>



Misc:
    aimax_ets : [BNDX,EEM,EFA,EMB,EWJ,GLD,HYG,IWM,IYR,LQD,QQQ,SHY,SPY,TIP,TLT]
    aigo_mercube_etfs : [BNDX,EEM,EFA,EMB,EWJ,GLD,HYG,IWM,IYR,LQD,METYSV3,QQQ,SPY,TIP,XLE,XME,TREASURY_10YR,TREASURY_2YR,TREASURY_30YR]
    aigo_etfs : [BNDX,EEM,EFA,EMB,EWJ,GLD,HYG,IWM,IYR,LQD,METYS,QQQ,SHY,SPY,TIP,TLT,XLE,XME]
    aisrt_etfs : [XLB,XLC,XLE,XLF,XLI,XLK,XLP,XLRE,XLU,XLV,XLY,XME]
    mercube_ets_mappings : {'METYSV3':'HSMETYV3','TREASURY_2YR':'MQFIUSTU','TREASURY_10YR':'MQFIUSTY','TREASURY_30YR':'MQFIUSUS','SPY':'MQFTUSE1','IWM':'MQFTUSS1','QQQ':'MQFTUSN1'}


S3_paths:
    #upload
    aigo_mercube_deliveryfile_bucket : aimax
    aigo_mercube_deliveryfile_path : AIGO8_MERQUBE_Folder/AIGO_MERQUBE_ER_{date}.xlsx
    aigo_deliveryfile_bucket : aimax
    aigo_deliveryfile_path : AIGO_ER_Folder/AIGO_ER_{date}.xlsx
    aimax_deliveryfile_bucket : aimax
    aimax_deliveryfile_path : AIMAX_ER_Folder/AIMAX_ER_{date}.xlsx
    aisrt_deliveryfile_bucket : aimax
    aisrt_deliveryfile_path : AISRT_MERQUBE_Folder/AISRT_MERQUBE_ER_{date}.xlsx



    #read
    preportfolio_file_bucket : aimax
    preportfolio_file_path : ETF_Pre_Portfolio_Folder/ETF_pre_portfolio_{date}.xlsx