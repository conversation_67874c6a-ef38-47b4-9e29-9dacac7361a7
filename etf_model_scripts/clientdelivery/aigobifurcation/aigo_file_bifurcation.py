from helpers import *

def aigo_file_bifurcation(delivery_date):
    datelist = pd.bdate_range((pd.to_datetime(delivery_date)-BDay(5)).strftime("%Y-%m-%d"), delivery_date)
    for date in datelist[::-1]:
        preportfolio_date = (pd.to_datetime(date)-BDay(1)).strftime("%m_%d_%Y")
        try:
            preportfolio_file = s3conn.read_as_dataframe(config['S3_paths']['preportfolio_file_bucket'],config['S3_paths']['preportfolio_file_path'].format(date=preportfolio_date))[['Date','Equity','Avg','confidence_score']]
            break
        except:
            continue
    preportfolio_file.rename(columns={'Avg':'ER'},inplace=True)
    generate_aigo_mercube_outgoing_file(preportfolio_file,delivery_date)
    generate_aigo_outgoing_file(preportfolio_file,delivery_date)
    generate_aisrt_outgoing_file(preportfolio_file,delivery_date)
    generate_aimax_outgoing_file(preportfolio_file,delivery_date)

if __name__ == "__main__":
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")
    
    # run_date = (datetime.today()-BDay(0)).strftime("%Y-%m-%d")
    # run_date = '2025-04-07'
    try:
        aigo_file_bifurcation(run_date)
        print(f"AIGO file bifurcation completed for {run_date}")
        send_email(f'Monthly AIGO File Bifurcation : Run COMPLETED successfully for {run_date}','SUCCESS')
    except Exception as e:
        print(f'Monthly AIGO File Bifurcation : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        send_email( f'Monthly AIGO File Bifurcation : Run FAILED for run date {run_date}', traceback.format_exc())
        print(e)
        raise
