import sys
from eq_common_utils.utils.config.s3_config import s3_config
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
import requests 
import pandas as pd
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from functools import reduce
import xlsxwriter
from io import BytesIO
import traceback


s3conn = s3_config()
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

aimax_etfs = config['Misc']['aimax_ets']
aigo_mercube_etfs = config['Misc']['aigo_mercube_etfs']
aigo_etfs = config['Misc']['aigo_etfs']
aisrt_etfs = config['Misc']['aisrt_etfs']


def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def upload_obj(bucket,object_path,obj):
    s3_client = boto3.client('s3',aws_access_key_id=s3conn._key_id,
         aws_secret_access_key=s3conn._secret)
    response = s3_client.upload_fileobj(obj, bucket, object_path) 

def upload_excel_to_s3(df,cols,bucket,path,decimal_places=None,perc_or_not = True):
    if not perc_or_not:
        cols_to_round = ['ER', 'confidence_score']
        df[cols_to_round] = df[cols_to_round].round(15)
    buffer = BytesIO()
    if decimal_places is not None:
        format_string = f'0.{"0" * decimal_places}%'
    with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet0')
    
        # Access workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Sheet0']
        # Apply percentage format to ER column (C column in Excel)
        if perc_or_not:
            percent_format = workbook.add_format({'num_format': format_string})
            for col in cols:
                df[col] = df[col].round(15)
                col_idx = df.columns.get_loc(col)
                col_letter = xlsxwriter.utility.xl_col_to_name(col_idx)
                worksheet.set_column(f'{col_letter}:{col_letter}', None, percent_format)
    # Reset buffer position
    buffer.seek(0)
    upload_obj(bucket,path,buffer)

def generate_aigo_mercube_outgoing_file(df,delivery_date):
    df['Date'] = pd.to_datetime(delivery_date).strftime("%m/%d/%Y")
    mercube_date = pd.to_datetime(df['Date'].values[0]).strftime("%Y_%m_%d")
    df = df[df['Equity'].isin(aigo_mercube_etfs)]
    df.reset_index(inplace=True,drop=True)
    df = df.set_index('Equity').reindex(aigo_mercube_etfs).reset_index()
    df = df[['Date','Equity','ER','confidence_score']]
    df['Equity'] = df['Equity'].replace(config['Misc']['mercube_ets_mappings'])
    upload_excel_to_s3(df,['ER'],config['S3_paths']['aigo_mercube_deliveryfile_bucket'],config['S3_paths']['aigo_mercube_deliveryfile_path'].format(date=mercube_date),perc_or_not = False)
    return df,mercube_date

def generate_aigo_outgoing_file(df,delivery_date):
    df['Date'] = pd.to_datetime(delivery_date).strftime("%m/%d/%Y")
    aigo_date = pd.to_datetime(df['Date'].values[0]).strftime("%m_%d_%Y")
    df = df[df['Equity'].isin(aigo_etfs)]
    df.reset_index(inplace=True,drop=True)
    df = df.set_index('Equity').reindex(aigo_etfs).reset_index()
    df = df[['Date','Equity','ER','confidence_score']]
    upload_excel_to_s3(df,['ER'],config['S3_paths']['aigo_deliveryfile_bucket'],config['S3_paths']['aigo_deliveryfile_path'].format(date=aigo_date),7)
    return df,aigo_date

def generate_aisrt_outgoing_file(df,delivery_date):
    df['Date'] = pd.to_datetime(delivery_date).strftime("%Y/%m/%d")
    aisrt_date = pd.to_datetime(df['Date'].values[0]).strftime("%Y_%m_%d")
    df = df[df['Equity'].isin(aisrt_etfs)]
    df.reset_index(inplace=True,drop=True)
    df = df.set_index('Equity').reindex(aisrt_etfs).reset_index()
    df = df[['Date','Equity','ER','confidence_score']]
    upload_excel_to_s3(df,['ER'],config['S3_paths']['aisrt_deliveryfile_bucket'],config['S3_paths']['aisrt_deliveryfile_path'].format(date=aisrt_date),7)
    return df,aisrt_date

def generate_aimax_outgoing_file(df,delivery_date):
    df['Date'] = pd.to_datetime(delivery_date).strftime("%m/%d/%Y")
    aimax_date = pd.to_datetime(df['Date'].values[0]).strftime("%m_%d_%Y")
    df = df[df['Equity'].isin(aimax_etfs)]
    df = df.set_index('Equity').reindex(aimax_etfs).reset_index()
    df = df[['Equity','ER']]
    df.reset_index(inplace=True,drop=True)
    df.set_index('Equity',inplace=True)
    df_t = df.T
    df_t.index = [aimax_date.replace("_","/")]
    df_t.index.name = "Date"
    df_t.columns.name = None
    df_t.reset_index(inplace=True)
    upload_excel_to_s3(df_t,aimax_etfs,config['S3_paths']['aimax_deliveryfile_bucket'],config['S3_paths']['aimax_deliveryfile_path'].format(date=aimax_date),4)
    return df_t,aimax_date

def prepare_body_html(body):
    receiver = config['Gmail_creds']['email_receiver_success']
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    receiver = [email.strip() for email in receiver.split(',')]
    return html,receiver

def send_email(subject , body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']
    # receiver_passed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_passed')
    # receiver_failed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_failed')
    # receiver = [email.strip() for email in receiver.split(',')]
    body_html,receiver = prepare_body_html(body)
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)



