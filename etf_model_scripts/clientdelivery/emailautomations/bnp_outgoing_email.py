from helpers import *

def bnp_client_delivery(run_date):
    # try:
    today = datetime.today().strftime("%Y-%m-%d")
    df = get_latest_csv(config['S3_paths']['bnp_deliveryfile_path'],config['S3_paths']['bnp_deliveryfile_bucket'])
    s3conn.write_advanced_as_df(df,config['S3_paths']['sftp_bnp_upload_bucket'],config['S3_paths']['sftp_bnp_upload_path'].format(date=today))
    send_bnp_email(f'BNP Delivery: completed for run date {run_date}')
    print("Mail has been sent.")
    # except Exception as e:
    #     print(e)

if __name__ == "__main__":
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")
    
    # run_date = (datetime.today()-BDay(0)).strftime("%Y-%m-%d")
    # run_date = '2025-04-07'
    try:
        bnp_client_delivery(run_date)
        print(f"BNP client delivery script completed for {run_date}")
        send_email(f'BNP client delivery script : Run COMPLETED successfully for {run_date}','SUCCESS')
    except Exception as e:
        print(f'BNP client delivery script : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        send_email( f'BNP client delivery script : Run FAILED for run date {run_date}', traceback.format_exc())
        print(e)
        raise
