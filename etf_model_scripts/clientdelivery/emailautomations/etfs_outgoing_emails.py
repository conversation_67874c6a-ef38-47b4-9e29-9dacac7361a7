from helpers import *

def aigo_er_first_email(run_date):
    aigo_date = pd.to_datetime(run_date).strftime("%m_%d_%Y")
    aigo_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aigo_deliveryfile_bucket'],config['S3_paths']['aigo_deliveryfile_path'].format(date=aigo_date))
    filename = f'AIGO_ER_{aigo_date}.xlsx'
    get_on_local(aigo_delivery_file,['ER'],filename,decimal_places=7)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aigo']['first_email_receiver_success'],subject=f'PORTFOLIO AIGO ON {pd.to_datetime(aigo_date.replace("_","-")).strftime('%d-%m-%Y')}',body=config['body']['aigo'],attachment_path=filepath,alias = 'AIGO_Portfolio')
    os.remove(filepath)

def aigo_er_second_email(run_date):
    aigo_date = pd.to_datetime(run_date).strftime("%m_%d_%Y")
    aigo_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aigo_deliveryfile_bucket'],config['S3_paths']['aigo_deliveryfile_path'].format(date=aigo_date))
    filename = f'AIGO_ER_{aigo_date}.xlsx'
    get_on_local(aigo_delivery_file,['ER'],filename,decimal_places=7)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aigo']['second_email_receiver_success'],subject=f'PORTFOLIO AIGO ON {pd.to_datetime(aigo_date.replace("_","-")).strftime('%d-%m-%Y')}',body=config['body']['aigo'],attachment_path=filepath,alias = 'AIGO_Portfolio')
    df = pd.read_excel(filepath)
    sftp_upload(df,['ER'],file_path = filepath)
    os.remove(filepath)

def aimax_first_email(run_date):
    aimax_date = pd.to_datetime(run_date).strftime("%m_%d_%Y")
    aimax_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aimax_deliveryfile_bucket'],config['S3_paths']['aimax_deliveryfile_path'].format(date=aimax_date))
    filename = f'AIMAX_ER_{aimax_date}.xlsx'
    get_on_local(aimax_delivery_file,config['Misc']['aimax_etfs'],filename,decimal_places=4)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aimax']['first_email_receiver_success'],subject=f'PORTFOLIO AIMAX ON {pd.to_datetime(aimax_date.replace("_","-")).strftime('%d-%m-%Y')}',body=config['body']['aimax'],attachment_path=filepath,alias = 'AIMAX_Portfolio')
    os.remove(filepath)

def aimax_second_email(run_date):
    aimax_date = pd.to_datetime(run_date).strftime("%m_%d_%Y")
    aimax_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aimax_deliveryfile_bucket'],config['S3_paths']['aimax_deliveryfile_path'].format(date=aimax_date))
    filename = f'AIMAX_ER_{aimax_date}.xlsx'
    get_on_local(aimax_delivery_file,config['Misc']['aimax_etfs'],filename,decimal_places=4)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aigo']['second_email_receiver_success'],subject=f'PORTFOLIO AIMAX ON {pd.to_datetime(aimax_date.replace("_","-")).strftime('%d-%m-%Y')}',body=config['body']['aimax'],attachment_path=filepath,alias = 'AIMAX_Portfolio')
    df = pd.read_excel(filepath)
    sftp_upload(df,['ER'],file_path = filepath)
    os.remove(filepath)

def aigo_merqube_first_email(run_date):
    aigo_mercube_date = pd.to_datetime(run_date).strftime("%Y_%m_%d")
    aigo_mercube_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aigo_mercube_deliveryfile_bucket'],config['S3_paths']['aigo_mercube_deliveryfile_path'].format(date=aigo_mercube_date))
    filename = f'AIGO_MERQUBE_ER_{aigo_mercube_date}.xlsx'
    get_on_local(aigo_mercube_delivery_file,['ER'],filename,perc_or_not = False)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aigo_mercube']['first_email_receiver_success'],subject=f'PORTFOLIO AIGO ON {pd.to_datetime(aigo_mercube_date.replace("_","-")).strftime('%Y-%m-%d')}',body=config['body']['aigo_mercube'],attachment_path=filepath,alias = 'AIGO Email')
    os.remove(filepath)

def aigo_merqube_second_email(run_date):
    aigo_mercube_date = pd.to_datetime(run_date).strftime("%Y_%m_%d")
    aigo_mercube_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aigo_mercube_deliveryfile_bucket'],config['S3_paths']['aigo_mercube_deliveryfile_path'].format(date=aigo_mercube_date))
    filename = f'AIGO_MERQUBE_ER_{aigo_mercube_date}.xlsx'
    get_on_local(aigo_mercube_delivery_file,['ER'],filename,perc_or_not = False)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aigo_mercube']['second_email_receiver_success'],subject=f'PORTFOLIO AIGO ON {pd.to_datetime(aigo_mercube_date.replace("_","-")).strftime('%Y-%m-%d')}',body=config['body']['aigo_mercube'],attachment_path=filepath,alias = 'AIGO Email')
    df = pd.read_excel(filepath)
    # sftp_upload(df,['ER'],file_path = filepath)
    sftp_upload(df,['ER'],config['S3_paths']['sftp_aigo_mercube_upload_bucket'],config['S3_paths']['sftp_aigo_mercube_upload_path'].format(date=aigo_mercube_date),file_path=filepath,perc_or_not = False,direct=False)
    os.remove(filepath)

def aisrt_merqube_first_email(run_date):
    aisrt_mercube_date = pd.to_datetime(run_date).strftime("%Y_%m_%d")
    aisrt_mercube_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aisrt_deliveryfile_bucket'],config['S3_paths']['aisrt_deliveryfile_path'].format(date=aisrt_mercube_date))
    filename = f'AISRT_MERQUBE_ER_{aisrt_mercube_date}.xlsx'
    get_on_local(aisrt_mercube_delivery_file,['ER'],filename,decimal_places=7)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aisrt']['first_email_receiver_success'],subject=f'PORTFOLIO AISRT ON {pd.to_datetime(aisrt_mercube_date.replace("_","-")).strftime('%Y-%m-%d')}',body=config['body']['aisrt'],attachment_path=filepath,alias = 'AISRT Email')
    os.remove(filepath)

def aisrt_merqube_second_email(run_date):
    aisrt_mercube_date = pd.to_datetime(run_date).strftime("%Y_%m_%d")
    aisrt_mercube_delivery_file = s3conn.read_as_dataframe(config['S3_paths']['aisrt_deliveryfile_bucket'],config['S3_paths']['aisrt_deliveryfile_path'].format(date=aisrt_mercube_date))
    filename = f'AISRT_MERQUBE_ER_{aisrt_mercube_date}.xlsx'
    get_on_local(aisrt_mercube_delivery_file,['ER'],filename,decimal_places=7)
    filepath = f'{os.getcwd()}/{filename}'
    send_email_with_attachment(sender_email=config['Gmail_creds']['gmail_sender'],token_password=config['Gmail_creds']['token_password'],recipient_email=config['email_list']['aisrt']['second_email_receiver_success'],subject=f'PORTFOLIO AISRT ON {pd.to_datetime(aisrt_mercube_date.replace("_","-")).strftime('%Y-%m-%d')}',body=config['body']['aisrt'],attachment_path=filepath,alias = 'AISRT Email')
    df = pd.read_excel(filepath)
    # sftp_upload(df,['ER'],file_path = filepath)
    sftp_upload(df,['ER'],config['S3_paths']['sftp_aisrt_upload_bucket'],config['S3_paths']['sftp_aisrt_upload_path'].format(date=aisrt_mercube_date),file_path=filepath,decimal_places=7,direct=False)
    os.remove(filepath)

def get_actual_run_date(run_date):
    today = datetime.strptime(run_date,"%Y-%m-%d").date()
    weekday = today.weekday()  # Monday=0, ..., Sunday=6

    # Only proceed on Thursday or Friday
    if weekday not in [3, 4]:
        print("This script should only run on Thursday or Friday.")
        send_email(f'FRIDAY CHECK: for run date {run_date}', f"Delivery Script not triggered as this script should only run on Thursday or Friday. Try running with 'friday_check' trigger as False.")
        sys.exit(0)

    # Determine target Thursday and Friday
    friday = today if weekday == 4 else today + timedelta(days=1)
    thursday = friday - timedelta(days=1)

    # Get market open days between Thursday and Friday
    schedule = market.valid_days(start_date=thursday.strftime('%Y-%m-%d'), end_date=friday.strftime('%Y-%m-%d'))
    schedule = pd.to_datetime(schedule).date

    if weekday == 3:  # Running on Thursday
        if friday not in schedule:
            print("Friday is a market holiday. Running today (Thursday).")
            send_email(f'FRIDAY CHECK: for run date {run_date}', f"Friday is a market holiday. Running today (Thursday).")
            return today.strftime("%Y-%m-%d")
        else:
            print("Friday is a trading day. Skipping Thursday run.")
            send_email(f'FRIDAY CHECK: for run date {run_date}', f'Friday is a trading day. Skipping Thursday run.')
            sys.exit(0)
    elif weekday == 4:  # Running on Friday
        if today in schedule:
            print("Running today (Friday) as it's a trading day.")
            send_email(f'FRIDAY CHECK: for run date {run_date}', f'Running today (Friday) as its a trading day.')
            return today.strftime("%Y-%m-%d")
        else:
            print("Friday is a market holiday. Already handled on Thursday.")
            send_email(f'FRIDAY CHECK: for run date {run_date}', f'Friday is a market holiday. Already handled on Thursday.')
            sys.exit(0)

if __name__ == "__main__":
    if len(sys.argv)== 2:
        run_date= date.today().strftime("%Y-%m-%d")
        friday_check = True
    elif len(sys.argv) == 3:
        if is_valid_date(sys.argv[2]):
            run_date=pd.to_datetime(sys.argv[2]).strftime("%Y-%m-%d")
            friday_check = True
            # run_date = get_actual_run_date(sys.argv[2])
        else:
            run_date= date.today().strftime("%Y-%m-%d")
            friday_check = sys.argv[2]      
    elif len(sys.argv) > 3:
        run_date=pd.to_datetime(sys.argv[2]).strftime("%Y-%m-%d")
        friday_check = sys.argv[3]
    
    task_name =  sys.argv[1]
    if friday_check==True:
        run_date = get_actual_run_date(run_date)
    print(f"Running task: {task_name} for run date: {run_date}")
    try:
        if task_name == 'aigo_er_first_email':
            aigo_er_first_email(run_date)
            print(f"AIGO ER first email sent for {run_date}")
        elif task_name == 'aigo_er_second_email':
            aigo_er_second_email(run_date)
            print(f"AIGO ER second email sent for {run_date}")
        elif task_name == 'aimax_first_email':
            aimax_first_email(run_date)
            print(f"AIMAX first email sent for {run_date}")
        elif task_name == 'aimax_second_email':
            aimax_second_email(run_date)
            print(f"AIMAX second email sent for {run_date}")
        elif task_name == 'aigo_merqube_first_email':
            aigo_merqube_first_email(run_date)
            print(f"AIGO Mercube first email sent for {run_date}")
        elif task_name == 'aigo_merqube_second_email':
            aigo_merqube_second_email(run_date)
            print(f"AIGO Mercube second email sent for {run_date}")
        elif task_name == 'aisrt_merqube_first_email':
            aisrt_merqube_first_email(run_date)
            print(f"AISRT Mercube first email sent for {run_date}")
        elif task_name == 'aisrt_merqube_second_email':
            aisrt_merqube_second_email(run_date)
            print(f"AISRT Mercube second email sent for {run_date}")
        elif task_name =='all_tasks':
            aigo_er_first_email(run_date)
            aigo_er_second_email(run_date)
            aimax_first_email(run_date)
            aimax_second_email(run_date)
            aigo_merqube_first_email(run_date)
            aigo_merqube_second_email(run_date)
            aisrt_merqube_first_email(run_date)
            aisrt_merqube_second_email(run_date)
            print(f"All tasks completed successfully for run date: {run_date}")
        else:
            raise ValueError("Invalid task name provided.")
        print(f"Task {task_name} completed successfully for run date: {run_date}")
        send_email(f'TASK: {task_name} : Run SUCCESSFUL for run date {run_date}', f'{task_name} sent successfully for run date: {run_date}')
    except Exception as e:
        print(f"An error occurred while running the task:{task_name}.")
        print(traceback.format_exc())
        send_email( f'TASK: {task_name} : Run FAILED for run date {run_date}', traceback.format_exc())
        print(e)
        raise
