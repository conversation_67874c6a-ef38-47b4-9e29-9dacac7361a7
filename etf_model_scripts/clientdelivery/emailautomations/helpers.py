import sys
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.sftp_helper import Sftp
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
import requests 
import pandas as pd
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from functools import reduce
import xlsxwriter
from io import BytesIO
import traceback
import smtplib
from email.message import EmailMessage
import os
import pandas_market_calendars as mcal
from email.utils import formataddr




s3conn = s3_config()
with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

market = mcal.get_calendar("NYSE")

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

def send_email_with_attachment(sender_email, token_password, recipient_email, subject, body, attachment_path,alias):
    # Create the email message
    msg = EmailMessage()
    msg['Subject'] = subject
    msg['From'] = formataddr((alias, sender_email))
    # msg['From'] = sender_email
    msg['To'] = recipient_email
    # msg.attach(MIMEText(body, "html"))
    msg.set_content(body)
    msg.add_alternative(body, subtype='html')

    # Attach the file
    if attachment_path and os.path.exists(attachment_path):
        with open(attachment_path, 'rb') as f:
            file_data = f.read()
            file_name = os.path.basename(attachment_path)
            msg.add_attachment(file_data, maintype='application', subtype='octet-stream', filename=file_name)
    else:
        print(f"Attachment not found: {attachment_path}")
        return
    # Determine SMTP server based on sender's domain
    # if 'gmail.com' in sender_email:
    smtp_server = 'smtp.gmail.com'
    smtp_port = 587
    # elif 'outlook.com' in sender_email or 'hotmail.com' in sender_email:
    #     smtp_server = 'smtp.office365.com'
    #     smtp_port = 587
    # else:
    #     raise ValueError("Unsupported email provider. Set SMTP manually if needed.")
    # # Send the email

    try:
        with smtplib.SMTP(smtp_server, smtp_port) as smtp:
            smtp.starttls()
            smtp.login(sender_email, token_password)
            smtp.send_message(msg)
            print("Email sent successfully.")
    except Exception as e:
        print(f"Failed to send email: {e}")


def get_on_local(df, cols, filename, decimal_places=None, perc_or_not=True):
    if not perc_or_not:
        cols_to_round = ['ER', 'confidence_score']
        df[cols_to_round] = df[cols_to_round].round(15)

    buffer = BytesIO()
    
    if decimal_places is not None:
        format_string = f'0.{"0" * decimal_places}%'

    with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet0')

        # Access workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Sheet0']

        # Apply percentage format if required
        if perc_or_not and decimal_places is not None:
            percent_format = workbook.add_format({'num_format': format_string})
            for col in cols:
                df[col] = df[col].round(15)
                col_idx = df.columns.get_loc(col)
                col_letter = xlsxwriter.utility.xl_col_to_name(col_idx)
                worksheet.set_column(f'{col_letter}:{col_letter}', None, percent_format)

    # Save the Excel file locally
    with open(filename, 'wb') as f:
        f.write(buffer.getvalue())

    # Reset buffer for potential upload/email
    buffer.seek(0)
    # return buffer  # Optional: return buffer if needed

def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def prepare_body_html(body):
    receiver = config['Gmail_creds']['internal_email_list']
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    receiver = [email.strip() for email in receiver.split(',')]
    return html,receiver

def send_email(subject , body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_internal_sender']
    body_html,receiver = prepare_body_html(body)
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)

def upload_obj(bucket,object_path,obj):
    s3_client = boto3.client('s3',aws_access_key_id=s3conn._key_id,
         aws_secret_access_key=s3conn._secret)
    response = s3_client.upload_fileobj(obj, bucket, object_path) 


def upload_excel_to_s3(df,cols,bucket,path,decimal_places=None,perc_or_not = True):
    if not perc_or_not:
        cols_to_round = ['ER', 'confidence_score']
        df[cols_to_round] = df[cols_to_round].round(15)
    buffer = BytesIO()
    if decimal_places is not None:
        format_string = f'0.{"0" * decimal_places}%'
    with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet0')
    
        # Access workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Sheet0']
        # Apply percentage format to ER column (C column in Excel)
        if perc_or_not:
            percent_format = workbook.add_format({'num_format': format_string})
            for col in cols:
                df[col] = df[col].round(15)
                col_idx = df.columns.get_loc(col)
                col_letter = xlsxwriter.utility.xl_col_to_name(col_idx)
                worksheet.set_column(f'{col_letter}:{col_letter}', None, percent_format)
    # Reset buffer position
    buffer.seek(0)
    upload_obj(bucket,path,buffer)


def sftp_upload(df,cols,bucket=None,path = None,file_path=None,decimal_places=None,perc_or_not = True,direct=True):
    if direct:
        host = config['sftp_creds']['sftp_host']
        port = config['sftp_creds']['sftp_port']
        usr = config['sftp_creds']['sftp_username']
        pwd = config['sftp_creds']['sftp_password']
        print(f"Uploading to SFTP server {host} at port {port} with user {usr}")
        filename = file_path.split('/')[-1]
        sftp = Sftp(host,usr,pwd,port)
        sftp.connect()
        sftp.upload(file_path, f"/{filename}")
        sftp.disconnect()
    else:
        upload_excel_to_s3(df,cols,bucket,path,decimal_places,perc_or_not)

def is_valid_date(s):
    try:
        datetime.strptime(s, "%Y-%m-%d")
        return True
    except ValueError:
        return False
    
def get_latest_csv(folder_name,bucket_name):
    s3=boto3.client('s3',aws_access_key_id=s3conn._key_id,aws_secret_access_key=s3conn._secret)
    paginator = s3.get_paginator('list_objects_v2')
    pages = paginator.paginate(Bucket=bucket_name, Prefix=folder_name)
    for page in pages:
        page_content_list=page['Contents']
    page_content_list=sorted(page_content_list, key = lambda i: i['LastModified'])
    get_file_name=page_content_list[-1].get('Key')
    object_file_name = s3.get_object(Bucket=bucket_name, Key=get_file_name)
    print(get_file_name)
    input_file = pd.read_csv(object_file_name['Body'])
    return input_file

def prepare_bnp_body_html(body):
    receiver = config['email_list']['bnp']
    body = '<html><title></title><body>Hi,<br><p>BNP delivery file has been uploaded to sftp succesfully. <br><br>Thanks<br>Tanmay</body></html>'
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    receiver = [email.strip() for email in receiver.split(',')]
    return html,receiver

def send_bnp_email(subject , body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_internal_sender']
    body_html,receiver = prepare_bnp_body_html(body)
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)






