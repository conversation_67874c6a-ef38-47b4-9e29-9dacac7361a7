from helpers import *

ss = s3_config()
config = configparser.ConfigParser()
config.read('config.properties')

def prepare_body_html(body):
    if isinstance(body, pd.DataFrame):
        body = body.to_html(index=False, border=1)
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html

def send_email(subject , receiver, body = None):
    gmail_creds_bucket = config.get('S3_files','gmail_creds_bucket')
    gmail_creds_path =config.get('S3_files','gmail_creds_path')
    sender = config.get('S3_files','gmail_sender')
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, ss, sender, receiver , subject, body_html)

def get_delivery_file(run_date):
    latest_date = (pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
    latest_date_f= (pd.to_datetime(run_date)-BDay(1)).strftime("%m-%d-%Y")
    latest_date_c = (pd.to_datetime(run_date)-BDay(1))
    
    monthly_multiasset_bucket = config.get('MONTHLY_MULTIASSET','monthly_multiasset_bucket')
    multiasset_monthly_upload_bucket = config.get('MONTHLY_MULTIASSET','multiasset_monthly_upload_bucket')
    multiasset_error_rate_filename_path = config.get('MONTHLY_MULTIASSET','multiasset_error_rate_filename_path').format(latest_date = latest_date)
    multiasset_monthly_localfile_path = config.get('MONTHLY_MULTIASSET','multiasset_monthly_localfile_path')
    multiasset_monthly_upload_path = config.get('MONTHLY_MULTIASSET','multiasset_monthly_upload_path').format(date=latest_date_f)
    multiasset_monthly_versioning_bucket = config.get('MONTHLY_MULTIASSET','multiasset_monthly_versioning_bucket')
    multiasset_monthly_versioning_path = config.get('MONTHLY_MULTIASSET','multiasset_monthly_versioning_path').format(date=latest_date)
    api_url = config.get('Inhouse_API','inhouse_api_url_by_isin')
    
    err_df = ss.read_as_dataframe(monthly_multiasset_bucket,multiasset_error_rate_filename_path)
    err_df = err_df.rename(columns={'tic':'Equity'})

    df_t = ss.read_as_dataframe(monthly_multiasset_bucket,multiasset_monthly_localfile_path)
    sdate = (latest_date_c - relativedelta(years=2)).strftime("%Y-%m-%d")
    edate = latest_date
    df_close = pd.DataFrame()
    for i in range(len(df_t)):
        isin = df_t.iloc[i]['isin']
        # ccode = df_t.iloc[i]['country_code']
        df_c = close_price(api_url,isin,sdate,edate)
        #df_c = df_c.rename(columns={isin:tic})
        if i == 0:
            df_close = df_c.copy()
        else:    
            df_close = pd.merge(df_close,df_c,on='date',how='left')  
    # df_close.to_csv(local_path+"close.csv",index=False)        
    df_close.set_index('date', inplace=True)
    df3 = df_close.pct_change(periods=22)
    df3 = df3.describe().T
    df3.index.names = ['isin']     
    df3 = df3.reset_index()
    err_df = pd.merge(err_df,df_t,on='Equity',how='left')
    err_df = pd.merge(err_df,df3[['isin','min','max']],on='isin',how='left')
    err_df = err_df.rename(columns={'monthly_close_change':'actual_change','monthly_predictions':'ER'})
    err_df['ER'] = err_df['ER']/100
    err_df['actual_change'] = err_df['actual_change']/100
    err_df['Avg'] = err_df['ER']
    err_df = err_df.assign(er_high=err_df.apply(lambda row: er_high_func(row),axis=1))
    err_df = err_df.assign(er_low=err_df.apply(lambda row: er_low_func(row),axis=1))
    err_df = err_df.assign(er_high=err_df.apply(lambda row: row['ER'] if row['er_high'] <= row['ER'] else row['er_high'],axis=1))
    err_df = err_df.assign(er_low=err_df.apply(lambda row: row['ER'] if row['er_low'] >= row['ER'] else row['er_low'],axis=1))
    err_df['ER4'] = err_df['ER'] / ((err_df['max'] - err_df['min']) * (1 - err_df['confidence_score']))
    err_df = err_df[['Equity','actual_change','ER','Avg','confidence_score','accuracy','close_price','min','max','er_high','er_low','ER4']]
    print(err_df)
    wb = Workbook()
    ws = wb.active
    for row in dataframe_to_rows(err_df, index=False, header=True):
        ws.append(row)
    actual_loc = err_df.columns.get_loc('actual_change')
    actual_loc = get_column_letter(actual_loc + 1) 
    er_loc = err_df.columns.get_loc('ER')
    er_loc = get_column_letter(er_loc + 1) 
    avg_loc = err_df.columns.get_loc('Avg')
    avg_loc = get_column_letter(avg_loc + 1) 
    #mod_loc = etf_format.columns.get_loc('mod_er')
    #mod_loc = get_column_letter(mod_loc + 1) 
    print(actual_loc,':------:',er_loc,":----:",avg_loc,":-----:")
    f = [actual_loc,er_loc,avg_loc]
    for col_v in f:
        number_format = '0.00%'
        for cell in ws[col_v]:
                cell.number_format = number_format

    upload_excel(ss, wb, multiasset_monthly_upload_bucket, multiasset_monthly_upload_path)
    #s3conn.write_advanced_as_df(wb, local_s3_bucket, local_s3_path.format(latest_date = latest_date.strftime('%m_%d_%Y')))

    ## writting to versioned s3
    ss.write_advanced_as_df(err_df, multiasset_monthly_versioning_bucket, multiasset_monthly_versioning_path)
    # # wb.save(local_path+"ETF_pre_portfolio_"+latest_date_f+".xlsx") 
    # ss.write_advanced_as_df(wb,multiasset_monthly_upload_bucket,multiasset_monthly_upload_path,f'ETF_pre_portfolio_{latest_date_f}.xlsx')
    # #####versioning#######
    # ss.write_advanced_as_df(wb,multiasset_monthly_versioning_bucket,multiasset_monthly_versioning_path,f'ETF_pre_portfolio_{latest_date_f}.xlsx')
    return err_df
                            
if __name__ == "__main__":
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")
    
    # run_date = (datetime.today()-BDay(0)).strftime("%Y-%m-%d")
    # run_date = '2025-04-07'
    try:
        etf_final_df = get_delivery_file(run_date)
        print(f"Monthly Multiasset Delivery file generated for {run_date}")
        receiver = config.get('MONTHLY_MULTIASSET','monthly_multiasset_email_receiver_success')
        send_email(f'Monthly Multiasset Preportfolio : Run COMPLETED successfully for {run_date}', receiver, etf_final_df)
    except Exception as e:
        print(f'Monthly Multiasset Preportfolio : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config.get('MONTHLY_MULTIASSET','monthly_multiasset_email_failed')
        send_email( f'Monthly Multiasset Preportfolio : Run FAILED for run date {run_date}', receiver, traceback.format_exc())
        print(e)
        raise