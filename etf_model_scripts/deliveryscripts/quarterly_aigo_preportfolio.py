from helpers import  *
s3conn = s3_config()
config = configparser.ConfigParser()
config.read('config.properties')

# 1
def run_preportfolio(run_date):
    
    local_path = config.get('QUARTERLY_AIGO', 'quarterly_aigo_bucket_1') + config.get('QUARTERLY_AIGO','quarterly_aigo_path_1') + config.get('QUARTERLY_AIGO', "quarterly_aigo_path_2")

    # use run-date
    day_diff = 1
    latest_date= (pd.to_datetime(run_date)- BDay(day_diff)).strftime("%Y-%m-%d")
    latest_date_c = (pd.to_datetime(run_date)- BDay(day_diff))
    latest_date_f= latest_date_c.strftime("%m-%d-%Y")
    print(f'Last business day for this run : {latest_date}')
    
    bucketname = config.get('QUARTERLY_AIGO', "quarterly_aigo_bucket_1")
    foldername = config.get('QUARTERLY_AIGO', "quarterly_aigo_path_3")
    error_rate_filename = config.get('QUARTERLY_AIGO', "quarterly_aigo_file_1") + latest_date + ".csv"

    err_df= s3conn.read_as_dataframe(bucketname, foldername + error_rate_filename)
    err_df = err_df.rename(columns={'tic':'Equity'})

    t_df  = s3conn.read_as_dataframe(config.get('QUARTERLY_AIGO', 'quarterly_aigo_bucket_1'), config.get('QUARTERLY_AIGO','quarterly_aigo_path_1') + "etf.csv")
    
    sdate = (latest_date_c - relativedelta(years=2)).strftime("%Y-%m-%d")
    edate = latest_date
    close_df = pd.DataFrame()
    for i in range(len(t_df)):
        isin = t_df.iloc[i]['isin']
        ccode = t_df.iloc[i]['country_code']
        df_c = close_price(config.get('Inhouse_API', 'inhouse_api_url_by_isin'), isin, sdate, edate)
        #df_c = df_c.rename(columns={isin:tic})
        if i == 0:
            close_df = df_c.copy()
        else:    
            close_df = pd.merge(close_df,df_c,on='date',how='left')

    close_df.set_index('date', inplace=True)
    tmp_df = close_df.pct_change(periods=22)
    tmp_df = tmp_df.describe().T
    tmp_df.index.names = ['isin']     
    tmp_df = tmp_df.reset_index()
    
    err_df = pd.merge(err_df,t_df[['Ticker','isin']].rename(columns={'Ticker':'Equity'}),on='Equity',how='left')
    err_df = pd.merge(err_df,tmp_df[['isin','min','max']],on='isin',how='left')
    err_df = err_df.rename(columns={'quarterly_close_change':'actual_change','quarterly_predictions':'ER'})
    err_df['ER'] = err_df['ER']/100
    err_df['actual_change'] = err_df['actual_change']/100
    err_df['Avg'] = err_df['ER']
    
    err_df = err_df.assign(er_high=err_df.apply(lambda row: er_high_func(row),axis=1))
    err_df = err_df.assign(er_low=err_df.apply(lambda row: er_low_func(row),axis=1))
    err_df = err_df.assign(er_high=err_df.apply(lambda row: row['ER'] if row['er_high'] <= row['ER'] else row['er_high'],axis=1))
    err_df = err_df.assign(er_low=err_df.apply(lambda row: row['ER'] if row['er_low'] >= row['ER'] else row['er_low'],axis=1))
    err_df['ER4'] = err_df['ER'] / ((err_df['max'] - err_df['min']) * (1 - err_df['confidence_score']))
    
    err_df = err_df[['Equity','actual_change','ER','Avg','confidence_score','accuracy','close_price','min','max','er_high','er_low','ER4']]
    err_df = err_df.sort_values(by='Equity')
    
    wb = Workbook()
    ws = wb.active
    for row in dataframe_to_rows(err_df, index=False, header=True):
        ws.append(row)
    actual_loc = err_df.columns.get_loc('actual_change')
    actual_loc = get_column_letter(actual_loc + 1) 
    er_loc = err_df.columns.get_loc('ER')
    er_loc = get_column_letter(er_loc + 1) 
    avg_loc = err_df.columns.get_loc('Avg')
    avg_loc = get_column_letter(avg_loc + 1) 
    #mod_loc = etf_format.columns.get_loc('mod_er')
    #mod_loc = get_column_letter(mod_loc + 1) 
    print(actual_loc,':------:',er_loc,":----:",avg_loc,":-----:")
    f = [actual_loc,er_loc,avg_loc]
    for col_v in f:
        number_format = '0.00%'
        for cell in ws[col_v]:
                cell.number_format = number_format

    upload_excel(s3conn, wb, config.get('QUARTERLY_AIGO', "quarterly_aigo_bucket_2"), f"{config.get('QUARTERLY_AIGO', "quarterly_aigo_path_4")}/{config.get('QUARTERLY_AIGO', "quarterly_aigo_file_2")}{latest_date_f}.xlsx")

    versioned_s3_bucket = config.get('QUARTERLY_AIGO','quarterly_aigo_all_versioned_portfolio_bucket')
    versioned_s3_path   = config.get('QUARTERLY_AIGO','quarterly_aigo_all_versioned_portfolio_path')
    s3conn.write_advanced_as_df(err_df, versioned_s3_bucket, versioned_s3_path.format(latest_date = latest_date))
    
    print(latest_date_f)
    return err_df

# 2
def prepare_body_html(body):
    if isinstance(body, pd.DataFrame):
        body = body.to_html(index=False, border=1)
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html

# 3
def send_email(subject , receiver, body = None):
    gmail_creds_bucket = config.get('S3_files','gmail_creds_bucket')
    gmail_creds_path =config.get('S3_files','gmail_creds_path')
    sender = config.get('S3_files','gmail_sender')
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)
    
if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    try:
        etf_final_df = run_preportfolio(run_date)
        print('Quarterly Aigo Preportfolio : Run COMPLETED successfully')
        receiver = config.get('QUARTERLY_AIGO','quarterly_aigo_email_receiver_success')
        send_email(f'Quarterly Aigo Preportfolio : Run COMPLETED successfully for run date {run_date}',receiver, etf_final_df)
        
    except Exception as e:
        print(f'Quarterly Aigo Preportfolio : Run FAILED for run date {run_date} due to the Error : {e}')
        receiver = config.get('QUARTERLY_AIGO','quarterly_aigo_email_receiver_failed')
        send_email( f'Quarterly Aigo Preportfolio : Run FAILED for run date {run_date}', receiver, traceback.format_exc())
        raise
        