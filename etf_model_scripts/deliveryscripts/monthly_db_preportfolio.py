from helpers import  *
s3conn = s3_config()
config = configparser.ConfigParser()
config.read('config.properties')

def prepare_body_html(body):
    if isinstance(body, pd.DataFrame):
        body = body.to_html(index=False, border=1)
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html

def send_email(subject ,receiver, body = None):
    gmail_creds_bucket = config.get('S3_files','gmail_creds_bucket')
    gmail_creds_path =config.get('S3_files','gmail_creds_path')
    sender = config.get('S3_files','gmail_sender')
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)
    
def run_preportfolio(run_date):
    ## getting last business date
    day_diff = 1
    latest_date= (pd.to_datetime(run_date)- BDay(day_diff)).strftime("%Y-%m-%d")
    latest_date_date_time = (pd.to_datetime(run_date) - BDay(day_diff))
    print(latest_date , latest_date_date_time)
    print(f'Last business day for this run : {latest_date}')

    # get metrics file
    metrics_bucket = config.get('MONTHLY_DB','monthly_db_metrics_bucket')
    metrics_path  = config.get('MONTHLY_DB','monthly_db_metrics_path')
    DB_conf_df = s3conn.read_as_dataframe(metrics_bucket,metrics_path.format(latest_date = latest_date))
    DB_conf_df = DB_conf_df[['equity','close_price','accuracy']]
    DB_conf_df = DB_conf_df.rename(columns={'equity':'isin'})
    print('getting metrics file done')
    print(DB_conf_df)


    # get predictions file
    prediction_bucket = config.get('MONTHLY_DB','monthly_db_prediction_bucket')
    prediction_path  = config.get('MONTHLY_DB','monthly_db_prediction_path')
    DB_ER_df = s3conn.read_as_dataframe(prediction_bucket,prediction_path.format(latest_date = latest_date))
    DB_ER_df.rename(columns={'final_monthly_predictions':'ER'},inplace=True)
    DB_ER_df['ER'] = DB_ER_df['ER'] / 100
    print('getting er file done')
    print(DB_ER_df)

    ## merging the metrics and ER file
    df_er = DB_ER_df
    df_er.rename(columns={'etf':'isin'},inplace=True)
    df_conf = DB_conf_df
    df_er = pd.merge(df_er,df_conf,on='isin',how='left')
    print('merging of metrics and ER file done')
    print(df_er)

    ## getting the 2 years min & max
    api_url = config.get('Inhouse_API','inhouse_api_url_by_isin')
    start_date = (latest_date_date_time - relativedelta(years=2)).strftime("%Y-%m-%d")
    end_date = latest_date
    date_list = pd.date_range(start=start_date,end=end_date,freq='B').strftime("%Y-%m-%d").tolist()
    df_f = pd.DataFrame(date_list,columns=['date'])
    df = df_er
    ll = df['isin'].tolist()
    for i in ll:
        isin = i
        df1 = close_price(api_url,isin,start_date,end_date)
        df_f = pd.merge(df_f,df1,on='date',how='left')
    df_f = df_f.ffill()
    df_f = df_f.set_index('date')
    df_c = df_f.pct_change(periods=22)
    min_max = df_c.describe().T
    min_max.index.names = ['isin']     
    min_max.reset_index(inplace = True)
    print('getting 2year min & max done')
    print(min_max)

    ## merging minmax and er
    df_er = df_er
    df_min_max = min_max
    df_er = pd.merge(df_er,df_min_max[['isin','min','max']],on='isin',how='left')
    print(df_er)

    ## calculating ER4 and er_high & er_low
    df_er = df_er
    df_er['ER4'] = df_er['ER'] / ((df_er['max'] - df_er['min']) * (1 - df_er['confidence_score']))
    df_er = df_er.assign(er_high=df_er.apply(lambda row: er_high_func(row),axis=1))
    df_er = df_er.assign(er_low=df_er.apply(lambda row: er_low_func(row),axis=1))
    df_er = df_er.assign(er_high=df_er.apply(lambda row: row['ER'] if row['er_high'] <= row['ER'] else row['er_high'],axis=1))
    df_er = df_er.assign(er_low=df_er.apply(lambda row: row['ER'] if row['er_low'] >= row['ER'] else row['er_low'],axis=1))
    print('ER4, er_high and er_low done')
    print(df_er)

    ## writting to local s3
    local_s3_bucket = config.get('MONTHLY_DB','monthly_db_local_portfolio_bucket')
    local_s3_path = config.get('MONTHLY_DB','monthly_db_local_portfolio_path')
    s3conn.write_advanced_as_df(df_er,local_s3_bucket,local_s3_path.format(latest_date = latest_date))
    print('upload to local s3 done')

    ## writting to versioned s3
    versioned_s3_bucket = config.get('MONTHLY_DB','monthly_db_versioned_portfolio_bucket')
    versioned_s3_path = config.get('MONTHLY_DB','monthly_db_versioned_portfolio_path')
    s3conn.write_advanced_as_df(df_er,versioned_s3_bucket,versioned_s3_path.format(latest_date = latest_date))
    print('upload to versioned s3 done')

    return df_er

if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    
    try:
        etf_final_df = run_preportfolio(run_date)
        print(f'Monthly DB Preportfolio : Run COMPLETED successfully for run date {run_date}')
        receiver = config.get('MONTHLY_DB','db_email_receiver_success')
        send_email(f'Monthly DB Preportfolio : Run COMPLETED successfully for run date {run_date}', receiver, etf_final_df)
    
    except Exception as e:
        print(f'Monthly DB Preportfolio : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config.get('MONTHLY_DB','db_email_receiver_failed')
        send_email( f'Monthly DB Preportfolio : Run FAILED for run date {run_date}', receiver, traceback.format_exc())
        raise