from helpers import *
# import xlsxwriter
ss = s3_config()
config = configparser.ConfigParser()
config.read('config.properties')

def prepare_body_html(body,df):
    if df is not None and not df.empty:
        receiver = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_passed')
        html  = f'<html><title></title><body>Hi,<br><p><b>BNP files have been generated in following paths.</b> <br> BNP delivery file path : <a href="https://s3.console.aws.amazon.com/s3/buckets/etf-predictions?region=us-east-1&bucketType=general&prefix=Monthly/new_BNP_Paribas/portfolio_construction_smoothing/&showversions=false">Delivery files</a><br>BNP internal check file path :<a href="https://s3.console.aws.amazon.com/s3/buckets/etf-predictions?region=us-east-1&bucketType=general&prefix=Monthly/new_BNP_Paribas/BNP-checks%28internal+file%29/&showversions=false">Internal Check files</a></p>Please review the file before 7:30 IST.<br>Todays delivery file will look like:<br>{df.to_html()}<br>Thanks</body></html>'
    else:
        receiver = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_failed')
        html = f"""\
                    <html>
                      <head></head>
                      <body>
                       {body}
                      </body>
                    </html>
                    """
    receiver = [email.strip() for email in receiver.split(',')]
    return html,receiver

def send_email(subject , body = None,df = None):
    gmail_creds_bucket = config.get('S3_files','gmail_creds_bucket')
    gmail_creds_path =config.get('S3_files','gmail_creds_path')
    sender = config.get('S3_files','gmail_sender')
    # receiver_passed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_passed')
    # receiver_failed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_failed')
    # receiver = [email.strip() for email in receiver.split(',')]
    body_html,receiver = prepare_body_html(body,df=df)
    SendMessage(gmail_creds_bucket , gmail_creds_path, ss, sender, receiver , subject, body_html)


def upload_s3file(file_name,bucket,object_name):
    s3_client = boto3.client('s3',aws_access_key_id=ss._key_id,
         aws_secret_access_key=ss._secret)
    response = s3_client.upload_file(file_name, bucket, object_name) 

def upload_obj(bucket,object_path,obj):
    s3_client = boto3.client('s3',aws_access_key_id=ss._key_id,
         aws_secret_access_key=ss._secret)
    response = s3_client.upload_fileobj(obj, bucket, object_path) 
    
def process_ER_alerts(df):
    while df["Trigger ER Alert"].any():  # Continue until all alerts are 0
        for index, row in df.iterrows():
            if row["Trigger ER Alert"] == 1:
                # Step 1: Average the ER
                new_ER = (row["ER"] + row["ER_yes"]) / 2
                
                # Step 2: Calculate ER change
                ER_change = new_ER - row["ER_yes"]
                
                # Step 3: Check if |ER_change| > ER_std to trigger alert again
                if abs(ER_change) > row["ER_std"]:
                    df.at[index, "ER"] = new_ER  # Update the ER value
                    df.at[index, "ER_change"] = ER_change  # Update ER_change
                    df.at[index, "Trigger ER Alert"] = 1  # Keep the trigger alert on for this row
                else:
                    df.at[index, "ER"] = new_ER  # Finalize the new ER for this ticker
                    df.at[index, "ER_change"] = ER_change  # Finalize ER_change
                    df.at[index, "Trigger ER Alert"] = 0  # Set the trigger alert to 0

    return df

def calc_ER_high_low(df,date):
    df.set_index('Ticker',inplace=True)
    tickers = ['BNPIFUS','BNPIFEM','BNPIFEU','BNPIFJP','BNPIFE10','BNPIFU10','BNPIG0GC','BNPIFCN','BNPIFJ10','BNPIDSBU']
    for ticker in tickers:
        api_url = config.get('Inhouse_API','inhouse_api_url_by_isin')
        sdate = (pd.to_datetime(date)-BDay(260*3)).strftime("%Y-%m-%d")
        bnp_daily_data = close_price(api_url,ticker,sdate,date)
        bnp_daily_data['date'] = pd.to_datetime(bnp_daily_data['date'])
        bnp_daily_data.sort_values(by='date', inplace=True)
        bnp_daily_data = bnp_daily_data.set_index('date')
        bnp_daily_data['monthly_close_change'] = bnp_daily_data[ticker].pct_change(22)*100
        bnp_daily_data['yearhigh'] = bnp_daily_data['monthly_close_change'].rolling(window=504, min_periods=504).max()
        bnp_daily_data['yearlow'] = bnp_daily_data['monthly_close_change'].rolling(window=504, min_periods=504).min()
        df.loc[ticker,['max']]=bnp_daily_data['yearhigh'].values[-1]/100
        df.loc[ticker,['min']]=bnp_daily_data['yearlow'].values[-1]/100
    df = df.assign(er_high=df.apply(lambda row: er_high_func(row),axis=1))
    df = df.assign(er_low=df.apply(lambda row: er_low_func(row),axis=1))
    df = df.assign(er_high=df.apply(lambda row: row['ER'] if row['er_high'] <= row['ER'] else row['er_high'],axis=1))
    df = df.assign(er_low=df.apply(lambda row: row['ER'] if row['er_low'] >= row['ER'] else row['er_low'],axis=1))
    df.reset_index(inplace=True)
    return df

def get_delivery_file(run_date):
    print(run_date)
    date = (pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
    bnp_main_bucket = config.get('MONTHLY_BNP','bnp_main_bucket')
    bnp_platform_bucket = config.get('MONTHLY_BNP','bnp_platform_bucket')
    bnp_versioning_bucket = config.get('MONTHLY_BNP','bnp_versioning_bucket')
    bnp_delivery_file_path_yest = config.get('MONTHLY_BNP','bnp_delivery_file_path').format(date=date)
    bnp_delivery_file_path_today = config.get('MONTHLY_BNP','bnp_delivery_file_path').format(date=run_date)
    bnp_delivery_folder_path = config.get('MONTHLY_BNP','bnp_delivery_folder_path')
    best_pipeline_path = config.get('MONTHLY_BNP','best_pipeline_path').format(date=date)
    bnp_platform_file_path = config.get('MONTHLY_BNP','bnp_platform_file_path').format(date=date)
    bnp_checks_file_path = config.get('MONTHLY_BNP','bnp_checks_file_path').format(date=run_date)

    try:
        bnp_yes_df = ss.read_as_dataframe(bnp_main_bucket,bnp_delivery_file_path_yest)
    except:
        bnp_yes_df = ss.read_as_dataframe(bnp_main_bucket,ss.get_latest_obj(bnp_main_bucket,bnp_delivery_folder_path)['Key'])
    bnp_today_df = pd.DataFrame(bnp_yes_df['Ticker'].tolist(),columns=['Ticker'])
    bnp_today_df.set_index('Ticker',inplace=True)
    bnp_final_df= ss.read_as_dataframe(bnp_main_bucket,best_pipeline_path)
    bnp_final_df.rename(columns={'etf':'Ticker','final_monthly_predictions':'ER'},inplace=True)
    bnp_final_df = bnp_final_df[['Ticker','ER','confidence_score']]
    bnp_final_df['ER'] = bnp_final_df['ER']/100
    bnp_final_df.set_index('Ticker',inplace=True)
    tickers = ['BNPIFUS','BNPIFEM','BNPIFEU','BNPIFJP','BNPIFE10','BNPIFU10','BNPIG0GC','BNPIFCN','BNPIFJ10','BNPIDSBU']
    for tic in tickers:
        bnp_today_df.loc[tic,'ER'] = bnp_final_df.loc[tic,'ER']
        bnp_today_df.loc[tic,'confidence_score'] = bnp_final_df.loc[tic,'confidence_score']
    bnp_today_df.reset_index(inplace=True)

    dupp_df = bnp_today_df.copy()
    bnp_checks_df = dupp_df
    bnp_checks_df['ER_yes'] = bnp_yes_df['ER']
    bnp_checks_df['ER_change'] = bnp_checks_df[['ER', 'ER_yes']].apply(lambda x: ((x[0] - x[1])), axis=1)
    
    bnp_checks_df.set_index('Ticker',inplace=True)
    st_dev = {}
    equities = ['BNPIFUS','BNPIFEM','BNPIFEU','BNPIFJP','BNPIFCN']
    for eq in equities:
        tic = eq
        bnp_historical_pred_data_path = config.get('MONTHLY_BNP','bnp_historical_pred_data_path').format(tic=tic)
        print(bnp_historical_pred_data_path)
        print(tic)
        df = ss.read_as_dataframe(bnp_main_bucket,bnp_historical_pred_data_path)
        df=df[df['date']<=date]
        df['smoothing_ER'] = df['monthly_predictions'].rolling(5).apply(lambda x:(x.iloc[4]*0.6+x.iloc[3]*0.1+x.iloc[2]*0.1+x.iloc[1]*0.1+x.iloc[0]*0.1)/100)
        bnp_checks_df.loc[tic,['ER_std']]=df['smoothing_ER'].std()
        
    bnp_checks_df.reset_index(inplace=True)
    bnp_checks_df['operation'] = [[bnp_checks_df.loc[i, 'ER_change'], bnp_checks_df.loc[i, 'ER_std']] for i in range(len(bnp_checks_df))]
    bnp_checks_df['Trigger ER Alert'] = bnp_checks_df['operation'].apply(lambda x:1 if abs(x[0])-x[1] >=0 else 0)
    print(bnp_checks_df)
    bnp_checks_df = process_ER_alerts(bnp_checks_df)
    bnp_today_df['ER'] = bnp_checks_df['ER']
    bnp_trigger_df = calc_ER_high_low(bnp_checks_df,date)
    bnp_platform_df = bnp_trigger_df
    bnp_platform_df.reset_index(inplace=True)
    bnp_platform_df['ER4_operation'] = [[bnp_platform_df.loc[i, 'ER'], bnp_platform_df.loc[i, 'max'], bnp_platform_df.loc[i, 'min'],bnp_platform_df.loc[i, 'confidence_score']] for i in range(len(bnp_platform_df))]
    bnp_platform_df['ER_4'] = bnp_platform_df['ER4_operation'].apply(lambda x: x[0]/((x[1]-x[2])*x[3]))
    bnp_platform_df.drop(['max','min','ER_yes','operation'],axis=1,inplace=True)
    bnp_platform_df.rename(columns={'Trigger ER Alert':'trigger','er_high':'ER_high','er_low':'ER_low'},inplace=True)
    bnp_platform_df = bnp_platform_df[['Ticker','ER','ER_high','ER_low','confidence_score','trigger','ER_change','ER_4'
    ]]
    bnp_today_df  = bnp_trigger_df[['Ticker','ER','ER_high','ER_low','confidence_score']]
    bnp_checks_df.drop(columns=['operation'],inplace=True)
    bnp_checks_df.reset_index(inplace=True)
    print(bnp_today_df)
    # platform_test_bucket = config.get('MONTHLY_BNP','platform_test_bucket')
    # bnp_versioning_test_bucket = config.get('MONTHLY_BNP','bnp_versioning_test_bucket')
    # bnp_test_main_bucket = config.get('MONTHLY_BNP','bnp_test_main_bucket')
    bnp_versioning_path = config.get('MONTHLY_BNP','bnp_versioning_path').format(date=date)
    ss.write_advanced_as_df(bnp_today_df,bnp_main_bucket,bnp_delivery_file_path_today)
    ss.write_advanced_as_df(bnp_platform_df,bnp_platform_bucket,bnp_platform_file_path)
    ss.write_advanced_as_df(bnp_today_df,bnp_versioning_bucket,bnp_versioning_path)

    excel_buffer = BytesIO()
    with pd.ExcelWriter(excel_buffer, engine='xlsxwriter') as writer:
        bnp_yes_df.to_excel(writer, sheet_name=date, index=False)
        bnp_today_df.to_excel(writer, sheet_name=run_date, index=False)
        bnp_checks_df.to_excel(writer, sheet_name='BNP-Checks', index=False)
    excel_buffer.seek(0)
    upload_obj(bnp_main_bucket,bnp_checks_file_path,excel_buffer)
    print(bnp_today_df)
    return bnp_today_df
    
if __name__ == "__main__":
    bnp_tickers = ['BNPIFUS','BNPIFEM','BNPIFEU','BNPIFJP','BNPIFCN','BNPIFE10','BNPIFU10','BNPIFJ10','BNPIG0GC','BNPIDSBU']
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")
    
    # run_date = (datetime.today()-BDay(0)).strftime("%Y-%m-%d")
    # run_date = '2025-04-07'
    try:
        delivery_df = get_delivery_file(run_date)
        print(f"Monthly BNP Paribas Delivery file generated for {run_date}")
        send_email(f'Monthly BNP Paribas Preportfolio : Run COMPLETED successfully for {run_date}',df = delivery_df)
    except Exception as e:
        print(f'Monthly BNP Paribas Preportfolio : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        send_email( f'Monthly BNP Paribas Preportfolio : Run FAILED for run date {run_date}', traceback.format_exc())
        print(e)
        raise
