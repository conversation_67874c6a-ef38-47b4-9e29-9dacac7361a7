from helpers import  *
s3conn = s3_config()
config = configparser.ConfigParser()
config.read('config.properties')

def er_func(row):
    if row['ER'] > row['max']:
        return row['max']
    elif row['ER'] < row['min']:
        return row['min']
    else:
        return row['ER']  

def prepare_body_html(body):
    if isinstance(body, pd.DataFrame):
        body = body.to_html(index=False, border=1)
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html

def send_email(subject , receiver, body = None):
    gmail_creds_bucket = config.get('S3_files','gmail_creds_bucket')
    gmail_creds_path =config.get('S3_files','gmail_creds_path')
    sender = config.get('S3_files','gmail_sender')
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)
        
def run_preportfolio(run_date):
    ## getting last business date
    latest_date= pd.to_datetime(run_date)- BDay(1)
    previous_month = (latest_date  + relativedelta(months=-1)).strftime("%Y-%m-%d")
    current_date = latest_date.strftime("%Y-%m-%d")
    date_range = pd.date_range(start=previous_month,end=current_date,freq='W-THU').strftime("%Y-%m-%d").tolist()
    file_date = date_range[0]
    print(latest_date , date_range[0])
    print(f'Last business day for this run : {latest_date}')

    ### getting the 2 years min & max
    aigo_local_bucket = config.get('MONTHLY_AIGO','monthly_aigo_local_path_bucket')
    aigo_local_path = config.get('MONTHLY_AIGO','monthly_aigo_local_path_directory')
    df_t = s3conn.read_as_dataframe(aigo_local_bucket, aigo_local_path + 'etf.csv')
    start_date = (latest_date - relativedelta(years=2)).strftime("%Y-%m-%d")
    end_date = latest_date.strftime("%Y-%m-%d")
    df_close = pd.DataFrame()
    api_url = config.get('Inhouse_API','inhouse_api_url_by_isin')
    for i in range(len(df_t)):
        tic = df_t.iloc[i]['Ticker']
        isin = df_t.iloc[i]['isin']
        ccode = df_t.iloc[i]['country_code']
        pred_file = df_t.iloc[i]['tic_cors'] 
        df_c = close_price(api_url,isin,start_date,end_date)
        df_c = df_c.rename(columns={isin:tic})
        if i == 0:
            df_close = df_c.copy()
        else:    
            df_close = pd.merge(df_close,df_c,on='date',how='left')       
    df_close.set_index('date', inplace=True)
    min_max = df_close.pct_change(periods=22)
    min_max = min_max.describe().T
    min_max.index.names = ['Ticker']     
    min_max.reset_index(inplace = True)
    print('getting 2year min & max done')
    print(min_max)

    
    # get metrics file aigo
    aigo_metrics_bucket = config.get('MONTHLY_AIGO','monthly_aigo_metrics_bucket')
    aigo_metrics_path  =config.get('MONTHLY_AIGO','monthly_aigo_metrics_path')
    formatted_latest_date = latest_date.strftime("%Y-%m-%d")
    aigo_conf_df = s3conn.read_as_dataframe(aigo_metrics_bucket, aigo_metrics_path.format(latest_date = formatted_latest_date))
    aigo_conf_df = aigo_conf_df[['date','equity','weekly_close_change','monthly_predictions','accuracy','close_price','monthly_close_change']]
    aigo_conf_df.rename(columns={'weekly_close_change':'actual_change','monthly_predictions':'ER','equity':'Equity'},inplace=True)
    aigo_conf_df['Equity'] = aigo_conf_df['Equity'].str.upper()
    print('getting aigo metrics file done')
    print(aigo_conf_df)
    
    # get metrics file sector
    sector_metrics_bucket = config.get('MONTHLY_AIGO','monthly_sector_metrics_bucket')
    sector_metrics_path  =config.get('MONTHLY_AIGO','monthly_sector_metrics_path')
    formatted_latest_date = latest_date.strftime("%Y-%m-%d")
    sector_conf_df = s3conn.read_as_dataframe(sector_metrics_bucket, sector_metrics_path.format(latest_date = formatted_latest_date))
    sector_conf_df = sector_conf_df[['date','equity','weekly_close_change','monthly_predictions','accuracy','close_price','monthly_close_change']]
    sector_conf_df.rename(columns={'weekly_close_change':'actual_change','monthly_predictions':'ER','equity':'Equity'},inplace=True)
    sector_conf_df['Equity'] = sector_conf_df['Equity'].str.upper()
    print('getting sector metrics file done')
    print(sector_conf_df)

    ## Formatting aigo with template
    aigo_template_bucket = config.get('MONTHLY_AIGO','monthly_aigo_local_path_bucket')
    aigo_template_path = config.get('MONTHLY_AIGO','monthly_aigo_local_path_directory') +'ETF_pre_portfolio_format.xlsx'
    aigo_template_df = s3conn.read_as_dataframe(aigo_template_bucket, aigo_template_path)
    aigo_format_df = pd.merge(aigo_template_df, aigo_conf_df, on='Equity', how='left')
    print('aigo formatting done')
    print(aigo_format_df)
    
    ## Formatting sector with template
    sector_template_bucket = config.get('MONTHLY_AIGO','monthly_aigo_local_path_bucket')
    sector_template_path = config.get('MONTHLY_AIGO','monthly_aigo_local_path_directory') +'ETF_sector_pre_portfolio_format.xlsx'
    sector_template_df = s3conn.read_as_dataframe(sector_template_bucket, sector_template_path)
    sector_format_df = pd.merge(sector_template_df, sector_conf_df, on='Equity', how='left')
    print('sector formatting done')
    print(sector_format_df)
    
    ## concatenating the formatted part
    etf_format_df = pd.concat([aigo_format_df,sector_format_df],ignore_index=True)
    print('etf format concatenation done')
    print(etf_format_df)


    # get aigo predictions file
    aigo_prediction_bucket = config.get('MONTHLY_AIGO','monthly_aigo_prediction_bucket')
    aigo_prediction_path  = config.get('MONTHLY_AIGO','monthly_aigo_prediction_path')
    formatted_latest_date = latest_date.strftime("%Y-%m-%d")
    aigo_ER_df = s3conn.read_as_dataframe(aigo_prediction_bucket, aigo_prediction_path.format(latest_date = formatted_latest_date))
    aigo_ER_df.replace({'MQFIUSTU':'treasury_2yr','MQFIUSTY':'treasury_10yr','MQFIUSUS':'treasury_30yr','HSMETYSN':'metys',
                        'HSMETYV3':'metysv3'}, inplace=True)
    aigo_ER_df.rename(columns={'etf':'Equity'},inplace=True)
    aigo_ER_df['Equity'] = aigo_ER_df['Equity'].str.upper()
    print('getting aigo er file done')
    print(aigo_ER_df)
    
    # get sector predictions file
    sector_prediction_bucket = config.get('MONTHLY_AIGO','monthly_sector_prediction_bucket')
    sector_prediction_path  = config.get('MONTHLY_AIGO','monthly_sector_prediction_path')
    formatted_latest_date = latest_date.strftime("%Y-%m-%d")
    sector_ER_df = s3conn.read_as_dataframe(sector_prediction_bucket, sector_prediction_path.format(latest_date = formatted_latest_date))
    sector_ER_df.rename(columns={'etf':'Equity'},inplace=True)
    sector_ER_df['Equity'] = sector_ER_df['Equity'].str.upper()
    print('getting sector er file done')
    print(sector_ER_df)
    
    ## concatenating the prediction part
    er_df = pd.concat([aigo_ER_df,sector_ER_df],ignore_index=True)
    print('er concatenation done')
    print(er_df)

    ## Merging metrics & predictions
    etf_format_df = pd.merge(etf_format_df,er_df[['Equity','final_monthly_predictions','confidence_score']],on='Equity',how='left')
    etf_format_df.rename(columns={'final_monthly_predictions':'Avg','date':'Date'},inplace=True)
    
    etf_format_df = etf_format_df[['Date','Equity','actual_change','ER','Avg','confidence_score','accuracy','close_price']]    
    etf_format_df['actual_change'] = etf_format_df['actual_change'] / 100
    etf_format_df['ER'] = etf_format_df['ER'] / 100
    etf_format_df['Avg'] = etf_format_df['Avg']/100
    
    etf_format_df['Date'] = latest_date.strftime('%Y/%m/%d')
    
    df_min_max = min_max
    df_min_max.rename(columns={'Ticker':'Equity'},inplace=True)
    etf_format_df = pd.merge(etf_format_df,df_min_max[['Equity','min','max']],on='Equity',how='left')
    etf_format_df.rename(columns={'ER':'ER_O'},inplace=True)
    etf_format_df['ER'] = etf_format_df['Avg']
    etf_format_df = etf_format_df.assign(ER=etf_format_df.apply(lambda row: er_func(row),axis=1))
    etf_format_df = etf_format_df.assign(er_high=etf_format_df.apply(lambda row: er_high_func(row),axis=1))
    etf_format_df = etf_format_df.assign(er_low=etf_format_df.apply(lambda row: er_low_func(row),axis=1))
    etf_format_df = etf_format_df.assign(er_high=etf_format_df.apply(lambda row: row['ER'] if row['er_high'] <= row['ER'] else row['er_high'],axis=1))
    etf_format_df = etf_format_df.assign(er_low=etf_format_df.apply(lambda row: row['ER'] if row['er_low'] >= row['ER'] else row['er_low'],axis=1))
    etf_format_df['ER4'] = etf_format_df['ER'] / ((1+(etf_format_df['max'] - etf_format_df['min'])) * (2 - etf_format_df['confidence_score']))
    etf_format_df.drop(columns=['ER'],inplace=True)
    etf_format_df.rename(columns={'ER_O':'ER'},inplace=True)
    print('etf final formatting done')
    print(etf_format_df)

    ## writting in excel file
    wb = Workbook()
    ws = wb.active
    for row in dataframe_to_rows(etf_format_df, index=False, header=True):
        ws.append(row)
    actual_loc = etf_format_df.columns.get_loc('actual_change')
    actual_loc = get_column_letter(actual_loc + 1) 
    er_loc = etf_format_df.columns.get_loc('ER')
    er_loc = get_column_letter(er_loc + 1) 
    avg_loc = etf_format_df.columns.get_loc('Avg')
    avg_loc = get_column_letter(avg_loc + 1) 
    #mod_loc = etf_format.columns.get_loc('mod_er')
    #mod_loc = get_column_letter(mod_loc + 1) 
    print(actual_loc,':------:',er_loc,":----:",avg_loc,":-----:")
    f = [actual_loc,er_loc,avg_loc]
    for col_v in f:
        number_format = '0.00%'
        for cell in ws[col_v]:
                cell.number_format = number_format

    ## writting to local s3
    local_s3_bucket = config.get('MONTHLY_AIGO','monthly_aigo_all_local_portfolio_bucket')
    local_s3_path = config.get('MONTHLY_AIGO','monthly_aigo_all_local_portfolio_path')
    upload_excel(s3conn, wb, local_s3_bucket, local_s3_path.format(latest_date = latest_date.strftime('%m_%d_%Y')))
    #s3conn.write_advanced_as_df(wb, local_s3_bucket, local_s3_path.format(latest_date = latest_date.strftime('%m_%d_%Y')))

    ## writting to versioned s3
    versioned_s3_bucket = config.get('MONTHLY_AIGO','monthly_aigo_all_versioned_portfolio_bucket')
    versioned_s3_path = config.get('MONTHLY_AIGO','monthly_aigo_all_versioned_portfolio_path')
    s3conn.write_advanced_as_df(etf_format_df, versioned_s3_bucket, versioned_s3_path.format(latest_date = latest_date.strftime('%Y-%m-%d')))

    return etf_format_df

if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting run for run date :{run_date}')
    
    try:
        etf_final_df = run_preportfolio(run_date)
        print(f'Monthly Aigo Preportfolio : Run COMPLETED successfully for run date {run_date}')
        receiver = config.get('MONTHLY_AIGO','aigo_email_receiver_success')
        send_email(f'Monthly Aigo Preportfolio : Run COMPLETED successfully for run date {run_date}', receiver, etf_final_df)
    
    except Exception as e:
        print(f'Monthly Aigo Preportfolio : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        receiver = config.get('MONTHLY_AIGO','aigo_email_receiver_failed')
        send_email( f'Monthly Aigo Preportfolio : Run FAILED for run date {run_date}',receiver,  traceback.format_exc())
        raise