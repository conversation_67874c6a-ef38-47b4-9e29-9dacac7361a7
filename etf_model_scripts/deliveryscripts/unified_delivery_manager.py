"""
ETF Model Scripts - Unified Delivery Manager

Refactored delivery script manager that replaces all duplicate delivery scripts
with a single, configurable, and maintainable solution.
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, List
import pandas as pd
from datetime import datetime
import configparser

# Import shared utilities package
from shared_utils import (
    load_config, create_model_logger, create_email_sender, create_error_handler,
    ErrorSeverity, ModelError, ConfigurationError, ErrorContext
)

# Import existing helpers
from helpers import *


class UnifiedDeliveryManager:
    """Unified delivery manager for all ETF model delivery scripts."""
    
    # Delivery configuration mapping
    DELIVERY_CONFIGS = {
        'monthly_aigo': {
            'model_type': 'aigo',
            'scheduler': 'monthly',
            'description': 'Monthly AIGO pre-portfolio delivery'
        },
        'monthly_bnp': {
            'model_type': 'bnp',
            'scheduler': 'monthly',
            'description': 'Monthly BNP pre-portfolio delivery'
        },
        'monthly_db': {
            'model_type': 'db',
            'scheduler': 'monthly',
            'description': 'Monthly DB pre-portfolio delivery'
        },
        'monthly_multiasset': {
            'model_type': 'multiasset',
            'scheduler': 'monthly',
            'description': 'Monthly Multi-Asset pre-portfolio delivery'
        },
        'quarterly_aigo': {
            'model_type': 'aigo',
            'scheduler': 'quarterly',
            'description': 'Quarterly AIGO pre-portfolio delivery'
        },
        'quarterly_multiasset': {
            'model_type': 'multiasset',
            'scheduler': 'quarterly',
            'description': 'Quarterly Multi-Asset pre-portfolio delivery'
        }
    }
    
    def __init__(self, delivery_type: str, config_path: str = 'config.properties'):
        """
        Initialize Unified Delivery Manager.
        
        Args:
            delivery_type: Type of delivery (e.g., 'monthly_aigo', 'quarterly_multiasset')
            config_path: Path to configuration file
        """
        if delivery_type not in self.DELIVERY_CONFIGS:
            raise ConfigurationError(f"Invalid delivery type: {delivery_type}")
        
        self.delivery_type = delivery_type
        self.delivery_config = self.DELIVERY_CONFIGS[delivery_type]
        
        # Setup script directory and configuration
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.config = self._load_properties_config(config_path)
        
        # Setup utilities
        from eq_common_utils.utils.config.s3_config import s3_config
        s3_client = s3_config()
        self.email_sender = create_email_sender(self._convert_config_for_email(), s3_client)
        
        # Setup logging
        self.logger = create_model_logger(
            model_name='etf_delivery',
            tag=self.delivery_config['model_type'],
            scheduler=self.delivery_config['scheduler'],
            run_date=datetime.now().strftime('%Y-%m-%d'),
            log_dir=os.path.join(self.script_dir, 'logs')
        )
        
        # Setup error handler
        self.error_handler = create_error_handler(self.logger.get_logger(), self.email_sender)
        
        self.logger.log_model_start({
            'delivery_type': delivery_type,
            'model_type': self.delivery_config['model_type'],
            'scheduler': self.delivery_config['scheduler']
        })
    
    def _load_properties_config(self, config_path: str) -> configparser.ConfigParser:
        """Load properties configuration file."""
        try:
            config = configparser.ConfigParser()
            full_path = os.path.join(self.script_dir, config_path)
            config.read(full_path)
            return config
            
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {e}")
    
    def _convert_config_for_email(self) -> Dict[str, Any]:
        """Convert properties config to format expected by EmailSender."""
        try:
            return {
                'Gmail_creds': {
                    'gmail_creds_bucket': self.config.get('S3_files', 'gmail_creds_bucket'),
                    'gmail_creds_path': self.config.get('S3_files', 'gmail_creds_path'),
                    'gmail_sender': self.config.get('S3_files', 'gmail_sender')
                }
            }
        except Exception as e:
            self.logger.warning(f"Could not convert config for email: {e}")
            return {}
    
    def send_notification(self, subject: str, body: str, recipients: Optional[str] = None) -> bool:
        """
        Send email notification.
        
        Args:
            subject: Email subject
            body: Email body
            recipients: Comma-separated recipient emails
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not recipients:
                # Use default recipients based on delivery type
                recipients = self._get_default_recipients()
            
            if not recipients:
                self.logger.warning("No recipients configured for notifications")
                return False
            
            success = self.email_sender.send_email(subject, recipients, body)
            
            if success:
                self.logger.info(f"Notification sent successfully: {subject}")
            else:
                self.logger.error(f"Failed to send notification: {subject}")
            
            return success
            
        except Exception as e:
            self.error_handler.handle_error(
                ModelError(f"Failed to send notification: {e}", ErrorSeverity.LOW),
                context={"subject": subject, "recipients": recipients}
            )
            return False
    
    def _get_default_recipients(self) -> str:
        """Get default recipients based on delivery type."""
        try:
            # Map delivery types to recipient configuration keys
            recipient_mapping = {
                'monthly_aigo': 'aigo_monthly_recipients',
                'monthly_bnp': 'bnp_monthly_recipients',
                'monthly_db': 'db_monthly_recipients',
                'monthly_multiasset': 'multiasset_monthly_recipients',
                'quarterly_aigo': 'aigo_quarterly_recipients',
                'quarterly_multiasset': 'multiasset_quarterly_recipients'
            }
            
            recipient_key = recipient_mapping.get(self.delivery_type)
            if recipient_key and self.config.has_option('Recipients', recipient_key):
                return self.config.get('Recipients', recipient_key)
            
            # Fallback to general recipients
            if self.config.has_option('Recipients', 'default_recipients'):
                return self.config.get('Recipients', 'default_recipients')
            
            return ""
            
        except Exception as e:
            self.logger.warning(f"Could not get default recipients: {e}")
            return ""
    
    def execute_delivery(self) -> bool:
        """
        Execute the delivery process.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(f"Starting delivery execution: {self.delivery_config['description']}")
            
            # Send start notification
            start_subject = f"ETF Delivery Started: {self.delivery_config['description']}"
            self.send_notification(start_subject, f"Delivery process initiated for {self.delivery_type}")
            
            # Execute delivery logic based on type
            success = self._execute_delivery_logic()
            
            # Send completion notification
            status = "COMPLETED" if success else "FAILED"
            end_subject = f"ETF Delivery {status}: {self.delivery_config['description']}"
            end_body = f"Delivery process {status.lower()} for {self.delivery_type}"
            
            self.send_notification(end_subject, end_body)
            
            # Log completion
            self.logger.log_model_end(success, {
                'delivery_type': self.delivery_type,
                'model_type': self.delivery_config['model_type'],
                'scheduler': self.delivery_config['scheduler']
            })
            
            return success
            
        except Exception as e:
            self.error_handler.handle_error(
                ModelError(f"Delivery execution failed: {e}", ErrorSeverity.HIGH),
                context={"delivery_type": self.delivery_type}
            )
            return False
    
    def _execute_delivery_logic(self) -> bool:
        """Execute the specific delivery logic based on delivery type."""
        try:
            # This is where the specific delivery logic would be implemented
            # For now, we'll use a placeholder that calls the appropriate helper functions
            
            if self.delivery_type == 'monthly_aigo':
                return self._execute_monthly_aigo_delivery()
            elif self.delivery_type == 'monthly_bnp':
                return self._execute_monthly_bnp_delivery()
            elif self.delivery_type == 'monthly_db':
                return self._execute_monthly_db_delivery()
            elif self.delivery_type == 'monthly_multiasset':
                return self._execute_monthly_multiasset_delivery()
            elif self.delivery_type == 'quarterly_aigo':
                return self._execute_quarterly_aigo_delivery()
            elif self.delivery_type == 'quarterly_multiasset':
                return self._execute_quarterly_multiasset_delivery()
            else:
                raise ModelError(f"Unknown delivery type: {self.delivery_type}", ErrorSeverity.HIGH)
                
        except Exception as e:
            self.logger.error(f"Delivery logic execution failed: {e}")
            return False
    
    def _execute_monthly_aigo_delivery(self) -> bool:
        """Execute monthly AIGO delivery logic."""
        # Placeholder - implement actual logic from monthly_aigo_preportfolio.py
        self.logger.info("Executing monthly AIGO delivery logic")
        return True
    
    def _execute_monthly_bnp_delivery(self) -> bool:
        """Execute monthly BNP delivery logic."""
        # Placeholder - implement actual logic from monthly_bnp_preportfolio.py
        self.logger.info("Executing monthly BNP delivery logic")
        return True
    
    def _execute_monthly_db_delivery(self) -> bool:
        """Execute monthly DB delivery logic."""
        # Placeholder - implement actual logic from monthly_db_preportfolio.py
        self.logger.info("Executing monthly DB delivery logic")
        return True
    
    def _execute_monthly_multiasset_delivery(self) -> bool:
        """Execute monthly multi-asset delivery logic."""
        # Placeholder - implement actual logic from monthly_multiasset_preportfolio.py
        self.logger.info("Executing monthly multi-asset delivery logic")
        return True
    
    def _execute_quarterly_aigo_delivery(self) -> bool:
        """Execute quarterly AIGO delivery logic."""
        # Placeholder - implement actual logic from quarterly_aigo_preportfolio.py
        self.logger.info("Executing quarterly AIGO delivery logic")
        return True
    
    def _execute_quarterly_multiasset_delivery(self) -> bool:
        """Execute quarterly multi-asset delivery logic."""
        # Placeholder - implement actual logic from quarterly_multiasset_preportfolio.py
        self.logger.info("Executing quarterly multi-asset delivery logic")
        return True


def parse_arguments() -> str:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        prog='Unified Delivery Manager',
        description='Unified delivery manager for ETF model delivery scripts'
    )
    parser.add_argument(
        'delivery_type',
        choices=list(UnifiedDeliveryManager.DELIVERY_CONFIGS.keys()),
        help='Type of delivery to execute'
    )
    
    args = parser.parse_args()
    return args.delivery_type


def main():
    """Main execution function."""
    try:
        # Parse arguments
        delivery_type = parse_arguments()
        
        # Initialize and run delivery manager
        manager = UnifiedDeliveryManager(delivery_type)
        success = manager.execute_delivery()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"Critical error in delivery execution: {e}")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
