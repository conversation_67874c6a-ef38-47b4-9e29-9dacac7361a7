Following Files can be run for delivery file generation

1. monthly_aigo_preportfolio.py
2. monthly_db_preportfolio.py
3. monthly_bnp_preportfolio.py
4. monthly_multiasset_preportfolio.py --> Dependency - Depends on script 1 (monthly aigo) & Monthly Multiasset Daily Run


5. quarterly_aigo_preportfolio.py
6. quarterly_multiasset_preportfolio.py --> Dependency - Depends on script 5 (quarterly aigo) & Quarterly Multiasset Daily Run


How to Run?

From Terminal in the given directory, run -> '''python file_name rundate(optional)'''
                                    Example -> python monthly_aigo_preportfolio.py 2025-01-01

The run_date argument is optional (if needed to run for previous dates), otherwise it will take today's date as run date by default

