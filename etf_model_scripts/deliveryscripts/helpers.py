from eq_common_utils.utils.config.s3_config import s3_config
from google.oauth2.credentials import Credentials
from openpyxl.utils.dataframe import dataframe_to_rows
from email.mime.application import MIMEApplication
from dateutil.relativedelta import relativedelta
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email import encoders
from email.message import EmailMessage
import base64
from datetime import timedelta, date, datetime
from openpyxl.utils import get_column_letter
#from pandas.tseries.offsets import BDay
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from pandas.tseries.offsets import *
from pandas import json_normalize
from openpyxl import Workbook
import dateutil.relativedelta
from dateutil.rrule import *
from functools import reduce
from zipfile import ZipFile
from io import BytesIO
import pandas as pd
import configparser
import numpy as np
import threading
import traceback
import datetime
import requests
import smtplib
import boto3
import json
import math
import xlrd
import sys
import os
import tempfile

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

def er_low_func(row):
    try:
        return row['ER'] - (row['ER'] - row['min']) * (1 - row['confidence_score'])
    except:
        print(traceback.format_exc())
        
def er_high_func(row):
    return row['ER'] + (row['max'] - row['ER']) * (1 - row['confidence_score'])


def close_price(api_url, isin, startdate, enddate):
    '''
        Function to get price data from Inhouse Stock API
    '''
    try:
        df1 = pd.DataFrame()
        response = requests.get(api_url.format(isin = isin, startdate = startdate, enddate = enddate))
        response_data = response.json()
        check = bool(response_data['status'])
        if check:
            df1 = pd.DataFrame.from_dict(
                json_normalize(response_data['data']['stocks'])[['date', 'close']]).iloc[::-1]
            df1.rename(columns={'close': isin}, inplace=True)
            return df1
    except Exception as e:
        print(e)       

def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def upload_excel(s3conn, workbook, bucket, path):
    """
    Desc.: Upload each sheet in openpyxl.Workbook to S3
    
    Arguments:
        - workbook : openpyxl.Workbook
        - bucket   : string
        - path     : string : e.g., 'folder_1/file.xlsx'
    
    returns None
    """

    # 1 convert to df
    sheet = workbook.active

    # this is a generator (of rows)
    data = sheet.values
    ## Get the first row as header
    columns = next(data)

    # convert to df
    df = pd.DataFrame(data, columns=columns)

    # 2 convert df to bytes
    # create buffer
    buffer = BytesIO()
    
    # write excel file to buffer
    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
        df.to_excel(writer, index= False, sheet_name= 'Sheet1')
    
    # read bytes
    binary_file = buffer.getvalue()
    
    # upload to S3, as an excel file
    s3conn.upload_binary_file(binary_file, bucket, path)
