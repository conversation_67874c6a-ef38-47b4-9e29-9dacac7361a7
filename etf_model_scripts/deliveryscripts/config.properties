[S3_files]
gmail_creds_bucket = etf-predictions
gmail_creds_path = preportfolio/gmail_credentials/credentials.json
gmail_sender = <EMAIL>

[MONTHLY_AIGO]
monthly_aigo_local_path_bucket = etf-predictions
monthly_aigo_local_path_directory = preportfolio/Monthly/aigo/
monthly_aigo_prediction_bucket = etf-predictions
monthly_aigo_prediction_path = Monthly/best_pipeline/daily/{latest_date}/aigo_best_results_{latest_date}.csv
monthly_sector_prediction_bucket = etf-predictions
monthly_sector_prediction_path = Monthly/best_pipeline/daily/{latest_date}/sector_best_results_{latest_date}.csv
monthly_aigo_metrics_bucket = etf-predictions
monthly_aigo_metrics_path = Monthly/error-rate/aigo_results_{latest_date}.csv
monthly_sector_metrics_bucket = etf-predictions
monthly_sector_metrics_path = Monthly/sector/client/sector_results_{latest_date}.csv
monthly_aigo_all_local_portfolio_bucket = aimax
monthly_aigo_all_local_portfolio_path = ETF_Pre_Portfolio_Folder/ETF_pre_portfolio_{latest_date}.xlsx
monthly_aigo_all_versioned_portfolio_bucket = eq-model-output
monthly_aigo_all_versioned_portfolio_path = etf_model/aigo/delivery/monthly/{latest_date}/aigo.csv
#monthly_aigo_all_local_portfolio_bucket = histetfdata
#monthly_aigo_all_local_portfolio_path = ETF_Pre_Portfolio_Folder/ETF_pre_portfolio_{latest_date}.xlsx
#monthly_aigo_all_versioned_portfolio_bucket = histetfdata
#monthly_aigo_all_versioned_portfolio_path = etf_model/aigo/delivery/monthly/{latest_date}/aigo.csv
aigo_email_receiver_success = <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL> , <EMAIL> , <EMAIL> , <EMAIL> ,<EMAIL> 
aigo_email_receiver_failed = <EMAIL>, <EMAIL>, <EMAIL>

[MONTHLY_DB]
monthly_db_local_path_bucket = etf-predictions
monthly_db_local_path_directory = preportfolio/Monthly/db/
monthly_db_prediction_bucket = etf-predictions
monthly_db_prediction_path = Monthly/best_pipeline/daily/{latest_date}/db_best_results_{latest_date}.csv
monthly_db_metrics_bucket = etf-predictions
monthly_db_metrics_path = Monthly/db/client/db_results_{latest_date}.csv
monthly_db_local_portfolio_bucket = micro-ops-output
monthly_db_local_portfolio_path = platform/dbetf_construction_files/db_results_{latest_date}.csv
monthly_db_versioned_portfolio_bucket = eq-model-output
monthly_db_versioned_portfolio_path = etf_model/db/delivery/monthly/{latest_date}/db.csv
#monthly_db_local_portfolio_bucket = histetfdata
#monthly_db_local_portfolio_path = platform/dbetf_construction_files/db_results_{latest_date}.csv
#monthly_db_versioned_portfolio_bucket = histetfdata
#monthly_db_versioned_portfolio_path = etf_model/db/delivery/monthly/{latest_date}/db.csv
db_email_receiver_success = <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL> , <EMAIL> , <EMAIL> , <EMAIL> ,<EMAIL> 
db_email_receiver_failed = <EMAIL>, <EMAIL>, <EMAIL>

[MONTHLY_BNP]
bnp_main_bucket = etf-predictions
bnp_platform_bucket = micro-ops-output
bnp_versioning_bucket = eq-model-output
bnp_delivery_file_path = Monthly/new_BNP_Paribas/portfolio_construction_smoothing/BNP_{date}.csv
bnp_delivery_folder_path = Monthly/new_BNP_Paribas/portfolio_construction_smoothing/
bnp_historical_pred_data_path = Monthly/new_BNP_Paribas/Client/Final_data/{tic}.csv
best_pipeline_path = Monthly/best_pipeline/daily/{date}/bnp_best_results_{date}.csv
#platform_test_bucket = histetfdata
bnp_platform_file_path = platform/bnp_construction_smoothing/BNP_{date}.csv
#bnp_versioning_test_bucket = histetfdata
bnp_versioning_path = etf_model/bnp/delivery/monthly/{date}/bnp.csv
#bnp_test_main_bucket = histetfdata
bnp_checks_file_path = Monthly/new_BNP_Paribas/BNP-checks(internal file)/BNP_{date}_internal_file.xlsx
monthly_bnp_email_receiver_passed = <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
monthly_bnp_email_receiver_failed = <EMAIL>, <EMAIL>, <EMAIL>

[MONTHLY_MULTIASSET]
monthly_multiasset_bucket = etf-predictions
multiasset_monthly_upload_bucket = aimax
multiasset_error_rate_filename_path = Monthly/multiasset/client/multiasset_results_{latest_date}.csv
multiasset_monthly_localfile_path = preportfolio/Monthly/multiasset/etf_aigo_multi.csv
multiasset_monthly_upload_path = ETF_Pre_Portfolio_multi/ETF_pre_portfolio_{date}.xlsx
multiasset_monthly_versioning_bucket = eq-model-output
multiasset_monthly_versioning_path =  etf_model/multiasset/delivery/monthly/{date}/multiasset.csv
monthly_multiasset_email_receiver_success = <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL> , <EMAIL> , <EMAIL> , <EMAIL> ,<EMAIL> 
monthly_multiasset_email_receiver_failed = <EMAIL>, <EMAIL>, <EMAIL>

[QUARTERLY_AIGO]
quarterly_aigo_bucket_1 = etf-predictions
quarterly_aigo_bucket_2 = aimax

quarterly_aigo_path_1   = preportfolio/Quarterly/aigo/
quarterly_aigo_path_2   = /quaterly-aigo/
quarterly_aigo_path_3   = Quarterly/public_etfs/transformed_data/client/
quarterly_aigo_path_4   = ETF_Pre_Portfolio_quaterly

quarterly_aigo_file_1   = etfs_quarterly_returns_results_
quarterly_aigo_file_2   = ETF_pre_portfolio_

quarterly_aigo_all_versioned_portfolio_bucket = eq-model-output
quarterly_aigo_all_versioned_portfolio_path   = etf_model/aigo/delivery/quarterly/{latest_date}/aigo.csv

quarterly_aigo_email_receiver_success = <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL> , <EMAIL> , <EMAIL> , <EMAIL> ,<EMAIL> 
quarterly_aigo_email_receiver_failed = <EMAIL>, <EMAIL>, <EMAIL>
    
[QUARTERLY_MULTIASSET]
quarterly_multiasset_bucket_1 = etf-predictions
quarterly_multiasset_bucket_2 = aimax

quarterly_multiasset_path_1   = preportfolio/Quarterly/multiasset/
quarterly_multiasset_path_2   = /multi-asset_q/
quarterly_multiasset_path_3   = Quarterly/multiasset/client/
quarterly_multiasset_path_4   = ETF_Pre_Portfolio_multi_quarterly

quarterly_multiasset_file_1   = multiasset_results_
quarterly_multiasset_file_2   = etf_aigo_multi.csv
quarterly_multiasset_file_3   = ETF_pre_portfolio_

quarterly_multiasset_all_versioned_portfolio_bucket = eq-model-output
quarterly_multiasset_all_versioned_portfolio_path   = etf_model/multiasset/delivery/quarterly/{latest_date}/multiasset.csv

quarterly_multiasset_email_receiver_success = <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL> , <EMAIL> , <EMAIL> , <EMAIL> ,<EMAIL> 
quarterly_multiasset_email_receiver_failed = <EMAIL>, <EMAIL>, <EMAIL>
 
[Master_active_firms]
api_url = http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all
 
[Inhouse_API]
inhouse_api_url_by_isin = http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin={isin}&startDate={startdate}&endDate={enddate}&countrycode=USA
inhouse_api_url_by_tic = http://52.2.217.192:8080/stockhistory/getstocksbytic?tic={tic}&startDate={startdate}&endDate={enddate}&countrycode=USA
