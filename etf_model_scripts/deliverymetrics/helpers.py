from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from opensearchpy.connection.http_requests import RequestsHttpConnection
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from opensearchpy.client import OpenSearch
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
import requests
import pandas as pd
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
import traceback
import sys
from eq_common_utils.utils.metrics_helper import MetricsHelper
import regex as re
import warnings
warnings.filterwarnings("ignore")

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None

def prepare_body_html(body):
    html = f"""\
                <html>
                  <head></head>
                  <body>
                   {body}
                  </body>
                </html>
                """
    return html

# def do_s3_versioning(s3conn, schedular, lastbday, multiasset_etfs, product, predictions_bucket, predictions_path, upload_bucket, upload_path):
#     ######## get predictions data #################
#     for etf in multiasset_etfs:
#         curr_df = pd.DataFrame(None)
#         curr_df = s3conn.read_as_dataframe(predictions_bucket, predictions_path.format(schedular =schedular, etf =etf))
#         curr_df = curr_df[curr_df['date']<=lastbday]
#         curr_df  = curr_df[-1:]
#         curr_df.reset_index(inplace = True, drop = True)
#         isin = curr_df['isin'].values[0]
#         s3conn.write_advanced_as_df(curr_df, upload_bucket, 
#                                 upload_path.format(product =product, lower_schedular =schedular.lower(),latest_date=lastbday, isin = isin))
        
#     print('s3 versioning of the prediction data done')


def get_instockapi_data (api_url, firm ,start_date, end_date):
    '''
        Function to get price data from Inhouse Stock API
    '''
    isin = firm
    stock_data = requests.get(api_url.format(identifier = isin, startdate = start_date, enddate = end_date)).json()
 
    stock_data = pd.DataFrame(stock_data['data']['stocks'])[['date','isin','tic','adj_close','close']]
    stock_data.columns= ['date','isin','tic','adj_close','close_price']
    
    stock_data.drop_duplicates(subset=['date'],keep='first', inplace=True)
    stock_data['date']=pd.to_datetime(stock_data['date'])
    stock_data.sort_values('date', ascending =True , inplace=True)
    stock_data.reset_index(inplace=True, drop=True)
 
    stock_data['weekly_close_change']=stock_data['adj_close'].pct_change(periods=5)*100
    stock_data['monthly_close_change']=stock_data['adj_close'].pct_change(periods=22)*100
    stock_data['quarterly_close_change']=stock_data['adj_close'].pct_change(periods=66)*100
    stock_data['actual_monthly_returns']=stock_data['monthly_close_change'].shift(-22)
    stock_data['actual_quarterly_returns']= stock_data['quarterly_close_change'].shift(-66)
    stock_data['date'] = stock_data['date'].astype(str)
    return stock_data

def calculate_metrics (metrics_obj ,etf, temp_df, lastbday, schedular,  prediction_column, actual_column):
    '''
        Function to calculate metrics using inhouse library
    '''
    if schedular == 'Monthly':
        shift_window = 22
    elif schedular == 'Quarterly':
        shift_window = 66

    isin = temp_df['isin'].values[0]
    
    temp_df = temp_df[['date',actual_column, prediction_column]].copy()
    temp_df[actual_column] = temp_df[actual_column].shift(shift_window)
    temp_df[prediction_column] = temp_df[prediction_column].shift(shift_window)
    
    temp_df = temp_df.dropna()
    temp_df.reset_index(inplace = True, drop = True)
    
    new_metrics = metrics_obj.calculate_metrics_helper(temp_df , isin =isin, prediction_column =prediction_column, 
                                                       actual_column = actual_column) 
    new_metrics = new_metrics[new_metrics['date']<= lastbday]
    metrics_oneday = new_metrics[-1:]
    metrics_oneday.reset_index(inplace = True, drop = True)
    metrics_oneday = metrics_oneday.drop(columns = ['actual_returns','predictions'], axis = 1)

    metrics_oneday['tic'] = etf
    return metrics_oneday

def split_upload_df_product_and_isinwise (final_results, product_name, schedular, latest_date, upload_bucket, upload_path, s3conn):
    '''
        Function to upload data in product folder with isin
    '''
    final_results.reset_index(inplace=True, drop = True)
    
    for index,row in final_results.iterrows():
        isin_df  = pd.DataFrame([row])
        #print(isin_df)
        isin = isin_df['isin'].values[0]
        s3conn.write_advanced_as_df(isin_df, upload_bucket, upload_path.format(lower_schedular = schedular.lower() ,product = product_name, latest_date = latest_date, isin = isin))

    upload_path = upload_path.format(lower_schedular = schedular.lower() ,product = product_name, latest_date = latest_date, isin = 'isin')
    print(f'product file {product_name} uploaded at this path : {upload_path}')

def upload_df_productwise(final_results, product_name, schedular, latest_date, upload_bucket, upload_path, s3conn):
    '''
        Function to upload data in product folder aggregated
    '''
    product_file = pd.DataFrame(None)
    product_file = final_results.copy()
    product_file.reset_index(inplace=True, drop = True)
    product_file.loc[:, 'date'] = latest_date
    s3conn.write_advanced_as_df(product_file, upload_bucket, upload_path.format(lower_schedular = schedular.lower() ,product = product_name, latest_date = latest_date))

    upload_path = upload_path.format(lower_schedular = schedular.lower() ,product = product_name, latest_date = latest_date)
    print(f'product file {product_name} uploaded at this path : {upload_path}') 
    