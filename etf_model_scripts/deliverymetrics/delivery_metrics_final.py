from helpers import *

s3conn = s3_config()
metrics_obj = MetricsHelper(None)

with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

def send_email(subject , receiver, body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket , gmail_creds_path, s3conn, sender, receiver , subject, body_html)


def get_delivery_data(product, schedular, start_date, end_date):
    """
    Desc.: GET all the data (from S3) within the specified date-range, for the specified product & schedular
    - the files (on S3) contain a date in their name, it is ensured that this date lies within this date-range
    
    Arguments:
        product    : str
        schedular  : str
        start_date : str
        end_date   : str
        
    Returns a pd.DataFrame()
    """
    etf_dict={'TREASURY_2YR': 'MQFIUSTU',
         'TREASURY_10YR': 'MQFIUSTY',
         'TREASURY_30YR': 'MQFIUSUS',
         'METYS': 'HSMETYSN',
         'METYSV3' : 'HSMETYV3'}

    # 0 initialization
    # a
    product, schedular = product.lower(), schedular.lower()

    # b read the bucket-&-path, from 'config.yaml'
    bucket = config['S3_paths'][f'{schedular}_{product}_local_portfolio_bucket']
    path   = config['S3_paths'][f'{schedular}_{product}_local_portfolio_path']

    # c these lists shall store the dataframes & dates
    pre_port= []
    dfs     = []
    dates   = []

    # d select the date-format
    if 'aigo' == product:
        if schedular ==  'monthly':
            _format_  =  '%m_%d_%Y'
            
        elif schedular == 'quarterly':
            _format_  =  '%m-%d-%Y'
            
    elif 'multiasset' == product:
        _format_  =  '%m-%d-%Y'
        
    else:
        _format_  = '%Y-%m-%d'

    # 1 GET the files within the specified daterange
    
    # a GET the data

    # special case: increase the end_date by 1 day
    if 'bnp' == product:
        end_date  = datetime.strptime(end_date, '%Y-%m-%d')
        end_date += BDay(1)
        end_date.strftime('%Y-%m-%d')
        
    for date in pd.date_range(start_date, end_date, freq= 'B'):

        # If the file exists, GET it
        try:
            df= s3conn.read_as_dataframe(bucket, path.format( latest_date= date.strftime(_format_)))
        except Exception as e:
            continue
        
        # store
        dfs.append( df )
        dates.append( date )

    # special case
    if 'bnp' == product:
        tmp= []
        for date in dates:
            date -= BDay(1)
            tmp.append(date)
        dates= tmp

    print(f'product: {product}, schedular: {schedular}\nbucket: {bucket}\tdirectory: {'/'.join(path.split('/')[:-1]) + '/'[:-1]}\tlen(dfs): {len(dfs)}\n')
    # b error check
    if len(dfs) == 0:
        print(f'\nERROR: No Keys found at: {bucket}, {directory}  :: for the date-range {start_date} : {end_date}')
        return pd.DataFrame()

    # 2 post processing
    # a select columns based on product
    cols= None
    if 'db' == product:
        cols= ['date', 'isin'  , 'ER']
    elif 'bnp' == product:
        cols= ['date', 'Ticker', 'ER']
    else:
        cols= ['date', 'Equity', 'Avg']

    # b rename column
    if product == 'aigo' and schedular == 'monthly':
        for df in dfs:
            df.rename(mapper=  {'Date' : 'date'}, axis= 1, inplace= True)

    # c include the column 'date' in aigo-quarterly and bnp-monthly
    if 'db' == product or (product == 'aigo' and schedular == 'monthly'):
        #pre_port= list( map(lambda df: df.loc[:, cols], dfs) )
        for df in dfs:
            if all(col in df.columns for col in cols):
                pre_port.append(df.loc[:, cols])
    else:
        for date, df in zip(dates, dfs):
            df['date']= date
            pre_port.append(df.loc[:, cols])
    
    # d
    pre_port_df= pd.concat( pre_port )
    mapper= dict(zip(cols[1:], ['tic', 'delivery_predictions']))
    pre_port_df.rename(mapper = mapper, axis= 1, inplace= True)
    pre_port_df['date']= pd.to_datetime(pre_port_df['date'], format= 'mixed')
    pre_port_df.reset_index(inplace= True, drop= True)
    pre_port_df.replace(etf_dict, inplace= True)
    pre_port_df.drop_duplicates(subset= ['date', 'tic'], keep= 'first', inplace= True)
    pre_port_df['delivery_predictions']= 100 * pre_port_df['delivery_predictions']
    

    return pre_port_df


def run_metrics_product_wise(schedular, start_date, end_date, product, prices_start_date, latest_date):
    
    print(f'Starting run_metrics_product_wise for {product} and schedular {schedular}')
    cols_to_choose_inhouse = ['date','isin','tic',f'actual_{schedular.lower()}_returns']
    inhouse_api_url_by_tic = config['Inhouse_API']['inhouse_api_url_by_tic']

    if product =='aigo' and schedular == 'Monthly':
        etf_list = config['misc'][f'aigo_etfs'] + config['misc'][f'sector_etfs']
    else:
        etf_list = config['misc'][f'{product}_etfs']

    
    product = product.split('_')[0]
    preportdf = get_delivery_data(product, schedular, start_date, end_date)

    metrics_all = []
    for etf in etf_list:
        etf_raw_df = get_instockapi_data(inhouse_api_url_by_tic, etf, prices_start_date, end_date)
        etf_raw_df = etf_raw_df[cols_to_choose_inhouse]
        etf_raw_df['date'] = pd.to_datetime(etf_raw_df['date'])

        etf_delivery_df = preportdf[preportdf['tic']==etf]
        etf_delivery_df.reset_index(inplace=True, drop=True)

        etf_temp = pd.merge(etf_raw_df, etf_delivery_df, on = ['date','tic'], how = 'left')
        etf_temp.sort_values('date', ascending=True, inplace=True)
        etf_temp = etf_temp.ffill()
        etf_temp['date'] = etf_temp['date'].astype('str')
        
        metrics = calculate_metrics (metrics_obj ,etf, etf_temp, latest_date, schedular,  prediction_column = 'delivery_predictions', 
                                    actual_column = f'actual_{schedular.lower()}_returns')
    
        metrics_all.append(metrics)
    metrics_all_df = pd.concat(metrics_all, axis = 0)
    metrics_all_df.reset_index(inplace = True, drop = True)
    return metrics_all_df

def run_delivery_based_metrics_run (run_date, schedular):
    print(f'Starting delivery based metrics daily run for run date {run_date} for schedular {schedular}')

    ### getting last business date
    latest_date=(pd.to_datetime(run_date)-BDay(1)).strftime("%Y-%m-%d")
    start_date = (pd.to_datetime(latest_date) - timedelta(850)).strftime("%Y-%m-%d")
    #start_date = (pd.to_datetime(latest_date) - timedelta(200)).strftime("%Y-%m-%d")
    prices_start_date = (pd.to_datetime(latest_date) - timedelta(days = 850)).strftime("%Y-%m-%d")
    end_date = (pd.to_datetime(latest_date)).strftime("%Y-%m-%d")
    print(start_date, end_date, prices_start_date)

    s3versioned_postprocessed_upload_bucket = config['S3_paths']['upload_s3versioned_postprocessed_metrics_bucket']
    s3versioned_postprocessed_upload_path = config['S3_paths']['upload_s3versioned_postprocessed_metrics_path']
    s3versioned_product_upload_bucket = config['S3_paths']['upload_s3versioned_delivery_metrics_bucket']
    s3versioned_product_upload_path = config['S3_paths']['upload_s3versioned_delivery_metrics_path']

    all_prod_metrics_bucket = config['S3_paths']['upload_all_prod_delivery_metrics_local_bucket']
    all_prod_metrics_path = config['S3_paths']['upload_all_prod_delivery_metrics_local_path']
    
    if schedular == "Monthly":
        product_etf_list = ['aigo' , 'db', 'bnp', 'multiasset']
    elif schedular =='Quarterly':
        product_etf_list = ['aigo_pub' , 'multiasset']

    metrics_all_prod = []
    for product_etf in product_etf_list:
        metrics_df = run_metrics_product_wise( schedular, start_date, end_date, product_etf, prices_start_date, latest_date)
        metrics_all_prod.append(metrics_df)
        product_name = product_etf.split('_')[0]
        
        ### splitting uploading isinwise 
        split_upload_df_product_and_isinwise (metrics_df, product_name, schedular, latest_date, s3versioned_postprocessed_upload_bucket, s3versioned_postprocessed_upload_path, s3conn)
        #### product wise uploading to s3versioned
        upload_df_productwise(metrics_df, product_name, schedular, latest_date, s3versioned_product_upload_bucket, s3versioned_product_upload_path, s3conn)

    metrics_all_prod_df  = pd.concat(metrics_all_prod, axis = 0)
    metrics_all_prod_df.reset_index(inplace = True, drop = True)
    
    #saving to local s3
    s3conn.write_advanced_as_df(metrics_all_prod_df, all_prod_metrics_bucket, all_prod_metrics_path.format(schedular = schedular, schedular_lower = schedular.lower(), latest_date = latest_date))
    print(f'Saving to Local s3 Done for {schedular}')
    print(metrics_all_prod_df)

if __name__=='__main__':
    
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")

    print(f'starting Multiasset run for run date :{run_date}')
    
    run_schedular  = config['misc']['run_schedular']

    for schedular in run_schedular:
        if schedular not in ['Monthly','Quarterly']:
            print(f'Invalid schedular : {schedular}, only accept in [Monthly,Quarterly]')
            continue
        
        try:
            run_delivery_based_metrics_run(run_date, schedular)
            print(f'delivery based metrics run part completed for {schedular} schedular')

            print(f'{schedular} Delivery Metrics Daily Run: Run COMPLETED successfully for run date {run_date}')
            
            receiver = config['Gmail_creds']['email_receiver_success']
            send_email(f'{schedular} Delivery Metrics Daily Run : Run COMPLETED successfully for run date {run_date}',receiver, 'SUCCESS')
            
        except Exception as e:
            print(f'{schedular} Delivery Metrics Daily Run : Run FAILED for run date {run_date} due to the Error : {e}')
            print(traceback.format_exc())
            receiver = config['Gmail_creds']['email_receiver_failed']
            send_email( f'{schedular} Delivery Metrics Daily Run : Run FAILED for run date {run_date}', receiver, traceback.format_exc())
            raise
