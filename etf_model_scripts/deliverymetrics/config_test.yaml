Gmail_creds:
    gmail_creds_bucket : etf-predictions
    gmail_creds_path: preportfolio/gmail_credentials/credentials.json
    gmail_sender : <EMAIL>
    email_receiver_success : <EMAIL>, <EMAIL>, <EMAIL>
    email_receiver_failed : <EMAIL>, <EMAIL>, <EMAIL>

Tic_ISIN_map:
    isin_tic_map_bucket : etf-predictions
    isin_tic_map_path : preportfolio/etfs_isin_map/etfs_tic_isin_mapping.csv
    multiasset_isin_tic_map_bucket : etf-predictions
    multiasset_isin_tic_map_path : preportfolio/etfs_isin_map/multiasset_tic_isin_mapping.csv
    
Master_active_firms:
    api_url : http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all

Inhouse_API:
    inhouse_api_url_by_isin : http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA
    inhouse_api_url_by_tic : http://52.2.217.192:8080/stockhistory/getstocksbytic?tic={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA

S3_paths:

    monthly_aigo_local_portfolio_bucket : aimax
    monthly_aigo_local_portfolio_path : ETF_Pre_Portfolio_Folder/ETF_pre_portfolio_{latest_date}.xlsx

    monthly_db_local_portfolio_bucket : micro-ops-output
    monthly_db_local_portfolio_path : platform/dbetf_construction_files/db_results_{latest_date}.csv

    monthly_bnp_local_portfolio_bucket : etf-predictions
    monthly_bnp_local_portfolio_path : Monthly/new_BNP_Paribas/portfolio_construction_smoothing/BNP_{latest_date}.csv

    quarterly_aigo_local_portfolio_bucket : aimax
    quarterly_aigo_local_portfolio_path : ETF_Pre_Portfolio_quaterly/ETF_pre_portfolio_{latest_date}.xlsx
    
    monthly_multiasset_local_portfolio_bucket : aimax
    monthly_multiasset_local_portfolio_path : ETF_Pre_Portfolio_multi/ETF_pre_portfolio_{latest_date}.xlsx

    quarterly_multiasset_local_portfolio_bucket : aimax
    quarterly_multiasset_local_portfolio_path : ETF_Pre_Portfolio_multi_quarterly/ETF_pre_portfolio_{latest_date}.xlsx


    upload_all_prod_delivery_metrics_local_bucket : histetfdata
    upload_all_prod_delivery_metrics_local_path : "{schedular}/delivery_metrics/{schedular_lower}_model_delivery_metrics_{latest_date}.csv"
    
    
    upload_s3versioned_delivery_metrics_bucket : histetfdata
    upload_s3versioned_delivery_metrics_path : etf_model/{product}/delivery/{lower_schedular}/{latest_date}/{product}_metrics.csv
    upload_s3versioned_postprocessed_metrics_bucket : histetfdata
    upload_s3versioned_postprocessed_metrics_path : etf_model/{product}/etf_post_processed/{lower_schedular}/{latest_date}/metrics/{isin}.csv
    

misc:
    aigo_etfs : [QQQ, EEM, EFA, IWM, IYR, EWJ, SPY, XME, XLE,TIP, LQD, TLT, SHY, EMB, HYG, GLD, BNDX,MQFIUSTU, MQFIUSTY, MQFIUSUS, HSMETYSN,
                    HSMETYV3]
    sector_etfs : [ XLB, XLV, XLP, XLY, XLF, XLI, XLK, XLC, XLRE, XLU]
    db_etfs : [DBEEEMGF,DBEETGFU,DBEEURGF,DBEEUNGF,DBEEUGFT,DBRCOYGC,DBDRUS10,DBDRUS02,DBDRUS20]
    bnp_etfs : [BNPIFU10,BNPIFCN, BNPIFJP, BNPIFE10, BNPIFJ10, BNPIDSBU,BNPIFUS, BNPIFEM, BNPIG0GC, BNPIFEU]
    
    aigo_pub_etfs : [QQQ, EEM, EFA, IWM, IYR, EWJ, SPY, XME, XLE,TIP, LQD, TLT, SHY, EMB, HYG, GLD, BNDX, XLB, XLV, XLP, XLY, XLF, XLI, XLK, XLC,
                        XLRE, XLU]

    multiasset_etfs : [AOK ,AOM, AOA, AOR]
    run_schedular : [Monthly, Quarterly]