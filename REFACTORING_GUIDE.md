# Refactoring Guide for DS-Inference-Scripts

This guide demonstrates how to refactor existing model scripts to use the new shared utilities, improving code quality and reducing duplication.

## 🔄 **Before and After Examples**

### Email Sending Refactoring

**Before (Duplicated across 15+ files):**
```python
def send_email(subject, receiver, body=None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path = config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']
    
    receiver = [email.strip() for email in receiver.split(',')]
    body_html = prepare_body_html(body)
    
    SendMessage(gmail_creds_bucket, gmail_creds_path, s3conn, sender, receiver, subject, body_html)
```

**After (Using shared utilities):**
```python
from shared_utils.email_utils import create_email_sender
from shared_utils.config_utils import load_config

config = load_config('config.yaml')
email_sender = create_email_sender(config.to_dict(), s3_client)

# Simple email sending
email_sender.send_email(subject, receiver, body)
```

### Configuration Management Refactoring

**Before (Inconsistent across scripts):**
```python
# Some scripts use YAML
with open('config.yaml', 'r') as file:
    config = yaml.safe_load(file)

# Others use ConfigParser
config = configparser.ConfigParser()
config.read('config.properties')

# Different access patterns
bucket = config['Gmail_creds']['gmail_creds_bucket']  # YAML
bucket = config.get('S3_files', 'gmail_creds_bucket')  # Properties
```

**After (Unified approach):**
```python
from shared_utils.config_utils import load_config, validate_common_config

config = load_config('config.yaml')  # Works with YAML, JSON, or Properties
validate_common_config(config)  # Validate required keys

bucket = config.get('Gmail_creds.gmail_creds_bucket')  # Unified access
```

### Logging Refactoring

**Before (Inconsistent setup):**
```python
# Different logging setups across scripts
logger = logging.getLogger('my_logger')
logger.setLevel(logging.DEBUG)
file_handler = logging.FileHandler(log_file_path, mode='w')
formatter = logging.Formatter('%(levelname)s: %(message)s')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
```

**After (Standardized logging):**
```python
from shared_utils.logging_utils import create_model_logger

logger = create_model_logger(
    model_name='best_model',
    tag='aieq',
    scheduler='monthly',
    run_date='2024-01-15',
    log_dir='logs'
)

logger.log_model_start({'total_isins': 1000})
logger.log_prediction_summary(950, 50, 0.85)
logger.log_model_end(success=True)
```

### S3 Operations Refactoring

**Before (Scattered S3 operations):**
```python
def download_s3_object(s3_client, bucketname, s3_filepath, local_folder):
    if not os.path.exists(local_folder):
        os.mkdir(local_folder)
    filename = s3_filepath.strip().split('/')[-1]
    local_filepath = os.path.join(local_folder, filename)
    s3_client.download_file(bucketname, s3_filepath, local_filepath)
    return local_filepath
```

**After (Using S3Manager):**
```python
from shared_utils.s3_utils import create_s3_manager

s3_manager = create_s3_manager(s3_client)
local_path = s3_manager.download_to_folder(bucket, s3_path, local_folder)

# Or for DataFrames
df = s3_manager.read_dataframe(bucket, 'data/predictions.csv')
s3_manager.write_dataframe(results_df, bucket, 'output/results.csv')
```

## 🛠️ **Step-by-Step Refactoring Process**

### 1. Update Imports
Replace scattered imports with shared utilities:

```python
# Add to the top of your script
import sys
from pathlib import Path

# Add shared_utils to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'shared_utils'))

from shared_utils.config_utils import load_config, validate_common_config
from shared_utils.logging_utils import create_model_logger
from shared_utils.email_utils import create_email_sender
from shared_utils.s3_utils import create_s3_manager
```

### 2. Standardize Configuration Loading
```python
# Replace existing config loading
config = load_config('config.yaml')
validate_common_config(config)

# Use unified access pattern
bucket = config.get('s3_buckets.output_bucket')
api_key = config.get('ibm.api_key')
```

### 3. Setup Standardized Logging
```python
# Replace existing logger setup
logger = create_model_logger(
    model_name=MODEL_NAME,
    tag=tag,
    scheduler=scheduler,
    run_date=run_date,
    log_dir=config.get('logging.log_dir', 'logs')
)
```

### 4. Replace Email Functions
```python
# Replace existing email functions
email_sender = create_email_sender(config.to_dict(), s3_client)

def send_notification(subject, recipients, body):
    return email_sender.send_email(subject, recipients, body)
```

### 5. Standardize S3 Operations
```python
# Replace existing S3 operations
s3_manager = create_s3_manager(s3_client)

# Replace manual file operations
df = s3_manager.read_dataframe(bucket, input_path)
s3_manager.write_dataframe(results, bucket, output_path)
```

## 📋 **Refactoring Checklist**

### For Each Script:
- [ ] Update imports to use shared utilities
- [ ] Replace configuration loading with `load_config()`
- [ ] Replace logger setup with `create_model_logger()`
- [ ] Replace email functions with `EmailSender`
- [ ] Replace S3 operations with `S3Manager`
- [ ] Add proper error handling using logger
- [ ] Validate configuration with validation functions
- [ ] Test the refactored script thoroughly

### Code Quality Improvements:
- [ ] Remove duplicate functions
- [ ] Standardize variable naming (s3_client, not s3conn/ss)
- [ ] Add type hints where possible
- [ ] Replace bare `except:` with specific exceptions
- [ ] Add docstrings to functions
- [ ] Remove hardcoded values to configuration

## 🎯 **Priority Scripts for Refactoring**

1. **High Priority** (Most duplicated code):
   - `etf_model_scripts/etf_models_trigger_main.py`
   - `best_model_scripts/main.py`
   - All delivery scripts in `etf_model_scripts/deliveryscripts/`

2. **Medium Priority**:
   - `management_model_scripts/management_model_daily_run.py`
   - `lstm_model_scripts/lstm_model_daily_run.py`
   - `fin_model_scripts/Financial_Model_Prediction_Script.py`

3. **Low Priority**:
   - Helper modules that are already relatively clean
   - Scripts with minimal duplication

## 🧪 **Testing Refactored Scripts**

### Before Deployment:
1. **Unit Tests**: Test individual functions
2. **Integration Tests**: Test full script execution
3. **Configuration Tests**: Validate config loading
4. **Email Tests**: Test notification sending
5. **S3 Tests**: Test data read/write operations

### Example Test:
```python
def test_refactored_script():
    # Test configuration loading
    config = load_config('test_config.yaml')
    assert config.get('test_key') == 'test_value'
    
    # Test logger creation
    logger = create_model_logger('test_model', 'test_tag', 'daily', '2024-01-01')
    assert logger.logger.name == 'test_model.test_tag.daily'
    
    # Test email sender
    email_sender = create_email_sender(config.to_dict())
    assert email_sender is not None
```

## 📈 **Expected Benefits**

After refactoring:
- **90% reduction** in duplicate email code
- **Consistent logging** across all scripts
- **Unified configuration** management
- **Better error handling** and debugging
- **Easier maintenance** and updates
- **Improved code readability**
- **Standardized patterns** for new scripts

## 🚀 **Next Steps**

1. Start with the highest priority scripts
2. Refactor one script at a time
3. Test thoroughly before moving to the next
4. Update documentation as you go
5. Create unit tests for shared utilities
6. Consider adding CI/CD pipeline for automated testing
