#!/usr/bin/env python
# coding: utf-8

"""
Financial Model Scripts - Prediction Script

Refactored financial model prediction script with improved error handling,
logging, and standardized utilities usage.
"""

import argparse
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# Import shared utilities package (installed from Git)
from shared_utils import (
    load_config, validate_common_config, create_model_logger, create_error_handler,
    ErrorSeverity, ModelError, ConfigurationError, ErrorContext
)

# Import existing modules (to be refactored gradually)
from Imports_Modified import *


class FinancialModelRunner:
    """Financial Model execution orchestrator with improved error handling."""

    def __init__(self, tag: str, scheduler: str):
        """
        Initialize Financial Model Runner.

        Args:
            tag: Model tag
            scheduler: Scheduler type
        """
        self.tag = tag.lower()
        self.scheduler = scheduler.lower()

        # Setup script directory and configuration
        self.script_dir = self._get_script_directory()
        config_path = os.path.join(self.script_dir, 'config_modified.yaml')
        self.config = load_config(config_path)
        self._validate_config()

        # Setup logging
        self.logger = create_model_logger(
            model_name='financial_model',
            tag=self.tag,
            scheduler=self.scheduler,
            run_date=datetime.now().strftime('%Y-%m-%d'),
            log_dir=os.path.join(self.script_dir, 'logs')
        )

        # Setup error handler
        self.error_handler = create_error_handler(self.logger.get_logger())

        # Initialize connections and helpers
        self.conn = self._initialize_connection()
        self.ph = predictionHelper()
        self.ph.input_columns = self.conn.input_cols

        self.logger.log_model_start({
            'tag': self.tag,
            'scheduler': self.scheduler,
            'connection_type': type(self.conn).__name__
        })

    def _get_script_directory(self) -> str:
        """Get script directory path."""
        if '__file__' in globals():
            return os.path.dirname(os.path.abspath(__file__))
        else:
            return os.getcwd()

    def _validate_config(self) -> None:
        """Validate configuration requirements."""
        required_keys = [
            'tags.aieq_tag',
            'tags.aigo_tag',
            'tags.indiat1_tag',
            'schedulars.schedular_m',
            'schedulars.schedular_d'
        ]

        if not self.config.validate_required_keys(required_keys):
            raise ConfigurationError("Missing required configuration keys")

    def _initialize_connection(self):
        """Initialize the appropriate connection based on tag and scheduler."""
        try:
            tag_config = self.config.get('tags', {})
            scheduler_config = self.config.get('schedulars', {})

            if self.tag == tag_config.get('aieq_tag', '').lower():
                if self.scheduler == scheduler_config.get('schedular_m', '').lower():
                    conn = AieqMonthlyHelper(self.tag)
                elif self.scheduler == scheduler_config.get('schedular_d', '').lower():
                    conn = AieqDailyHelper(self.tag)
                else:
                    raise ConfigurationError(f"Invalid scheduler '{self.scheduler}' for tag '{self.tag}'")

            elif self.tag == tag_config.get('aigo_tag', '').lower():
                conn = AigoDataHelper(self.tag)

            elif self.tag == tag_config.get('indiat1_tag', '').lower():
                conn = IndiaDataHelper(self.tag)

            else:
                raise ConfigurationError(f"Invalid tag '{self.tag}'")

            self.logger.info(f"Initialized connection: {type(conn).__name__}")
            return conn

        except Exception as e:
            self.error_handler.handle_error(
                ConfigurationError(f"Failed to initialize connection: {e}", ErrorSeverity.CRITICAL),
                context={"tag": self.tag, "scheduler": self.scheduler}
            )
            raise
        log_dir = os.path.dirname(log_file_path)
        print(log_dir)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)  # Create the directory if it doesn't exist
        logger = logging.getLogger('my_logger')
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            logger.removeHandler(handler)
        file_handler = logging.FileHandler(log_file_path, mode='a')  # Use mode='w' for write mode
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger

    # try
    env = config['environments']['prod_env']
    print(env)
    num = config['n_bday']
    current_date = date.today()
    saved_date = (current_date - BDay(num)).date()
    log_file_path = config['logger_path'].replace('folder',script_dir).replace('date',str(saved_date)).replace('tag',conn.tag.capitalize())
    global logger
    logger = create_logger(log_file_path)


    today = (datetime.today().date()-BDay(num)).strftime("%m/%d/%Y")
    date_string = (pd.to_datetime(today)+timedelta(1)).strftime("%Y-%m-%d")
    logger.info(f"Prediction run for {today}")

    if env == 'prod':
        receivers_list = config['email_credentials']['receiver_emails_for_trigger']
    else:
        #for testing purpose
        receivers_list = conn.sender_email
    try:    
#         conn.starting_mail(conn.tag.capitalize(), conn.schedular.capitalize(), today, receivers_list, 'Prediction')
        subject = f'Financial model {tag.upper()} {schedular.capitalize()} Predictions run started'
        message_text = f"Financial model {tag.upper()} {schedular.capitalize()} Prediction run for {str(pd.to_datetime(today).date())} has started"
        conn.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)
    except:
         logger.error("Error in sending starting mail")

    conn.log_file_path = log_file_path

    es_prod = es_config(env='prod') 
    es_pre_prod = es_config(env='pre')

    column_list = ph.input_columns

    try:    
        masters_isin_list = conn.get_isin_list()
    except Exception as e:
        logger.error(f"Error in getting ISIN list: {e}")
        try:
            conn.failure_mail(f"Error in getting ISIN list: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
        except:
            logger.error("Error in getting ISIN list and error in sending failure mail")      
        raise SystemExit('Stop right there') 
        
    try:
        dep_file_total = conn.s3_client.read_as_dataframe(conn.dep_bucket_name, conn.dep_file)
        dep_file = dep_file_total.loc[dep_file_total['expiration_date'].isnull()]
        latest_years = dep_file.groupby('name')['year'].transform('max')
        filtered_df = dep_file[dep_file['year'] == latest_years]
        dep_file=filtered_df[filtered_df['name'].isin(masters_isin_list['isin'].tolist())]
        dep_file['name']=dep_file['name'].apply(lambda x:x.split('_')[0])
        dep=dep_file.copy()
        dep.drop_duplicates(subset='name', inplace=True,keep='first')
        no_models_list = masters_isin_list[~masters_isin_list[conn.isin_col].isin(dep['name'].unique().tolist())][conn.isin_col].tolist()
        logger.info(f"No: of ISINs with no models: {len(no_models_list)}")
        dep.drop(columns= conn.cols_to_drop_from_dep, inplace=True)
    except Exception as e:
        logger.error(f"Error in reading deployment file from S3 for {tag} {schedular}: {e}")
        try:
            conn.failure_mail(f"Error in reading deployment file from S3 for {tag} {schedular}: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
        except Exception as e:
            logger.error(f"Error in reading deployment file from S3 for {tag} {schedular} and error in sending failure mail, {e}")     
        raise SystemExit('Stop right there')

    try:
        isin_list = [x for x in masters_isin_list[conn.isin_col].tolist()]
        start_date = (pd.to_datetime(today) - timedelta(days=config['look_back_period'])).isoformat()
        end_date = (pd.to_datetime(today)).isoformat()
        q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date,
                      "lte": end_date}}},{"term": {"schedular.keyword": conn.schedular.capitalize()}}]}}}
        if env == 'prod':
            test_data = conn.get_es_data(es_prod, start_date, end_date, isin_list, q_total, conn.fin_index)
        else:
            #for testing purpose             
            test_data = conn.get_es_data(es_pre_prod, start_date, end_date, isin_list, q_total, conn.pre_fin_index)
        test_data = test_data.sort_values([conn.isin_col, conn.date_col])
        hist_data = test_data[test_data[conn.date_col]<pd.to_datetime(today)]
        test_data = test_data[column_list]
        test_data1 = test_data[test_data[conn.date_col]==pd.to_datetime(today)]
        nan_dict = test_data1.set_index('isin').isna().apply(lambda x: list(x.index[x]), axis=1).to_dict()
        nan_dict = {k: v for k, v in nan_dict.items() if v}
        test_data_original = test_data.copy()
        test_data[test_data.columns] = test_data.groupby([conn.isin_col])[test_data.columns].ffill()
    #         test_data = test_data.groupby('isin', group_keys=False).apply(lambda group: group.sort_values('date').ffill().fillna(0))
        filled_entries = []
        for isin, features in nan_dict.items():
            for feature in features:
                # Check if the NaN is now filled for today's date
                original_nan = test_data_original.loc[
                    (test_data_original[conn.isin_col] == isin) & 
                    (test_data_original[conn.date_col] == pd.to_datetime(today)), feature
                ].isna().values[0]

                new_value = test_data.loc[
                    (test_data[conn.isin_col] == isin) & 
                    (test_data[conn.date_col] == pd.to_datetime(today)), feature
                ].values[0]

                # If it was NaN and now it's not, it got forward filled
                if original_nan and pd.notna(new_value):
                    filled_entries.append({
                        "isin": isin,
                        "feature": feature,
                        "filled_value": new_value
                    })
        filled_df = pd.DataFrame(filled_entries)
        if env == 'prod':
            conn.s3_client.write_advanced_as_df(filled_df, conn.bucket_name, conn.ffilled_file.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d")))
        else:
            conn.s3_client.write_advanced_as_df(filled_df, conn.bucket_name, conn.ffilled_file_temp.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d")))
        test_data = test_data[test_data[conn.date_col]==pd.to_datetime(today)]
        test_data[conn.date_col]=test_data[conn.date_col].dt.strftime('%Y-%m-%d')
        test_data.reset_index(drop=True, inplace=True)    
    except Exception as e:    
        logger.error(f"Error in reading input data from ES for {tag} {schedular}: {e}") 
        try:
            conn.failure_mail(f"Error in reading input data from ES for {tag} {schedular}: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
        except:
            logger.error(f"Error in reading input data from ES for {tag} {schedular} and error in sending failure mail")     
        raise SystemExit('Stop right there')   


    try:
        dep=dep[dep[conn.name_col].isin(test_data[conn.isin_col])]
        pred_input = []
        for i in range(len(dep)):
            isin = dep.iloc[i][conn.name_col]
            single_row = test_data[test_data[conn.isin_col]==isin]
            single_row = single_row[column_list]
            single_row.reset_index(drop=True, inplace=True)
            pred_input.append(single_row)
        dep['test_data'] = pred_input  
    except Exception as e:
        logger.error(f"Error in filtering the required input columns: {e}") 

    try:
        deployment_toSubmit = dep.drop(columns=conn.name_col,axis=1)
        deployment_toSubmit=deployment_toSubmit.rename(columns=config['columns']['space_col_rename'])
        deployment_toSubmit = deployment_toSubmit[conn.cols_to_keep_for_dep]
    except Exception as e:
        logger.error(f"Error in preparing deployment_toSubmit: {e}")

    CLOUD_API_KEY = ph.ibm_api_key
    wml_credentials = Credentials(
                       url = conn.ibm_url,
                       api_key = CLOUD_API_KEY,
                      )
    client = APIClient(credentials = wml_credentials)

    conn.patch_https_connection_pool(maxsize=100)

    try:
        ###### for daily run
        all_final_results = pd.DataFrame()
        if len(deployment_toSubmit)>0:
            stt = datetime.now()
            prediction_input_deployments = deployment_toSubmit.copy()
            max_allowed_time = 2
            max_retries = 2
            n_retry = 0
            while(n_retry <= max_retries):
                if len(prediction_input_deployments) == 0:
                    print('No deployments to fetch predictions for.')
                    break

                logger.info(f"Deployments to submit shape: {prediction_input_deployments.shape}")
                start = time.time()
                prediction_results = ph.trigger_predict(prediction_input_deployments, wml_credentials, max_run_duration = max_allowed_time * 60)
                end = time.time()
                logger.info(f'Total time taken for the prediction jobs to run: {round((end-start)/60, 2)} mins.')
                final_results = pd.merge(prediction_results, prediction_input_deployments,  how='left', left_on=[conn.deployment_space_col,conn.deployment_id_col], right_on = [conn.deployment_space_col,conn.deployment_id_col])[[conn.deployment_id_col,conn.pred_col,conn.status_col]]
                all_final_results = pd.concat([all_final_results, final_results[final_results[conn.pred_col].notna()].reset_index(drop=True)]).reset_index(drop=True)
                final_results = final_results[final_results[conn.pred_col].isna()].reset_index(drop = True)
                if len(final_results) != 0:
                    n_retry += 1
                    logger.info(f"Retry #{n_retry}.")
                    prediction_input_deployments = pd.merge(final_results.drop(columns = [conn.pred_col,conn.status_col]), prediction_input_deployments, on = [conn.deployment_id_col], how = 'left').reset_index(drop = True)
                    logger.info(f'Number of deployments in retry {len(prediction_input_deployments)}.')
                else:
                    logger.info('Prediction jobs completed for all deployments to submit.')
                    break
        else:
            logger.info(f"No deployments to fetch predictions for")
            final_results = pd.DataFrame(columns=[conn.deployment_id_col,conn.pred_col,conn.status_col])
        all_final_results=pd.concat([all_final_results,final_results.reset_index(drop=True)]).reset_index(drop=True)              
        ett = datetime.now()  
    except Exception as e:
        logger.error(f"Error while fetching predictions for {tag} {schedular}: {e}") 
        try:
            conn.failure_mail(f"Error while fetching predictions for {tag} {schedular}: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
        except:
            logger.error(f"Error while fetching predictions for {tag} {schedular} and error in sending failure mail")    
        raise SystemExit('Stop right there')


    success_jobs = all_final_results[all_final_results[conn.status_col] == 'completed']
    failed_results = all_final_results[all_final_results[conn.status_col] != 'completed']

    if len(ph.failed_df)>0: 
        try:
            logger.info(f"{len(ph.failed_df)} jobs have failed")
            try:            
                    failed_df_old = conn.s3_client.read_as_dataframe(conn.bucket_name, conn.failed_df_filename.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d")))  
            except:
                    failed_df_old = pd.DataFrame()
            failed_df = pd.concat([failed_df_old, ph.failed_df])  
            conn.s3_client.write_advanced_as_df(failed_df, conn.bucket_name, conn.failed_df_filename.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d")))
        except Exception as e:
            logger.error(f"Error in uploading failed job details to S3, {e}")
    else:
        logger.info("No jobs have failed")


    try:
        ph.job_failed+=len(all_final_results[~all_final_results[conn.status_col].isin(['failed','completed'])])
    except Exception as e:
        logger.error(f"Error in updating job_failed: {e}") 

    try:
        result_df=pd.merge(dep, all_final_results, how="left", on =conn.deployment_id_col)
        combined_df=result_df[[conn.name_col,conn.pred_col]]
        combined_df=pd.merge(test_data, combined_df, how="left", left_on =conn.isin_col, right_on=conn.name_col)
        combined_df.drop(conn.name_col,axis=1,inplace=True)
        combined_df[conn.pred_col]=combined_df[conn.pred_col].map(lambda x:x[0] if (type(x)==list and len(x)>0) else 0)
    except Exception as e:
        logger.error(f"Error while merging the predictions with the input data for {tag} {schedular}: {e}") 
        try:
            conn.failure_mail(f"Error while merging the predictions with the input data for {tag} {schedular}: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
        except:
            logger.error(f"Error while merging the predictions with the input data for {tag} {schedular} and error in sending failure mail")      
        raise SystemExit('Stop right there')
    
    masters_sublist = masters_isin_list[masters_isin_list['isin'].isin(test_data1['isin'].tolist())]
    final_predictions = masters_sublist[['isin']].merge(combined_df,on=['isin'],how='left')
    final_predictions = final_predictions.merge(masters_sublist[['isin','ind_code']],on=['isin'],how='left')
    final_predictions['sector_code'] = final_predictions['ind_code'].astype(str).str[:2]

    sector_preds = final_predictions.groupby(final_predictions['sector_code'])[conn.pred_col].mean().reset_index()
    sector_pred_map = dict(zip(sector_preds['sector_code'], sector_preds[conn.pred_col]))
    if len(final_predictions)>0:
        # Identify rows to fill: NaNs in prediction & ISIN in not_trained_model_isins

        mask = final_predictions['isin'].isin(no_models_list)
        final_predictions.loc[mask, conn.pred_col] = final_predictions.loc[mask, 'sector_code'].map(sector_pred_map)
        final_predictions.loc[mask, 'model_identifier'] = config['sector_avg_identifier']

        try:
            failed_ones=pd.merge(failed_results, dep, how="left", on =conn.deployment_id_col)[conn.name_col].tolist()
            final_predictions[config['columns']['er_copied_col']] = False
            final_predictions.loc[final_predictions[conn.isin_col].isin(failed_ones), config['columns']['er_copied_col']] = True
        except Exception as e:
            logger.error(f"Error while flagging the ISINs failed in predictions: {e}")         


        if len(failed_ones)>0:
            logger.info("Following ISINs have failed during prediction:")
            for isin in failed_ones:
                logger.info(f"{isin}")

        final_predictions[conn.pred_col]=final_predictions[conn.pred_col].astype(float)


        date_obj = pd.to_datetime(final_predictions[conn.date_col].iloc[0])
        new_date_string = date_obj.strftime("%Y-%m-%d")


        final_predictions[conn.date_col]=pd.to_datetime(final_predictions[conn.date_col])
        final_predictions[conn.date_col] = final_predictions[conn.date_col].dt.strftime('%Y-%m-%d')


        final_predictions.loc[final_predictions[conn.isin_col].isin(failed_ones),conn.pred_col] = np.nan
        final_df  = final_predictions.copy()


        new_date_string=pd.to_datetime(final_df[conn.date_col].iloc[0]).strftime("%Y-%m-%d")
        new_date_string


        final_df[conn.date_col]=pd.to_datetime(final_df[conn.date_col]).dt.date

        try:
            if env == 'prod':
                daily_run_file = conn.s3_client.read_as_dataframe(conn.daily_run_bucket_name, conn.daily_run_filename.replace('date',new_date_string))  
                daily_run_file[conn.date_col] = pd.to_datetime(daily_run_file[conn.date_col]).dt.date
            else:    
                #for testing purpose             
                daily_run_file = conn.s3_client.read_as_dataframe(conn.bucket_name, conn.daily_run_file_temp.replace('date',new_date_string))
                daily_run_file[conn.date_col] = pd.to_datetime(daily_run_file[conn.date_col]).dt.date
        except:
            daily_run_file = pd.DataFrame(columns=final_df.columns)

        daily_run_file = daily_run_file.reindex(columns=final_df.columns)
        final_df = pd.concat([daily_run_file[final_df.columns], final_df])
        final_df = final_df.drop_duplicates([conn.isin_col], keep='last')


        try:
            final_df['Zscore'], final_df['F-score'] = ph.calculate_score(final_df[conn.pred_col])
        except Exception as e:
            logger.error(f"Error in calculating Zscore and F-score: {e}")     


        final_df = pd.merge(final_df, dep[[conn.name_col,'model_year',conn.training_date_col]], left_on=conn.isin_col, right_on=conn.name_col, how='left')
        mask = final_df['model_identifier'] != config['sector_avg_identifier']
        final_df.loc[mask, 'model_identifier'] = (
        final_df.loc[mask, conn.isin_col].astype(str) + "_" +
        final_df.loc[mask, 'model_year'].astype(str) + "_" +
        pd.to_datetime(final_df.loc[mask, conn.training_date_col], format='%Y-%m-%d_%H:%M:%S').dt.strftime('%Y-%m-%d_%H:%M:%S')
    )
        final_df.drop(columns=[conn.name_col,'model_year',conn.training_date_col], inplace=True)

        if len(nan_dict)>0:  
    #             final_df = final_df.parallel_apply(lambda row: conn.revert_nan(row, nan_dict), axis=1)
            rows = final_df.to_dict(orient="records")
            with ThreadPoolExecutor() as executor:
                results = list(executor.map(lambda row: conn.revert_nan(row, nan_dict), rows))
            final_df = pd.DataFrame(results)  

        final_df['ER_Copied'] = final_df['ER_Copied'].replace(0,False)
        try:     
            if env == 'prod':
                conn.s3_client.write_advanced_as_df(final_df, conn.daily_run_bucket_name, conn.daily_run_file.replace('date',new_date_string))
            else:    
                #for testing purpose            
                conn.s3_client.write_advanced_as_df(final_df, conn.bucket_name,
                                                conn.daily_run_file_temp.replace('date',new_date_string))                 
            logger.info("Successfully saved the final data to S3") 
        except Exception as e:
            logger.error(f"Error in saving final data to S3 for {tag} {schedular}: {e}") 
            try:
                conn.failure_mail(f"Error in saving final data to S3 for {tag} {schedular}: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
            except:
                logger.error(f"Error in saving final data to S3 for {tag} {schedular} and error in sending failure mail")    
            raise SystemExit('Stop right there')
       
        if env == 'prod':
            s3_version_bucket_name = conn.s3_versioning_bucket
            s3_version_filename = conn.s3_versioning_pred_file.replace('date',str(new_date_string)) 
        else:
            s3_version_bucket_name = config['s3_paths']['test_s3_versioning_bucket']
            s3_version_filename = conn.s3_versioning_pred_file.replace('date',str(new_date_string))
        try:                      
            #final_df.parallel_apply(lambda row: conn.save_row(row.name, row, s3_version_bucket_name, s3_version_filename), axis=1)
            rows = [(idx, row, s3_version_bucket_name, s3_version_filename) for idx, row in final_df.iterrows()]
            with ThreadPoolExecutor() as executor:
                executor.map(lambda args: conn.save_row(*args), rows)
        except Exception as e:
             logger.info(f"Error in saving the data to S3 versioning path: {e}")   
             try:
                conn.failure_mail(f"Error in saving the data to S3 versioning path: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
             except:
                logger.error("Error in saving the data to S3 versioning path and error in sending failure mail")  

        n_jobs = pd.DataFrame(columns=ph.auto_ai_jobs_cols, data=[[(datetime.today().date()), stt, ett, ph.job_count, ph.job_failed, ph.job_success]])
        if int(ph.job_failed)>int(config['job_fail_threshold']):
            try:
                conn.job_failure_mail(ph.job_failed,tag.upper(),schedular.capitalize())
            except:
                 logger.error(f"An unusual increase in job failures has been detected in the Financial model run for {conn.schedular}. A total of {ph.job_failed} jobs have failed. Failed to send mail.")                   

        try:
            n_job_file = conn.s3_client.read_as_dataframe(ph.autoai_job_bucket,conn.jobcount_filename)
        except:
            n_job_file = pd.DataFrame()


        n_job_file_new = pd.concat([n_job_file, n_jobs])


        try:
            conn.s3_client.write_advanced_as_df(n_job_file_new, ph.autoai_job_bucket,conn.jobcount_filename)
            logger.info("Successfully saved the job count data to S3") 
        except Exception as e:
            logger.error(f"Error in saving the job count data to S3: {e}")        


        if env == 'prod':
            client = es_config(env='prod') 
        else:    
            #for testing purpose 
            client = es_config(env='pre')
        final_df=final_df[final_df[conn.isin_col].isin(combined_df[conn.isin_col].tolist())]                     
        final_df=final_df.drop_duplicates(subset=[conn.isin_col]).reset_index(drop=True)
        if final_df.empty:
            raise Exception('File is empty!')
        final_df.replace([np.inf,-np.inf],np.nan,inplace=True)
        final_df['schedular']=conn.schedular.capitalize()
        final_df['updated_at']= datetime.now()
        if env == 'prod':               
            x=client.save_records_v2(final_df,conn.fin_index,refresh=True)
        else:    
            #for testing purpose                 
            x=client.save_records_v2(final_df,conn.pre_fin_index,refresh=True)                     


        if len(x)>0:
            logger.error(f"Error in saving final data for {tag} {schedular} to ES")
            try:
                conn.failure_mail(f"Error in saving final data for {tag} {schedular} to ES", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
            except:
                logger.error(f"Error in saving final data for {tag} {schedular} to ES and error in sending failure mail")
            raise SystemExit('Stop right there')
        else:
            logger.info(f"Successfully saved data for {tag} {schedular} to ES")
    else:
        final_df = pd.DataFrame(columns=ph.input_columns+[conn.pred_col, 'model_identifier' ])
        logger.info("Data not collected for any isins")

    try:
        if env == 'prod':
            receivers_list = conn.receiver_emails
        else:    
            #for testing purpose           
            receivers_list = conn.sender_email  
        subject = f'Financial {tag.upper()} {schedular.capitalize()} model daily run has been completed'
        message_text = f"""
    <html>
    <body>
    <p>Financial {tag.upper()} {schedular.capitalize()} model run for {str(pd.to_datetime(today).date())} has been successfully completed.</p>

    <p><strong>Summary:</strong><br>
      &nbsp;&nbsp;&nbsp;- Total Number of ISINs: {masters_isin_list['isin'].nunique()}<br>
      &nbsp;&nbsp;&nbsp;- Number of Successful Runs: {final_df['predictions'].notna().sum()}<br>
      &nbsp;&nbsp;&nbsp;- Number of Failed Runs: {final_df['predictions'].isna().sum()}<br>  
      &nbsp;&nbsp;&nbsp;- Number of isins not traded: {int(masters_isin_list['isin'].nunique()) - int(final_df['isin'].nunique())}<br>
      &nbsp;&nbsp;&nbsp;- Number of ISINs with 0 as Prediction: {final_df[final_df['predictions'] == 0]['isin'].nunique()}<br>
      &nbsp;&nbsp;&nbsp;- Number of isins with predictions from individual model: {int(final_df.apply(lambda row: str(row['isin']) in str(row['model_identifier']), axis=1).sum())}<br>
      &nbsp;&nbsp;&nbsp;- Number of isins with predictions from sector-based model: {int(final_df.apply(
    lambda row: str(row['isin']) not in str(row['model_identifier']) and str(row['model_identifier']) != config['sector_avg_identifier'] 
    if pd.notnull(row['isin']) and pd.notnull(row['model_identifier']) 
    else False,
    axis=1
    ).sum())}<br>
      &nbsp;&nbsp;&nbsp;- Number of isins with predictions from sector-average: {int(final_df['model_identifier'].apply(lambda x: str(x) == config['sector_avg_identifier']).sum())}<br>   
    </p>

    <p>Records for {final_df['isin'].nunique()} ISINs have been added to Elasticsearch.</p>
    </body>
    </html>
    """
        conn.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)
    except Exception as e:
        print(e)
        logger.error(f"Error in sending mail regarding run completion: {e}")

  
    logger.info(f"Completed the run for {tag} {schedular}")
    conn.s3_client.upload_file(log_file_path, conn.bucket_name, conn.logs_filename.replace('date',str(datetime.now())))
except Exception as e:
    logger.error(f"Error in the overall script for prediction, {traceback.format_exc()}")
    try:                       
        conn.failure_mail(f"Error in the overall script, {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
    except Exception as e:
        logger.error(f"Error in the overall script for prediction and error in sending failure mail, {e}")                       
    raise SystemExit('Stop right there')






