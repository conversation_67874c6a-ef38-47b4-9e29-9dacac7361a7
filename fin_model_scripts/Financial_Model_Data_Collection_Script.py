#!/usr/bin/env python
# coding: utf-8


import argparse
import os
import sys
from Imports_Modified import *


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='Finance Prod Script', description="<PERSON>ript to run data_collection with two parameters.")
    parser.add_argument('-t', '--tag')
    parser.add_argument('-s', '--schedular')
    
    args = parser.parse_args()
    
    schedular = args.schedular
    tag = args.tag


try:
    if '__file__' in globals():
        script_dir = os.path.dirname(os.path.abspath(__file__))
    else:
        # Default to current working directory
        script_dir = os.getcwd()
    sys.path.insert(0, os.path.abspath(script_dir))
    config_path = os.path.join(script_dir, 'config_modified.yaml')  # Path to config.yaml
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)

    if tag.lower() == config['tags']['aieq_tag'].lower():
        if schedular.lower() == config['schedulars']['schedular_m'].lower():
            conn = AieqMonthlyHelper(tag.lower())
        elif schedular.lower() == config['schedulars']['schedular_d'].lower():
            conn = AieqDailyHelper(tag.lower()) 
    elif tag.lower() == config['tags']['aigo_tag'].lower():
            conn = AigoDataHelper(tag.lower())     
    elif tag.lower() == config['tags']['indiat1_tag'].lower():
            conn = IndiaDataHelper(tag.lower())

    def create_logger(log_file_path):
        log_dir = os.path.dirname(log_file_path)
        print(log_dir)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)  # Create the directory if it doesn't exist
        logger = logging.getLogger('my_logger')
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            logger.removeHandler(handler)
        file_handler = logging.FileHandler(log_file_path, mode='w')  # Use mode='w' for write mode
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger


    env = config['environments']['prod_env']
    print(env)
    num = config['n_bday']
    current_date = date.today()
    saved_date = (current_date - BDay(num)).date()
    log_file_path = config['logger_path'].replace('folder',script_dir).replace('date',str(saved_date)).replace('tag',conn.tag.capitalize())
    global logger
    logger = create_logger(log_file_path)


    today = (datetime.today().date()-BDay(num)).strftime("%m/%d/%Y")
    date_string = (pd.to_datetime(today)+timedelta(1)).strftime("%Y-%m-%d")
    logger.info(f"Data collection run for {today}")
    
    es_prod = es_config(env='prod')
    es_pre_prod = es_config(env='pre')

    if env == 'prod':
        receivers_list = config['email_credentials']['receiver_emails_for_trigger']
    else:
        #for testing purpose
        receivers_list = conn.sender_email
    try:  
        subject = f'Financial model {tag.upper()} {schedular.capitalize()} Data Collection run started'
        message_text = f"Financial model {tag.upper()} {schedular.capitalize()} Data Collection run for {str(pd.to_datetime(today).date())} has started"
        conn.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)
    except:
         logger.error("Error in sending starting mail")

    conn.log_file_path = log_file_path

    try:    
        masters_isin_list = conn.get_isin_list()
    except Exception as e:
        logger.error(f"Error in getting ISIN list: {e}")
        try:
            conn.failure_mail(f"Error in getting ISIN list: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
        except:
            logger.error("Error in getting ISIN list and error in sending failure mail")      
        raise SystemExit('Stop right there')   

    isins_df = masters_isin_list.copy()
    
    logger.info(f"Number of ISINs in the run: {len(isins_df)}")
    if tag.lower() != config['tags']['aigo_tag'].lower():
        isins_df['country_code'] = conn.country_code
    else:
        for cntry, code in config['aigo_macro_ccd_changes'].items():
            isins_df.loc[isins_df['country_code'] == cntry, 'country_code'] = code
    
    data_present = False
    if tag.lower() == config['tags']['aieq_tag'].lower():       
        try:
            isin_list = isins_df['isin'].tolist()
            start_date = (pd.to_datetime(today) - timedelta(days=1)).isoformat()
            end_date = (pd.to_datetime(today)).isoformat()
            if schedular.lower() == config['schedulars']['schedular_m'].lower():
                other_schedular = config['schedulars']['schedular_d']
            if schedular.lower() == config['schedulars']['schedular_d'].lower():
                other_schedular = config['schedulars']['schedular_m']    
            q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date,
                          "lte": end_date}}},{"term": {"schedular.keyword": other_schedular.capitalize()}}]}}}
            if env == 'prod':                   
                es_data = conn.get_es_data(es_prod, start_date, end_date, isin_list, q_total, conn.fin_index)
            else:     
                #for testing purpose                
                es_data = conn.get_es_data(es_pre_prod, start_date, end_date, isin_list, q_total, conn.pre_fin_index)
            es_today_data =  es_data[es_data[conn.date_col]==pd.to_datetime(today)]  
            if len(es_today_data)!=0:
                logger.info(f"Data present for the other schedular {other_schedular}. So using that")
                data_present = True  
        except Exception as e:    
            logger.error(f"Error in reading input data from ES for the other schedular schedular: {e}")         
    
    if data_present == False:   
        try:  
            non_cs_companies = isins_df[isins_df['fin_src']!=config['fin_src_cs']]
            non_cs_companies.reset_index(drop=True, inplace=True)
            cs_companies = isins_df[~isins_df['isin'].isin(non_cs_companies['isin'])]
            cs_companies.reset_index(drop=True, inplace=True)
            non_cs_companies.drop(columns=['fin_src'], inplace=True)
            cs_companies.drop(columns = ['fin_src'], inplace=True)
        except Exception as e:
            logger.error(f"Error in separating CS and Non-CS companies: {e}")
            try:
                conn.failure_mail(f"Error in separating CS and Non-CS companies: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
            except:
                logger.error("Error in separating CS and Non-CS companies and error in sending failure mail")      
            raise SystemExit('Stop right there')

        try:    
            non_cs_companies['mnemonic_list_d'] = [conn.daily_non_cs_mnems for i in non_cs_companies.index]
            non_cs_companies['mnemonic_list_q'] = [conn.q_non_cs_mnems for i in non_cs_companies.index]
            cs_companies['mnemonic_list_d'] = [conn.daily_cs_mnems for i in cs_companies.index]
            cs_companies['mnemonic_list_q'] = [conn.q_cs_mnems for i in cs_companies.index]
            non_cs_companies1 = non_cs_companies[conn.id_and_mnemonic_cols]
            cs_companies1 = cs_companies[conn.id_and_mnemonic_cols]
        except Exception as e:
            logger.error(f"Error in assigning capiq mnemonics to companies: {e}")
            try:
                conn.failure_mail(f"Error in assigning capiq mnemonics to companies: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
            except:
                logger.error("Error in assigning capiq mnemonics to companies and error in sending failure mail")    
            raise SystemExit('Stop right there')

    
        cds = isins_df['country_code'].unique() 

        # Phase data
        phase_data = conn.get_phase_data(today)

        # Phase model predictions for daily schedular
        if (tag.lower() == config['tags']['aieq_tag'].lower()) & (schedular.capitalize() == config['schedulars']['schedular_d']):
            phase_data_dict = conn.get_phase_model_data(today)
            phase_country_df = pd.DataFrame.from_dict({
            conn.date_col: [pd.to_datetime(list(phase_data_dict.values())[0]['date'].iloc[0])],
            'country_code': [conn.country_code],
            'vlue_er': [phase_data_dict['vlue']['predictions'].iloc[0]],
            'mtum_er': [phase_data_dict['mtum']['predictions'].iloc[0]],
            'iwf_er': [phase_data_dict['iwf']['predictions'].iloc[0]],
            'sphq_er': [phase_data_dict['sphq']['predictions'].iloc[0]],
            'vlue_corr_er': [phase_data_dict['vlue_corr']['predictions'].iloc[0]],
            'mtum_corr_er': [phase_data_dict['mtum_corr']['predictions'].iloc[0]],
            'iwf_corr_er': [phase_data_dict['iwf_corr']['predictions'].iloc[0]],
            'sphq_corr_er': [phase_data_dict['sphq_corr']['predictions'].iloc[0]]
        })
            if pd.to_datetime(today) not in pd.to_datetime(phase_country_df['date'].values):
                phase_country_df.at[len(phase_country_df), 'date'] = pd.to_datetime(today)
            drop_cols = set(list(phase_country_df.columns))-{'date','country_code'}     
            phase_data.drop(columns=drop_cols, inplace=True) 
            phase_data = pd.merge(phase_data, phase_country_df, on=['date','country_code'], how='left')

        # Macro data
        macros = conn.get_macro_data(today, cds)

        # benchmark
        benchmark = conn.get_benchmark_data(cds, today)

        # Technical Indicators
        try:
            tec=conn.s3_client.read_as_dataframe(conn.tech_ind_bucket, conn.tech_ind_filename.replace('date',date_string)) 
            tec=tec[conn.tech_cols+[conn.date_col]]
        except Exception as e:    
            logger.error(f"Error in fetching technical indicators: {e}") 

        try:
            isin_list = isins_df['isin'].tolist()
            start_date = (pd.to_datetime(today) - timedelta(days=config['look_back_period'])).isoformat()
            end_date = (pd.to_datetime(today)-timedelta(1)).isoformat()
            q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date,
                          "lte": end_date}}},{"term": {"schedular.keyword": conn.schedular.capitalize()}}]}}}
            hist_data = conn.get_es_data(es_prod, start_date, end_date, isin_list, q_total, conn.fin_index)
        except Exception as e:    
            logger.error(f"Error in fetching previous data for forward filling: {e}")        

        try:
            datetime_date=datetime.strptime(today, '%m/%d/%Y')
            current_month = datetime_date.month
            quarter_start_month = (current_month - 1) // 3 * 3 + 1
            first_date = datetime(datetime_date.year, quarter_start_month, 1) + BDay(0)
            first_date = first_date.strftime("%m/%d/%Y")
        except Exception as e:    
            logger.error(f"Error in finding the first date of the quarter: {e}") 

        try:
            cq_trained_list = conn.s3_client.read_as_dataframe(conn.bucket_name, conn.cq_trained_list_file)['isin'].unique().tolist()
        except:
            cq_trained_list = []
        if len(cq_trained_list)>0:    
            cq_trained_list = [x for x in cq_trained_list if pd.notna(x)]

        nan_dict={}
        total_df=pd.DataFrame()
        all_companies_data=[]

        def single_company_data(p, fin_src):
            try:
                if fin_src == config['fin_src_cs']:
                    company = pd.DataFrame(cs_companies1.iloc[p]).T
                else:
                    company = pd.DataFrame(non_cs_companies1.iloc[p]).T
                df = pd.DataFrame(index=range(1))
                isin = company.iloc[0][conn.isin_col]
                exchange = company[conn.exchange_col].values[0]
                country_code = isins_df.loc[isins_df['isin'] == isin, 'country_code'].values[0]
                single_date_response = conn.get_capiq_data_as_of_date(company.values[0].tolist(), today)[0]
                prev_daily_isin = hist_data[hist_data[conn.isin_col]==isin]
                if len(prev_daily_isin)>0:
                    prev_daily_isin[conn.date_col] = pd.to_datetime(prev_daily_isin[conn.date_col]) 
                    prev_daily_isin=prev_daily_isin.sort_values(conn.date_col)
                    prev_daily = prev_daily_isin.ffill()
                    prev_daily = prev_daily.iloc[[-1]]
                else:
                    prev_daily = prev_daily_isin.copy()  

                # capiq daily datapoints
                df, security_traded = conn.capiq_asofdate_data_process(df, single_date_response, isin, fin_src, today)
                if not security_traded:
                    return
                if df is None or df.empty:
                    df[conn.date_col] = today
                    df[config['columns']['daily_cols']] = np.nan
                else:   
                    for col in config['columns']['daily_cols']:
                        if col not in df.columns:
                            df[col] = np.nan
                    if exchange == config['lse_exchange']:
                        df['closeprice'] = df['closeprice'].astype(float) * 100
                        df[['volume', 'marketcap', 'sharesoutstanding']] = df[['volume', 'marketcap', 'sharesoutstanding']].astype(float) * 1000000       
                df[conn.isin_col] = company[conn.isin_col].iloc[0]
                df[conn.tic_col] = isins_df[isins_df[conn.isin_col]==isin][conn.tic_col].values[0]
                col = df.pop(conn.isin_col)
                df.insert(1, conn.isin_col, col)
                
                # capiq quarterly datapoints
                nan_columns = []
                if pd.to_datetime(today).strftime("%A") == config['weekday']:
                    df, nan_columns = conn.capiq_cq_data_process(company, df, nan_columns, isin, fin_src)
                else:
                    q_cols = conn.quarterly_cols
                    try:
                        q_data_isin = prev_daily[prev_daily[conn.isin_col]==isin].reset_index()
                        df[q_cols] = q_data_isin[q_cols]
                    except:
                        logger.info(f"{isin} not present in previous days file")
                        df[q_cols]=0

                file_name = df[conn.isin_col].values[0]
                df[conn.date_col] = pd.to_datetime(df[conn.date_col])

                # macro economic data
                try:
                    if country_code not in macros.keys():
                        try:
                            ctry_cd = proxy_mapping[country_code]
                        except:
                            ctry_cd = config['aieq_country_code']
                    else:
                        ctry_cd = country_code
                    macro = macros[ctry_cd]
                    macro[conn.date_col]=pd.to_datetime(macro[conn.date_col])
                    macro = macro.sort_values(by=[conn.date_col])
                    macro1 = macro[conn.macro_cols+[conn.date_col]]
                    df = pd.merge(df,macro1,on=conn.date_col,how='left')
                except Exception as e:
                    df[conn.macro_cols] = np.nan
                    logger.info(f"Error in fetching Macro data for the ISIN: {isin}, {e}. It will be forward filled for ISIN: {isin}")

                # beta data    
                try:
                    beta_cols = conn.beta_cols
                    if fin_src == config['fin_src_cs']: 
                        betas = conn.get_beta_for_date(cs_companies1.iloc[p][[conn.isin_col, conn.tic_col, config['columns']['exchange_col']]], first_date, id_type = conn.tic_col)
                    else:
                        betas = conn.get_beta_for_date(non_cs_companies1.iloc[p][[conn.isin_col, conn.tic_col, config['columns']['exchange_col']]], first_date, id_type = conn.tic_col)
                    beta_df = pd.DataFrame([betas])
                    beta_df = beta_df.replace('Data Unavailable', np.nan)
                    beta_df = beta_df.replace('CapabilityNeeded', np.nan)
                    beta_df = beta_df.replace('NaN', np.nan)
                    beta_df[conn.date_col] = pd.to_datetime(today)
                    df = pd.merge(df,beta_df,on=conn.date_col,how='left')
                    nan_columns_beta = df[beta_cols].columns[df[beta_cols].isna().any()].tolist()
                    if len(nan_columns_beta)>0:
                        logger.info(f"Beta will be forward filled for ISIN {isin} for the columns {nan_columns_beta}")
                except Exception as e:
                    df[beta_cols] = np.nan
                    logger.info(f"Error in fetching Beta for the ISIN: {isin}, {e}. It will be forward filled for ISIN: {isin}")        

                # momentum data
                try:
                    mom = conn.momentum(isin, today)
                    mom = mom[conn.momentum_cols+[conn.date_col]]
                    df = pd.merge(df,mom,on=conn.date_col,how='left')
                except Exception as e:
                    logger.error(f"Error in fetching Momentum for the ISIN: {isin}, {e}") 
                    return

                # dcf data
                try:
                    if (isin in cq_trained_list) or ('ind' in tag):
                        dcf_index = conn.dcf_new_index
                    else:
                        dcf_index = conn.dcf_old_index
                    dcf_df = conn.dcf(isin, dcf_index, today)
                    dcf_df = dcf_df[conn.dcf_cols+[conn.date_col]]
                    df = pd.merge(df,dcf_df,on=conn.date_col,how='left')
                except Exception as e:
                    logger.error(f"Error in fetching DCF for the ISIN: {isin}, {e}")
                    return

                # benchmark data
                try:
                    if country_code not in benchmark.keys():
                        try:
                            ctry_cd = proxy_mapping[country_code]
                        except:
                            ctry_cd = config['aieq_country_code']
                    else:
                        ctry_cd = country_code
                    benchmark_data = benchmark[ctry_cd].reset_index(drop=False)
        #             benchmark_data.ffill(inplace = True)
                    df = pd.merge(df, benchmark_data, on=conn.date_col, how='left')
                except Exception as e:
                    logger.error(f"Error in fetching Benchmark data for the ISIN: {isin}, {e}") 

                # technical indicators data    
                try:
                    tec_isin = tec[tec[conn.isin_col]==isin][conn.tech_cols+[conn.date_col]]
                    tec_isin[conn.date_col] = pd.to_datetime(tec_isin[conn.date_col])
                    tec_isin.drop(conn.isin_col,axis=1,inplace=True)
                    df = pd.merge(df,tec_isin,on=conn.date_col,how='left')
                except Exception as e:
                    df[list(set(conn.tech_cols) - {'isin'})] = np.nan
                    logger.error(f"Error in fetching Technical Indicators for the ISIN: {isin}, {e}. It will be ffilled.")

                # on-the-fly calculations  
                after_calc = conn.calculations_process(company, country_code, phase_data, prev_daily_isin, today, isin)
                if after_calc is None or after_calc.empty:
                    df[conn.calc_cols+conn.phase_model_cols] = np.nan
                else:
                    df = pd.merge(df,after_calc,on=conn.date_col,how='left')
                try:
                    df[config['columns']['equity_value_per_share_col']] = df[config['columns']['total_common_equity_col']].astype(float) / df[config['columns']['sharesoutstanding_col']].astype(float)
                except Exception as e:
                    logger.error(f"Error in calculating equity_value_per_share for the ISIN: {isin}, {e}. It will be ffilled.")
                    df[config['columns']['equity_value_per_share_col']] = np.nan 
                try:
                    df=df.drop(columns=[config['columns']['cp_adj_col']])
                except:
                    pass
                df = df.replace('Data Unavailable', np.nan)
                df = df.replace('CapabilityNeeded', np.nan)
                df = df.replace('NaN', np.nan)
                df = df.rename(columns=config['columns']['renamings'])
                nan_columns.extend(df.columns[df.isna().any()].tolist())
                nan_columns = list(set(nan_columns))
                if len(nan_columns)>0:
                    nan_dict[isin] = nan_columns
                    logger.info(f"Columns for which values are getting ffilled for {isin}: {nan_columns}")
                return df
            except Exception as e:
                logger.error(f"Error in the main function for the ISIN: {isin}, {traceback.format_exc()}")   



        list_index_non_cs = [i for i in range(len(non_cs_companies1))]
        if len(list_index_non_cs)>0:
            ts = time.time()
            logger.info("Starting data collection for Non-CS companies")
            with ThreadPoolExecutor(max_workers=50) as executor:
                futures = [executor.submit(single_company_data, index, config['fin_src_ciq'])  # Pass "CS" directly
                   for index in list_index_non_cs]

                result_list = []
                for future in futures:
                    try:
                        result_list.append(future.result())
                    except Exception as e:
                        logger.error(f"Error in processing data: {e}")

            td = time.time()
            logger.info(f'Time taken for collecting and combining data for Non-CS companies: {td - ts}')
            try:
                non_cs_df = pd.concat(result_list) if result_list else pd.DataFrame()
            except:
                logger.info("No Non-CS companies got data")
                non_cs_df = pd.DataFrame(columns=conn.input_cols)
        else:
            logger.info("No Non-CS companies in the ISIN list")
            non_cs_df = pd.DataFrame(columns=conn.input_cols)

        list_index_cs = [i for i in range(len(cs_companies1))]
        if len(list_index_cs)>0:
            ts = time.time()
            logger.info("Starting data collection for CS companies")
            with ThreadPoolExecutor(max_workers=50) as executor:
                futures = [executor.submit(single_company_data, index, config['fin_src_cs'])  # Pass "CS" directly
                   for index in list_index_cs]

                result_list = []
                for future in futures:
                    try:
                        result_list.append(future.result())
                    except Exception as e:
                        logger.error(f"Error in processing data: {e}")

            td = time.time()
            logger.info(f'Time taken for collecting and combining data for CS companies: {td - ts}')
            try:
                cs_df = pd.concat(result_list) if result_list else pd.DataFrame()
            except:
                logger.info("No CS companies got data")
                cs_df = pd.DataFrame(columns=conn.input_cols)
        else:
            logger.info("No CS companies in the ISIN list")
            cs_df = pd.DataFrame(columns=conn.input_cols)

        total_df = pd.concat([non_cs_df,cs_df],axis=0)

        # Rerunning for the ISINs failed during data collection
        failed_data_collection_cs = cs_companies1[~cs_companies1[conn.isin_col].isin(total_df[conn.isin_col])]
        if not failed_data_collection_cs.empty:
            logger.info(f'Rerunning for the failed CS ISINs')

            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(single_company_data, ind, config['fin_src_cs']) 
                           for ind in failed_data_collection_cs.index]

            result_list = []
            for future in futures:
                try:
                    result_list.append(future.result())
                except Exception as e:
                    logger.error(f"Error in processing data in the retry for CS: {e}")

            if result_list:
                total_df = pd.concat([total_df] + result_list, axis=0)            


        failed_data_collection_non_cs = non_cs_companies1[~non_cs_companies1[conn.isin_col].isin(total_df[conn.isin_col])]
        if not failed_data_collection_non_cs.empty:
            logger.info(f'Rerunning for the failed Non-CS ISINs')

            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(single_company_data, ind, config['fin_src_ciq']) 
                           for ind in failed_data_collection_non_cs.index]

            result_list = []
            for future in futures:
                try:
                    result_list.append(future.result())
                except Exception as e:
                    logger.error(f"Error in processing data in the retry for CIQ: {e}")

            if result_list:
                total_df = pd.concat([total_df] + result_list, axis=0)

        #     raise SystemExit('Stop right there')
        total_df = total_df.replace(np.inf,np.nan)
        total_df = total_df.replace(-np.inf,np.nan)

        data_fetch_failed_isins = isins_df[~isins_df[conn.isin_col].isin(total_df[conn.isin_col].tolist())][conn.isin_col].tolist()
        if len(data_fetch_failed_isins)>0:
            logger.info("Following ISINs have failed during data collection:")
            for isin in data_fetch_failed_isins:
                logger.info(f"{isin}")


        if len(nan_dict)>0:
            rows = total_df.to_dict(orient="records")
            with ThreadPoolExecutor() as executor:
                results = list(executor.map(lambda row: conn.revert_nan(row, nan_dict), rows))
            final_df_nan = pd.DataFrame(results)    
            final_df_nan[conn.date_col] = pd.to_datetime(final_df_nan[conn.date_col])
        else:
            final_df_nan = total_df.copy()
            
    else:
        try:
            for col in config['columns']['cols_to_drop_from_monthly_data']:
                if col in es_data.columns.tolist():
                    es_today_data.drop(columns=col, inplace=True)
            es_today_data = es_today_data[es_today_data[conn.date_col]==pd.to_datetime(today)]
            es_today_data[conn.date_col]=es_today_data[conn.date_col].dt.strftime('%Y-%m-%d')
            es_today_data.reset_index(drop=True, inplace=True)
            
            # changing phase model prediction
            phase_model_dictionary = conn.phase_model_dict
            start_date = (pd.to_datetime(today) - timedelta(days=config['look_back_period'])).isoformat()
            end_date = (pd.to_datetime(today)).isoformat()
            country = conn.aieq_country_code


            phase_data_dict = {}
            for model in phase_model_dictionary.keys():
                try:
                    q_total = {"query": {"bool": {"must": [{"bool": {"should": [{"match": {"id": phase_model_dictionary[model].replace('country', country)}}]}},
                            {"range": {"date": {"gte": start_date,"lte": end_date}}},
                            {"term": {"schedular.keyword": conn.schedular.capitalize()}}]}}}

                    model_data = conn.get_es_data(es_prod, start_date, end_date, None, q_total, conn.phase_model_index).iloc[[-1]]
                    phase_data_dict[model] = model_data
                except Exception as e:    
                    logger.error(f"Error in fetching phase model data for {model}: {e}")
                    try:               
                        conn.failure_mail(f"Error in fetching phase model data for {model}: {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
                    except:
                        logger.error(f"Error in fetching phase model data for {model} and error in sending failure mail")                 
                    raise SystemExit('Stop right there')

                
            try:
                es_today_data['iwf_corr_er']= phase_data_dict['iwf_corr']['predictions'].values[0]
                es_today_data['iwf_er']= phase_data_dict['iwf']['predictions'].values[0]
                es_today_data['mtum_corr_er']= phase_data_dict['mtum_corr']['predictions'].values[0]
                es_today_data['mtum_er']= phase_data_dict['mtum']['predictions'].values[0]
                es_today_data['sphq_corr_er']= phase_data_dict['sphq_corr']['predictions'].values[0]
                es_today_data['sphq_er']= phase_data_dict['sphq']['predictions'].values[0]
                es_today_data['vlue_corr_er']= phase_data_dict['vlue_corr']['predictions'].values[0]
                es_today_data['vlue_er']= phase_data_dict['vlue']['predictions'].values[0]
            except Exception as e:
                logger.error(f"Error in assigning the phase daily model columns: {e}")     
            
            final_df_nan = es_today_data.copy()
        except Exception as e:    
            logger.error(f"Error in preparing input data from the other schedular data: {e}") 
            try:                   
                conn.failure_mail(f"Error in preparing input data from the other schedular data, {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
            except:
                logger.error("Error in preparing input data from the other schedular data and error in sending failure mail")                    
            raise SystemExit('Stop right there') 
     
    final_df_nan = final_df_nan[conn.input_cols]
        
    final_df_nan.loc[:, ~final_df_nan.columns.isin([conn.date_col,conn.isin_col,conn.tic_col])] = final_df_nan.loc[:, ~final_df_nan.columns.isin([conn.date_col,conn.isin_col,conn.tic_col])].astype(float)

    today = (datetime.today().date()-BDay(num)).strftime("%m/%d/%Y")
    final_df_nan[conn.date_col] = pd.to_datetime(today)   
    
    if len(final_df_nan)==0:
        try:
            receiver_list = [conn.sender_email]
            subject = f'Financial {tag.upper()} {schedular.capitalize()} model data collection run has been completed - No Data Available'
            message_text = f"Financial {tag.upper()} {schedular.capitalize()} model data collection for {str(pd.to_datetime(today).date())} has been successfully completed. But no data got collected."
            conn.SendMessage(config['sender_name'], receiver_list, subject, None, message_text, None)
        except Exception as e:
            logger.error(f"Error in sending mail regarding data collection completion: {e}")    
    else:        
        # saving raw data to s3
        try:
            try:
                if env == 'prod':
                    existing = conn.s3_client.read_as_dataframe(conn.bucket_name,conn.raw_data_file.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d")))
                    existing[conn.date_col] = pd.to_datetime(existing[conn.date_col])
                #for testing purpose
                else:
                    existing = conn.s3_client.read_as_dataframe(conn.bucket_name,conn.raw_data_file_temp.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d")))
                    existing[conn.date_col] = pd.to_datetime(existing[conn.date_col])
            except:
                existing = pd.DataFrame()                     
            total_df = pd.concat([existing, final_df_nan])  
            total_df.drop_duplicates([conn.isin_col],keep='last', inplace=True) 
            if env == 'prod':
                conn.s3_client.write_advanced_as_df(total_df,conn.bucket_name,conn.raw_data_file.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d")))
            else:
                #for testing purpose  
                conn.s3_client.write_advanced_as_df(total_df,conn.bucket_name,conn.raw_data_file_temp.replace('date',(pd.to_datetime(today)).strftime("%Y-%m-%d"))) 

        except Exception as e:
            logger.error(f"Error in saving input data to S3: {e}")


        #saving raw data to es
        if env == 'prod':    
            client = es_config(env='prod') 
        else:
            #for testing purpose
            client = es_config(env='pre')
        total_df = final_df_nan.drop_duplicates(subset=[conn.isin_col]).reset_index(drop=True)
        if total_df.empty:
            raise Exception('File is empty!')
        total_df[conn.date_col] = pd.to_datetime(total_df[conn.date_col])
        total_df[conn.date_col] = total_df[conn.date_col].dt.date
        total_df.replace([np.inf,-np.inf],np.nan,inplace=True)
        total_df['schedular'] = conn.schedular.capitalize()
        total_df['updated_at'] = datetime.now()
        if env == 'prod':
            x=client.save_records_v2(total_df,conn.fin_index,refresh=True) 
        else:    
            #for testing purpose                 
            x = client.save_records_v2(total_df,conn.pre_fin_index,refresh=True)

        if len(x)>0:
            logger.error("Error in saving input data to ES")
            try:
                conn.failure_mail("Error in saving input data to ES", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
            except:
                logger.error("Error in saving input data to ES and error in sending failure mail")    
            raise SystemExit('Stop right there')
        else:
            logger.info("Successfully saved input data to ES")

        try:
            receiver_list = [conn.sender_email]
            subject = f'Financial {tag.upper()} {schedular.capitalize()} model data collection run has been completed'
            message_text = f"Financial {tag.upper()} {schedular.capitalize()} model data collection for {str(pd.to_datetime(today).date())} has been successfully completed. Records for {total_df['isin'].nunique()} ISINs have been added to Elasticsearch."
            conn.SendMessage(config['sender_name'], receiver_list, subject, None, message_text, None)
        except Exception as e:
            logger.error(f"Error in sending mail regarding data collection completion: {e}")      

    logger.info(f"Completed the data collection for {tag.upper()} {schedular.capitalize()}")
    logger.info(f"#########################################################################")                       
except Exception as e:
    logger.error(f"Error in the overall script for data collection, {traceback.format_exc()}")
    try:                       
        conn.failure_mail(f"Error in the overall script, {e}", conn.tag, conn.schedular, conn.log_file_path, conn.logs_filename, env)
    except Exception as e:
        logger.error(f"Error in the overall script for data collection and error in sending failure mail {e}")                       
    raise SystemExit('Stop right there')






