#!/usr/bin/env python
# coding: utf-8


from concurrent.futures import ThreadPoolExecutor
from datetime import datetime,date,timedelta
from urllib.request import urlopen, Request
from pathlib import Path
from io import StringIO
import io
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
from pandarallel import pandarallel
pandarallel.initialize(progress_bar=True,nb_workers=4)
import numpy as np
import requests
import logging
import boto3
import json
import time
import warnings
import traceback
import yfinance as yf
warnings.filterwarnings("ignore")
from pandas.tseries.offsets import BDay
from multiprocessing.pool import ThreadPool
import os, ssl
from fredapi import Fred
from collections import defaultdict
# from tqdm.notebook import tqdm, IProgress
from tqdm import tqdm
from eq_common_utils.utils.opensearch_helper import OpenSearch
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from ibm_watsonx_ai import APIClient, Credentials
from ibm_watsonx_ai.experiment import AutoAI
from ibm_watsonx_ai.deployment import WebService, Batch
from ibm_watsonx_ai.helpers.connections import DataConnection, S3Location
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from numpy_ext import rolling_apply as rolling_apply_ext
import yaml
import sys
import smtplib
import argparse
from email.mime.text import MIMEText
import base64
from google.oauth2.credentials import Credentials as GoogleCredentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.message import EmailMessage


if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config_modified.yaml', 'r') as file:
    config = yaml.safe_load(file)


global logger
logger = logging.getLogger('my_logger')


class GenericHelper:
    def __init__(self, tag):
        self.tag = tag
        self.s3_client = s3_config()
        self.masters_url = config['url']['masters_url']
        self.spglobal_url = config['snp_creds']['spglobal_url']
        self.head_auth = config['snp_creds']['head_auth']
        self.content_type = config['snp_creds']['content_type']
        self.daily_non_cs_mnems = config['snp_creds']['daily_non_cs_mnems']
        self.daily_cs_mnems = config['snp_creds']['daily_cs_mnems']
        self.q_non_cs_mnems = config['snp_creds']['q_non_cs_mnems']
        self.q_cs_mnems = config['snp_creds']['q_cs_mnems']
        self.macro_api_key = config['macro']['api_key']
        self.macro_env_var = config['macro']['env_var']
        self.old_bucket_name = config['s3_paths']['old_bucket_name']
        self.bucket_name = config['s3_paths']['bucket_name']
        self.calc_mnems = config['snp_creds']['calc_mnems']
        self.beta_mnems = config['snp_creds']['beta_mnems']         
        self.tech_ind_bucket = config['s3_paths']['technical_indicator_bucket']
        self.tech_ind_filename = config['s3_paths']['technical_indicator_filename']
        self.tech_cols = config['columns']['tech_ind']
        self.fin_index = config['index']['fin_es_index']
        self.pre_fin_index = config['index']['preprod_fin_index']
        self.momentum_index = config['index']['momentum_index']
        self.dcf_old_index = config['index']['dcf_old_index']
        self.dcf_new_index = config['index']['dcf_new_index']
        self.phase_model_index = config['index']['phase_model_index']
        self.input_columns = config['columns']['input_cols']
        self.quarterly_cols = config['columns']['quarterly_cols']
        self.daily_cols = config['columns']['daily_cols']
        self.phase_cols = config['columns']['phase_cols']
        self.phase_model_cols = config['columns']['phase_model_cols']
        self.beta_cols = config['columns']['beta_cols']
        self.momentum_cols = config['columns']['momentum_cols']
        self.dcf_cols = config['columns']['dcf_cols']
        self.calc_cols = config['columns']['calc_cols']
        self.cols_to_drop_from_dep = config['columns']['cols_to_drop_from_dep']
        self.cols_to_keep_for_dep = config['columns']['cols_to_keep_for_dep']
        self.id_and_mnemonic_cols = config['columns']['id_and_mnemonic_cols']
        self.date_col = config['columns']['date_col']
        self.isin_col = config['columns']['isin_col']
        self.tic_col = config['columns']['tic_col']
        self.exchange_col = config['columns']['exchange_col']
        self.sender_email = config['email_credentials']['sender_email']
        self.receiver_emails = config['email_credentials']['receiver_emails']
        self.smtp_credential = config['email_credentials']['smtp_credential']
        self.server_address = config['email_credentials']['server_address']
        self.port_no = config['email_credentials']['port_no']
        self.receiver_emails_for_less_hits = config['email_credentials']['receiver_emails_for_less_hits']
        self.single_date_fn = config['snp_creds']['single_date_fn']
        self.daterange_fn = config['snp_creds']['daterange_fn']  
        self.q_period_type = config['snp_creds']['q_period_type']
        self.resp_key = config['snp_creds']['resp_key']
        self.schedular_m = config['schedulars']['schedular_m']
        self.schedular_d = config['schedulars']['schedular_d']
        self.aieq_country_code = config['aieq_country_code']
        self.deployment_space_col = config['columns']['deployment_space_col']
        self.deployment_id_col = config['columns']['deployment_id_col']
        self.pred_col = config['columns']['pred_col']
        self.status_col = config['columns']['status_col']
        self.name_col = config['columns']['name_col']
        self.year_col = config['columns']['year_col']
        self.training_date_col = config['columns']['training_date_col']
        self.s3_versioning_bucket = config['s3_paths']['s3_versioning_bucket']
        self.s3_versioning_pred_filename_daily = config['s3_paths']['s3_versioning_pred_filename_daily']
        self.s3_versioning_pred_filename_daily_temp = config['s3_paths']['s3_versioning_pred_filename_daily_temp']
        
    
    def get_master_df(self,tag):
        master_json = requests.get(f'{self.masters_url}{tag.lower()}').json()
        master_df = pd.DataFrame(master_json["data"][f"masteractivefirms_{tag.lower()}"])
        return master_df
    
    def get_capiq_data_as_of_date(self, body, today):
        # for getting daily data
        tic, exchange, isin, mnemonic_list_d, mnemonic_list_q, ipodate = body
        identifiers = [f'{tic}:{exchange}', isin]  # List of identifiers to try in order
        final_results = []  # List to store all results
        url = self.spglobal_url
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
        for identifier in identifiers:
            input_list = []
            for mnemonic in mnemonic_list_d:
                body_req = {
                    "function": self.single_date_fn,
                    "identifier": identifier,
                    "mnemonic": mnemonic,
                    "properties": {
                        'asofDate': today
                    }
                }
                input_list.append(body_req)

            # Send the request for all mnemonics together
            data = {"inputRequests": input_list}
            time.sleep(5) 
            data_json = json.dumps(data)
            response = requests.post(url, data=data_json, headers=headers)

            try:
                resp_json = json.loads(response.text)
                if resp_json[self.resp_key][0].get('ErrMsg') == 'InvalidIdentifier':
                    logger.info(f'{identifier} is InvalidIdentifier')
                    continue  # Try the next identifier if this one fails

                elif len(resp_json[self.resp_key][0].get('ErrMsg', '')) > 0:
                    logger.error(f"Error from S&P: {resp_json[self.resp_key][0]['ErrMsg']}, ISIN: {isin}")
                    continue             
                              
                elif resp_json[self.resp_key][0]['Rows'][0]['Row'][0] == 'Data Unavailable':
                    logger.info(f'{identifier} is giving CP as Data Unavailable')
                    continue  # Try the next identifier if data is unavailable
                              
                resp_json[self.isin_col] = isin
                final_results.append(resp_json)  # Store the successful response
                break  # Exit after the first successful batch response

            except Exception as e:
                logger.error(f"Error in getting daily data, {e} with the response from S&P being {response.text}, for the request {input_list}")
                continue  # In case of any exception, try the next identifier

        if len(final_results)>0:
            return final_results
        else:
            logger.error(f"Failed to retrieve daily data with all identifiers and mnemonics for the isin: {isin}")
            return None  # Return None if all identifiers fail for all mnemonics
                                 
    def capiq_asofdate_data_process(self, df, single_date_response, isin, fin_src, today):                             
        try:
            security_traded = True                      
            for i in range(len(single_date_response[self.resp_key])):
                try:
                    single_date_data = []
                    value = single_date_response[self.resp_key][i]['Mnemonic'].split('IQ_')[1].lower()
                    if fin_src == config['fin_src_cs']:
                        if single_date_response[self.resp_key][i]['Mnemonic']==config['pe_excl_fwd_mnem']:
                                value = value.split('_ciq')[0]
                        if '_cs' in value:
                            value = value.split('_cs')[0]
                    data = single_date_response[self.resp_key][i]['Rows'][0]['Row'][0]
                    if value not in  [config['columns']['cp_col'],config['columns']['cp_adj_col']]:
                        if (data in ['Data Unavailable','NaN']):
                            data = np.nan
                    else:    
                        if (data in ['Data Unavailable','NaN']):
                            if pd.to_datetime(today) == pd.to_datetime(datetime.today().date() - BDay(1)):    
                                logger.info('Running for last BDay. So checking last trading date')  
                                cp_reason = self.get_last_trading_date( isin, today)  
                                if not pd.isna(cp_reason):   
                                    logger.info(f"Closeprice is not available for the ISIN and the security was not traded: {isin}")
                                    security_traded = False  
                                else:   
                                    logger.info(f"Closeprice is not available for the ISIN and but security was traded: {isin}")
                                    security_traded = True 
                            else:
                                 logger.info('Running for a hist day. So not checking last trading date') 
                                 security_traded = True
                            data = np.nan 
                    single_date_data.append([single_date_response[self.resp_key][i]['Properties']['asofdate'],
                                             data])
                    df1 = pd.DataFrame(columns=[self.date_col,value],data=single_date_data)
                    df1[self.isin_col]=single_date_response[self.isin_col]
                    if i==0:
                        df = df1.copy()
                    else:
                        df = pd.concat([df,df1[value]],axis=1)
                except Exception as e:
                    logger.error(f"Error in fetching daily datapoints from S&P for the ISIN: {isin}, {value}, {e}")
            return df, security_traded                     
        except Exception as e:
            logger.error(f"Error in getting response for daily datapoints from S&P for the ISIN: {isin}, {e}") 
            return pd.DataFrame(), security_traded  
     
    def get_capiq_data_quarterly_using_CQ(self, body):
        tic, exchange, isin, mnemonic_list_d, mnemonic_list_q, ipodate = body

        identifiers = [f'{tic}:{exchange}', isin]
        url = self.spglobal_url
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
        final_result = []

        for identifier in identifiers:
            input_list = []
            for mnemonic in mnemonic_list_q:
                body_req = {
                    "function": self.single_date_fn,
                    "identifier": identifier,
                    "mnemonic": mnemonic,
                    "properties": {
                        'periodtype': self.q_period_type
                    }
                }
                input_list.append(body_req)
            request_list = [input_list[i:i+config['snp_creds']['single_request_limit']] for i in range(0, len(input_list), config['snp_creds']['single_request_limit'])]
            for req_batch in request_list:
                data = {"inputRequests": req_batch}
                data_json = json.dumps(data)
                time.sleep(2)  # Optional: Sleep to avoid rate limiting
                response = requests.post(url, data=data_json, headers=headers)
                try:
                    resp_json = json.loads(response.text)
                    # Check for InvalidIdentifier error
                    if resp_json[self.resp_key][0].get('ErrMsg') == 'InvalidIdentifier':
                        logger.info(f'{identifier} is InvalidIdentifier')
                        break  # If identifier is invalid, try the next one
                    elif len(resp_json[self.resp_key][0].get('ErrMsg', '')) > 0:
                        logger.error(f"Error: {resp_json[self.resp_key][0]['ErrMsg']}, ISIN: {isin}")
                        continue  # Try the next batch if there's another error
                    resp_json[self.isin_col] = isin
                    final_result.append(resp_json)
                except Exception as e:
                    logger.error(f"Error in getting quarterly data, {e} with the response from S&P being {response.text},for the request {req_batch}")
                    continue
            if len(final_result)>0:
                break  # Stop if we get valid results with the current identifier
        if len(final_result)>0:
            return final_result
        else:
            logger.error(f"Failed to retrieve quarterly data with all identifiers and mnemonics for the isin: {isin}")
            return None  # Return None if all identifiers fail                                   
                                     
    def capiq_cq_data_process(self, company, df, nan_columns, isin, fin_src):
        try:  
            json_re = self.get_capiq_data_quarterly_using_CQ(company.values[0].tolist())                         
            df_re = []
            for i in range(len(json_re)):
                df_re.append(pd.DataFrame(json_re[i][self.resp_key]))
            res_df = pd.concat(df_re)
            res_df.reset_index(drop=True, inplace=True)
            q_group = res_df.groupby('Mnemonic')
            list_of_quarter_data = []
            for name, group in q_group:
                group['Rows'] = group['Rows'].apply(self.convert)
                res = pd.DataFrame(group.iloc[-1]).T
                res.reset_index(drop=True, inplace=True)
                list_of_quarter_data.append(res)
            f_q = pd.concat(list_of_quarter_data)
            f_q.reset_index(drop=True, inplace=True)
        except Exception as e:
            logger.error(f"Error in fetching quarterly datapoints from S&P for the ISIN: {isin}, {e}")
            return None                            
        for m in range(len(f_q)):
            try:
                key=f_q['Mnemonic'].iloc[m].split('IQ_')[1].lower()
                if '_cs' in key:
                    key = key.split('_cs')[0]
                if fin_src != config['fin_src_cs']:        
                    if key == 'sga_suppl':
                        key = 'sga'        
                value = f_q['Rows'].iloc[m]
                df.at[0,key]=value
            except Exception as e:
                logger.error(f"Error in processing quarterly datapoints for the ISIN: {isin}, {e}") 
                return None
        df.rename(columns=config['columns']['sga_rename'], inplace=True) 
        nan_columns.extend(df[[config['columns']['sga_col'], config['columns']['rd_exp_col']]].columns[df[[config['columns']['sga_col'], config['columns']['rd_exp_col']]].isna().any()].tolist())
        # extra calculations
        if fin_src == config['fin_src_cs']: 
            try:
                df[[config['columns']['minority_interest_col'], config['columns']['minority_int_non_redeem_col']]] =     df[[config['columns']['minority_interest_col'], config['columns']['minority_int_non_redeem_col']]].replace(np.nan, 0)
                minority=float(df[config['columns']['minority_interest_col']]) + float(df[config['columns']['minority_int_non_redeem_col']])
                df[config['columns']['minority_interest_col']]=minority 
            except Exception as e:
                logger.error(f"Error in calculating minority_interest for the ISIN: {isin}, {e}")   
                df[config['columns']['minority_interest_col']] = np.nan
            try:
                df.drop([config['columns']['minority_int_non_redeem_col']],axis=1,inplace=True)
            except Exception as e:
                pass 
            try:
                df[[config['columns']['gp_col'],config['columns']['sga_col'],config['columns']['non_oper_exp_col']]] = df[[config['columns']['gp_col'],config['columns']['sga_col'],config['columns']['non_oper_exp_col']]].replace(np.nan,0)
                net_oper = float(df[config['columns']['gp_col']])-float(df[config['columns']['sga_col']])-float(df[config['columns']['non_oper_exp_col']])
                df[config['columns']['net_operating_income_col']]= net_oper
            except Exception as e:
                logger.error(f"Error in calculating net_operating_income. It will be ffilled for the ISIN: {isin}, {e}")
                df[config['columns']['net_operating_income_col']]=np.nan
            try:
                df.drop([config['columns']['gp_col'],config['columns']['non_oper_exp_col']],axis=1,inplace=True)
            except Exception as e:
                pass
        else:
            try:
                df[[config['columns']['gp_col'],config['columns']['sga_col'],config['columns']['rd_exp_col'],config['columns']['other_oper_col']]]=df[[config['columns']['gp_col'],config['columns']['sga_col'],config['columns']['rd_exp_col'],config['columns']['other_oper_col']]].replace(np.nan,0)
                net_oper=float(df[config['columns']['gp_col']])-float(df[config['columns']['sga_col']])-float(df[config['columns']['rd_exp_col']])-float(df[config['columns']['other_oper_col']])
                df[config['columns']['net_operating_income_col']]= net_oper
            except Exception as e:
                logger.error(f"Error in calculating net_operating_income. It will be ffilled for the ISIN: {isin}, {e}") 
                df[config['columns']['net_operating_income_col']] = np.nan
            try:
                df.drop([config['columns']['gp_col'],config['columns']['other_oper_col']],axis=1,inplace=True)
            except:
                pass
        df = df.replace('Data Unavailable', np.nan)
        df = df.replace('CapabilityNeeded', np.nan)                             
        return df, nan_columns
                                     
    def convert(self, row):
        try:
            row = row[0]['Row'][0]
            if row == 'Data Unavailable' or row == 'NaN':
                row = np.nan
            row = float(row)
            return row
        except Exception as e:
            row = np.nan
            return row
                                     
    def get_last_trading_date(self, isin, date):
        url = self.spglobal_url  
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}                                  
        data = {
                "inputRequests": [
                {
                    "function": config['snp_creds']['single_date_fn'],
                    "identifier": isin,
                    "mnemonic": config['snp_creds']['trade_check_mnem']
                }
            ]
        }
        data_json = json.dumps(data)

        response = requests.post(url, data=data_json, headers=headers)
        try:
            resp_json=json.loads(response.text)


            if resp_json == None:
                return np.nan

            else:
                try:
                    tmp=pd.json_normalize(resp_json['GDSSDKResponse'])
                    data = tmp['Rows'].iloc[0]
                    date_traded = data[0]['Row'][0]
                    if date_traded == 'Data Unavailable':
                        return 'Last Traded Date Unavailable'
                    else:
                        date_traded = datetime.strptime(date_traded,'%Y-%m-%d')
                        date_obj = datetime.strptime(date,'%m/%d/%Y')
                        if date_traded<date_obj:
                            logger.info( f'{isin} not traded on {date_obj}')          
                            return f'Not Traded on {date_obj}'
                        else:
                            return np.nan
                except:
                    return np.nan
        except Exception as e:
            logger.info(f"Error in finding the reason for no closeprice, {e}")
            return np.nan                                   
                              
    def get_capiq_phase_data(self, today, phase_values):
        url = self.spglobal_url
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}                      
        input_list = []
        for identifier in phase_values.keys():                    
            start_date=(pd.to_datetime(today) - timedelta(days=config['snp_creds']['phase_n_days'])).strftime("%m/%d/%Y")
    #         end_date=(pd.to_datetime(today)-timedelta(1)).strftime("%m/%d/%Y")
            end_date= today
            body_req = {
                  "function": self.daterange_fn,
                  "identifier": identifier,
                  "mnemonic": config['cp_adj_mnemonic'],
                  "properties": {
                    "startDate": start_date, # startdate
                    "endDate" : end_date #enddate
                  }
            }
            input_list.append(body_req)
        data = {"inputRequests" : input_list}
        data_json = json.dumps(data)
        response = requests.post(url, data=data_json, headers=headers)
        try:
            resp_json=json.loads(response.text)
            phase_data=pd.DataFrame(columns=['date'])
            for row in resp_json[self.resp_key]:
                data=[x['Row'] for x in row['Rows']]
                mnem_name = phase_values[row['Identifier']]
                df=pd.DataFrame(columns=[mnem_name,self.date_col], data=data)
                phase_data = pd.merge(phase_data,df, on=self.date_col, how='outer')
            phase_data[self.date_col] = pd.to_datetime(phase_data[self.date_col])
            return phase_data
        except Exception as e:
            logger.error(f"Error in getting Phase data, {e} with the response from S&P being {response.text},for the request {input_list}")
                        
    def fetch_macro_data_using_token(self, token_dict):
        data = {}
        for token, name in token_dict.items():
            data[name] = pd.DataFrame(data=fred.get_series(token), columns=[name])
        return data    
                        
    def convert_macro_to_daily(self, df):
        from datetime import datetime
        df.index = pd.to_datetime(df.index)
        start_date = df.index.min() - pd.DateOffset(day=1)
        end_date = datetime.now().date()
        dates = pd.date_range(start_date, end_date, freq='D')
        dates.name = 'date'
        df = df.reindex(dates, method='ffill')
        df.fillna(0, inplace=True)
        return df 
                        
    def macro_data(self, today, token_dict):  
        if (not os.environ.get(self.macro_env_var, '') and
        getattr(ssl, '_create_unverified_context', None)):
            ssl._create_default_https_context = ssl._create_unverified_context
        try: 
            global fred                         
            fred = Fred(api_key=self.macro_api_key)
        except Exception as e:
            logger.error(f"Error in getting fred data, {e}")
        
        try:
            dataframe = self.fetch_macro_data_using_token(token_dict)
            logger.info("Fetched data from FRED API Successfully !!!!")
        except Exception as e:
            logger.error(f"Error in getting fred data,{e}")
        dfs_to_concat = []                             
        try:                             
            inflation_rate = self.convert_macro_to_daily(dataframe["inflation_rate"])
            dfs_to_concat.append(inflation_rate)                         
        except:
            pass
        try:                             
            yield_maturity = self.convert_macro_to_daily(dataframe["yield_maturity"])
            dfs_to_concat.append(yield_maturity)                         
        except:
            pass
        try:                             
            unemployment = self.convert_macro_to_daily(dataframe["unemployment"])
            dfs_to_concat.append(unemployment)                           
        except:
            pass
        try:                             
            opinion_survey = self.convert_macro_to_daily(dataframe["opinion_survey"])
            dfs_to_concat.append(opinion_survey)                          
        except:
            pass
        try:                             
            ppi = self.convert_macro_to_daily(dataframe["ppi"])
            dfs_to_concat.append(ppi)                             
        except:
            pass
        try:                             
            cpi = self.convert_macro_to_daily(dataframe["cpi"])
            dfs_to_concat.append(cpi)                         
        except:
            pass                             
        macro_df = pd.concat(dfs_to_concat, axis = 1)
        macro_df.dropna(inplace = True)
        macro_df.reset_index(inplace = True)  
        macro_df=macro_df[pd.to_datetime(macro_df['date'])<=pd.to_datetime(today)]
        macro_df=macro_df[pd.to_datetime(macro_df['date'])>=pd.to_datetime((pd.to_datetime(today) - timedelta(days=config['look_back_period_for_calc'])))]

        return macro_df
        return macro_df
                                                             
                                     
    def momentum(self, company, today): 
        es = es_config('pre')
        year = datetime.today().date().year
        indices = [f"{self.momentum_index}_{year-2}",f"{self.momentum_index}_{year-1}",f"{self.momentum_index}_{year}"] 
        q_total = {"query":{"bool": {"must":[{"bool":{"should":[{"match":{"Isin":company}}]}}]}}}
        mom_df = es.get_es_hits_as_df(indexes=indices, body=q_total, size=10000)                                         
        mom_df = mom_df[config['columns']['momentum_raw_cols']]                      
        mom_df[self.date_col]=pd.to_datetime(mom_df[self.date_col])
        mom_df.sort_values(self.date_col, ascending=True, inplace=True)
        mom_df.reset_index(inplace=True, drop=True)
        mom_df = mom_df[mom_df[self.date_col]<=pd.to_datetime(today)]
        mom_df.columns = [column.replace('_','').lower() for column in mom_df.columns]
        start = [self.date_col]
        columns_order = start + list(mom_df.columns[mom_df.columns!=self.date_col])
        mom_df = mom_df[columns_order]
        return mom_df 
                              
    def dcf(self, company, dcf_index, today):
        es = es_config('pre')
        year = datetime.today().date().year
        indices = [f"{dcf_index}_{year-2}",f"{dcf_index}_{year-1}",f"{dcf_index}_{year}"] 
        q_total = {"query":{"bool": {"must":[{"bool":{"should":[{"match":{"Isin":company}}]}}]}}}   
        dcf_df = es.get_es_hits_as_df(indexes=indices, body=q_total, size=10000)  
        dcf_df = dcf_df[self.dcf_cols + [self.date_col]]                     
        dcf_df[self.date_col]=pd.to_datetime(dcf_df[self.date_col])
        dcf_df.sort_values(self.date_col, ascending=True, inplace=True)
        dcf_df.reset_index(inplace=True, drop=True)
        dcf_df=dcf_df[dcf_df[self.date_col]<=pd.to_datetime(today)]
        return dcf_df 
                              
    def get_capiq_data_daterange(self, body, today):
        tic, exchange, isin, mnemonic_list1, mnemonic_list2, ipodate = body
        # Identifiers to try in order
        identifiers = [f'{tic}:{exchange}', isin]
        url = self.spglobal_url
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
        start_date = (pd.to_datetime(today) - timedelta(days=config['look_back_period_for_calc'])).strftime("%m/%d/%Y")
        end_date = today
        input_list = []
        # Create the request template for mnemonics
        for mnemonic in self.calc_mnems:
            body_req = {
                "function": self.daterange_fn,
                "identifier": None,  # Placeholder to be filled in the loop
                "mnemonic": mnemonic,
                "properties": {
                    "startDate": start_date,
                    "endDate": end_date
                }
            }
            input_list.append(body_req)
        # Iterate over identifiers
        for identifier in identifiers:
            for body_req in input_list:
                body_req["identifier"] = identifier
            data = {"inputRequests": input_list}
            data_json = json.dumps(data)
            response = requests.post(url, data=data_json, headers=headers)
            try:
                resp_json = json.loads(response.text)
                if resp_json[self.resp_key][0].get('ErrMsg') == 'InvalidIdentifier':
                    logger.info(f'{identifier} is InvalidIdentifier')
                    continue  # Try the next identifier
                if resp_json[self.resp_key][0]['Rows'][0]['Row'][0] == 'Data Unavailable':
                    logger.info(f'{identifier} is giving Data Unavailable')
                    continue  # Try the next identifier
                resp_json[self.isin_col] = isin
                return resp_json
            except Exception as e:
                logger.error(f"Error in getting data for a daterange, {e} with the response from S&P being {response.text},for the request {input_list}")
                continue
        logger.error("All identifiers failed")
        return None
                              
    def convert2float(self, value):
        try:
            return float(value)
        except:
            return np.nan 
                              
    def convert2header(self, value):
        value = value.lower()[3:]
        if value[-3:] == '_cs':
            value=value.split('_cs')[0]
        if value == 'sga_suppl':
            value = 'sga'
        return value
                              
    def get_beta_for_date(self, identifier, date, id_type = 'tic'):
        input_list = []
        isin, tic, exchange = identifier
        mnemonics = self.beta_mnems
        url = self.spglobal_url
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}   
        for mnemonic in mnemonics:
            body = {
                      "function": self.single_date_fn,
                      "identifier": isin if id_type == 'isin' else f'{tic}:{exchange}' ,
                      "mnemonic": mnemonic,
                      "properties": {
                        "asOfDate": date
                      }
                    }
            input_list.append(body)
        data = {"inputRequests" : input_list}
        data_json = json.dumps(data)
        response = requests.post(url, data=data_json, headers=headers)
        try:
            resp_json=json.loads(response.text)
            result = {mnemonics[x['Headers'][0]]: float(x['Rows'][0]['Row'][0]) for x in resp_json[self.resp_key]}        
        except Exception as e:
            result =  {'beta_5yr': np.nan, 'beta_1yr': np.nan, 'beta_2yr': np.nan}
            if id_type == 'tic':
                return self.get_beta_for_date(identifier, date, id_type='isin')
        result['date'] = datetime.strptime(date, '%m/%d/%Y').strftime('%Y-%m-%d')
        return result
                              
   
    def basic_calculations(self, daily_df):  
        s = 0
                                     
        try:
            for calc in config["calculations"]:
                output_col = calc["output_column"]
                input_col = calc["input_column"]

                if calc["method"] == "pct_change":
                    daily_df[output_col] = daily_df[input_col].pct_change(periods=calc["periods"]) * calc["scale"]
                elif calc["method"] == "rolling_mean":
                    daily_df[output_col] = daily_df[input_col].astype(float).rolling(calc["window"], min_periods=calc["min_periods"]).mean()
        
        except Exception as e:
            s = 1
            logger.error(f"Error in basic calculations,{e}") 
        return daily_df if s == 0 else None  
                                                             
    def calculations(self, daily_df):
        s = 0                                  
        daily_df = self.basic_calculations(daily_df)

        # Perform correlation calculations dynamically
        try:
            corr_config = config["correlation_columns"]
            base_col = corr_config["base_column"]
            base_price_col = corr_config["base_price_col"]                        
            comparison_cols = corr_config["comparison_columns"]
                                     
            daily_df[[base_price_col] + comparison_cols] = daily_df[[base_price_col] + comparison_cols].astype(float)
            
            daily_df[base_col] = daily_df[base_price_col].pct_change() * 100
                                     
            # Compute 1-day change for comparison columns
            for col in comparison_cols:
                col_prefix=col.split('_')[0]                     
                daily_df[f"{col_prefix}_change_1d"] = daily_df[col].pct_change() * 100
                daily_df[f"{col_prefix}_%1d"] = daily_df[f"{col_prefix}_change_1d"] - daily_df[base_col]
      
            # Compute rolling correlations dynamically
            for period_label, period_value in corr_config["correlation_periods"].items():
                if period_label!='1d':                     
                    for col in comparison_cols:
                        col_prefix=col.split('_')[0]              
                        daily_df[f"{col_prefix}_%{period_label}"] = daily_df[base_col].rolling(period_value).corr(daily_df[f"{col_prefix}_change_1d"])
        
        except Exception as e:
            s = 1
            logger.error(f"Error in correlation calculations, {e}")

        # Clean up excess columns
        try:
            daily_df.sort_values(by="date", inplace=True)
            daily_df.ffill(inplace=True)
            daily_df.fillna(0, inplace=True)
        
        except Exception as e:
            s = 1
            logger.error(f"Error in cleaning excess columns, {e}")

        return daily_df if s == 0 else None 
                                     
    def calculations_process(self, company, country_code, phase_data, prev_daily_isin, today, isin):
        try:    
            if country_code not in phase_data['country_code'].unique().tolist():
                try:
                    ctry_cd = proxy_mapping[country_code]
                except:
                    ctry_cd = config['aieq_country_code']
            else:
                ctry_cd = country_code
            country_phase = phase_data[phase_data['country_code'] == ctry_cd]
            if pd.to_datetime(pd.to_datetime(today) - BDay(1)) not in pd.to_datetime(country_phase['date'].values):
                country_phase.loc[len(country_phase), 'date'] = pd.to_datetime(pd.to_datetime(today) - BDay(1))
            country_phase['date'] = pd.to_datetime(country_phase['date'])
#             country_phase.ffill(inplace = True)
        except Exception as e:
            logger.error(f"Error in getting phase data for {isin}, {e}")
        try:
            clc_dict = self.get_capiq_data_daterange(company.values[0].tolist(), today)
            clc_df_dict = {self.convert2header(x['Mnemonic']): pd.DataFrame({self.convert2header(x['Mnemonic']): [self.convert2float(y['Row'][0]) for y in x['Rows']],
                          'date': [pd.to_datetime(y['Row'][1]) for y in x['Rows']]
                            }) if x['Rows'][0]['Row'][1] != '' else pd.DataFrame()
                            for x in clc_dict[self.resp_key]}
            calc_df = pd.DataFrame()
            for k in clc_df_dict.keys():
                if len(calc_df) == 0:
                    calc_df = clc_df_dict[k]
                else:
                    calc_df = pd.merge(calc_df, clc_df_dict[k], on = self.date_col).ffill()
            calc_df.ffill(inplace = True)
            calc_df[self.isin_col] = isin
            try:
                calc_df[self.date_col] = pd.to_datetime(calc_df[self.date_col])
            except Exception as e:
                calc_len = len(calc_df)
                temp_phase=country_phase.tail(calc_len).reset_index()
                calc_df[self.date_col] = temp_phase[self.date_col]
                calc_df[self.date_col] = pd.to_datetime(calc_df[self.date_col])
            calc_df = calc_df.replace('Data Unavailable', np.nan)
            calc_df = calc_df.replace('CapabilityNeeded', np.nan)
            calc_df = calc_df.replace('NaN', np.nan)
            calc_df.ffill(inplace=True)
            calc_df.fillna(0,inplace=True)
            calc_df = calc_df.tail(config['look_back_period_for_calc'])
            country_phase = country_phase.tail(config['look_back_period_for_calc'])                         
            for_calc = pd.merge(calc_df, country_phase.drop(columns = ['country_code']), how="left", on =self.date_col) 
            if len(prev_daily_isin)>0:
                for_calc = pd.merge(prev_daily_isin[[self.date_col,config['columns']['cp_col']]], for_calc, how="outer", on=self.date_col)
            else:
                logger.info(f"{isin} not present in previous days file")
            for_calc[self.date_col] = pd.to_datetime(for_calc[self.date_col]) 
            for_calc.sort_values(self.date_col, inplace=True)
            for_calc.ffill(inplace=True)
            for_calc.fillna(0,inplace=True)
            try:
                for_calc[config['columns']['cp_adj_col']] = for_calc[config['columns']['cp_adj_col']].astype(float)
            except Exception as e:
                pass
            try:
                for_calc[config['columns']['vol_col']] = for_calc[config['columns']['vol_col']].astype(float)
            except Exception as e:
                pass
            for_calc.rename(columns=config['columns']['aigo_growth_rename'], inplace=True)
            for_calc.reset_index(drop=True, inplace=True)                          
            after_calc = self.calculations(for_calc)      
            excess_cols = list(set(config["excess_columns"]))
            after_calc.drop(columns=excess_cols, inplace=True, errors="ignore")
            after_calc[self.date_col] = pd.to_datetime(after_calc[self.date_col])
            return after_calc
        except Exception as e:
            logger.error(f"Error in doing the on-the-fly calculation for the ISIN: {isin}, {e}")                                             
                              
    def benchmark_close_and_vol(self, today, identifier):
        input_list=[]
        url = self.spglobal_url
        headers = {'Authorization': self.head_auth,'Content-type': self.content_type}         
        for mnemonic in config['snp_creds']['benchmark_mnems']:
            body = {
                  "function": self.single_date_fn,
                  "identifier": identifier,
                  "mnemonic": mnemonic,
                  "properties": {
                  'asofDate': today
                  }
            }
            input_list.append(body)
        data = {"inputRequests" : input_list}
        data_json = json.dumps(data)
        response = requests.post(url, data=data_json, headers=headers)
        try:
            resp_json=json.loads(response.text)
            return resp_json
        except Exception as e:
            logger.info(f"Error in getting benchmark CP and Volume, {e}") 
       
    def common_macro_data(self, today):
        try:
            token_dict = self.macro_tokens
            macro = self.macro_data(today, token_dict)
        except Exception as e:    
            logger.error(f"Error in fetching common macro data: {e}. It will be ffilled.")
            macro = pd.DataFrame(columns=self.macro_cols)
            macro.at[0,self.date_col]=today
            macro[self.date_col]=pd.to_datetime(macro[self.date_col])
            macro.loc[0,self.macro_cols]=np.nan  
        return macro
                                     
    def get_phase_model_data(self, today):
        try: 
            es_prod = es_config('prod')                      
            phase_model_dictionary = self.phase_model_dict
            start_date = (pd.to_datetime(today) - timedelta(days=config['look_back_period_for_calc'])).isoformat()
            end_date = (pd.to_datetime(today)).isoformat()
            country = self.country_code

            phase_data_dict = {}
            for model in phase_model_dictionary.keys():
                try:
                    q_total = {"query": {"bool": {"must": [{"bool": {"should": [{"match": {"id": phase_model_dictionary[model].replace('country', country)}}]}},
                            {"range": {"date": {"gte": start_date,"lte": end_date}}},
                            {"term": {"schedular.keyword": self.schedular.capitalize()}}]}}}

                    model_data = self.get_es_data(es_prod, start_date, end_date, None, q_total, self.phase_model_index).iloc[[-1]].reset_index(drop=True)
                    phase_data_dict[model] = model_data
                except Exception as e:    
                    logger.error(f"Error in fetching phase model data for {model}: {e}") 
            return phase_data_dict                     
        except Exception as e:
            logger.error(f"Error in fetching phase model data: {e}")
    
    def capiq_benchmark_data_processing(self, cds, today):
        try:                             
            benchmark_mapping = config['aigo_benchemark_mapping']
            proxy_mapping = config['aigo_country_proxy_mapping']
            benchmark = {}
            for country_code in cds:
                if country_code not in benchmark_mapping.keys():
                    if country_code in proxy_mapping.keys():
                        logger.info(f'Identifier not available for country code {country_code}. Using proxy data. Proxy: {proxy_mapping[country_code]}.')
                        country_code = proxy_mapping[country_code]
                    else:
                        logger.info(f'Identifier not available for country code {country_code}. Proxy not available so using USA.')
                        country_code = config['aieq_country_code']
                benchmark_dict = self.benchmark_close_and_vol((datetime.strptime(today,"%m/%d/%Y")).strftime("%m/%d/%Y"), benchmark_mapping[country_code]['identifier'])
                try:
                    benchmark[country_code] = pd.concat([pd.DataFrame({'benchmark_' + self.convert2header(x['Mnemonic']): [self.convert2float(x['Rows'][0]['Row'][0])], 'date': pd.to_datetime(x['Properties']['asofdate'], format = '%m/%d/%Y')}).set_index('date') for x in benchmark_dict['GDSSDKResponse']], axis = 1)
                except:
                    logger.info(f'Ticker not available for country code {country}. Using SPY data.')
                    benchmark_dict = self.benchmark_close_and_vol((datetime.strptime(today,"%m/%d/%Y")-timedelta(1)).strftime("%m/%d/%Y"),'USA')
                    benchmark[country_code] = pd.concat([pd.DataFrame({'benchmark_' + self.convert2header(x['Mnemonic']): [self.convert2float(x['Rows'][0]['Row'][0])], 'date': pd.to_datetime(x['Properties']['asofdate'], format = '%m/%d/%Y')}).set_index('date') for x in benchmark_dict['GDSSDKResponse']], axis = 1)
                                 
        except Exception as e:                         
            benchmark = {}
            benchmark_df = pd.DataFrame({self.date_col:[pd.to_datetime(today)],config['columns']['benchmark_cp_adj_col']:[np.nan],config['columns']['benchmark_vol_col']:[np.nan]}) 
            benchmark_df.set_index('date', inplace=True)                         
            for country_code in cds:
                 benchmark[country_code] = benchmark_df                     
            logger.error(f"Error in fetching benchmark data: {e}")
        return benchmark
                                     
    def all_country_phase_data(self, today):
        try:                             
            phase_bucket = self.old_bucket_name
            phase_loc = config['s3_paths']['aigo_phase_filename']
            fl = self.s3_client.get_objects_in_range_advance(phase_bucket,phase_loc,pd.to_datetime(pd.to_datetime(today)-timedelta(config['look_back_period_for_calc'])),pd.to_datetime(pd.to_datetime(today)+timedelta(2)))
            fl = [x['Key'] for x in fl if (x['Key'].split('.')[-1] == 'csv') and (pd.to_datetime(x['Key'].split('_')[-1].split('.')[0]) <= pd.to_datetime(today))]
            all_phase = pd.DataFrame()
            for file in tqdm(fl):
                all_phase = pd.concat([all_phase, self.s3_client.read_as_dataframe(phase_bucket, file)])
            all_phase['date'] = pd.to_datetime(all_phase['date'])
            phase_data = pd.DataFrame()
            for date, phase_df in tqdm(all_phase.groupby('date')):
                phase_df['new_id'] = [x.split('_')[0] for x in phase_df['id']]
                phase_data_temp = pd.DataFrame()
                for country_code, data in phase_df.groupby('new_id'):
                    phase = pd.DataFrame({'date': [date]})
                    phase['country_code'] = country_code
                    phase['vlue_close'] = data.loc[data['vlue_close_price'].notna(), 'vlue_close_price'].values[0] if data['vlue_close_price'].notna().sum() != 0 else np.nan
                    phase['mtum_close'] = data.loc[data['mtum_close_price'].notna(), 'mtum_close_price'].values[0] if data['mtum_close_price'].notna().sum() != 0 else np.nan
                    phase['iwf_close'] = data.loc[data['iwf_close_price'].notna(), 'iwf_close_price'].values[0] if data['iwf_close_price'].notna().sum() != 0 else np.nan
                    phase['sphq_close'] = data.loc[data['sphq_close_price'].notna(), 'sphq_close_price'].values[0] if data['sphq_close_price'].notna().sum() != 0 else np.nan
                    phase['vlue_er'] = data.loc[data['id'] == country_code + '_V', 'predictions'].values[0] if country_code + '_V' in data['id'].values else np.nan
                    phase['mtum_er'] = data.loc[data['id'] == country_code + '_M', 'predictions'].values[0] if country_code + '_M' in data['id'].values else np.nan
                    phase['iwf_er'] = data.loc[data['id'] == country_code + '_G', 'predictions'].values[0] if country_code + '_G' in data['id'].values else np.nan
                    phase['sphq_er'] = data.loc[data['id'] == country_code + '_Q', 'predictions'].values[0] if country_code + '_Q' in data['id'].values else np.nan
                    phase['vlue_corr_er'] = data.loc[data['id'] == country_code + '_V_B', 'predictions'].values[0] if country_code + '_V_B' in data['id'].values else np.nan
                    phase['mtum_corr_er'] = data.loc[data['id'] == country_code + '_M_B', 'predictions'].values[0] if country_code + '_M_B' in data['id'].values else np.nan
                    phase['iwf_corr_er'] = data.loc[data['id'] == country_code + '_G_B', 'predictions'].values[0] if country_code + '_G_B' in data['id'].values else np.nan
                    phase['sphq_corr_er'] = data.loc[data['id'] == country_code + '_Q_B', 'predictions'].values[0] if country_code + '_Q_B' in data['id'].values else np.nan
                    phase_data_temp = pd.concat([phase_data_temp, phase])
                phase_data_temp.reset_index(drop = True, inplace = True)
                phase_data = pd.concat([phase_data, phase_data_temp])
            return phase_data   
        except Exception as e:
            logger.error(f"Error in fetching aigo phase data,{e}")    
                                     
    def single_company_phase_data(self, today):
        try:                 
            phase_data = self.all_country_phase_data(today)
            phase_data = phase_data[phase_data['country_code'] == self.country_code]                         
            phase_data[self.date_col] = pd.to_datetime(phase_data[self.date_col])
            phase_data.sort_values(self.date_col, inplace=True)                         
            today = phase_data[self.date_col].iloc[-1]
            today = pd.to_datetime(today).strftime("%m/%d/%Y")                                                  
        except Exception as e:
            phase_data = pd.DataFrame(columns=[self.date_col, 'country_code']+config['columns']['all_country_phase_cols'])   
            phase_data.loc[0] = [today, self.country_code] + [np.nan] * len(config['columns']['all_country_phase_cols'])   
            phase_data[self.date_col] = pd.to_datetime(phase_data[self.date_col])                           
            logger.error(f"Error in fetching phase data: {e}. It will be ffilled.")                                   
        return phase_data
                                     
    def get_es_data(self, es, start_date, end_date, isin_list, q_total, index_prefix):
        start_year = (pd.to_datetime(start_date)).year
        end_year = (pd.to_datetime(end_date)).year
        try:
            data = []
            hits = []
            for year in range(start_year, end_year + 1):
                try:
                    response,total_docs = es.search_with_pagination(index=f"{index_prefix}_{year}",query=q_total,paginate=False,strict=False)
                except Exception as e:
                    logger.error(f"Error in getting data from ES, {e}")                 
                    pass
                for hit in response:
                    es_data=hit['_source']
                    data.append(es_data)
            df=pd.DataFrame(data)
            df[self.date_col]=pd.to_datetime(df[self.date_col])
            df.sort_values(self.date_col, ascending=True, inplace=True)
            df.reset_index(inplace=True, drop=True)
            return df
        except Exception as e:
            logger.info(f"Error in get_es_data, {e}")                         
                                     
    def revert_nan(self, row, nan_dict):
        if row['isin'] in nan_dict:
            columns = nan_dict[row['isin']]
            if isinstance(columns, list): 
                for col in columns:
                    row[col] = np.nan  
            else:  
                row[columns] = np.nan
        return row 
                                     
    def save_row(self, idx, row, bucket_name, filename):
        row_df = row.to_frame().T
        isin  = row_df['isin'].iloc[0]
        filename = filename.replace('isin',isin)                             
        self.s3_client.write_advanced_as_df(row_df, bucket_name, filename)                                  
                                     
    def failure_mail(self, error, tag, schedular, log_file_path, log_file_s3_path, env):
        num = config['n_bday']
        log_path_today = log_file_s3_path.replace('date',str(datetime.now()))                             
        current_date = date.today()
        saved_date = (current_date - BDay(num)).date()                             
        sender_email = self.sender_email             
        if env == 'prod':
            receivers_list = config['email_credentials']['receiver_emails_for_failure'] 
        elif env == 'pre-prod':
            receivers_list = self.sender_email
        subject = f'Financial Model {tag.upper()} {schedular.capitalize()} run failed'
        message_text = f"""
<html>
<body>
<p>Financial Model <strong>{tag.upper()}</strong> {schedular.capitalize()} run for <strong>{saved_date}</strong> was interrupted due to the following error:</p>

<p>{error}</p>

<p>For detailed logs, please refer to the log file at:<br>
<strong>Bucket:</strong> {self.bucket_name}<br>
<strong>Path:</strong> {log_path_today}
</p>
</body>
</html>
"""
        self.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)                                    
        self.s3_client.upload_file(log_file_path, self.bucket_name, log_path_today)
                                     
    def job_failure_mail(self, n_job_failed, tag, schedular):
        num = config['n_bday']
        current_date = date.today()
        saved_date = (current_date - BDay(num)).date()                             
        sender_email = self.sender_email             
        receivers_list = conn.sender_email          
        subject = f'Increase in prediction job failures in Financial model {tag} {schedular} run'
        message_text = f"An unusual increase in job failures has been detected in the Financial model run for {tag} {schedular} on {saved_date}. A total of {n_job_failed} jobs have failed."
        self.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)            
                                     
                                     
    def starting_mail(self, tag, schedular, today, receiver_emails, process):
        sender_email = self.sender_email 
        message = MIMEText(f"Financial model {tag.upper()} {schedular.capitalize()} {process} run for {today} has started")
        password = self.smtp_credential                            
        with smtplib.SMTP(self.server_address, self.port_no) as smtp:
            smtp.starttls()
            smtp.login(sender_email, password)
            message['From'] = sender_email
            message['Subject'] = f'Financial model {tag.upper()} {schedular.capitalize()} {process} run started'
            smtp.sendmail(sender_email, receiver_emails, message.as_string()) 
                                     
    def patch_https_connection_pool(self, **constructor_kwargs):
        """
        This allows to override the default parameters of the
        HTTPConnectionPool constructor.
        For example, to increase the poolsize to fix problems
        with "HttpSConnectionPool is full, discarding connection"
        call this function with maxsize=16 (or whatever size
        you want to give to the connection pool)
        """
        from urllib3 import connectionpool, poolmanager
        class MyHTTPSConnectionPool(connectionpool.HTTPSConnectionPool):
            def __init__(self, *args,**kwargs):
                kwargs.update(constructor_kwargs)
                super(MyHTTPSConnectionPool, self).__init__(*args,**kwargs)
        poolmanager.pool_classes_by_scheme['https'] = MyHTTPSConnectionPool
                                     
    def generate_backward_nan_report(self, df, feature_cols, date_col='date', isin_col='isin'):
        nan_report = defaultdict(list)
        df = df.sort_values(by=[isin_col, date_col])
        last_date = df[date_col].max()
        grouped = df.groupby(isin_col)

        for isin, group in grouped:
            group = group.sort_values(date_col, ascending=False).reset_index(drop=True)
            last_row = group.iloc[0]

            for feat in feature_cols:
                if pd.isna(last_row[feat]):
                    nan_streak = 0
                    start_date = None
                    for _, row in group.iterrows():
                        if pd.isna(row[feat]):
                            nan_streak += 1
                            start_date = row[date_col]
                        else:
                            break
                    if nan_streak > 0:
                        nan_report[isin].append({
                            'feature': feat,
                            'start_date': start_date,
                            'end_date': last_date,
                            'no_of_days_nan': nan_streak
                        })
        return nan_report                                      
                                     
    def flatten_nan_report(self, nan_report):
        records = []
        for isin, issues in nan_report.items():
            for issue in issues:
                records.append({
                    'isin': isin,
                    'feature': issue['feature'],
                    'start_date': issue['start_date'],
                    'end_date': issue['end_date'],
                    'no_of_days_nan': issue['no_of_days_nan']
                })
        return pd.DataFrame(records)                                   
                                     
    def get_credentials(self):
        SCOPES = config['common_gmail']['scope']
        creds = None
 
        response = self.s3_client._s3Client.get_object(Bucket=config['common_gmail']['gmail_cred_bucket'], Key=config['common_gmail']['gmail_cred_file'])
        credentials_data = json.loads(response['Body'].read().decode("utf-8"))
        creds = GoogleCredentials.from_authorized_user_info(info=credentials_data, scopes=SCOPES)
        return creds                                 
                                     
    def SendMessage(self, sender, to, subject, filename, message_text, attachments: list = None):
        credentials = self.get_credentials()
        if credentials == None:
            return print("credentials not found, Generate credentials")
        try:
            service = build('gmail', 'v1', credentials=credentials)
            message = EmailMessage()
            html = message_text
            body = MIMEText(html, 'html')
            message.set_content(body)
            if attachments:
                for i,attachment in enumerate(attachments):
                    with open(attachment, 'rb') as content_file:
                        content = content_file.read()
                        message.add_attachment(content, maintype='application', subtype=(
                            attachment.split('.')[1]), filename=filename[i])
            message['To'] = to
            message['From'] = sender
            message['Subject'] = subject

            encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
                .decode()

            create_message = {
                'raw': encoded_message
            }
            send_message = (service.users().messages().send(
                userId="me", body=create_message).execute())
            print(F'Message Id: {send_message["id"]}')
        except HttpError as error:
            print(F'An error occurred: {error}')
            send_message = None                                  

class AieqDataHelper(GenericHelper):
    def __init__(self, tag): 
        super().__init__(tag)                             
        self.phase_mnems = config['phase']   
        self.phase_model_folder = config['s3_paths']['phase_folder'] 
        self.phase_model_dict = config['phase']['model_ids'] 
        self.macro_tokens = config['macro']['tokens']                                 
        self.country_code = config['aieq_country_code'] 
        self.cq_trained_list_file = config['s3_paths']['aieq_cq_trained_list_filename']    
        self.input_cols = config['columns']['input_cols']
        self.daily_run_bucket_name = config['s3_paths']['old_bucket_name']  
                                     
    def get_isin_list(self):
        try:    
            df = self.get_master_df(config['tags']['aieq_tag'])
            return df                         
        except Exception as e:
            logger.error(f"Error in getting ISIN list: {e}")
            try:
                self.failure_mail(f"Error in getting ISIN list: {e}", self.tag, self.schedular, self.log_file_path, self.logs_filename)
            except:
                logger.error("Error in getting ISIN list and error in sending failure mail")   
                                     
    def get_phase_data(self, today):
        phase_data = self.single_company_phase_data(today)
        return phase_data
                                     
    def get_macro_data(self, today, cds):
        try:
            macro = self.common_macro_data(today)
            macros = {}
            macros[config['aieq_country_code']] = macro                                                 
        except Exception as e:
            macros = {}                         
            macro = pd.DataFrame(columns=[self.date_col]+config['columns']['macro_cols'])   
            macro.loc[0] = [today] + [np.nan] * len(config['columns']['macro_cols']) 
            macro[self.date_col] = pd.to_datetime(macro[self.date_col])              
            macros[config['aieq_country_code']] = macro                                                              
            logger.error(f"Error in fetching macro data: {e}. It will be ffilled.")       
        return macros 
                                     
    def get_benchmark_data(self, cds, today):
        benchmark = self.capiq_benchmark_data_processing(cds, today)                             
        return benchmark
                                     
class AieqMonthlyHelper(AieqDataHelper):
    def __init__(self, tag):
        super().__init__(tag)  
        self.dep_bucket_name = config['s3_paths']['bucket_name']                               
        self.dep_file = config['s3_paths']['aieq_deployment_filename_monthly']   
        self.raw_data_file = config['s3_paths']['aieq_raw_data_filename_monthly']
        self.raw_data_file_temp = config['s3_paths']['aieq_raw_data_filename_monthly_temp']
        self.daily_run_bucket_name = config['s3_paths']['old_bucket_name']                             
        self.daily_run_file = config['s3_paths']['aieq_daily_run_filename_monthly']
        self.daily_run_file_temp = config['s3_paths']['aieq_daily_run_filename_monthly_temp'] 
        self.logs_filename = config['s3_paths']['aieq_daily_run_logs_filename_monthly']                                 
        self.schedular = config['schedulars']['schedular_m']         
        self.macro_cols = config['columns']['macro_cols']          
        self.s3_versioning_pred_file = config['s3_paths']['s3_versioning_pred_filename_monthly']
        self.s3_versioning_pred_file_temp = config['s3_paths']['s3_versioning_pred_filename_monthly_temp'] 
        self.jobcount_filename = config['s3_paths']['aieq_jobcount_filename_monthly'] 
        self.failed_df_filename = config['s3_paths']['aieq_failed_df_filename_monthly'] 
        self.ibm_url = config['prediction_credentials']['dallas_url']  
        self.ffilled_file = config['s3_paths']['aieq_ffilled_filename_monthly']
        self.ffilled_file_temp = config['s3_paths']['aieq_ffilled_filename_monthly_temp']           
                                     
class AieqDailyHelper(AieqDataHelper):
    def __init__(self, tag):
        super().__init__(tag)  
        self.dep_bucket_name = config['s3_paths']['old_bucket_name']  
        self.dep_file = config['s3_paths']['aieq_deployment_filename_daily'] 
        self.raw_data_file = config['s3_paths']['aieq_raw_data_filename_daily']
        self.raw_data_file_temp = config['s3_paths']['aieq_raw_data_filename_daily_temp']
        self.daily_run_file = config['s3_paths']['aieq_daily_run_filename_daily']
        self.daily_run_file_temp = config['s3_paths']['aieq_daily_run_filename_daily_temp'] 
        self.logs_filename = config['s3_paths']['aieq_daily_run_logs_filename_daily']                             
        self.schedular = config['schedulars']['schedular_d']
        self.macro_cols = config['columns']['macro_cols']               
        self.s3_versioning_pred_file = config['s3_paths']['s3_versioning_pred_filename_daily']
        self.s3_versioning_pred_file_temp = config['s3_paths']['s3_versioning_pred_filename_daily_temp'] 
        self.jobcount_filename = config['s3_paths']['aieq_jobcount_filename_daily'] 
        self.failed_df_filename = config['s3_paths']['aieq_failed_df_filename_daily']  
        self.ibm_url = config['prediction_credentials']['frankfurt_url']  
        self.ffilled_file = config['s3_paths']['aieq_ffilled_filename_daily']
        self.ffilled_file_temp = config['s3_paths']['aieq_ffilled_filename_daily_temp']           
                         
class AigoDataHelper(GenericHelper):
    def __init__(self, tag):
        super().__init__(tag)     
        self.dep_bucket_name = config['s3_paths']['bucket_name']                                   
        self.dep_file = config['s3_paths']['aigo_deployment_filename_monthly']                              
        self.raw_data_file = config['s3_paths']['aigo_raw_data_filename_monthly']
        self.raw_data_file_temp = config['s3_paths']['aigo_raw_data_filename_monthly_temp']
        self.daily_run_file = config['s3_paths']['aigo_daily_run_filename_monthly']
        self.daily_run_file_temp = config['s3_paths']['aigo_daily_run_filename_monthly_temp']
        self.logs_filename = config['s3_paths']['aigo_daily_run_logs_filename']                             
        self.schedular = config['schedulars']['schedular_m'] 
        self.macro_cols = config['columns']['macro_cols']               
        self.s3_versioning_pred_file = config['s3_paths']['s3_versioning_pred_filename_monthly']
        self.s3_versioning_pred_file_temp = config['s3_paths']['s3_versioning_pred_filename_monthly_temp']  
        self.macro_tokens = config['macro']['tokens']  
        self.cq_trained_list_file = config['s3_paths']['aigo_cq_trained_list_filename'] 
        self.input_cols = config['columns']['input_cols']  
        self.jobcount_filename = config['s3_paths']['aigo_jobcount_filename_monthly']   
        self.failed_df_filename = config['s3_paths']['aigo_failed_df_filename_monthly']
        self.ibm_url = config['prediction_credentials']['dallas_url'] 
        self.daily_run_bucket_name = config['s3_paths']['bucket_name']
        self.ffilled_file = config['s3_paths']['aigo_ffilled_filename_monthly']
        self.ffilled_file_temp = config['s3_paths']['aigo_ffilled_filename_monthly_temp']          
                                     
    def get_isin_list(self):
        try:    
            aigo_df = self.get_master_df(config['tags']['aigo_tag'])
            aieq_df = self.get_master_df(config['tags']['aieq_tag'])
            aifint_df = self.get_master_df(config['tags']['aifint_tag'])
            indsec_df = self.get_master_df(config['tags']['indsec_tag'])
            indt1_df = self.get_master_df(config['tags']['indiat1_tag'])
            df = aigo_df[[(x not in aieq_df['isin'].values) & (x not in aifint_df['isin'].values) & (x not in indsec_df['isin'].values) & (x not in indt1_df['isin'].values) for x in aigo_df['isin']]].reset_index(drop = True)
            return df                         
        except Exception as e:
            logger.error(f"Error in getting ISIN list: {e}")
            try:
                self.failure_mail(f"Error in getting ISIN list: {e}", self.tag, self.schedular, self.log_file_path, self.logs_filename)
            except:
                logger.error("Error in getting ISIN list and error in sending failure mail")   
                                     
    def get_phase_data(self, today):
        try:
            phase_data = self.all_country_phase_data(today)
            return phase_data                         
        except Exception as e:
            logger.error(f"Error in fetching phase data: {e}. It will be ffilled.")
                                     
    def get_macro_data(self, today, cds):
        try:
            macro = self.common_macro_data(today)
            macros = {}
            macros[config['aieq_country_code']] = macro      
            for country in cds:
                if country == config['aieq_country_code']:
                    continue
                macros[country] = self.get_aigo_macro_data(country, today) 
                logger.info(f"Got AIGO Macro data from internal API for {country}")
                time.sleep(30)    
            macros = self.macro_data_process(macros, today)                        
        except Exception as e:
            macros = {}    
            macro = pd.DataFrame(columns=[self.date_col]+config['columns']['macro_cols'])   
            macro.loc[0] = [today] + [np.nan] * len(config['columns']['macro_cols']) 
            macro[self.date_col] = pd.to_datetime(macro[self.date_col])                          
            for country in cds:                                     
                macros[country] = macro                         
            logger.error(f"Error in fetching macro data: {e}. It will be ffilled.")    
        return macros  
                                     
    def get_aigo_macro_data(self, country, today):
        try:                             
            raw_cols = config['columns']['aigo_macro_row_cols']
            rename_cols = config['columns']['aigo_macro_rename_cols']                             
            st = (pd.to_datetime(today) - timedelta(days=360)).strftime('%Y-%m-%d')
            ed = datetime.strptime(today, '%m/%d/%Y').strftime('%Y-%m-%d')
            macro_url = config['aigo_macro_url'].replace('{st}',st).replace('{ed}',ed).replace('{country}',country)

            response = json.loads(requests.get(macro_url).text)
            if response['status'] == True:
                data = response['data']
                intrim_macro_dicts = {}
                for x in data:
                    if intrim_macro_dicts.get(x[self.date_col]) is None:
                        intrim_macro_dicts[x[self.date_col]] = {}
                    intrim_macro_dicts[x[self.date_col]][x['data_point']] = x['value']
                macro_dicts = [{self.date_col: x, **y} for x, y in intrim_macro_dicts.items()]
                dataframe = pd.DataFrame.from_dict(macro_dicts)
                avil_cols = list(set(dataframe.columns).intersection(raw_cols))
                return dataframe[avil_cols].rename(columns = {x: y for x, y in rename_cols.items() if x in avil_cols}).sort_values(by = ['date']).reset_index(drop=True)
            else:
                logger.info(f'Macro Data not available for, {country}')
                return None 
        except Exception as e:
            logger.error(f"Error in getting aigo macro data from internal API,{e}") 
                                     
    def macro_data_process(self, macros, today): 
        try:                             
            temp_data = macros[config['aieq_country_code']].copy()
            temp_data[self.date_col] = pd.to_datetime(temp_data[self.date_col])
            macros[config['aieq_country_code']] = temp_data
            proxy_mapping = config['aigo_country_proxy_mapping']

            for country in macros.keys():
                if country == config['aieq_country_code']:
                    continue
                if macros[country] is None or 'yield_maturity' not in macros[country].columns:
                    country_to_use = proxy_mapping[country] if country in proxy_mapping.keys() else config['aieq_country_code']
                    logger.info(f'Proxy: {country}, {country_to_use}.')
                    try:
                        macros[country] = macros[country_to_use].copy()
                    except:
                        macros[country] = self.get_aigo_macro_data(country_to_use)
                elif 'opinion_survey' not in macros[country].columns:
                    macros[country]['opinion_survey'] = 0

                # cols fix
                temp_data = macros[country].copy()
                for col in config['columns']['macro_cols']:
                    if col not in temp_data.columns:
                        temp_data[col] = 0
                if len(temp_data.columns) > len(macros[country].columns):
                    macros[country] = temp_data
                macros[country] = macros[country][[self.date_col] + config['columns']['macro_cols']]

                # rows fix
                temp_data = macros[country].copy()
                temp_data[self.date_col] = pd.to_datetime(temp_data[self.date_col])
                for row in macros[config['aieq_country_code']][self.date_col]:
                    if row not in macros[country]:
                        temp_data = pd.concat([temp_data, pd.DataFrame.from_dict([{**{self.date_col: row}, **{x: np.nan for x in config['columns']['macro_cols']}}])])
                if len(temp_data) > len(macros[country]):
                    macros[country] = temp_data

                macros[country] = macros[country].sort_values(by = self.date_col).ffill()
                macros[country] = macros[country][pd.to_datetime(macros[country][self.date_col]) >= pd.to_datetime((pd.to_datetime(today) - timedelta(days=config['look_back_period_for_calc'])))].drop_duplicates(subset = self.date_col)
                if len(macros[country][self.date_col].unique()) != len(macros[country]):
                    logger.error(f'Issue while fixing data for {country}.')
                    continue

                macros[country] = macros[country].sort_values(by = self.date_col).ffill()
                macros[country] = macros[country][pd.to_datetime(macros[country][self.date_col]) >= pd.to_datetime((pd.to_datetime(today) - timedelta(days=config['look_back_period_for_calc'])))]
            return macros
        except Exception as e:
            logger.error(f"Error in processing aigo macro data,{e}")   
                                            
    
    def get_benchmark_data(self, cds, today):
        benchmark = self.capiq_benchmark_data_processing(cds, today)                             
        return benchmark
                                    
class IndiaDataHelper(GenericHelper):
    def __init__(self, tag): 
        super().__init__(tag)                                                              
        self.country_code = config['ind_country_code'] 
        self.schedular = config['schedulars']['schedular_m'] 
        self.dep_bucket_name = config['s3_paths']['bucket_name']                                   
        self.dep_file = config['s3_paths']['india_deployment_filename_monthly']                              
        self.raw_data_file = config['s3_paths']['india_raw_data_filename_monthly']
        self.raw_data_file_temp = config['s3_paths']['india_raw_data_filename_monthly_temp']
        self.daily_run_file = config['s3_paths']['india_daily_run_filename_monthly']
        self.daily_run_file_temp = config['s3_paths']['india_daily_run_filename_monthly_temp']
        self.logs_filename = config['s3_paths']['india_daily_run_logs_filename']                             
        self.schedular = config['schedulars']['schedular_m'] 
        self.macro_cols = list(set(config['columns']['macro_cols']  ) - {'opinion_survey', 'ppi'})             
        self.s3_versioning_pred_file = config['s3_paths']['s3_versioning_pred_filename_monthly']
        self.s3_versioning_pred_file_temp = config['s3_paths']['s3_versioning_pred_filename_monthly_temp'] 
        self.macro_tokens = config['macro']['india_tokens']                             
        self.input_cols = list(set(config['columns']['input_cols']) - {'opinion_survey', 'ppi'})
        self.jobcount_filename = config['s3_paths']['india_jobcount_filename_monthly']  
        self.failed_df_filename = config['s3_paths']['india_failed_df_filename_monthly'] 
        self.ibm_url = config['prediction_credentials']['dallas_url']
        self.daily_run_bucket_name = config['s3_paths']['bucket_name']  
        self.ffilled_file = config['s3_paths']['india_ffilled_filename_monthly']
        self.ffilled_file_temp = config['s3_paths']['india_ffilled_filename_monthly_temp']          
                                     
    def get_isin_list(self):
        try:    
            df = self.get_master_df(config['tags']['indiat1_tag'])
            return df                         
        except Exception as e:
            logger.error(f"Error in getting ISIN list: {e}")
            try:
                self.failure_mail(f"Error in getting ISIN list: {e}", self.tag, self.schedular, self.log_file_path, self.logs_filename)
            except:
                logger.error("Error in getting ISIN list and error in sending failure mail") 
    
    def get_phase_data(self, today):
        phase_data = self.single_company_phase_data(today)
        return phase_data
                                     
    def get_macro_data(self, today, cds):
        try:
            macro = self.common_macro_data(today)
            macros = {}
            macros[self.country_code] = macro                                                 
        except Exception as e:
            macros = {}                         
            macro = pd.DataFrame(columns=[self.date_col]+config['columns']['macro_cols'])   
            macro.loc[0] = [today] + [np.nan] * len(config['columns']['macro_cols']) 
            macro[self.date_col] = pd.to_datetime(macro[self.date_col])              
            macros[self.country_code] = macro                                                              
            logger.error(f"Error in fetching macro data: {e}. It will be ffilled.")       
        return macros                                 
                                     
    def get_benchmark_data(self, cds, today):
        benchmark = {}                             
        benchmark_data=yf.download(config['yfinance']['india_benchmark'], 
                           interval='1d', 
                           start=(pd.to_datetime(today) - BDay(5)).strftime('%Y-%m-%d'), 
                           end=(pd.to_datetime(today) + BDay(1)).strftime('%Y-%m-%d'),auto_adjust = False)
        benchmark_data=benchmark_data.reset_index()
        benchmark_data.columns = benchmark_data.columns.get_level_values(0)
        benchmark_data=benchmark_data[['Date','Adj Close', 'Volume']].rename(columns = {'Adj Close': config['columns']['benchmark_cp_adj_col'], 'Volume': config['columns']['benchmark_vol_col'],'Date':self.date_col})
        benchmark_data = benchmark_data.sort_values(self.date_col)                        
        benchmark_data=benchmark_data.set_index(self.date_col)
        benchmark_data.index.name = self.date_col
        benchmark[self.country_code] = benchmark_data
        return benchmark   
                                     
class predictionHelper():
    def __init__(self, prediction_batch_size = 300):  # keep less for historical predictions
        self.batch_run_size = prediction_batch_size
        self.job_count = 0
        self.job_failed = 0
        self.job_success = 0
        self.failed_df = pd.DataFrame()                          
        self.ibm_api_key = config['prediction_credentials']['api_key']
        self.ibm_api_key_daily = config['prediction_credentials']['api_key_daily']
        self.auto_ai_jobs_cols = config['columns']['auto_ai_jobs_cols']
        self.autoai_job_bucket = config['s3_paths']['autoai_job_bucket']
                                              
        
    def del_jobs(self, job_id, wmlclient):
        try:
            status = wmlclient.deployments.delete_job(job_id, hard_delete=True)
            return f'{job_id}:{status}'
        except Exception as e:
            print(f'job:{job_id}:{str(e)}')

    def create_deployment_jobs(self, deployment_id, test_data, wmlclient, service):
        try:
            actuals = []
            if test_data is None:
                logger.info(f"Input data not available for: {deployment_id}")
                raise Exception("Input data not available")
            test_data = test_data[self.input_columns]    
            service.get(deployment_id)
            model_id = wmlclient.deployments.get_details(deployment_uid=deployment_id)['entity']['asset']['id']
            input_schema = wmlclient.repository.get_model_details(model_id)['entity']['schemas']['input'][0]['fields']                   
            input_columns = list(pd.DataFrame(input_schema)['name'].values)                 
            test_data = test_data[input_columns]
            test_data = test_data.replace(np.inf, np.nan).replace(-np.inf, np.nan).ffill().fillna(0)
            job_payload_ref = {wmlclient.deployments.ScoringMetaNames.INPUT_DATA: [{'fields': [], 'values': test_data.values.tolist()}]}
            job = wmlclient.deployments.create_job(deployment_id, meta_props=job_payload_ref) # Creates the scoring/prediction job
            job_id = wmlclient.deployments.get_job_uid(job)
            logger.info(f'deployment id: {deployment_id} job id: {job_id}.')
            return [deployment_id, job_id]
        except Exception as e:
            logger.error(f'for deployment_id: {deployment_id}, error is {e}')
            return [deployment_id, '']

    def get_state(self, job_id, wmlclient):
        status = ''
        values=[]
        try:
            # Get only the status and predictions output from get_job_details
            job_details = wmlclient.deployments.get_job_details(job_id)#, include='predictions,status')
            status =  job_details['entity']['scoring']['status']['state']
            if status=='completed':
                values = [value[0] for value in job_details['entity']['scoring']['predictions'][0]['values']]
            return [job_id, status, values]
        except Exception as e:
            return [job_id, 'error', []]

    def trigger_predict(self, deployment_toSubmit, wml_credentials, max_run_duration = 60):
        delete_pool = ThreadPool(24)
        all_jobs_status = []
        all_jobs_created = []
        all_jobs_deleted = []
        start = time.perf_counter()
        for space_id in deployment_toSubmit['deployment_space'].unique().tolist():
            wmlclient = APIClient(credentials = wml_credentials, space_id = space_id)
            service = Batch(source_wml_credentials=wml_credentials, source_space_id=space_id)
            logger.info(f"starting for space: {space_id}")
            delete_results = [] 
            job_status = []     
            jobs_deleted = []   
            run_duration = 0
            space_start = time.perf_counter()
            deployments = deployment_toSubmit[deployment_toSubmit['deployment_space']==space_id]
            logger.info(f"length of deployment list: {deployments.shape[0]}")
            jobs_to_submit=self.batch_run_size
            jobs_submitted=0
            jobs_executing=0
            no_new_jobs_counter = 0 # added
            prev_jobs_executing = 0 # added
            j=0
            while (( jobs_to_submit > 0 ) or ( jobs_executing > 0 )) and ( run_duration < max_run_duration * 60) and no_new_jobs_counter < 50: # changed
                jobs_to_submit=min(deployments.shape[0]-jobs_submitted,jobs_to_submit)
                to_fetch_list=()
                if ( jobs_to_submit > 0 ) :   
                    deployment_jobs = deployments.iloc[jobs_submitted:(jobs_submitted+jobs_to_submit)].copy()
                    deployment_jobs = deployment_jobs[['deployment_id', 'test_data']].copy()
    #                 print(f'deployment_jobs : {deployment_jobs}')
                    job_created = []
                    job_create_start = time.perf_counter()
                    create_results = []
                    logger.info(f'jobs_to_submit: {jobs_to_submit}, iteration:{j+1}, submitted: {jobs_submitted}, jobs_to_submit_actual: {deployment_jobs.shape[0]}, total_jobs: {deployments.shape[0]}')
                    thread_pool_size = jobs_to_submit if jobs_to_submit <= 100 else 100
                    pool = ThreadPool(thread_pool_size)
                    for deployment in deployment_jobs.values.tolist():
                        create_results.append(pool.apply_async(self.create_deployment_jobs, deployment+[wmlclient, service]))
                    data_store_dict = {}    

                    pool.close()
                    pool.join()
                    job_created = [r.get() for r in create_results]
                    all_jobs_created.extend(job_created)
                    job_create_finish = time.perf_counter()
                    logger.info(f'create jobs finished in {round((job_create_finish-job_create_start), 2)} seconds(s)')
                    logger.info(f'number of jobs created: {len(job_created)}')
                    logger.info(f'number of jobs created with error : {len([job for job in job_created if job is None])}')
                    logger.info(f'number of jobs created with no job_id: {len([job for job in job_created if job[1]==""])}')
                    self.job_count += len(job_created)    
                    job_created_df = pd.DataFrame(columns=['deployment_id', 'job_id'], data=job_created)
                    to_fetch_list=job_created_df[['job_id']].values.tolist()            
                wait_time = 5 # 0 if jobs_to_submit > 0 else 5 # changed
                no_new_jobs_counter = no_new_jobs_counter+1 if jobs_executing == prev_jobs_executing else 0 # added
                prev_jobs_executing = jobs_executing #added
#                 print(f'waiting for {wait_time} sec ...')
                time.sleep(wait_time)
#                 print('done waiting')
                job_fetch_start = time.perf_counter()
                fetch_results = []
                pool = ThreadPool(24)
                if len(to_fetch_list) > 0  :  
                    to_fetch_list.extend([job[0]] for job in job_status if job[1] in ["queued", "running"])
                else:
                    to_fetch_list = ([job[0]] for job in job_status if job[1] in ["queued", "running"])
                for job in to_fetch_list:
                    fetch_results.append(pool.apply_async(self.get_state, job+[wmlclient]))
                pool.close()
                pool.join()
                job_status = [r.get() for r in fetch_results]
                all_jobs_status.extend([job for job in job_status if job[1] in ["completed", "failed"]])
                job_fetch_finish = time.perf_counter()
                logger.info(f'fetch jobs finished in {round((job_fetch_finish-job_fetch_start), 2)} seconds(s)')
                logger.info(f'number of jobs fetched: {len(job_status)}')
                logger.info(f'number of jobs fetched and completed: {len([job for job in job_status if job[1] in ["completed"]])}')
                logger.info(f'number of jobs fetched and queued: {len([job for job in job_status if job[1] in ["queued"]])}')
                logger.info(f'number of jobs fetched and failed: {len([job for job in job_status if job[1] in ["failed"]])}')
                self.job_failed += len([job for job in job_status if job[1] in ["failed"]])   
                self.job_success += len([job for job in job_status if job[1] in ["completed"]])        
                self.failed_df = pd.concat([self.failed_df, job_created_df[job_created_df['job_id'].isin([job[0] for job in job_status if job[1] in ["failed"]])]])   
#                 failed_df.to_csv('/home/<USER>/sandra/django/daily_run_logs/failed_dataframe.csv',index=False) 
                logger.info(f'number of jobs fetched and running: {len([job for job in job_status if job[1] in ["running"]])}')
                logger.info(f'number of jobs fetched otherwise:{len([job for job in job_status if job[1] not in ["completed", "queued", "failed", "running"]])}')
                job_fetched_df = pd.DataFrame(columns=['job_id', 'status', 'values'], data=job_status)

                # Delete completed and failed jobs
                job_list = [job[0] for job in job_fetched_df[['job_id', 'status']].values.tolist() if (job[1] in ["completed", "failed"])]
                if ( len(job_list) > 0):     
                    for job in job_list: 
                        delete_results.append(delete_pool.apply_async(self.del_jobs,(job, wmlclient,)) )
                job_delete_finish = time.perf_counter()
                jobs_submitted+=jobs_to_submit
                jobs_to_submit=len(job_list)
                jobs_executing=len([job for job in job_status if job[1] in ["queued", "running"]])
                j+=1
                logger.info(f'total time taken in this loop, {j}: {round((job_delete_finish-job_create_start), 2)} sec(s)\n')
                run_duration = ( job_delete_finish - start )
                logger.info(f'current run duration : {run_duration} jobs_executing : {jobs_executing} submitted:{jobs_submitted},total_jobs:{len(deployments)}\n')
            if (jobs_executing > 0):     
                for job in [job for job in job_status if job[1] in ["queued", "running"]]:
                    delete_results.append(delete_pool.apply_async(self.del_jobs,(job[0], wmlclient,)) )        
            jobs_deleted = [res for res in delete_results if res is not None]
            all_jobs_deleted.extend(jobs_deleted)
            logger.info(f'total time taken in this space: {round((job_delete_finish-space_start)/60, 2)} min(s)\n')
            logger.info(f'length of all jobs created:{len(all_jobs_created)}')
            logger.info(f'length of all jobs status:{len(all_jobs_status)}')
            logger.info(f'length of all jobs deleted:{len(all_jobs_deleted)}')
        # Wait for all deletes to finish
        delete_pool.close()
        delete_pool.join()
        logger.info(f"length of all_jobs_status is {len(all_jobs_status)}")
        df_all_jobs_status = pd.DataFrame(all_jobs_status, columns=['job_id', 'status', 'predictions'])
        df_all_jobs_created = pd.DataFrame(all_jobs_created, columns=['deployment_id', 'job_id'])
        result_df = pd.merge(df_all_jobs_created, df_all_jobs_status, how='left', on='job_id')
        result_df = pd.merge(deployment_toSubmit[['deployment_space', 'deployment_id']], result_df, on='deployment_id', how = 'left')
        logger.info(f'total time taken:{round((job_delete_finish-start)/60, 2)} min(s)')
        return result_df
    
    def _score_calculation_helper(self, z_score, min_threshold, max_threshold):
        score = None
        if z_score > max_threshold:
            score = 10
        elif z_score < min_threshold:
            score = 1
        else:
            score = (z_score - min_threshold) / (max_threshold - min_threshold) * 9 + 1
        return score 
                            
    def calculate_score(self, predictions): # series of predictions (in percentage)
        processed_predictions = predictions.apply(lambda x: max(min(x, 100), -100)) # preprocess_columns
        Zscore = (processed_predictions - processed_predictions.mean())/(processed_predictions.std())
        min_Z_score_threshold = Zscore.median() - 3 * Zscore.std()
        max_Z_score_threshold = Zscore.median() + 3 * Zscore.std()
        score = Zscore.apply(lambda x: self._score_calculation_helper(x, min_Z_score_threshold, max_Z_score_threshold))

        return Zscore, score
                            
                                   






