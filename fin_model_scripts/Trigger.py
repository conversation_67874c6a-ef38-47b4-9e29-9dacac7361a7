#!/usr/bin/env python
# coding: utf-8


import argparse
import subprocess
import os
import sys
from Imports_Modified import *




if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()
sys.path.insert(0, os.path.abspath(script_dir))
config_path = os.path.join(script_dir, 'config_modified.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)




if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='Finance Prod Script', description="Script to trigger daily runs with two params.")
    parser.add_argument('-t', '--tag')
    parser.add_argument('-s', '--schedular')
    
    args = parser.parse_args()
    
    schedular = args.schedular
    tag = args.tag

    venv_python = config['script_trigger_paths']['venv_python']
    data_collection_script_path = config['script_trigger_paths']['data_collection_script_path']
    prediction_script_path = config['script_trigger_paths']['prediction_script_path']
    print(f'Entering subprocess using tag {tag} and schedular {schedular}')
    subprocess.run([venv_python, data_collection_script_path, "--tag",tag, "--schedular", schedular])
    subprocess.run([venv_python, prediction_script_path,"--tag",tag, "--schedular", schedular])









