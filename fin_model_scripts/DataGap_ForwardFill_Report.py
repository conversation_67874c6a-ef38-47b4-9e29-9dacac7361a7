#!/usr/bin/env python
# coding: utf-8

# In[1]:


import argparse
import os
import sys
from Imports_Modified import *


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='Finance Prod Script', description="Script to run data_collection with two parameters.")
    parser.add_argument('-t', '--tag')
    parser.add_argument('-s', '--schedular')
    
    args = parser.parse_args()
    
    schedular = args.schedular
    tag = args.tag


if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()
sys.path.insert(0, os.path.abspath(script_dir))
config_path = os.path.join(script_dir, 'config_modified.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

if tag.lower() == config['tags']['aieq_tag'].lower():
    if schedular.lower() == config['schedulars']['schedular_m'].lower():
        conn = AieqMonthlyHelper(tag.lower())
    elif schedular.lower() == config['schedulars']['schedular_d'].lower():
        conn = AieqDailyHelper(tag.lower()) 
elif tag.lower() == config['tags']['aigo_tag'].lower():
        conn = AigoDataHelper(tag.lower())     
elif tag.lower() == config['tags']['indiat1_tag'].lower():
        conn = IndiaDataHelper(tag.lower())                

def create_logger(log_file_path):
    log_dir = os.path.dirname(log_file_path)
    print(log_dir)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)  # Create the directory if it doesn't exist
    logger = logging.getLogger('my_logger')
    logger.setLevel(logging.DEBUG)
    for handler in logger.handlers:
        logger.removeHandler(handler)
    file_handler = logging.FileHandler(log_file_path, mode='w')  # Use mode='w' for write mode
    formatter = logging.Formatter('%(levelname)s: %(message)s')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    return logger


env = config['environments']['prod_env']
print(env)
num = config['n_bday']
current_date = date.today()
saved_date = (current_date - BDay(num)).date()
log_file_path = config['logger_path_data_check'].replace('folder',script_dir).replace('date',str(saved_date)).replace('tag',conn.tag.capitalize())
global logger
logger = create_logger(log_file_path)


try:    
    masters_isin_list = conn.get_isin_list()
except Exception as e:
    logger.error(f"Error in getting ISIN list: {e}")    
    raise SystemExit('Stop right there')   


column_list = conn.input_cols
if conn.date_col in column_list:
    column_list.remove(conn.date_col)

# Add pred_col if it's not already there
if conn.pred_col not in column_list:
    column_list.append(conn.pred_col)


today=(datetime.today().date()-BDay(1)).strftime("%m/%d/%Y")
today


isin_list = masters_isin_list['isin'].tolist()
es = es_config(env='prod')
start_date = (pd.to_datetime(today) - BDay(config['look_back_period'])).isoformat()
end_date = (pd.to_datetime(today)).isoformat()
start_year = (pd.to_datetime(start_date)).year
end_year = (pd.to_datetime(end_date)).year
index_prefix = config['index']['fin_es_index']
query = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date,
              "lte": end_date}}},{"term": {"schedular.keyword": conn.schedular.capitalize()}}]}}}

data = []
hits = []
for year in range(start_year, end_year + 1):
    try:
        response,total_docs = es.search_with_pagination(index=f"{index_prefix}_{year}",query=query,paginate=False,strict=False)
    except Exception as e:
        print(f"Error in getting data from ES, {e}")
    for hit in response:
        es_data=hit['_source']
        data.append(es_data)
df=pd.DataFrame(data)
df['date']=pd.to_datetime(df['date'])
df.sort_values('date', ascending=True, inplace=True)
df.reset_index(inplace=True, drop=True)


st = time.time()
nan_report = conn.generate_backward_nan_report(df, feature_cols=column_list)
et = time.time()
print(et-st)


feature_owner_mapping = config['feature_owner_mapping']


model_mapping = config['model_mapping']


feature_to_email = {}
for email, features in feature_owner_mapping.items():
    for feat in features:
        feature_to_email[feat] = email


flat_df = conn.flatten_nan_report(nan_report)


length_gt_5 = flat_df[flat_df['no_of_days_nan'] > 5].copy()


len(length_gt_5)


length_gt_5['isin'].nunique()


if env == 'prod':
    file_list = conn.s3_client.get_objects_in_range_advance(conn.bucket_name, conn.ffilled_file.split('ffilled_features_date')[0]), pd.to_datetime(pd.to_datetime(today)-timedelta(22)),pd.to_datetime(pd.to_datetime(today)+timedelta(2))
else:    
    file_list = conn.s3_client.get_objects_in_range_advance(conn.bucket_name, conn.ffilled_file_temp.split('ffilled_features_date')[0]), pd.to_datetime(pd.to_datetime(today)-BDay(22)),pd.to_datetime(pd.to_datetime(today)+timedelta(2))
if len(file_list[0])>0:
    file_list = [x['Key'] for x in file_list[0] if (x['Key'].split('.')[-1] == 'csv') and (pd.to_datetime(x['Key'].split('_')[-1].split('.')[0]) <= pd.to_datetime(today))]
else:
    file_list = []
len(file_list)


filled_df = pd.DataFrame()
if len(file_list)>0:    
    for file in file_list:
        filled_df_temp = conn.s3_client.read_as_dataframe(conn.bucket_name, file)  
        filled_df = pd.concat([filled_df,filled_df_temp])




if not filled_df.empty:
    forward_fill_counts = (
        filled_df
        .groupby(['isin', 'feature'])
        .size()
        .reset_index(name='no_of_days_forward_filled')
    )
    flat_df = flat_df.merge(
        forward_fill_counts,
        on=['isin', 'feature'],
        how='left'
    )
    flat_df['no_of_days_forward_filled'] = flat_df['no_of_days_forward_filled'].fillna(flat_df['no_of_days_nan'])

else:
    flat_df['no_of_days_forward_filled'] = flat_df['no_of_days_nan']


flat_df['no_of_days_nan'] = flat_df['no_of_days_nan'].astype(int)
flat_df['no_of_days_forward_filled'] = flat_df['no_of_days_forward_filled'].astype(int)


length_gt_2 = flat_df[(flat_df['no_of_days_nan'] > 2) & (flat_df['no_of_days_nan'] <= 5)].copy()
length_gt_5 = flat_df[flat_df['no_of_days_nan'] > 5].copy()

length_gt_2['owner'] = length_gt_2['feature'].map(feature_to_email)
length_gt_5['owner'] = length_gt_5['feature'].map(feature_to_email)


os.makedirs(config['high_priority_folder'], exist_ok=True)
os.makedirs(config['low_priority_folder'], exist_ok=True)
low_priority_files = []
high_priority_files = []
for owner_email, mail_df in length_gt_2.groupby('owner'):
    mail_df = mail_df.drop(columns='owner')
    file_path = os.path.join(
    config['low_priority_folder'], f'Low_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(today).date())}.csv'
)
    mail_df.to_csv(file_path, index=False)
    low_priority_files.append(owner_email)
for owner_email, mail_df in length_gt_5.groupby('owner'):
    mail_df = mail_df.drop(columns='owner')
    file_path = os.path.join(
    config['high_priority_folder'], f'High_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(today).date())}.csv'
)
    mail_df.to_csv(file_path, index=False)
    high_priority_files.append(owner_email)


for owner_email in model_mapping.keys():
    attachments = []
    message_parts = ["Hi,</p>", f"<p>Please find below the data quality report for {tag.upper()} {schedular.capitalize()} based on recent observations.</p>", "<ul>"]
    if owner_email in low_priority_files and owner_email in high_priority_files:
        subject = f"[Data Quality Report - {tag.upper()} {schedular.capitalize()}] Forward Fill Checker – High & Low Priority Flags"
        message_parts.append("<li><strong>High Priority:</strong> Features with more than <strong>5 consecutive missing values</strong>.</li>")
        message_parts.append("<li><strong>Low Priority:</strong> Features with more than <strong>2 consecutive missing values</strong>.</li>")

        attachments.extend([
            os.path.join(config['low_priority_folder'], f"Low_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(today).date())}.csv"),
            os.path.join(config['high_priority_folder'], f"High_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(today).date())}.csv")
        ])
    elif owner_email in high_priority_files:
        subject = f"[Data Quality Report - {tag.upper()} {schedular.capitalize()}] Forward Fill Checker – High Priority Flags"
        message_parts.append("<li><strong>High Priority:</strong> Features with more than <strong>5 consecutive missing values</strong>.</li>")
        attachments.append(os.path.join(config['high_priority_folder'],f'High_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(today).date())}.csv'))             
    elif owner_email in low_priority_files:
        subject = f"[Data Quality Report - {tag.upper()} {schedular.capitalize()}] Forward Fill Checker – Low Priority Flags"
        message_parts.append("<li><strong>Low Priority:</strong> Features with more than <strong>2 consecutive missing values</strong>.</li>")
        attachments.append(os.path.join(config['low_priority_folder'],f'Low_priority_alert_{model_mapping[owner_email]}_{str(pd.to_datetime(today).date())}.csv'))
    if len(attachments)>0:    
        message_parts.append("<p>Kindly review and take necessary actions.</p>")   
        message_parts.append(
        "<p>Feel free to reach out if you need any help or clarification.<br><br>"
        "Best regards,<br>Sandra P</p>"
    )
        message_text = "\n".join(message_parts)
        if env == 'prod':
            receiver_list = [owner_email]+config['email_alert_senders']
        else:
            receiver_list = config['email_credentials']['sender_email']
        filenames = [
    x.split(f"{config['high_priority_folder']}/")[1]
    if f"{config['high_priority_folder']}/" in x else
    x.split(f"{config['low_priority_folder']}/")[1]
    for x in attachments
    if f"{config['high_priority_folder']}/" in x or f"{config['low_priority_folder']}/" in x
]
        conn.SendMessage(config['sender_name'], receiver_list, subject, filenames, message_text, attachments)
#         break

for folder_path in [config['high_priority_folder'], config['low_priority_folder']]:
    if os.path.exists(folder_path) and os.path.isdir(folder_path):
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
        os.rmdir(folder_path)