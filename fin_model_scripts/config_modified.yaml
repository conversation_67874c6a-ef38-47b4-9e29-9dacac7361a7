 
tags:
    aieq_tag: aieq
    aigo_tag: aigo
    aiego_tag: aiego
    ust1_tag: tier1
    indiat1_tag: indt1
    india_tag: indeq
    aifint_tag: aifint
    indsec_tag: indsec
    
snp_creds:
  content_type: application/json
  head_auth: Basic ************************************************
  spglobal_url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json
  single_date_fn: GDSP
  daterange_fn: GDSHE
  q_period_type: IQ_CQ
  resp_key: GDSSDKResponse
  single_request_limit: 500
  phase_n_days: 70
  daily_non_cs_mnems: ["IQ_CLOSEPRICE_ADJ","IQ_CLOSEPRICE","IQ_VOLUME","IQ_MARKETCAP", "IQ_TEV", 'IQ_SHARESOUTSTANDING', 'IQ_PRICE_VOL_HIST_5YR', 'IQ_PRICE_VOL_HIST_2YR','IQ_PRICE_VOL_HIST_YR',
'IQ_PRICE_VOL_HIST_6MTH', 'IQ_PRICE_VOL_HIST_3MTH', 'IQ_PE_EXCL', 'IQ_PEG_FWD_CIQ', 'IQ_PRICE_CFPS_FWD_CIQ',
'IQ_PE_EXCL_FWD', 'IQ_PRICE_SALES','IQ_PBV','IQ_DIVIDEND_YIELD']
  daily_cs_mnems: ["IQ_CLOSEPRICE_ADJ","IQ_CLOSEPRICE","IQ_VOLUME","IQ_MARKETCAP", "IQ_TEV", 'IQ_SHARESOUTSTANDING', 'IQ_PRICE_VOL_HIST_5YR', 'IQ_PRICE_VOL_HIST_2YR','IQ_PRICE_VOL_HIST_YR',
'IQ_PRICE_VOL_HIST_6MTH', 'IQ_PRICE_VOL_HIST_3MTH', 'IQ_PE_EXCL_CS', 'IQ_PEG_FWD_CIQ', 'IQ_PRICE_CFPS_FWD_CIQ',
'IQ_PE_EXCL_FWD_CIQ', 'IQ_PRICE_SALES_CS','IQ_PBV_CS','IQ_DIVIDEND_YIELD']
  q_non_cs_mnems: [ 'IQ_CASH_ST_INVEST','IQ_EBITDA','IQ_LT_DEBT','IQ_MINORITY_INTEREST','IQ_TOTAL_DEBT','IQ_TOTAL_COMMON_EQUITY','IQ_PREF_EQUITY','IQ_SGA_SUPPL','IQ_UFCF_3YR_ANN_CAGR','IQ_EPS_5YR_ANN_CAGR','IQ_TBV_5YR_ANN_CAGR','IQ_TOTAL_REV_3YR_ANN_CAGR','IQ_TOTAL_REV_5YR_ANN_CAGR','IQ_EPS_1YR_ANN_GROWTH','IQ_TOTAL_REV_1YR_ANN_GROWTH','IQ_EST_ACT_RETURN_ASSETS_CIQ','IQ_COGS','IQ_CURR_FOREIGN_TAXES','IQ_TOTAL_REV','IQ_RD_EXP','IQ_UNLEVERED_FCF','IQ_GROSS_MARGIN','IQ_CURRENT_RATIO','IQ_QUICK_RATIO','IQ_TOTAL_DEBT_EQUITY','IQ_LT_DEBT_EQUITY','IQ_EBIT_MARGIN','IQ_AR_TURNS','IQ_INVENTORY_TURNS','IQ_ASSET_TURNS','IQ_FIXED_ASSET_TURNS','IQ_EBITDA_CAPEX_INT','IQ_CAPEX_PCT_REV','IQ_TOTAL_DEBT_CAPITAL','IQ_TOTAL_REV_EMPLOYEE','IQ_Z_SCORE','IQ_RETURN_ASSETS','IQ_RETURN_CAPITAL','IQ_RETURN_EQUITY','IQ_NI_MARGIN','IQ_EARNING_CO_MARGIN','IQ_EBITDA_MARGIN','IQ_SGA_MARGIN','IQ_EBIT_INT','IQ_NET_DEBT_EBITDA','IQ_PAYOUT_RATIO','IQ_EFFECT_TAX_RATE','IQ_TOTAL_REV_SHARE','IQ_RE','IQ_UFCF_5YR_ANN_CAGR','IQ_GP','IQ_OTHER_OPER','IQ_DPS_5YR_ANN_CAGR' ]
  q_cs_mnems: ['IQ_CASH_ST_INVEST_CS','IQ_EBITDA_CS','IQ_LT_DEBT','IQ_MINORITY_INTEREST_CS','IQ_MINORITY_INT_NON_REDEEM_CS','IQ_TOTAL_DEBT','IQ_TOTAL_COMMON_EQUITY','IQ_PREF_EQUITY_CS','IQ_SGA_CS','IQ_UFCF_3YR_ANN_CAGR_CS','IQ_EPS_5YR_ANN_CAGR_CS','IQ_TBV_5YR_ANN_CAGR_CS','IQ_TOTAL_REV_3YR_ANN_CAGR_CS','IQ_TOTAL_REV_5YR_ANN_CAGR_CS','IQ_EPS_1YR_ANN_GROWTH_CS','IQ_TOTAL_REV_1YR_ANN_GROWTH_CS','IQ_EST_ACT_RETURN_ASSETS_CIQ','IQ_COGS_CS','IQ_CURR_FOREIGN_TAXES','IQ_TOTAL_REV_CS','IQ_RD_EXP','IQ_UNLEVERED_FCF_CS','IQ_GROSS_MARGIN_CS','IQ_CURRENT_RATIO_CS','IQ_QUICK_RATIO_CS','IQ_TOTAL_DEBT_EQUITY_CS','IQ_LT_DEBT_EQUITY_CS','IQ_EBIT_MARGIN_CS','IQ_AR_TURNS_CS','IQ_INVENTORY_TURNS_CS','IQ_ASSET_TURNS_CS','IQ_FIXED_ASSET_TURNS_CS','IQ_EBITDA_CAPEX_INT_CS','IQ_CAPEX_PCT_REV','IQ_TOTAL_DEBT_CAPITAL_CS','IQ_TOTAL_REV_EMPLOYEE_CS','IQ_Z_SCORE','IQ_RETURN_ASSETS_CS','IQ_RETURN_CAPITAL_CS','IQ_RETURN_EQUITY_CS','IQ_NI_MARGIN_CS','IQ_EARNING_CO_MARGIN_CS','IQ_EBITDA_MARGIN_CS','IQ_SGA_MARGIN_CS','IQ_EBIT_INT_CS','IQ_NET_DEBT_EBITDA_CS','IQ_PAYOUT_RATIO_CS','IQ_EFFECT_TAX_RATE','IQ_TOTAL_REV_SHARE_CS','IQ_RE_CS','IQ_UFCF_5YR_ANN_CAGR_CS','IQ_GP_CS','IQ_NON_OPER_EXP_CS','IQ_DPS_5YR_ANN_CAGR']
  calc_mnems: ['IQ_CLOSEPRICE_ADJ', 'IQ_VOLUME', 'IQ_MARKETCAP']
  benchmark_mnems: ['IQ_CLOSEPRICE_ADJ','IQ_VOLUME']
  trade_check_mnem : IQ_PRICEDATE
  beta_mnems: 
    IQ_BETA_5YR: beta_5yr
    IQ_BETA_1YR: beta_1yr
    IQ_BETA_2YR: beta_2yr
  us_benchmark: SPY:ARCA
  
url:
  masters_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=
  
prediction_credentials: 
  api_key: TU-lJAQslyN1aQfhe8MepQxkcoc9kEYN6gQoLYuFN3oG
  dallas_url: https://us-south.ml.cloud.ibm.com
  api_key_daily: XHCG_3TowFlKr2DExm75l41dcnaNnFGwHEvWaKI74MCv
  frankfurt_url: https://eu-de.ml.cloud.ibm.com
  
s3_paths:
  bucket_name: financial-model
  old_bucket_name: financial-model-data-collection
  test_s3_versioning_bucket: eq-pre-model-output
  technical_indicator_bucket: portfolio-model
  autoai_job_bucket: autoai-jobs
  aieq_deployment_filename_monthly: all_data/aieq/monthly/model_training_details/deployment_details.csv
  queue_filename: all_data/tag/schedular/model_training_details/training_queue.csv
  aieq_deployment_filename_daily: model_training_details/deployment_details.csv
  phase_folder: phase_data/daily_run_schedular
  technical_indicator_filename: daily_technical_indicators/technical_indicators_date.csv
  calculation_foldername: calculation_columns
  aieq_raw_data_filename_monthly: all_data/aieq/monthly/daily_run/raw_data/input_data_date.csv
  aieq_raw_data_filename_monthly_temp: temp/all_data/aieq/monthly/daily_run/raw_data/input_data_date.csv
  aieq_raw_data_filename_daily: all_data/aieq/daily/daily_run/raw_data/input_data_date.csv
  aieq_raw_data_filename_daily_temp: temp/all_data/aieq/daily/daily_run/raw_data/input_data_date.csv
  aieq_daily_run_filename_monthly: aieq/monthly_predictions/monthly_prediction_date.csv
  aieq_daily_run_filename_monthly_temp: temp/all_data/aieq/monthly/daily_run/predictions/monthly_prediction_date.csv
  aieq_daily_run_filename_daily: aieq/daily_predictions/daily_prediction_date.csv
  aieq_daily_run_filename_daily_temp: temp/all_data/aieq/daily/daily_run/predictions/daily_prediction_date.csv
  aieq_jobcount_filename_monthly: financial-model/aieq_monthly.csv
  aieq_jobcount_filename_daily: financial-model/aieq_daily.csv
  aieq_failed_df_filename_monthly: all_data/aieq/monthly/daily_run/adhoc/failed_jobs/failed_jobs_date.csv
  aieq_failed_df_filename_daily: all_data/aieq/daily/daily_run/adhoc/failed_jobs/failed_jobs_date.csv
  aieq_daily_run_logs_filename_monthly: all_data/aieq/monthly/daily_run/adhoc/aieq_log_files/date_daily_run.log
  aieq_daily_run_logs_filename_daily: all_data/aieq/daily/daily_run/adhoc/aieq_log_files/date_daily_run.log
  calc_folder: calculation_columns
  s3_versioning_bucket: eq-model-output 
  s3_versioning_pred_filename_monthly: financial_model/monthly/date/predictions/isin.csv
  s3_versioning_pred_filename_daily: financial_model/daily/date/predictions/isin.csv
  s3_versioning_pred_filename_monthly_temp: temp/financial_model/monthly/date/predictions/isin.csv
  s3_versioning_pred_filename_daily_temp: temp/financial_model/daily/date/predictions/isin.csv
  aigo_cq_trained_isin_list: all_data/aigo/monthly/adhoc/isins_retrained_with_CQ.csv
  aigo_phase_filename: phase_data_aigo/daily_run_monthly/
  aigo_raw_data_filename_monthly: aigo/daily_predictions/raw_data/data_date.csv
  aigo_raw_data_filename_monthly_temp: temp/all_data/aigo/monthly/daily_run/raw_data/input_data_date.csv
  aigo_deployment_filename_monthly: all_data/aigo/monthly/model_training_details/deployment_details.csv
  aigo_failed_df_filename_monthly: all_data/aigo/monthly/daily_run/adhoc/failed_jobs/failed_jobs_date.csv
  aigo_daily_run_filename_monthly: aigo/daily_predictions/prediction_data/monthly_prediction_date.csv
  aigo_daily_run_filename_monthly_temp: temp/all_data/aigo/monthly/daily_run/predictions/monthly_prediction_date.csv
  aigo_jobcount_filename_monthly: financial-model/aigo_monthly.csv
  aigo_daily_run_logs_filename: all_data/aigo/monthly/daily_run/adhoc/aigo_log_files/date_daily_run.log
  india_raw_data_filename_monthly: india/daily_predictions/raw_data/data_date.csv
  india_raw_data_filename_monthly_temp: temp/all_data/india/monthly/daily_run/raw_data/input_data_date.csv
  india_deployment_filename_monthly: all_data/india/monthly/model_training_details/deployment_details.csv
  india_daily_run_filename_monthly: india/daily_predictions/prediction_data/monthly_prediction_date.csv
  india_daily_run_filename_monthly_temp: temp/all_data/india/monthly/daily_run/predictions/monthly_prediction_date.csv
  india_jobcount_filename_monthly: financial-model/india_monthly.csv
  india_failed_df_filename_monthly: all_data/india/monthly/daily_run/adhoc/failed_jobs/failed_jobs_date.csv
  india_daily_run_logs_filename: all_data/india/monthly/daily_run/adhoc/india_log_files/date_daily_run.log
  aieq_cq_trained_list_filename: all_data/aieq/monthly/adhoc/isins_trained_with_CY_DCF_and_CQ_quarterly.csv
  aigo_cq_trained_list_filename: all_data/aigo/monthly/adhoc/isins_trained_with_CY_DCF_and_CQ_quarterly.csv
  aieq_ffilled_filename_monthly: all_data/aieq/monthly/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv
  aieq_ffilled_filename_monthly_temp: temp/all_data/aieq/monthly/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv
  aieq_ffilled_filename_daily: all_data/aieq/daily/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv
  aieq_ffilled_filename_daily_temp: temp/all_data/aieq/daily/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv
  aigo_ffilled_filename_monthly: all_data/aigo/monthly/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv
  aigo_ffilled_filename_monthly_temp: temp/all_data/aigo/monthly/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv
  india_ffilled_filename_monthly: all_data/india/monthly/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv
  india_ffilled_filename_monthly_temp: temp/all_data/india/monthly/daily_run/adhoc/forward_filled_features/ffilled_features_date.csv

environments:
  pre_prod_env: pre-prod
  prod_env: prod
  
index:
  fin_es_index: eq_financial_model
  preprod_fin_index: pre_financial_model
  momentum_index: eq_momentum
  dcf_old_index: eq_dcf
  dcf_new_index: training_eq_dcf
  phase_model_index: eq_phase_model
  
phase:
  identifiers:
    SPHQ:ARCA: sphq_close
    VLUE:BATS: vlue_close
    MTUM:BATS: mtum_close
    IWF:ARCA: growth_close
  model_ids:
    iwf: country_G
    iwf_corr: country_G_B
    mtum: country_M
    mtum_corr: country_M_B
    sphq: country_Q
    sphq_corr: country_Q_B
    vlue: country_V
    vlue_corr: country_V_B 
    
macro:
   api_key: 57b0fb46e088b7bfcdf4b85ce5606edb
   env_var: PYTHONHTTPSVERIFY
   tokens:
       FPCPITOTLZGUSA: inflation_rate
       CPIAUCSL: cpi
       CSCICP03USM665S: opinion_survey
       UNRATENSA: unemployment
       PPIACO: ppi
       BAA10Y: yield_maturity
   india_tokens:
       FPCPITOTLZGIND: inflation_rate
       INDCPIALLMINMEI: cpi
       SLUEM1524ZSIND: unemployment
       INDIRLTLT01STM: yield_maturity
       
columns:
   tech_ind: ['ADX', 'APO', 'CCI', 'DX', 'MFI', 'MOM', 'PPO', 'RSI', 'ULTOSC',
               'WILLR', 'MACD_0', 'MACD_1', 'MACD_2', 'MACDFIX_0', 'MACDFIX_1',
               'MACDFIX_2', 'STOCH_0', 'STOCH_1', 'STOCHF_0', 'STOCHF_1', 'STOCHRSI_0',
               'STOCHRSI_1','isin']
   input_cols: ['date', 'closeprice', 'volume', 'marketcap', 'tev', 'sharesoutstanding', 'price_vol_hist_5yr', 'price_vol_hist_2yr', 'price_vol_hist_yr', 'price_vol_hist_6mth', 'price_vol_hist_3mth', 'pe_excl', 'peg_fwd_ciq', 'price_cfps_fwd_ciq', 'pe_excl_fwd', 'price_sales', 'pbv', 'dividend_yield', 'inflation_rate', 'cpi', 'opinion_survey', 'unemployment', 'ppi', 'yield_maturity', 'growth_1d', 'sphq_1d', 'vlue_1d', 'mtum_1d', 'growth_7d', 'sphq_7d', 'vlue_7d', 'mtum_7d', 'growth_30d', 'sphq_30d', 'vlue_30d', 'mtum_30d', 'adx', 'apo', 'cci', 'dx', 'mfi', 'mom', 'ppo', 'rsi', 'ultosc', 'willr', 'macd_0', 'macd_1', 'macd_2', 'macdfix_0', 'macdfix_1', 'macdfix_2', 'stoch_0', 'stoch_1', 'stochf_0', 'stochf_1', 'stochrsi_0', 'stochrsi_1', 'mp1d', 'mp1w', 'mp1m', 'mp1q', 'mp6m', 'mp1y', 'terminalroe', 'valprice', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'volume_change_yesterday', 'volume_change_weekly', 'volume_change_monthly', 'average_market_cap', 'ar_turns', 'asset_turns', 'cash_st_invest', 'cogs', 'current_ratio', 'earning_co_margin', 'ebit_int', 'ebit_margin', 'ebitda', 'ebitda_capex_int', 'ebitda_margin', 'effect_tax_rate', 'eps_1yr_ann_growth', 'eps_5yr_ann_cagr', 'est_act_return_assets_ciq', 'fixed_asset_turns', 'gross_margin', 'inventory_turns', 'lt_debt', 'lt_debt_equity', 'minority_interest', 'net_debt_ebitda', 'net_operating_income', 'ni_margin', 'payout_ratio', 'pref_equity', 'quick_ratio', 'rd_exp', 're', 'return_assets', 'return_capital', 'return_equity', 'sga', 'sga_margin', 'tbv_5yr_ann_cagr', 'total_common_equity', 'total_debt', 'total_debt_capital', 'total_debt_equity', 'total_rev', 'total_rev_1yr_ann_growth', 'total_rev_3yr_ann_cagr', 'total_rev_5yr_ann_cagr', 'total_rev_employee', 'total_rev_share', 'ufcf_3yr_ann_cagr', 'ufcf_5yr_ann_cagr', 'unlevered_fcf', 'z_score', 'beta_1yr', 'beta_2yr', 'beta_5yr', 'isin', 'tic', 'dps_5yr_ann_cagr', 'curr_foreign_taxes', 'capex_pct_rev', 'benchmark_closeprice_adj', 'benchmark_volume', 'equity_value_per_share', 'iwf_corr_er', 'iwf_er', 'mtum_corr_er', 'mtum_er', 'sphq_corr_er', 'sphq_er', 'vlue_corr_er', 'vlue_er'] 
   quarterly_cols: ['ar_turns','asset_turns','capex_pct_rev','cash_st_invest','cogs','current_ratio','curr_foreign_taxes','dps_5yr_ann_cagr','earning_co_margin','ebitda','ebitda_capex_int','ebitda_margin','ebit_int','ebit_margin','effect_tax_rate','eps_1yr_ann_growth','eps_5yr_ann_cagr','est_act_return_assets_ciq','fixed_asset_turns','gross_margin','inventory_turns','lt_debt','lt_debt_equity','minority_interest','net_debt_ebitda','ni_margin','payout_ratio','pref_equity','quick_ratio','rd_exp','re','return_assets','return_capital','return_equity','sga_margin','sga','tbv_5yr_ann_cagr','total_common_equity','total_debt','total_debt_capital','total_debt_equity','total_rev','total_rev_1yr_ann_growth','total_rev_3yr_ann_cagr','total_rev_5yr_ann_cagr','total_rev_employee',
                    'total_rev_share','ufcf_3yr_ann_cagr','ufcf_5yr_ann_cagr','unlevered_fcf','z_score','net_operating_income']
   daily_cols : ['closeprice', 'volume', 'marketcap', 'tev', 'sharesoutstanding', 'price_vol_hist_5yr', 'price_vol_hist_2yr', 'price_vol_hist_yr', 'price_vol_hist_6mth', 'price_vol_hist_3mth', 'pe_excl', 'peg_fwd_ciq', 'price_cfps_fwd_ciq', 'pe_excl_fwd', 'price_sales', 'pbv', 'dividend_yield']                 
   macro_cols: ['inflation_rate','cpi','opinion_survey','unemployment','ppi','yield_maturity']  
   phase_cols: ['sphq_close','vlue_close','mtum_close','growth_close']
   beta_cols: ['beta_1yr','beta_2yr','beta_5yr']
   momentum_raw_cols: ['MP_1D','MP_1W','MP_1M','MP_1Q','MP_6M','MP_1Y','date']
   momentum_cols: ['mp1d','mp1w','mp1m','mp1q','mp6m','mp1y']
   dcf_cols: ['TerminalRoe','ValPrice']
   calc_cols: ['close_change_yesterday','close_change_weekly','close_change_monthly','volume_change_yesterday','volume_change_weekly', 'volume_change_monthly','average_market_cap','sphq_%1d','vlue_%1d','mtum_%1d','growth_%1d','sphq_%7d','vlue_%7d','mtum_%7d','growth_%7d','sphq_%30d','vlue_%30d','mtum_%30d','growth_%30d']
   auto_ai_jobs_cols: ['date','start_time','end_time','total_job_count','failed_job_count', 'success_job_count']
   cols_to_drop_from_dep: ['rmse', 'feature_importance', 'estimator']
   cols_to_keep_for_dep: ['deployment_space','deployment_id','test_data']
   cols_to_drop_from_monthly_data: ['iwf_corr_er','iwf_er','mtum_corr_er','mtum_er','sphq_corr_er','sphq_er','vlue_corr_er','vlue_er','predictions','Zscore','F-score','ER_Copied','actual_monthly_returns','schedular','updated_at']
   id_and_mnemonic_cols: ['tic','exchange','isin','mnemonic_list_d','mnemonic_list_q','ipodate']
   metrics_required_cols: ['date', 'isin', 'actual_monthly_returns', 'monthly_predictions']
   all_metrics_cols: ['mean_absolute_error', 'mean_squared_error', 'root_mean_squared_error', 'r2_score', 'adjusted_r2_score', 'total_perc_diff', 'abs_total_diff', 'total_variance_perc_diff', 'abs_total_variance_perc_diff', 'correlation_score', 'directionality_score', 'mean_directionality', 'confidence_score', 'avg_confidence_score', 'accuracy_14_day', 'accuracy_22_day', 'accuracy_1_day']
   aigo_macro_row_cols: ['date', 'ppi', 'cpi', 'consumer_confidence_index', 'govt_bond_yied_long_term_interest_rate', 'inflation', 'unemployment']
   all_country_phase_cols: ['vlue_close', 'mtum_close', 'iwf_close', 'sphq_close', 'vlue_er', 'mtum_er', 'iwf_er', 'sphq_er', 'vlue_corr_er', 'mtum_corr_er', 'iwf_corr_er', 'sphq_corr_er']
   phase_model_cols: ['vlue_er', 'mtum_er', 'iwf_er', 'sphq_er', 'vlue_corr_er', 'mtum_corr_er', 'iwf_corr_er', 'sphq_corr_er']
   aigo_macro_rename_cols: 
       consumer_confidence_index: opinion_survey
       inflation': inflation_rate
       govt_bond_yied_long_term_interest_rate: yield_maturity   
       
   date_col: date
   isin_col: isin
   tic_col: tic
   exchange_col: exchange
   cp_col: closeprice
   cp_adj_col: closeprice_adj
   vol_col: volume
   gp_col: gp
   sga_col: sga
   rd_exp_col: rd_exp
   other_oper_col: other_oper
   non_oper_exp_col: non_oper_exp
   net_operating_income_col: net_operating_income
   equity_value_per_share_col: equity_value_per_share
   total_common_equity_col: total_common_equity
   sharesoutstanding_col: sharesoutstanding
   minority_interest_col: minority_interest
   minority_int_non_redeem_col: minority_int_non_redeem
   benchmark_cp_adj_col: benchmark_closeprice_adj
   benchmark_vol_col: benchmark_volume
   deployment_space_col: deployment_space
   space_id_col: space_id
   deployment_id_col: deployment_id
   pred_col: predictions
   status_col: status
   name_col: name
   er_copied_col: ER_Copied
   year_col: year
   training_date_col: training_date
   expiration_date_col: expiration_date
   renamings:
       growth_%1d: growth_1d
       sphq_%1d: sphq_1d
       vlue_%1d: vlue_1d
       mtum_%1d: mtum_1d
       growth_%7d: growth_7d
       sphq_%7d: sphq_7d
       vlue_%7d: vlue_7d
       mtum_%7d: mtum_7d
       growth_%30d: growth_30d
       sphq_%30d: sphq_30d
       vlue_%30d: vlue_30d
       mtum_%30d: mtum_30d
       ADX: adx
       APO: apo
       CCI: cci
       DX: dx
       MFI: mfi
       MOM: mom
       PPO: ppo
       RSI: rsi
       ULTOSC: ultosc
       WILLR: willr
       MACD_0: macd_0
       MACD_1: macd_1
       MACD_2: macd_2
       MACDFIX_0: macdfix_0
       MACDFIX_1: macdfix_1
       MACDFIX_2: macdfix_2
       STOCH_0: stoch_0
       STOCH_1: stoch_1
       STOCHF_0: stochf_0
       STOCHF_1: stochf_1
       STOCHRSI_0: stochrsi_0
       STOCHRSI_1: stochrsi_1
       TerminalRoe: terminalroe
       ValPrice: valprice
   sga_rename: {'sga_suppl':'sga'}
   space_col_rename: {'space_id':'deployment_space'}
   aigo_growth_rename: {'iwf_close':'growth_close'}
   
calculations:
  - output_column: "close_change_yesterday"
    input_column: "closeprice_adj"
    method: "pct_change"
    periods: 1
    scale: 100

  - output_column: "close_change_weekly"
    input_column: "closeprice_adj"
    method: "pct_change"
    periods: 5
    scale: 100

  - output_column: "close_change_monthly"
    input_column: "closeprice_adj"
    method: "pct_change"
    periods: 22
    scale: 100

  - output_column: "volume_change_yesterday"
    input_column: "volume"
    method: "pct_change"
    periods: 1
    scale: 100

  - output_column: "volume_change_weekly"
    input_column: "volume"
    method: "pct_change"
    periods: 5
    scale: 100

  - output_column: "volume_change_monthly"
    input_column: "volume"
    method: "pct_change"
    periods: 22
    scale: 100

  - output_column: "average_market_cap"
    input_column: "marketcap"
    method: "rolling_mean"
    window: 22
    min_periods: 22

correlation_columns:
  base_column: "close_change_1d"
  base_price_col: "closeprice_adj"
  comparison_columns: 
    - "sphq_close"
    - "vlue_close"
    - "mtum_close"
    - "growth_close"
  correlation_periods:
    "1d": 1
    "7d": 5
    "30d": 22

excess_columns:
  - "sphq_close"
  - "vlue_close"
  - "mtum_close"
  - "growth_close"
  - "close_change_1d"
  - "sphq_change_1d"
  - "vlue_change_1d"
  - "mtum_change_1d"
  - "growth_change_1d"
  - "closeprice_adj"
  - "isin"
  - "volume"
  - "marketcap"
  - "closeprice"

aigo_country_proxy_mapping: 
    AUT: EUR
    BEL: EUR
    DNK: EUR
    EGY: USA
    FIN: EUR
    FRA: EUR
    DEU: EUR
    GRC: EUR
    HUN: EUR
    IDN: USA
    IRL: EUR
    ISR: USA
    ITA: EUR
    KWT: USA
    MYS: USA
    NLD: EUR
    NZL: USA
    NOR: EUR
    PHL: USA
    POL: EUR
    PRT: EUR
    QAT: USA
    SAU: USA
    SGP: USA
    ESP: EUR
    SWE: EUR
    THA: USA
    TUR: USA
    ARE: USA
    LUX: EUR
    AR: USA
    BMU: USA
    CYM: USA
    GGY: USA
    MAC: USA
    MCO: USA
    PAN: USA
    South Korea: USA
    
aigo_macro_ccd_changes: 
    Russia: RUS
    Macau: MAC   
    
aigo_benchemark_mapping:
    USA: 
        Country: 'us'
        identifier: 'SPY:ARCA'
    CHN: 
        Country: 'china'
        identifier: 'MCHI:NasdaqGM'
    AUS:
        Country: 'australia'
        identifier: 'EWA:ARCA'
    AUT: 
        Country: 'austria'
        identifier: 'IQ379950025'
    BEL:
        Country: 'belgium'
        identifier: 'EWK:ARCA'
    BRA: 
        Country: 'brazil'
        identifier: 'XBOV11:BOVESPA'
    CAN: 
        Country: 'canada'
        identifier: 'XIU:TSX'
    CHL:
        Country: 'chile'
        identifier: 'EUFN:SNSE'
    COL: 
        Country: 'columbia'
        identifier: 'GXG:ARCA'
    DNK: 
        Country: 'denmark'
        identifier: 'IQ379953693'
    EGY: 
        Country: 'egypt'
        identifier: 'IQ379956604'
    EUR: 
        Country: 'europe'
        identifier: 'IEV:ARCA'
    FIN: 
        Country: 'finland'
        identifier: 'EFNL:BATS'
    FRA: 
        Country: 'france'
        identifier: 'EWQ:ARCA'
    DEU: 
        Country: 'germany'
        identifier: 'FLGR:ARCA'
    GRC:
        Country: 'greece'
        identifier: 'GREK:ARCA'
    HKG: 
        Country: 'hong_kong'
        identifier: '2819:SEHK'
    HUN:
        Country: 'hungary'
        identifier: 'IQ379968144'
    IND: 
        Country: 'india'
        identifier: 'HDFCGROWTH:NSEI'
    IDN: 
        Country: 'indonesia'
        identifier: 'IDX:ARCA'
    IRL: 
        Country: 'ireland'
        identifier: 'EIRL:ARCA'
    ISR: 
        Country: 'israel'
        identifier: 'EIS:ARCA'
    ITA: 
        Country: 'italy'
        identifier: 'EWI:ARCA'
    JPN: 
        Country: 'japan'
        identifier: 'EWJ:ARCA'
    KWT: 
        Country: 'kuwait'
        identifier: 'IQ582900262'
    MYS: 
        Country: 'malaysia'
        identifier: 'EWM:ARCA'
    MEX: 
        Country: 'mexico'
        identifier: 'ANGELD 10:BMV'
    NLD: 
        Country: 'netherlands'
        identifier: 'EWN:ARCA'
    NZL: 
        Country: 'new_zealand'
        identifier: 'ENZL:NasdaqGM'
    NOR: 
        Country: 'norway'
        identifier: 'ENOR:BATS'
    PHL: 
        Country: 'philippines'
        identifier: 'EPHE:ARCA'
    POL: 
        Country: 'poland'
        identifier: 'EPOL:ARCA'
    PRT: 
        Country: 'portugal'
        identifier: 'IQ379971734'
    QAT: 
        Country: 'qatar'
        identifier: 'QAT:NasdaqGM'
    RUS: 
        Country: 'russia'
        identifier: 'IQT127396429'
    SAU: 
        Country: 'saudi_arabia'
        identifier: 'KSA:ARCA'
    SGP: 
        Country: 'singapore'
        identifier: 'EWS:ARCA'
    ZAF: 
        Country: 'south_africa'
        identifier: 'EZA:ARCA'
    KOR: 
        Country: 'south_korea'
        identifier: 'EWY:ARCA'
    ESP: 
        Country: 'spain'
        identifier: 'EWP:ARCA'
    SWE: 
        Country: 'sweden'
        identifier: 'EWD:ARCA'
    CHE: 
        Country: 'switzerland'
        identifier: 'CHDVD:SWX'
    TWN: 
        Country: 'taiwan'
        identifier: 'IQ379972159'
    THA: 
        Country: 'thailand'
        identifier: 'THD:ARCA'
    TUR: 
        Country: 'turkey'
        identifier: 'TUR:NasdaqGM'
    ARE: 
        Country: 'uae'
        identifier: 'UAE:NasdaqGM'
    GBR: 
        Country: 'uk'
        identifier: 'EWU:ARCA'
    LUX: 
        Country: 'luxembourg'
        identifier: 'EWQ:ARCA'

yfinance:
    india_benchmark: '^NSEI'
    
aigo_macro_url: 'http://************:8080/worldmacroeconomy/get_data?startDate={st}&endDate={ed}&country={country}'  
date_references:
    recent_training_dates: ["2025-01-01","2025-02-09","2025-02-18","2025-03-20"]

schedulars:
    schedular_m: "Monthly"
    schedular_d: "Daily"
    
script_trigger_paths:
    venv_python: "/home/<USER>/scripts/envs/fin_env/bin/python"  
    data_collection_script_path: "/home/<USER>/scripts/fin_model_scripts/Financial_Model_Data_Collection_Script.py"
    prediction_script_path: "/home/<USER>/scripts/fin_model_scripts/Financial_Model_Prediction_Script.py"    
    
aieq_country_code: "USA"
ind_country_code: "IND"
lse_exchange: "LSE"
n_bday: 1
job_fail_threshold: 50
weekday: Monday
pe_excl_fwd_mnem: IQ_PE_EXCL_FWD_CIQ
logger_path: folder/Log_Files_tag/date_daily_run.log
logger_path_data_check: folder/Log_Files_Data_Check_tag/date_daily_run.log
cp_adj_mnemonic: IQ_CLOSEPRICE_ADJ
fin_src_cs: CS
fin_src_ciq: CIQ
sector_avg_identifier: sector-average
look_back_period: 22
look_back_period_for_calc: 50
high_priority_folder: High_priority_reports
low_priority_folder: Low_priority_reports

email_credentials:
    sender_email: <EMAIL>
    receiver_emails: ['<EMAIL>','<EMAIL>','<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
    receiver_emails_for_trigger: [ '<EMAIL>', '<EMAIL>'] 
    receiver_emails_for_failure: [ '<EMAIL>', '<EMAIL>', '<EMAIL>' ] 
    receiver_emails_for_less_hits: ['<EMAIL>','<EMAIL>','<EMAIL>']
    smtp_credential: bnfd pfpc hehm klpq
    server_address: smtp.gmail.com
    port_no: 587
    
common_gmail:
    scope: ['https://www.googleapis.com/auth/gmail.send']
    gmail_cred_bucket: etf-predictions
    gmail_cred_file: preportfolio/gmail_credentials/credentials.json
    
sender_name: "Sandra P <<EMAIL>>"

email_alert_senders: ['<EMAIL>', '<EMAIL>', '<EMAIL>']

feature_owner_mapping: {'<EMAIL>':['adx','apo','cci','dx','mfi','mom','ppo','rsi','ultosc','willr','macd_0','macd_1','macd_2','macdfix_0','macdfix_1','macdfix_2','stoch_0','stoch_1','stochf_0','stochf_1','stochrsi_0','stochrsi_1'],

                        '<EMAIL>':['closeprice', 'volume', 'marketcap', 'tev', 'sharesoutstanding', 'price_vol_hist_5yr', 'price_vol_hist_2yr', 'price_vol_hist_yr', 'price_vol_hist_6mth', 'price_vol_hist_3mth', 'pe_excl', 'peg_fwd_ciq', 'price_cfps_fwd_ciq', 'pe_excl_fwd', 'price_sales', 'pbv', 'dividend_yield', 'inflation_rate', 'cpi', 'opinion_survey', 'unemployment', 'ppi', 'yield_maturity', 'growth_1d', 'sphq_1d', 'vlue_1d', 'mtum_1d', 'growth_7d', 'sphq_7d', 'vlue_7d', 'mtum_7d', 'growth_30d', 'sphq_30d', 'vlue_30d', 'mtum_30d','close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'volume_change_yesterday', 'volume_change_weekly', 'volume_change_monthly', 'average_market_cap', 'ar_turns', 'asset_turns', 'cash_st_invest', 'cogs', 'current_ratio', 'earning_co_margin', 'ebit_int', 'ebit_margin', 'ebitda', 'ebitda_capex_int', 'ebitda_margin', 'effect_tax_rate', 'eps_1yr_ann_growth', 'eps_5yr_ann_cagr', 'est_act_return_assets_ciq', 'fixed_asset_turns', 'gross_margin', 'inventory_turns', 'lt_debt', 'lt_debt_equity', 'minority_interest', 'net_debt_ebitda', 'net_operating_income', 'ni_margin', 'payout_ratio', 'pref_equity', 'quick_ratio', 'rd_exp', 're', 'return_assets', 'return_capital', 'return_equity', 'sga', 'sga_margin', 'tbv_5yr_ann_cagr', 'total_common_equity', 'total_debt', 'total_debt_capital', 'total_debt_equity', 'total_rev', 'total_rev_1yr_ann_growth', 'total_rev_3yr_ann_cagr', 'total_rev_5yr_ann_cagr', 'total_rev_employee', 'total_rev_share', 'ufcf_3yr_ann_cagr', 'ufcf_5yr_ann_cagr', 'unlevered_fcf', 'z_score', 'beta_1yr', 'beta_2yr', 'beta_5yr', 'isin', 'tic', 'dps_5yr_ann_cagr', 'curr_foreign_taxes', 'capex_pct_rev', 'benchmark_closeprice_adj', 'benchmark_volume', 'equity_value_per_share', 'iwf_corr_er', 'iwf_er', 'mtum_corr_er', 'mtum_er', 'sphq_corr_er', 'sphq_er', 'vlue_corr_er', 'vlue_er', 'predictions'],

                        '<EMAIL>':['mp1d','mp1w','mp1m','mp1q','mp6m','mp1y','terminalroe', 'valprice']}

model_mapping: {'<EMAIL>':'TI', '<EMAIL>':'finance', '<EMAIL>':'dcf-momentum'} 