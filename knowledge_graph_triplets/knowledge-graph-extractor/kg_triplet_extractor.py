#TODO: introduce special dates from recurring economic announcements

import pickle
import instructor
import json
import regex as re
from dotenv import load_dotenv
import os
from groq import Groq
from pydantic import BaseModel, Field, validator, conlist, BeforeValidator, model_validator, field_validator
from typing import List, Dict, Any, Optional, Literal, Set, Union, TypeVar, Annotated, get_args
from datetime import datetime
from thefuzz import fuzz

# Import the new prompt
from triplet_extraction_prompt_small import TRIPLET_EXTRACTION_PROMPT

from valid_nodes_rels import *

#valid_primary_nodes = list(get_args(primary_nodes))
#try:
#    with open('new_relationships.txt', 'r', encoding='utf-8') as f:
#        new_relationships = f.readlines()
#        new_relationships = [r.strip() for r in new_relationships]
#except:
#    new_relationships = []
#primary_relationships.extend(new_relationships)
#literal_primary_relationships = Literal[tuple(primary_relationships)]

from value_normalizer import ValueNormalizer
normalizer = ValueNormalizer()

# Import API key (use your existing approach)
from personal import GROQ_API_KEY


# Test the combined function
variants_to_map = ["REPORTS_ON", "COMPETES_AGAINST", "LOCATED_AT", "FOUNDED", "UNKNOWN_TYPE"]
mapped_relationships_combined = {variant: map_relationship_type_combined(variant) for variant in variants_to_map}
print(mapped_relationships_combined)

def get_node_labels(node_type):
    """
    Returns all applicable labels for a node type, including the type itself
    and all its parent types in the hierarchy
    """
    labels = [node_type]
    
    # Add all parent types
    if node_type in parent_lookup:
        labels.extend(parent_lookup[node_type])
    
    return list(set(labels))  # Remove duplicates

class NodeLabels(BaseModel):
    """Model for representing a node's hierarchical labels"""
    primary: str
    all_labels: List[str]

    @model_validator(mode='after')
    def validate_labels(self):
        if self.primary not in primary_nodes:
            raise ValueError(f"Primary label {self.primary} must be one of: {primary_nodes}")
        
        if self.primary not in self.all_labels:
            raise ValueError(f"Primary label {self.primary} must be included in all_labels")
        return self

class Node(BaseModel):
    id: str
    labels: NodeLabels
    properties: Dict[str, Any] = Field(default_factory=dict)

    @classmethod
    def create(cls, id: str, type: str, properties: Dict[str, Any] = None):
        """Factory method to create a node with proper hierarchical labels"""
        if properties is None:
            properties = {}
            
        all_labels = get_node_labels(type)
        
        # If the type is not in primary_nodes, try to find a valid primary type
        primary_type = type
        if type not in primary_nodes:
            # Find a parent that is a primary node
            for label in all_labels:
                if label in primary_nodes:
                    primary_type = label
                    break
        
        return cls(
            id=id,
            labels=NodeLabels(primary=primary_type, all_labels=all_labels),
            properties=properties
        )

    @field_validator('labels')
    def validate_node_type(cls, v):
        if v.primary not in primary_nodes:
            raise ValueError(f'Invalid primary node type: {v.primary}')
        return v
    
    @field_validator('id')
    def validate_node_id(cls, v, values):
        if hasattr(values, 'labels') and values.labels.primary in registry['nodes']:
            pattern = registry['nodes'][values.labels.primary]['id_pattern']
            if not re.match(pattern, v):
                raise ValueError(f"Invalid ID for {values.labels.primary}: {v}")
        return v
        
    @field_validator('properties')
    def validate_node_properties(cls, v, values):
        if hasattr(values, 'labels') and values.labels.primary in registry['nodes']:
            recommended_props = registry['nodes'][values.labels.primary]['props']
            present_props = set(v.keys())
            
            # Check if at least one of the recommended properties is present
            if not any(prop in present_props for prop in recommended_props):
                print(f"Warning: Node of type {values.labels.primary} has none of the recommended properties: {recommended_props}")
                # raise ValueError(f"Node of type {values.labels.primary} must have at least one of these properties: {recommended_props}")
                
            # Special case for Events - they must have dates
            if values.labels.primary == "Event" and "date" not in v:
                raise ValueError("Event nodes must have 'date' property")
            
            # Property format validation
            # don't normalize node properties.. they need to be highly specific!

                
        return v

from datetime import datetime
import re

def normalize_date(value, property_name=None, assume_current_year=True):
    """
    Parse a date string in various formats and normalize to YYYY-MM-DD format.
    
    Args:
        value: The date string to parse
        property_name: Optional name of the property (for warning messages)
        assume_current_year: If True, add current year to dates without year
        
    Returns:
        String in YYYY-MM-DD format if successful, original string if parsing failed
    """
    if not value or not isinstance(value, str):
        print(f"Warning: Property '{property_name}' has invalid or empty date value")
        return value
    
    # Clean the input string
    value = value.strip()
    
    # Try standard ISO format first
    formats = [
        '%Y-%m-%d',          # 2023-10-24
        '%Y/%m/%d',          # 2023/10/24
        '%d-%m-%Y',          # 24-10-2023
        '%d/%m/%Y',          # 24/10/2023
        '%m-%d-%Y',          # 10-24-2023
        '%m/%d/%Y',          # 10/24/2023
        '%b %d, %Y',         # Oct 24, 2023
        '%B %d, %Y',         # October 24, 2023
        '%d %b %Y',          # 24 Oct 2023
        '%d %B %Y',          # 24 October 2023
        '%b %d %Y',          # Oct 24 2023
        '%B %d %Y',          # May 10 2024
        '%Y-%m-%dT%H:%M:%S', # 2023-10-24T14:30:00 (ISO format with time)
        '%Y-%m-%dT%H:%M:%SZ' # 2023-10-24T14:30:00Z (ISO format with UTC time)
    ]
    
    # Try all formats with year
    for fmt in formats:
        try:
            date_obj = datetime.strptime(value, fmt)
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            pass
    
    # Handle formats without year (if assume_current_year is True)
    if assume_current_year:
        current_year = datetime.now().year
        formats_without_year = [
            '%d %b',          # 24 Oct
            '%d %B',          # 24 October
            '%b %d',          # Oct 24
            '%B %d',          # October 24
            '%d-%m',          # 24-10
            '%m-%d',          # 10-24
            '%d/%m',          # 24/10
            '%m/%d'           # 10/24
        ]
        
        # Try formats without year by adding the current year
        for fmt in formats_without_year:
            try:
                date_obj = datetime.strptime(value, fmt)
                # Set the year to current year
                date_obj = date_obj.replace(year=current_year)
                return date_obj.strftime('%Y-%m-%d')
            except ValueError:
                pass
    
    # Handle common text formats like "Fri, Mar 14"
    weekday_pattern = r'(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{1,2})'
    match = re.match(weekday_pattern, value, re.IGNORECASE)
    if match and assume_current_year:
        try:
            month_map = {
                'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
            }
            month = month_map[match.group(2).lower()[:3]]
            day = int(match.group(3))
            year = datetime.now().year
            date_obj = datetime(year, month, day)
            return date_obj.strftime('%Y-%m-%d')
        except (ValueError, KeyError):
            pass
    
    # If we get here, we couldn't parse the date
    if property_name:
        print(f"Warning: Property '{property_name}' has invalid date format {value}")
    else:
        print(f"Warning: Invalid date format {value}")
    
    return value  # Return original value if parsing fails

def normalize_magnitude(value):
    """
    Normalizes magnitude values into three standard categories:
    POSITIVE, NEUTRAL, NEGATIVE
    
    Args:
        value (any): The original magnitude value
        
    Returns:
        str: Normalized value as "POSITIVE", "NEUTRAL", or "NEGATIVE"
        or the original value if it's numeric or can't be normalized
    """
    # If it's numeric or percentage, return as is
    if isinstance(value, (int, float)) or (isinstance(value, str) and '%' in value):
        return value
    
    # Skip normalization if not a string
    if not isinstance(value, str):
        return value
    
    # Convert to lowercase for case-insensitive matching
    value_lower = value.lower()
    
    # Positive terms
    positive_terms = [
        'strong', 'improved', 'good', 'expansion', 'momentum', 'positive', 'increase', 
        'growth', 'gain', 'up', 'higher', 'rising', 'recovery', 'bullish', 'outperform',
        'better', 'favorable', 'robust', 'accelerate', 'exceeded', 'boom', 'surge', 
        'record high', 'profitable', 'successful', 'optimistic', 'upbeat', 'buoyant', 
        'resilient', 'thriving', 'flourishing', 'advantageous', 'beneficial', 'promising', 
        'upswing'
    ]

    # Negative terms
    negative_terms = [
        'reduced', 'negative', 'decline', 'weak', 'down', 'lower', 'decreasing', 'bearish',
        'contraction', 'slowdown', 'deterioration', 'underperform', 'fall', 'drop',
        'poor', 'unfavorable', 'disappointing', 'miss', 'below', 'loss', 'deficit', 
        'crisis', 'recession', 'downward trend', 'downturn', 'collapse', 'bankruptcy', 
        'insolvency', 'adverse', 'unstable', 'volatile', 'setback', 'impairment'
    ]

    # Neutral terms
    neutral_terms = [
        'stable', 'steady', 'flat', 'unchanged', 'consistent', 'moderate', 'mixed',
        'balanced', 'neutral', 'maintained', 'in-line', 'expected', 'as anticipated',
        'unchanged', 'status quo', 'normal', 'average', 'typical', 'standard', 'ongoing',
        'continuing', 'sustained', 'unchallenged', 'unaffected'
    ]
    
    # Check for term presence
    if any(term in value_lower for term in positive_terms):
        return "POSITIVE"
    elif any(term in value_lower for term in negative_terms):
        return "NEGATIVE"
    elif any(term in value_lower for term in neutral_terms):
        return "NEUTRAL"
    
    # If no match found, return original value
    return None

def validate_magnitude_property(prop, value):
    """
    Validates magnitude, percentage, and amount properties to allow both numeric values
    and common qualitative financial descriptors.
    
    Args:
        prop (str): Property name
        value (any): Property value
        
    Returns:
        bool: True if valid, False otherwise
    """
    if prop in ['magnitude', 'percentage', 'amount']:
        # Valid if it's a number or percentage
        if isinstance(value, (int, float)) or (isinstance(value, str) and '%' in value):
            return True
            
        value = normalize_magnitude(value)
        if value is None:            
            # If we got here, it doesn't match our patterns
            print(f"Warning: Property '{prop}' should be numeric with optional % or a recognized qualitative term, got: {value}")
            return False
        
    # Not a magnitude property, so no validation needed
    return True

class Relationship(BaseModel):
    source: str
    target: str
    type: str
    mapped_type: Optional[str] = None
    properties: Dict[str, Any] = Field(default_factory=dict)
    
    @model_validator(mode='after')
    def map_relationship(self):
        """Map the relationship type to a primary type"""
        try:
            if self.type not in primary_relationships:
                self.mapped_type = map_relationship_type_combined(self.type, 
                                                                source_node_type=None,  # We'll handle this at KG level
                                                                target_node_type=None)  # We'll handle this at KG level
            else:
                self.mapped_type = self.type
        except Exception as e:
            print(f"Relationship mapping error: {e}")
            self.mapped_type = self.type  # Fallback
        return self

    @field_validator('properties')
    def validate_relationship_props(cls, v, info):
        # In Pydantic V2, we need to access the data differently
        try:
            # Get the data object (either via data attribute or the old values dict)
            data = info.data if hasattr(info, 'data') else info
            
            # Try to access the type attribute/key
            if hasattr(data, 'type'):
                rel_type = data.type
            elif isinstance(data, dict) and 'type' in data:
                rel_type = data['type']
            else:
                rel_type = ''
                
            mapped_type = map_relationship_type_combined(rel_type)
            
            # Validation logic remains the same
            if mapped_type in registry['relationships'] and 'props' in registry['relationships'][mapped_type]:
                required_props = registry['relationships'][mapped_type].get('props', [])
                
                if required_props:
                    present_props = set(v.keys())
                    if not any(prop in present_props for prop in required_props):
                        print(f"Warning: Relationship of type {mapped_type} has none of the required properties: {required_props}, only {str(v)}")
                
            # Property format validation
            for prop in list(v.keys()):
                value = v[prop]
                if (prop == 'date' or prop.endswith('_date')) and isinstance(value, str):
                    value = normalize_date(value)
                    #datetime.strptime(value, '%Y-%m-%d')
                
                if prop in ['magnitude', 'percentage', 'amount']:
                    if not validate_magnitude_property(prop, value):
                        print(f"Warning: Property '{prop}' should be numeric with optional % {value}")
                    v['original_'+prop] = value
                    x = normalizer.normalize_value(value) # {'original': 'High', 'type': 'qualitative', 'group': 'high', 'value': 0.9}
                    v[prop] = x['group']
                    v['numeric_'+prop] = x['value']
                    if 'unit' in x and 'unit' not in v:
                        v['unit'] = x['unit']
                    if 'currency' in x and 'currency' not in v:
                        v['currency'] = x['currency']
                    #cleanup unknown/NAN/null/None etc.. leave for now.. 


        except Exception as e:
            print(f"Property validation warning: {e}")
            
        return v
    
class KnowledgeGraph(BaseModel):
    nodes: List[Node] = Field(default_factory=list)
    relationships: List[Relationship] = Field(default_factory=list)

    @model_validator(mode='after')
    def validate_lists(self):
        if not self.nodes and not self.relationships:
            raise ValueError("Either nodes or relationships must be present")
        
        # Validate relationships against registry
        self._validate_relationships()
        
        return self
    
    def _validate_relationships(self):
        """Validate relationships against registry using node type information"""
        # Create lookup dictionaries for efficient validation
        node_dict = {node.id: node for node in self.nodes}
        valid_relationships = []
        invalid_count = 0
        
        for rel in self.relationships:
            is_valid = True
            
            # Skip relationships with non-existent nodes
            if rel.source not in node_dict or rel.target not in node_dict:
                invalid_count += 1
                continue
                
            # Get source and target nodes
            source_node = node_dict[rel.source]
            target_node = node_dict[rel.target]
            
            # Update the mapped_type using source and target node types for better context
            source_type = source_node.labels.primary
            target_type = target_node.labels.primary
            
            # Remap with better context
            if not rel.mapped_type or rel.mapped_type == rel.type:
                rel.mapped_type = map_relationship_type_combined(
                    rel.type, 
                    source_node_type=source_type,
                    target_node_type=target_type
                )
                
            # If relationship type is in registry, validate source and target types
            if rel.mapped_type in registry['relationships']:
                reg_entry = registry['relationships'][rel.mapped_type]
                
                if 'valid_sources' in reg_entry and 'valid_targets' in reg_entry:
                    valid_source_types = reg_entry['valid_sources']
                    valid_target_types = reg_entry['valid_targets']
                    
                    # Check if any of the source node's types match valid source types
                    source_type_valid = False
                    for label in source_node.labels.all_labels:
                        if label in valid_source_types:
                            source_type_valid = True
                            break
                    
                    # Check if any of the target node's types match valid target types
                    target_type_valid = False
                    for label in target_node.labels.all_labels:
                        if label in valid_target_types:
                            target_type_valid = True
                            break
                    
                    # If either source or target type is invalid, skip this relationship
                    if not source_type_valid:
                        print(f"Warning: Invalid source type {source_node.labels.primary} for relationship {rel.mapped_type}. Expected one of {valid_source_types}")
                        is_valid = False
                        
                    if not target_type_valid:
                        print(f"Warning: Invalid target type {target_node.labels.primary} for relationship {rel.mapped_type}. Expected one of {valid_target_types}")
                        is_valid = False
            
            if is_valid:
                is_valid = not is_forbidden_combination(source_type, rel.mapped_type, target_type)[0]
            # If we get here and relationship is valid, keep it
            if is_valid:
                rel.type, rel.mapped_type = rel.mapped_type, rel.type
                valid_relationships.append(rel)
            else:
                invalid_count += 1
        
        if invalid_count > 0:
            print(f"Removed {invalid_count} invalid relationships during validation")
            
        # Update relationships with only valid ones
        self.relationships = valid_relationships
            
    def export_to_neo4j_json(self):
        """Export the knowledge graph in a format suitable for Neo4j import"""
        neo4j_nodes = []
        for node in self.nodes:
            neo4j_node = {
                "id": node.id,
                "type": node.labels.primary,  # Explicitly include type
                "labels": node.labels.all_labels if hasattr(node.labels, 'all_labels') else [],
                "properties": node.properties or {}  # Ensure properties is never None
            }
            neo4j_nodes.append(neo4j_node)
        
        neo4j_relationships = []
        for rel in self.relationships:
            neo4j_rel = {
                "source_id": rel.source,
                "target_id": rel.target,
                "type": rel.mapped_type or rel.type,
                "properties": rel.properties or {}  # Ensure properties is never None
            }
            neo4j_relationships.append(neo4j_rel)
        
        return {
            "nodes": neo4j_nodes,
            "relationships": neo4j_relationships
        }

class RawOutput(BaseModel):
    nodes: List[Dict[str, Any]]
    relationships: List[Dict[str, Any]] = Field(default_factory=list)

class KnowledgeGraphExtractor:
    """A class to extract structured knowledge graph data from text using Groq and Instructor."""
    
    def __init__(
        self,
        model: str = "meta-llama/llama-4-scout-17b-16e-instruct", # Or any other Groq model you prefer
        temperature: float = 0.2,
        max_tokens: int = 3000,  # Reduced from 4000 to prevent overgeneration
        api_key: Optional[str] = None,
        chunk_size: int = 2000  # Added chunk size parameter for text splitting
    ):
        """Initialize the KnowledgeGraphExtractor."""
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.chunk_size = chunk_size
        
        # Set up the Groq client with Instructor
        self.api_key = api_key or GROQ_API_KEY
        if not self.api_key:
            raise ValueError("Groq API key must be provided either as an argument or in the .env file")
            
        self.client = instructor.from_groq(Groq(api_key=self.api_key), mode=instructor.Mode.JSON)
        
        # Load system prompt
        self.system_prompt = TRIPLET_EXTRACTION_PROMPT.split('## User Text:')[0]
    
    def _chunk_text(self, text: str) -> List[str]:
        """Split text into manageable chunks."""
        words = text.split()
        chunks = []
        current_chunk = []
        current_length = 0
        
        for word in words:
            if current_length + len(word) + 1 > self.chunk_size:
                chunks.append(' '.join(current_chunk))
                current_chunk = [word]
                current_length = len(word)
            else:
                current_chunk.append(word)
                current_length += len(word) + 1  # +1 for space
                
        if current_chunk:
            chunks.append(' '.join(current_chunk))
            
        return chunks
    
    def extract_knowledge_graph(self, text: str) -> KnowledgeGraph:
        """Extract a knowledge graph from text using Instructor."""
        # Split text into manageable chunks
        text_chunks = self._chunk_text(text)
        print(f"Split text into {len(text_chunks)} chunks")
        
        all_nodes = []
        all_relationships = []
        node_ids = set()  # Track unique node IDs to prevent duplicates
        
        full_system_prompt = self.system_prompt + "\n\nAdditional guidelines for node types:\n"
        full_system_prompt += "- Use hierarchical labels for nodes (e.g., a 'Stock' is also a 'FinancialInstrument')\n"
        full_system_prompt += "- Each node should have a primary type and possibly additional types\n"
        full_system_prompt += "- Ensure that relationship types match the expected patterns\n"
        full_system_prompt += "- Focus on extracting ONLY the most important entities and relationships\n"
        full_system_prompt += "- Limit the number of nodes to 30 or fewer\n"  # Added limit
        
        for i, chunk in enumerate(text_chunks):
            print(f"Processing chunk {i+1}/{len(text_chunks)}...")
            user_prompt = f"## User Text (chunk {i+1}/{len(text_chunks)}):\n\n{chunk}"
            
            try:
                # Use a try/except block with fallback options
                try:
                    print(f"Attempting extraction with RawOutput model for chunk {i+1}...")
                    extraction_result = self.client.chat.completions.create(
                        model=self.model,
                        response_model=RawOutput,
                        service_tier="flex",
                        messages=[
                            {"role": "system", "content": full_system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        temperature=self.temperature,
                        max_tokens=self.max_tokens,
                    )
                    
                    # Process extracted nodes, avoiding duplicates
                    for raw_node in extraction_result.nodes:
                        try:
                            node_id = raw_node.get('id')
                            # Skip if we've seen this node ID before
                            if node_id in node_ids:
                                continue
                                
                            # Use Node.create to generate proper hierarchical labels
                            converted_node = Node.create(
                                id=node_id, 
                                type=raw_node.get('type', 'Entity'),
                                properties=raw_node.get('properties', {})
                            )
                            all_nodes.append(converted_node)
                            node_ids.add(node_id)
                        except Exception as node_err:
                            print(f"Error converting node: {node_err}")
                    
                    # Process relationships
                    for raw_rel in extraction_result.relationships:
                        try:
                            converted_rel = Relationship(
                                source=raw_rel['source'],
                                target=raw_rel['target'],
                                type=raw_rel['type'],
                                properties=raw_rel.get('properties', {})
                            )
                            all_relationships.append(converted_rel)
                        except Exception as rel_err:
                            print(f"Error converting relationship: {rel_err}")
                
                except Exception as instructor_err:
                    print(f"Instructor model failed for chunk {i+1}: {instructor_err}")
                    
            except Exception as e:
                print(f"Error processing chunk {i+1}: {e}")
                # Continue to next chunk instead of failing entirely
        
        print(f"Extraction complete. Found {len(all_nodes)} unique nodes and {len(all_relationships)} relationships.")
        
        # Create KnowledgeGraph from all extracted elements
        knowledge_graph = KnowledgeGraph(
            nodes=all_nodes,
            relationships=all_relationships
        )
        
        return self._apply_node_validation(knowledge_graph)
    
    def _apply_node_validation(self, kg: KnowledgeGraph) -> KnowledgeGraph:
        """Apply post-extraction validation and cleanup for nodes."""
        # Validation code remains the same
        valid_nodes = []
        node_ids = set()
        
        # Node Validation
        for node in kg.nodes:
            # Check if node has hierarchical labels, if not, create them
            if not hasattr(node, 'labels') or not isinstance(node.labels, NodeLabels):
                # If this is from a raw extraction that didn't use hierarchical labels
                if hasattr(node, 'type'):
                    # Create a proper node with hierarchical labels
                    primary_type = node.type
                    all_labels = get_node_labels(primary_type)
                    
                    new_node = Node(
                        id=node.id,
                        labels=NodeLabels(primary=primary_type, all_labels=all_labels),
                        properties=node.properties
                    )
                    node = new_node
            
            # Check recommended properties based on registry
            try:
                if node.labels.primary in registry['nodes']:
                    recommended_props = registry['nodes'][node.labels.primary]['props']
                    present_props = set(node.properties.keys())
                    missing_props = [prop for prop in recommended_props if prop not in present_props]
                    
                    # Only require at least one recommended property to be present
                    if missing_props and len(missing_props) == len(recommended_props):
                        print(f"Warning: Node {node.id} has none of the recommended properties: {recommended_props}")
                        # We could add some default properties here if needed
                        # But we'll keep the node since having no recommended properties is allowed
            except Exception as e:
                print(f"Property validation error for node {node.id}: {e}")
            
            # Deduplicate nodes
            if node.id not in node_ids:
                valid_nodes.append(node)
                node_ids.add(node.id)
        
        # Update the nodes in the knowledge graph
        kg.nodes = valid_nodes
        
        return kg
    
    def save_to_json(self, kg: KnowledgeGraph, filepath: str) -> None:
        """Save knowledge graph to JSON file.
        
        Args:
            kg: KnowledgeGraph object containing nodes and relationships
            filepath: Path to save the JSON file
        """
        # Create output structure
        output = {
            "nodes": [node.dict() for node in kg.nodes],
            "relationships": [rel.dict() for rel in kg.relationships]
        }
        
        # Save to file
        with open(filepath, 'w') as f:
            json.dump(output, f, indent=2, default=str)
            
        print(f"Saved knowledge graph to {filepath}")
        print(f"  - {len(kg.nodes)} nodes")
        print(f"  - {len(kg.relationships)} relationships")
    
    def save_to_json(self, kg: KnowledgeGraph, filepath: str) -> None:
        """Save knowledge graph to JSON file.
        
        Args:
            kg: KnowledgeGraph object containing nodes and relationships
            filepath: Path to save the JSON file
        """
        # Create output structure
        output = {
            "nodes": [node.dict() for node in kg.nodes],
            "relationships": [rel.dict() for rel in kg.relationships]
        }
        
        # Save to file
        with open(filepath, 'w') as f:
            json.dump(output, f, indent=2, default=str)
            
        print(f"Saved knowledge graph to {filepath}")
        print(f"  - {len(kg.nodes)} nodes")
        print(f"  - {len(kg.relationships)} relationships")
        
    def save_as_jsonlines(self, kg, filepath, append=True):
        """
        Save all nodes and relationships as JSON Lines with append support.
        If append=True and file exists, new elements will be appended.
        
        Args:
            kg: Knowledge graph object with nodes and relationships
            filepath: Path to save the file
            append: If True, append to existing file; if False, overwrite
        """
        # Determine file mode based on append flag and file existence
        file_exists = os.path.exists(filepath)
        mode = 'a' if append and file_exists else 'w'
        
        with open(filepath, mode) as f:
            # Write all nodes directly
            for node in kg.nodes:
                f.write(json.dumps(node.dict(), default=str) + '\n')
            
            # Write all relationships directly
            for rel in kg.relationships:
                f.write(json.dumps(rel.dict(), default=str) + '\n')
        
        action = "Appended" if append and file_exists else "Saved"
        print(f"{action} {len(kg.nodes) + len(kg.relationships)} elements to {filepath}")

    def save_to_separate_files(self, kg: KnowledgeGraph, base_filepath: str) -> None:
        """Save nodes and relationships to separate JSON files.
        
        Args:
            kg: KnowledgeGraph object containing nodes and relationships
            base_filepath: Base path for output files
        """
        # Save nodes to file
        nodes_data = [node.dict() for node in kg.nodes]
        with open(f"{base_filepath}_nodes.json", 'w') as f:
            json.dump(nodes_data, f, indent=2, default=str)
        
        # Save relationships to file
        relationships_data = [rel.dict() for rel in kg.relationships]
        with open(f"{base_filepath}_relationships.json", 'w') as f:
            json.dump(relationships_data, f, indent=2, default=str)
            
        print(f"Saved {len(kg.nodes)} nodes to {base_filepath}_nodes.json")
        print(f"Saved {len(kg.relationships)} relationships to {base_filepath}_relationships.json")
        
    def visualize_graph(self, kg: KnowledgeGraph) -> None:
        """Generate visualization code for the knowledge graph.
        
        Args:
            kg: KnowledgeGraph object containing nodes and relationships
        """
        # This is a placeholder - in a real implementation, you would generate
        # visualization using a library like D3.js, NetworkX, or Cytoscape
        print("Knowledge Graph Summary:")
        print(f"Nodes ({len(kg.nodes)}):")
        for node in kg.nodes:
            print(f"  - {node.id} ({node.type}): {', '.join([f'{k}={v}' for k, v in node.properties.items()])}")
        
        print(f"\nRelationships ({len(kg.relationships)}):")
        for rel in kg.relationships:
            props = ', '.join([f'{k}={v}' for k, v in rel.properties.items()]) if rel.properties else ""
            print(f"  - {rel.source} --[{rel.type}]--> {rel.target} {f'({props})' if props else ''}")
