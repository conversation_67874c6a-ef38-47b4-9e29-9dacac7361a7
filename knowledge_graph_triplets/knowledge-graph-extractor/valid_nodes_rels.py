primary_nodes = [
    'Company', 'Metric', 'Event', 'Person', 'Location', 'FinancialInstrument', 'Product', 'Index', 'Sector', 
    'Organization', 'Currency', 'Regulation', 'Country', 'Date', 'Year', 'Technology', 'Market', 'Report', 
    'Document', 'Project', 'Industry', 'Concept', 'Service', 'Disease', 'Commodity', 'Conference', 'Platform', 
    'Software', 'Revenue', 'Price', 'Signal', 'Research', 'Patent', 'Algorithm', 'Standard', 'Protocol',
    'Award', 'Certification', 'Dividend', 'Funding', 'IPO', 'Merger', 'Acquisition', 'Partnership', 'Contract',
    'Supply', 'Demand', 'Distribution', 'LogisticsNetwork', 'SupplyChain', 'Trend', 'Risk', 'Opportunity',
    'Innovation', 'Infrastructure', 'Ecosystem', 'MediaOutlet', 'Publication', 'Dataset', 'API', 'Framework',
    'Library', 'Database', 'Cloud', 'Network', 'Campaign', 'Initiative', 'Strategy', 'Policy', 'Forecast',
    'Prediction', 'Resource', 'Material', 'Asset', 'Liability', 'Debt', 'Investment', 'Fund', 'Portfolio',
    'Account', 'Transaction', 'Payment', 'Tax', 'Subsidy', 'Grant', 'Loan', 'Crisis', 'Disaster', 'Recovery'
]

node_type_mappings = {
    "Location": [
        "Country", "Region", "City", "State", "Province", "Continent", 
        "Geographic Region", "Geographic Area", "District", "Neighborhood", "Territory",
        "Zone", "Area", "Locale", "Metropolitan Area", "Suburb", "Rural Area", "Urban Area",
        "Coastal Region", "Island", "Peninsula", "Mountain Range", "Valley", "Desert",
        "Forest", "National Park", "Nature Reserve", "Landmark", "Building", "Campus"
    ],
    
    "Organization": [
        "Group", "Government", "Government Agency", "University", "Non-profit", 
        "Institution", "Hospital", "School", "International Organization", "Corporation",
        "Association", "Foundation", "Research Institute", "Laboratory", "Consortium",
        "Alliance", "Coalition", "Society", "Club", "Commission", "Committee", "Council",
        "Authority", "Agency", "Bureau", "Department", "Board", "Federation", "Union",
        "Cooperative", "Think Tank", "NGO", "Charity", "Trust", "Fund", "Political Party"
    ],
    
    "Report": [
        "Financial Report", "Quarterly Report", "Annual Report", "Market Report",
        "Earnings Report", "Quarterly Results", "Annual Results", "Half-Yearly Report",
        "Quarterly Financial Report", "Half-Yearly Financials", "Interim Results",
        "Audit Report", "Sustainability Report", "Corporate Responsibility Report",
        "ESG Report", "Impact Report", "Performance Report", "Analyst Report",
        "Industry Report", "Trend Report", "Forecast Report", "Research Report",
        "White Paper", "Technical Report", "Case Study", "Survey Report", "Statistical Report",
        "Benchmark Report", "Comparative Analysis", "Competitive Analysis", "SWOT Analysis",
        "Risk Assessment Report", "Due Diligence Report", "Feasibility Study", "Economic Outlook"
    ],
    
    "Document": [
        "Report", "Press Release", "Financial Statement", "Publication", "Article",
        "Paper", "Filing", "SEC Filing", "Proxy Statement", "Sustainability Report",
        "Patent", "Contract", "Agreement", "Memorandum", "Policy", "Regulation",
        "Standard", "Specification", "Manual", "Guide", "Brochure", "Prospectus",
        "Presentation", "Slide Deck", "Infographic", "Chart", "Diagram", "Blueprint",
        "Book", "Journal", "Magazine", "Newsletter", "Blog Post", "Transcript",
        "Letter", "Email", "Memo", "Note", "Review", "Testimonial", "Endorsement",
        "Certificate", "Diploma", "License", "Permit", "Authorization", "Declaration"
    ],
    
    "Price": [
        "Price Increase", "Price Drop", "Price Surge", "Price Change", "Price Rise",
        "Price Movement", "Price Dynamics", "Price Fall", "Price Decline", "Price Range",
        "Price Target", "Price Performance", "Share Price", "Stock Price",
        "Price/Moving Average Price", "Price Volume Charts", "Pricing Strategy",
        "Pricing Model", "Price Point", "Price Tier", "Subscription Price", "Retail Price", 
        "Wholesale Price", "Manufacturer's Suggested Retail Price", "Discount Price",
        "Premium Price", "Entry-level Price", "Luxury Price", "Value Price", "Dynamic Price",
        "Surge Price", "Promotional Price", "Clearance Price", "Markdown", "Markup",
        "Cost Plus Pricing", "Value-Based Pricing", "Competitive Pricing", "Penetration Pricing",
        "Skimming Pricing", "Psychological Pricing", "Bundle Pricing", "Freemium Pricing"
    ],
    
    "Signal": [
        "Bearish Signal", "Bullish Signal", "Stock Surge", "Bearish Turning Point",
        "Bullish Turning Point", "Buy Signal", "Short Signal", "Oversold Signal",
        "Overbought Signal", "Bullish Signals", "Bearish Signals", "Bearish Breakout",
        "Bullish Breakout", "AI-Generated Signals", "Technical Signal", "Fundamental Signal",
        "Momentum Signal", "Reversal Signal", "Trend Continuation Signal", "Divergence Signal",
        "Convergence Signal", "Price Action Signal", "Volume Signal", "Moving Average Signal",
        "Relative Strength Signal", "Oscillator Signal", "Sentiment Signal", "Volatility Signal",
        "Support Signal", "Resistance Signal", "Breakout Signal", "Breakdown Signal",
        "Consolidation Signal", "Accumulation Signal", "Distribution Signal", "Smart Money Signal",
        "Insider Activity Signal", "Institutional Flow Signal", "Retail Investor Signal", "Option Flow Signal"
    ],
    
    "Market": [
        "Market Trend", "Market Analysis", "Market Sentiment", "Market Forecast",
        "Market Research Report", "Market Research", "Market Performance", 
        "Market Condition", "Market Dynamics", "Market Movement", "Market Growth",
        "Market Share", "Market Cap", "Market Cap Increase", "Market Cap Decline",
        "Market Capitalization", "Market Size", "Market Segment", "Market Decline",
        "Market Correction", "Market Crash", "Market Volatility", "Market Recovery",
        "Market Downturn", "Market Disruption", "Market Evolution", "Market Maturity",
        "Market Saturation", "Market Penetration", "Market Development", "Market Entry",
        "Market Exit", "Market Consolidation", "Market Expansion", "Market Contraction",
        "Market Fit", "Market Opportunity", "Market Barrier", "Market Risk", "Market Failure",
        "Market Equilibrium", "Market Inefficiency", "Market Bubble", "Market Cycle",
        "Market Rotation", "Market Breadth", "Market Depth", "Market Liquidity", "Market Structure",
        "Primary Market", "Secondary Market", "Spot Market", "Futures Market", "Options Market",
        "Emerging Market", "Developed Market", "Frontier Market", "Bear Market", "Bull Market"
    ],
    
    "Metric": [
        "Financial Metric", "Revenue", "Revenue Growth", "Revenue Increase", 
        "Revenue Decline", "Net Profit", "Net Profit Margin", "Net Profit Increase",
        "Net Profit Growth", "Earnings Per Share", "EPS Growth", "Return on Equity",
        "Return on Assets", "Return on Capital Employed", "Profit", "EBITDA",
        "Free Cash Flow", "Operating Cash Flow", "Gross Margin", "Net Income",
        "Earnings", "Earnings Yield", "Earnings Growth", "Earnings Surprise",
        "Earnings Beat", "Earnings Miss", "Price-to-Earnings Ratio", "Forward P/E",
        "Trailing P/E", "Price-to-Sales Ratio", "Price-to-Book Ratio", "Enterprise Value",
        "EV/EBITDA", "Dividend Yield", "Dividend Payout Ratio", "Dividend Growth Rate",
        "Share Buyback", "Debt-to-Equity Ratio", "Debt-to-Assets Ratio", "Leverage Ratio",
        "Current Ratio", "Quick Ratio", "Cash Ratio", "Asset Turnover", "Inventory Turnover",
        "Days Sales Outstanding", "Days Payable Outstanding", "Working Capital", "Operating Margin",
        "Profit Margin", "Gross Profit", "Operating Profit", "Net Cash Position", "Cash Burn Rate"
    ],
    
    "Date": [
        "Year", "Month", "Period", "Financial Year", "Quarter", "Time Period",
        "Timeframe", "Fiscal Year", "Financial Quarter", "Financial Period",
        "Quarterly Period", "Day", "Day of the Week", "Trading Day", "Business Day",
        "Holiday", "Weekend", "Season", "Decade", "Century", "Millennium", "Era",
        "Age", "Epoch", "Evening", "Morning", "Afternoon", "Night", "Midnight",
        "Dawn", "Dusk", "Hour", "Minute", "Second", "Week", "Fortnight", "Semester",
        "Trimester", "Anniversary", "Birthday", "Milestone", "Deadline", "Session"
    ],
    
    "Product": [
        "Software", "Software Service", "Hardware", "Electric Vehicle",
        "Smartphone", "Medical Device", "Drug", "Food Product", "Pharmaceutical",
        "Vaccine", "Biologic", "Processor", "Graphics Card", "Sensor",
        "Camera", "Smartwatch", "Laptop", "Tablet", "Desktop Computer", "Server",
        "Network Equipment", "IoT Device", "Wearable", "Connected Device", "Consumer Electronics",
        "Appliance", "Tool", "Machinery", "Equipment", "Vehicle", "Aircraft", "Vessel",
        "Satellite", "Drone", "Robot", "Automation System", "Diagnostic Tool", "Testing Equipment",
        "Scientific Instrument", "Measurement Device", "Communication Device", "Entertainment System",
        "Gaming Console", "Virtual Reality Headset", "Augmented Reality Device", "3D Printer",
        "Battery", "Power Supply", "Energy Storage", "Renewable Energy System", "Chemical Product",
        "Organic Product", "Synthetic Product", "Genetically Modified Product", "Natural Product"
    ],
    
    "FinancialInstrument": [
        "Stock", "Stocks", "Bond", "ETF", "Commodity", "Currency", "Cryptocurrency",
        "Equity", "Preferred Stock", "Common Stock", "Mutual Fund", "Exchange-Traded Fund",
        "Investment Fund", "Venture Capital", "Private Equity", "Hedge Fund", "Index Fund",
        "Treasury Bill", "Treasury Note", "Treasury Bond", "Municipal Bond", "Corporate Bond",
        "Government Bond", "High-Yield Bond", "Convertible Bond", "Zero-Coupon Bond", "Fixed-Income",
        "Asset-Backed Security", "Mortgage-Backed Security", "Collateralized Debt Obligation",
        "Derivative", "Option", "Futures Contract", "Forward Contract", "Swap", "Credit Default Swap",
        "Interest Rate Swap", "Real Estate Investment Trust", "Certificate of Deposit", "Commercial Paper",
        "Money Market Fund", "Pension Fund", "Annuity", "Insurance Policy", "Structured Product", "Warrant"
    ],
    
    "Index": [
        "Stock Index", "Stock Market Index", "Currency Index", "Price Index",
        "Performance Index", "Composite Index", "Sector Index", "Industry Index",
        "Blue-Chip Index", "Large-Cap Index", "Mid-Cap Index", "Small-Cap Index",
        "Growth Index", "Value Index", "Dividend Index", "Equal-Weight Index",
        "Market-Cap Weighted Index", "Price-Weighted Index", "Fundamentally Weighted Index",
        "Volatility Index", "Bond Index", "Credit Index", "Fixed-Income Index",
        "Commodity Index", "Inflation Index", "Consumer Price Index", "Producer Price Index",
        "Housing Price Index", "Economic Index", "Business Cycle Index", "Sentiment Index",
        "Confidence Index", "Manufacturing Index", "Services Index", "Employment Index",
        "Unemployment Index", "Leading Indicator", "Lagging Indicator", "Coincident Indicator"
    ],
    
    "Event": [
        "CEO Appointment", "Annual General Meeting", "Acquisition", "Dividend Payment",
        "Partnership", "Merger", "Joint Venture", "Product Launch", "Board Meeting",
        "Board Appointment", "Executive Appointment", "CFO Appointment", 
        "Dividend Announcement", "Earnings Call", "Conference Call", "Capital Markets Day",
        "Share Buyback", "Share Repurchase", "Buyback", "IPO", "Public Offering", "Secondary Offering",
        "Private Placement", "Debt Offering", "Bond Issuance", "Stock Split", "Reverse Stock Split",
        "Spinoff", "Divestiture", "Bankruptcy Filing", "Restructuring", "Reorganization", 
        "Corporate Action", "Shareholder Meeting", "Proxy Fight", "Activist Campaign",
        "Short Seller Attack", "Whistleblower Revelation", "Accounting Issue", "Financial Restatement",
        "Audit Finding", "Regulatory Investigation", "Legal Settlement", "Court Ruling", "Patent Grant",
        "Patent Expiration", "Licensing Agreement", "Market Entry", "Market Exit", "Facility Opening",
        "Facility Closure", "Layoffs", "Hiring Wave", "Strike", "Labor Dispute", "Trade Show", "Award Ceremony",
        "Industry Conference", "Analyst Day", "Investor Day", "Media Event", "Press Conference"
    ],
    
    "Person": [
        "CEO", "Executive", "Manager", "Employee", "Founder", "Co-Founder", "Entrepreneur",
        "Investor", "Shareholder", "Stakeholder", "Board Member", "Director", "Chairperson",
        "Analyst", "Researcher", "Scientist", "Engineer", "Developer", "Designer", "Architect",
        "Consultant", "Advisor", "Specialist", "Expert", "Authority", "Thought Leader", "Influencer",
        "Politician", "Regulator", "Policy Maker", "Legislator", "Judge", "Lawyer", "Accountant",
        "Auditor", "Economist", "Strategist", "Marketer", "Sales Representative", "Customer",
        "Client", "Partner", "Supplier", "Distributor", "Competitor", "Collaborator", "Mentor",
        "Protégé", "Student", "Teacher", "Professor", "Author", "Journalist", "Editor", "Publisher"
    ],
    
    "Technology": [
        "Artificial Intelligence", "Machine Learning", "Deep Learning", "Natural Language Processing",
        "Computer Vision", "Robotics", "Automation", "Internet of Things", "Blockchain", "Distributed Ledger",
        "Cloud Computing", "Edge Computing", "Quantum Computing", "Virtual Reality", "Augmented Reality",
        "Mixed Reality", "3D Printing", "Additive Manufacturing", "Nanotechnology", "Biotechnology",
        "Gene Editing", "CRISPR", "Stem Cell", "Renewable Energy", "Solar Power", "Wind Power",
        "Hydroelectric Power", "Geothermal Energy", "Nuclear Energy", "Battery Technology", "Energy Storage",
        "Electric Vehicle Technology", "Autonomous Vehicle", "Drone Technology", "Satellite Technology",
        "5G", "6G", "Wireless Technology", "Fiber Optic", "Semiconductor", "Microprocessor", "Integrated Circuit",
        "Display Technology", "OLED", "QLED", "MicroLED", "Biometric", "Facial Recognition", "Voice Recognition",
        "Fingerprint Scanning", "Iris Scanning", "Cybersecurity", "Encryption", "Quantum Encryption",
        "Big Data", "Data Analytics", "Predictive Analytics", "Prescriptive Analytics", "Data Mining",
        "Data Visualization", "Business Intelligence", "Enterprise Resource Planning", "Customer Relationship Management"
    ],
    
    "Research": [
        "Scientific Research", "Clinical Research", "Medical Research", "Pharmaceutical Research",
        "Engineering Research", "Market Research", "Consumer Research", "Social Research",
        "Behavioral Research", "Economic Research", "Financial Research", "Investment Research",
        "Academic Research", "Industrial Research", "Applied Research", "Basic Research",
        "Fundamental Research", "Experimental Research", "Theoretical Research", "Empirical Research",
        "Qualitative Research", "Quantitative Research", "Mixed Methods Research", "Longitudinal Research",
        "Cross-Sectional Research", "Case Study", "Survey Research", "Observational Research",
        "Field Research", "Laboratory Research", "Action Research", "Participatory Research",
        "Interdisciplinary Research", "Multidisciplinary Research", "Collaborative Research",
        "Investigative Research", "Exploratory Research", "Descriptive Research", "Explanatory Research",
        "Predictive Research", "Comparative Research", "Historical Research", "Meta-Analysis", "Systematic Review"
    ],
    
    "Patent": [
        "Utility Patent", "Design Patent", "Plant Patent", "Provisional Patent", "Non-Provisional Patent",
        "International Patent", "PCT Application", "Continuation Patent", "Divisional Patent",
        "Continuation-in-Part", "Software Patent", "Hardware Patent", "Process Patent", "Method Patent",
        "Composition Patent", "Pharmaceutical Patent", "Biological Patent", "Mechanical Patent",
        "Electrical Patent", "Chemical Patent", "Medical Device Patent", "Biotechnology Patent",
        "Nanotechnology Patent", "Green Technology Patent", "AI Patent", "Machine Learning Patent",
        "Computer Vision Patent", "Natural Language Processing Patent", "Blockchain Patent",
        "Internet of Things Patent", "Renewable Energy Patent", "Clean Energy Patent", "Battery Patent",
        "Electric Vehicle Patent", "Autonomous Vehicle Patent", "Robotics Patent", "Semiconductor Patent",
        "Telecommunications Patent", "Wireless Technology Patent", "Display Technology Patent", "5G Patent"
    ],
    
    "Strategy": [
        "Business Strategy", "Corporate Strategy", "Growth Strategy", "Market Entry Strategy",
        "Expansion Strategy", "Diversification Strategy", "Acquisition Strategy", "Merger Strategy",
        "Divestiture Strategy", "Innovation Strategy", "R&D Strategy", "Product Strategy",
        "Service Strategy", "Marketing Strategy", "Sales Strategy", "Pricing Strategy",
        "Distribution Strategy", "Channel Strategy", "Digital Strategy", "E-commerce Strategy",
        "Mobile Strategy", "Social Media Strategy", "Content Strategy", "Advertising Strategy",
        "Branding Strategy", "Customer Experience Strategy", "Customer Acquisition Strategy",
        "Customer Retention Strategy", "Loyalty Strategy", "Competitive Strategy", "Differentiation Strategy",
        "Cost Leadership Strategy", "Focus Strategy", "Blue Ocean Strategy", "Red Ocean Strategy",
        "Data Strategy", "Analytics Strategy", "IT Strategy", "Technology Strategy", "Sourcing Strategy",
        "Supply Chain Strategy", "Logistics Strategy", "Manufacturing Strategy", "Operations Strategy",
        "Human Resources Strategy", "Talent Management Strategy", "Organizational Strategy",
        "Change Management Strategy", "Risk Management Strategy", "Financial Strategy", "Investment Strategy",
        "Capital Allocation Strategy", "Funding Strategy", "Exit Strategy", "Sustainability Strategy",
        "ESG Strategy", "Corporate Social Responsibility Strategy", "Regulatory Strategy", "Compliance Strategy",
        "Legal Strategy", "IP Strategy", "Patent Strategy", "Licensing Strategy"
    ]
}

primary_relationships = [
    'AFFECTS', 'LEADS', 'REPORTED', 'COMPETES_WITH', 'PARTNERS_WITH', 'LOCATED_IN', 'PRODUCES', 'OWNS', 'CAUSES',
    'INVESTED_IN', 'PART_OF', 'LAUNCHED', 'USES', 'LISTED_ON', 'HAS', 'ACQUIRED', 'COMPARED_TO', 'INFLUENCED_BY',
    'PARTICIPATES_IN', 'OPERATES_IN', 'TRADED_IN', 'RANKED', 'REPORTS', 'IS_A', 'EXCHANGE_RATE', 'WORKED_AT',
    'SERVES', 'APPOINTED', 'RELATED_TO', 'CONTAINS', 'REGULATED_BY', 'TREATS', 'IMPORTS', 'REGULATES', 'IS',
    'EXPORTS', 'REQUIRES', 'SUPPORTS', 'DEVELOPS', 'RECOMMENDS', 'COMPATIBLE_WITH', 'WORKS_FOR', 'PARTICIPATED_IN',
    'OFFERS', 'ANNOUNCED', 'MEMBER_OF', 'RANKED_BY', 'APPROVED', 'INCLUDED_IN', 'OPERATES', 'COVERS', 'INFLUENCED',
    'CONSUMES', 'HOSTS', 'EXCHANGED_WITH', 'CREATED', 'RANKS', 'MEMBER', 'AWARDED', 'INVESTS_IN', 'DEVELOPED',
    'REPORTS_ON', 'ISSUED', 'COMPLIES_WITH', 'EMPLOYED', 'ISSUED_BY', 'PROVIDES', 'EDUCATED_AT', 'LISTED',
    'WORKS_AT', 'EMPLOYED_BY', 'FORECASTS', 'FEATURES', 'IS_PART_OF', 'OWNED_BY', 'INTEGRATES_WITH', 'APPLIES_TO',
    'APPROVES', 'USED_IN', 'AFFECTED_BY', 'SCHEDULED', 'TRACKS', 'APPLICABLE_TO', 'LOCATED_NEAR', 'PRODUCED_BY',
    'ANALYZED', 'USED', 'FOUNDED', 'MEASURED_BY', 'MEETS', 'PUBLISHED', 'INFLUENCES', 'COMPARES_TO', 'TARGETS',
    'IMPLEMENTED', 'ANALYZES', 'REVIEWED', 'PROPOSED', 'OPPOSES', 'APPLIES', 'FUNDS', 'SPONSORS', 'MANAGES',
    'DISTRIBUTES', 'SELLS', 'BUYS', 'LICENSES', 'PATENTS', 'CERTIFIES', 'AUTHORIZES', 'INNOVATES', 'DISRUPTS',
    'TRANSFORMS', 'SUPERSEDES', 'REPLACES', 'ENHANCES', 'EXTENDS', 'COMPLEMENTS', 'COMPLETES', 'DEPENDS_ON',
    'ENABLES', 'FACILITATES', 'ACCELERATES', 'SLOWS', 'PREVENTS', 'REDUCES', 'INCREASES', 'EXPANDS', 'CONTRACTS',
    'PREDICTS', 'DETECTS', 'MEASURES', 'CALCULATES', 'ESTIMATES', 'SIMULATES', 'MODELS', 'DESIGNS', 'ARCHITECTS',
    'ENGINEERS', 'CONSTRUCTS', 'INSTALLS', 'IMPLEMENTS', 'DEPLOYS', 'MAINTAINS', 'SUPPORTS', 'SERVICES', 'REPAIRS',
    'UPGRADES', 'IMPROVES', 'OPTIMIZES', 'DOWNGRADES', 'DEPRECATES', 'DISCONTINUES', 'ELIMINATES', 'TERMINATES'
]

primary_relationships_by = {
    "AFFECTS": "AFFECTED_BY",
    "LEADS": "LED_BY",
    "REPORTED": "REPORTS",
    "LOCATED_IN": "CONTAINS",
    "PRODUCES": "PRODUCED_BY",
    "OWNS": "OWNED_BY",
    "CAUSES": "CAUSED_BY",
    "INVESTED_IN": "INVESTS_IN",
    "PART_OF": "CONTAINS",
    "LAUNCHED": "LAUNCHED_BY",
    "USES": "USED_BY",
    "LISTED_ON": "LISTS",
    "HAS": "BELONGS_TO",
    "ACQUIRED": "ACQUIRED_BY",
    "INFLUENCED_BY": "INFLUENCES",
    "PARTICIPATES_IN": "HAS_PARTICIPANT",
    "OPERATES_IN": "HOSTS_OPERATION",
    "TRADED_IN": "HOSTS_TRADING",
    "RANKED": "RANKS",
    "REPORTS": "REPORTED_BY",
    "IS_A": "TYPE_OF",
    "WORKED_AT": "EMPLOYS",
    "SERVES": "SERVED_BY",
    "APPOINTED": "APPOINTED_BY",
    "CONTAINS": "LOCATED_IN",
    "REGULATED_BY": "REGULATES",
    "TREATS": "TREATED_BY",
    "IMPORTS": "EXPORTED_BY",
    "REGULATES": "REGULATED_BY",
    "IS": "TYPE_OF",
    "EXPORTS": "IMPORTED_BY",
    "REQUIRES": "REQUIRED_BY",
    "SUPPORTS": "SUPPORTED_BY",
    "DEVELOPS": "DEVELOPED_BY",
    "RECOMMENDS": "RECOMMENDED_BY",
    "WORKS_FOR": "EMPLOYS",
    "PARTICIPATED_IN": "HAS_PARTICIPANT",
    "OFFERS": "OFFERED_BY",
    "ANNOUNCED": "ANNOUNCED_BY",
    "MEMBER_OF": "HAS_MEMBER",
    "RANKED_BY": "RANKS",
    "APPROVED": "APPROVED_BY",
    "INCLUDED_IN": "INCLUDES",
    "OPERATES": "OPERATED_BY",
    "COVERS": "COVERED_BY",
    "INFLUENCED": "INFLUENCES",
    "CONSUMES": "CONSUMED_BY",
    "HOSTS": "HOSTED_BY",
    "CREATED": "CREATED_BY",
    "RANKS": "RANKED_BY",
    "MEMBER": "HAS_MEMBER",
    "AWARDED": "AWARDED_BY",
    "INVESTS_IN": "INVESTED_IN",
    "DEVELOPED": "DEVELOPED_BY",
    "REPORTS_ON": "REPORTED_ON_BY",
    "ISSUED": "ISSUED_BY",
    "COMPLIES_WITH": "REQUIREMENT_FOR",
    "EMPLOYED": "EMPLOYS",
    "ISSUED_BY": "ISSUES",
    "PROVIDES": "PROVIDED_BY",
    "EDUCATED_AT": "EDUCATES",
    "LISTED": "LISTED_BY",
    "WORKS_AT": "EMPLOYS",
    "EMPLOYED_BY": "EMPLOYS",
    "FORECASTS": "FORECASTED_BY",
    "FEATURES": "FEATURED_BY",
    "IS_PART_OF": "CONTAINS",
    "OWNED_BY": "OWNS",
    "APPLIES_TO": "SUBJECT_OF",
    "APPROVES": "APPROVED_BY",
    "USED_IN": "USES",
    "AFFECTED_BY": "AFFECTS",
    "SCHEDULED": "SCHEDULED_BY",
    "TRACKS": "TRACKED_BY",
    "APPLICABLE_TO": "GOVERNED_BY",
    "PRODUCED_BY": "PRODUCES",
    "ANALYZED": "ANALYZED_BY",
    "USED": "USED_BY",
    "FOUNDED": "FOUNDED_BY",
    "MEASURED_BY": "MEASURES",
    "PUBLISHED": "PUBLISHED_BY",
    "INFLUENCES": "INFLUENCED_BY",
    "TARGETS": "TARGETED_BY",
    "IMPLEMENTED": "IMPLEMENTED_BY",
    "ANALYZES": "ANALYZED_BY",
    "REVIEWED": "REVIEWED_BY",
    "PROPOSED": "PROPOSED_BY",
    "OPPOSES": "OPPOSED_BY",
    "APPLIES": "APPLIED_BY",
    "FUNDS": "FUNDED_BY",
    "SPONSORS": "SPONSORED_BY",
    "MANAGES": "MANAGED_BY",
    "DISTRIBUTES": "DISTRIBUTED_BY",
    "SELLS": "SOLD_BY",
    "BUYS": "BOUGHT_BY",
    "LICENSES": "LICENSED_BY",
    "PATENTS": "PATENTED_BY",
    "CERTIFIES": "CERTIFIED_BY",
    "AUTHORIZES": "AUTHORIZED_BY",
    "INNOVATES": "INNOVATION_OF",
    "DISRUPTS": "DISRUPTED_BY",
    "TRANSFORMS": "TRANSFORMED_BY",
    "SUPERSEDES": "SUPERSEDED_BY",
    "REPLACES": "REPLACED_BY",
    "ENHANCES": "ENHANCED_BY",
    "EXTENDS": "EXTENDED_BY",
    "COMPLEMENTS": "COMPLEMENTED_BY",
    "COMPLETES": "COMPLETED_BY",
    "DEPENDS_ON": "DEPENDENCY_FOR",
    "ENABLES": "ENABLED_BY",
    "FACILITATES": "FACILITATED_BY",
    "ACCELERATES": "ACCELERATED_BY",
    "SLOWS": "SLOWED_BY",
    "PREVENTS": "PREVENTED_BY",
    "REDUCES": "REDUCED_BY",
    "INCREASES": "INCREASED_BY",
    "EXPANDS": "EXPANDED_BY",
    "CONTRACTS": "CONTRACTED_BY",
    "PREDICTS": "PREDICTED_BY",
    "DETECTS": "DETECTED_BY",
    "MEASURES": "MEASURED_BY",
    "CALCULATES": "CALCULATED_BY",
    "ESTIMATES": "ESTIMATED_BY",
    "SIMULATES": "SIMULATED_BY",
    "MODELS": "MODELED_BY",
    "DESIGNS": "DESIGNED_BY",
    "ARCHITECTS": "ARCHITECTED_BY",
    "ENGINEERS": "ENGINEERED_BY",
    "CONSTRUCTS": "CONSTRUCTED_BY",
    "INSTALLS": "INSTALLED_BY",
    "IMPLEMENTS": "IMPLEMENTED_BY",
    "DEPLOYS": "DEPLOYED_BY",
    "MAINTAINS": "MAINTAINED_BY",
    "SERVICES": "SERVICED_BY",
    "REPAIRS": "REPAIRED_BY",
    "UPGRADES": "UPGRADED_BY",
    "IMPROVES": "IMPROVED_BY",
    "OPTIMIZES": "OPTIMIZED_BY",
    "DOWNGRADES": "DOWNGRADED_BY",
    "DEPRECATES": "DEPRECATED_BY",
    "DISCONTINUES": "DISCONTINUED_BY",
    "ELIMINATES": "ELIMINATED_BY",
    "TERMINATES": "TERMINATED_BY"
}

# Relationship groups
# these are deduplicated !! be careful with additions!
primary_relationship_types = {
    "AFFECTS": [
        "IMPACTS", "INFLUENCES", "CHANGES", "ALTERS", "MODIFIES", "IMPACTS_ON", 
        "HAS_IMPACT_ON", "EFFECTS", "CAUSES_CHANGE_IN", "DISTURBS", "TRANSFORMS", "SHAPES", 
        "REDEFINES", "CONTRIBUTES_TO_CHANGE_IN", "DRIVES_CHANGE_IN", "CATALYZES_CHANGE_IN", 
        "DETERMINES_OUTCOME_OF", "PIVOTAL_FOR", "CRITICAL_FOR", "DECISIVE_FOR"
    ],   
    
    "LEADS": [
        "DIRECTS", "MANAGES", "HEADS", "RUNS", "SUPERVISES", "COMMANDS", "CONTROLS", 
        "OVERSEES", "GUIDES", "CONDUCTS", "STEERS", "CHAIRS", "IS_CEO_OF", "IS_DIRECTOR_OF", 
        "ADMINISTERS", "GOVERNS", "PRESIDES_OVER", "COORDINATES", "SHEPHERDS", "SPEARHEADS", 
        "PILOTS", "CAPTAINS", "NAVIGATES", "HELMS", "IS_AT_HELM_OF", "IS_IN_CHARGE_OF", 
        "IS_RESPONSIBLE_FOR", "IS_ACCOUNTABLE_FOR", "IS_LEADER_OF", "IS_CHIEF_OF", 
        "IS_HEAD_OF", "IS_EXECUTIVE_OF"
    ],
    
    "REPORTED": [
        "REPORTS_ON", "REPORTS_TO", "REPORTED_BY", "DOCUMENTS", "ANNOUNCES", "DISCLOSES", 
        "PUBLISHES", "ISSUES_REPORT", "COMMUNICATES", "NOTIFIES", "INFORMS_ABOUT", 
        "FILES_REPORT_ON", "JOURNALS", "RECORDS", "RELEASES_INFORMATION_ON", "SHARES_DATA_ON", 
        "PRESENTS_FINDINGS_ON", "BROADCASTS", "CIRCULATES", "DISSEMINATES", 
        "DISTRIBUTES_INFORMATION_ON", "RELEASES_STATEMENT_ON", "ISSUES_PRESS_RELEASE_ON", 
        "PROVIDES_DETAILS_ON", "TELEGRAPHS", "SIGNALS", "ARTICULATES", "TRANSMITS_INFORMATION_ON", 
        "CONVEYS_DETAILS_ABOUT", "RELAYS_INFORMATION_ON", "EXPRESSES", "VERBALIZES"
    ],
    
    "COMPETES_WITH": [
        "COMPETES_AGAINST", "COMPETES_IN", "COMPETING_WITH", "RIVALS", "CONTENDS_WITH", 
        "CHALLENGES", "VIES_WITH", "OPPOSES", "IN_COMPETITION_WITH", "BATTLES", "FEUDS_WITH", 
        "CONTESTS_WITH", "CLASHES_WITH", "CONFRONTS", "COUNTERS", "COMBATS", "CONTESTS", 
        "DISPUTES_WITH", "WRESTLES_WITH", "STRIVES_AGAINST", "STRUGGLES_AGAINST", 
        "FACES_OFF_AGAINST", "TAKES_ON", "GOES_UP_AGAINST", "SQUARES_OFF_AGAINST", 
        "LOCKS_HORNS_WITH", "IN_RIVALRY_WITH", "ANTAGONIZES", "JOUSTS_WITH", "DUELS_WITH", 
        "SPARS_WITH"
    ],
    
    "LOCATED_IN": [
        "LOCATED_AT", "LOCATED_NEAR", "HEADQUARTERED_IN", "BASED_IN", "SITUATED_IN", 
        "POSITIONED_AT", "ESTABLISHED_IN", "FOUND_IN", "PLACED_IN", "RESIDES_IN", 
        "DOMICILED_IN", "OPERATES_FROM", "HOUSED_IN", "STATIONED_AT", "CENTERED_IN", 
        "ANCHORED_IN", "INSTALLED_IN", "LODGED_IN", "FIXED_IN", "SETTLED_IN", "ROOTED_IN", 
        "GROUNDED_IN", "PLANTED_IN", "NESTLED_IN", "ENSCONCED_IN", "ACCOMMODATED_IN", 
        "EMPLACED_IN", "SHELTERED_IN", "QUARTERED_IN", "BILLETED_IN", "ENCAMPED_IN", 
        "HOSTED_IN", "HARBORED_IN"
    ],
    
    "OWNS": [
        "OWNED_BY", "OWNER", "POSSESSES", "HOLDS", "PROPRIETOR_OF", "HAS_OWNERSHIP_OF", 
        "BELONGS_TO", "PROPERTY_OF", "ACQUIRES", "HAS_TITLE_TO", "RETAINS", "KEEPS", 
        "LAYS_CLAIM_TO", "HAS_RIGHTS_TO", "LEGALLY_POSSESSES", "ENTITLED_TO", "CUSTODIAN_OF", 
        "GUARDIAN_OF", "STEWARD_OF", "ADMINISTRATOR_OF", "HOLDS_DEED_TO", "HOLDS_RIGHTS_TO", 
        "HAS_DOMINION_OVER", "LORDS_OVER", "SOVEREIGN_OF", "RULER_OF", "MASTER_OF", 
        "BOSS_OF", "IN_POSSESSION_OF", "OCCUPIES", "SEIZES", "CAPTURES", "SECURES", 
        "ASSUMES_OWNERSHIP_OF", "CLAIMS", "APPROPRIATES", "EXPROPRIATES", "CONFISCATES", 
        "COMMANDEERS", "REQUISITIONS", "ANNEXES", "ASSUMES_CONTROL_OF"
    ],
    
    "PARTNERS_WITH": [
        "PARTNERED_WITH", "PARTNERSHIP", "COLLABORATES_WITH", "ALLIES_WITH", "JOINS_WITH", 
        "COOPERATES_WITH", "TEAMS_UP_WITH", "ASSOCIATES_WITH", "WORKS_WITH", "AFFILIATE_WITH", 
        "JOINT_VENTURE_WITH", "ALIGNS_WITH", "CONFEDERATES_WITH", "CONSORTS_WITH", 
        "BANDS_TOGETHER_WITH", "UNITES_WITH", "COMBINES_FORCES_WITH", "PULLS_TOGETHER_WITH", 
        "LINKS_UP_WITH", "NETWORKS_WITH", "CONNECTS_WITH", "LEAGUES_WITH", "FEDERATES_WITH", 
        "CONSOLIDATES_WITH", "MERGES_WITH", "JOINS_FORCES_WITH", "COLLUDES_WITH", 
        "CONSPIRES_WITH", "SCHEMES_WITH", "PLOTS_WITH", "GANGS_UP_WITH", "BONDS_WITH", 
        "FORMS_ALLIANCE_WITH", "FORMS_COALITION_WITH", "FORMS_UNION_WITH", "FORMS_SYNDICATE_WITH", 
        "FORMS_CONSORTIUM_WITH", "FORMS_CONFEDERACY_WITH", "FORMS_LEAGUE_WITH", 
        "FORMS_FEDERATION_WITH", "FORMS_ASSOCIATION_WITH", "FORMS_SOCIETY_WITH", 
        "FORMS_CLUB_WITH", "FORMS_COOPERATIVE_WITH"
    ],
    
    "INVESTED_IN": [
        "INVESTS_IN", "INVESTOR", "FUNDS", "FINANCES", "BACKS", "STAKES_IN", "PUTS_MONEY_IN", 
        "CAPITALIZES", "SUBSIDIZES", "UNDERWRITES", "SPONSORS", "BANKROLLS", "ALLOCATES_CAPITAL_TO", 
        "INJECTS_CAPITAL_INTO", "PROVIDES_FUNDING_FOR", "SUPPORTS_FINANCIALLY", "ENDOWS", 
        "GRANTS_MONEY_TO", "CONTRIBUTES_CAPITAL_TO", "VENTURE_CAPITAL_FOR", "SEED_CAPITAL_FOR", 
        "ANGEL_INVESTMENT_IN", "PRIVATE_EQUITY_IN", "PUTS_STAKE_IN", "BUYS_INTO", 
        "PURCHASES_SHARES_IN", "ACQUIRES_STOCK_IN", "OBTAINS_INTEREST_IN", "SECURES_POSITION_IN", 
        "ESTABLISHES_HOLDING_IN", "COMMITS_RESOURCES_TO", "DEVOTES_ASSETS_TO", "ASSIGNS_CAPITAL_TO", 
        "EARMARKS_FUNDS_FOR"
    ],
    
    "PART_OF": [
        "IS_PART_OF", "SUBSIDIARY_OF", "COMPONENT_OF", "DIVISION_OF", "SEGMENT_OF", "BRANCH_OF", 
        "SECTION_OF", "UNIT_OF", "ELEMENT_OF", "MEMBER_OF", "CONSTITUTES", "INCLUDED_IN", 
        "INCORPORATED_IN", "CONTAINED_IN", "HOUSED_WITHIN", "INTEGRATED_IN", "EMBEDDED_IN", 
        "NESTED_WITHIN", "RESIDES_WITHIN", "SITS_WITHIN", "POSITIONED_WITHIN", "LOCATED_WITHIN", 
        "FOUND_WITHIN", "SUBSUMED_BY", "ENCOMPASSED_BY", "EMBRACED_BY", "ENVELOPED_BY", 
        "ENCAPSULATED_BY", "ENCLOSED_BY", "SURROUNDED_BY", "BOUNDED_BY", "DELIMITED_BY", 
        "CLASSIFIED_UNDER", "CATEGORIZED_UNDER", "GROUPED_UNDER", "FALL_UNDER", "COMES_UNDER", 
        "UMBRELLA_OF", "SUBSET_OF", "SLICE_OF", "PORTION_OF", "FRACTION_OF", "PERCENTAGE_OF", 
        "FRAGMENT_OF", "INGREDIENT_OF", "CONSTITUENT_OF", "BUILDING_BLOCK_OF"
    ],
    
    "CREATED": [
        "FOUNDED", "CREATES", "ESTABLISHES", "DEVELOPS", "ORIGINATES", "LAUNCHES", "INVENTS", 
        "FORMS", "INSTITUTES", "STARTS", "SETS_UP", "ORGANIZES", "DEVISES", "COMPOSES", 
        "CONCEIVES", "DESIGNS", "ENGINEERS", "ARCHITECTS", "MASTERMINDS", "SCULPTS", "ERECTS", 
        "RAISES", "BRINGS_FORTH", "CAUSES_TO_EXIST", "MAKES_HAPPEN", "FORMULATES", "INAUGURATES", 
        "INTRODUCES", "PIONEERS"
    ],
    
    "CAUSES": [
        "TRIGGERS", "INDUCES", "RESULTS_IN", "LEADS_TO", "BRINGS_ABOUT", "PRECIPITATES", "SPARKS", 
        "INCITES", "ENGENDERS", "PROVOKES", "INITIATES", "STIMULATES", "KINDLES", "INSTIGATES", 
        "PROMPTS", "ELICITS", "EVOKES", "GIVES_RISE_TO", "STIRS_UP", "SETS_OFF", "SETS_IN_MOTION", 
        "KICKS_OFF", "ACTIVATES", "IGNITES", "INFLAMES", "FANS_THE_FLAMES_OF", "FUELS", "DRIVES", 
        "POWERS", "MOTIVATES", "INSPIRES", "IMPELS", "PROPELS", "COMPELS", "FORCES", "NECESSITATES", 
        "OCCASIONS", "BEGETS", "SPAWNS", "BREEDS", "FOSTERS", "CULTIVATES", "NURTURES", 
        "PERPETUATES", "SUSTAINS"
    ],
    
    "PRODUCES": [
        "MANUFACTURES", "MAKES", "GENERATES", "FABRICATES", "ASSEMBLES", "CONSTRUCTS", "YIELDS", 
        "OUTPUTS", "TURNS_OUT", "CRAFTS", "BUILDS", "SYNTHESIZES", "FORGES", "MOLDS", "CASTS", 
        "FASHIONS", "CONTRIVES", "CONCOCTS", "PREPARES", "PROCESSES", "REFINES", "DISTILLS", 
        "EXTRACTS", "DERIVES", "SEPARATES", "ISOLATES", "PURIFIES", "GROWS", "PROPAGATES", "BEARS", 
        "RENDERS", "DELIVERS", "SUPPLIES", "FURNISHES", "PROVIDES", "ISSUES", "DISTRIBUTES", 
        "CHURNS_OUT", "PUMPS_OUT", "CRANKS_OUT", "GRINDS_OUT", "SPITS_OUT", "PUTS_OUT", "KICKS_OUT"
    ],
    
    "USES": [
        "UTILIZES", "EMPLOYS", "APPLIES", "LEVERAGES", "EXERCISES", "IMPLEMENTS", "DEPLOYS", 
        "HARNESSES", "EXPLOITS", "CONSUMES", "WIELDS", "ADOPTS", "PRACTICES", "ENACTS", "OPERATES", 
        "MANIPULATES", "HANDLES", "ORCHESTRATES", "PERFORMS", "EXECUTES", "CARRIES_OUT", 
        "BRINGS_TO_BEAR", "DRAWS_UPON", "LEANS_ON", "RELIES_ON", "DEPENDS_ON", "COUNTS_ON", 
        "TURNS_TO", "RESORTS_TO", "AVAILS_ONESELF_OF", "TAKES_ADVANTAGE_OF", "CAPITALIZES_ON", 
        "PROFITS_FROM", "BENEFITS_FROM", "GAINS_FROM", "MAKES_USE_OF", "PUTS_TO_USE", 
        "PRESSES_INTO_SERVICE", "ENLISTS", "RECRUITS", "CONSCRIPTS", "INTEGRATES", "ACCOMMODATES"
    ],
    
    "LISTED_ON": [
        "TRADED_ON", "QUOTED_ON", "REGISTERED_ON", "ENROLLED_ON", "PRESENT_ON", "RECORDED_ON", 
        "CATALOGED_ON", "PUBLISHED_ON", "INCLUDED_ON", "FEATURED_ON", "DISPLAYED_ON", "SHOWN_ON", 
        "EXHIBITED_ON", "PRESENTED_ON", "EXPOSED_ON", "ADVERTISED_ON", "PROMOTED_ON", "MARKETED_ON", 
        "MERCHANDISED_ON", "VENDED_ON", "RETAILED_ON", "OFFERED_ON", "AVAILABLE_ON", "ACCESSIBLE_ON", 
        "OBTAINABLE_ON", "PROCURABLE_ON", "ACQUIRABLE_ON", "PURCHASABLE_ON", "BUYABLE_ON", 
        "POSTED_ON", "PLACED_ON", "SITUATED_ON", "POSITIONED_ON", "LOCATED_ON", "FOUND_ON", 
        "DISCOVERED_ON", "DETECTED_ON", "SPOTTED_ON", "SIGHTED_ON", "GLIMPSED_ON", "OBSERVED_ON", 
        "WITNESSED_ON", "PERCEIVED_ON", "NOTICED_ON", "RECOGNIZED_ON", "IDENTIFIED_ON", 
        "PINPOINTED_ON", "TARGETED_ON", "ZEROED_IN_ON", "HOMED_IN_ON", "FOCUSED_ON"
    ],
    
    "HAS": [
        "CONTAINS", "INCLUDES", "INCORPORATES", "ENCOMPASSES", "COMPRISES", "MAINTAINS", "FEATURES", 
        "INVOLVES", "EMBODIES", "EMBRACES", "CARRIES", "SUPPORTS", "EXHIBITS", "DEMONSTRATES", 
        "DISPLAYS", "PRESENTS", "SHOWS", "REVEALS", "EXPOSES", "UNCOVERS", "LAYS_BARE", 
        "MAKES_EVIDENT", "MAKES_APPARENT", "MAKES_OBVIOUS", "MAKES_VISIBLE", "MAKES_MANIFEST", 
        "MAKES_PATENT", "MAKES_PALPABLE", "MAKES_TANGIBLE", "MAKES_CONCRETE", "MAKES_REAL", 
        "MAKES_ACTUAL", "MAKES_PHYSICAL", "MAKES_MATERIAL", "MAKES_SUBSTANTIAL", "CARRIES_WITH_IT", 
        "BRINGS_WITH_IT", "ENTAILS", "IMPLIES", "INDICATES", "SIGNIFIES", "DENOTES", "CONNOTES", 
        "SUGGESTS", "CONJURES", "INSINUATES"
    ],
    
    "ACQUIRED": [
        "BOUGHT", "PURCHASED", "OBTAINED", "PROCURED", "GAINED", "SECURED", "TOOK_OVER", "ANNEXED", 
        "ATTAINED", "APPROPRIATED", "ABSORBED", "ASSUMED_CONTROL_OF", "COLLECTED", "AMASSED", 
        "GATHERED", "ACCUMULATED", "HOARDED", "STOCKPILED", "PILED_UP", "HARVESTED", "REAPED", 
        "GLEANED", "PLUCKED", "PICKED", "SEIZED", "CAPTURED", "CONQUERED", "CLAIMED", 
        "TOOK_POSSESSION_OF", "TOOK_OWNERSHIP_OF", "LAID_HANDS_ON", "GOT_HOLD_OF", "LATCHED_ONTO", 
        "GRABBED", "SNATCHED", "NABBED", "CAUGHT", "TRAPPED", "ENSNARED", "NETTED", "LANDED", 
        "BAGGED", "SNAGGED", "HOOKED", "ROPED_IN", "CAME_BY", "CAME_INTO_POSSESSION_OF", 
        "CAME_INTO_OWNERSHIP_OF", "RECEIVED", "ACCEPTED", "TOOK_DELIVERY_OF", "TOOK_RECEIPT_OF"
    ],
    
    "COMPARED_TO": [
        "CONTRASTED_WITH", "MEASURED_AGAINST", "EVALUATED_AGAINST", "JUDGED_AGAINST", 
        "WEIGHED_AGAINST", "ASSESSED_RELATIVE_TO", "BENCHMARKED_AGAINST", "MATCHED_AGAINST", 
        "PARALLELED_WITH", "LIKENED_TO", "ANALOGIZED_TO", "SET_AGAINST", "PUT_AGAINST", 
        "PLACED_AGAINST", "POSED_AGAINST", "POSITIONED_AGAINST", "PITCHED_AGAINST", 
        "PITTED_AGAINST", "STACKED_UP_AGAINST", "STOOD_UP_AGAINST", "LINED_UP_AGAINST", 
        "SIZED_UP_AGAINST", "SQUARED_OFF_AGAINST", "JUXTAPOSED_WITH", 
        "CONSIDERED_IN_RELATION_TO", "EXAMINED_IN_LIGHT_OF", "ANALYZED_IN_TERMS_OF", 
        "VIEWED_IN_COMPARISON_WITH", "SEEN_IN_RELATION_TO", "REGARDED_IN_COMPARISON_WITH", 
        "THOUGHT_OF_IN_RELATION_TO", "LOOKED_AT_IN_COMPARISON_WITH", "OBSERVED_IN_RELATION_TO", 
        "PERCEIVED_IN_COMPARISON_WITH", "UNDERSTOOD_IN_RELATION_TO", 
        "COMPREHENDED_IN_COMPARISON_WITH", "GRASPED_IN_RELATION_TO"
    ],
    
    "INFLUENCED_BY": [
        "LED_BY", "PRODUCED_BY", "AFFECTED_BY", "IMPACTED_BY", "SHAPED_BY", "MOLDED_BY", 
        "SWAYED_BY", "DETERMINED_BY", "CONDITIONED_BY", "PERSUADED_BY", "INSPIRED_BY", 
        "GUIDED_BY", "DIRECTED_BY", "CHANGED_BY", "TRANSFORMED_BY", "MODIFIED_BY", 
        "ALTERED_BY", "ADJUSTED_BY", "ADAPTED_BY", "CONVERTED_BY", "REFORMED_BY", 
        "RESHAPED_BY", "REMODELED_BY", "RECAST_BY", "REMADE_BY", "REVISED_BY", "REWORKED_BY", 
        "REDONE_BY", "REORGANIZED_BY", "RECONFIGURED_BY", "REENGINEERED_BY", "REDESIGNED_BY", 
        "REDRAWN_BY", "RESTRUCTURED_BY", "REORDERED_BY", "REARRANGED_BY", "REPOSITIONED_BY", 
        "REORIENTED_BY", "REDIRECTED_BY", "REFOCUSED_BY", "REALIGNED_BY", "READJUSTED_BY", 
        "RECALIBRATED_BY", "RETUNED_BY", "REBALANCED_BY", "RECONDITIONED_BY", "RETOOLED_BY", 
        "REFRESHED_BY", "RENEWED_BY", "REGENERATED_BY", "REJUVENATED_BY", "REVITALIZED_BY", 
        "REINVIGORATED_BY", "REINVENTED_BY", "REVOLUTIONIZED_BY", "RADICALIZED_BY", "DEMOCRATIZED_BY"
    ]
}

node_type_context = {
    "Company": [
        "REPORTED", "LOCATED_IN", "PARTNERS_WITH", "COMPETES_WITH", "OPERATES_IN", 
        "PRODUCES", "ACQUIRES", "OWNS", "INVESTED_IN", "LAUNCHES", "DEVELOPS", 
        "LISTED_ON", "HAS", "TRADED_IN", "REQUIRES", "USES", "CREATED", "SPONSORS", 
        "FUNDS", "EMPLOYS", "DISRUPTS", "INNOVATES", "SELLS", "BUYS", "DISTRIBUTES", 
        "MANUFACTURES", "DESIGNS", "PATENTS", "LICENSES", "CERTIFIED_BY", "AUDITED_BY", 
        "AUTHORIZED_BY", "REGULATED_BY", "REPORTS_TO", "ANALYZED_BY", "RECOMMENDED_BY", 
        "INVESTS_IN", "FOUNDED_BY", "MANAGED_BY", "LED_BY", "DIRECTED_BY", 
        "CONTROLLED_BY", "OWNED_BY"
    ],
    
    "Organization": [
        "REPORTED", "LOCATED_IN", "PARTNERS_WITH", "OPERATES_IN", "REGULATES", "SUPPORTS", 
        "RECOMMENDS", "PARTICIPATES_IN", "WORKS_FOR", "FOUNDED", "OWNS", "PRODUCES", 
        "DEVELOPS", "FUNDS", "SPONSORS", "INVESTS_IN", "CERTIFIES", "ACCREDITS", 
        "AUTHORIZES", "APPROVES", "AUDITS", "INSPECTS", "INVESTIGATES", "MONITORS", 
        "EXAMINES", "EVALUATES", "ASSESSES", "ANALYZES", "STUDIES", "RESEARCHES", 
        "CONDUCTS", "PERFORMS", "EXECUTES", "IMPLEMENTS", "DEPLOYS", "DISTRIBUTES", 
        "ALLOCATES", "ASSIGNS", "MANAGES", "ADMINISTERS", "OVERSEES", "CONTROLS", 
        "COORDINATES", "ORGANIZES", "ARRANGES", "PLANS", "DESIGNS", "DEVELOPS", "CREATES", 
        "BUILDS", "CONSTRUCTS", "ESTABLISHES", "INSTITUTES", "FOUNDS", "SETS_UP", 
        "INITIATES", "STARTS", "LAUNCHES", "INTRODUCES", "PROMOTES", "ADVOCATES", 
        "CHAMPIONS", "DEFENDS", "PROTECTS", "SAFEGUARDS", "SECURES", "MAINTAINS", 
        "SUSTAINS", "SUPPORTS", "REINFORCES", "STRENGTHENS", "ENHANCES", "IMPROVES", 
        "UPGRADES", "UPDATES", "MODERNIZES", "REVOLUTIONIZES", "TRANSFORMS", "CHANGES", 
        "MODIFIES"
    ],
    
    "Person": [
        "LEADS", "WORKS_FOR", "FOUNDED", "CREATED", "DEVELOPED", "INVESTED_IN", "OWNS", 
        "PARTICIPATES_IN", "LOCATED_IN", "REPORTS", "RECOMMENDS", "INFLUENCES", "AFFECTS", 
        "MANAGES", "DIRECTS", "CONTROLS", "OVERSEES", "SUPERVISES", "COORDINATES", 
        "ADMINISTERS", "GOVERNS", "RULES", "COMMANDS", "PRESIDES", "CHAIRS", "HEADS", 
        "LEADS", "GUIDES", "STEERS", "NAVIGATES", "PILOTS", "DRIVES", "OPERATES", "RUNS", 
        "CONDUCTS", "EXECUTES", "PERFORMS", "ACCOMPLISHES", "ACHIEVES", "ATTAINS", 
        "REALIZES", "FULFILLS", "COMPLETES", "FINISHES", "CONCLUDES", "TERMINATES", "ENDS", 
        "CEASES", "HALTS", "STOPS", "PAUSES", "SUSPENDS", "INTERRUPTS", "DISRUPTS", 
        "OBSTRUCTS", "BLOCKS", "IMPEDES", "HINDERS", "HAMPERS", "RESTRICTS", "LIMITS", 
        "CONFINES", "CONSTRAINS", "CONSTRICTS", "NARROWS", "TIGHTENS", "SQUEEZES", 
        "COMPRESSES", "CONDENSES", "SHRINKS", "REDUCES", "DIMINISHES", "DECREASES", 
        "LOWERS", "MINIMIZES", "MITIGATES", "ALLEVIATES", "RELIEVES", "EASES", "SOFTENS", 
        "MOLLIFIES", "PLACATES", "PACIFIES", "CALMS", "SOOTHES", "REASSURES", "COMFORTS", 
        "CONSOLES", "SOLACES", "HEARTENS", "ENCOURAGES", "INSPIRES", "MOTIVATES", 
        "STIMULATES", "ACTIVATES", "TRIGGERS", "INITIATES", "STARTS", "BEGINS", "COMMENCES", 
        "LAUNCHES", "INTRODUCES", "ESTABLISHES", "INSTITUTES", "FOUNDS", "CREATES", 
        "GENERATES", "PRODUCES", "YIELDS", "BEARS", "GIVES_RISE_TO", "RESULTS_IN", 
        "LEADS_TO", "CAUSES", "EFFECTS", "INDUCES", "ELICITS", "EVOKES", "PROMPTS"
    ],
    
    "Event": [
        "AFFECTS", "CAUSES", "OCCURRED_IN", "PARTICIPATED_IN", "REPORTED", "INFLUENCED_BY", 
        "RELATED_TO", "LEADS_TO", "IMPACTS", "INVOLVES", "HAS", "ORGANIZED_BY", 
        "SPONSORED_BY", "HOSTED_BY", "FUNDED_BY", "SUPPORTED_BY", "ENDORSED_BY", 
        "APPROVED_BY", "AUTHORIZED_BY", "SANCTIONED_BY", "PERMITTED_BY", "ALLOWED_BY", 
        "ENABLED_BY", "FACILITATED_BY", "EXPEDITED_BY", "ACCELERATED_BY", "HASTENED_BY", 
        "QUICKENED_BY", "SPEEDED_BY", "HURRIED_BY", "RUSHED_BY", "PRECEDED_BY", 
        "FOLLOWED_BY", "SUCCEEDED_BY", "SUPERSEDED_BY", "REPLACED_BY", "DISPLACED_BY", 
        "SUPPLANTED_BY", "SUBSTITUTED_BY", "SWAPPED_BY", "EXCHANGED_BY", "TRADED_BY", 
        "BARTERED_BY", "DEALT_BY", "NEGOTIATED_BY", "MEDIATED_BY", "ARBITRATED_BY", 
        "ADJUDICATED_BY", "JUDGED_BY", "REFEREED_BY", "UMPIRED_BY", "OFFICIATED_BY", 
        "ADMINISTERED_BY", "MANAGED_BY", "CONTROLLED_BY", "DIRECTED_BY", "GUIDED_BY", 
        "STEERED_BY", "NAVIGATED_BY", "PILOTED_BY", "CAPTAINED_BY", "COMMANDED_BY", "LED_BY", 
        "HEADED_BY", "CHAIRED_BY", "PRESIDED_BY", "GOVERNED_BY", "RULED_BY", "REGULATED_BY", 
        "MONITORED_BY", "TRACKED_BY", "TRACED_BY", "FOLLOWED_BY", "PURSUED_BY", "CHASED_BY", 
        "HUNTED_BY", "SOUGHT_BY", "SEARCHED_BY", "LOOKED_FOR_BY", "INVESTIGATED_BY", 
        "EXAMINED_BY", "INSPECTED_BY", "SCRUTINIZED_BY", "STUDIED_BY", "ANALYZED_BY", 
        "ASSESSED_BY", "EVALUATED_BY", "APPRAISED_BY", "ESTIMATED_BY", "GAUGED_BY", 
        "MEASURED_BY", "QUANTIFIED_BY", "CALCULATED_BY", "COMPUTED_BY", "COUNTED_BY", 
        "ENUMERATED_BY", "TALLIED_BY", "TOTALED_BY", "SUMMED_BY", "ADDED_BY", "SUBTRACTED_BY", 
        "MULTIPLIED_BY", "DIVIDED_BY", "FACTORED_BY", "SQUARED_BY", "CUBED_BY", "ROOTED_BY", 
        "POWERED_BY", "EXPONENTIATED_BY", "LOGARITHMED_BY", "DIFFERENTIATED_BY", "INTEGRATED_BY", 
        "DERIVED_BY", "SOLVED_BY", "PROVEN_BY", "DEMONSTRATED_BY", "SHOWN_BY", "REVEALED_BY", 
        "DISCLOSED_BY", "DIVULGED_BY", "EXPOSED_BY", "UNCOVERED_BY", "DISCOVERED_BY", "FOUND_BY", 
        "LOCATED_BY", "SPOTTED_BY", "DETECTED_BY", "SENSED_BY", "PERCEIVED_BY", "NOTICED_BY", 
        "OBSERVED_BY", "WITNESSED_BY", "VIEWED_BY", "SEEN_BY", "WATCHED_BY", "MONITORED_BY", 
        "TRACKED_BY", "TRACED_BY", "FOLLOWED_BY", "PURSUED_BY", "CHASED_BY", "HUNTED_BY", 
        "SOUGHT_BY", "SEARCHED_BY", "LOOKED_FOR_BY"
    ],
    
    "Date": [
        "RELATES_TO", "MARKS", "INDICATES", "REFERENCES", "ASSOCIATED_WITH", "DENOTES", 
        "SIGNIFIES", "REPRESENTS", "SYMBOLIZES", "STANDS_FOR", "EMBODIES", "PERSONIFIES", 
        "TYPIFIES", "EXEMPLIFIES", "ILLUSTRATES", "DEMONSTRATES", "SHOWS", "REVEALS", 
        "DISCLOSES", "EXPOSES", "UNCOVERS", "DIVULGES", "BETRAYS", "GIVES_AWAY", "LEAKS", 
        "SPILLS", "LETS_OUT", "BRINGS_TO_LIGHT", "BRINGS_TO_THE_FORE", "BRINGS_TO_THE_SURFACE", 
        "BRINGS_TO_ATTENTION", "CALLS_ATTENTION_TO", "DRAWS_ATTENTION_TO", "HIGHLIGHTS", 
        "EMPHASIZES", "STRESSES", "ACCENTUATES", "UNDERSCORES", "UNDERLINES", "ITALICIZES", 
        "BOLDS", "CAPITALIZES", "PUNCTUATES", "EXCLAIMS", "QUESTIONS", "WONDERS", "PONDERS", 
        "CONTEMPLATES", "MEDITATES", "REFLECTS", "THINKS", "CONSIDERS", "EXAMINES", "STUDIES", 
        "ANALYZES", "INVESTIGATES", "EXPLORES", "PROBES", "DELVES", "DIGS", "MINES", "QUARRIES", 
        "EXCAVATES", "UNEARTHS", "DREDGES", "TRAWLS", "FISHES", "HOOKS", "CATCHES", "CAPTURES", 
        "SNARES", "TRAPS"
    ],
    
    "Year": [
        "CONTAINS", "ENCOMPASSES", "INCLUDES", "FEATURES", "MARKS", "WITNESSED", 
        "EXPERIENCED", "ENDURED", "SUFFERED", "UNDERWENT", "SUSTAINED", "WITHSTOOD", 
        "WEATHERED", "SURVIVED", "LIVED_THROUGH", "PERSISTED_THROUGH", "LASTED_THROUGH", 
        "CONTINUED_THROUGH", "REMAINED_THROUGH", "STAYED_THROUGH", "KEPT_THROUGH", 
        "HELD_THROUGH", "MAINTAINED_THROUGH", "PRESERVED_THROUGH", "RETAINED_THROUGH", 
        "CONSERVED_THROUGH", "SAVED_THROUGH", "PROTECTED_THROUGH", "GUARDED_THROUGH", 
        "SHIELDED_THROUGH", "DEFENDED_THROUGH", "SECURED_THROUGH", "FORTIFIED_THROUGH", 
        "STRENGTHENED_THROUGH", "REINFORCED_THROUGH", "BOLSTERED_THROUGH", 
        "SUPPORTED_THROUGH", "UPHELD_THROUGH", "SUSTAINED_THROUGH", "NOURISHED_THROUGH", 
        "FED_THROUGH", "WATERED_THROUGH", "IRRIGATED_THROUGH", "CULTIVATED_THROUGH", 
        "FARMED_THROUGH", "HARVESTED_THROUGH", "REAPED_THROUGH", "GATHERED_THROUGH", 
        "COLLECTED_THROUGH", "ASSEMBLED_THROUGH", "AMASSED_THROUGH", "ACCUMULATED_THROUGH", 
        "HOARDED_THROUGH", "STOCKPILED_THROUGH", "STORED_THROUGH", "DEPOSITED_THROUGH", 
        "BANKED_THROUGH", "INVESTED_THROUGH", "FUNDED_THROUGH", "FINANCED_THROUGH", 
        "SUBSIDIZED_THROUGH", "SUPPORTED_THROUGH", "SPONSORED_THROUGH", "ENDORSED_THROUGH", 
        "BACKED_THROUGH", "PROMOTED_THROUGH", "ADVOCATED_THROUGH", "CHAMPIONED_THROUGH", 
        "DEFENDED_THROUGH", "PROTECTED_THROUGH", "SAFEGUARDED_THROUGH", "SECURED_THROUGH", 
        "PRESERVED_THROUGH", "CONSERVED_THROUGH", "MAINTAINED_THROUGH", "KEPT_THROUGH", 
        "RETAINED_THROUGH", "SAVED_THROUGH", "RESCUED_THROUGH", "DELIVERED_THROUGH", 
        "LIBERATED_THROUGH", "FREED_THROUGH", "RELEASED_THROUGH", "EMANCIPATED_THROUGH", 
        "DISCHARGED_THROUGH", "DISMISSED_THROUGH", "EXPELLED_THROUGH", "EJECTED_THROUGH", 
        "EVICTED_THROUGH", "OUSTED_THROUGH", "REMOVED_THROUGH", "ELIMINATED_THROUGH", 
        "ERADICATED_THROUGH", "EXTERMINATED_THROUGH", "ANNIHILATED_THROUGH", 
        "DESTROYED_THROUGH", "DEMOLISHED_THROUGH", "RAZED_THROUGH", "LEVELED_THROUGH", 
        "FLATTENED_THROUGH", "CRUSHED_THROUGH", "SMASHED_THROUGH", "SHATTERED_THROUGH", 
        "BROKE_THROUGH", "FRACTURED_THROUGH", "CRACKED_THROUGH", "SPLIT_THROUGH", 
        "DIVIDED_THROUGH", "SEPARATED_THROUGH", "PARTED_THROUGH", "DISJOINED_THROUGH", 
        "DISCONNECTED_THROUGH", "DETACHED_THROUGH", "SEVERED_THROUGH"
    ],

    "Location": [
        "CONTAINS", "PART_OF", "LOCATED_IN", "LOCATED_NEAR", "BORDERS", 
        "CONNECTED_TO", "ACCESSED_BY", "HOSTS", "HOUSES", "SERVES", 
        "HEADQUARTERS_OF", "BASE_FOR", "HOME_TO", "ORIGIN_OF", "DESTINATION_FOR", 
        "VISITED_BY", "TRAVELED_TO", "GOVERNED_BY", "ADMINISTERED_BY", "MANAGED_BY", 
        "OWNED_BY", "DEVELOPED_BY", "BUILT_BY", "DESIGNED_BY", "PLANNED_BY", 
        "POPULATED_BY", "INHABITED_BY", "EVACUATED_BY", "ABANDONED_BY", "DISCOVERED_BY", 
        "EXPLORED_BY", "MAPPED_BY", "SURVEYED_BY", "MEASURED_BY", "ANALYZED_BY", 
        "STUDIED_BY", "RESEARCHED_BY", "DOCUMENTED_BY", "DESCRIBED_BY", "PHOTOGRAPHED_BY", 
        "FILMED_BY", "DEPICTED_BY", "REPRESENTED_BY", "AFFECTED_BY", "CHANGED_BY", 
        "MODIFIED_BY", "TRANSFORMED_BY", "DAMAGED_BY", "DESTROYED_BY", "REBUILT_BY", 
        "RESTORED_BY", "PRESERVED_BY", "PROTECTED_BY", "DESIGNATED_AS", "CLASSIFIED_AS", 
        "CATEGORIZED_AS", "KNOWN_FOR", "FAMOUS_FOR", "NOTABLE_FOR", "CHARACTERIZED_BY", 
        "DEFINED_BY", "IDENTIFIED_BY", "REFERENCED_BY", "MENTIONED_IN", "FEATURED_IN", 
        "APPEARS_IN", "SITUATED_IN", "POSITIONED_IN", "ESTABLISHED_IN", "FOUNDED_IN",
        "CLIMATE_OF", "WEATHER_IN", "TERRAIN_OF", "GEOGRAPHY_OF", "TOPOLOGY_OF",
        "COORDINATES_OF", "LATITUDE_OF", "LONGITUDE_OF", "ELEVATION_OF", "AREA_OF",
        "SIZE_OF", "DISTANCE_FROM", "ROUTE_TO", "ACCESSIBLE_FROM", "RESOURCES_IN",
        "PRODUCES", "EXPORTS", "IMPORTS", "TRADES_IN", "REGULATED_BY", "TAXED_BY",
        "INVESTED_IN", "DEVELOPED", "URBANIZED", "INDUSTRIALIZED", "AGRICULTURAL_IN",
        "MINED_IN", "EXTRACTED_FROM", "POLLUTED_BY", "CLEANED_BY", "CONSERVED_BY",
        "PROTECTED_AREA_IN", "RESTRICTED_ZONE_IN", "RISK_ZONE_FOR"
    ],

    "Technology": [
        "DEVELOPED_BY", "USED_BY", "SOLD_BY", "LICENSED_BY", "PATENTED_BY", "IMPLEMENTED_BY", 
        "INTEGRATED_BY", "INSTALLED_BY", "DEPLOYED_BY", "MAINTAINED_BY", "SERVICED_BY", 
        "SUPPORTED_BY", "UPGRADED_BY", "UPDATED_BY", "ENHANCED_BY", "IMPROVED_BY", 
        "OPTIMIZED_BY", "STREAMLINED_BY", "SIMPLIFIED_BY", "AUTOMATED_BY", "DIGITIZED_BY", 
        "COMPUTERIZED_BY", "MECHANIZED_BY", "ROBOTIZED_BY", "MINIATURIZED_BY", "MAXIMIZED_BY", 
        "EXPANDED_BY", "EXTENDED_BY", "PROLONGED_BY", "CONTINUED_BY", "PERPETUATED_BY", 
        "SUSTAINED_BY", "PRESERVED_BY", "CONSERVED_BY", "SAVED_BY"
    ],
    
    "Research": [
        "CONDUCTED_BY", "PERFORMED_BY", "EXECUTED_BY", "CARRIED_OUT_BY", "UNDERTAKEN_BY", 
        "PURSUED_BY", "DIRECTED_BY", "MANAGED_BY", "COORDINATED_BY", "ORGANIZED_BY", 
        "ADMINISTERED_BY", "OVERSEEN_BY", "SUPERVISED_BY", "GUIDED_BY", "STEERED_BY", 
        "LED_BY", "HEADED_BY", "CHAIRED_BY", "PRESIDED_BY", "GOVERNED_BY", "RULED_BY", 
        "CONTROLLED_BY", "COMMANDED_BY", "ORDERED_BY", "INSTRUCTED_BY", "DIRECTED_BY", 
        "GUIDED_BY", "ADVISED_BY", "COUNSELED_BY", "TAUGHT_BY", "EDUCATED_BY", 
        "INSTRUCTED_BY", "TRAINED_BY", "COACHED_BY", "MENTORED_BY", "TUTORED_BY"
    ],
    
    "Strategy": [
        "DEVELOPED_BY", "FORMULATED_BY", "DEVISED_BY", "CONCEIVED_BY", "CREATED_BY", 
        "GENERATED_BY", "PRODUCED_BY", "ESTABLISHED_BY", "INSTITUTED_BY", "FOUNDED_BY", 
        "ORIGINATED_BY", "INITIATED_BY", "STARTED_BY", "LAUNCHED_BY", "INTRODUCED_BY", 
        "IMPLEMENTED_BY", "EXECUTED_BY", "CARRIED_OUT_BY", "EFFECTUATED_BY", "REALIZED_BY", 
        "ACTUALIZED_BY", "MATERIALIZED_BY", "CONCRETIZED_BY", "SUBSTANTIATED_BY", 
        "VERIFIED_BY", "CONFIRMED_BY", "VALIDATED_BY", "AUTHENTICATED_BY", "CERTIFIED_BY", 
        "ACCREDITED_BY", "APPROVED_BY", "ENDORSED_BY", "SANCTIONED_BY", "AUTHORIZED_BY", 
        "PERMITTED_BY"
    ],
    
    "Patent": [
        "OWNED_BY", "HELD_BY", "POSSESSED_BY", "MAINTAINED_BY", "CONTROLLED_BY", "FILED_BY", 
        "APPLIED_FOR_BY", "SUBMITTED_BY", "PROSECUTED_BY", "LITIGATED_BY", "DEFENDED_BY", 
        "ENFORCED_BY", "INFRINGED_BY", "LICENSED_BY", "ASSIGNED_BY", "TRANSFERRED_BY", 
        "CONVEYED_BY", "GRANTED_TO", "ISSUED_TO", "AWARDED_TO", "EXTENDED_FOR", "RENEWED_BY", 
        "ABANDONED_BY", "EXPIRED_FOR", "REVOKED_FROM", "INVALIDATED_FOR", "CHALLENGED_BY", 
        "OPPOSED_BY", "DISPUTED_BY", "CONTESTED_BY", "QUESTIONED_BY", "DOUBTED_BY", 
        "SUSPECTED_BY", "MISTRUSTED_BY", "DISTRUSTED_BY"
    ],
    
    "Innovation": [
        "CREATED_BY", "INVENTED_BY", "DESIGNED_BY", "DEVELOPED_BY", "DISCOVERED_BY", 
        "PIONEERED_BY", "CONCEIVED_BY", "ESTABLISHED_BY", "INTRODUCED_BY", "LAUNCHED_BY", 
        "DEPLOYED_BY", "IMPLEMENTED_BY", "COMMERCIALIZED_BY", "MARKETED_BY", "SOLD_BY", 
        "LICENSED_BY", "DISTRIBUTED_BY", "MANUFACTURED_BY", "PRODUCED_BY", "CONSTRUCTED_BY", 
        "BUILT_BY", "ASSEMBLED_BY", "FABRICATED_BY", "ENGINEERED_BY", "ARCHITECTED_BY"
    ],

    "Metric": [
        "MEASURES", "INDICATES", "TRACKS", "EVALUATES", "QUANTIFIES", "ASSESSES", "COMPARES", 
        "RELATES_TO", "DERIVED_FROM", "AFFECTS", "INFLUENCED_BY", "REPORTED", "ANALYZED", 
        "USED_BY", "CALCULATED_BY", "DEFINED_BY", "PUBLISHED_BY", "MONITORED_BY", "FORECASTS", 
        "BENCHMARKS", "TARGETS", "REPRESENTS", "DESCRIBES", "REFLECTS", "SIGNALS", "PREDICTS", 
        "ESTIMATES", "APPROXIMATES", "COMPUTES", "TALLIES", "SUMS", "AVERAGES", "AGGREGATES", 
        "CONSOLIDATES", "CALCULATES", "COUNTS", "ENUMERATES", "GAUGES", "RATES"
    ],
    
    "FinancialInstrument": [
        "TRADED_IN", "LISTED_ON", "ISSUED_BY", "OWNED_BY", "CREATED_BY", "REGULATED_BY", 
        "RATED_BY", "VALUED_AT", "PRICED_AT", "DENOMINATED_IN", "SETTLED_IN", "PAYS", 
        "YIELDS", "MATURES_ON", "EXPIRES_ON", "CONVERTS_TO", "HEDGES_AGAINST", 
        "SPECULATES_ON", "INVESTS_IN", "FINANCES", "FUNDS", "SECURITIZES", "COLLATERALIZES", 
        "GUARANTEES", "INSURES_AGAINST", "BENCHMARKED_AGAINST", "COMPARED_TO", "AFFECTED_BY", 
        "INFLUENCES"
    ],
    
    "Product": [
        "PRODUCED_BY", "DEVELOPED_BY", "DESIGNED_BY", "MANUFACTURED_BY", 
        "DISTRIBUTED_BY", "SOLD_BY", "MARKETED_BY", "ADVERTISED_BY", "PURCHASED_BY", 
        "USED_BY", "CONSUMED_BY", "REVIEWED_BY", "RATED_BY", "COMPARED_TO", 
        "COMPETES_WITH", "COMPLEMENTS", "REPLACES", "UPGRADES", "ENHANCES", 
        "COMPATIBLE_WITH", "INTEGRATED_WITH", "BUNDLED_WITH", "PACKAGED_WITH", 
        "PRICED_AT", "DISCOUNTED_BY", "REGULATED_BY", "CERTIFIED_BY", "APPROVED_BY", 
        "LICENSED_BY", "PATENTED_BY", "TRADEMARKED_BY", "COPYRIGHTED_BY", "RECALLED_BY", 
        "DISCONTINUED_BY", "LAUNCHED_BY", "FEATURED_IN", "INCLUDED_IN"
    ],
    
    "Index": [
        "TRACKS", "MEASURES", "REPRESENTS", "INCLUDES", "EXCLUDES", "COMPOSED_OF", 
        "CALCULATED_BY", "PUBLISHED_BY", "MAINTAINED_BY", "REBALANCED_BY", 
        "WEIGHTED_BY", "BENCHMARKED_BY", "COMPARED_TO", "ANALYZED_BY", "USED_BY", 
        "REFERENCED_BY", "INDICATES", "SIGNALS", "FORECASTS", "PREDICTS", 
        "REFLECTS", "AFFECTED_BY", "INFLUENCES", "RISES", "FALLS", "FLUCTUATES", 
        "TRENDS", "CORRELATED_WITH", "INVERSELY_RELATED_TO", "OUTPERFORMS", 
        "UNDERPERFORMS", "LISTED_ON", "TRADED_ON"
    ],
    
    "Sector": [
        "CONTAINS", "INCLUDES", "COMPOSED_OF", "PART_OF", "RELATED_TO", 
        "CONNECTED_TO", "COMPARED_TO", "ANALYZED_BY", "STUDIED_BY", "RESEARCHED_BY", 
        "REGULATED_BY", "AFFECTED_BY", "INFLUENCES", "GROWS", "DECLINES", 
        "CONSOLIDATES", "FRAGMENTS", "EVOLVES", "TRANSFORMS", "DISRUPTED_BY", 
        "INNOVATES", "MATURES", "EMERGES", "SATURATES", "CYCLICAL", "DEFENSIVE", 
        "SENSITIVE_TO", "RESISTANT_TO", "REPRESENTED_BY", "MEASURED_BY", 
        "BENCHMARKED_BY", "FORECASTED_BY", "OUTPERFORMS", "UNDERPERFORMS"
    ],
    
    "Currency": [
        "ISSUED_BY", "REGULATED_BY", "USED_BY", "ACCEPTED_BY", "TRADED_AGAINST", 
        "EXCHANGED_FOR", "CONVERTED_TO", "DENOMINATED_IN", "PEGGED_TO", 
        "BACKED_BY", "AFFECTED_BY", "INFLUENCES", "APPRECIATES", "DEPRECIATES", 
        "INFLATES", "DEFLATES", "DEVALUED_BY", "REVALUED_BY", "STABILIZED_BY", 
        "MEASURED_AGAINST", "COMPARED_TO", "ANALYZED_BY", "FORECASTED_BY", 
        "RESEARCHED_BY", "REPLACED_BY", "SUPERSEDES", "CIRCULATES_IN", 
        "LEGAL_TENDER_IN", "MINTED_BY", "PRINTED_BY", "DISTRIBUTED_BY", 
        "COUNTERFEITED", "STORED_IN", "TRANSFERRED_THROUGH"
    ],
    
    "Regulation": [
        "ISSUED_BY", "ENFORCED_BY", "CREATED_BY", "AMENDED_BY", "REPEALED_BY", 
        "SUPERSEDED_BY", "INTERPRETED_BY", "APPLIED_TO", "AFFECTS", "RESTRICTS", 
        "PROHIBITS", "PERMITS", "REQUIRES", "MANDATES", "INCENTIVIZES", 
        "PENALIZES", "REGULATES", "GOVERNS", "CONTROLS", "OVERSEES", "MONITORS", 
        "AUDITS", "INSPECTS", "CERTIFIES", "LICENSES", "AUTHORIZES", "APPROVES", 
        "EXEMPTS", "WAIVES", "CHALLENGED_BY", "CONTESTED_BY", "COMPLIED_WITH_BY", 
        "VIOLATED_BY", "IMPLEMENTED_BY", "FOLLOWED_BY", "REFERENCED_BY", 
        "ANALYZED_BY", "INTERPRETED_BY", "CLARIFIED_BY"
    ],
    
    "Country": [
        "LOCATED_IN", "BORDERS", "CONTAINS", "GOVERNED_BY", "EXPORTS_TO", 
        "IMPORTS_FROM", "TRADES_WITH", "ALLIES_WITH", "COMPETES_WITH", "CONFLICTS_WITH", 
        "SANCTIONS", "REGULATES", "TAXES", "SUBSIDIZES", "INVESTS_IN", "DIVESTS_FROM", 
        "DEVELOPS", "PRODUCES", "CONSUMES", "RESEARCHES", "INNOVATES", "POLLUTES", 
        "PROTECTS", "CONSERVES", "REPRESENTED_BY", "MEASURED_BY", "RATED_BY", 
        "ANALYZED_BY", "COMPARED_TO", "AFFECTED_BY", "INFLUENCES", "JOINS", 
        "WITHDRAWS_FROM", "HOSTS", "PARTICIPATES_IN", "SIGNS", "RATIFIES", 
        "IMPLEMENTS", "ENFORCES", "COMPLIES_WITH"
    ],
    
    "Revenue": [
        "GENERATED_BY", "EARNED_BY", "RECEIVED_BY", "REPORTED_BY", "ANALYZED_BY", 
        "COMPARED_TO", "GROWS", "DECLINES", "FLUCTUATES", "FORECASTED_BY", 
        "AFFECTED_BY", "INFLUENCES", "DERIVED_FROM", "ATTRIBUTED_TO", "ALLOCATED_TO", 
        "DISTRIBUTED_TO", "TAXED_BY", "MEASURED_IN", "CALCULATED_AS", "RECOGNIZED_BY", 
        "RECORDED_BY", "AUDITED_BY", "ADJUSTED_BY", "NORMALIZED_BY", "CONSOLIDATED_BY", 
        "SEGMENTED_BY", "BROKEN_DOWN_BY", "PROJECTED_BY", "ESTIMATED_BY", 
        "TARGETED_BY", "BENCHMARKED_AGAINST", "CONTRIBUTES_TO", "ACCOUNTS_FOR", 
        "REPRESENTS", "INDICATES"
    ],
    
    "Price": [
        "SET_BY", "DETERMINED_BY", "CALCULATED_BY", "QUOTED_BY", "OFFERED_BY", 
        "ACCEPTED_BY", "PAID_BY", "NEGOTIATED_BY", "DISCOUNTED_BY", "MARKED_UP_BY", 
        "ADVERTISED_BY", "LISTED_BY", "REGULATED_BY", "CONTROLLED_BY", "AFFECTED_BY", 
        "INFLUENCES", "INCREASES", "DECREASES", "FLUCTUATES", "STABILIZES", 
        "COMPARED_TO", "MEASURED_IN", "DENOMINATED_IN", "ANALYZED_BY", "FORECASTED_BY", 
        "TRACKED_BY", "MONITORED_BY", "RELATES_TO", "REFLECTS", "SIGNALS", 
        "INDICATES", "REPRESENTS", "APPROXIMATES", "TARGETS", "ANCHORS", 
        "BENCHMARKED_AGAINST", "ELASTICITY", "SENSITIVITY"
    ],
    
    "Signal": [
        "GENERATED_BY", "DETECTED_BY", "INTERPRETED_BY", "ANALYZED_BY", "USED_BY", 
        "INDICATES", "PREDICTS", "FORECASTS", "SUGGESTS", "WARNS", "CONFIRMS", 
        "CONTRADICTS", "REINFORCES", "WEAKENS", "PRECEDES", "FOLLOWS", "CORRELATES_WITH", 
        "CAUSED_BY", "RESULTS_IN", "AFFECTS", "INFLUENCES", "FILTERED_BY", "AMPLIFIED_BY", 
        "ATTENUATED_BY", "DISTORTED_BY", "CLARIFIED_BY", "TRANSMITTED_BY", "RECEIVED_BY", 
        "PROCESSED_BY", "MODULATED_BY", "ENCODED_BY", "DECODED_BY", "QUANTIFIED_BY", 
        "QUALIFIED_BY", "CLASSIFIED_BY", "CATEGORIZED_BY", "RANKED_BY", "RATED_BY"
    ],
    
    "Industry": [
        "CONTAINS", "PART_OF", "RELATED_TO", "CONNECTED_TO", "COMPARED_TO", 
        "COMPETES_WITH", "COLLABORATES_WITH", "SUPPLIES", "SERVED_BY", "REGULATED_BY", 
        "AFFECTED_BY", "INFLUENCES", "DISRUPTED_BY", "TRANSFORMED_BY", "INNOVATES", 
        "CONSOLIDATES", "FRAGMENTS", "GROWS", "DECLINES", "MATURES", "EMERGES", 
        "EVOLVES", "EMPLOYS", "PRODUCES", "CONSUMES", "INVESTS", "RESEARCHES", 
        "DEVELOPS", "ANALYZED_BY", "STUDIED_BY", "MEASURED_BY", "BENCHMARKED_BY", 
        "REPRESENTED_BY", "FORECASTED_BY", "CYCLICAL", "SEASONAL", "SENSITIVE_TO"
    ],
    
    "Concept": [
        "DEVELOPED_BY", "CREATED_BY", "DEFINED_BY", "DESCRIBED_BY", "EXPLAINED_BY", 
        "ANALYZED_BY", "RESEARCHED_BY", "STUDIED_BY", "APPLIED_BY", "USED_BY", 
        "IMPLEMENTED_BY", "RELATES_TO", "CONNECTED_TO", "PART_OF", "CONTAINS", 
        "DERIVES_FROM", "LEADS_TO", "EVOLVES_INTO", "AFFECTS", "INFLUENCES", 
        "CONTRADICTS", "SUPPORTS", "REFUTES", "CHALLENGES", "EXTENDS", "LIMITS", 
        "ABSTRACTS", "CONCRETIZES", "GENERALIZES", "SPECIALIZES", "CATEGORIZES", 
        "CLASSIFIES", "ORGANIZES", "STRUCTURES", "REPRESENTS", "SYMBOLIZES", 
        "MODELS", "SIMULATES", "PREDICTS", "EXPLAINS"
    ],
    
    "Service": [
        "PROVIDED_BY", "DELIVERED_BY", "PERFORMED_BY", "OFFERED_BY", "SOLD_BY", 
        "MARKETED_BY", "ADVERTISED_BY", "PURCHASED_BY", "CONSUMED_BY", "USED_BY", 
        "DEVELOPED_BY", "DESIGNED_BY", "IMPROVED_BY", "CUSTOMIZED_BY", "PERSONALIZED_BY", 
        "STANDARDIZED_BY", "AUTOMATED_BY", "DIGITIZED_BY", "REGULATED_BY", "LICENSED_BY", 
        "CERTIFIED_BY", "ACCREDITED_BY", "RATED_BY", "REVIEWED_BY", "COMPARED_TO", 
        "COMPETES_WITH", "COMPLEMENTS", "BUNDLED_WITH", "PRICED_BY", "DISCOUNTED_BY", 
        "CONTRACTED_BY", "OUTSOURCED_TO", "INTEGRATED_WITH", "MEASURED_BY", 
        "MONITORED_BY", "ASSESSED_BY", "EVALUATED_BY", "ANALYZED_BY"
    ],
    
    "Disease": [
        "AFFECTS", "INFECTS", "TRANSMITTED_BY", "CAUSED_BY", "DIAGNOSED_BY", 
        "TREATED_BY", "PREVENTED_BY", "RESEARCHED_BY", "STUDIED_BY", "ANALYZED_BY", 
        "MONITORED_BY", "TRACKED_BY", "REPORTED_BY", "DESCRIBED_BY", "CLASSIFIED_BY", 
        "CATEGORIZED_BY", "RELATED_TO", "LEADS_TO", "RESULTS_FROM", "COMPLICATED_BY", 
        "EXACERBATED_BY", "MITIGATED_BY", "OCCURS_IN", "PREVALENT_IN", "ENDEMIC_TO", 
        "EPIDEMIC_IN", "PANDEMIC_ACROSS", "MANIFESTS_AS", "PRESENTS_WITH", "DETECTED_BY", 
        "SCREENED_FOR", "TESTED_FOR", "MEASURED_BY", "QUANTIFIED_BY", "MODELED_BY", 
        "SIMULATED_BY", "FORECASTED_BY", "PREDICTS", "INDICATES"
    ],
    
    "Commodity": [
        "PRODUCED_BY", "EXTRACTED_BY", "MINED_BY", "HARVESTED_BY", "REFINED_BY", 
        "PROCESSED_BY", "TRADED_BY", "SHIPPED_BY", "STORED_BY", "CONSUMED_BY", 
        "USED_BY", "PURCHASED_BY", "SOLD_BY", "PRICED_IN", "DENOMINATED_IN", 
        "MEASURED_IN", "REGULATED_BY", "TAXED_BY", "SUBSIDIZED_BY", "AFFECTED_BY", 
        "INFLUENCES", "SUBSTITUTED_BY", "COMPLEMENTS", "DERIVED_FROM", "CONVERTED_TO", 
        "FUTURES_TRADED_ON", "OPTIONS_TRADED_ON", "ANALYZED_BY", "FORECASTED_BY", 
        "RESEARCHED_BY", "COMPARED_TO", "BENCHMARKED_AGAINST", "RISES", "FALLS", 
        "FLUCTUATES", "CORRELATES_WITH", "INVERSELY_RELATED_TO", "CYCLICAL", "SEASONAL"
    ],
    
    "Conference": [
        "ORGANIZED_BY", "HOSTED_BY", "SPONSORED_BY", "FUNDED_BY", "ATTENDED_BY", 
        "PRESENTED_AT", "KEYNOTED_BY", "MODERATED_BY", "FOCUSED_ON", "COVERS", 
        "DISCUSSES", "ADDRESSES", "EXPLORES", "EXAMINES", "ANALYZES", "HIGHLIGHTS", 
        "FEATURES", "SHOWCASES", "LOCATED_AT", "HELD_IN", "SCHEDULED_FOR", 
        "OCCURS_ON", "LASTS_FOR", "PRECEDES", "FOLLOWS", "ANNUAL", "BIENNIAL", 
        "RECURRING", "ONE-TIME", "VIRTUAL", "IN-PERSON", "HYBRID", "RECORDED_BY", 
        "PUBLISHED_BY", "REPORTED_ON", "REVIEWED_BY", "EVALUATED_BY", "CERTIFIED_BY", 
        "ACCREDITED_BY"
    ],
    
    "Platform": [
        "DEVELOPED_BY", "OWNED_BY", "OPERATED_BY", "MAINTAINED_BY", "HOSTED_BY", 
        "USED_BY", "ACCESSED_BY", "INTEGRATED_WITH", "CONNECTS", "ENABLES", 
        "FACILITATES", "SUPPORTS", "PROVIDES", "OFFERS", "FEATURES", "INCLUDES", 
        "BUILT_ON", "RUNS_ON", "COMPATIBLE_WITH", "REQUIRES", "DEPENDS_ON", 
        "EXTENDS", "ENHANCES", "IMPROVES", "SIMPLIFIES", "AUTOMATES", "DIGITIZES", 
        "MONETIZES", "ANALYZES", "PROCESSES", "MANAGES", "ORGANIZES", "DISTRIBUTES", 
        "DELIVERS", "COMPETES_WITH", "DISRUPTS", "REGULATED_BY", "CERTIFIED_BY", 
        "SECURED_BY"
    ],
    
    "Software": [
        "DEVELOPED_BY", "DESIGNED_BY", "CREATED_BY", "PROGRAMMED_BY", "CODED_BY", 
        "ENGINEERED_BY", "ARCHITECTED_BY", "MAINTAINED_BY", "UPDATED_BY", "PATCHED_BY", 
        "OWNED_BY", "LICENSED_BY", "DISTRIBUTED_BY", "SOLD_BY", "PURCHASED_BY", 
        "INSTALLED_BY", "USED_BY", "RUNS_ON", "COMPATIBLE_WITH", "INTEGRATES_WITH", 
        "CONNECTS_TO", "COMMUNICATES_WITH", "ANALYZES", "PROCESSES", "TRANSFORMS", 
        "STORES", "RETRIEVES", "DISPLAYS", "GENERATES", "CALCULATES", "SIMULATES", 
        "MODELS", "AUTOMATES", "OPTIMIZES", "SECURES", "ENCRYPTED_BY", "PROTECTED_BY", 
        "AUTHENTICATED_BY", "AUDITED_BY"
    ],
    
    "Project": [
        "MANAGED_BY", "LED_BY", "EXECUTED_BY", "IMPLEMENTED_BY", "DEVELOPED_BY", 
        "DESIGNED_BY", "PLANNED_BY", "INITIATED_BY", "FUNDED_BY", "SPONSORED_BY", 
        "OWNED_BY", "CONTRIBUTES_TO", "PART_OF", "RELATED_TO", "DEPENDS_ON", 
        "ENABLES", "ACHIEVES", "DELIVERS", "PRODUCES", "CREATES", "RESULTS_IN", 
        "SCHEDULED_FOR", "STARTS_ON", "ENDS_ON", "DELAYED_BY", "ACCELERATED_BY", 
        "APPROVED_BY", "REVIEWED_BY", "AUDITED_BY", "EVALUATED_BY", "MEASURED_BY", 
        "TRACKED_BY", "REPORTED_ON", "DOCUMENTED_BY", "COMMUNICATED_BY", "LOCATED_AT", 
        "INVOLVES", "REQUIRES", "ALLOCATED", "UTILIZES"
    ],
    
    "Document": [
        "CREATED_BY", "WRITTEN_BY", "AUTHORED_BY", "COMPILED_BY", "EDITED_BY", 
        "REVIEWED_BY", "APPROVED_BY", "SIGNED_BY", "PUBLISHED_BY", "DISTRIBUTED_BY", 
        "STORED_BY", "OWNED_BY", "ACCESSED_BY", "READ_BY", "CITED_BY", "REFERENCED_BY", 
        "CONTAINS", "DESCRIBES", "EXPLAINS", "ANALYZES", "EVALUATES", "RECOMMENDS", 
        "CONFIRMS", "VERIFIES", "CERTIFIES", "AUTHENTICATES", "SUBMITTED_TO", 
        "FILED_WITH", "REGISTERED_WITH", "CLASSIFIED_AS", "CATEGORIZED_AS", 
        "RELATED_TO", "PART_OF", "SUPPLEMENTS", "SUPERSEDES", "AMENDS", "EXTENDS", 
        "UPDATES", "REPLACES", "VERSION_OF"
    ],
    
    "Report": [
        "WRITTEN_BY", "AUTHORED_BY", "PREPARED_BY", "COMPILED_BY", "RESEARCHED_BY", 
        "PUBLISHED_BY", "DISTRIBUTED_BY", "COMMISSIONED_BY", "REQUESTED_BY", 
        "SUBMITTED_TO", "PRESENTED_TO", "CONTAINS", "ANALYZES", "EVALUATES", 
        "EXAMINES", "INVESTIGATES", "REVIEWS", "SUMMARIZES", "DETAILS", "DESCRIBES", 
        "EXPLAINS", "INTERPRETS", "FORECASTS", "PREDICTS", "RECOMMENDS", "SUGGESTS", 
        "PROPOSES", "CONCLUDES", "BASED_ON", "USES_DATA_FROM", "REFERENCES", 
        "CITES", "COVERS", "ADDRESSES", "FOCUSES_ON", "DATED", "PERIODIC", "ANNUAL", 
        "QUARTERLY", "MONTHLY", "AUDITED", "CERTIFIED"
    ],

    "Market": [
        "CONTAINS", "INCLUDES", "COMPOSED_OF", "PART_OF", "RELATED_TO", 
        "CONNECTED_TO", "SERVES", "REGULATED_BY", "GOVERNED_BY", "MONITORED_BY", 
        "OPERATED_BY", "MANAGED_BY", "ACCESSED_BY", "PARTICIPATED_IN", "TRADED_IN", 
        "INVESTED_IN", "ANALYZED_BY", "STUDIED_BY", "RESEARCHED_BY", "REPORTED_ON", 
        "FORECASTED_BY", "AFFECTED_BY", "INFLUENCES", "RESPONDS_TO", "REACTS_TO", 
        "GROWS", "DECLINES", "EXPANDS", "CONTRACTS", "MATURES", "EMERGES", 
        "DEVELOPS", "EVOLVES", "TRANSFORMS", "DISRUPTED_BY", "INNOVATED_BY", 
        "CONSOLIDATED_BY", "FRAGMENTED_BY", "SATURATED", "EFFICIENT", "INEFFICIENT", 
        "COMPETITIVE", "MONOPOLISTIC", "OLIGOPOLISTIC", "SEGMENTED", "NICHE", 
        "MASS", "GLOBAL", "REGIONAL", "LOCAL", "DOMESTIC", "FOREIGN", "EXPORT", 
        "IMPORT", "SUPPLY", "DEMAND", "EQUILIBRIUM", "BUBBLE", "CRASH", "CORRECTION", 
        "RECESSION", "RECOVERY", "BULL", "BEAR", "VOLATILE", "STABLE", "LIQUID", 
        "ILLIQUID", "PRIMARY", "SECONDARY", "SPOT", "FUTURES", "OPTIONS", "DERIVATIVES",
        "OVER_THE_COUNTER", "EXCHANGE_TRADED", "WHOLESALE", "RETAIL", "CONSUMER", 
        "INDUSTRIAL", "COMMODITY", "FINANCIAL", "CAPITAL", "MONEY", "CREDIT", "DEBT", 
        "EQUITY", "SECURITIES", "FOREX", "REAL_ESTATE", "CRYPTOCURRENCY"
    ],
    
    "Framework": [
        "CREATED_BY", "DEVELOPED_BY", "DESIGNED_BY", "ESTABLISHED_BY", "PUBLISHED_BY", 
        "MAINTAINED_BY", "UPDATED_BY", "REVISED_BY", "IMPLEMENTED_BY", "ADOPTED_BY", 
        "USED_BY", "APPLIED_BY", "REFERENCED_BY", "CITED_BY", "BUILT_ON", 
        "BASED_ON", "DERIVED_FROM", "EXTENDS", "ENHANCES", "IMPROVES", "REPLACES", 
        "SUPERSEDES", "COMPLEMENTS", "INTEGRATES_WITH", "COMPATIBLE_WITH", 
        "STRUCTURED_AS", "ORGANIZED_AS", "DEFINED_AS", "CONSISTS_OF", "INCLUDES", 
        "CONTAINS", "COMPRISES", "PROVIDES", "ENABLES", "FACILITATES", "SUPPORTS", 
        "GUIDES", "DIRECTS", "GOVERNS", "REGULATES", "STANDARDIZES", "FORMALIZES", 
        "CODIFIES", "SYSTEMATIZES", "MEASURES", "EVALUATES", "ASSESSES", "ANALYZES", 
        "COMPARES", "BENCHMARKS", "CATEGORIZES", "CLASSIFIES", "TAXONOMIZES", 
        "CONTEXTUALIZES", "CONCEPTUALIZES", "THEORIZES", "MODELS", "SIMULATES", 
        "REPRESENTS", "ABSTRACTS", "SIMPLIFIES", "GENERALIZES", "SPECIALIZES"
    ],
    
    "Campaign": [
        "CREATED_BY", "DESIGNED_BY", "PLANNED_BY", "EXECUTED_BY", "IMPLEMENTED_BY", 
        "MANAGED_BY", "COORDINATED_BY", "DIRECTED_BY", "LED_BY", "SPONSORED_BY", 
        "FUNDED_BY", "SUPPORTED_BY", "ENDORSED_BY", "PARTICIPATED_IN", "CONTRIBUTES_TO", 
        "TARGETS", "FOCUSES_ON", "ADDRESSES", "PROMOTES", "MARKETS", "ADVERTISES", 
        "PUBLICIZES", "COMMUNICATES", "REACHES", "ENGAGES", "INFLUENCES", "PERSUADES", 
        "CONVERTS", "ACTIVATES", "MOBILIZES", "LAUNCHES", "RUNS", "OPERATES", 
        "CONDUCTS", "PERFORMS", "EXECUTES", "ACHIEVES", "SUCCEEDS", "FAILS", 
        "MEASURED_BY", "EVALUATED_BY", "ANALYZED_BY", "REPORTED_ON", "DOCUMENTED_BY", 
        "STARTED_ON", "ENDED_ON", "SCHEDULED_FOR", "BUDGETED_AT", "COSTS", 
        "GENERATES", "PRODUCES", "RESULTS_IN", "LEADS_TO", "CAUSES", "AFFECTS", 
        "CHANGES", "TRANSFORMS", "DIGITAL", "TRADITIONAL", "INTEGRATED", "MULTI-CHANNEL", 
        "CROSS-PLATFORM", "SOCIAL_MEDIA", "EMAIL", "DIRECT_MAIL", "TELEVISION", 
        "RADIO", "PRINT", "OUTDOOR", "EVENT", "EXPERIENTIAL", "GUERRILLA", "VIRAL", 
        "WORD_OF_MOUTH", "REFERRAL", "AFFILIATE", "INFLUENCER", "CONTENT", "BRAND", 
        "PRODUCT", "SERVICE", "POLITICAL", "ADVOCACY", "FUNDRAISING", "AWARENESS", 
        "EDUCATIONAL"
    ],
    
    "Initiative": [
        "LAUNCHED_BY", "STARTED_BY", "CREATED_BY", "DEVELOPED_BY", "DESIGNED_BY", 
        "LED_BY", "MANAGED_BY", "COORDINATED_BY", "IMPLEMENTED_BY", "EXECUTED_BY", 
        "SPONSORED_BY", "FUNDED_BY", "SUPPORTED_BY", "ENDORSED_BY", "APPROVED_BY", 
        "AUTHORIZED_BY", "OVERSEEN_BY", "PARTICIPATED_IN", "CONTRIBUTED_TO", 
        "JOINED_BY", "ALIGNED_WITH", "ADDRESSES", "FOCUSES_ON", "TARGETS", 
        "AIMS_AT", "RESPONDS_TO", "SOLVES", "IMPROVES", "ENHANCES", "TRANSFORMS", 
        "CHANGES", "INNOVATES", "DISRUPTS", "REVOLUTIONIZES", "PLANNED_FOR", 
        "SCHEDULED_FOR", "LAUNCHED_ON", "COMPLETED_BY", "PART_OF", "RELATES_TO", 
        "CONTRIBUTES_TO", "COMPLEMENTS", "BUILDS_UPON", "EXTENDS", "EXPANDS", 
        "SCALES", "REPLICATES", "MEASURED_BY", "EVALUATED_BY", "ASSESSED_BY", 
        "ANALYZED_BY", "REPORTED_ON", "SUCCEEDS", "FAILS", "ACHIEVES", "IMPACTS", 
        "AFFECTS", "BENEFITS", "STRATEGIC", "TACTICAL", "OPERATIONAL", "CORPORATE", 
        "GOVERNMENTAL", "NON-PROFIT", "COMMUNITY", "GLOBAL", "REGIONAL", "LOCAL", 
        "DIGITAL", "SUSTAINABILITY", "DIVERSITY", "INNOVATION", "COST-SAVING", 
        "EFFICIENCY", "QUALITY", "SAFETY", "COMPLIANCE", "RISK_MANAGEMENT", 
        "CHANGE_MANAGEMENT", "ORGANIZATIONAL_DEVELOPMENT", "CONTINUOUS_IMPROVEMENT"
    ],
    
    "Policy": [
        "CREATED_BY", "DEVELOPED_BY", "FORMULATED_BY", "DESIGNED_BY", "WRITTEN_BY", 
        "ENACTED_BY", "IMPLEMENTED_BY", "ENFORCED_BY", "ADMINISTERED_BY", "INTERPRETED_BY", 
        "AMENDED_BY", "REVISED_BY", "UPDATED_BY", "REPLACED_BY", "REPEALED_BY", 
        "OVERTURNED_BY", "UPHELD_BY", "CHALLENGED_BY", "CONTESTED_BY", "COMPLIED_WITH_BY", 
        "VIOLATED_BY", "EXEMPTED_FROM_BY", "WAIVED_FOR", "APPLIED_TO", "COVERS", 
        "AFFECTS", "REGULATES", "RESTRICTS", "PROHIBITS", "PERMITS", "ALLOWS", 
        "AUTHORIZES", "MANDATES", "REQUIRES", "INCENTIVIZES", "PENALIZES", "TAXES", 
        "SUBSIDIZES", "PROTECTS", "PROMOTES", "DISCOURAGES", "ADDRESSES", "RESPONDS_TO", 
        "SOLVES", "MITIGATES", "PREVENTS", "REDUCES", "INCREASES", "IMPROVES", 
        "WORSENS", "BASED_ON", "DERIVED_FROM", "ALIGNED_WITH", "CONTRADICTS", 
        "SUPERSEDES", "SUPPLEMENTS", "COMPLEMENTS", "EFFECTIVE_FROM", "EXPIRES_ON", 
        "REVIEWED_ON", "EVALUATED_BY", "ANALYZED_BY", "RESEARCHED_BY", "CRITICIZED_BY", 
        "SUPPORTED_BY", "ENDORSED_BY", "REJECTED_BY", "MONETARY", "FISCAL", "ECONOMIC", 
        "SOCIAL", "ENVIRONMENTAL", "FOREIGN", "DOMESTIC", "TRADE", "IMMIGRATION", 
        "HEALTHCARE", "EDUCATION", "DEFENSE", "SECURITY", "ENERGY", "TRANSPORTATION", 
        "HOUSING", "AGRICULTURE", "LABOR", "CORPORATE", "REGULATORY", "COMPLIANCE", 
        "ETHICS", "PRIVACY", "DATA", "TECHNOLOGY"
    ],
    
    "Resource": [
        "EXTRACTED_BY", "MINED_BY", "HARVESTED_BY", "PRODUCED_BY", "CREATED_BY", 
        "DEVELOPED_BY", "MANUFACTURED_BY", "PROCESSED_BY", "REFINED_BY", "SYNTHESIZED_BY", 
        "OWNED_BY", "CONTROLLED_BY", "MANAGED_BY", "ALLOCATED_BY", "DISTRIBUTED_BY", 
        "SUPPLIED_BY", "PROVIDED_BY", "UTILIZED_BY", "CONSUMED_BY", "DEPLETED_BY", 
        "CONSERVED_BY", "PRESERVED_BY", "RECYCLED_BY", "REUSED_BY", "REPURPOSED_BY", 
        "SUBSTITUTED_BY", "REPLACED_BY", "LOCATED_IN", "FOUND_IN", "SOURCED_FROM", 
        "TRANSPORTED_BY", "STORED_BY", "STOCKPILED_BY", "INVENTORIED_BY", 
        "MEASURED_BY", "QUANTIFIED_BY", "VALUED_AT", "PRICED_AT", "TRADED_BY", 
        "EXPORTED_BY", "IMPORTED_BY", "TAXED_BY", "SUBSIDIZED_BY", "REGULATED_BY", 
        "PROTECTED_BY", "RESEARCHED_BY", "STUDIED_BY", "ANALYZED_BY", "EVALUATED_BY", 
        "RENEWABLE", "NONRENEWABLE", "SUSTAINABLE", "UNSUSTAINABLE", "ABUNDANT", 
        "SCARCE", "CRITICAL", "STRATEGIC", "ESSENTIAL", "LUXURY", "RAW", "PROCESSED", 
        "NATURAL", "ARTIFICIAL", "TANGIBLE", "INTANGIBLE", "HUMAN", "FINANCIAL", 
        "PHYSICAL", "INFORMATION", "KNOWLEDGE", "INTELLECTUAL", "TECHNOLOGICAL", 
        "AGRICULTURAL", "MINERAL", "ENERGY", "WATER", "LAND", "LABOR", "CAPITAL", 
        "TIME", "ATTENTION", "TALENT", "SKILLS", "EXPERTISE", "EXPERIENCE"
    ],
    
    "Material": [
        "PRODUCED_BY", "MANUFACTURED_BY", "CREATED_BY", "DEVELOPED_BY", "DESIGNED_BY", 
        "ENGINEERED_BY", "SOURCED_FROM", "EXTRACTED_FROM", "MINED_FROM", "HARVESTED_FROM", 
        "REFINED_BY", "PROCESSED_BY", "TREATED_BY", "MODIFIED_BY", "COMBINED_WITH", 
        "MIXED_WITH", "ALLOYED_WITH", "COMPOSED_OF", "DERIVED_FROM", "MADE_OF", 
        "USED_BY", "UTILIZED_BY", "APPLIED_BY", "CONSUMED_BY", "INCORPORATED_BY", 
        "INTEGRATED_BY", "INSTALLED_BY", "DEPLOYED_BY", "TESTED_BY", "ANALYZED_BY", 
        "RESEARCHED_BY", "STUDIED_BY", "CHARACTERIZED_BY", "SPECIFIED_BY", "STANDARDIZED_BY", 
        "CERTIFIED_BY", "REGULATED_BY", "RESTRICTED_BY", "TRANSPORTED_BY", "SHIPPED_BY", 
        "STORED_BY", "PACKAGED_BY", "DISTRIBUTED_BY", "SOLD_BY", "PURCHASED_BY", 
        "RECYCLED_BY", "REUSED_BY", "REPURPOSED_BY", "DISPOSED_BY", "DEGRADED_BY", 
        "DECOMPOSED_BY", "CORRODED_BY", "WEATHERED_BY", "RAW", "PROCESSED", "FINISHED", 
        "NATURAL", "SYNTHETIC", "COMPOSITE", "ORGANIC", "INORGANIC", "METALLIC", 
        "CERAMIC", "POLYMERIC", "SEMICONDUCTING", "INSULATING", "CONDUCTING", 
        "MAGNETIC", "OPTICAL", "STRUCTURAL", "FUNCTIONAL", "SMART", "ADVANCED", 
        "TRADITIONAL", "SUSTAINABLE", "BIODEGRADABLE", "RECYCLABLE", "RENEWABLE", 
        "NONRENEWABLE", "TOXIC", "HAZARDOUS", "BENIGN", "BIOCOMPATIBLE", "RARE", 
        "ABUNDANT", "EXPENSIVE", "ECONOMICAL", "DURABLE", "EPHEMERAL", "LIGHTWEIGHT", 
        "HEAVY", "STRONG", "WEAK", "FLEXIBLE", "RIGID", "REACTIVE", "INERT"
    ],
    
    "Fund": [
        "MANAGED_BY", "OPERATED_BY", "ADMINISTERED_BY", "OVERSEEN_BY", "CREATED_BY", 
        "ESTABLISHED_BY", "FOUNDED_BY", "LAUNCHED_BY", "FUNDED_BY", "SEEDED_BY", 
        "CAPITALIZED_BY", "INVESTED_BY", "OWNED_BY", "HELD_BY", "REGULATED_BY", 
        "SUPERVISED_BY", "AUDITED_BY", "CUSTODIED_BY", "ASSESSED_BY", "RATED_BY", 
        "INVESTED_IN", "ALLOCATES_TO", "FINANCES", "SUPPORTS", "BACKS", "UNDERWRITES", 
        "SPONSORS", "GRANTS_TO", "LENDS_TO", "LOANS_TO", "DONATES_TO", "CONTRIBUTES_TO", 
        "RETURNS", "YIELDS", "PERFORMS", "APPRECIATES", "DEPRECIATES", "GROWS", 
        "SHRINKS", "DISTRIBUTES", "PAYS_OUT", "REINVESTS", "COMPOUNDS", "CHARGES", 
        "FEES", "EXPENSES", "TRADED_ON", "LISTED_ON", "PRICED_AT", "VALUED_AT", 
        "BENCHMARKED_AGAINST", "COMPARED_TO", "CORRELATED_WITH", "DIVERSIFIED", 
        "CONCENTRATED", "LEVERAGED", "HEDGED", "MUTUAL", "EXCHANGE-TRADED", "INDEX", 
        "ACTIVE", "PASSIVE", "OPEN-END", "CLOSED-END", "HEDGE", "PRIVATE_EQUITY", 
        "VENTURE_CAPITAL", "PENSION", "ENDOWMENT", "SOVEREIGN_WEALTH", "MONEY_MARKET", 
        "BOND", "EQUITY", "BALANCED", "GROWTH", "VALUE", "INCOME", "TOTAL_RETURN", 
        "LONG-ONLY", "LONG-SHORT", "MARKET_NEUTRAL", "ARBITRAGE", "GLOBAL", "REGIONAL", 
        "EMERGING_MARKETS", "DEVELOPED_MARKETS", "SECTOR", "THEMATIC", "SUSTAINABLE", 
        "ESG", "IMPACT", "CHARITABLE", "DONOR-ADVISED", "RETAIL", "INSTITUTIONAL", 
        "TAXABLE", "TAX-EXEMPT", "LIQUID", "ILLIQUID", "ALTERNATIVE"
    ]

}

# Initialize the node hierarchy dictionary
node_hierarchy = {}

def build_hierarchy_tree():
    """Builds a tree of node hierarchies for multi-labeling"""
    # First, create the tree from mappings
    for parent, children in node_type_mappings.items():
        if parent not in node_hierarchy:
            node_hierarchy[parent] = set()
        for child in children:
            node_hierarchy[parent].add(child)
    
    # Add primary nodes with no explicit relationships as top-level
    for node in primary_nodes:
        if node not in node_hierarchy and node not in [child for children in node_hierarchy.values() for child in children]:
            node_hierarchy[node] = set()

# Create reverse lookup for child to parent
parent_lookup = {}

def build_parent_lookup():
    """Builds a lookup dictionary to find parents of a given node type"""
    for parent, children in node_hierarchy.items():
        for child in children:
            if child not in parent_lookup:
                parent_lookup[child] = set()
            parent_lookup[child].add(parent)
            
            # Also add parent's parents recursively
            parents_to_process = list(parent_lookup.get(parent, set()))
            while parents_to_process:
                grandparent = parents_to_process.pop()
                parent_lookup[child].add(grandparent)
                parents_to_process.extend(parent_lookup.get(grandparent, set()))

# Build reverse lookup for relationship mappings
relationship_mapping_reverse = {}


def build_relationship_mappings():
    """Builds a reverse lookup for relationship mappings"""
    # Clear existing mappings to avoid potential issues with residual data
    relationship_mapping_reverse.clear()
    
    # Build the reverse lookup
    for primary, variants in primary_relationship_types.items():
        for variant in variants:
            relationship_mapping_reverse[variant] = primary
    
    # Logging for debugging - can be removed in production
    if "DIRECTS" in relationship_mapping_reverse:
        print(f"Debug: DIRECTS maps to {relationship_mapping_reverse['DIRECTS']}")
    else:
        print("Debug: DIRECTS not found in mapping")
        
    # Verify a few key mappings to ensure the function is working correctly
    expected_mappings = {
        "IMPACTS": "AFFECTS",
        "DIRECTS": "LEADS",
        "MANUFACTURES": "PRODUCES"
    }
    
    for variant, expected_primary in expected_mappings.items():
        actual_primary = relationship_mapping_reverse.get(variant)
        if actual_primary != expected_primary:
            print(f"Warning: Expected {variant} to map to {expected_primary}, but got {actual_primary}")

build_hierarchy_tree()
build_parent_lookup()
build_relationship_mappings()

def map_relationship_type(variant):
    """Maps a relationship variant to its primary type using exact matching"""
    # First check if it's an exact match to a primary type
    if variant in primary_relationships:
        return variant
    
    # Check if it's in our mapping dictionary
    if variant in relationship_mapping_reverse:
        return relationship_mapping_reverse[variant]
    
    # If no match, return the original
    return variant

from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer, util
import numpy as np
model = SentenceTransformer('all-MiniLM-L12-v2')

# Encode primary nodes and relationships
node_embeddings = model.encode([x.replace('_',' ').lower() for x in primary_nodes], convert_to_tensor=True)
relationship_embeddings = model.encode([x.replace('_',' ').lower() for x in primary_relationships+list(relationship_mapping_reverse.keys())], convert_to_tensor=True)

# Save embeddings in arrays
node_embeddings_array = node_embeddings.cpu().numpy()
relationship_embeddings_array = relationship_embeddings.cpu().numpy()

#literal_primary_nodes = Literal[tuple(primary_nodes)]
#literal_primary_relationships = Literal[tuple(primary_relationships)]

def map_relationship_type_combined(variant, source_node_type=None, target_node_type=None, inner=False):
    global relationship_embeddings_array, primary_relationships#, literal_primary_relationships
    """Maps a relationship variant using exact matching, then falls back to semantic similarity"""
    # First try exact mapping
    exact_match = map_relationship_type(variant)
    if exact_match != variant:
        return exact_match
    
    # If no exact match, use embedding-based similarity
    variant_embedding = model.encode([variant.replace('_',' ').lower()])
       
    # Compute cosine similarity
    cosine_sim = cosine_similarity(variant_embedding, relationship_embeddings_array)
    
    # Find the closest match
    max_index = np.argmax(cosine_sim)
    if max_index < len(primary_relationships):
        cosine_match = primary_relationships[max_index]
    else:
        cosine_match = list(relationship_mapping_reverse.keys())[max_index-len(primary_relationships)]
    cosine_score = cosine_sim[0, max_index]

    best_primary_match = None
    if cosine_score < 0.5:
        print(f"Warning: No similar type found for {variant} best match is {cosine_match}, with score {cosine_score}")
    
    elif cosine_score > 0.95:
        best_primary_match = relationship_mapping_reverse.get(cosine_match, cosine_match)
 
    elif source_node_type and target_node_type:
        # Get relationships that are valid for the source node type
        source_context_matches = []
        if source_node_type in node_type_context:
            source_context_matches = [t for t in node_type_context[source_node_type] if t in primary_relationships]
        
        # Get relationships that are valid for the target node type
        target_context_matches = []
        if target_node_type in node_type_context:
            target_context_matches = [t for t in node_type_context[target_node_type] if t in primary_relationships]
        
        # Combine and look for overlaps first (relationships that work for both nodes)
        combined_matches = [t for t in source_context_matches if t in target_context_matches]
        
        # If we have overlapping matches and our cosine match is one of them, use it
        if combined_matches and cosine_match in combined_matches:
            best_primary_match = relationship_mapping_reverse.get(cosine_match, cosine_match)
        else:
            # If no overlap but we have context matches, prefer those over the cosine match
            context_matches = list(set(source_context_matches + target_context_matches))
            if context_matches:
                # Find the closest match among context matches
                best_context_score = -1
                
                for context_match in context_matches:
                    idx = primary_relationships.index(context_match)
                    score = cosine_sim[0, idx]
                    if score > best_context_score:
                        best_context_score = score
                        best_primary_match = relationship_mapping_reverse.get(cosine_match, context_match)
                        cosine_score = score
    
    # Return the cosine match if it's above a certain threshold
    if cosine_score > 0.5 if inner else 0.85:
        best_primary_match = relationship_mapping_reverse.get(cosine_match, cosine_match)
    elif False:
        variant = variant.upper().replace(' ', '_').replace('/', '_').replace('-', '_')
        primary_relationships.append(variant)
        #literal_primary_relationships = Literal[tuple(primary_relationships)]
        relationship_embeddings_array = np.append(relationship_embeddings_array, variant_embedding, axis=0)
        with open('new_relationships.txt', 'a', encoding='utf-8') as f:
            f.write(variant+'\n')
        return variant
    variant = variant.upper().replace(' ', '_').replace('/', '_').replace('-', '_')
    if best_primary_match:
        best_primary_match = best_primary_match.upper().replace(' ', '_').replace('/', '_').replace('-', '_')
        if (best_primary_match.endswith('_BY') and not variant.endswith('_BY')) or \
            (not best_primary_match.endswith('_BY') and variant.endswith('_BY')):
            if best_primary_match in primary_relationships_by:
                best_primary_match = primary_relationships_by[best_primary_match]
            else:
                print('low not', cosine_score, variant, best_primary_match)
                return None
    print('low', cosine_score, variant, best_primary_match)
    return best_primary_match

for company, values in node_type_context.items():
    for variant in values:
        if variant in primary_relationships or variant in relationship_mapping_reverse:continue
        norm = map_relationship_type_combined(variant, None, None, True)
        if norm and norm != variant:
            print(f"{variant} -> {norm}")
            relationship_mapping_reverse[variant] = norm
        else:
            print('missing', variant)



registry = {
    'nodes': {
        # Business entities
        'Company': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&]+$',
            'props': ['name', 'sector', 'revenue', 'founded_date', 'headquarters', 'employee_count', 'ticker', 'exchange']
        },
        'Organization': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&]+$',
            'props': ['name', 'type', 'founded_date', 'headquarters', 'mission']
        },
        'Group': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'purpose', 'member_count', 'founding_date']
        },
        
        # People
        'Person': {
            'id_pattern': r'^[A-Z][a-z]+\-?[A-Z]?[a-z]*$',
            'props': ['name', 'birth_date', 'nationality', 'occupation', 'education']
        },
        
        # Locations
        'Location': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'coordinates', 'population', 'area', 'timezone']
        },
        'Country': {
            'id_pattern': r'^[A-Za-z\.\-\&\s]+$',
            'props': ['name', 'capital', 'population', 'currency', 'continent', 'gdp', 'official_language']
        },
        'City': {
            'id_pattern': r'^[A-Za-z\.\-\&\s]+$',
            'props': ['name', 'country', 'population', 'coordinates', 'founded_year', 'mayor']
        },
        'Region': {
            'id_pattern': r'^[A-Za-z\.\-\&\s]+$',
            'props': ['name', 'country', 'capital', 'population', 'area']
        },
        
        # Financial instruments
        'FinancialInstrument': {
            'id_pattern': r'^[A-Za-z0-9\.\-\:]+$',
            'props': ['name', 'type', 'issuer', 'issue_date', 'denomination', 'market_value']
        },
        'Stock': {
            'id_pattern': r'^[A-Z]{1,5}(\.[A-Z]{1,2})?$',
            'props': ['ticker', 'company', 'exchange', 'share_price', 'market_cap', 'pe_ratio', 'dividend_yield']
        },
        'Bond': {
            'id_pattern': r'^[A-Za-z0-9\.\-\:]+$',
            'props': ['name', 'issuer', 'issue_date', 'maturity_date', 'coupon_rate', 'face_value', 'yield']
        },
        'Currency': {
            'id_pattern': r'^[A-Z]{3}$',
            'props': ['code', 'name', 'symbol', 'issuing_authority', 'inflation_rate']
        },
        'Cryptocurrency': {
            'id_pattern': r'^[A-Za-z0-9\.\-]+$',
            'props': ['symbol', 'name', 'blockchain', 'launch_date', 'market_cap', 'circulating_supply']
        },
        'Commodity': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'category', 'unit', 'price', 'primary_producers', 'trading_market']
        },
        
        # Market entities
        'Market': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'type', 'participants', 'volume', 'regulation', 'geography']
        },
        'Exchange': {
            'id_pattern': r'^[A-Za-z0-9\.\-]+$',
            'props': ['name', 'location', 'founded_date', 'trading_hours', 'market_cap', 'listed_companies']
        },
        'Index': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'ticker', 'components', 'calculation_method', 'base_value', 'launch_date']
        },
        
        # Products and services
        'Product': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'manufacturer', 'category', 'launch_date', 'price', 'features']
        },
        'Service': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'provider', 'category', 'launch_date', 'pricing', 'features']
        },
        'Software': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'developer', 'version', 'release_date', 'platform', 'license_type']
        },
        'Platform': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'owner', 'launch_date', 'type', 'user_base', 'features']
        },
        
        # Events and time
        'Event': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'date', 'location', 'organizer', 'type', 'participants']
        },
        'Conference': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'date', 'location', 'organizer', 'topic', 'attendees']
        },
        'Date': {
            'id_pattern': r'^[0-9]{4}(-[0-9]{2}){0,2}$',
            'props': ['year', 'month', 'day', 'quarter', 'weekday', 'is_holiday']
        },
        'Year': {
            'id_pattern': r'^[0-9]{4}$',
            'props': ['value', 'is_leap_year', 'fiscal_year']
        },
        
        # Sectors and industries
        'Sector': {
            'id_pattern': r'^[A-Za-z\.\-\&\s]+$',
            'props': ['name', 'description', 'market_cap', 'growth_rate', 'companies']
        },
        'Industry': {
            'id_pattern': r'^[A-Za-z\.\-\&\s]+$',
            'props': ['name', 'sector', 'market_size', 'growth_rate', 'major_players']
        },
        
        # Reports and documents
        'Report': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['title', 'publication_date', 'author', 'publisher', 'type', 'subject']
        },
        'Document': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['title', 'creation_date', 'author', 'type', 'content', 'format']
        },
        
        # Metrics and measurements
        'Metric': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'description', 'unit', 'calculation_method', 'frequency', 'source']
        },
        'Price': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['value', 'currency', 'item', 'date', 'source', 'change_percent']
        },
        'Revenue': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['value', 'currency', 'company', 'period', 'source', 'growth_rate']
        },
        
        # Technology and concepts
        'Technology': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'category', 'developer', 'year_introduced', 'applications', 'current_state']
        },
        'Concept': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'definition', 'field', 'originator', 'related_concepts']
        },
        
        # Projects
        'Project': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'start_date', 'end_date', 'manager', 'budget', 'status']
        },
        
        # Regulation and legal
        'Regulation': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'issuing_authority', 'effective_date', 'scope', 'penalties', 'targeted_sector']
        },
        
        # Health
        'Disease': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'causative_agent', 'symptoms', 'treatments', 'prevalence', 'affected_systems']
        },
        
        # Signals
        'Signal': {
            'id_pattern': r'^[A-Za-z0-9\.\-\&\s]+$',
            'props': ['name', 'type', 'source', 'date', 'strength', 'direction', 'confidence']
        }
    },
    
    'relationships': {
        # Core business relationships
        'AFFECTS': {
            'valid_sources': ['Market', 'Regulation', 'Event', 'Technology', 'Price', 'Currency', 'Report', 'Disease', 'Person', 'Metric', 'FinancialInstrument', 'Company'],
            'valid_targets': ['Stock', 'Currency', 'Company', 'Market', 'Price', 'Revenue', 'Industry', 'Sector', 'Person', 'Metric', 'FinancialInstrument', 'Event'],
            'props': ['impact_type', 'magnitude', 'date', 'direction', 'confidence', 'duration', 'sentiment', 'volatility', 'value', 'type', 'description', 'metric', 'rank', 'percentage', 'event']
        },
        'LEADS': {
            'valid_sources': ['Person', 'Organization'],
            'valid_targets': ['Company', 'Organization', 'Department', 'Team', 'Project', 'Event'],
            'props': ['title', 'start_date', 'end_date', 'responsibilities', 'achievements', 'effectiveness', 'role', 'date', 'position', 'magnitude', 'impact_type', 'duration']
        },
        'REPORTED': {
            'valid_sources': ['Company', 'Organization', 'Person', 'Report', 'Document', 'Event', 'Metric'],
            'valid_targets': ['Revenue', 'Event', 'Metric', 'Price', 'Market', 'Disease', 'Technology', 'Company', 'Date'],
            'props': ['date', 'source', 'confidence', 'method', 'context', 'revision_number', 'value', 'magnitude', 'impact_type', 'metric', 'type', 'name', 'amount', 'rank', 'range', 'year', 'revenue', 'market_cap', 'close', 'debt_to_equity', 'target', 'fiscal_period', 'description', 'unit', 'role', 'start_date', 'release_date', 'price', 'net_profit', 'inverse']
        },
        'COMPETES_WITH': {
            'valid_sources': ['Company', 'Product', 'Service', 'Technology', 'Person', 'Organization'],
            'valid_targets': ['Company', 'Product', 'Service', 'Technology', 'Person', 'Organization'],
            'props': ['market', 'intensity', 'start_date', 'advantage', 'disadvantage', 'market_share', 'date', 'rank', 'magnitude', 'impact_type', 'description', 'sector']
        },
        'PARTNERS_WITH': {
            'valid_sources': ['Company', 'Organization', 'Person'],
            'valid_targets': ['Company', 'Organization', 'Person'],
            'props': ['since', 'purpose', 'investment', 'projects', 'exclusivity', 'revenue_share', 'date', 'role', 'magnitude', 'type', 'impact_type', 'description', 'partner']
        },
        'LOCATED_IN': {
            'valid_sources': ['Company', 'Organization', 'Person', 'Event', 'Project', 'Product', 'City'],
            'valid_targets': ['Country', 'City', 'Region', 'Location', 'Building', 'Address'],
            'props': ['since', 'headquarters', 'facility_type', 'size', 'importance', 'tax_jurisdiction', 'date', 'magnitude', 'location', 'country', 'value', 'impact_type', 'type', 'description', 'confidence', 'city', 'region', 'address']
        },
        'PRODUCES': {
            'valid_sources': ['Company', 'Organization', 'Country', 'Person', 'Product'],
            'valid_targets': ['Product', 'Service', 'Report', 'Technology', 'Commodity', 'Company'],
            'props': ['since', 'volume', 'facility', 'quality', 'cost', 'capacity', 'date', 'magnitude', 'type', 'interval', 'quantity', 'sector', 'location', 'name', 'description']
        },
        'PRODUCED_BY': {
            'valid_sources': ['Product', 'Service', 'Report', 'Technology', 'Commodity'],
            'valid_targets': ['Company', 'Organization', 'Country', 'Person'],
            'props': ['since', 'volume', 'facility', 'quality', 'cost', 'exclusivity', 'date', 'magnitude']
        },
        'OWNS': {
            'valid_sources': ['Company', 'Organization', 'Person', 'Country'],
            'valid_targets': ['Company', 'Product', 'Service', 'Property', 'Technology', 'FinancialInstrument'],
            'props': ['since', 'stake_percentage', 'acquisition_cost', 'control_type', 'voting_rights', 'date', 'magnitude', 'percentage', 'amount', 'type', 'impact_type', 'region', 'market_cap']
        },
        'CAUSES': {
            'valid_sources': ['Event', 'Person', 'Company', 'Technology', 'Disease', 'Regulation'],
            'valid_targets': ['Event', 'Price', 'Market', 'Disease', 'Technology', 'Behavior'],
            'props': ['certainty', 'mechanism', 'delay', 'studies', 'context', 'statistical_significance', 'date', 'magnitude', 'confidence', 'type', 'role', 'impact_type', 'description', 'value']
        },
        
        # Financial relationships
        'INVESTED_IN': {
            'valid_sources': ['Company', 'Person', 'Organization', 'Fund'],
            'valid_targets': ['Company', 'Project', 'Product', 'Service', 'Technology', 'Stock', 'Bond'],
            'props': ['amount', 'currency', 'date', 'stake_percentage', 'valuation', 'round', 'exit_strategy', 'magnitude', 'role']
        },
        'PART_OF': {
            'valid_sources': ['Company', 'Organization', 'Product', 'City', 'Person', 'Event'],
            'valid_targets': ['Company', 'Organization', 'Group', 'Index', 'Country', 'Region', 'Event', 'Sector', 'Industry'],
            'props': ['since', 'role', 'importance', 'contribution', 'division', 'weight', 'date', 'magnitude', 'type', 'name', 'rank', 'impact_type', 'sector', 'description', 'relationship', 'percentage', 'confidence']
        },
        'LAUNCHED': {
            'valid_sources': ['Company', 'Organization', 'Person'],
            'valid_targets': ['Product', 'Service', 'Event', 'Project', 'Campaign'],
            'props': ['date', 'location', 'success_metric', 'investment', 'target_audience', 'market_reception', 'magnitude', 'impact_type']
        },
        'USES': {
            'valid_sources': ['Company', 'Organization', 'Person', 'Product', 'Service'],
            'valid_targets': ['Technology', 'Product', 'Service', 'Resource', 'Currency', 'Method'],
            'props': ['since', 'frequency', 'purpose', 'effectiveness', 'dependency', 'cost', 'date', 'magnitude', 'currency', 'impact_type', 'price', 'value', 'unit', 'confidence', 'type', 'name', 'metric', 'description', 'amount']
        },
        'LISTED_ON': {
            'valid_sources': ['Stock', 'Company', 'Bond', 'ETF', 'Cryptocurrency', 'FinancialInstrument'],
            'valid_targets': ['Exchange', 'Market', 'Platform', 'Index', 'Rating_Agency'],
            'props': ['listing_date', 'ticker', 'initial_price', 'listing_requirements', 'market_segment', 'rating', 'date', 'exchange', 'magnitude', 'rank', 'name', 'percentage', 'type', 'symbol', 'role', 'market_cap', 'market']
        },
        'HAS': {
            'valid_sources': ['Company', 'Product', 'Organization', 'Technology', 'Person', 'Event', 'Report'],
            'valid_targets': ['Feature', 'Property', 'Attribute', 'Component', 'Characteristic', 'Metric'],
            'props': ['since', 'importance', 'exclusivity', 'quality', 'quantity', 'proprietary', 'date', 'value', 'magnitude']
        },
        'ACQUIRED': {
            'valid_sources': ['Company', 'Organization', 'Person'],
            'valid_targets': ['Company', 'Product', 'Technology', 'Service', 'Property'],
            'props': ['date', 'amount', 'currency', 'deal_type', 'percentage', 'integration_status', 'synergy_value', 'magnitude']
        },
        'COMPARED_TO': {
            'valid_sources': ['Company', 'Product', 'Stock', 'Metric', 'Technology', 'Price', 'Market'],
            'valid_targets': ['Company', 'Product', 'Stock', 'Metric', 'Technology', 'Price', 'Market', 'Index'],
            'props': ['basis', 'result', 'date', 'method', 'evaluator', 'context', 'relative_performance', 'magnitude', 'impact_type', 'metric', 'rank', 'value', 'comparison_type', 'index', 'relative_price_change', 'confidence']
        },
        'INFLUENCED_BY': {
            'valid_sources': ['Company', 'Person', 'Market', 'Price', 'Product', 'Stock', 'Policy'],
            'valid_targets': ['Person', 'Company', 'Event', 'Regulation', 'Technology', 'Market', 'Report'],
            'props': ['degree', 'mechanism', 'evidence', 'time_period', 'context', 'lag', 'date', 'magnitude', 'impact_type', 'criterion', 'confidence', 'indicator', 'description', 'criteria']
        },
        
        # New financial relationships
        'MERGED_WITH': {
            'valid_sources': ['Company', 'Organization'],
            'valid_targets': ['Company', 'Organization'],
            'props': ['date', 'value', 'structure', 'surviving_entity', 'regulatory_approval', 'synergy_estimate']
        },
        'SPUN_OFF': {
            'valid_sources': ['Company', 'Division', 'Subsidiary'],
            'valid_targets': ['Company', 'Organization'],
            'props': ['date', 'valuation', 'ownership_structure', 'reason', 'market_reception', 'performance']
        },
        'DEFAULTED_ON': {
            'valid_sources': ['Company', 'Organization', 'Country', 'Person'],
            'valid_targets': ['Loan', 'Bond', 'Debt', 'Payment', 'Obligation'],
            'props': ['date', 'amount', 'reason', 'consequences', 'restructuring', 'recovery_rate']
        },
        'HEDGES_AGAINST': {
            'valid_sources': ['Investment', 'Strategy', 'Company', 'Fund'],
            'valid_targets': ['Risk', 'Price', 'Currency', 'Commodity', 'Market', 'Event'],
            'props': ['method', 'effectiveness', 'cost', 'duration', 'exposure_reduced', 'correlation']
        },
        'DENOMINATED_IN': {
            'valid_sources': ['Bond', 'Security', 'Contract', 'Transaction', 'Commodity'],
            'valid_targets': ['Currency', 'Cryptocurrency', 'Asset'],
            'props': ['since', 'exchange_risk', 'conversion_terms', 'revaluation_frequency', 'volatility']
        },
        'CORRELATED_WITH': {
            'valid_sources': ['Price', 'Stock', 'Commodity', 'Currency', 'Index', 'Metric'],
            'valid_targets': ['Price', 'Stock', 'Commodity', 'Currency', 'Index', 'Metric'],
            'props': ['coefficient', 'period', 'significance', 'causality', 'stability', 'lag', 'magnitude']
        },
        
        # Temporal relationships
        'PRECEDES': {
            'valid_sources': ['Event', 'Announcement', 'Report', 'Transaction'],
            'valid_targets': ['Event', 'Announcement', 'Report', 'Transaction', 'Market_Move'],
            'props': ['time_gap', 'causality', 'predictive_power', 'regularity', 'significance', 'date']
        },
        'FOLLOWS': {
            'valid_sources': ['Event', 'Announcement', 'Report', 'Transaction', 'Market_Move'],
            'valid_targets': ['Event', 'Announcement', 'Report', 'Transaction'],
            'props': ['time_gap', 'causality', 'reaction', 'pattern', 'consistency']
        },
        'PREDICTS': {
            'valid_sources': ['Indicator', 'Metric', 'Model', 'Signal', 'Pattern'],
            'valid_targets': ['Event', 'Price', 'Market', 'Performance', 'Trend'],
            'props': ['accuracy', 'lead_time', 'confidence', 'conditions', 'historical_reliability', 'date']
        },
        
        # Participation and operation
        'PARTICIPATES_IN': {
            'valid_sources': ['Company', 'Organization', 'Person', 'Country'],
            'valid_targets': ['Event', 'Market', 'Project', 'Conference', 'Initiative', 'Program'],
            'props': ['role', 'since', 'until', 'contribution', 'outcome', 'importance', 'influence', 'date']
        },
        'OPERATES_IN': {
            'valid_sources': ['Company', 'Organization', 'Person', 'Product', 'Service'],
            'valid_targets': ['Market', 'Industry', 'Sector', 'Country', 'Region', 'City'],
            'props': ['since', 'scale', 'market_share', 'regulatory_status', 'challenges', 'competitive_position', 'date', 'magnitude', 'type', 'name', 'impact_type', 'year', 'sector', 'location', 'day', 'value', 'description', 'time', 'rank', 'market', 'country', 'region']
        },
        'TRADED_IN': {
            'valid_sources': ['Stock', 'Bond', 'Currency', 'Commodity', 'Cryptocurrency', 'FinancialInstrument', 'Company'],
            'valid_targets': ['Market', 'Exchange', 'Currency', 'Platform'],
            'props': ['volume', 'frequency', 'average_price', 'volatility', 'liquidity', 'spread', 'date', 'magnitude', 'currency', 'price', 'impact_type', 'exchange', 'rank', 'amount', 'reason', 'exchange_rate', 'symbol', 'value', 'sector', 'currency_conversion', 'confidence', 'code']
        },
        
        # Geopolitical relationships
        'SANCTIONS': {
            'valid_sources': ['Country', 'Organization', 'Regulatory_Body'],
            'valid_targets': ['Country', 'Company', 'Person', 'Industry', 'Sector'],
            'props': ['start_date', 'end_date', 'type', 'severity', 'impact', 'compliance_requirements']
        },
        'EMBARGOES': {
            'valid_sources': ['Country', 'Trade_Bloc', 'Organization'],
            'valid_targets': ['Country', 'Product', 'Commodity', 'Industry', 'Company'],
            'props': ['start_date', 'scope', 'exceptions', 'enforcement', 'economic_impact', 'compliance_rate']
        },
        
        # Ranking and evaluation
        'RANKED': {
            'valid_sources': ['Company', 'Product', 'Person', 'Country', 'City', 'University', 'Fund', 'Stock', 'Bond'],
            'valid_targets': ['Ranking', 'Category', 'List', 'Industry', 'Sector', 'Metric'],
            'props': ['position', 'score', 'date', 'criteria', 'previous_position', 'evaluator', 'peer_comparison', 'rank']
        },
        'REPORTS': {
            'valid_sources': ['Person', 'Department', 'Company', 'Organization'],
            'valid_targets': ['Person', 'Department', 'Company', 'Organization'],
            'props': ['since', 'role', 'frequency', 'communication_method', 'hierarchy_level', 'authority_level', 'date']
        },
        'IS_A': {
            'valid_sources': ['Company', 'Organization', 'Product', 'Person', 'Event'],
            'valid_targets': ['Category', 'Type', 'Classification', 'Group', 'Class'],
            'props': ['since', 'classification_system', 'certainty', 'specific_type', 'criteria_met', 'date']
        },
        
        # Economic relationships
        'EXCHANGE_RATE': {
            'valid_sources': ['Currency', 'Cryptocurrency'],
            'valid_targets': ['Currency', 'Cryptocurrency'],
            'props': ['rate', 'date', 'source', 'volatility', 'trend', 'volume', 'spread']
        },
        'VOLATILITY_OF': {
            'valid_sources': ['Market', 'Stock', 'Bond', 'Commodity', 'Currency', 'Index'],
            'valid_targets': ['Price', 'Return', 'Volume', 'Spread', 'Metric'],
            'props': ['value', 'period', 'calculation_method', 'historical_context', 'relative_to_benchmark']
        },
        
        # Work and employment
        'WORKED_AT': {
            'valid_sources': ['Person'],
            'valid_targets': ['Company', 'Organization', 'Department', 'Location'],
            'props': ['start_date', 'end_date', 'title', 'responsibilities', 'achievements', 'reason_for_leaving', 'compensation', 'role', 'position', 'duration', 'location', 'date']
        },
        'SERVES': {
            'valid_sources': ['Person', 'Company', 'Organization'],
            'valid_targets': ['Person', 'Company', 'Organization', 'Country', 'Community'],
            'props': ['role', 'start_date', 'end_date', 'capacity', 'impact', 'compensation', 'date']
        },
        'APPOINTED': {
            'valid_sources': ['Person', 'Company', 'Organization'],
            'valid_targets': ['Position', 'Role', 'Committee', 'Board', 'Project'],
            'props': ['date', 'term_length', 'appointer', 'previous_holder', 'reason', 'selection_process']
        },
        
        # Additional relationship types
        'RELATED_TO': {
            'valid_sources': ['Company', 'Person', 'Product', 'Event', 'Technology', 'Concept', 'Disease'],
            'valid_targets': ['Company', 'Person', 'Product', 'Event', 'Technology', 'Concept', 'Disease'],
            'props': ['relationship_type', 'strength', 'evidence', 'context', 'significance', 'discovery_method', 'date', 'magnitude']
        },
        'CONTAINS': {
            'valid_sources': ['Product', 'Service', 'Report', 'Index', 'Region', 'Organization', 'Event'],
            'valid_targets': ['Component', 'Ingredient', 'Element', 'Feature', 'Section', 'Stock', 'Company'],
            'props': ['amount', 'percentage', 'importance', 'function', 'since', 'visibility', 'date', 'magnitude']
        },
        'REGULATED_BY': {
            'valid_sources': ['Company', 'Product', 'Market', 'Industry', 'Currency', 'FinancialInstrument'],
            'valid_targets': ['Regulation', 'Organization', 'Government', 'Agency', 'Authority', 'Framework'],
            'props': ['since', 'compliance_status', 'impact', 'requirements', 'penalties', 'reporting_burden', 'date', 'magnitude', 'benchmark', 'criteria', 'impact_type', 'type', 'sections', 'article', 'section', 'relationship', 'regulation', 'name', 'indicator']
        },
        'TREATS': {
            'valid_sources': ['Drug', 'Therapy', 'Treatment', 'Procedure', 'Device', 'Technology'],
            'valid_targets': ['Disease', 'Condition', 'Symptom', 'Disorder', 'Injury', 'Pathogen'],
            'props': ['efficacy', 'side_effects', 'mechanism', 'approval_status', 'treatment_duration', 'cost_effectiveness', 'date']
        },
        'IMPORTS': {
            'valid_sources': ['Country', 'Company', 'Region', 'Industry'],
            'valid_targets': ['Product', 'Service', 'Resource', 'Commodity', 'Technology'],
            'props': ['volume', 'value', 'date', 'origin', 'tariff', 'trend', 'dependency_level']
        },
        'REGULATES': {
            'valid_sources': ['Regulation', 'Government', 'Organization', 'Agency', 'Authority'],
            'valid_targets': ['Company', 'Industry', 'Market', 'Product', 'Activity', 'Technology', 'Financial_Market'],
            'props': ['since', 'scope', 'enforcement_mechanism', 'penalties', 'compliance_cost', 'effectiveness']
        },
        'IS': {
            'valid_sources': ['Person', 'Company', 'Product', 'Organization', 'Location'],
            'valid_targets': ['Category', 'Type', 'Classification', 'Role', 'Status', 'Condition'],
            'props': ['since', 'until', 'certainty', 'context', 'defining_characteristics', 'exceptions', 'date']
        },
        'EXPORTS': {
            'valid_sources': ['Country', 'Company', 'Region', 'Industry'],
            'valid_targets': ['Product', 'Service', 'Resource', 'Commodity', 'Technology'],
            'props': ['volume', 'value', 'date', 'destination', 'growth_rate', 'market_share', 'competitive_advantage']
        },
        'REQUIRES': {
            'valid_sources': ['Product', 'Service', 'Company', 'Project', 'Technology', 'Process'],
            'valid_targets': ['Resource', 'Component', 'Skill', 'Technology', 'Material', 'License'],
            'props': ['quantity', 'criticality', 'alternatives', 'cost', 'supply_risk', 'sustainability']
        },
        'SUPPORTS': {
            'valid_sources': ['Person', 'Company', 'Organization', 'Product', 'Technology'],
            'valid_targets': ['Cause', 'Initiative', 'Project', 'Policy', 'Person', 'Organization'],
            'props': ['type', 'since', 'amount', 'public', 'impact', 'motivation', 'benefits_received', 'date', 'magnitude']
        },
        'DEVELOPS': {
            'valid_sources': ['Company', 'Organization', 'Person', 'Team'],
            'valid_targets': ['Product', 'Technology', 'Service', 'Software', 'Method', 'Treatment'],
            'props': ['start_date', 'status', 'investment', 'version', 'approach', 'goal', 'innovation_level']
        },
        'RECOMMENDS': {
            'valid_sources': ['Person', 'Organization', 'Report', 'Agency', 'Analyst'],
            'valid_targets': ['Product', 'Service', 'Stock', 'Strategy', 'Method', 'Treatment'],
            'props': ['date', 'confidence', 'basis', 'conditions', 'target_audience', 'expected_outcome', 'disclosure_status']
        },
        'COMPATIBLE_WITH': {
            'valid_sources': ['Product', 'Software', 'Technology', 'Service', 'Device', 'Component'],
            'valid_targets': ['Product', 'Software', 'Technology', 'Service', 'Device', 'Component'],
            'props': ['since', 'limitations', 'issues', 'requirements', 'tested_version', 'compatibility_level']
        },
        'WORKS_FOR': {
            'valid_sources': ['Person'],
            'valid_targets': ['Company', 'Organization', 'Government', 'Person'],
            'props': ['start_date', 'end_date', 'title', 'department', 'salary_range', 'responsibilities', 'performance', 'date', 'role']
        },
        'PARTICIPATED_IN': {
            'valid_sources': ['Person', 'Company', 'Organization', 'Country'],
            'valid_targets': ['Event', 'Project', 'Initiative', 'Conference', 'Program', 'Study'],
            'props': ['role', 'start_date', 'end_date', 'contribution', 'outcome', 'recognition', 'impact', 'date']
        },
        'LED_BY': {
            'valid_sources': ['Company', 'Organization', 'Department', 'Team', 'Project', 'Event'],
            'valid_targets': ['Person', 'Organization'],
            'props': ['start_date', 'end_date', 'title', 'authority_level', 'performance', 'leadership_style']
        }
    }
}


forbidden_combinations = {
    # Geographic hierarchy inconsistencies
    ("Country", "LOCATED_IN", "Country"),
    ("Country", "LOCATED_IN", "City"),
    ("Country", "LOCATED_IN", "Region"),
    ("Region", "LOCATED_IN", "City"),
    
    # Organizational hierarchy inconsistencies
    ("Organization", "PART_OF", "Product"),
    ("Company", "PART_OF", "Product"),
    ("Person", "PART_OF", "Product"),
    
    # Financial instrument inconsistencies
    ("Stock", "LISTED_ON", "Stock"),
    ("Bond", "LISTED_ON", "Bond"),
    ("Stock", "TRADED_IN", "Stock"),
    ("Currency", "DENOMINATED_IN", "Currency"),  # Same currency
    
    # Production inconsistencies
    ("Person", "PRODUCES", "Person"),
    ("Company", "PRODUCES", "Company"),
    ("Organization", "PRODUCES", "Organization"),
    ("Country", "PRODUCES", "Country"),
    
    # Ownership inconsistencies
    ("Person", "OWNS", "Person"),
    ("FinancialInstrument", "OWNS", "Company"),  # Instruments don't own companies
    ("Product", "OWNS", "Company"),  # Products don't own companies
    
    # Market relationship inconsistencies
    ("Market", "TRADED_IN", "Market"),
    ("Exchange", "TRADED_IN", "Exchange"),
    
    # Regulatory inconsistencies
    ("Regulation", "REGULATED_BY", "Regulation"),
    ("Company", "REGULATES", "Regulation"),  # Companies don't regulate regulations
    ("Product", "REGULATES", "Regulation"),  # Products don't regulate regulations
    
    # Impact relationship inconsistencies
    ("Disease", "AFFECTS", "Disease"),
    
    # Leadership inconsistencies
    ("Event", "LEADS", "Company"),  # Events don't lead companies
    ("Event", "LEADS", "Organization"),
    
    # Investment inconsistencies
    ("Fund", "INVESTED_IN", "Fund"),  # Circular investment
    ("Bond", "INVESTED_IN", "Company"),  # Bonds don't invest
    ("Stock", "INVESTED_IN", "Company"),  # Stocks don't invest
    
    # Competitive inconsistencies
    ("Event", "COMPETES_WITH", "Company"),  # Events don't compete with companies
    ("Currency", "COMPETES_WITH", "Person"),  # Currencies don't compete with people
    
    # Operational inconsistencies
    ("Product", "OPERATES_IN", "Product"),  # Products don't operate in products
    ("Service", "OPERATES_IN", "Service"),  # Services don't operate in services
    
    # Reporting inconsistencies
    ("Report", "REPORTS", "Report"),  # Reports don't report to reports
    ("Document", "REPORTS", "Document"),  # Documents don't report to documents
    
    # Causal inconsistencies
    ("Price", "CAUSES", "Regulation"),  # Prices don't cause regulations
    ("Revenue", "CAUSES", "Event"),  # Revenue doesn't cause events
    
    # Employment inconsistencies
    ("Company", "WORKS_FOR", "Company"),  # Companies don't work for companies
    ("Organization", "WORKS_FOR", "Organization"),  # Organizations don't work for organizations
    
    # Treatment inconsistencies
    ("Disease", "TREATS", "Disease"),  # Diseases don't treat diseases
    ("Disease", "TREATS", "Drug"),  # Diseases don't treat drugs
    
    # Trade inconsistencies
    ("Country", "IMPORTS", "Country"),  # Countries don't import countries
    ("Country", "EXPORTS", "Country"),  # Countries don't export countries
    
    # Technical inconsistencies
    ("Technology", "COMPATIBLE_WITH", "Person"),  # Technologies aren't compatible with people
    ("Software", "COMPATIBLE_WITH", "Person"),  # Software isn't compatible with people
    
    # Metric inconsistencies
    ("Metric", "MEASURES", "Metric"),  # Metrics don't measure metrics
    
    # Temporal inconsistencies
    ("Date", "PRECEDES", "Year"),  # Mixing date types
    ("Year", "FOLLOWS", "Date"),  # Mixing date types
    
    # Economic inconsistencies
    ("Price", "VOLATILITY_OF", "Price"),  # Circular reference
    
    # Acquisition inconsistencies
    ("Property", "ACQUIRED", "Company"),  # Properties don't acquire companies
    ("Technology", "ACQUIRED", "Company"),  # Technologies don't acquire companies
    
    # Merger inconsistencies
    ("Product", "MERGED_WITH", "Company"),  # Products don't merge with companies
    ("Service", "MERGED_WITH", "Organization"),  # Services don't merge with organizations
    
    # Report inconsistencies
    ("Event", "REPORTED", "Report"),  # Events don't report reports
    ("Event", "REPORTED", "Document"),  # Events don't report documents
    
    # Service inconsistencies
    ("Service", "SERVES", "Service"),  # Services don't serve services
    
    # Influence inconsistencies
    ("Event", "INFLUENCED_BY", "Event"),  # Need to use CAUSES instead
    
    # Participation inconsistencies
    ("Event", "PARTICIPATES_IN", "Event"),  # Events don't participate in events
    ("Conference", "PARTICIPATES_IN", "Conference"),  # Conferences don't participate in conferences
    
    # Comparison inconsistencies
    ("Concept", "COMPARED_TO", "Market"),  # Concepts aren't compared to markets in financial contexts
    
    # Launch inconsistencies
    ("Product", "LAUNCHED", "Organization"),  # Products don't launch organizations
    ("Campaign", "LAUNCHED", "Company"),  # Campaigns don't launch companies
    
    # Requirement inconsistencies
    ("License", "REQUIRES", "Company"),  # Licenses don't require companies
    ("Material", "REQUIRES", "Company"),  # Materials don't require companies
    
    # Support inconsistencies 
    ("Cause", "SUPPORTS", "Company"),  # Causes don't support companies
    ("Initiative", "SUPPORTS", "Technology"),  # Initiatives don't support technologies
    
    # Development inconsistencies
    ("Software", "DEVELOPS", "Company"),  # Software doesn't develop companies
    ("Method", "DEVELOPS", "Organization"),  # Methods don't develop organizations
    
    # Recommendation inconsistencies
    ("Product", "RECOMMENDS", "Person"),  # Products don't recommend people
    ("Service", "RECOMMENDS", "Organization"),  # Services don't recommend organizations
    
    # Dependency inconsistencies
    ("Resource", "DEPENDS_ON", "Resource"),  # Circular dependency
    ("Skill", "DEPENDS_ON", "Skill"),  # Circular dependency

    # AFFECTS - Company as Source
    ("Company", "AFFECTS", "Company"),        # Use COMPETES_WITH or PARTNERS_WITH instead
    ("Company", "AFFECTS", "Person"),         # Too vague - use more specific relationship
    ("Company", "AFFECTS", "Disease"),        # Companies don't affect diseases directly
    
    # AFFECTS - Metric as Source/Target
    ("Metric", "AFFECTS", "Person"),          # Metrics don't affect people directly
    ("Metric", "AFFECTS", "Disease"),         # Metrics don't affect diseases
    ("Metric", "AFFECTS", "Report"),          # Metrics don't affect reports
    ("Disease", "AFFECTS", "Metric"),         # Diseases don't affect metrics directly
    
    # AFFECTS - FinancialInstrument
    ("FinancialInstrument", "AFFECTS", "Disease"), # Financial instruments don't affect diseases
    ("FinancialInstrument", "AFFECTS", "Report"),  # Financial instruments don't affect reports directly
    ("Disease", "AFFECTS", "FinancialInstrument"), # Diseases don't affect financial instruments directly
    
    # AFFECTS - Event as Target
    ("Disease", "AFFECTS", "Event"),          # Diseases don't affect financial events
    ("Currency", "AFFECTS", "Event"),         # Too direct - market dynamics are more complex
    
    # REPORTED - Event as Source
    ("Event", "REPORTED", "Event"),           # Events don't report events
    ("Event", "REPORTED", "Disease"),         # Events don't report diseases
    
    # REPORTED - Company as Target
    ("Report", "REPORTED", "Company"),        # Circular reference - reports don't report companies
    ("Company", "REPORTED", "Company"),       # Companies don't report companies
    
    # COMPARED_TO - Index
    ("Index", "COMPARED_TO", "Index"),        # Need different indexes
    ("Index", "COMPARED_TO", "Person"),       # Indexes aren't compared to people
    ("Person", "COMPARED_TO", "Index"),       # People aren't compared to indexes
    
    # REPORTED - Date
    ("Date", "REPORTED", "Date"),             # Dates don't report dates
    ("Year", "REPORTED", "Date"),             # Years don't report dates
    
    # PART_OF - Sector/Industry
    ("Sector", "PART_OF", "Sector"),          # Sectors aren't part of sectors
    ("Industry", "PART_OF", "Industry"),      # Industries aren't part of industries
    ("Sector", "PART_OF", "Company"),         # Sectors aren't part of companies
    ("Industry", "PART_OF", "Company"),       # Industries aren't part of companies
    
    # PRODUCES - Company as Target
    ("Person", "PRODUCES", "Company"),        # People don't directly produce companies
    ("Commodity", "PRODUCES", "Company"),     # Commodities don't produce companies
    
    # PRODUCES - Product as Source
    ("Product", "PRODUCES", "Person"),        # Products don't produce people
    ("Product", "PRODUCES", "Company"),       # Products don't produce companies
    ("Product", "PRODUCES", "Organization"),  # Products don't produce organizations
    
    # TRADED_IN - Company as Source
    ("Company", "TRADED_IN", "Company"),      # Companies aren't traded in companies
    ("Company", "TRADED_IN", "Currency"),     # Companies aren't traded in currencies
    
    # Cross-category logical inconsistencies
    ("Metric", "PRODUCES", "Event"),          # Metrics don't produce events
    ("Date", "AFFECTS", "Currency"),          # Dates don't affect currencies
    ("Event", "REPORTED", "Price"),           # Events don't report prices
    ("Company", "TRADED_IN", "Stock"),        # Companies aren't traded in stocks
    ("Product", "PRODUCES", "Sector"),        # Products don't produce sectors
    ("FinancialInstrument", "AFFECTS", "Person"), # Financial instruments don't affect people directly
    ("Index", "COMPARED_TO", "Disease"),      # Indexes aren't compared to diseases
    ("Sector", "PART_OF", "Person"),          # Sectors aren't part of people
    ("Industry", "PART_OF", "Person"),        # Industries aren't part of people

    # Company as Source for AFFECTS - Forbidden Combinations
    ("Company", "AFFECTS", "Company"),        # Use COMPETES_WITH or PARTNERS_WITH instead
    ("Company", "AFFECTS", "Disease"),        # Companies don't affect diseases directly
    ("Company", "AFFECTS", "Report"),         # Companies don't affect reports (they create/publish them)
    ("Company", "AFFECTS", "Person"),         # Too vague - use more specific relationships like EMPLOYS

    # Metric as Source for REPORTED - Forbidden Combinations
    ("Metric", "REPORTED", "Person"),         # Metrics don't report people
    ("Metric", "REPORTED", "Organization"),   # Metrics don't report organizations
    ("Metric", "REPORTED", "Document"),       # Metrics don't report documents
    ("Metric", "REPORTED", "Report"),         # Metrics don't report reports
    ("Metric", "REPORTED", "Metric"),         # Would be recursive/confusing - use AFFECTS instead
}

def is_forbidden_combination(source_type, relationship_type, target_type):
    """Determines if a type combination is forbidden based on logical rules"""
    
    # Check specific combinations first
    if (source_type, relationship_type, target_type) in forbidden_combinations:
        return True, f"Combination ({source_type}, {relationship_type}, {target_type}) is explicitly forbidden"
    
    # Location hierarchy rule
    location_types = {"Country", "Region", "City", "Address"}
    if relationship_type == "LOCATED_IN" and source_type in location_types and target_type in location_types:
        location_hierarchy = {"Country": 1, "Region": 2, "City": 3, "Address": 4}
        if location_hierarchy.get(source_type, 0) <= location_hierarchy.get(target_type, 999):
            return True, f"Invalid location hierarchy: {source_type} cannot be located in {target_type}"
    
    # Production rule - organizations can't produce organizations
    org_types = {"Company", "Organization", "Person", "Country"}
    if relationship_type == "PRODUCES" and source_type in org_types and target_type in org_types:
        return True, f"{source_type} cannot produce {target_type}"
    
    # Financial instrument rules
    if relationship_type in {"INVESTS_IN", "ACQUIRES"} and source_type in {"Stock", "Bond", "FinancialInstrument"}:
        return True, f"{source_type} cannot {relationship_type.lower()} entities"
    
    # Event causality rules
    if relationship_type in {"LEADS", "WORKS_FOR", "DEVELOPS"} and source_type == "Event":
        return True, f"Events cannot {relationship_type.lower()} entities"
    
    # Self-incompatible relationships
    self_incompatible = {"PRECEDES", "FOLLOWS", "EXCHANGE_RATE", "MERGED_WITH"}
    if relationship_type in self_incompatible and source_type == target_type:
        return True, f"Self-referential {relationship_type} between {source_type}s is invalid"
    
    return False, ""

# Tests for the build methods
def test_build_hierarchy_tree():
    """Tests the build_hierarchy_tree function"""
    # Clear the node_hierarchy dictionary
    node_hierarchy.clear()
    
    # Call the build function
    build_hierarchy_tree()
    
    # Test that primary nodes exist in hierarchy
    assert "Company" in node_hierarchy, "Primary node 'Company' should be in hierarchy"
    assert "Person" in node_hierarchy, "Primary node 'Person' should be in hierarchy"
    
    # Test that child nodes are properly mapped
    assert "Country" in node_hierarchy["Location"], "Country should be a child of Location"
    assert "City" in node_hierarchy["Location"], "City should be a child of Location"
    
    # Test that a primary node with no explicit children has an empty set
    assert isinstance(node_hierarchy.get("Concept", set()), set), "Concept should map to a set"
    
    print("✅ build_hierarchy_tree test passed")

def test_build_parent_lookup():
    """Tests the build_parent_lookup function"""
    # Ensure hierarchy is built first
    if not node_hierarchy:
        build_hierarchy_tree()
    
    # Clear parent_lookup
    parent_lookup.clear()
    
    # Call the build function
    build_parent_lookup()
    
    # Test direct parent relationships
    assert "Location" in parent_lookup.get("Country", set()), "Location should be a parent of Country"
    assert "Location" in parent_lookup.get("City", set()), "Location should be a parent of City"
    
    # Test multi-level parent relationships (if available in data)
    for child, parents in parent_lookup.items():
        for parent in parents:
            # Check that parent exists in node_hierarchy
            assert parent in node_hierarchy, f"Parent '{parent}' of '{child}' should exist in node_hierarchy"
    
    print("✅ build_parent_lookup test passed")

def test_build_relationship_mappings():
    """Tests the build_relationship_mappings function"""
    # Clear relationship_mapping_reverse
    relationship_mapping_reverse.clear()
    
    # Call the build function
    build_relationship_mappings()
    
    # Test direct relationship mappings
    assert relationship_mapping_reverse.get("IMPACTS") == "AFFECTS", "IMPACTS should map to AFFECTS"
    assert relationship_mapping_reverse.get("DIRECTS") == "LEADS", "DIRECTS should map to LEADS"
    
    # Test coverage of all variants
    for primary, variants in primary_relationship_types.items():
        for variant in variants:
            assert variant in relationship_mapping_reverse, f"Variant '{variant}' should be in relationship_mapping_reverse"
            assert relationship_mapping_reverse[variant] == primary, f"Variant '{variant}' should map to '{primary}'"
    
    print("✅ build_relationship_mappings test passed")

def test_node_type_consistency():
    """Tests that node types are consistent across mappings"""
    # Ensure all hierarchies are built
    if not node_hierarchy:
        build_hierarchy_tree()
    if not parent_lookup:
        build_parent_lookup()
    
    # Check that all node types in context mapping exist in hierarchy or primary nodes
    for node_type in node_type_context.keys():
        assert (node_type in node_hierarchy or 
                node_type in primary_nodes or 
                any(node_type in children for children in node_hierarchy.values())), \
                f"Node type '{node_type}' in context mapping should exist in hierarchy or primary nodes"
    
    # Check that all primary nodes have context mappings if they interact with relationships
    for node in primary_nodes:
        if any(node in registry['relationships'][rel]['valid_sources'] or 
               node in registry['relationships'][rel]['valid_targets'] 
               for rel in registry['relationships']):
            if not node in node_type_context:
                print(f"Primary node '{node}' used in relationships should have a context mapping")
    
    print("✅ node_type_consistency test passed")

def test_registry_consistency():
    """Tests that registry entries are consistent"""
    # Check that all nodes in registry have a valid id_pattern and props
    for node_type, node_info in registry['nodes'].items():
        assert 'id_pattern' in node_info, f"Node type '{node_type}' should have an id_pattern"
        assert 'props' in node_info, f"Node type '{node_type}' should have props"
        assert isinstance(node_info['props'], list), f"Props for '{node_type}' should be a list"
    
    # Check that all relationships in registry have valid sources, targets, and props
    for rel_type, rel_info in registry['relationships'].items():
        assert 'valid_sources' in rel_info, f"Relationship '{rel_type}' should have valid_sources"
        assert 'valid_targets' in rel_info, f"Relationship '{rel_type}' should have valid_targets"
        assert 'props' in rel_info, f"Relationship '{rel_type}' should have props"
        assert isinstance(rel_info['valid_sources'], list), f"valid_sources for '{rel_type}' should be a list"
        assert isinstance(rel_info['valid_targets'], list), f"valid_targets for '{rel_type}' should be a list"
        assert isinstance(rel_info['props'], list), f"Props for '{rel_type}' should be a list"
    
    print("✅ registry_consistency test passed")

def test_relationship_reciprocity():
    """Tests that relationship types have appropriate reciprocal mappings"""
    # Define expected reciprocal relationships
    reciprocals = {
        "AFFECTS": "AFFECTED_BY",
        "OWNS": "OWNED_BY",
        "PRODUCES": "PRODUCED_BY",
        "LEADS": "LED_BY",
        "REPORTS": "REPORTED_BY",
        "INVESTS_IN": "INVESTED_IN",
        "CREATES": "CREATED_BY",
        "DEVELOPS": "DEVELOPED_BY"
    }
    
    # Check reciprocals exist in appropriate mappings
    for rel, recip in reciprocals.items():
        if rel in primary_relationship_types:
            assert recip in [variant for variants in primary_relationship_types.values() for variant in variants], \
                f"Reciprocal '{recip}' of '{rel}' should exist in relationship variants"
    
    print("✅ relationship_reciprocity test passed")



# Knowledge Graph Usage Examples

def initialize_knowledge_graph():
    """Initialize all hierarchies and lookups"""
    build_hierarchy_tree()
    build_parent_lookup()
    build_relationship_mappings()
    print("Knowledge graph initialized successfully!")

def find_node_parents(node_type):
    """Find all parent categories for a given node type"""
    if node_type not in parent_lookup:
        return f"No parents found for '{node_type}'. Is it a valid node type?"
    
    parents = parent_lookup.get(node_type, set())
    return f"Parents of {node_type}: {sorted(list(parents))}"

def find_node_children(parent_node):
    """Find all child node types for a parent node"""
    if parent_node not in node_hierarchy:
        return f"No children found for '{parent_node}'. Is it a valid parent node type?"
    
    children = node_hierarchy.get(parent_node, set())
    return f"Children of {parent_node}: {sorted(list(children))}"

def normalize_relationship(relationship_variant):
    """Normalize a relationship variant to its primary type"""
    primary_relationship = relationship_mapping_reverse.get(relationship_variant, relationship_variant)
    
    if primary_relationship == relationship_variant and relationship_variant not in primary_relationships:
        return f"'{relationship_variant}' is not a known relationship variant or primary type."
    
    return f"'{relationship_variant}' normalizes to '{primary_relationship}'"

def check_relationship_validity(source_type, relationship, target_type):
    """Check if a relationship is valid between two node types"""
    # First, normalize the relationship if it's a variant
    primary_rel = relationship_mapping_reverse.get(relationship, relationship)
    
    if primary_rel not in registry['relationships']:
        return f"Relationship '{primary_rel}' is not defined in the registry."
    
    # Check if the relationship is valid
    is_valid = (
        source_type in registry['relationships'][primary_rel]['valid_sources'] and
        target_type in registry['relationships'][primary_rel]['valid_targets']
    )
    
    return f"Relationship '{source_type} {relationship} {target_type}' is {'valid' if is_valid else 'invalid'}"

def get_node_properties(node_type):
    """Get properties for a node type"""
    if node_type not in registry['nodes']:
        return f"Node type '{node_type}' is not defined in the registry."
    
    node_props = registry['nodes'].get(node_type, {}).get('props', [])
    return f"Properties for {node_type}: {node_props}"

def find_relationship_variants(primary_rel):
    """Find all variants of a primary relationship"""
    if primary_rel not in primary_relationship_types:
        return f"'{primary_rel}' is not a primary relationship type."
    
    variants = primary_relationship_types.get(primary_rel, [])
    return f"Variants of {primary_rel}: {variants}"

def find_possible_relationships(node_type):
    """Find all possible relationship types for a node type"""
    if node_type not in node_type_context:
        return f"Node type '{node_type}' has no defined context relationships."
    
    relationships = node_type_context.get(node_type, [])
    return f"Possible relationships for {node_type}: {relationships}"

def find_valid_sources_for_relationship(relationship):
    """Find all valid source node types for a relationship"""
    if relationship not in registry['relationships']:
        # Try to normalize it first if it's a variant
        primary_rel = relationship_mapping_reverse.get(relationship, relationship)
        if primary_rel not in registry['relationships']:
            return f"Relationship '{relationship}' is not defined in the registry."
        relationship = primary_rel
    
    valid_sources = registry['relationships'][relationship]['valid_sources']
    return f"Valid source node types for {relationship}: {valid_sources}"

def find_valid_targets_for_relationship(relationship):
    """Find all valid target node types for a relationship"""
    if relationship not in registry['relationships']:
        # Try to normalize it first if it's a variant
        primary_rel = relationship_mapping_reverse.get(relationship, relationship)
        if primary_rel not in registry['relationships']:
            return f"Relationship '{relationship}' is not defined in the registry."
        relationship = primary_rel
    
    valid_targets = registry['relationships'][relationship]['valid_targets']
    return f"Valid target node types for {relationship}: {valid_targets}"

def suggest_relationship_path(source_type, target_type, max_hops=2):
    """Suggest possible relationship paths between two node types"""
    paths = []
    
    # Check direct relationships
    for rel in registry['relationships']:
        if (source_type in registry['relationships'][rel]['valid_sources'] and
            target_type in registry['relationships'][rel]['valid_targets']):
            paths.append(f"{source_type} --[{rel}]--> {target_type}")
    
    # If no direct paths and max_hops > 1, check for 2-hop paths
    if not paths and max_hops > 1:
        for intermediate in primary_nodes:
            for rel1 in registry['relationships']:
                if source_type in registry['relationships'][rel1]['valid_sources'] and intermediate in registry['relationships'][rel1]['valid_targets']:
                    for rel2 in registry['relationships']:
                        if intermediate in registry['relationships'][rel2]['valid_sources'] and target_type in registry['relationships'][rel2]['valid_targets']:
                            paths.append(f"{source_type} --[{rel1}]--> {intermediate} --[{rel2}]--> {target_type}")
    
    if not paths:
        return f"No relationship paths found between {source_type} and {target_type} within {max_hops} hops."
    
    return f"Possible relationship paths between {source_type} and {target_type}:\n" + "\n".join(paths)

def find_duplicate_relationship_variants():
    """
    Find relationship variants that appear in multiple primary relationship lists.
    This can cause mapping inconsistencies and should be resolved.
    
    Returns:
        dict: Dictionary mapping variant names to lists of primary relationships they appear in
    """
    duplicates = {}
    
    # Track which primary relationship each variant belongs to
    variant_to_primaries = {}
    
    for primary, variants in primary_relationship_types.items():
        if primary not in variants:variants.append(primary)
        for variant in variants:
            if variant not in variant_to_primaries:
                variant_to_primaries[variant] = []
            variant_to_primaries[variant].append(primary)
    
    # Find variants with more than one primary
    for variant, primaries in variant_to_primaries.items():
        if len(primaries) > 1:
            duplicates[variant] = primaries
    
    return duplicates

def fix_duplicate_relationship_variants():
    """
    Identify and fix duplicate relationship variants across primary relationships.
    
    Returns:
        dict: Dictionary of changes made, mapping variant names to the primary they were kept with
    """
    # Find duplicates first
    duplicates = find_duplicate_relationship_variants()
    
    if not duplicates:
        print("No duplicate relationship variants found. Nothing to fix.")
        return {}
    
    # Prepare to track changes
    changes = {}
    
    # Create a deep copy of primary_relationship_types to modify
    fixed_primaries = {k: list(v) for k, v in primary_relationship_types.items()}
    
    # For each duplicate, decide which primary to keep it with and remove from others
    for variant, primaries in duplicates.items():
        print(f"Found duplicate: '{variant}' appears in: {primaries}")
        
        # Simple heuristic - keep with the first primary alphabetically
        # (This can be replaced with a more sophisticated decision)
        keep_with = sorted(primaries)[0]
        print(f"  - Keeping '{variant}' with '{keep_with}'")
        
        # Remove from all other primaries
        for primary in primaries:
            if primary != keep_with:
                fixed_primaries[primary].remove(variant)
                print(f"  - Removed '{variant}' from '{primary}'")
        
        changes[variant] = keep_with
    
    # Return changes
    return changes

def rebuild_with_fixed_variants():
    """
    Find duplicate relationship variants, fix them, and rebuild the relationship mappings.
    
    Returns:
        tuple: (fixed_primaries, relationship_mapping_reverse)
    """
    # Find and fix duplicates
    changes = fix_duplicate_relationship_variants()
    
    if not changes:
        print("No changes needed.")
        return primary_relationship_types, relationship_mapping_reverse
    
    # Create a deep copy with the fixes applied
    fixed_primaries = {k: list(v) for k, v in primary_relationship_types.items()}
    
    # Apply all changes
    for variant, keep_with in changes.items():
        for primary in fixed_primaries:
            if primary != keep_with and variant in fixed_primaries[primary]:
                fixed_primaries[primary].remove(variant)
    
    # Rebuild the reverse mapping with the fixed primaries
    fixed_reverse_mapping = {}
    for primary, variants in fixed_primaries.items():
        for variant in variants:
            fixed_reverse_mapping[variant] = primary
    
    return fixed_primaries, fixed_reverse_mapping

def print_relationship_variants_report():
    """
    Print a detailed report of relationship variants, highlighting duplicates.
    """
    print("\n=== RELATIONSHIP VARIANTS REPORT ===\n")
    
    # Find duplicates
    duplicates = find_duplicate_relationship_variants()
    
    # Count variants per primary
    primary_counts = {primary: len(variants) for primary, variants in primary_relationship_types.items()}
    
    # Sort primaries by count of variants (descending)
    sorted_primaries = sorted(primary_counts.items(), key=lambda x: x[1], reverse=True)
    
    # Print summary
    print(f"Total primary relationships: {len(primary_relationship_types)}")
    print(f"Total relationship variants: {sum(primary_counts.values())}")
    print(f"Duplicate variants: {len(duplicates)}")
    print("\nPrimary relationships by number of variants:")
    
    for primary, count in sorted_primaries:
        print(f"  • {primary}: {count} variants")
    
    # Print duplicates if any
    if duplicates:
        print("\nDuplicate variants found:")
        for variant, primaries in sorted(duplicates.items()):
            print(f"  • '{variant}' appears in: {', '.join(primaries)}")
    
    print("\n=== END OF REPORT ===")


# Function to actually update the primary_relationship_types dictionary
def update_primary_relationship_types():
    """
    Update the primary_relationship_types dictionary to remove duplicate variants.
    This modifies the global dictionary in place.
    
    Returns:
        dict: Dictionary of changes made
    """
    # Find duplicates
    duplicates = find_duplicate_relationship_variants()
    
    if not duplicates:
        print("No duplicate relationship variants found. Nothing to update.")
        return {}
    
    changes = {}
    
    # For each duplicate, keep it only with the first primary alphabetically
    for variant, primaries in duplicates.items():
        keep_with = sorted(primaries)[0]
        changes[variant] = keep_with
        
        # Remove from all other primaries
        for primary in primaries:
            if primary != keep_with and variant in primary_relationship_types[primary]:
                primary_relationship_types[primary].remove(variant)
                print(f"Removed '{variant}' from '{primary}' (keeping with '{keep_with}')")
    
    # Rebuild the reverse mapping
    relationship_mapping_reverse.clear()
    for primary, variants in primary_relationship_types.items():
        for variant in variants:
            relationship_mapping_reverse[variant] = primary
    
    return changes

def validate_relationship(source_id, relationship_type, target_id, source_type, target_type):
    """
    Validates if a relationship between source and target is valid
    according to the registry definitions.
    
    Args:
        source_id (str): ID of the source node
        relationship_type (str): Type of relationship
        target_id (str): ID of the target node
        
    Returns:
        tuple: (is_valid, error_message)
    """
    # First, determine node types based on IDs (simplified for this example)
    # In a real implementation, you'd likely have a lookup mechanism or pattern matching
    
    # Check if relationship type exists in registry
    if relationship_type not in registry['relationships']:
        return False, f"Relationship type '{relationship_type}' is not defined in registry"
    
    # Check if source type is allowed for this relationship
    valid_sources = registry['relationships'][relationship_type]['valid_sources']
    if source_type not in valid_sources:
        return False, f"Source type '{source_type}' is not allowed for relationship '{relationship_type}'. Valid sources: {valid_sources}"
    
    # Check if target type is allowed for this relationship
    valid_targets = registry['relationships'][relationship_type]['valid_targets']
    if target_type not in valid_targets:
        return False, f"Target type '{target_type}' is not allowed for relationship '{relationship_type}'. Valid targets: {valid_targets}"
    
    # If we got here, the relationship is valid
    return True, "Relationship is valid"


if __name__ == "__main__":
    """Run all test functions"""
    # Initialize the hierarchies and lookups
    

    print_relationship_variants_report()
    
    if False:
        # Run tests
        test_build_hierarchy_tree()
        test_build_parent_lookup()
        test_build_relationship_mappings()
        test_node_type_consistency()
        test_registry_consistency()
        test_relationship_reciprocity()
    
        print("🎉 All tests passed!")


    
    # Example 1: Find all parent categories for a node type
    node_type = "City"
    parents = parent_lookup.get(node_type, set())
    print(f"Parents of {node_type}: {parents}")
    
    # Example 2: Find all possible relationship types for a Company
    company_relationships = node_type_context.get("Company", [])
    print(f"Possible relationships for a Company: {company_relationships}")
    
    # Example 3: Normalize a relationship variant to its primary type
    relationship_variant = "IMPACTS"
    primary_relationship = relationship_mapping_reverse.get(relationship_variant, relationship_variant)
    print(f"'{relationship_variant}' normalizes to '{primary_relationship}'")
    
    # Example 4: Check if a relationship is valid between two node types
    source_type = "Company"
    target_type = "Product"
    relationship = "PRODUCES"
    
    is_valid = (
        source_type in registry['relationships'][relationship]['valid_sources'] and
        target_type in registry['relationships'][relationship]['valid_targets']
    )
    
    print(f"Relationship '{source_type} {relationship} {target_type}' is {'valid' if is_valid else 'invalid'}")
    
    # Example 5: Get all child node types for a parent node
    parent_node = "Location"
    children = node_hierarchy.get(parent_node, set())
    print(f"Children of {parent_node}: {children}")
    
    # Example 6: Get properties for a node type
    node_props = registry['nodes'].get('Company', {}).get('props', [])
    print(f"Properties for Company: {node_props}")
    
    # Example 7: Find all variants of a primary relationship
    primary_rel = "AFFECTS"
    variants = primary_relationship_types.get(primary_rel, [])
    print(f"Variants of {primary_rel}: {variants}")

    """Main demonstration function"""
    #initialize_knowledge_graph()
    
    # Examples
    print("\n=== Knowledge Graph Examples ===\n")
    
    print(find_node_parents("City"))
    print(find_node_children("Location"))
    print(normalize_relationship("IMPACTS"))
    print(check_relationship_validity("Company", "PRODUCES", "Product"))
    print(get_node_properties("Company"))
    print(find_relationship_variants("AFFECTS"))
    print(find_possible_relationships("Company"))
    print(find_valid_sources_for_relationship("PRODUCES"))
    print(find_valid_targets_for_relationship("PRODUCES"))
    print(suggest_relationship_path("Company", "Technology"))

    # Test "Canada LOCATED_IN Brazil"
    source_type = "Country"  # Assuming Canada is a Country
    target_type = "Country"  # Assuming Brazil is a Country

    is_valid, message = validate_relationship("Canada", "LOCATED_IN", "Brazil", source_type, target_type)
    print(f"Is 'Canada LOCATED_IN Brazil' valid? {is_valid}")
    print(message)
    print('F', is_forbidden_combination(source_type, "LOCATED_IN", target_type))

    source_type = "City"  # Assuming Canada is a Country
    target_type = "City"  # Assuming Brazil is a Country
    is_valid, message = validate_relationship("London", "LOCATED_IN", "London", source_type, target_type)
    print(f"Is 'London LOCATED_IN London' valid? {is_valid}")
    print(message)
    print('F', is_forbidden_combination(source_type, "LOCATED_IN", target_type))

    # Let's also check the valid sources and targets for LOCATED_IN to understand why
    print("\nValid sources for LOCATED_IN:", registry['relationships']['LOCATED_IN']['valid_sources'])
    print("Valid targets for LOCATED_IN:", registry['relationships']['LOCATED_IN']['valid_targets'])

