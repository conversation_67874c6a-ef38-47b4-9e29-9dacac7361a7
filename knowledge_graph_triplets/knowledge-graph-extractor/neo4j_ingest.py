import traceback
import os
import shutil
import time
import json
import logging
from pathlib import Path
from random import randint
from pybloom_live import ScalableBloomFilter
from collections import defaultdict
from neo4j import GraphDatabase
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import regex as re

# Constants
BATCH_SIZE = 1000  # Increased batch size for better performance
MAX_WORKERS = 4    # Number of parallel threads

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


"""
#RESET:

# All nodes and relationships.
MATCH (n) DETACH DELETE n

# All indexes and constraints.
CALL apoc.schema.assert({},{},true) YIELD label, key RETURN *

// Create indexes for primary node types
// In Neo4j 5, we use CREATE INDEX syntax

// Create range indexes for common lookup properties
CREATE INDEX company_name IF NOT EXISTS FOR (c:Company) ON (c.name);
CREATE INDEX person_name IF NOT EXISTS FOR (p:Person) ON (p.name);
CREATE INDEX metric_name IF NOT EXISTS FOR (m:Metric) ON (m.name);
CREATE INDEX event_name IF NOT EXISTS FOR (e:Event) ON (e.name, e.date);
CREATE INDEX location_name IF NOT EXISTS FOR (l:Location) ON (l.name);
CREATE INDEX product_name IF NOT EXISTS FOR (p:Product) ON (p.name);
CREATE INDEX organization_name IF NOT EXISTS FOR (o:Organization) ON (o.name);
CREATE INDEX currency_code IF NOT EXISTS FOR (c:Currency) ON (c.code);
CREATE INDEX country_code IF NOT EXISTS FOR (c:Country) ON (c.code);
CREATE INDEX date_value IF NOT EXISTS FOR (d:Date) ON (d.value);
CREATE INDEX year_value IF NOT EXISTS FOR (y:Year) ON (y.value);
CREATE INDEX technology_name IF NOT EXISTS FOR (t:Technology) ON (t.name);
CREATE INDEX market_name IF NOT EXISTS FOR (m:Market) ON (m.name);
CREATE INDEX document_id IF NOT EXISTS FOR (d:Document) ON (d.id);
CREATE INDEX report_id IF NOT EXISTS FOR (r:Report) ON (r.id);
CREATE INDEX industry_name IF NOT EXISTS FOR (i:Industry) ON (i.name);
CREATE INDEX sector_name IF NOT EXISTS FOR (s:Sector) ON (s.name);
CREATE INDEX concept_name IF NOT EXISTS FOR (c:Concept) ON (c.name);
CREATE INDEX disease_name IF NOT EXISTS FOR (d:Disease) ON (d.name);
CREATE INDEX commodity_name IF NOT EXISTS FOR (c:Commodity) ON (c.name);
CREATE INDEX platform_name IF NOT EXISTS FOR (p:Platform) ON (p.name);
CREATE INDEX software_name IF NOT EXISTS FOR (s:Software) ON (s.name);
CREATE INDEX patent_id IF NOT EXISTS FOR (p:Patent) ON (p.id);
CREATE INDEX algorithm_name IF NOT EXISTS FOR (a:Algorithm) ON (a.name);
CREATE INDEX standard_code IF NOT EXISTS FOR (s:Standard) ON (s.code);
CREATE INDEX protocol_name IF NOT EXISTS FOR (p:Protocol) ON (p.name);
CREATE INDEX award_name IF NOT EXISTS FOR (a:Award) ON (a.name);
CREATE INDEX certification_name IF NOT EXISTS FOR (c:Certification) ON (c.name);
CREATE INDEX fund_name IF NOT EXISTS FOR (f:Fund) ON (f.name);
CREATE INDEX portfolio_id IF NOT EXISTS FOR (p:Portfolio) ON (p.id);
CREATE INDEX dataset_name IF NOT EXISTS FOR (d:Dataset) ON (d.name);
CREATE INDEX api_name IF NOT EXISTS FOR (a:API) ON (a.name);
CREATE INDEX framework_name IF NOT EXISTS FOR (f:Framework) ON (f.name);
CREATE INDEX library_name IF NOT EXISTS FOR (l:Library) ON (l.name);
CREATE INDEX database_name IF NOT EXISTS FOR (d:Database) ON (d.name);
CREATE INDEX cloud_name IF NOT EXISTS FOR (c:Cloud) ON (c.name);
CREATE INDEX network_name IF NOT EXISTS FOR (n:Network) ON (n.name);
CREATE INDEX campaign_name IF NOT EXISTS FOR (c:Campaign) ON (c.name);
CREATE INDEX strategy_name IF NOT EXISTS FOR (s:Strategy) ON (s.name);
CREATE INDEX policy_id IF NOT EXISTS FOR (p:Policy) ON (p.id);
CREATE INDEX material_name IF NOT EXISTS FOR (m:Material) ON (m.name);
CREATE INDEX asset_id IF NOT EXISTS FOR (a:Asset) ON (a.id);
CREATE INDEX transaction_id IF NOT EXISTS FOR (t:Transaction) ON (t.id);

"""

# # Keys typically used in properties
# DISPLAY_KEYS = ["name", "title", "label", "id"]
 
# Neo4j credentials
NEO4J_URI = "bolt://44.218.209.236:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "equbot12"
 
# Connect to Neo4j
driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))


done_jsons_path = 'done_jsons.txt'
# Load previously processed paragraph hashes
try:
    done_jsons = ScalableBloomFilter(mode=ScalableBloomFilter.SMALL_SET_GROWTH)
    with open(done_jsons_path, 'rb') as f:
        ScalableBloomFilter.fromfile(f)
except FileNotFoundError:
    done_jsons = ScalableBloomFilter(mode=ScalableBloomFilter.SMALL_SET_GROWTH)

def batch_update_nodes(tx, label, batch, id_field='id'):
    """
    Update or create nodes in batch mode.
    This function will create nodes if they don't exist and maintain property history.
    """
    # First, make sure nodes exist - create them if they don't
    create_query = f"""
    UNWIND $batch AS row
    MERGE (n:{label} {{ {id_field}: row.id }})
    ON CREATE SET n.name = row.properties.name
    RETURN count(n) AS created
    """
    
    # Create missing nodes
    created = tx.run(create_query, batch=batch).single()["created"]
    if created > 0:
        print(f"Created {created} new {label} nodes")
    
    # Now update properties and maintain history without APOC
    update_query = f"""
    UNWIND $batch AS row
    MATCH (n:{label} {{ {id_field}: row.id }})
    SET n += apoc.map.removeKeys(row.properties, ['name', 'date'])
    WITH n, row.properties AS props
    UNWIND keys(props) AS propKey
    WITH n, propKey, props[propKey] AS propValue, props['date'] AS date
    WHERE NOT propKey IN ['name', 'date']
    WITH n, propKey, propValue, date,
         CASE 
            WHEN n[propKey + '_history'] IS NULL THEN '[]' 
            ELSE n[propKey + '_history'] 
         END AS historyJson
    WITH n, propKey, propValue, date, 
         apoc.convert.fromJsonList(historyJson) AS history
    WITH n, propKey, propValue, date, history,
         (SIZE(history) = 0 OR history[-1].value <> toString(propValue) OR history[-1].date <> date) AS toAdd
    WITH n, propKey, propValue, date, history, toAdd,
         CASE 
           WHEN toAdd THEN 
             apoc.convert.toJson(history + [{{value: toString(propValue), date: date}}])
           ELSE n[propKey + '_history'] 
         END AS newHistory
    CALL apoc.create.setProperty(n, propKey + '_history', newHistory) YIELD node
    RETURN count(n) AS updated
    """

    # Preprocess batch to handle types
    for row in batch:
        props = row.get('properties', {})
        # Convert any complex values (maps/dicts) to JSON strings
        for key, value in list(props.items()):
            if isinstance(value, dict) or isinstance(value, list):
                props[key] = json.dumps(value)

    # Run the update query
    return tx.run(update_query, batch=batch).single()["updated"]


def batch_update_rels(tx, rel_type, batch):
    """
    Update or create relationships in batch mode with history tracking.
    This function will create relationships if they don't exist.
    """
    # First create missing relationships
    create_query = f"""
    UNWIND $batch AS row
    MATCH (source {{id: row.source}})
    MATCH (target {{id: row.target}})
    MERGE (source)-[r:`{rel_type}`]->(target)
    RETURN count(r) AS created
    """
    
    created = tx.run(create_query, batch=batch).single()["created"]
    if created > 0:
        print(f"Created {created} new {rel_type} relationships")
    
    # Then update all relationships with history tracking
    update_query = f"""
    UNWIND $batch AS row
    MATCH (source {{id: row.source}})-[r:`{rel_type}`]->(target {{id: row.target}})
    SET r += apoc.map.removeKeys(row.properties, ['name', 'date'])
    WITH r, row.properties AS props
    UNWIND keys(props) AS propKey
    WITH r, propKey, props[propKey] AS propValue, props['date'] AS date
    WHERE NOT propKey IN ['name', 'date']
    WITH r, propKey, propValue, date,
         CASE 
            WHEN r[propKey + '_history'] IS NULL THEN '[]' 
            ELSE r[propKey + '_history'] 
         END AS historyJson
    WITH r, propKey, propValue, date, 
         apoc.convert.fromJsonList(historyJson) AS history
    WITH r, propKey, propValue, date, history,
         (SIZE(history) = 0 OR history[-1].value <> toString(propValue) OR history[-1].date <> date) AS toAdd
    WITH r, propKey, propValue, date, history, toAdd,
         CASE 
           WHEN toAdd THEN 
             apoc.convert.toJson(history + [{{value: toString(propValue), date: date}}])
           ELSE r[propKey + '_history'] 
         END AS newHistory
    CALL apoc.create.setRelProperty(r, propKey + '_history', newHistory) YIELD rel
    RETURN count(r) AS updated
    """
    
    # Preprocess batch to handle types
    for row in batch:
        props = row.get('properties', {})
        # Convert any complex values (maps/dicts) to JSON strings
        for key, value in list(props.items()):
            if isinstance(value, dict) or isinstance(value, list):
                props[key] = json.dumps(value)

    return tx.run(update_query, batch=batch).single()["updated"]


def process_jsonl(file_path):
    """Process a JSONL file in highly optimized batches"""
    error_log_path = "errors.txt"
    batch_size = BATCH_SIZE
    
    with open(error_log_path, "w") as error_file:
        # Check APOC
        with driver.session(database='kgtriplets') as session:
            try:
                session.run("RETURN apoc.convert.toJson({test: 'value'}) as json").single()
                session.run("WITH '[]' as empty RETURN CASE WHEN empty = '[]' THEN [] ELSE apoc.convert.fromJsonList(empty) END as result").single()
            except Exception as e:
                error_msg = f"Required APOC functions are not available: {str(e)}"
                logger.error(error_msg)
                error_file.write(error_msg + "\n")
                error_file.write("Make sure APOC is installed and procedures are properly configured\n")
                return 0
        
        # Pre-process and batch the data
        node_batches = {}
        rel_batches = {}
        batch_count = 0
        total_processed = 0
        
        with open(file_path, 'r') as f:
            line_count = sum(1 for _ in f)
        
        logger.info(f"Starting processing of {file_path} with {line_count} lines")
        
        with open(file_path, 'r') as f:
            with driver.session(database='kgtriplets') as session:
                for count, line in enumerate(f, start=1):
                    line_hash = line.strip()
                    
                    if done_jsons.__contains__(line_hash):
                        logger.info(f"Skipped {count} already processed line")
                        continue
                    
                    try:
                        data = json.loads(line_hash)
                        
                        if 'source' in data and 'target' in data:
                            # Relationship
                            rel_type = data.get('type', data.get('mapped_type', 'RELATED'))
                            if rel_type == 'RELATED':
                                print('RELATED:', data)
                            properties = data.get('properties', {})
                            
                            if 'date' not in properties:
                                properties['date'] = data.get('date', datetime.now().strftime('%Y-%m-%d'))
                            
                            rel_entry = {
                                'source': data['source'],
                                'target': data['target'],
                                'properties': properties,
                            }
                            
                            rel_batches.setdefault(rel_type, []).append(rel_entry)
                            
                        else:
                            # Node
                            node_id = data['id']
                            node_label = data.get('labels', {}).get('primary', 'Entity')
                            properties = data.get('properties', {})
                            
                            # Ensure necessary properties
                            if 'name' not in properties:
                                properties['name'] = node_id
                                
                            if 'date' not in properties:
                                properties['date'] = data.get('date', datetime.now().strftime('%Y-%m-%d'))
                            
                            node_entry = {
                                'id': node_id,
                                'properties': properties
                            }
                            
                            node_batches.setdefault(node_label, []).append(node_entry)
                        
                        # Process batch when we reach threshold
                        batch_items = sum(len(nodes) for nodes in node_batches.values()) + sum(len(rels) for rels in rel_batches.values())
                        
                        if batch_items >= batch_size:
                            # Process nodes first
                            if node_batches:
                                for node_label, nodes in node_batches.items():
                                    try:
                                        node_stats = session.execute_write(batch_update_nodes, node_label, nodes)
                                        logger.info(f"Batch {batch_count}: Processed {len(nodes)} nodes (label={node_label})")
                                        total_processed += len(nodes)
                                    except Exception as e:
                                        error_msg = f"Error processing nodes with label {node_label}: {str(e)}"
                                        logger.error(error_msg)
                                        error_file.write(error_msg + "\n")
                                node_batches = {}
                            
                            # Then process relationships
                            if rel_batches:
                                for rel_type, rels in rel_batches.items():
                                    try:
                                        rel_stats = session.execute_write(batch_update_rels, rel_type, rels)
                                        logger.info(f"Batch {batch_count}: Processed {len(rels)} relationships (type={rel_type})")
                                        total_processed += len(rels)
                                    except Exception as e:
                                        error_msg = f"Error processing relationships with type {rel_type}: {str(e)}"
                                        logger.error(error_msg)
                                        error_file.write(error_msg + "\n")
                                rel_batches = {}
                            
                            batch_count += 1
                            
                            # Save progress
                            if batch_count % 5 == 0 or randint(1, 100) < 5:
                                with open(done_jsons_path, 'wb') as f_done:
                                    done_jsons.tofile(f_done)
                                logger.info(f"Progress: {count}/{line_count} lines ({(count/line_count)*100:.1f}%)")
                        
                        # Mark as processed
                        done_jsons.add(line_hash)
                        
                    except Exception as e:
                        error_message = f"Error on line {count} {line}: {str(e)}\n"
                        logger.error(error_message.strip())
                        error_file.write(error_message)
                        traceback.print_exc()
                
                # Process remaining batches
                if node_batches:
                    for node_label, nodes in node_batches.items():
                        try:
                            node_stats = session.execute_write(batch_update_nodes, node_label, nodes)
                            logger.info(f"Final batch: Processed {len(nodes)} nodes (label={node_label})")
                            total_processed += len(nodes)
                        except Exception as e:
                            error_msg = f"Error processing nodes with label {node_label}: {str(e)}"
                            logger.error(error_msg)
                            error_file.write(error_msg + "\n")
                
                if rel_batches:
                    for rel_type, rels in rel_batches.items():
                        try:
                            rel_stats = session.execute_write(batch_update_rels, rel_type, rels)
                            logger.info(f"Final batch: Processed {len(rels)} relationships (type={rel_type})")
                            total_processed += len(rels)
                        except Exception as e:
                            error_msg = f"Error processing relationships with type {rel_type}: {str(e)}"
                            logger.error(error_msg)
                            error_file.write(error_msg + "\n")
                
                # Final save of processed items
                with open(done_jsons_path, 'wb') as f_done:
                    done_jsons.tofile(f_done)
        
        logger.info(f"Processing complete. Total processed: {total_processed}")
        return total_processed

if __name__ == "__main__":            
    jsons_dir = Path('financial_kg')
    # Sort files by modified time, newest first
    all_files = sorted(
        jsons_dir.glob(f"*.jsonld"), 
        key=lambda x: os.path.getmtime(x),
        reverse=True
    )

    for json_path in all_files:
        logger.info(f"Processing file: {json_path}")
        pre_size = os.path.getsize(json_path)
        processed_count = process_jsonl(json_path)
        post_size = os.path.getsize(json_path)
        
        # If file size didn't change, assume it was fully processed
        if pre_size == post_size and processed_count > 0:
            done_dir = os.path.join(os.path.dirname(json_path), 'done')
            os.makedirs(done_dir, exist_ok=True)
            filename = os.path.basename(json_path)
            dest_path = os.path.join(done_dir, filename)
            
            if os.path.exists(dest_path):
                # Append content if destination already exists
                with open(json_path, 'r') as src_file:
                    content = src_file.read()
                with open(dest_path, 'a') as dest_file:
                    dest_file.write(content)
                os.remove(json_path)
                logger.info(f"Appended {json_path} to {dest_path} and deleted original")
            else:
                # Otherwise just move the file
                shutil.copy2(json_path, dest_path)
                os.remove(json_path)
                logger.info(f"Copied {json_path} to {dest_path} and deleted original")
            
        logger.info(f"Completed processing {json_path}, items processed: {processed_count}")
