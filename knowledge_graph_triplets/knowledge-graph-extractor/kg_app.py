import traceback
import os
import hashlib
import json
import time
import queue
import threading
import regex as re
from random import randint
from pybloom_live import ScalableBloomFilter
from time import sleep
from datetime import datetime
from dotenv import load_dotenv
from fastapi import FastAPI, Body, HTTPException, Request, Security
from fastapi.security.api_key import APIKeyHeader
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List, Dict
from kg_triplet_extractor import KnowledgeGraphExtractor
from persistent_priority_queue import PersistentPriorityQueue
import uvicorn

# Load environment variables
load_dotenv()

LOCAL_KG_API_KEY = "myveryscretnotsolongcode2"
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)
security = HTTPBearer()


async def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)):
    print(f"Auth token received: {credentials.credentials}")  # Let's see what we're getting
    if credentials.credentials != LOCAL_KG_API_KEY:
        raise HTTPException(401, "Invalid token")
    return True

app = FastAPI(title="Knowledge Graph Extraction Service")

def hash_text(text):
    """Create a hash for a text string to track processed paragraphs."""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

# Output files for knowledge graph
output_base = 'financial_kg'
if not os.path.exists(output_base):
    os.mkdir(output_base)


# Queue for processing items in order by date (most recent first)
#kg_queue = queue.PriorityQueue()
kg_queue = PersistentPriorityQueue("kg_queue_backup.pkl")


# File to track processed paragraphs
done_paragraphs_path = 'done_kg_app_paragraphs.txt'
# Load previously processed paragraph hashes
try:
    processed_paragraphs = ScalableBloomFilter(mode=ScalableBloomFilter.SMALL_SET_GROWTH)
    with open(done_paragraphs_path, 'rb') as f:
        ScalableBloomFilter.fromfile(f)
except FileNotFoundError:
    processed_paragraphs = ScalableBloomFilter(mode=ScalableBloomFilter.SMALL_SET_GROWTH)

# Flag to track if the processor is running
processor_running = False

# Define request model
class ExtractRequest(BaseModel):
    article_id: str
    domain: str
    text: str
    date: str  # Format: YYYY-MM-DD

# Define response model - simplified as requested
class ExtractResponse(BaseModel):
    status: str
    message: Optional[str] = None

# Define queue item model
class QueueItem:
    def __init__(self, priority: int, article_id: str, domain: str, text: str, date: str, text_hash: str):
        self.priority = priority  # Negative priority so most recent dates come first
        self.article_id = article_id
        self.domain = domain
        self.text = text
        self.date = date
        self.text_hash = text_hash
    
    def __lt__(self, other):
        return self.priority < other.priority

process_lock = threading.Lock()

def ensure_processor_running():
    global processor_running
    
    with process_lock:
        if not processor_running:
            # Start a single background thread
            thread = threading.Thread(target=process_queue)
            thread.daemon = True  # This allows the main app to exit even if this thread is running
            thread.start()
            processor_running = True
            print("Background processor started")

# Function to process the queue in background
def process_queue():
    global processor_running
    if not processor_running:
        ensure_processor_running()
        
    try:
        # Initialize the extractor here so it's only created when needed
        extractor = KnowledgeGraphExtractor(
            model="llama-3.3-70b-versatile",
            temperature=0.1
        )
        
        print(f"Starting queue processor, queue size: {kg_queue.qsize()}")
        
        while True:
            while not kg_queue.empty():
                try:
                    # Get next item from queue
                    item = kg_queue.get(block=False)
                    
                    # Skip if already processed (double check)
                    if processed_paragraphs.__contains__(item.text_hash):
                        print(f"Skipping already processed item: {item.text_hash}")
                        continue
                    
                    print(f"Processing item: {item.article_id}, date: {item.date}")
                    
                    # Extract knowledge graph
                    kg = extractor.extract_knowledge_graph(text=item.text)
                    
                    if kg is None or (not kg.nodes and not kg.relationships):
                        print(f"No knowledge graph extracted for: {item.article_id}")
                        continue
                    
                    # Add date and article_id to all nodes
                    for node in kg.nodes:
                        node.properties["date"] = item.date
                        node.properties["article_id"] = item.article_id
                        node.properties["domain"] = item.domain
                    
                    # Save to file system
                    date_compact = item.date.replace('-', '')
                    paragraph_output = f"{output_base}/{date_compact}"
                    extractor.save_as_jsonlines(kg, f"{paragraph_output}.jsonld")
                    
                    processed_paragraphs.add(item.text_hash)
                    if randint(1,100) < 5:
                        with open(done_paragraphs_path, 'wb') as f:
                            processed_paragraphs.tofile(f)
                    
                    print(f"Processed item: {item.article_id}, extracted {len(kg.nodes)} nodes, {len(kg.relationships)} relationships")
                    
                except queue.Empty:
                    break
                except Exception as e:
                    print(f"Error processing queue item: {str(e)}")
                    traceback.print_exc()
                finally:
                    kg_queue.task_done()
            
            #print("Queue processing complete")
            sleep(1)
    
    except Exception as e:
        print(f"Error in queue processor: {str(e)}")
        traceback.print_exc()
    finally:
        processor_running = False

@app.on_event("startup")
def startup_event():
    # The queue automatically loads data on initialization
    ensure_processor_running()

@app.on_event("shutdown")
async def shutdown_event():
    # The queue automatically saves data at shutdown
    pass

@app.post("/submit", response_model=ExtractResponse)
async def submit_to_kg_extraction(request: ExtractRequest = Body(...), authorized: bool = Security(security)):
    ensure_processor_running()
    try:
        if kg_queue.qsize() > 10000:
            raise HTTPException(status_code=400, detail="queue full, 10,000 items waiting!")
        # Validate date format
        try:
            date_obj = datetime.strptime(request.date, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%Y-%m-%d')
            # Calculate priority (negative timestamp so recent dates come first)
            priority = -int(date_obj.timestamp())
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")

        # Get text and create hash
        text = request.text
        text_hash = hash_text(text)
        
        # Check if already processed
        if processed_paragraphs.__contains__(text_hash):
            return ExtractResponse(
                status="skipped",
                message="This text has already been processed"
            )
        
        # Add to queue
        queue_item = QueueItem(
            priority=priority,
            article_id=request.article_id,
            domain=request.domain,
            text=text,
            date=formatted_date,
            text_hash=text_hash
        )
        kg_queue.put(queue_item)
        
        # Return success response
        return ExtractResponse(
            status="queued",
            message=f"Text has been queued for processing (queue size: {kg_queue.qsize()})"
        )
        
    except Exception as e:
        traceback_str = traceback.format_exc()
        error_message = f"Error submitting text for extraction: {str(e)}"
        print(error_message)
        print(traceback_str)
        raise HTTPException(status_code=500, detail=error_message)

@app.get("/download/{date}")
async def download_jsonl_file(date: str):#, authorized: bool = Security(security)):
    """
    Download a JSONL file for a given date.
    Date should be in format YYYY-MM-DD
    """
    # Validate date format
    if not re.match(r"^\d{4}-\d{2}-\d{2}$", date):
        raise HTTPException(status_code=400, detail="Date must be in format YYYY-MM-DD")
    
    # Convert date to the format used in filenames (remove dashes)
    date_compact = date.replace('-', '')
    
    # Construct the file path
    file_path = f"{output_base}/{date_compact}.jsonld"
    
    # Check if file exists
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"No data found for date {date}")
    
    # Define a generator function to stream the file
    def file_iterator():
        with open(file_path, mode="rb") as file:
            yield from file
    
    # Create a StreamingResponse to handle the file download
    return StreamingResponse(
        file_iterator(),
        media_type="application/ld+json",
        headers={
            "Content-Disposition": f"attachment; filename={date_compact}.jsonld"
        }
    )

@app.get("/queue/status")
async def queue_status():
    return {
        "queue_size": kg_queue.qsize(),
        "processor_running": processor_running,
        "processed_paragraphs": len(processed_paragraphs)
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# Combine knowledge graphs endpoint - still available for administrative use
@app.post("/admin/combine")
async def combine_knowledge_graphs(authorized: bool = Security(security)):
    try:
        # Load all JSONL files
        import glob
        jsonl_files = glob.glob(f"{output_base}/*.jsonld")
        
        all_nodes = []
        all_relationships = []
        
        for file in jsonl_files:
            with open(file, 'r') as f:
                for line in f:
                    data = json.loads(line)
                    if "nodes" in data:
                        all_nodes.extend(data["nodes"])
                    if "relationships" in data:
                        all_relationships.extend(data["relationships"])
        
        # Remove duplicate nodes (by ID)
        unique_nodes = []
        seen_node_ids = set()
        for node in all_nodes:
            node_id = node.get("id")
            if node_id and node_id not in seen_node_ids:
                unique_nodes.append(node)
                seen_node_ids.add(node_id)
        
        # Remove duplicate relationships
        unique_relationships = []
        seen_relationships = set()
        for rel in all_relationships:
            source = rel.get("source")
            rel_type = rel.get("type")
            target = rel.get("target")
            if source and rel_type and target:
                rel_key = (source, rel_type, target)
                if rel_key not in seen_relationships:
                    unique_relationships.append(rel)
                    seen_relationships.add(rel_key)
        
        # Save to JSON
        combined_kg = {
            "nodes": unique_nodes,
            "relationships": unique_relationships
        }
        
        with open(f"{output_base}_combined.json", 'w') as f:
            json.dump(combined_kg, f, indent=2, default=str)
            
        return {
            "status": "success",
            "nodes_count": len(unique_nodes),
            "relationships_count": len(unique_relationships),
            "output_file": f"{output_base}_combined.json"
        }
        
    except Exception as e:
        traceback_str = traceback.format_exc()
        error_message = f"Error combining knowledge graphs: {str(e)}"
        print(error_message)
        print(traceback_str)
        raise HTTPException(status_code=500, detail=error_message)

if __name__ == "__main__":
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8642,                
        ssl_keyfile="key.pem",
        ssl_certfile="cert.pem")
