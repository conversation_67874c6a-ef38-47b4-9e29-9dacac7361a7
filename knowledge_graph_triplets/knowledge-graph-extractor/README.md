# Brief:

 - <PERSON><PERSON> prompt for https://console.groq.com/docs/model/llama-4-scout-17b-16e-instruct model
   `triplet_extraction_prompt_small.py`
 - mini financial ontology with hierarchies and constrains
   `valid_nodes_rels.py`
 - main triplet extraction script
   `kg_triplet_extractor.py`
 - server accepting title/text/date/article_id and saving extracted structured data to local jsonld file
   `kg_app.py`
 - script running extraction on past data
   `text2kg.py`
 - script ingestion structured data from jsonld files into NEO4J instance for further use
   `neo4j_ingest.py`


 - there is also `clustering.py` script selecting sample data from the archived deduplicated news files, yet it keeps crashing the main vector db script on this server because after a while processes run out of memory.. so I run this script at times.. it can work for about half day without interrupting the main one. 


kg_app, text2kg, and clustering are started with nohup python ... &


REST API: [https://**************:8642/docs](https://**************:8642/docs)

# Long:

# Financial Knowledge Graph Extraction System

A system for extracting structured financial knowledge graphs from text using <PERSON>ro<PERSON>'s Llama-4-Scout model and storing them in Neo4j for analysis and querying.

## System Overview

This system processes financial text to extract entities (companies, people, events, metrics) and their relationships, then stores them as a knowledge graph in Neo4j. The pipeline includes validation, deduplication, and batch processing for production-scale data ingestion.

## Architecture Flow

```
Financial Text → LLM Extraction → Validation → JSON-LD Storage → Neo4j Ingestion
```

## Core Components

### 1. Financial Ontology (`valid_nodes_rels.py`)

Defines the structured vocabulary and constraints for the financial domain:

- **Primary Node Types**: Company, Metric, Event, Person, Location, FinancialInstrument, Product, Software, Revenue, Price, Signal, Research, Patent, Algorithm, Standard, etc.
- **Node Hierarchies**: Organized taxonomies (e.g., Location → Country/Region/City/State)
- **Relationship Types**: AFFECTS, LEADS, REPORTED, COMPETES_WITH, PARTNERS_WITH, LOCATED_IN, PRODUCES, OWNS, CAUSES, etc.
- **Relationship Mappings**: Hierarchical relationship structures with specialized subtypes

**Key Features:**
- Hierarchical node classification
- Relationship type mappings and constraints
- Domain-specific financial vocabulary
- Validation rules for entity types

### 2. LLM Prompt Engineering (`triplet_extraction_prompt_small.py`)

Engineered prompt for Groq's Llama-4-Scout-17B model to extract structured knowledge:

- **Entity ID Formatting**: Standardized naming conventions (e.g., "John-Smith", "Apple-Earnings-Report")
- **Structured Output**: JSON format with nodes and relationships
- **Financial Context**: Domain-specific examples and constraints
- **Property Extraction**: Automatic detection of relevant metadata (sector, market_cap, dates, impact)

**Example Output Format:**
```json
{
  "nodes": [
    {
      "id": "Intel",
      "type": "Company",
      "properties": {"sector": "Semiconductors"}
    }
  ],
  "relationships": [
    {
      "source": "Intel-Stock-Surge",
      "target": "Intel",
      "type": "AFFECTS",
      "properties": {"impact_type": "Positive", "magnitude": "13%"}
    }
  ]
}
```

### 3. Triplet Extractor (`kg_triplet_extractor.py`)

Core extraction engine with validation and processing:

**Key Classes:**
- `NodeLabels`: Manages hierarchical node labeling
- `Node`: Validates node structure and properties
- `Relationship`: Handles relationship mapping and validation
- `KnowledgeGraph`: Container for complete graph structure
- `KnowledgeGraphExtractor`: Main extraction orchestrator

**Features:**
- **Text Chunking**: Handles large documents by intelligent splitting
- **Validation Pipeline**: Multi-stage validation for nodes and relationships
- **Date Normalization**: Standardizes temporal information
- **Magnitude Processing**: Categorizes impact levels (POSITIVE/NEUTRAL/NEGATIVE)
- **Neo4j Export**: Generates compatible JSON format
- **Error Handling**: Robust processing with fallback mechanisms

### 4. REST API Server (`kg_app.py`)

Production-ready FastAPI server for real-time processing:

**Endpoints:**
- `POST /submit`: Queue new articles for processing
- `GET /download/{date}`: Retrieve processed JSON-LD files by date
- `GET /queue/status`: Monitor processing queue status
- `GET /health`: Health check endpoint

**Features:**
- **Queue-based Processing**: Asynchronous background processing
- **Deduplication**: Bloom filter prevents reprocessing
- **Priority Handling**: Intelligent queue management
- **Date-based Storage**: Organized file structure
- **Authentication**: Secured endpoints
- **Monitoring**: Real-time status tracking

**Request Format:**
```json
{
  "article_id": "unique_identifier",
  "domain": "finance.com",
  "text": "financial article content",
  "date": "2025-05-05"
}
```

### 5. Batch Processing (`text2kg.py`)

Batch processor for historical data:

- **Bulk Processing**: Handles large datasets of pre-processed articles
- **Progress Tracking**: Monitors processing status
- **Error Recovery**: Resumes from interruption points
- **Output Management**: Organizes results by date/domain

### 6. Neo4j Ingestion (`neo4j_ingest.py`)

Neo4j ingestion system:

**Key Functions:**
- `batch_update_nodes()`: Node creation/updates with history tracking
- `batch_update_rels()`: Relationship management with temporal versioning
- `process_jsonl()`: Optimized batch processing of JSON-LD files

**Features:**
- **Batch Processing**: Configurable batch sizes for optimal performance
- **History Tracking**: Maintains property change history without APOC
- **Duplicate Prevention**: Hash-based deduplication
- **Error Logging**: Comprehensive error tracking and recovery
- **Database Optimization**: Efficient Cypher queries for large-scale ingestion

## Data Flow

### 1. Text Input
Financial articles, reports, or news content are submitted with metadata (ID, date, domain).

### 2. LLM Extraction
The Groq Llama-4-Scout model processes text using the engineered prompt to identify:
- Financial entities (companies, people, events, metrics)
- Relationships between entities
- Temporal information and impact assessments

### 3. Validation & Normalization
Extracted data undergoes multi-stage validation:
- Entity type checking against ontology
- Relationship validation
- Date and magnitude normalization
- Duplicate detection

### 4. JSON-LD Storage
Validated knowledge graphs are stored as JSON-LD files organized by date for:
- Backup and recovery
- Batch processing
- Data lineage tracking

### 5. Neo4j Ingestion
Batch ingestion into Neo4j with:
- Optimized Cypher queries
- History tracking
- Relationship indexing

## Configuration

### Environment Variables
```bash
GROQ_API_KEY=your_groq_api_key
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
BATCH_SIZE=1000
```

### Model Configuration
- **Model**: llama-4-scout-17b-16e-instruct
- **Service Tier**: Should increase to enterprise.. currently on dev.

## Usage Examples

### Real-time Processing
```python
# Submit article via API
response = requests.post('/submit', json={
    'article_id': 'fin_001',
    'domain': 'finance.com',
    'text': 'Apple reported record Q1 earnings...',
    'date': '2025-05-05'
})
```

### Neo4j Querying
```cypher
// Find companies affected by recent events
MATCH (c:Company)<-[r:AFFECTS]-(e:Event)
WHERE e.date > date('2025-01-01')
RETURN c.id, e.type, r.impact_type, r.magnitude
```

## Performance Considerations

- **Batch Processing**: Optimized for large-scale data ingestion
- **Memory Management**: Efficient handling of large documents
- **Queue Management**: Asynchronous processing prevents bottlenecks
- **Database Optimization**: Indexed queries and batch operations
- **Error Recovery**: Robust handling of processing failures

## Future Enhancements

- NEO4J optimization with smaller schema and better indexes. 

