TRIPLET_EXTRACTION_PROMPT = """
# Financial Knowledge Graph Extraction

You'll extract entities and relationships from financial text and convert them into a structured JSON format.

## Your Task
1. Read the financial text provided
2. Identify relevant financial entities
3. Determine relationships between these entities
4. Output a properly structured JSON with both nodes and relationships

## Entity ID Format
- Companies: Use name ("Apple", "Microsoft")
- People: Use FirstName-LastName ("<PERSON><PERSON><PERSON>")
- Events: Use descriptive name with related entity ("Apple-Earnings-Report", "Fed-Rate-Hike")
- Financial metrics: Use descriptive format ("Revenue-2023Q1")

## Entity Types
- Company: Businesses, banks, financial institutions
- Person: Executives, investors, economists
- Event: Stock movements, product launches, IPOs, mergers
- FinancialInstrument: Stocks, bonds, currencies, crypto
- Product: Physical products, services, financial products
- Metric: Financial values, ratios, numbers
- Location: Countries, cities, markets
- Date: Fiscal periods, quarters, specific dates
- Year: Annual periods, fiscal years
- Regulation: Laws, compliance requirements
- Sector: Industry categories
- Index: Market indexes (S&P 500, NASDAQ, etc.)
- Market: Trading venues, market segments
- Currency: Monetary units, exchange media
- Organization: Non-corporate entities, institutions
- Technology: Technical innovations, platforms
- Industry: Broader industrial classifications

## Relationship Types (UPPERCASE):
- AFFECTS: Shows impact on entity
- LEADS: Person leads organization
- COMPETES_WITH: Competition relationship
- OWNS: Ownership relationship
- PRODUCES: Creates or manufactures
- INVESTED_IN: Investment relationship
- LOCATED_IN: Location relationship
- PARTNERS_WITH: Partnership or alliance
- ACQUIRED: Acquisition relationship
- REGULATED_BY: Regulatory oversight
- REPORTED: Financial reporting
- TRADED_IN: Trading relationship
- LISTED_ON: Listing relationship
- OPERATES_IN: Operational presence
- PART_OF: Component relationship
- CAUSES: Causal relationship
- DEVELOPED: Development relationship
- USES: Usage relationship
- LAUNCHED: Product/service launch
- APPOINTED: Executive appointment
- COMPARED_TO: Comparative relationship
- INFLUENCED_BY: Influence relationship

## Important Properties
For entities, include:
- Companies: sector, headquarters, market_cap
- Events: type, magnitude, impact
- People: role, organization

For relationships, include:
- date: YYYY-MM-DD format when known
- impact_type: Positive/Negative/Neutral
- magnitude: With specific affected metric

## Example 1
Input: "Intel shares surged 13% on Thursday after appointing Lip-Bu Tan as CEO, marking its largest single-day gain since 2020."

Output:
```json
{
  "nodes": [
    {
      "id": "Intel",
      "type": "Company",
      "properties": {
        "sector": "Semiconductors"
      }
    },
    {
      "id": "Intel-Stock-Surge",
      "type": "Event",
      "properties": {
        "type": "Stock Surge",
        "magnitude": "13%",
        "description": "largest single-day gain since 2020"
      }
    },
    {
      "id": "Lip-Bu-Tan",
      "type": "Person",
      "properties": {
        "role": "CEO"
      }
    },
    {
      "id": "Intel-CEO-Appointment",
      "type": "Event",
      "properties": {
        "type": "CEO Appointment"
      }
    }
  ],
  "relationships": [
    {
      "source": "Intel-Stock-Surge",
      "target": "Intel",
      "type": "AFFECTS",
      "properties": {
        "impact_type": "Positive",
        "magnitude": "13%"
      }
    },
    {
      "source": "Lip-Bu-Tan",
      "target": "Intel",
      "type": "LEADS",
      "properties": {
        "role": "CEO",
        "start_date": "2025-03-13"
      }
    },
    {
      "source": "Intel-CEO-Appointment",
      "target": "Intel-Stock-Surge",
      "type": "CAUSES",
      "properties": {
        "date": "2025-03-19",
        "confidence": 0.9
      }
    }
  ]
}
```

## Example 2
Input: "Adobe announced expanding its workflow integrations, deepening relationships with banking clients"

Output:
```json
{
  "nodes": [
    {
      "id": "Adobe", 
      "type": "Company"
    },
    {
      "id": "Workflow-Integration", 
      "type": "Product", 
      "properties": {
        "type": "Software Service"
      }
    },
    {
      "id": "Banking-Sector", 
      "type": "Sector", 
      "properties": {
        "name": "Banking"
      }
    }
  ],
  "relationships": [
    {
      "source": "Adobe", 
      "target": "Workflow-Integration", 
      "type": "PRODUCES",
      "properties": {
        "date": "2025-03-19"
      }
    },
    {
      "source": "Adobe", 
      "target": "Banking-Sector", 
      "type": "PARTNERS_WITH",
      "properties": {
        "date": "2025-03-19"
      }
    }
  ]
}
```

## Rules
1. Only include facts explicitly stated in the text
2. Do not include trivial relationships (e.g., "Apple is_a Company")
3. Make sure entity IDs follow the format guidelines
4. Include both nodes and relationships in your output
5. Format output as valid JSON with "nodes" and "relationships" arrays

Now, extract entities and relationships from this financial text:

{input}
""".replace('{','{{').replace('}','}}').replace('{{input}}','{input}')
