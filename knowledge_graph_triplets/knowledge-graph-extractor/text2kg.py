import ast, hashlib, traceback, csv, faiss, base64, numpy as np, time, json, regex as re, os, lmdb, msgpack, base64, requests, logging, scipy.spatial.distance as distance
import matplotlib.pyplot as plt, lz4.frame, pandas as pd, pickle, spacy, nltk, string, ahocorasick, functools, tldextract, json, pickle, glob, ast
from langdetect import detect
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from scipy.cluster.hierarchy import fcluster, linkage
from scipy.spatial.distance import pdist, squareform
from functools import wraps
from dataclasses import dataclass
from elasticsearch import Elasticsearch
import asyncio, json
from pybloom_live import ScalableBloomFilter
from urllib.parse import urlparse
from opensearchpy import OpenSearch, RequestsHttpConnection
from requests_aws4auth import AWS4Auth
from collections import Counter, defaultdict, deque, OrderedDict
from sklearn.feature_extraction.text import CountVectorizer
from itertools import combinations, islice
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score
from sklearn.feature_extraction.text import TfidfVectorizer
from textblob import TextBlob
from datetime import datetime, timedelta, date
from typing import Dict, Set, List, Tuple, Optional, Any, Union
from pathlib import Path
from scipy.stats import mode
from scipy.spatial.distance import cdist
from scipy.interpolate import interp1d
from tqdm import tqdm
import msgpack_numpy as m
m.patch()
nltk.download('stopwords')
nltk.download('punkt')


# many functions now obsolete as i again process clustered results, not raw data!

#with LN redirect from moreover
IGNORE_DOMAIN = {
    'reporter.am',
    'etfdailynews.com',
    'theenterpriseleader.com',
    'marketbeat.com',
    'americanbankingnews.com',
    'themarketsdaily.com',
    'thelincolnianonline.com',
    'modernreaders.com',
    'tickerreport.com',
    'thestockobserver.com',
    'com-unik.info',
    'zolmax.com', 
    'transcriptdaily.com',
    'defenseworld.net'
    }

def get_domain(x):
    try:return tldextract.extract(x.strip()).registered_domain
    except:return ""


VECTORS_SEARCH_ENDPOINT = "https://**************:8000/search"
ARTICLE_SEARCH_ENDPOINT = "https://**************:8000/article"
LOCAL_VECTOR_STORE_API_KEY = "myveryscretnotsolongcode"
LOCAL_KG_API_KEY = "myveryscretnotsolongcode2"
KG_ENDPOINT = "https://**************:8642/"


def timer_single(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Function '{func.__name__}' executed in {execution_time:.6f} seconds")
        return result
    return wrapper


def timer(func):
    execution_times = deque(maxlen=1000)  # Store last 1000 execution times
    call_count = 0
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        nonlocal call_count
        
        # Time the function execution
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        # Calculate execution time
        execution_time = end_time - start_time
        execution_times.append(execution_time)
        
        # Increment call count
        call_count += 1
        
        # Print stats every 1000 executions
        if call_count % 1000 == 0:
            avg_time = sum(execution_times) / len(execution_times)
            print(f"Function '{func.__name__}' statistics after {call_count} calls:")
            print(f"  Last execution: {execution_time:.6f} seconds")
            print(f"  Average execution (last {len(execution_times)}): {avg_time:.6f} seconds")
        
        return result
    
    return wrapper

def lru_cache(maxsize=128):
    def decorator(func):
        # Use OrderedDict to maintain insertion order (oldest first)
        func.cache = OrderedDict()
        func.maxsize = maxsize
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create a hashable key from args and kwargs
            key = args
            if kwargs:
                key = (*args, frozenset(kwargs.items()))
                
            try:
                # If key exists, move it to the end (most recently used)
                value = func.cache.pop(key)
                func.cache[key] = value
                return value
            except KeyError:
                # Key doesn't exist, compute the result
                result = func(*args, **kwargs)
                func.cache[key] = result
                
                # If cache is full, remove the oldest item (first in the OrderedDict)
                if func.maxsize > 0 and len(func.cache) > func.maxsize:
                    func.cache.popitem(last=False)
                    
                return result
                
        # Add utility methods similar to functools.lru_cache
        def cache_info():
            return {
                'hits': 0,  # Not tracked in this implementation
                'misses': 0,  # Not tracked in this implementation
                'maxsize': func.maxsize,
                'currsize': len(func.cache)
            }
            
        def cache_clear():
            func.cache.clear()
            
        wrapper.cache_info = cache_info
        wrapper.cache_clear = cache_clear
        
        return wrapper
    return decorator

def save_cache(func, filename):
    with open(filename, 'wb') as f:
        pickle.dump(func.cache, f)

def load_cache(func, filename):
    with open(filename, 'rb') as f:
        func.cache = pickle.load(f)

def numpy_to_json_serializable(data_dict):
    json_dict = {}
    for key, value in data_dict.items():
        if isinstance(value, np.ndarray):
            json_dict[key] = {
                'type': 'ndarray',
                'dtype': str(value.dtype),
                'shape': value.shape,
                'data': value.tolist()
            }
        elif isinstance(value, list) and value and isinstance(value[0], np.ndarray):
            json_dict[key] = {
                'type': 'ndarray_list',
                'data': [{
                    'dtype': str(arr.dtype),
                    'shape': arr.shape,
                    'data': arr.tolist()
                } for arr in value]
            }
        elif isinstance(value, (date, datetime)):
            # Convert date/datetime to ISO format string
            json_dict[key] = {
                'type': 'date',
                'data': value.isoformat()
            }
        else:
            try:
                json.dumps(value)
                json_dict[key] = value
            except (TypeError, OverflowError):
                json_dict[key] = {
                    'type': 'pickle',
                    'data': base64.b64encode(pickle.dumps(value)).decode('utf-8')
                }
    return json_dict

def json_serializable_to_numpy(json_dict):
    data_dict = {}
    for key, value in json_dict.items():
        if isinstance(value, dict) and 'type' in value:
            if value['type'] == 'ndarray':
                data_dict[key] = np.array(value['data'], dtype=np.dtype(value['dtype']))
            elif value['type'] == 'ndarray_list':
                data_dict[key] = [
                    np.array(item['data'], dtype=np.dtype(item['dtype']))
                    for item in value['data']
                ]
            elif value['type'] == 'pickle':
                data_dict[key] = pickle.loads(base64.b64decode(value['data']))
            elif value['type'] == 'date':
                # Check if the date has time components (datetime) or just date
                if 'T' in value['data']:
                    data_dict[key] = datetime.fromisoformat(value['data'])
                else:
                    data_dict[key] = date.fromisoformat(value['data'])
            else:
                data_dict[key] = value
        else:
            data_dict[key] = value
    return data_dict

def find_matches(full_text, start, chars_between, end):
    """Find all matches and their character count differences"""
    pattern = f"{re.escape(start)}(.+?){re.escape(end)}"
    matches = []
    
    for match in re.finditer(pattern, full_text, re.DOTALL):
        middle = match.group(1)
        diff = abs(len(middle) - chars_between)
        matches.append((diff, match.group(0)))
        
    if not matches:
        return ''
            
    min_diff = min(match[0] for match in matches)
    return [m for m in matches if m[0] == min_diff][0][1]

def connect_elasticsearch_aws():
    _es = None
    _es = Elasticsearch(
        ['https://search-equbot-ln-es-v2v7spplqpcsjwxg5hg3qmhajq.us-east-1.es.amazonaws.com'],
        http_auth=('equbot_es', 'Equb0t!23'),
        scheme="https",
        port=443,timeout=30, max_retries=10, retry_on_timeout=True
    )
    if _es.ping():pass#print('Yay Connect')
    else:print('Awww it could not connect!')
    return _es

def connect_openSearch():
   aws_access_key  = '********************'
   aws_secret_key = 'sjzRbkq5UFYz3Fku3NgO2Qr0r0bUvKhqYvm/jiDd'
   region='us-east-1'
   service='es'
   hosts = ['https://search-unstructured-es-ml3vahhmgvadk5gjdku5ditlzu.us-east-1.es.amazonaws.com:443']
   auth = AWS4Auth(aws_access_key, aws_secret_key, region, service)
   port = 443
   client = OpenSearch(
       hosts=hosts,
       port=port,
       http_auth=auth,
       connection_class=RequestsHttpConnection,
       timeout=50, 
       max_retries=5, 
       retry_on_timeout=True
    )
   return client

printed = False

#@lru_cache(maxsize=524288)
@timer
def fetch_documents(art_ids, index='ln_index'):
    global printed

    query = {
        "query": {
            "terms": {
                "id": art_ids
            }
        }
    }

    results = client_es.search(index=index, body=json.dumps(query))
    year = 2025
    index2=f"quantexa_data_{year}"
    results2 = client_os.search(index=index2, body=json.dumps(query))
    documents = {}
    domains = {}
    for hit in results['hits']['hits']+results2['hits']['hits']:
        source = hit['_source']
        doc_id = source['id']
        domain = get_domain(source['url'])
        if domain == 'moreover.com':
            if 'source' in source and 'homeUrl' in source['source']:
                domain = get_domain(source['source']['homeUrl'])
            elif not printed:
                printed = True
                print(source['source'])
        if domain in IGNORE_DOMAIN:
            print('skipping', domain)
            continue
        domains[doc_id] = domain
        if 'content' in source:
            content = f"{source['content']}\n\n{source['title']}"
        else:
            content = source['title']
        documents[doc_id] = content
    return documents, domains

#try:
#    load_cache(fetch_documents, 'fetch_documents_cache.pkl')
#    print('loaded cache')
#except:pass

client_es=connect_elasticsearch_aws()
client_os=connect_openSearch()

def decompress_ranges(ranges):
    result = []
    for start, count in ranges:
        result.extend(range(start, start + count))
    return result

def get_week_key(date_obj: date) -> str:
    return f"{date_obj.isocalendar()[0]}-{date_obj.isocalendar()[1]}"

def get_iso_date_str(date_obj: date) -> str:
    """Convert date object to ISO format string for comparison with stored date keys"""
    return date_obj.isoformat()

def parse_week_from_path(path: str) -> tuple[int, int]:
    match = re.search(r'week_(\d+)-(\d+)', str(path))
    if not match:
        raise ValueError(f"Invalid path format: {path}")
    return int(match.group(1)), int(match.group(2))

def get_weekday(date_obj: date) -> int:
    return date_obj.weekday()

def get_date_range_for_week(year: int, week: int) -> tuple[date, date]:
    first_day = datetime.strptime(f'{year}-W{week}-1', '%Y-W%W-%w').date()
    last_day = first_day + timedelta(days=6)

    today = date.today()
    today_week = today.isocalendar()[1]
    today_year = today.isocalendar()[0]
    
    if year == today_year and week == today_week:
        return first_day, last_day, today
    else:
        return first_day, last_day, None

def find_large_vector_files(base_path: str) -> list[Path]:
    min_size_bytes = 0.3 * 1024 * 1024 * 1024
    vector_files = []
    
    for path in Path(base_path).rglob('vectors.faiss'):
        if path.stat().st_size > min_size_bytes:
            vector_files.append(path)
    
    def numeric_sort_key(path):
        numbers = re.findall(r'\d+', str(path))
        return [int(num) for num in numbers]
    
    return sorted(vector_files, key=numeric_sort_key, reverse=True)

class DataFetcher:
    def __init__(self, path: str):
        self.path = Path(path)
        self._env = None
        self._dbs = None
        self._initialize_db()

    def _initialize_db(self):
        """Initialize the LMDB environment and databases."""
        self._env = lmdb.open(
            str(self.path / "data.lmdb"),
            map_size=0,  # Set to 0 to avoid any write operations.
            subdir=True,
            readonly=True,  # Open in read-only mode
            map_async=False,  # Disable async mode since it's read-only
            writemap=False,   # Disable writemap in read-only mode
            max_dbs=4         # Only necessary databases will be considered
        )
        self._dbs = {
            'metadata': self._env.open_db(b'metadata'),
            'companies': self._env.open_db(b'companies'),
            'dates': self._env.open_db(b'dates'),
            'articles': self._env.open_db(b'articles')
        }

    @timer_single
    def fetch_date_keys_for_given_path(self, end_date: date) -> Dict[str, List[int]]:
        """Get date keys and their associated values in a given date range."""
        date_keys_and_values = {}

        t0 = time.time()
        with self._env.begin() as txn:
            cursor = txn.cursor(self._dbs['dates'])
            
            end_str = end_date.isoformat().encode()
            
            found = cursor.set_range(end_str)
            
            while found:
                key, value = cursor.item()
                if key > end_str:
                    break

                # Decode the values (assuming it's msgpack compressed)
                date_keys = decompress_ranges(msgpack.unpackb(value, raw=False))
                date_keys_and_values[key.decode()] = date_keys  # Store keys with their corresponding list of values

                found = cursor.next()

        print(f"Fetched date keys and values in {time.time() - t0:.4f} seconds")
        return date_keys_and_values


    @timer_single
    def fetch_metas_for_given_keys(self, keys: List[int]) -> Dict[str, List[int]]:
        metas = {}
        keys = set(keys)  # Convert to set for O(1) lookup
        
        with self._env.begin() as txn:
            cursor = txn.cursor(self._dbs['metadata'])  # Use metadata db, not dates
            
            # Iterate through all entries
            found = cursor.first()
            while found:
                key, value = cursor.item()
                key_int = int(key.decode())  # Convert bytes key to int
                
                # Check if this key is in our target set
                if key_int in keys:
                    data = msgpack.unpackb(value, raw=False)
                    metas[key_int] = data
                    
                    # Optional: break if we found all keys we're looking for
                    if len(metas) == len(keys):
                        break
                
                found = cursor.next()
        
        return metas

@timer_single
def load_nonvectors_for_date_range(target_date: date, vector_files: list[Path]):
    target_week_key = get_week_key(target_date)
    #target_day = get_weekday(target_date)
    target_date_str = get_iso_date_str(target_date)
    
    all_relevant_ids: Set[int] = set()

    new2old = {}
    texts = {}
    vid2id = {}
    all_filtered_ids = []
    vid2domains = {}
    art_ids = {}

    for file in vector_files:
        year, week = parse_week_from_path(file)
        week_key = f"{year}-{week}"
        if week_key != target_week_key:
            continue
            
        #vector_path = str(file.parent.parent)
        vector_path = str(file.parent).replace('/vectors/', 'vector_data/')
        fetcher = DataFetcher(vector_path)
        
        # Get date keys and values for the current week
        date_keys_and_values = fetcher.fetch_date_keys_for_given_path(target_date)
        
        # Print debug info and collect relevant IDs
        for date_key, vals in date_keys_and_values.items():

            """ date_key format:
            start_str = start_date.isoformat().encode()
            end_str = end_date.isoformat().encode()
            """

            if date_key == target_date_str:
                if len(vals) < 50000:
                    print(f"Skipping day {date_key}, not enough values: {len(vals)}")
                    return None, None, None, None
                print(f"Day {date_key}, First values: {vals[:33]}")
                all_relevant_ids.update(map(int, vals))
        
        # Load FAISS index
        index = faiss.read_index_binary(str(file))
        
        # Get stored IDs and create a mask for relevant ones
        stored_ids = faiss.vector_to_array(index.id_map)

        #FIXME: remova after testing
        #all_relevant_ids = set(list(all_relevant_ids)[:10000])
        
        #print('MM S', min(stored_ids), max(stored_ids))
        #print('MM A', min(all_relevant_ids), max(all_relevant_ids))
        relevant_mask = np.isin(stored_ids, list(all_relevant_ids))
        relevant_indices = np.where(relevant_mask)[0]
        
        if len(relevant_indices) == 0:
            continue
        
        # Filter and load only relevant vectors
        filtered_ids = stored_ids[relevant_mask]
        #print('MM F', min(filtered_ids), max(filtered_ids))
        print(f"Total relevant IDs found: {len(all_relevant_ids)}")
        print(f"Total filtered IDs found: {len(filtered_ids)}")
        metas = fetcher.fetch_metas_for_given_keys(filtered_ids)
        for vid, metadata in metas.items():
            vid = int(vid)
            if metadata['text'] and metadata['lc'] in {'en', 'eng'}:
                try:
                    start, chars_between, end = metadata['text'].split('|')
                    if chars_between:
                        try:chars_between_int = int(chars_between)
                        except:
                            print('fail', chars_between)
                            continue
                        art_ids[(str(vid), start, chars_between, end)] = metadata["id"]
                except:
                    print('fail2', metadata['text'])
                    continue

        grouped_by_doc_id = {}
        for key, doc_id in art_ids.items():
            if doc_id not in grouped_by_doc_id:
                grouped_by_doc_id[doc_id] = []
            grouped_by_doc_id[doc_id].append(key)

        unique_doc_ids = list(grouped_by_doc_id.keys())
        batch_size = 10
        filtered_ids_ = set()
        for i in range(0, len(unique_doc_ids), batch_size):
            batch_doc_ids = unique_doc_ids[i:i+batch_size]
            docs, domains = fetch_documents(batch_doc_ids)
            
            for doc_id in batch_doc_ids:
                if doc_id in docs:
                    # Process all keys associated with this document ID
                    for vid, start, chars_between, end in grouped_by_doc_id[doc_id]:
                        text = find_matches(docs[doc_id], start, chars_between_int, end)
                        texts[vid] = text
                        vid2id[vid] = doc_id
                        vid2domains[vid] = domains[doc_id]
                        filtered_ids_.add(vid)
                    print(f"doc id found {doc_id}")
                else:
                    print(f"! doc id missing {doc_id}")

        #save_cache(fetch_documents, 'fetch_documents_cache.pkl')
        filtered_ids = list(filtered_ids_)
        relevant_mask = np.isin(stored_ids, filtered_ids)
        relevant_indices = np.where(relevant_mask)[0]
        all_filtered_ids.extend(filtered_ids)
        
        for new_idx, orig_idx in enumerate(relevant_indices):
            orig_idx = int(stored_ids[orig_idx])
            new2old[new_idx] = orig_idx
        if new2old:
            print('N2O', len(new2old), new_idx, orig_idx)
            #if str(orig_idx) in texts:
            #    print(texts[str(orig_idx)])
            #else:
            #    print('wtf', str(sorted(texts.keys()))[:111])
            #    print('wtf', str(sorted(texts.keys(), reverse=True))[:111])
        else:
            print('N2O empty')
            
    return texts, vid2id, all_filtered_ids, vid2domains

def load_spacy_model():
    """Load single multilingual spacy model"""
    return spacy.load('xx_ent_wiki_sm')

@timer_single
def preprocess_texts(texts):
    docs = []
    for text in texts:
        if text is None:
            text = ''
        #text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()        
        docs.append(text)
    return docs

nlp = load_spacy_model()

def is_latin_alphanumeric(text):
    pattern = r'^[a-zA-Z0-9.&\-]+$'
    return bool(re.match(pattern, text))
    #return text.replace(" ", "").replace("&","").isalnum() and text.isascii()


def ldect(text):
    try:
        language = detect(text)
        #print(f"Detected language: {language}")
    except Exception as e:
        #print(f"Error detecting language: {e}")
        language = "en" 
    return language

def company_map_get(item):
    i = 0
    while True:
        i+=1
        if item not in COMPANY_MAP or COMPANY_MAP[item] == item:
            return item
        item = COMPANY_MAP[item]
        if i>10:break #something wrong.. there is a loop
    return item
               
with open('company_map.pkl', 'rb') as file:
    COMPANY_MAP = pickle.load(file)
    
company_set = set(COMPANY_MAP.values())

with open('industry-macro-etf-holdings.json', 'r') as f:
    imeh = json.load(f)

A = ahocorasick.Automaton()
for key, value in COMPANY_MAP.items():
    A.add_word(key, (len(key), company_map_get(key)))  # Store length & standard name
A.make_automaton()

B = ahocorasick.Automaton()
for tab in imeh:
    for key, value in imeh[tab].items():
        B.add_word(key.lower(), (len(key), value))
        company_set.add(value)
B.make_automaton()

def check_last_non_empty_line(filename, buffer_size=128):
    with open(filename, 'rb') as file:
        file.seek(0, 2)
        file_size = file.tell()
        chunk_size = min(buffer_size, file_size)
        file.seek(file_size - chunk_size, 0)
        chunk = file.read(chunk_size).decode('utf-8')
        lines = [line.strip() for line in chunk.split('\n') if line.strip()]
        if not lines:
            return False
        return lines[-1] == 'FINISHED'
    
def load_date_results(paragraphs_dir: Path, target_date: date, base: str) -> Optional[Dict]:
    """Load clustering results for a specific date if they exist."""
    file_path = paragraphs_dir / f"{base}_{target_date.strftime('%Y%m%d')}.csv"
    try:
        if file_path.exists():
            if check_last_non_empty_line(file_path):
                return None
            df = pd.read_csv(file_path, encoding='utf-8')
            return df
        return None
    except Exception as e:
        print(f"Error loading results for {target_date}: {e}")
        return None

def save_aligned_to_csv(data, output_path):
    headers = ['vid', 'article_ids', 'paragraph_domains', 'company_paragraphs', 'processed']
    with open(output_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, quoting=csv.QUOTE_MINIMAL)
        writer.writerow(headers)
        for vid in data['vid2id']:
            article_id = data['vid2id'][vid]
            domain = data['vid2domains'].get(vid, '')
            text = re.sub(r'\s+', ' ', data['texts'].get(vid, '')).strip()
            processed = 0
            writer.writerow([vid, article_id, domain, text, processed])

processed_paragraphs = ScalableBloomFilter(mode=ScalableBloomFilter.SMALL_SET_GROWTH)#OrderedDict()


def hash_text(text):
    """Create a hash for a text string to track processed paragraphs."""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def parse_json_list(json_str):
    try:
        return ast.literal_eval(json_str)
    except (TypeError, json.JSONDecodeError):
        # Return empty list or original value if parsing fails
        return [] if json_str is None else json_str

def encode_list_to_json(list_value):
    if isinstance(list_value, list):
        return str(list_value)
    return list_value

def process_unprocessed_items(paragraphs_dir, target_date, base='texts'):
    #global processed_paragraphs
    """
    Processes items with processed=0 by submitting them to the API
    when the queue size is < 100
    """
    df = load_date_results(paragraphs_dir, target_date, base)

    if df is None or len(df) < 2:return

    json_columns = ['article_ids', 'paragraph_domains', 'company_paragraphs']
    for col in json_columns:
        df[col] = df[col].apply(parse_json_list)

    paragraphs_path = paragraphs_dir / f"{base}_{target_date.strftime('%Y%m%d')}.csv"
    # Create a copy to avoid modifying the original during iteration
    unprocessed = df[df['processed'] == 0].copy()

    if len(unprocessed) == 0:
        print("No unprocessed items found.")
        return
    
    print(f"Found {len(unprocessed)} unprocessed items to process.")
    
    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}',
        "Content-Type": "application/json"
    }
    
    for idx, row in unprocessed.iterrows():

        if 'paragraph_domains' in df.columns:
            original_rid = row['rid'] 
            #print(row['paragraph_domains'])
            if isinstance(row['paragraph_domains'], list) and len(row['paragraph_domains']) > 0:
                # Create a mini-df with just this row and explode it
                single_row_df = pd.DataFrame([row], columns=unprocessed.columns)
                exploded_row = single_row_df.explode(['article_ids', 'paragraph_domains', 'company_paragraphs'])
                
                #print('er', len(exploded_row))
                # Process each item in the exploded row
                for _, exploded_item in exploded_row.iterrows():
                    #print(original_rid, exploded_item['article_ids'])
                    while True:
                        try:
                            status_response = requests.get(f"{KG_ENDPOINT}/queue/status", headers=headers, verify=False)
                            status = status_response.json()
                            queue_size = status.get("queue_size", 0)

                            if queue_size < 100:

                                extract_data = {
                                    "article_id": str(exploded_item['article_ids']),
                                    "domain": exploded_item['paragraph_domains'],
                                    "text": exploded_item['company_paragraphs'],
                                    "date": target_date.strftime('%Y-%m-%d')
                                }

                                if processed_paragraphs.__contains__(hash_text(exploded_item['company_paragraphs'])):
                                    #print('already seen so skipping')
                                    break
                                
                                # Submit to API
                                response = requests.post(f"{KG_ENDPOINT}/submit", json=extract_data, headers=headers, verify=False)
                                
                                if response.status_code == 200:
                                    print(f"Successfully processed item {exploded_item['article_ids']}")
                                    # No need to update the exploded item - we'll update the original row directly
                            
                                else:
                                    print(f"Failed to process item {row['article_ids']}: {response.text}")
                                processed_paragraphs.add([hash_text(exploded_item['company_paragraphs'])])# = 1
                                break

                            else:
                                #print('queue full, wait')
                                time.sleep(5)
                                
                        except Exception as e:
                            print(f"Error processing item {row['article_ids']}: {str(e)}")
                            traceback_str = traceback.format_exc()
                            print(traceback_str)
                            time.sleep(5)
                            break
                
                #print('done orig row')
                # After processing all exploded items, mark the original row as processed
                df.loc[df['rid'] == original_rid, 'processed'] = 1
                
                # Save after each row is processed
                if (df['processed'] == 1).sum()%10==0:
                    df.to_csv(paragraphs_path, index=False, encoding='utf-8')
            else:
                print('wtf is list', type(row['paragraph_domains']), row['paragraph_domains'])
    
    #print('done rows')
    for col in json_columns:
        df[col] = df[col].apply(encode_list_to_json)

    df.to_csv(paragraphs_path, index=False, encoding='utf-8')      
    #if len(processed_paragraphs) > 15000000:
    #    processed_paragraphs = OrderedDict(islice(processed_paragraphs.items(), 10000000, None))
        
        
    if len(df) > 100000 and len(unprocessed) < len(df)//100:
        with open(paragraphs_path, 'a', encoding='utf-8') as w:
            w.write("\nFINISHED\n")
    
    return

@timer_single
def process_vector_files():
    # Create output directory
    paragraphs_dir = Path('paragraphs')
    paragraphs_dir.mkdir(exist_ok=True)
        
    # Find large vector files
    vector_files = find_large_vector_files('/vectors')
    if not vector_files:
        print("No vector files larger than 2GB found")
        return
    
    # Get all possible dates from vector files
    all_dates = set()
    for file in vector_files:
        year, week = parse_week_from_path(file)
        start_date, end_date, today_date = get_date_range_for_week(year, week)
        current_date = start_date
        while current_date <= end_date and (today_date is None or current_date < today_date): # do not process today as articles are still being processed!
            all_dates.add(current_date)
            current_date += timedelta(days=1)

    # Process each date
    for target_date in sorted(all_dates, reverse=True):
        paragraphs_path = paragraphs_dir / f"texts_{target_date.strftime('%Y%m%d')}.csv"
        if not paragraphs_path.exists():
            print(f"Processing {target_date}")
            
            texts, vid2id, filtered_ids, vid2domains = load_nonvectors_for_date_range(
                target_date, vector_files
            )
            if vid2id is None:
                print('empty stuff')
                continue

            #texts[vid] = text
            #vid2id[vid] = doc_id
            #vid2domains[vid] = domains[doc_id]
            #filtered_ids_.add(vid)

            stored_ids = filtered_ids#list(new2old.values())
            
            current_results = {
                #'date': target_date,
                #'stored_ids': stored_ids,
                'vid2id': vid2id,
                'vid2domains': vid2domains,
                'texts': texts # use these for KG, each separately, but cluster groups for noise removal stats!
            }
            save_aligned_to_csv(current_results, paragraphs_path)
        else:
            print(f"Doing {target_date}, output already exists")

        print(f"{str(target_date)}")
        process_unprocessed_items(paragraphs_dir, target_date, 'texts')

#while True:
#    process_vector_files()
#    time.sleep(10)

def process_clustered_files(base='companies'):
    # Create output directory
    paragraphs_dir = Path('clusters')

    all_files = sorted(
        paragraphs_dir.glob(f"{base}_*.csv"), 
        reverse=True
    )

    for paragraphs_path in all_files:
        date_str = str(paragraphs_path).split('/')[-1].split('_')[1].split('.')[0]            
        target_date = datetime.strptime(date_str, "%Y%m%d")
        print(paragraphs_path)
        process_unprocessed_items(paragraphs_dir, target_date, base)

while True:
    process_clustered_files()
    time.sleep(10)


