from typing import Any
from utils import *
from data_pipeline import *
from custom_dataset_helper import *
from model_properties import *

from transformers import EarlyStopping<PERSON><PERSON><PERSON>, Trainer, TrainingArguments # type: ignore
from tsfm_public.toolkit.callbacks import TrackingCallback
from tsfm_public.toolkit.time_series_preprocessor import TimeSeriesPreprocessor


output_directory = 'ttm_finetuned_models_do_you_bleed/'

def get_training_arguments():
    training_args = TrainingArguments(
        output_dir=output_directory,
        overwrite_output_dir=True,
        learning_rate=float(learning_rate),
        num_train_epochs=int(epochs),
        do_eval=True,
        eval_strategy='no',
        per_device_eval_batch_size=int(batch_size),
        per_device_train_batch_size=int(batch_size),
        dataloader_num_workers=8,
        report_to="none",
        save_strategy='epoch',
        save_on_each_node=True,
        logging_strategy='epoch',
        save_total_limit=2,
        logging_dir=os.path.join(output_directory, 'logs'),
        load_best_model_at_end=False,
        metric_for_best_model='eval_loss',
        greater_is_better=False,
    )
    return training_args


def initialize_timeseries_preprocessor(schedular):
    timestamp_col = 'date'
    id_cols = []
    target_cols = [f'actual_{schedular.lower()}_return']
    column_specifiers = {
        'timestamp_column': timestamp_col,
        'id_columns': id_cols,
        'target_columns': target_cols,
        'control_columns': []
    }

    ts_preprocessor = TimeSeriesPreprocessor(
        **column_specifiers,
        context_length = int(context_length),
        prediction_length = 1,
        scaling = False,
        encode_categorical = False,
        scaler_type = 'standard'
    )

    return ts_preprocessor

def initialize_trainer(model: Any, schedular) -> Trainer:
    
    training_args = get_training_arguments()

    early_stopping_callback = EarlyStoppingCallback(
        early_stopping_patience=int(early_stopping_patience),
        early_stopping_threshold=float(early_stopping_threshold))
    tracking_callback=TrackingCallback()

    custom_trainer=Trainer(
        model,
        args=training_args,
        callbacks=[early_stopping_callback, tracking_callback])
    
    return custom_trainer

def get_input_data(isin, data, schedular):
    
    ts_preprocessor = initialize_timeseries_preprocessor(schedular)
    dys = days[schedular.lower()]

    if not ts_preprocessor.context_length:
        logging.error("TimeSeriesPreprocessor must be instantiated with non-null context_length")
        raise ValueError("TimeSeriesPreprocessor must be instantiated with non-null context_length")
    if not ts_preprocessor.prediction_length:
        logging.error("TimeSeriesPreprocessor must be instantiated with non-null prediction_length")
        raise ValueError("TimeSeriesPreprocessor must be instantiated with non-null prediction_length")

    column_specifiers = {
        "id_columns": ts_preprocessor.id_columns,
        "timestamp_column": ts_preprocessor.timestamp_column,
        "target_columns": ts_preprocessor.target_columns,
        "observable_columns": ts_preprocessor.observable_columns,
        "control_columns": ts_preprocessor.control_columns,
        "conditional_columns": ts_preprocessor.conditional_columns,
        "static_categorical_columns": ts_preprocessor.static_categorical_columns,
    }

    params = column_specifiers
    params = column_specifiers
    params["context_length"] = ts_preprocessor.context_length
    params["prediction_length"] = ts_preprocessor.prediction_length
    params["stride"] = 1
    
    CustomForecastDFDataset = dataset_class(dys-1)

    dataset = CustomForecastDFDataset(data, **params)
    return dataset