from .logger import *
import pandas as pd # type: ignore
from eq_common_utils.utils.opensearch_helper import OpenSearch # type: ignore
import json
import configparser

config=configparser.ConfigParser()
properties_path=os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'ttm.properties')
config.read(properties_path)

host = config.get(section='opensearch_prod', option='host')
port = config.get(section='opensearch_prod', option='port')
key_id = config.get(section='opensearch_prod', option='key_id')
secret = config.get(section='opensearch_prod', option='secret')
region = config.get(section='opensearch_prod', option='region')

def connect_openSearch():
    hosts = [f'https://{host}:{port}']
    
    client = OpenSearch(
        host=host,
        port=port,
        key_id=key_id,
        secret=secret,
        region = region
     )
    return client

def get_values_from_es(isin,date_needed,index,schedular):
    year = pd.to_datetime(date_needed).date().year
    try:
        df=get_es_data(isin, [year,year], f'{index}')
        df=df[df['schedular']==f'{schedular}']
        df=df[pd.to_datetime(df['date'])==pd.to_datetime(date_needed)]
        return df
    except:
        print("Data not there for ISIN",isin)
 
def get_es_data(isin, dates, index_prefix):
    try:
        data=[]
        for year in range(dates[0], dates[1] + 1):
            q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
            try:
                result = es.run_query(query=json.loads(q_total),index=f"{index_prefix}_{year}")
            except Exception as e:
                logging.error(f"error in running elasticsearch query: {e} for query: {q_total}")
                pass
            for rs in result['hits']['hits']:
                es_data=rs['_source']
                data.append(es_data)
        df=pd.DataFrame(data)
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        return df
    except Exception as e:
        logging.error(f'error in collecting data from elasticsearch: {e}')
        pass

es=connect_openSearch()