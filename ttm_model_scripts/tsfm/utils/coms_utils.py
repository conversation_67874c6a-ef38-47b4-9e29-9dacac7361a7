from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
import smtplib

def send_email(subject, body, to_emails):
    # Sender email address
    try:
        from_email = '<EMAIL>'  # Replace with your email
        app_password = 'ldni kmvg txji ejtb'

        # Set up the MIME object
        msg = MIMEMultipart()
        msg['From'] = from_email
        msg['To'] = ', '.join(to_emails)  # Join multiple email addresses with a comma
        msg['Subject'] = subject

        # Attach the email body
        msg.attach(MIMEText(body, 'plain'))

        # Establish a connection to the SMTP server
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            server.starttls()
            server.login(from_email, app_password)

            # Send the email
            server.sendmail(from_email, to_emails, msg.as_string())
    except:
        print('error in sending email! -> {traceback.format_exc()}')
        pass

def daily_mail(daily_run_params):
    subject = f'TTM Model Daily Run Predictions'
    body = f'''
    Hello team,
    This is to notify you the status of ttm-model-run for Date: {daily_run_params['date']} 

    Status for Daily-Predictions =>
    Geography: aieq, Schedular: daily

    Total Number of Isins: {daily_run_params['aieq_daily']['daily-predictions']['total_isins']}
    Number of Successful Runs: {daily_run_params['aieq_daily']['daily-predictions']['successful_runs']}
    Number of Failed Runs: {daily_run_params['aieq_daily']['daily-predictions']['failed_runs']}
    Isins for which daily-run failed: {daily_run_params['aieq_daily']['daily-predictions']['failed_isins']}

    Geography: aieq, Schedular: monthly

    Total Number of Isins: {daily_run_params['aieq_monthly']['daily-predictions']['total_isins']}
    Number of Successful Runs: {daily_run_params['aieq_monthly']['daily-predictions']['successful_runs']}
    Number of Failed Runs: {daily_run_params['aieq_monthly']['daily-predictions']['failed_runs']}
    Isins for which daily-run failed: {daily_run_params['aieq_monthly']['daily-predictions']['failed_isins']}

    Geography: aieq, Schedular: quarterly

    Total Number of Isins: {daily_run_params['aieq_quarterly']['daily-predictions']['total_isins']}
    Number of Successful Runs: {daily_run_params['aieq_quarterly']['daily-predictions']['successful_runs']}
    Number of Failed Runs: {daily_run_params['aieq_quarterly']['daily-predictions']['failed_runs']}
    Isins for which daily-run failed: {daily_run_params['aieq_quarterly']['daily-predictions']['failed_isins']}

    Geography: indeq, Schedular: quarterly

    Total Number of Isins: {daily_run_params['indeq_quarterly']['daily-predictions']['total_isins']}
    Number of Successful Runs: {daily_run_params['indeq_quarterly']['daily-predictions']['successful_runs']}
    Number of Failed Runs: {daily_run_params['indeq_quarterly']['daily-predictions']['failed_runs']}
    Isins for which daily-run failed: {daily_run_params['indeq_quarterly']['daily-predictions']['failed_isins']}

    Geography: indeq, Schedular: monthly

    Total Number of Isins: {daily_run_params['indeq_monthly']['daily-predictions']['total_isins']}
    Number of Successful Runs: {daily_run_params['indeq_monthly']['daily-predictions']['successful_runs']}
    Number of Failed Runs: {daily_run_params['indeq_monthly']['daily-predictions']['failed_runs']}
    Isins for which daily-run failed: {daily_run_params['indeq_monthly']['daily-predictions']['failed_isins']}

    Status for Daily-Metrics =>
    Geography: aieq, Schedular: daily

    Total Number of Isins: {daily_run_params['aieq_daily']['daily-metrics']['total_isins']}
    Number of Successful Runs: {daily_run_params['aieq_daily']['daily-metrics']['successful_runs']}
    Number of Failed Runs: {daily_run_params['aieq_daily']['daily-metrics']['failed_runs']}

    Geography: aieq, Schedular: monthly

    Total Number of Isins: {daily_run_params['aieq_monthly']['daily-metrics']['total_isins']}
    Number of Successful Runs: {daily_run_params['aieq_monthly']['daily-metrics']['successful_runs']}
    Number of Failed Runs: {daily_run_params['aieq_monthly']['daily-metrics']['failed_runs']}

    Geography: aieq, Schedular: quarterly

    Total Number of Isins: {daily_run_params['aieq_quarterly']['daily-metrics']['total_isins']}
    Number of Successful Runs: {daily_run_params['aieq_quarterly']['daily-metrics']['successful_runs']}
    Number of Failed Runs: {daily_run_params['aieq_quarterly']['daily-metrics']['failed_runs']}

    Geography: indeq, Schedular: quarterly

    Total Number of Isins: {daily_run_params['indeq_quarterly']['daily-metrics']['total_isins']}
    Number of Successful Runs: {daily_run_params['indeq_quarterly']['daily-metrics']['successful_runs']}
    Number of Failed Runs: {daily_run_params['indeq_quarterly']['daily-metrics']['failed_runs']}

    Geography: indeq, Schedular: monthly

    Total Number of Isins: {daily_run_params['indeq_monthly']['daily-metrics']['total_isins']}
    Number of Successful Runs: {daily_run_params['indeq_monthly']['daily-metrics']['successful_runs']}
    Number of Failed Runs: {daily_run_params['indeq_monthly']['daily-metrics']['failed_runs']}

    If you have any questions or need further information, please feel free to reach out.

    Regards and Thanks,
    Kayithi Naga Sai
    '''
    recipient_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    send_email(subject, body, recipient_emails)


def geo_schedular_mail(daily_run_params, geo, schedular):
    subject = f'TTM Model Run status - {geo}:{schedular}'
    body = f'''
    Hello team,
    This is to notify you the status of ttm-model-run for Date: {daily_run_params['date']} 

    Status for Daily-Predictions =>
    Geography: {geo}, Schedular: {schedular}

    Total Number of Isins: {daily_run_params[f'{geo}_{schedular}']['daily-predictions']['total_isins']}
    Number of Successful Runs: {daily_run_params[f'{geo}_{schedular}']['daily-predictions']['successful_runs']}
    Number of Failed Runs: {daily_run_params[f'{geo}_{schedular}']['daily-predictions']['failed_runs']}
    Number of isins with forward-filled predictions: {daily_run_params[f'{geo}_{schedular}']['daily-predictions']['forward-filled-isins']}
    Number of isins with 0 as prediction: {daily_run_params[f'{geo}_{schedular}']['daily-predictions']['predictions == 0']}
    

    Status for Daily-Metrics =>
    Geography: {geo}, Schedular: {schedular}

    Total Number of Isins: {daily_run_params[f'{geo}_{schedular}']['daily-metrics']['total_isins']}
    Number of Successful Runs: {daily_run_params[f'{geo}_{schedular}']['daily-metrics']['successful_runs']}
    Number of Failed Runs: {daily_run_params[f'{geo}_{schedular}']['daily-metrics']['failed_runs']}

    If you have any questions or need further information, please feel free to reach out.

    Regards and Thanks,
    Kayithi Naga Sai
    '''
    recipient_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    send_email(subject, body, recipient_emails)

def trigger_mail(geo, schedular):
    subject = f'TTM Model Run Triggered - {geo}:{schedular}'
    body = f'''
    Hello team,
    This is to notify you the status of ttm-model has been triggered for geo={geo}: schedular={schedular} 

    If you have any questions or need further information, please feel free to reach out.

    Regards and Thanks,
    Kayithi Naga Sai
    '''
    recipient_emails = ['<EMAIL>', '<EMAIL>']
    send_email(subject, body, recipient_emails)


def error_mail(geo, schedular, error_body, mode='predictions'):
    subject = f'TTM Model Run Triggered - {geo}:{schedular}'
    body = f'''
    Hello team,
    This is to notify you that the ttm-model run has failed for geo={geo}: schedular={schedular}
    in the {mode} stage with the following error -> {error_body}.
    If you have any questions or need further information, please feel free to reach out.

    Regards and Thanks,
    Kayithi Naga Sai
    '''
    recipient_emails = ['<EMAIL>', '<EMAIL>']
    send_email(subject, body, recipient_emails)