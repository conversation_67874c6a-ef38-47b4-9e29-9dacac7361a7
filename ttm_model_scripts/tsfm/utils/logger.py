import warnings
import logging
import os

log_path = "ttm.log"
if os.path.exists(log_path):
    os.remove(log_path)

# Configure logging
logging.basicConfig(
    filename=log_path,
    filemode="w",
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,  # Change to WARNING if you don't want INFO logs
)

# Redirect warnings to logging and ignore them
logging.captureWarnings(True)
warnings.simplefilter("ignore")  # Suppress all warnings

# Suppress logs from ALL third-party libraries
for logger_name in logging.root.manager.loggerDict:
    if not logger_name.startswith("__main__"):  # Keep only your script's logs
        logging.getLogger(logger_name).setLevel(logging.CRITICAL)
logging.getLogger("elasticsearch").setLevel(logging.CRITICAL)
logging.getLogger("urllib3").setLevel(logging.CRITICAL)  # Suppresses HTTP logs
logging.getLogger("opensearch").setLevel(logging.CRITICAL)
# Example log messages
logger = logging.getLogger(__name__)
logger.info("This is an info log from your script.")
logger.warning("This is a warning log from your script.")
