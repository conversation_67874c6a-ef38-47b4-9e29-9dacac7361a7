{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": [" # TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "  **Using TTM-512-96 model.**"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "\n", "from tsfm_public import TinyTimeMixerForPrediction, TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.visualization import plot_predictions"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "set_seed(42)\n", "\n", "# Specify model parameters\n", "context_length = 512\n", "forecast_length = 96\n", "freeze_backbone = True\n", "learning_rate = 0.001\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_datasets() function\n", "# in notebooks/hfdemo/tinytimemixer/utils/ttm_utils.py\n", "# to see how it is used.\n", "DATA_ROOT_PATH = \"datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = \"ttm_results_benchmark_512_96/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Get model path"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["hf_model_path = \"ibm/TTM\"\n", "if context_length == 512:\n", "    hf_model_branch = \"main\"\n", "elif context_length == 1024:\n", "    hf_model_branch = \"1024_96_v1\"\n", "else:\n", "    raise ValueError(\"Current supported context lengths are 512 and 1024. Stay tuned for more TTMs!\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: etth1, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 8033, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm/TTM/main\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "85ec2afa39384bd4b35d8ebf67b5d033", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.19k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "645c7064979e44789dd78ab4500abf75", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/3.24M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.36317431926727295, 'eval_model_preparation_time': 0.0028, 'eval_runtime': 4.6427, 'eval_samples_per_second': 599.87, 'eval_steps_per_second': 9.477}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: etth1, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 311, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='55' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 55/250 00:16 < 01:02, 3.12 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.085700</td>\n", "      <td>0.656020</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.086200</td>\n", "      <td>0.656616</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.070400</td>\n", "      <td>0.657144</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.093300</td>\n", "      <td>0.658152</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.937400</td>\n", "      <td>0.659537</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.865200</td>\n", "      <td>0.661400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.803600</td>\n", "      <td>0.662929</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.748000</td>\n", "      <td>0.664672</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.698600</td>\n", "      <td>0.667598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.666000</td>\n", "      <td>0.674859</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.616600</td>\n", "      <td>0.685595</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.7791833227330988 seconds, Total Train Time = 18.585089206695557\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.363126665353775, 'eval_runtime': 0.9124, 'eval_samples_per_second': 3052.451, 'eval_steps_per_second': 48.225, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: etth1, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 717, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='204' max='600' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [204/600 00:28 < 00:55, 7.15 it/s, Epoch 17/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.043800</td>\n", "      <td>0.655415</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.990900</td>\n", "      <td>0.655896</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.884400</td>\n", "      <td>0.657076</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.792000</td>\n", "      <td>0.657461</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.665900</td>\n", "      <td>0.657554</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.621100</td>\n", "      <td>0.655823</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.527600</td>\n", "      <td>0.655078</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.474300</td>\n", "      <td>0.657213</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.443800</td>\n", "      <td>0.662531</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.433600</td>\n", "      <td>0.670480</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.410300</td>\n", "      <td>0.681129</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.407100</td>\n", "      <td>0.680766</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.393800</td>\n", "      <td>0.694353</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.392600</td>\n", "      <td>0.692552</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.375600</td>\n", "      <td>0.702562</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.373900</td>\n", "      <td>0.702306</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.369000</td>\n", "      <td>0.706614</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.7961447799907011 seconds, Total Train Time = 28.757564544677734\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3642033636569977, 'eval_runtime': 0.9201, 'eval_samples_per_second': 3027.003, 'eval_steps_per_second': 47.823, 'epoch': 17.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: etth2, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 8033, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.363    0.363     0.364\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm/TTM/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.28556710481643677, 'eval_model_preparation_time': 0.0021, 'eval_runtime': 1.4084, 'eval_samples_per_second': 1977.384, 'eval_steps_per_second': 31.241}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: etth2, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 311, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 60/250 00:19 < 01:02, 3.03 it/s, Epoch 12/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.497100</td>\n", "      <td>0.208019</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.439300</td>\n", "      <td>0.207998</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.450200</td>\n", "      <td>0.208099</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.424500</td>\n", "      <td>0.208681</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.399700</td>\n", "      <td>0.209764</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.336700</td>\n", "      <td>0.211253</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.267400</td>\n", "      <td>0.213202</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.247000</td>\n", "      <td>0.215709</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.223300</td>\n", "      <td>0.218617</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.187200</td>\n", "      <td>0.222340</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.170400</td>\n", "      <td>0.225701</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.159400</td>\n", "      <td>0.230151</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.6941847006479899 seconds, Total Train Time = 19.669750213623047\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2842232882976532, 'eval_runtime': 0.8497, 'eval_samples_per_second': 3277.649, 'eval_steps_per_second': 51.783, 'epoch': 12.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: etth2, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 717, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='132' max='600' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [132/600 00:18 < 01:06, 7.03 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.694300</td>\n", "      <td>0.208229</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.667200</td>\n", "      <td>0.208902</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.684900</td>\n", "      <td>0.210279</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.530900</td>\n", "      <td>0.212758</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.471600</td>\n", "      <td>0.216474</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.407100</td>\n", "      <td>0.222424</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.366300</td>\n", "      <td>0.230155</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.335900</td>\n", "      <td>0.234342</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.310300</td>\n", "      <td>0.233168</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.305700</td>\n", "      <td>0.231881</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.290800</td>\n", "      <td>0.239227</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.8266618685288862 seconds, Total Train Time = 19.24742102622986\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2839512825012207, 'eval_runtime': 0.8597, 'eval_samples_per_second': 3239.659, 'eval_steps_per_second': 51.183, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: ettm1, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.363    0.363     0.364\n", "1   etth2   0.286    0.284     0.284\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm/TTM/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 33953, val = 11425, test = 11425\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:05]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.41525667905807495, 'eval_model_preparation_time': 0.0022, 'eval_runtime': 5.9133, 'eval_samples_per_second': 1932.084, 'eval_steps_per_second': 30.271}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: ettm1, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 1607, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='520' max='1300' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 520/1300 00:53 < 01:20, 9.66 it/s, Epoch 20/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.550900</td>\n", "      <td>0.463731</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.479900</td>\n", "      <td>0.465929</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.454400</td>\n", "      <td>0.473586</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.367000</td>\n", "      <td>0.475486</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.315800</td>\n", "      <td>0.475515</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.269300</td>\n", "      <td>0.468186</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.253400</td>\n", "      <td>0.460052</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.239900</td>\n", "      <td>0.458469</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.233500</td>\n", "      <td>0.453531</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.225800</td>\n", "      <td>0.453469</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.222700</td>\n", "      <td>0.455705</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.217800</td>\n", "      <td>0.453836</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.213800</td>\n", "      <td>0.456086</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.212700</td>\n", "      <td>0.458392</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.208400</td>\n", "      <td>0.456380</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.207400</td>\n", "      <td>0.462406</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.204400</td>\n", "      <td>0.465798</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.201600</td>\n", "      <td>0.465260</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.199300</td>\n", "      <td>0.473123</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.200500</td>\n", "      <td>0.470573</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.0061900973320008 seconds, Total Train Time = 54.34703779220581\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3644302487373352, 'eval_runtime': 1.6557, 'eval_samples_per_second': 6900.436, 'eval_steps_per_second': 108.112, 'epoch': 20.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: ettm1, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 3309, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='936' max='2600' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 936/2600 00:53 < 01:35, 17.39 it/s, Epoch 18/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.653900</td>\n", "      <td>0.460911</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.553200</td>\n", "      <td>0.463849</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.452500</td>\n", "      <td>0.466370</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.364200</td>\n", "      <td>0.445985</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.320800</td>\n", "      <td>0.436441</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.302200</td>\n", "      <td>0.430455</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.293700</td>\n", "      <td>0.430863</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.284700</td>\n", "      <td>0.427922</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.279800</td>\n", "      <td>0.434429</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.275000</td>\n", "      <td>0.431091</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.270600</td>\n", "      <td>0.431898</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.268600</td>\n", "      <td>0.429764</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.265100</td>\n", "      <td>0.439841</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.264000</td>\n", "      <td>0.432602</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.261000</td>\n", "      <td>0.434874</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.260600</td>\n", "      <td>0.439803</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.256600</td>\n", "      <td>0.444250</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.255100</td>\n", "      <td>0.443020</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.373883843421936 seconds, Total Train Time = 54.45233702659607\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.37092921137809753, 'eval_runtime': 1.6994, 'eval_samples_per_second': 6723.14, 'eval_steps_per_second': 105.334, 'epoch': 18.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: ettm2, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.363    0.363     0.364\n", "1   etth2   0.286    0.284     0.284\n", "2   ettm1   0.415    0.364     0.371\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm/TTM/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 33953, val = 11425, test = 11425\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:05]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1860235333442688, 'eval_model_preparation_time': 0.0022, 'eval_runtime': 5.5954, 'eval_samples_per_second': 2041.859, 'eval_steps_per_second': 31.991}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: ettm2, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 1607, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='338' max='1300' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 338/1300 00:34 < 01:39, 9.65 it/s, Epoch 13/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.403100</td>\n", "      <td>0.130643</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.340000</td>\n", "      <td>0.129244</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.283400</td>\n", "      <td>0.128597</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.238700</td>\n", "      <td>0.130647</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.197600</td>\n", "      <td>0.135873</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.178500</td>\n", "      <td>0.141251</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.160400</td>\n", "      <td>0.143489</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.151500</td>\n", "      <td>0.143133</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.144200</td>\n", "      <td>0.145625</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.141300</td>\n", "      <td>0.146513</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.138700</td>\n", "      <td>0.148491</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.135700</td>\n", "      <td>0.151306</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.132300</td>\n", "      <td>0.146737</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.026107146189763 seconds, Total Train Time = 35.573742151260376\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17499123513698578, 'eval_runtime': 1.6567, 'eval_samples_per_second': 6896.184, 'eval_steps_per_second': 108.045, 'epoch': 13.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: ettm2, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 3309, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='624' max='2600' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 624/2600 00:36 < 01:54, 17.22 it/s, Epoch 12/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.366700</td>\n", "      <td>0.129779</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.267700</td>\n", "      <td>0.128715</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.215200</td>\n", "      <td>0.129231</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.169600</td>\n", "      <td>0.130869</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.150000</td>\n", "      <td>0.131003</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.139700</td>\n", "      <td>0.131113</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.134100</td>\n", "      <td>0.130966</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.129800</td>\n", "      <td>0.134528</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.127100</td>\n", "      <td>0.132286</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.124300</td>\n", "      <td>0.136354</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.122800</td>\n", "      <td>0.130616</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.120800</td>\n", "      <td>0.137425</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.3781344493230183 seconds, Total Train Time = 36.829975605010986\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17638567090034485, 'eval_runtime': 1.5909, 'eval_samples_per_second': 7181.252, 'eval_steps_per_second': 112.512, 'epoch': 12.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: weather, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 36280, val = 5175, test = 10444\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.363    0.363     0.364\n", "1   etth2   0.286    0.284     0.284\n", "2   ettm1   0.415    0.364     0.371\n", "3   ettm2   0.186    0.175     0.176\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm/TTM/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:06]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1524711698293686, 'eval_model_preparation_time': 0.0022, 'eval_runtime': 6.5114, 'eval_samples_per_second': 1603.959, 'eval_steps_per_second': 25.187}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: weather, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 1723, val = 5175, test = 10444\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='351' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 351/1350 00:33 < 01:36, 10.40 it/s, Epoch 13/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.160100</td>\n", "      <td>0.425349</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.155800</td>\n", "      <td>0.422991</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.151400</td>\n", "      <td>0.422865</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.146100</td>\n", "      <td>0.427230</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.140200</td>\n", "      <td>0.434825</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.133500</td>\n", "      <td>0.442507</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.127200</td>\n", "      <td>0.453159</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.120200</td>\n", "      <td>0.465943</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.114300</td>\n", "      <td>0.465322</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.109000</td>\n", "      <td>0.464073</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.103900</td>\n", "      <td>0.479937</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.098800</td>\n", "      <td>0.485888</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.095600</td>\n", "      <td>0.479965</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.1684032403505766 seconds, Total Train Time = 34.34052586555481\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15006662905216217, 'eval_runtime': 2.1448, 'eval_samples_per_second': 4869.363, 'eval_steps_per_second': 76.463, 'epoch': 13.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: weather, context length: 512, prediction length 96\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 3542, val = 5175, test = 10444\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='672' max='2800' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 672/2800 00:35 < 01:53, 18.72 it/s, Epoch 12/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.134900</td>\n", "      <td>0.422834</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.131000</td>\n", "      <td>0.421728</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.128000</td>\n", "      <td>0.422719</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.123700</td>\n", "      <td>0.425492</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.120500</td>\n", "      <td>0.428487</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.116000</td>\n", "      <td>0.436083</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.112200</td>\n", "      <td>0.438655</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.106800</td>\n", "      <td>0.437371</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.103000</td>\n", "      <td>0.436040</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.100000</td>\n", "      <td>0.427018</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.096600</td>\n", "      <td>0.435761</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.093300</td>\n", "      <td>0.433628</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.6051064729690552 seconds, Total Train Time = 36.556100368499756\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14866013824939728, 'eval_runtime': 2.1524, 'eval_samples_per_second': 4852.25, 'eval_steps_per_second': 76.194, 'epoch': 12.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: electricity, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   dataset  zs_mse  fs5_mse  fs10_mse\n", "0    etth1   0.363    0.363     0.364\n", "1    etth2   0.286    0.284     0.284\n", "2    ettm1   0.415    0.364     0.371\n", "3    ettm2   0.186    0.175     0.176\n", "4  weather   0.152    0.150     0.149\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm/TTM/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 17805, val = 2537, test = 5165\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 01:18]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17006558179855347, 'eval_model_preparation_time': 0.0023, 'eval_runtime': 79.6138, 'eval_samples_per_second': 64.876, 'eval_steps_per_second': 2.035}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: electricity, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 800, val = 2537, test = 5165\n", "/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1250' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1250/1250 07:03, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.186000</td>\n", "      <td>0.136702</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.179200</td>\n", "      <td>0.132026</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.173600</td>\n", "      <td>0.128869</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.167900</td>\n", "      <td>0.125446</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.163200</td>\n", "      <td>0.123641</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.159400</td>\n", "      <td>0.122560</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.156500</td>\n", "      <td>0.121135</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.153700</td>\n", "      <td>0.120255</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.151700</td>\n", "      <td>0.119879</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.149700</td>\n", "      <td>0.118841</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.147900</td>\n", "      <td>0.119294</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.146600</td>\n", "      <td>0.118377</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.145100</td>\n", "      <td>0.119855</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.144400</td>\n", "      <td>0.118071</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.142800</td>\n", "      <td>0.118609</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.142100</td>\n", "      <td>0.118664</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.141100</td>\n", "      <td>0.118297</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.140100</td>\n", "      <td>0.118825</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.139000</td>\n", "      <td>0.117799</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.138800</td>\n", "      <td>0.118162</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.138300</td>\n", "      <td>0.118339</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.137700</td>\n", "      <td>0.117534</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.137200</td>\n", "      <td>0.117699</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.136200</td>\n", "      <td>0.117654</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.135300</td>\n", "      <td>0.117274</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.135100</td>\n", "      <td>0.117221</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.134600</td>\n", "      <td>0.117807</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.134200</td>\n", "      <td>0.117367</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.133800</td>\n", "      <td>0.117252</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.133400</td>\n", "      <td>0.117081</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.133000</td>\n", "      <td>0.117083</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.132900</td>\n", "      <td>0.116850</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.132400</td>\n", "      <td>0.116892</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.132200</td>\n", "      <td>0.116912</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.131800</td>\n", "      <td>0.117315</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.131500</td>\n", "      <td>0.116783</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.130900</td>\n", "      <td>0.116776</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.130800</td>\n", "      <td>0.116731</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.130600</td>\n", "      <td>0.116967</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.130600</td>\n", "      <td>0.116730</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.130300</td>\n", "      <td>0.116513</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.130300</td>\n", "      <td>0.116554</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.130000</td>\n", "      <td>0.116673</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.130000</td>\n", "      <td>0.116653</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.130000</td>\n", "      <td>0.116706</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.130000</td>\n", "      <td>0.116553</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.130000</td>\n", "      <td>0.116500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.129900</td>\n", "      <td>0.116503</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.129700</td>\n", "      <td>0.116548</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.129800</td>\n", "      <td>0.116546</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 2.957715253829956 seconds, Total Train Time = 424.4246916770935\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:09]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14259681105613708, 'eval_runtime': 10.2424, 'eval_samples_per_second': 504.275, 'eval_steps_per_second': 15.817, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: electricity, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 1695, val = 2537, test = 5165\n", "/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1325' max='2650' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1325/2650 04:23 < 04:23, 5.03 it/s, Epoch 25/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.166400</td>\n", "      <td>0.131775</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.156800</td>\n", "      <td>0.126925</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.150300</td>\n", "      <td>0.123428</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.145700</td>\n", "      <td>0.121103</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.141900</td>\n", "      <td>0.119786</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.138900</td>\n", "      <td>0.118132</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.136400</td>\n", "      <td>0.117050</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.134200</td>\n", "      <td>0.116493</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.132600</td>\n", "      <td>0.116092</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.131200</td>\n", "      <td>0.115692</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.130500</td>\n", "      <td>0.115982</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.129500</td>\n", "      <td>0.115369</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.128500</td>\n", "      <td>0.115938</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.128100</td>\n", "      <td>0.115339</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.127300</td>\n", "      <td>0.114844</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.126600</td>\n", "      <td>0.115098</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.126100</td>\n", "      <td>0.115571</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.125900</td>\n", "      <td>0.115323</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.125100</td>\n", "      <td>0.115411</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.124700</td>\n", "      <td>0.114962</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.124200</td>\n", "      <td>0.114975</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.123700</td>\n", "      <td>0.114859</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.123400</td>\n", "      <td>0.114951</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.122900</td>\n", "      <td>0.115152</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.122600</td>\n", "      <td>0.115177</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 5.148207950592041 seconds, Total Train Time = 264.00721859931946\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:09]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.13970844447612762, 'eval_runtime': 10.0814, 'eval_samples_per_second': 512.33, 'eval_steps_per_second': 16.069, 'epoch': 25.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: traffic, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["       dataset  zs_mse  fs5_mse  fs10_mse\n", "0        etth1   0.363    0.363     0.364\n", "1        etth2   0.286    0.284     0.284\n", "2        ettm1   0.415    0.364     0.371\n", "3        ettm2   0.186    0.175     0.176\n", "4      weather   0.152    0.150     0.149\n", "5  electricity   0.170    0.143     0.140\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm/TTM/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 11673, val = 1661, test = 3413\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 01:57]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.5094045996665955, 'eval_model_preparation_time': 0.0022, 'eval_runtime': 117.7341, 'eval_samples_per_second': 28.989, 'eval_steps_per_second': 3.627}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: traffic, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 493, val = 1661, test = 3413\n", "/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='3100' max='3100' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [3100/3100 11:16, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.272700</td>\n", "      <td>0.393278</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.253400</td>\n", "      <td>0.375481</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.241100</td>\n", "      <td>0.360526</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.230500</td>\n", "      <td>0.351872</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.222200</td>\n", "      <td>0.344429</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.214800</td>\n", "      <td>0.339461</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.209600</td>\n", "      <td>0.338062</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.205600</td>\n", "      <td>0.336990</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.202900</td>\n", "      <td>0.336078</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.200000</td>\n", "      <td>0.334375</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.198000</td>\n", "      <td>0.333791</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.197000</td>\n", "      <td>0.333844</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.195100</td>\n", "      <td>0.333792</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.193600</td>\n", "      <td>0.333915</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.192700</td>\n", "      <td>0.334478</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.191500</td>\n", "      <td>0.333000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.190800</td>\n", "      <td>0.332865</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.189500</td>\n", "      <td>0.334100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.188800</td>\n", "      <td>0.332967</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.188100</td>\n", "      <td>0.331086</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.186900</td>\n", "      <td>0.332582</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.186700</td>\n", "      <td>0.331533</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.185800</td>\n", "      <td>0.330423</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.185100</td>\n", "      <td>0.331567</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.184600</td>\n", "      <td>0.331676</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.184500</td>\n", "      <td>0.330323</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.183900</td>\n", "      <td>0.330532</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.183300</td>\n", "      <td>0.329897</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.182400</td>\n", "      <td>0.330098</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.181900</td>\n", "      <td>0.330095</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.181800</td>\n", "      <td>0.329849</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.181300</td>\n", "      <td>0.329267</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.180700</td>\n", "      <td>0.329384</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.180200</td>\n", "      <td>0.329585</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.180200</td>\n", "      <td>0.328754</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.179500</td>\n", "      <td>0.328836</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.179400</td>\n", "      <td>0.328085</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.178800</td>\n", "      <td>0.328287</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.178700</td>\n", "      <td>0.328173</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.178400</td>\n", "      <td>0.328408</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.178100</td>\n", "      <td>0.328306</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.177900</td>\n", "      <td>0.327732</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.177700</td>\n", "      <td>0.328101</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.177600</td>\n", "      <td>0.327719</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.177700</td>\n", "      <td>0.327562</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.177300</td>\n", "      <td>0.327719</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.177100</td>\n", "      <td>0.327573</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.177200</td>\n", "      <td>0.327571</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.177200</td>\n", "      <td>0.327563</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.177200</td>\n", "      <td>0.327564</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 4.475683298110962 seconds, Total Train Time = 677.882349729538\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:16]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3968665301799774, 'eval_runtime': 17.4472, 'eval_samples_per_second': 195.618, 'eval_steps_per_second': 24.474, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Dataset name: traffic, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/u/wmgiffor/git/tsfm/tsfm_public/toolkit/dataset.py:186: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "INFO:p-86752:t-23060138476288:data_handling.py:load_dataset:Data lengths: train = 1081, val = 1661, test = 3413\n", "/dccstor/tsfm-reg-class/wmgiffor/.conda/envs/tsfm/lib/python3.10/site-packages/transformers/training_args.py:1525: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:p-86752:t-23060138476288:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "Using learning rate = 0.001\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='4080' max='6800' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [4080/6800 08:58 < 05:59, 7.57 it/s, Epoch 30/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.294200</td>\n", "      <td>0.380806</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.271200</td>\n", "      <td>0.362084</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.258500</td>\n", "      <td>0.351829</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.248300</td>\n", "      <td>0.345643</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.240500</td>\n", "      <td>0.340656</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.234500</td>\n", "      <td>0.339494</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.229600</td>\n", "      <td>0.335847</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.226500</td>\n", "      <td>0.335783</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.225500</td>\n", "      <td>0.338349</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.223200</td>\n", "      <td>0.336193</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.222600</td>\n", "      <td>0.343954</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.222100</td>\n", "      <td>0.340995</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.219800</td>\n", "      <td>0.339137</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.219100</td>\n", "      <td>0.335982</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.217700</td>\n", "      <td>0.344850</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.218600</td>\n", "      <td>0.342654</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.218100</td>\n", "      <td>0.333909</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.215300</td>\n", "      <td>0.338186</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.213600</td>\n", "      <td>0.342740</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.213100</td>\n", "      <td>0.332170</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.213200</td>\n", "      <td>0.335310</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.213000</td>\n", "      <td>0.334148</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.211000</td>\n", "      <td>0.337650</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.211000</td>\n", "      <td>0.340426</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.210200</td>\n", "      <td>0.339711</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.210900</td>\n", "      <td>0.342000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.209600</td>\n", "      <td>0.339016</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.208400</td>\n", "      <td>0.335918</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.207600</td>\n", "      <td>0.332504</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.206700</td>\n", "      <td>0.337658</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 8.763746841748555 seconds, Total Train Time = 539.691143989563\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:16]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4039205312728882, 'eval_runtime': 17.7536, 'eval_samples_per_second': 192.242, 'eval_steps_per_second': 24.051, 'epoch': 30.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse  fs10_mse\n", "0        etth1   0.363    0.363     0.364\n", "1        etth2   0.286    0.284     0.284\n", "2        ettm1   0.415    0.364     0.371\n", "3        ettm2   0.186    0.175     0.176\n", "4      weather   0.152    0.150     0.149\n", "5  electricity   0.170    0.143     0.140\n", "6      traffic   0.509    0.397     0.404\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"fs10_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs10_mean_epoch_time\": [],\n", "    \"fs10_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "    \"fs10_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}/{hf_model_branch}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(DATASET, context_length, forecast_length, dataset_root_path=DATA_ROOT_PATH)\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = TinyTimeMixerForPrediction.from_pretrained(hf_model_path, revision=hf_model_branch)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5, 10]:\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\", \"fs10_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>fs10_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs10_mean_epoch_time</th>\n", "      <th>fs10_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "      <th>fs10_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.363</td>\n", "      <td>0.363</td>\n", "      <td>0.364</td>\n", "      <td>4.643</td>\n", "      <td>0.779</td>\n", "      <td>18.585</td>\n", "      <td>0.796</td>\n", "      <td>28.758</td>\n", "      <td>0.656</td>\n", "      <td>0.655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.286</td>\n", "      <td>0.284</td>\n", "      <td>0.284</td>\n", "      <td>1.408</td>\n", "      <td>0.694</td>\n", "      <td>19.670</td>\n", "      <td>0.827</td>\n", "      <td>19.247</td>\n", "      <td>0.208</td>\n", "      <td>0.208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.415</td>\n", "      <td>0.364</td>\n", "      <td>0.371</td>\n", "      <td>5.913</td>\n", "      <td>1.006</td>\n", "      <td>54.347</td>\n", "      <td>1.374</td>\n", "      <td>54.452</td>\n", "      <td>0.453</td>\n", "      <td>0.428</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.186</td>\n", "      <td>0.175</td>\n", "      <td>0.176</td>\n", "      <td>5.595</td>\n", "      <td>1.026</td>\n", "      <td>35.574</td>\n", "      <td>1.378</td>\n", "      <td>36.830</td>\n", "      <td>0.129</td>\n", "      <td>0.129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.152</td>\n", "      <td>0.150</td>\n", "      <td>0.149</td>\n", "      <td>6.511</td>\n", "      <td>1.168</td>\n", "      <td>34.341</td>\n", "      <td>1.605</td>\n", "      <td>36.556</td>\n", "      <td>0.423</td>\n", "      <td>0.422</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.170</td>\n", "      <td>0.143</td>\n", "      <td>0.140</td>\n", "      <td>79.614</td>\n", "      <td>2.958</td>\n", "      <td>424.425</td>\n", "      <td>5.148</td>\n", "      <td>264.007</td>\n", "      <td>0.116</td>\n", "      <td>0.115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.509</td>\n", "      <td>0.397</td>\n", "      <td>0.404</td>\n", "      <td>117.734</td>\n", "      <td>4.476</td>\n", "      <td>677.882</td>\n", "      <td>8.764</td>\n", "      <td>539.691</td>\n", "      <td>0.328</td>\n", "      <td>0.332</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  fs10_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.363    0.363     0.364         4.643                0.779   \n", "1        etth2   0.286    0.284     0.284         1.408                0.694   \n", "2        ettm1   0.415    0.364     0.371         5.913                1.006   \n", "3        ettm2   0.186    0.175     0.176         5.595                1.026   \n", "4      weather   0.152    0.150     0.149         6.511                1.168   \n", "5  electricity   0.170    0.143     0.140        79.614                2.958   \n", "6      traffic   0.509    0.397     0.404       117.734                4.476   \n", "\n", "   fs5_total_train_time  fs10_mean_epoch_time  fs10_total_train_time  \\\n", "0                18.585                 0.796                 28.758   \n", "1                19.670                 0.827                 19.247   \n", "2                54.347                 1.374                 54.452   \n", "3                35.574                 1.378                 36.830   \n", "4                34.341                 1.605                 36.556   \n", "5               424.425                 5.148                264.007   \n", "6               677.882                 8.764                539.691   \n", "\n", "   fs5_best_val_metric  fs10_best_val_metric  \n", "0                0.656                 0.655  \n", "1                0.208                 0.208  \n", "2                0.453                 0.428  \n", "3                0.129                 0.129  \n", "4                0.423                 0.422  \n", "5                0.116                 0.115  \n", "6                0.328                 0.332  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}