{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Install IBM-Granite/granite-tsfm repository\n", "\n", "This notebook installs the IBM Time Series Foundation Model repository."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install ibm/tsfm\n", "! pip install \"tsfm_public[notebooks] @ git+ssh://**************/ibm-granite/granite-tsfm.git\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check installation\n", "from tsfm_public.models.tinytimemixer import TinyTimeMixerForPrediction\n", "\n", "\n", "model_512 = TinyTimeMixerForPrediction.from_pretrained(\"ibm/TTM\", revision=\"main\")\n", "model_512.config.context_length"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for another tsfm model\n", "model_1024 = TinyTimeMixerForPrediction.from_pretrained(\"ibm/TTM\", revision=\"1024_96_v1\")\n", "model_1024.config.num_patches"]}], "metadata": {"kernelspec": {"display_name": "torch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 2}