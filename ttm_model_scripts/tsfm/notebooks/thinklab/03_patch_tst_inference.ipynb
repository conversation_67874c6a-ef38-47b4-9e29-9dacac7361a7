{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Inferencing a Time Series Model using Channel Independence PatchTST\n", "\n", "<ul>\n", "<li>Contributors: IBM AI Research team and IBM Research Technology Education team\n", "<li>Contact for questions and technical support: <EMAIL>\n", "<li>Provenance: IBM Research\n", "<li>Version: 1.0.0\n", "<li>Release date: \n", "<li>Compute requirements: 4 CPU\n", "<li>Memory requirements: 16 GB\n", "<li>Notebook set: Time Series Foundation Model\n", "</ul>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary\n", "\n", "**Patch Time Series Transformer (PatchTST)** is a new method for long-term forecasting based on Transformer modeling. In PatchTST, a time series is segmented into subseries-level patches that are served as input tokens to Transformer. PatchTST was first proposed in 2023 in [this paper](https://arxiv.org/pdf/2211.14730.pdf). It can achieve state-of-the-art results when compared to other Transformer-based models.\n", "\n", "**Channel Independence PatchTST** is a variant of PatchTST where each channel contains a single univariate time series that shares the same embedding and Transformer weights across all the series.\n", "\n", "This notebook is the last of three notebooks that should be run in sequence. After running the first notebook, `01_patch_tst_pretrain.ipynb`, a pretrained model was saved in your private storage. The second notebook, `02_patch_tst_fine_tune.ipynb`, loaded the pretrained model and created a fine tuned model, which was also saved in your private storage. This notebook demonstrates inferencing on test data using the fine tuned model. The goal of this demonstration is to forecast the future sensor values (load, oil temperature) of an electric transformer using test data from the ETTh1 benchmark dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Table of Contents\n", "\n", "* <a href=\"#TST3_intro\">Channel Independence PatchTST</a>\n", "* <a href=\"#TST3_codes\">Code Samples</a>\n", "    * <a href=\"#TST3_import\">Step 1. Imports</a>\n", "    * <a href=\"#TST3_pipeln\">Step 2. Load model and construct forecasting pipeline</a>\n", "    * <a href=\"#TST3_datast\">Step 3. Load and prepare datasets </a>\n", "    * <a href=\"#TST3_forecs\">Step 4. Generate forecasts </a>\n", "    * <a href=\"#TST3_perfor\">Step 5. Evaluate performance </a>\n", "    * <a href=\"#TST3_visual\">Step 6. Plot results </a>\n", "* <a href=\"#TST3_concl\">Conclusion</a>\n", "* <a href=\"#TST3_learn\">Learn More</a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_intro\"></a>\n", "# Channel Independence PatchTST\n", "\n", "**Channel Independence PatchTST** is an efficient design of Transformer-based models for multivariate time series forecasting and self-supervised representation learning. It is demonstrated in the following diagram. It is based on two key components:\n", "\n", "- segmentation of time series into subseries-level patches that are served as input tokens to Transformer\n", "\n", "- channel independence where each channel contains a single univariate time series that shares the same embedding and Transformer weights across all the series.\n", "\n", "Patching design naturally has three-fold benefit: local semantic information is retained in the embedding; computation and memory usage of the attention maps are quadratically reduced given the same look-back window; and the model can attend longer history.\n", "\n", "Channel independence allows each time series to have its own embedding and attention maps while sharing the same model parameters across different channels.\n", "\n", "<div> <img src=\"./data/figures/patchTST.png\" alt=\"Drawing\" style=\"width: 600px;\"/></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_codes\"></a>\n", "# Code Samples\n", "\n", "This section includes documentation and code samples to demonstrate the use of the toolkit for inferencing."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_import\"></a>\n", "## Step 1. Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from transformers.models.patchtst import PatchTSTForPrediction\n", "\n", "from tsfm_public.toolkit.time_series_forecasting_pipeline import (\n", "    TimeSeriesForecastingPipeline,\n", ")\n", "from tsfm_public.toolkit.time_series_preprocessor import TimeSeriesPreprocessor\n", "from tsfm_public.toolkit.util import select_by_index\n", "from tsfm_public.toolkit.visualization import plot_ts_forecasting\n", "\n", "# Customized IBM stylesheet for plots - dark background\n", "# %run '/opt/ibm/visualization/plotly/plotly_template_dark.ipynb'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_pipeln\"></a>\n", "## Step 2. Load model and construct forecasting pipeline\n", "\n", " Please adjust the following parameters to suit your application:\n", " - timestamp_column: column name containing timestamp information, use None if there is no such column\n", " - id_columns: List of column names specifying the IDs of different time series. If no ID column exists, use []\n", " - forecast_columns: List of columns to be modeled\n", " - finetuned_model_path: Path to the finetuned model\n", "   "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["timestamp_column = \"date\"\n", "id_columns = []\n", "forecast_columns = [\"HUFL\", \"HULL\", \"MUFL\", \"MULL\", \"LUFL\", \"LULL\", \"OT\"]\n", "finetuned_model_path = \"model/forecasting\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["model = PatchTSTForPrediction.from_pretrained(finetuned_model_path)\n", "# model.model.mask_input = False\n", "forecast_pipeline = TimeSeriesForecastingPipeline(\n", "    model=model,\n", "    timestamp_column=timestamp_column,\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", ")\n", "context_length = model.config.context_length\n", "\n", "tsp = TimeSeriesPreprocessor.from_pretrained(\"preprocessor\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_datast\"></a>\n", "## Step 3. Load and prepare datasets\n", "\n", "The specific data loaded here is Electricity Transformer Temperature (ETT) data - including load, oil temperature in an electric transformer. In the next cell, please adjust the following parameters to suit your application:\n", "\n", " - dataset_path: path to local .csv file, or web address to a csv file for the data of interest. Data is loaded with pandas, so anything supported by pd.read_csv is supported: (https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html).\n", " - test_start_index, test_end_index: the start and end indices in the loaded data which delineate the test data.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["dataset_path = \"https://raw.githubusercontent.com/zhouhaoyi/ETDataset/main/ETT-small/ETTh2.csv\"\n", "test_start_index = 12 * 30 * 24 + 4 * 30 * 24 - context_length\n", "test_end_index = 12 * 30 * 24 + 8 * 30 * 24"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(\n", "    dataset_path,\n", "    parse_dates=[timestamp_column],\n", ")\n", "\n", "test_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=test_start_index,\n", "    end_index=test_end_index,\n", ")\n", "test_data = tsp.preprocess(test_data)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>HUFL</th>\n", "      <th>HULL</th>\n", "      <th>MUFL</th>\n", "      <th>MULL</th>\n", "      <th>LUFL</th>\n", "      <th>LULL</th>\n", "      <th>OT</th>\n", "      <th>__id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11008</th>\n", "      <td>2017-10-02 16:00:00</td>\n", "      <td>-0.832612</td>\n", "      <td>-2.675638</td>\n", "      <td>-0.366989</td>\n", "      <td>-2.270636</td>\n", "      <td>-1.969397</td>\n", "      <td>0.034774</td>\n", "      <td>0.107294</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11009</th>\n", "      <td>2017-10-02 17:00:00</td>\n", "      <td>-0.824573</td>\n", "      <td>-2.675638</td>\n", "      <td>-0.349490</td>\n", "      <td>-2.448201</td>\n", "      <td>-2.013137</td>\n", "      <td>0.034774</td>\n", "      <td>0.145276</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11010</th>\n", "      <td>2017-10-02 18:00:00</td>\n", "      <td>-0.511907</td>\n", "      <td>-2.675638</td>\n", "      <td>-0.238209</td>\n", "      <td>-2.172908</td>\n", "      <td>-1.477909</td>\n", "      <td>0.034774</td>\n", "      <td>0.126285</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11011</th>\n", "      <td>2017-10-02 19:00:00</td>\n", "      <td>-1.049096</td>\n", "      <td>-2.675638</td>\n", "      <td>-0.521217</td>\n", "      <td>-2.714549</td>\n", "      <td>-1.863816</td>\n", "      <td>0.034774</td>\n", "      <td>0.012471</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11012</th>\n", "      <td>2017-10-02 20:00:00</td>\n", "      <td>-0.776530</td>\n", "      <td>-2.675638</td>\n", "      <td>-0.435383</td>\n", "      <td>-2.910004</td>\n", "      <td>-1.572070</td>\n", "      <td>0.034774</td>\n", "      <td>0.031419</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     date      HUFL      HULL      MUFL      MULL      LUFL  \\\n", "11008 2017-10-02 16:00:00 -0.832612 -2.675638 -0.366989 -2.270636 -1.969397   \n", "11009 2017-10-02 17:00:00 -0.824573 -2.675638 -0.349490 -2.448201 -2.013137   \n", "11010 2017-10-02 18:00:00 -0.511907 -2.675638 -0.238209 -2.172908 -1.477909   \n", "11011 2017-10-02 19:00:00 -1.049096 -2.675638 -0.521217 -2.714549 -1.863816   \n", "11012 2017-10-02 20:00:00 -0.776530 -2.675638 -0.435383 -2.910004 -1.572070   \n", "\n", "           LULL        OT __id  \n", "11008  0.034774  0.107294    0  \n", "11009  0.034774  0.145276    0  \n", "11010  0.034774  0.126285    0  \n", "11011  0.034774  0.012471    0  \n", "11012  0.034774  0.031419    0  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["test_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_forecs\"></a>\n", "## Step 4. Generate forecasts\n", "\n", " Note that the ouput will consist of a Pandas dataframe with the following structure.\n", " If you have specified timestamp and/or ID columns they will be included. The forecast\n", " columns will be named `{forecast column}_prediction`, for each `{forecast column}` that was\n", " specified.\n", " Each forecast column will be a vector of values with length equal to the prediction horizon\n", " that was specified when the model was trained."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>HUFL_prediction</th>\n", "      <th>HULL_prediction</th>\n", "      <th>MUFL_prediction</th>\n", "      <th>MULL_prediction</th>\n", "      <th>LUFL_prediction</th>\n", "      <th>LULL_prediction</th>\n", "      <th>OT_prediction</th>\n", "      <th>HUFL</th>\n", "      <th>HULL</th>\n", "      <th>MUFL</th>\n", "      <th>MULL</th>\n", "      <th>LUFL</th>\n", "      <th>LULL</th>\n", "      <th>OT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2017-10-23 23:00:00</td>\n", "      <td>[-0.7573708295822144, -1.11812162399292, -1.07...</td>\n", "      <td>[-2.1647095680236816, -2.2807607650756836, -2....</td>\n", "      <td>[-0.3136693835258484, -0.48255351185798645, -0...</td>\n", "      <td>[-2.35709285736084, -2.5338454246520996, -2.38...</td>\n", "      <td>[-1.9644588232040405, -1.971610426902771, -1.9...</td>\n", "      <td>[0.11713476479053497, 0.09989950060844421, 0.1...</td>\n", "      <td>[-0.4055476784706116, -0.40766051411628723, -0...</td>\n", "      <td>[-0.9769346117973328, -0.9528172016143799, -0....</td>\n", "      <td>[-2.67563796043396, -2.67563796043396, -2.6756...</td>\n", "      <td>[-0.37653934955596924, -0.37333613634109497, -...</td>\n", "      <td>[-2.0927388668060303, -2.2706356048583984, -2....</td>\n", "      <td>[-1.967241883277893, -2.1462976932525635, -1.7...</td>\n", "      <td>[0.0977693721652031, -0.034485891461372375, 0....</td>\n", "      <td>[-0.6323868632316589, -0.6703246831893921, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2017-10-24 00:00:00</td>\n", "      <td>[-0.9172403812408447, -1.1139382123947144, -1....</td>\n", "      <td>[-2.424948215484619, -2.5887529850006104, -2.4...</td>\n", "      <td>[-0.37806278467178345, -0.46569451689720154, -...</td>\n", "      <td>[-2.2810373306274414, -2.4113337993621826, -2....</td>\n", "      <td>[-2.0412588119506836, -2.0124261379241943, -1....</td>\n", "      <td>[0.08244556188583374, 0.09385186433792114, 0.1...</td>\n", "      <td>[-0.44102829694747925, -0.41357100009918213, -...</td>\n", "      <td>[-0.9528172016143799, -0.9528172016143799, -0....</td>\n", "      <td>[-2.67563796043396, -2.67563796043396, -2.6756...</td>\n", "      <td>[-0.37333613634109497, -0.47827035188674927, -...</td>\n", "      <td>[-2.2706356048583984, -2.243802070617676, -2.1...</td>\n", "      <td>[-2.1462976932525635, -1.7030754089355469, -1....</td>\n", "      <td>[-0.034485891461372375, 0.12672606110572815, 0...</td>\n", "      <td>[-0.6703246831893921, -0.7272531390190125, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2017-10-24 01:00:00</td>\n", "      <td>[-0.8816351294517517, -1.1926673650741577, -1....</td>\n", "      <td>[-2.5134541988372803, -2.6657490730285645, -2....</td>\n", "      <td>[-0.3795532286167145, -0.5077905058860779, -0....</td>\n", "      <td>[-2.1818549633026123, -2.48525333404541, -2.48...</td>\n", "      <td>[-2.0615198612213135, -2.081102132797241, -2.0...</td>\n", "      <td>[0.05493534728884697, 0.039612457156181335, 0....</td>\n", "      <td>[-0.49441859126091003, -0.4442545473575592, -0...</td>\n", "      <td>[-0.9528172016143799, -0.7444687485694885, -1....</td>\n", "      <td>[-2.67563796043396, -2.67563796043396, -2.6756...</td>\n", "      <td>[-0.47827035188674927, -0.37968331575393677, -...</td>\n", "      <td>[-2.243802070617676, -2.128516912460327, -2.56...</td>\n", "      <td>[-1.7030754089355469, -1.6823902130126953, -1....</td>\n", "      <td>[0.12672606110572815, 0.11916186660528183, 0.2...</td>\n", "      <td>[-0.7272531390190125, -0.7651479840278625, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2017-10-24 02:00:00</td>\n", "      <td>[-0.8051353693008423, -1.1740937232971191, -1....</td>\n", "      <td>[-2.4607203006744385, -2.651930093765259, -2.5...</td>\n", "      <td>[-0.34204378724098206, -0.5074977278709412, -0...</td>\n", "      <td>[-2.1523020267486572, -2.5596530437469482, -2....</td>\n", "      <td>[-1.9140721559524536, -1.9520810842514038, -1....</td>\n", "      <td>[0.07673460990190506, 0.03763217851519585, 0.0...</td>\n", "      <td>[-0.5441737174987793, -0.49769264459609985, -0...</td>\n", "      <td>[-0.7444687485694885, -1.4017665386199951, -0....</td>\n", "      <td>[-2.67563796043396, -2.67563796043396, -2.6756...</td>\n", "      <td>[-0.37968331575393677, -0.7549904584884644, -0...</td>\n", "      <td>[-2.128516912460327, -2.563485860824585, -2.53...</td>\n", "      <td>[-1.6823902130126953, -1.7397053241729736, -2....</td>\n", "      <td>[0.11916186660528183, 0.28049200773239136, 0.2...</td>\n", "      <td>[-0.7651479840278625, -0.8031288981437683, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2017-10-24 03:00:00</td>\n", "      <td>[-0.786358118057251, -0.9781568050384521, -0.8...</td>\n", "      <td>[-2.4880452156066895, -2.6666765213012695, -2....</td>\n", "      <td>[-0.33083829283714294, -0.40023595094680786, -...</td>\n", "      <td>[-2.169320583343506, -2.5081868171691895, -2.4...</td>\n", "      <td>[-1.8694772720336914, -1.9276782274246216, -1....</td>\n", "      <td>[0.08226476609706879, 0.05729310214519501, 0.0...</td>\n", "      <td>[-0.5821459889411926, -0.5289478302001953, -0....</td>\n", "      <td>[-1.4017665386199951, -0.9849737286567688, -0....</td>\n", "      <td>[-2.67563796043396, -2.67563796043396, -2.6756...</td>\n", "      <td>[-0.7549904584884644, -0.4719233810901642, -0....</td>\n", "      <td>[-2.563485860824585, -2.5369837284088135, -1.9...</td>\n", "      <td>[-1.7397053241729736, -2.063772678375244, -2.0...</td>\n", "      <td>[0.28049200773239136, 0.28049200773239136, 0.1...</td>\n", "      <td>[-0.8031288981437683, -0.8600142002105713, -0....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 date                                    HUFL_prediction  \\\n", "0 2017-10-23 23:00:00  [-0.7573708295822144, -1.11812162399292, -1.07...   \n", "1 2017-10-24 00:00:00  [-0.9172403812408447, -1.1139382123947144, -1....   \n", "2 2017-10-24 01:00:00  [-0.8816351294517517, -1.1926673650741577, -1....   \n", "3 2017-10-24 02:00:00  [-0.8051353693008423, -1.1740937232971191, -1....   \n", "4 2017-10-24 03:00:00  [-0.786358118057251, -0.9781568050384521, -0.8...   \n", "\n", "                                     HULL_prediction  \\\n", "0  [-2.1647095680236816, -2.2807607650756836, -2....   \n", "1  [-2.424948215484619, -2.5887529850006104, -2.4...   \n", "2  [-2.5134541988372803, -2.6657490730285645, -2....   \n", "3  [-2.4607203006744385, -2.651930093765259, -2.5...   \n", "4  [-2.4880452156066895, -2.6666765213012695, -2....   \n", "\n", "                                     MUFL_prediction  \\\n", "0  [-0.3136693835258484, -0.48255351185798645, -0...   \n", "1  [-0.37806278467178345, -0.46569451689720154, -...   \n", "2  [-0.3795532286167145, -0.5077905058860779, -0....   \n", "3  [-0.34204378724098206, -0.5074977278709412, -0...   \n", "4  [-0.33083829283714294, -0.40023595094680786, -...   \n", "\n", "                                     MULL_prediction  \\\n", "0  [-2.35709285736084, -2.5338454246520996, -2.38...   \n", "1  [-2.2810373306274414, -2.4113337993621826, -2....   \n", "2  [-2.1818549633026123, -2.48525333404541, -2.48...   \n", "3  [-2.1523020267486572, -2.5596530437469482, -2....   \n", "4  [-2.169320583343506, -2.5081868171691895, -2.4...   \n", "\n", "                                     LUFL_prediction  \\\n", "0  [-1.9644588232040405, -1.971610426902771, -1.9...   \n", "1  [-2.0412588119506836, -2.0124261379241943, -1....   \n", "2  [-2.0615198612213135, -2.081102132797241, -2.0...   \n", "3  [-1.9140721559524536, -1.9520810842514038, -1....   \n", "4  [-1.8694772720336914, -1.9276782274246216, -1....   \n", "\n", "                                     LULL_prediction  \\\n", "0  [0.11713476479053497, 0.09989950060844421, 0.1...   \n", "1  [0.08244556188583374, 0.09385186433792114, 0.1...   \n", "2  [0.05493534728884697, 0.039612457156181335, 0....   \n", "3  [0.07673460990190506, 0.03763217851519585, 0.0...   \n", "4  [0.08226476609706879, 0.05729310214519501, 0.0...   \n", "\n", "                                       OT_prediction  \\\n", "0  [-0.4055476784706116, -0.40766051411628723, -0...   \n", "1  [-0.44102829694747925, -0.41357100009918213, -...   \n", "2  [-0.49441859126091003, -0.4442545473575592, -0...   \n", "3  [-0.5441737174987793, -0.49769264459609985, -0...   \n", "4  [-0.5821459889411926, -0.5289478302001953, -0....   \n", "\n", "                                                HUFL  \\\n", "0  [-0.9769346117973328, -0.9528172016143799, -0....   \n", "1  [-0.9528172016143799, -0.9528172016143799, -0....   \n", "2  [-0.9528172016143799, -0.7444687485694885, -1....   \n", "3  [-0.7444687485694885, -1.4017665386199951, -0....   \n", "4  [-1.4017665386199951, -0.9849737286567688, -0....   \n", "\n", "                                                HULL  \\\n", "0  [-2.67563796043396, -2.67563796043396, -2.6756...   \n", "1  [-2.67563796043396, -2.67563796043396, -2.6756...   \n", "2  [-2.67563796043396, -2.67563796043396, -2.6756...   \n", "3  [-2.67563796043396, -2.67563796043396, -2.6756...   \n", "4  [-2.67563796043396, -2.67563796043396, -2.6756...   \n", "\n", "                                                MUFL  \\\n", "0  [-0.37653934955596924, -0.37333613634109497, -...   \n", "1  [-0.37333613634109497, -0.47827035188674927, -...   \n", "2  [-0.47827035188674927, -0.37968331575393677, -...   \n", "3  [-0.37968331575393677, -0.7549904584884644, -0...   \n", "4  [-0.7549904584884644, -0.4719233810901642, -0....   \n", "\n", "                                                MULL  \\\n", "0  [-2.0927388668060303, -2.2706356048583984, -2....   \n", "1  [-2.2706356048583984, -2.243802070617676, -2.1...   \n", "2  [-2.243802070617676, -2.128516912460327, -2.56...   \n", "3  [-2.128516912460327, -2.563485860824585, -2.53...   \n", "4  [-2.563485860824585, -2.5369837284088135, -1.9...   \n", "\n", "                                                LUFL  \\\n", "0  [-1.967241883277893, -2.1462976932525635, -1.7...   \n", "1  [-2.1462976932525635, -1.7030754089355469, -1....   \n", "2  [-1.7030754089355469, -1.6823902130126953, -1....   \n", "3  [-1.6823902130126953, -1.7397053241729736, -2....   \n", "4  [-1.7397053241729736, -2.063772678375244, -2.0...   \n", "\n", "                                                LULL  \\\n", "0  [0.0977693721652031, -0.034485891461372375, 0....   \n", "1  [-0.034485891461372375, 0.12672606110572815, 0...   \n", "2  [0.12672606110572815, 0.11916186660528183, 0.2...   \n", "3  [0.11916186660528183, 0.28049200773239136, 0.2...   \n", "4  [0.28049200773239136, 0.28049200773239136, 0.1...   \n", "\n", "                                                  OT  \n", "0  [-0.6323868632316589, -0.6703246831893921, -0....  \n", "1  [-0.6703246831893921, -0.7272531390190125, -0....  \n", "2  [-0.7272531390190125, -0.7651479840278625, -0....  \n", "3  [-0.7651479840278625, -0.8031288981437683, -0....  \n", "4  [-0.8031288981437683, -0.8600142002105713, -0....  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["forecasts = forecast_pipeline(test_data)\n", "forecasts.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_perfor\"></a>\n", "## Step 5. Eva<PERSON>ate performance\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/tsfm_public/lib/python3.10/site-packages/tsevaluate/multivalue_timeseries_evaluator.py:73: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x[self.__class__._TMP_ID_COL] = self.__class__._TMP_ID_VAL\n"]}], "source": ["try:\n", "    from tsevaluate.multivalue_timeseries_evaluator import CrossTimeSeriesEvaluator\n", "\n", "    do_eval = True\n", "except ModuleNotFoundError:\n", "    # tsevaluate (utilities for evaluating multivariate and multi-time series forecasting) will be made available at a later date\n", "    print(\"tsevaluate module not available.\")\n", "    do_eval = False\n", "\n", "labels_ = forecasts[id_columns + [timestamp_column] + forecast_columns]\n", "forecasts_ = forecasts.drop(columns=forecast_columns)\n", "\n", "if do_eval:\n", "    eval = CrossTimeSeriesEvaluator(\n", "        timestamp_column=timestamp_column,\n", "        prediction_columns=[f\"{c}_prediction\" for c in forecast_columns],\n", "        label_columns=forecast_columns,\n", "        metrics_spec=[\"mse\", \"smape\", \"rmse\", \"mae\"],\n", "        multioutput=\"uniform_average\",\n", "    )\n", "    eval.evaluate(labels_, forecasts_)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_visual\"></a>\n", "## Step 6. Plot results\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "HUFL", "type": "scatter", "x": ["2017-10-02T16:00:00", "2017-10-02T17:00:00", "2017-10-02T18:00:00", "2017-10-02T19:00:00", "2017-10-02T20:00:00", "2017-10-02T21:00:00", "2017-10-02T22:00:00", "2017-10-02T23:00:00", "2017-10-03T00:00:00", "2017-10-03T01:00:00", "2017-10-03T02:00:00", "2017-10-03T03:00:00", "2017-10-03T04:00:00", "2017-10-03T05:00:00", "2017-10-03T06:00:00", "2017-10-03T07:00:00", "2017-10-03T08:00:00", "2017-10-03T09:00:00", "2017-10-03T10:00:00", "2017-10-03T11:00:00", "2017-10-03T12:00:00", "2017-10-03T13:00:00", "2017-10-03T14:00:00", "2017-10-03T15:00:00", "2017-10-03T16:00:00", "2017-10-03T17:00:00", "2017-10-03T18:00:00", "2017-10-03T19:00:00", "2017-10-03T20:00:00", "2017-10-03T21:00:00", "2017-10-03T22:00:00", "2017-10-03T23:00:00", "2017-10-04T00:00:00", "2017-10-04T01:00:00", "2017-10-04T02:00:00", "2017-10-04T03:00:00", "2017-10-04T04:00:00", "2017-10-04T05:00:00", "2017-10-04T06:00:00", "2017-10-04T07:00:00", "2017-10-04T08:00:00", "2017-10-04T09:00:00", "2017-10-04T10:00:00", "2017-10-04T11:00:00", "2017-10-04T12:00:00", "2017-10-04T13:00:00", "2017-10-04T14:00:00", "2017-10-04T15:00:00", "2017-10-04T16:00:00", "2017-10-04T17:00:00", "2017-10-04T18:00:00", "2017-10-04T19:00:00", "2017-10-04T20:00:00", "2017-10-04T21:00:00", "2017-10-04T22:00:00", "2017-10-04T23:00:00", "2017-10-05T00:00:00", "2017-10-05T01:00:00", "2017-10-05T02:00:00", "2017-10-05T03:00:00", "2017-10-05T04:00:00", "2017-10-05T05:00:00", "2017-10-05T06:00:00", "2017-10-05T07:00:00", "2017-10-05T08:00:00", "2017-10-05T09:00:00", "2017-10-05T10:00:00", "2017-10-05T11:00:00", "2017-10-05T12:00:00", "2017-10-05T13:00:00", "2017-10-05T14:00:00", "2017-10-05T15:00:00", "2017-10-05T16:00:00", "2017-10-05T17:00:00", "2017-10-05T18:00:00", "2017-10-05T19:00:00", "2017-10-05T20:00:00", "2017-10-05T21:00:00", "2017-10-05T22:00:00", "2017-10-05T23:00:00", "2017-10-06T00:00:00", "2017-10-06T01:00:00", "2017-10-06T02:00:00", "2017-10-06T03:00:00", "2017-10-06T04:00:00", "2017-10-06T05:00:00", "2017-10-06T06:00:00", "2017-10-06T07:00:00", "2017-10-06T08:00:00", "2017-10-06T09:00:00", "2017-10-06T10:00:00", "2017-10-06T11:00:00", "2017-10-06T12:00:00", "2017-10-06T13:00:00", "2017-10-06T14:00:00", "2017-10-06T15:00:00", "2017-10-06T16:00:00", "2017-10-06T17:00:00", "2017-10-06T18:00:00", "2017-10-06T19:00:00", "2017-10-06T20:00:00", "2017-10-06T21:00:00", "2017-10-06T22:00:00", "2017-10-06T23:00:00", "2017-10-07T00:00:00", "2017-10-07T01:00:00", "2017-10-07T02:00:00", "2017-10-07T03:00:00", "2017-10-07T04:00:00", "2017-10-07T05:00:00", "2017-10-07T06:00:00", "2017-10-07T07:00:00", "2017-10-07T08:00:00", "2017-10-07T09:00:00", "2017-10-07T10:00:00", "2017-10-07T11:00:00", "2017-10-07T12:00:00", "2017-10-07T13:00:00", "2017-10-07T14:00:00", "2017-10-07T15:00:00", "2017-10-07T16:00:00", "2017-10-07T17:00:00", "2017-10-07T18:00:00", "2017-10-07T19:00:00", "2017-10-07T20:00:00", "2017-10-07T21:00:00", "2017-10-07T22:00:00", "2017-10-07T23:00:00", "2017-10-08T00:00:00", "2017-10-08T01:00:00", "2017-10-08T02:00:00", "2017-10-08T03:00:00", "2017-10-08T04:00:00", "2017-10-08T05:00:00", "2017-10-08T06:00:00", "2017-10-08T07:00:00", "2017-10-08T08:00:00", "2017-10-08T09:00:00", "2017-10-08T10:00:00", "2017-10-08T11:00:00", "2017-10-08T12:00:00", "2017-10-08T13:00:00", "2017-10-08T14:00:00", "2017-10-08T15:00:00", "2017-10-08T16:00:00", "2017-10-08T17:00:00", "2017-10-08T18:00:00", "2017-10-08T19:00:00", "2017-10-08T20:00:00", "2017-10-08T21:00:00", "2017-10-08T22:00:00", "2017-10-08T23:00:00", "2017-10-09T00:00:00", "2017-10-09T01:00:00", "2017-10-09T02:00:00", "2017-10-09T03:00:00", "2017-10-09T04:00:00", "2017-10-09T05:00:00", "2017-10-09T06:00:00", "2017-10-09T07:00:00", "2017-10-09T08:00:00", "2017-10-09T09:00:00", "2017-10-09T10:00:00", "2017-10-09T11:00:00", "2017-10-09T12:00:00", "2017-10-09T13:00:00", "2017-10-09T14:00:00", "2017-10-09T15:00:00", "2017-10-09T16:00:00", "2017-10-09T17:00:00", "2017-10-09T18:00:00", "2017-10-09T19:00:00", "2017-10-09T20:00:00", "2017-10-09T21:00:00", "2017-10-09T22:00:00", "2017-10-09T23:00:00", "2017-10-10T00:00:00", "2017-10-10T01:00:00", "2017-10-10T02:00:00", "2017-10-10T03:00:00", "2017-10-10T04:00:00", "2017-10-10T05:00:00", "2017-10-10T06:00:00", "2017-10-10T07:00:00", "2017-10-10T08:00:00", "2017-10-10T09:00:00", "2017-10-10T10:00:00", "2017-10-10T11:00:00", "2017-10-10T12:00:00", "2017-10-10T13:00:00", "2017-10-10T14:00:00", "2017-10-10T15:00:00", "2017-10-10T16:00:00", "2017-10-10T17:00:00", "2017-10-10T18:00:00", "2017-10-10T19:00:00", "2017-10-10T20:00:00", "2017-10-10T21:00:00", "2017-10-10T22:00:00", "2017-10-10T23:00:00", "2017-10-11T00:00:00", "2017-10-11T01:00:00", "2017-10-11T02:00:00", "2017-10-11T03:00:00", "2017-10-11T04:00:00", "2017-10-11T05:00:00", "2017-10-11T06:00:00", "2017-10-11T07:00:00", "2017-10-11T08:00:00", "2017-10-11T09:00:00", "2017-10-11T10:00:00", "2017-10-11T11:00:00", "2017-10-11T12:00:00", "2017-10-11T13:00:00", "2017-10-11T14:00:00", "2017-10-11T15:00:00", "2017-10-11T16:00:00", "2017-10-11T17:00:00", "2017-10-11T18:00:00", "2017-10-11T19:00:00", "2017-10-11T20:00:00", "2017-10-11T21:00:00", "2017-10-11T22:00:00", "2017-10-11T23:00:00", "2017-10-12T00:00:00", "2017-10-12T01:00:00", "2017-10-12T02:00:00", "2017-10-12T03:00:00", "2017-10-12T04:00:00", "2017-10-12T05:00:00", "2017-10-12T06:00:00", "2017-10-12T07:00:00", "2017-10-12T08:00:00", "2017-10-12T09:00:00", "2017-10-12T10:00:00", "2017-10-12T11:00:00", "2017-10-12T12:00:00", "2017-10-12T13:00:00", "2017-10-12T14:00:00", "2017-10-12T15:00:00", "2017-10-12T16:00:00", "2017-10-12T17:00:00", "2017-10-12T18:00:00", "2017-10-12T19:00:00", "2017-10-12T20:00:00", "2017-10-12T21:00:00", "2017-10-12T22:00:00", "2017-10-12T23:00:00", "2017-10-13T00:00:00", "2017-10-13T01:00:00", "2017-10-13T02:00:00", "2017-10-13T03:00:00", "2017-10-13T04:00:00", "2017-10-13T05:00:00", "2017-10-13T06:00:00", "2017-10-13T07:00:00", "2017-10-13T08:00:00", "2017-10-13T09:00:00", "2017-10-13T10:00:00", "2017-10-13T11:00:00", "2017-10-13T12:00:00", "2017-10-13T13:00:00", "2017-10-13T14:00:00", "2017-10-13T15:00:00", "2017-10-13T16:00:00", "2017-10-13T17:00:00", "2017-10-13T18:00:00", "2017-10-13T19:00:00", "2017-10-13T20:00:00", "2017-10-13T21:00:00", "2017-10-13T22:00:00", "2017-10-13T23:00:00", "2017-10-14T00:00:00", "2017-10-14T01:00:00", "2017-10-14T02:00:00", "2017-10-14T03:00:00", "2017-10-14T04:00:00", "2017-10-14T05:00:00", "2017-10-14T06:00:00", "2017-10-14T07:00:00", "2017-10-14T08:00:00", "2017-10-14T09:00:00", "2017-10-14T10:00:00", "2017-10-14T11:00:00", "2017-10-14T12:00:00", "2017-10-14T13:00:00", "2017-10-14T14:00:00", "2017-10-14T15:00:00", "2017-10-14T16:00:00", "2017-10-14T17:00:00", "2017-10-14T18:00:00", "2017-10-14T19:00:00", "2017-10-14T20:00:00", "2017-10-14T21:00:00", "2017-10-14T22:00:00", "2017-10-14T23:00:00", "2017-10-15T00:00:00", "2017-10-15T01:00:00", "2017-10-15T02:00:00", "2017-10-15T03:00:00", "2017-10-15T04:00:00", "2017-10-15T05:00:00", "2017-10-15T06:00:00", "2017-10-15T07:00:00", "2017-10-15T08:00:00", "2017-10-15T09:00:00", "2017-10-15T10:00:00", "2017-10-15T11:00:00", "2017-10-15T12:00:00", "2017-10-15T13:00:00", "2017-10-15T14:00:00", "2017-10-15T15:00:00", "2017-10-15T16:00:00", "2017-10-15T17:00:00", "2017-10-15T18:00:00", "2017-10-15T19:00:00", "2017-10-15T20:00:00", "2017-10-15T21:00:00", "2017-10-15T22:00:00", "2017-10-15T23:00:00", "2017-10-16T00:00:00", "2017-10-16T01:00:00", "2017-10-16T02:00:00", "2017-10-16T03:00:00", "2017-10-16T04:00:00", "2017-10-16T05:00:00", "2017-10-16T06:00:00", "2017-10-16T07:00:00", "2017-10-16T08:00:00", "2017-10-16T09:00:00", "2017-10-16T10:00:00", "2017-10-16T11:00:00", "2017-10-16T12:00:00", "2017-10-16T13:00:00", "2017-10-16T14:00:00", "2017-10-16T15:00:00", "2017-10-16T16:00:00", "2017-10-16T17:00:00", "2017-10-16T18:00:00", "2017-10-16T19:00:00", "2017-10-16T20:00:00", "2017-10-16T21:00:00", "2017-10-16T22:00:00", "2017-10-16T23:00:00", "2017-10-17T00:00:00", "2017-10-17T01:00:00", "2017-10-17T02:00:00", "2017-10-17T03:00:00", "2017-10-17T04:00:00", "2017-10-17T05:00:00", "2017-10-17T06:00:00", "2017-10-17T07:00:00", "2017-10-17T08:00:00", "2017-10-17T09:00:00", "2017-10-17T10:00:00", "2017-10-17T11:00:00", "2017-10-17T12:00:00", "2017-10-17T13:00:00", "2017-10-17T14:00:00", "2017-10-17T15:00:00", "2017-10-17T16:00:00", "2017-10-17T17:00:00", "2017-10-17T18:00:00", "2017-10-17T19:00:00", "2017-10-17T20:00:00", "2017-10-17T21:00:00", "2017-10-17T22:00:00", "2017-10-17T23:00:00", "2017-10-18T00:00:00", "2017-10-18T01:00:00", "2017-10-18T02:00:00", "2017-10-18T03:00:00", "2017-10-18T04:00:00", "2017-10-18T05:00:00", "2017-10-18T06:00:00", "2017-10-18T07:00:00", "2017-10-18T08:00:00", "2017-10-18T09:00:00", "2017-10-18T10:00:00", "2017-10-18T11:00:00", "2017-10-18T12:00:00", "2017-10-18T13:00:00", "2017-10-18T14:00:00", "2017-10-18T15:00:00", "2017-10-18T16:00:00", "2017-10-18T17:00:00", "2017-10-18T18:00:00", "2017-10-18T19:00:00", "2017-10-18T20:00:00", "2017-10-18T21:00:00", "2017-10-18T22:00:00", "2017-10-18T23:00:00", "2017-10-19T00:00:00", "2017-10-19T01:00:00", "2017-10-19T02:00:00", "2017-10-19T03:00:00", "2017-10-19T04:00:00", "2017-10-19T05:00:00", "2017-10-19T06:00:00", "2017-10-19T07:00:00", "2017-10-19T08:00:00", "2017-10-19T09:00:00", "2017-10-19T10:00:00", "2017-10-19T11:00:00", "2017-10-19T12:00:00", "2017-10-19T13:00:00", "2017-10-19T14:00:00", "2017-10-19T15:00:00", "2017-10-19T16:00:00", "2017-10-19T17:00:00", "2017-10-19T18:00:00", "2017-10-19T19:00:00", "2017-10-19T20:00:00", "2017-10-19T21:00:00", "2017-10-19T22:00:00", "2017-10-19T23:00:00", "2017-10-20T00:00:00", "2017-10-20T01:00:00", "2017-10-20T02:00:00", "2017-10-20T03:00:00", "2017-10-20T04:00:00", "2017-10-20T05:00:00", "2017-10-20T06:00:00", "2017-10-20T07:00:00", "2017-10-20T08:00:00", "2017-10-20T09:00:00", "2017-10-20T10:00:00", "2017-10-20T11:00:00", "2017-10-20T12:00:00", "2017-10-20T13:00:00", "2017-10-20T14:00:00", "2017-10-20T15:00:00", "2017-10-20T16:00:00", "2017-10-20T17:00:00", "2017-10-20T18:00:00", "2017-10-20T19:00:00", "2017-10-20T20:00:00", "2017-10-20T21:00:00", "2017-10-20T22:00:00", "2017-10-20T23:00:00", "2017-10-21T00:00:00", "2017-10-21T01:00:00", "2017-10-21T02:00:00", "2017-10-21T03:00:00", "2017-10-21T04:00:00", "2017-10-21T05:00:00", "2017-10-21T06:00:00", "2017-10-21T07:00:00", "2017-10-21T08:00:00", "2017-10-21T09:00:00", "2017-10-21T10:00:00", "2017-10-21T11:00:00", "2017-10-21T12:00:00", "2017-10-21T13:00:00", "2017-10-21T14:00:00", "2017-10-21T15:00:00", "2017-10-21T16:00:00", "2017-10-21T17:00:00", "2017-10-21T18:00:00", "2017-10-21T19:00:00", "2017-10-21T20:00:00", "2017-10-21T21:00:00", "2017-10-21T22:00:00", "2017-10-21T23:00:00", "2017-10-22T00:00:00", "2017-10-22T01:00:00", "2017-10-22T02:00:00", "2017-10-22T03:00:00", "2017-10-22T04:00:00", "2017-10-22T05:00:00", "2017-10-22T06:00:00", "2017-10-22T07:00:00", "2017-10-22T08:00:00", "2017-10-22T09:00:00", "2017-10-22T10:00:00", "2017-10-22T11:00:00", "2017-10-22T12:00:00", "2017-10-22T13:00:00", "2017-10-22T14:00:00", "2017-10-22T15:00:00", "2017-10-22T16:00:00", "2017-10-22T17:00:00", "2017-10-22T18:00:00", "2017-10-22T19:00:00", "2017-10-22T20:00:00", "2017-10-22T21:00:00", "2017-10-22T22:00:00", "2017-10-22T23:00:00", "2017-10-23T00:00:00", "2017-10-23T01:00:00", "2017-10-23T02:00:00", "2017-10-23T03:00:00", "2017-10-23T04:00:00", "2017-10-23T05:00:00", "2017-10-23T06:00:00", "2017-10-23T07:00:00", "2017-10-23T08:00:00", "2017-10-23T09:00:00", "2017-10-23T10:00:00", "2017-10-23T11:00:00", "2017-10-23T12:00:00", "2017-10-23T13:00:00", "2017-10-23T14:00:00", "2017-10-23T15:00:00", "2017-10-23T16:00:00", "2017-10-23T17:00:00", "2017-10-23T18:00:00", "2017-10-23T19:00:00", "2017-10-23T20:00:00", "2017-10-23T21:00:00", "2017-10-23T22:00:00", "2017-10-23T23:00:00", "2017-10-24T00:00:00", "2017-10-24T01:00:00", "2017-10-24T02:00:00", "2017-10-24T03:00:00", "2017-10-24T04:00:00", "2017-10-24T05:00:00", "2017-10-24T06:00:00", "2017-10-24T07:00:00", "2017-10-24T08:00:00", "2017-10-24T09:00:00", "2017-10-24T10:00:00", "2017-10-24T11:00:00", "2017-10-24T12:00:00", "2017-10-24T13:00:00", "2017-10-24T14:00:00", "2017-10-24T15:00:00", "2017-10-24T16:00:00", "2017-10-24T17:00:00", "2017-10-24T18:00:00", "2017-10-24T19:00:00", "2017-10-24T20:00:00", "2017-10-24T21:00:00", "2017-10-24T22:00:00", "2017-10-24T23:00:00", "2017-10-25T00:00:00", "2017-10-25T01:00:00", "2017-10-25T02:00:00", "2017-10-25T03:00:00", "2017-10-25T04:00:00", "2017-10-25T05:00:00", "2017-10-25T06:00:00", "2017-10-25T07:00:00", "2017-10-25T08:00:00", "2017-10-25T09:00:00", "2017-10-25T10:00:00", "2017-10-25T11:00:00", "2017-10-25T12:00:00", "2017-10-25T13:00:00", "2017-10-25T14:00:00", "2017-10-25T15:00:00", "2017-10-25T16:00:00", "2017-10-25T17:00:00", "2017-10-25T18:00:00", "2017-10-25T19:00:00", "2017-10-25T20:00:00", "2017-10-25T21:00:00", "2017-10-25T22:00:00", "2017-10-25T23:00:00", "2017-10-26T00:00:00", "2017-10-26T01:00:00", "2017-10-26T02:00:00", "2017-10-26T03:00:00", "2017-10-26T04:00:00", "2017-10-26T05:00:00", "2017-10-26T06:00:00", "2017-10-26T07:00:00", "2017-10-26T08:00:00", "2017-10-26T09:00:00", "2017-10-26T10:00:00", "2017-10-26T11:00:00", "2017-10-26T12:00:00", "2017-10-26T13:00:00", "2017-10-26T14:00:00", "2017-10-26T15:00:00", "2017-10-26T16:00:00", "2017-10-26T17:00:00", "2017-10-26T18:00:00", "2017-10-26T19:00:00", "2017-10-26T20:00:00", "2017-10-26T21:00:00", "2017-10-26T22:00:00", "2017-10-26T23:00:00", "2017-10-27T00:00:00", "2017-10-27T01:00:00", "2017-10-27T02:00:00", "2017-10-27T03:00:00", "2017-10-27T04:00:00", "2017-10-27T05:00:00", "2017-10-27T06:00:00", "2017-10-27T07:00:00", "2017-10-27T08:00:00", "2017-10-27T09:00:00", "2017-10-27T10:00:00", "2017-10-27T11:00:00", "2017-10-27T12:00:00", "2017-10-27T13:00:00", "2017-10-27T14:00:00", "2017-10-27T15:00:00", "2017-10-27T16:00:00", "2017-10-27T17:00:00", "2017-10-27T18:00:00", "2017-10-27T19:00:00", "2017-10-27T20:00:00", "2017-10-27T21:00:00", "2017-10-27T22:00:00", "2017-10-27T23:00:00", "2017-10-28T00:00:00", "2017-10-28T01:00:00", "2017-10-28T02:00:00", "2017-10-28T03:00:00", "2017-10-28T04:00:00", "2017-10-28T05:00:00", "2017-10-28T06:00:00", "2017-10-28T07:00:00", "2017-10-28T08:00:00", "2017-10-28T09:00:00", "2017-10-28T10:00:00", "2017-10-28T11:00:00", "2017-10-28T12:00:00", "2017-10-28T13:00:00", "2017-10-28T14:00:00", "2017-10-28T15:00:00", "2017-10-28T16:00:00", "2017-10-28T17:00:00", "2017-10-28T18:00:00", "2017-10-28T19:00:00", "2017-10-28T20:00:00", "2017-10-28T21:00:00", "2017-10-28T22:00:00", "2017-10-28T23:00:00", "2017-10-29T00:00:00", "2017-10-29T01:00:00", "2017-10-29T02:00:00", "2017-10-29T03:00:00", "2017-10-29T04:00:00", "2017-10-29T05:00:00", "2017-10-29T06:00:00", "2017-10-29T07:00:00", "2017-10-29T08:00:00", "2017-10-29T09:00:00", "2017-10-29T10:00:00", "2017-10-29T11:00:00", "2017-10-29T12:00:00", "2017-10-29T13:00:00", "2017-10-29T14:00:00", "2017-10-29T15:00:00", "2017-10-29T16:00:00", "2017-10-29T17:00:00", "2017-10-29T18:00:00", "2017-10-29T19:00:00", "2017-10-29T20:00:00", "2017-10-29T21:00:00", "2017-10-29T22:00:00", "2017-10-29T23:00:00", "2017-10-30T00:00:00", "2017-10-30T01:00:00", "2017-10-30T02:00:00", "2017-10-30T03:00:00", "2017-10-30T04:00:00", "2017-10-30T05:00:00", "2017-10-30T06:00:00", "2017-10-30T07:00:00", "2017-10-30T08:00:00", "2017-10-30T09:00:00", "2017-10-30T10:00:00", "2017-10-30T11:00:00", "2017-10-30T12:00:00", "2017-10-30T13:00:00", "2017-10-30T14:00:00", "2017-10-30T15:00:00", "2017-10-30T16:00:00", "2017-10-30T17:00:00", "2017-10-30T18:00:00", "2017-10-30T19:00:00", "2017-10-30T20:00:00", "2017-10-30T21:00:00", "2017-10-30T22:00:00", "2017-10-30T23:00:00", "2017-10-31T00:00:00", "2017-10-31T01:00:00", "2017-10-31T02:00:00", "2017-10-31T03:00:00", "2017-10-31T04:00:00", "2017-10-31T05:00:00", "2017-10-31T06:00:00", "2017-10-31T07:00:00", "2017-10-31T08:00:00", "2017-10-31T09:00:00", "2017-10-31T10:00:00", "2017-10-31T11:00:00", "2017-10-31T12:00:00", "2017-10-31T13:00:00", "2017-10-31T14:00:00", "2017-10-31T15:00:00", "2017-10-31T16:00:00", "2017-10-31T17:00:00", "2017-10-31T18:00:00", "2017-10-31T19:00:00", "2017-10-31T20:00:00", "2017-10-31T21:00:00", "2017-10-31T22:00:00", "2017-10-31T23:00:00", "2017-11-01T00:00:00", "2017-11-01T01:00:00", "2017-11-01T02:00:00", "2017-11-01T03:00:00", "2017-11-01T04:00:00", "2017-11-01T05:00:00", "2017-11-01T06:00:00", "2017-11-01T07:00:00", "2017-11-01T08:00:00", "2017-11-01T09:00:00", "2017-11-01T10:00:00", "2017-11-01T11:00:00", "2017-11-01T12:00:00", "2017-11-01T13:00:00", "2017-11-01T14:00:00", "2017-11-01T15:00:00", "2017-11-01T16:00:00", "2017-11-01T17:00:00", "2017-11-01T18:00:00", "2017-11-01T19:00:00", "2017-11-01T20:00:00", "2017-11-01T21:00:00", "2017-11-01T22:00:00", "2017-11-01T23:00:00", "2017-11-02T00:00:00", "2017-11-02T01:00:00", "2017-11-02T02:00:00", "2017-11-02T03:00:00", "2017-11-02T04:00:00", "2017-11-02T05:00:00", "2017-11-02T06:00:00", "2017-11-02T07:00:00", "2017-11-02T08:00:00", "2017-11-02T09:00:00", "2017-11-02T10:00:00", "2017-11-02T11:00:00", "2017-11-02T12:00:00", "2017-11-02T13:00:00", "2017-11-02T14:00:00", "2017-11-02T15:00:00", "2017-11-02T16:00:00", "2017-11-02T17:00:00", "2017-11-02T18:00:00", "2017-11-02T19:00:00", "2017-11-02T20:00:00", "2017-11-02T21:00:00", "2017-11-02T22:00:00", "2017-11-02T23:00:00", "2017-11-03T00:00:00", "2017-11-03T01:00:00", "2017-11-03T02:00:00", "2017-11-03T03:00:00", "2017-11-03T04:00:00", "2017-11-03T05:00:00", "2017-11-03T06:00:00", "2017-11-03T07:00:00", "2017-11-03T08:00:00", "2017-11-03T09:00:00", "2017-11-03T10:00:00", "2017-11-03T11:00:00", "2017-11-03T12:00:00", "2017-11-03T13:00:00", "2017-11-03T14:00:00", "2017-11-03T15:00:00", "2017-11-03T16:00:00", "2017-11-03T17:00:00", "2017-11-03T18:00:00", "2017-11-03T19:00:00", "2017-11-03T20:00:00", "2017-11-03T21:00:00", "2017-11-03T22:00:00", "2017-11-03T23:00:00", "2017-11-04T00:00:00", "2017-11-04T01:00:00", "2017-11-04T02:00:00", "2017-11-04T03:00:00", "2017-11-04T04:00:00", "2017-11-04T05:00:00", "2017-11-04T06:00:00", "2017-11-04T07:00:00", "2017-11-04T08:00:00", "2017-11-04T09:00:00", "2017-11-04T10:00:00", "2017-11-04T11:00:00", "2017-11-04T12:00:00", "2017-11-04T13:00:00", "2017-11-04T14:00:00", "2017-11-04T15:00:00", "2017-11-04T16:00:00", "2017-11-04T17:00:00", "2017-11-04T18:00:00", "2017-11-04T19:00:00", "2017-11-04T20:00:00", "2017-11-04T21:00:00", "2017-11-04T22:00:00", "2017-11-04T23:00:00"], "y": [-0.8326122581264674, -0.8245731241014101, -0.5119070333531954, -1.0490956960391944, -0.7765296235945327, -0.4798461490746994, -0.7203513372408646, -1.16930046421141, -1.642367358340416, -1.2735226161552446, -1.393823036149194, -1.1853789148031688, -1.2896008842053592, -1.594228023470161, -1.2174397990816652, -1.2896008842053592, -1.7865938767660714, -1.1612613301863524, -1.4579449872478305, -1.3216619510254994, -1.8908158461682611, -1.5861888894451035, -1.834733211636326, -1.3857839021241365, -1.0891958968843918, -0.8967343917667486, -0.6402465873722026, -0.7203513372408646, -1.1051785131127734, -0.9368345926119466, -1.1533178479830282, -1.1132176471378303, -1.3376445672538813, -1.417844786402633, -1.626288907748657, -1.530106072371524, -1.3456837012789384, -1.5861888894451035, -1.2896008842053592, -1.626288907748657, -1.8908158461682611, -1.4017665183525176, -1.498045005551384, -1.16930046421141, -1.2094006650566076, -1.5861888894451035, -1.6583499745687975, -1.393823036149194, -1.498045005551384, -1.6583499745687975, -1.1773395982364676, -1.850715827864708, -1.6102106396985425, -1.530106072371524, -1.353722835303997, -1.698449992872351, -1.9309160470134588, -2.1233775521311022, -2.195538637254796, -1.9629771138335996, -1.722471925667434, -2.0591599492107324, -1.530106072371524, -1.5140278043214097, -1.802672144816185, -1.3055835004337406, -0.8085905078730294, -0.7684904895694753, -1.2815617501803018, -0.9207561420201884, -0.9448737266370036, -0.8566343734631953, -1.393823036149194, -0.7283904712659219, -0.6562292036005835, -1.024978293964022, -0.7684904895694753, -0.8485952394381386, -0.7283904712659219, -0.912812659816863, -1.0330174279890805, -1.1212569637045329, -1.024978293964022, -1.2896008842053592, -1.3055835004337406, -1.369705451532377, -1.0490956960391944, -1.1132176471378303, -1.2414615493351049, -1.16930046421141, -0.8485952394381386, -0.6322074533471453, -0.9849737449405572, -0.7844731057978568, -1.2495006833601612, -0.75241222151936, -0.5439679176316915, -0.8326122581264674, -0.6882900878790795, -0.6963292219041375, -0.8326122581264674, -0.49592441712481405, -0.9608563428653856, -0.75241222151936, -0.87271264151331, -1.1934180488282264, -1.057039178242518, -1.1612613301863524, -1.2094006650566076, -1.0330174279890805, -1.1132176471378303, -0.760451355544418, -1.3216619510254994, -0.8646735074882519, -0.5921074350435913, -0.49592441712481405, -1.2815617501803018, -1.1853789148031688, -1.0009565437105832, -0.672307471650698, -0.6882900878790795, -0.5760288019101876, -0.4157240154344182, -0.7123118381325183, -0.7844731057978568, -0.4477848997129143, -0.2072800766300381, -1.3617619693290532, -0.8005513738479707, -1.8266938950696243, -1.017034811760698, -0.8646735074882519, -1.313622634458798, -1.0089956777356408, -1.2414615493351049, -0.87271264151331, -1.2654834821301872, -1.0089956777356408, -0.8005513738479707, -0.5921074350435913, -0.6481900695755262, -0.6481900695755262, -0.6080900512719735, -0.6802509538540216, -0.46386353284631865, -0.3917022651809788, -0.3917022651809788, -1.1934180488282264, -0.8245731241014101, -0.49592441712481405, -1.2896008842053592, -0.9769346109155, -0.8245731241014101, -0.8406517572348139, -1.0009565437105832, -0.912812659816863, -0.8566343734631953, -0.8967343917667486, -0.8485952394381386, -1.554127822624963, -1.3456837012789384, -0.7364296052909792, -0.8967343917667486, -0.49592441712481405, -0.5520070516567489, -0.7765296235945327, -0.7203513372408646, -0.7283904712659219, -0.3996457473843029, -0.2955192472622022, -0.1351188089646996, -0.8326122581264674, -0.03893579104592302, -0.2233583446801521, -0.3034627294655263, -0.3917022651809788, -0.6963292219041375, -1.0410565620141363, -1.369705451532377, -0.8485952394381386, -0.9769346109155, -0.8406517572348139, -0.6802509538540216, -1.0490956960391944, -0.8165339900763529, -0.5840683010185334, -1.417844786402633, -1.024978293964022, -1.393823036149194, -1.2654834821301872, -0.4718070150496414, -0.6642683376256407, -0.7283904712659219, -0.6241683193220867, -0.4157240154344182, -0.6562292036005835, -1.024978293964022, -0.9688954768904426, -0.5119070333531954, -0.7925122398229141, -0.8005513738479707, -1.1212569637045329, -1.1612613301863524, -0.7765296235945327, -1.5702062732167223, -0.952817208840328, -0.7203513372408646, -0.5840683010185334, -1.3216619510254994, -0.8646735074882519, -1.057039178242518, -1.0811567628593346, -0.9368345926119466, -1.2013615310315506, -1.5861888894451035, -1.0811567628593346, -0.7765296235945327, -0.5439679176316915, -0.4237631494594755, -0.9287952760452448, -0.6882900878790795, -0.5439679176316915, -0.8566343734631953, -1.1533178479830282, -1.3857839021241365, -1.4579449872478305, -1.449905853222773, -1.3216619510254994, -1.6182497737236, -1.369705451532377, -1.858754961889765, -1.16930046421141, -1.4579449872478305, -0.904773525791806, -1.1773395982364676, -1.0330174279890805, -1.1132176471378303, -1.073117628834277, -1.2735226161552446, -1.2094006650566076, -0.9929172271438814, -0.9849737449405572, -0.8485952394381386, -1.0089956777356408, -1.073117628834277, -1.017034811760698, -0.9287952760452448, -0.9448737266370036, -1.4017665183525176, -1.858754961889765, -1.7465893277426063, -2.0431773329823506, -1.706489309439053, -1.8266938950696243, -1.5460886885999054, -1.5059884877547085, -1.810711278841243, -1.2094006650566076, -1.778650394562747, -1.698449992872351, -1.8988551627349632, -1.1853789148031688, -1.449905853222773, -1.0410565620141363, -1.0490956960391944, -0.9207561420201884, -0.7123118381325183, -0.8807517755383679, -0.6562292036005835, -0.75241222151936, -0.4798461490746994, -1.0891958968843918, -1.0009565437105832, -1.1612613301863524, -1.1051785131127734, -1.2975443664086836, -1.2495006833601612, -0.9608563428653856, -0.9849737449405572, -0.8886952577416913, -1.3055835004337406, -0.6963292219041375, -0.6882900878790795, -0.7203513372408646, -1.024978293964022, -0.9448737266370036, -1.073117628834277, -0.8646735074882519, -0.7844731057978568, -0.5119070333531954, -0.45582439882126063, -1.1853789148031688, -0.6322074533471453, -0.8165339900763529, -0.7283904712659219, -0.760451355544418, -0.87271264151331, -0.9207561420201884, -1.2334224153100466, -0.9769346109155, -1.4819667375012688, -1.017034811760698, -1.417844786402633, -0.9929172271438814, -1.393823036149194, -0.7844731057978568, -0.8085905078730294, -0.8646735074882519, -1.0009565437105832, -1.3617619693290532, -0.9849737449405572, -0.8245731241014101, -0.7684904895694753, -0.7043687210124839, -0.5119070333531954, -0.6161291852970301, -0.8245731241014101, -0.7043687210124839, -0.9929172271438814, -0.7203513372408646, -0.8967343917667486, -1.1533178479830282, -1.2654834821301872, -1.369705451532377, -1.3216619510254994, -1.1372395799329138, -2.131321034334427, -2.588309569142496, -3.213642024451392, -3.0372588786546855, -2.788714556463463, -2.6765490135871266, -2.756653580914145, -2.909014976457412, -2.868914775612214, -2.548209368297298, -1.5861888894451035, -0.7684904895694753, -0.4878852830997567, -0.5921074350435913, -0.9608563428653856, -1.0009565437105832, -0.7283904712659219, -1.057039178242518, -0.8245731241014101, -0.9688954768904426, -1.0971393790877149, -0.9368345926119466, -0.8485952394381386, -0.9688954768904426, -0.904773525791806, -0.9287952760452448, -1.5220669383464671, -0.8646735074882519, -1.1132176471378303, -0.7844731057978568, -1.0009565437105832, -0.8566343734631953, -0.6882900878790795, -0.3596413809024834, -0.49592441712481405, -0.4076848814093609, -0.26336271116197296, -0.6080900512719735, -0.5600461856818062, -0.5279853014033101, -0.6322074533471453, -0.7844731057978568, -0.8245731241014101, -1.0891958968843918, -0.9608563428653856, -1.017034811760698, -0.8085905078730294, -1.2094006650566076, -0.8807517755383679, -0.6882900878790795, -1.024978293964022, -0.8326122581264674, -1.0330174279890805, -0.8005513738479707, -0.9368345926119466, -0.8165339900763529, -0.7364296052909792, -0.6802509538540216, -0.5680853197068635, -0.3917022651809788, -0.46386353284631865, -0.9368345926119466, -0.5600461856818062, -0.7444687393160366, -0.672307471650698, -0.8967343917667486, -1.1612613301863524, -1.2013615310315506, -0.912812659816863, -1.2735226161552446, -0.9688954768904426, -1.024978293964022, -1.0650783122675758, -1.2013615310315506, -2.387904490550706, -2.1554386189512424, -2.259660588353433, -2.1233775521311022, -2.556248593593178, -2.572326861643292, -2.548209368297298, -2.4600656669452223, -2.3638827402972677, -2.115338418106045, -1.8988551627349632, -2.0191555827289123, -2.06719908323579, -2.163382101154567, -2.195538637254796, -2.3237825394520697, -2.6364488127419285, -2.580270343846616, -2.7165533800689468, -2.588309569142496, -2.556248593593178, -2.620370544691814, -2.307704271401955, -2.2275997040749367, -2.2996651373768975, -2.1393601683594836, -1.9469943150635736, -1.90689429676002, -1.979055381883714, -2.2917216551735735, -2.3959436245757635, -2.3318216734771267, -2.187499503229739, -2.1233775521311022, -1.9549377972668984, -2.187499503229739, -2.259660588353433, -2.187499503229739, -2.235543186278261, -2.235543186278261, -2.572326861643292, -2.540265886093974, -2.6364488127419285, -2.540265886093974, -2.708609989136445, -2.788714556463463, -2.6765490135871266, -2.532226752068917, -2.2516214543283755, -1.6583499745687975, -1.3857839021241365, -1.2013615310315506, -1.3376445672538813, -1.634328224315359, -1.634328224315359, -1.6904108588472937, -1.626288907748657, -1.2735226161552446, -1.417844786402633, -1.2975443664086836, -1.4339230544527468, -0.8646735074882519, -0.6161291852970301, -0.9608563428653856, -1.1212569637045329, -1.1853789148031688, -1.2254789331067226, -1.369705451532377, -1.802672144816185, -1.3456837012789384, -1.722471925667434, -0.8485952394381386, -1.2174397990816652, -1.1372395799329138, -0.7364296052909792, -0.8646735074882519, -0.9608563428653856, -1.017034811760698, -0.9849737449405572, -1.073117628834277, -1.017034811760698, -0.7364296052909792, -0.3756239971308648, -0.8245731241014101, -0.6161291852970301, -0.5360244354283674, -0.6001465690686486, -0.9608563428653856, -0.8326122581264674, -1.698449992872351, -1.16930046421141, -1.0330174279890805, -1.2896008842053592, -1.5621669566500205, -0.7765296235945327, -1.5781495728784016, -1.3376445672538813, -0.8566343734631953, -1.313622634458798, -1.073117628834277, -1.1452787139579712, -0.952817208840328, -1.057039178242518, -0.9608563428653856, -0.8807517755383679, -0.7844731057978568, -0.38366313115592215, -1.7865938767660714, -1.554127822624963, -0.3917022651809788, -0.4477848997129143, -0.6882900878790795, -0.9769346109155, -0.952817208840328, -0.952817208840328, -0.7444687393160366, -1.4017665183525176, -0.9849737449405572, -0.7283904712659219, -0.75241222151936, -1.4579449872478305, -1.0009565437105832, -1.2013615310315506, -0.9849737449405572, -0.9929172271438814, -2.0591599492107324, -1.0330174279890805, -0.7925122398229141, -0.9849737449405572, -0.7123118381325183, -0.7765296235945327, -0.7043687210124839, -0.8005513738479707, -0.5520070516567489, -0.7283904712659219, -0.8005513738479707, -0.9368345926119466, -1.1773395982364676, -1.1372395799329138, -1.1533178479830282, -1.417844786402633, -1.1853789148031688, -1.0650783122675758, -0.952817208840328, -1.6022671574952183, -1.2815617501803018, -0.9688954768904426, -0.9287952760452448, -1.1452787139579712, -0.904773525791806, -0.8326122581264674, -0.6963292219041375, -0.8165339900763529, -0.6322074533471453, -0.3275804966239873, -1.2334224153100466, -0.2554192289586488, -0.43974576568785695, -0.6001465690686486, -0.8886952577416913, -0.8165339900763529, -1.073117628834277, -1.1853789148031688, -1.257539999926863, -0.9688954768904426, -0.9287952760452448, -0.7283904712659219, -0.8485952394381386, -0.8165339900763529, -0.8807517755383679, -1.1212569637045329, -0.7444687393160366, -1.024978293964022, -0.9849737449405572, -1.1452787139579712, -1.1372395799329138, -0.8566343734631953, -0.8005513738479707, -0.31954099751564097, -0.7283904712659219, -0.5760288019101876, -0.7364296052909792, -0.5760288019101876, -0.75241222151936, -0.7925122398229141, -0.9688954768904426, -1.1212569637045329, -1.0410565620141363, -1.1612613301863524, -0.8886952577416913, -0.8005513738479707, -0.7925122398229141, -1.0089956777356408, -0.9287952760452448, -1.1934180488282264, -0.8646735074882519, -1.0891958968843918, -0.9368345926119466, -0.8406517572348139, -0.9608563428653856, -0.7925122398229141, -0.8807517755383679, -0.5600461856818062, -0.43974576568785695, -0.4798461490746994, -0.5760288019101876, -0.43180228348453287, -0.7203513372408646, -0.8807517755383679, -0.8886952577416913, -1.1533178479830282, -0.9929172271438814, -1.1372395799329138, -1.1212569637045329, -0.8566343734631953, -0.9287952760452448, -1.0650783122675758, -0.8485952394381386, -0.5840683010185334, -0.5760288019101876, -0.8245731241014101, -1.1292002633662122, -1.0811567628593346, -1.1612613301863524, -1.1533178479830282, -0.9368345926119466, -0.5680853197068635, -0.7364296052909792, -0.7925122398229141, -0.6562292036005835, -0.9368345926119466, -0.8085905078730294, -1.017034811760698, -0.9769346109155, -1.0891958968843918, -1.417844786402633, -1.257539999926863, -1.1612613301863524, -1.1452787139579712, -1.0410565620141363, -1.4900058715263265, -1.0891958968843918, -1.0891958968843918, -0.7283904712659219, -1.0971393790877149, -1.1533178479830282, -1.2094006650566076, -1.1051785131127734, -0.87271264151331, -0.8005513738479707, -0.5600461856818062, -0.8485952394381386, -0.5360244354283674, -0.8406517572348139, -0.6402465873722026, -0.8085905078730294, -0.9929172271438814, -1.1292002633662122, -1.257539999926863, -1.1292002633662122, -1.1051785131127734, -1.0330174279890805, -1.5460886885999054, -1.6022671574952183, -1.802672144816185, -0.8807517755383679, -1.6663891085938551, -1.6583499745687975, -0.9608563428653856, -0.8165339900763529, -1.329701085050557, -0.952817208840328, -0.87271264151331, -0.9448737266370036, -0.5199461673782528, -0.5760288019101876, -0.6963292219041375, -0.4718070150496414, -0.45582439882126063, -0.7444687393160366, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -0.9929172271438814, -1.0089956777356408, -1.017034811760698, -1.0330174279890805, -0.8967343917667486, -0.9769346109155, -0.8085905078730294, -0.5439679176316915, -0.6161291852970301, -0.6963292219041375, -0.6080900512719735, -0.9769346109155, -0.6402465873722026, -1.3216619510254994, -1.313622634458798, -1.842772345661384, -1.810711278841243, -0.8326122581264674, -0.7283904712659219, -0.33552397882731144, -0.8326122581264674, -0.8165339900763529, -0.4477848997129143, -0.5760288019101876, -0.7765296235945327, -0.9769346109155, -1.1292002633662122, -1.1533178479830282, -1.3777445855574346, -1.417844786402633, -1.1533178479830282, -1.024978293964022, -1.1051785131127734, -1.42588392042769, -0.8326122581264674, -1.1051785131127734, -0.5600461856818062, -0.9769346109155, -0.8165339900763529, -1.0891958968843918, -0.9207561420201884, -0.8085905078730294, -0.5279853014033101, -0.19924057752169175, -0.8165339900763529, -0.46386353284631865, -0.4798461490746994, -0.43974576568785695, -0.8326122581264674, -0.8406517572348139, -0.9448737266370036, -1.473927603476212, -1.3376445672538813, -1.2174397990816652, -1.2254789331067226, -1.1533178479830282, -0.904773525791806, -0.8005513738479707, -0.5119070333531954, -1.3216619510254994, -0.7684904895694753, -1.024978293964022, -0.8566343734631953, -1.017034811760698, -1.024978293964022, -0.46386353284631865, -0.6802509538540216, -0.27140184518703026, -0.46386353284631865, -0.46386353284631865, -0.46386353284631865, -0.9769346109155, -1.369705451532377, -1.0089956777356408, -1.329701085050557, -1.1292002633662122, -1.42588392042769, -1.1051785131127734, -1.0330174279890805, -1.073117628834277, -0.952817208840328, -1.1934180488282264, -0.5119070333531954, -0.6402465873722026, -0.3756239971308648, -0.7925122398229141, -0.8886952577416913, -0.904773525791806, -0.9929172271438814, -1.017034811760698, -0.8807517755383679, -0.6481900695755262, -0.8165339900763529, -1.1934180488282264, -0.33552397882731144, -0.3435631128523688, -0.5680853197068635]}, {"name": "HUFL_prediction", "type": "scatter", "x": ["2017-11-01T00:00:00", "2017-11-01T01:00:00", "2017-11-01T02:00:00", "2017-11-01T03:00:00", "2017-11-01T04:00:00", "2017-11-01T05:00:00", "2017-11-01T06:00:00", "2017-11-01T07:00:00", "2017-11-01T08:00:00", "2017-11-01T09:00:00", "2017-11-01T10:00:00", "2017-11-01T11:00:00", "2017-11-01T12:00:00", "2017-11-01T13:00:00", "2017-11-01T14:00:00", "2017-11-01T15:00:00", "2017-11-01T16:00:00", "2017-11-01T17:00:00", "2017-11-01T18:00:00", "2017-11-01T19:00:00", "2017-11-01T20:00:00", "2017-11-01T21:00:00", "2017-11-01T22:00:00", "2017-11-01T23:00:00", "2017-11-02T00:00:00", "2017-11-02T01:00:00", "2017-11-02T02:00:00", "2017-11-02T03:00:00", "2017-11-02T04:00:00", "2017-11-02T05:00:00", "2017-11-02T06:00:00", "2017-11-02T07:00:00", "2017-11-02T08:00:00", "2017-11-02T09:00:00", "2017-11-02T10:00:00", "2017-11-02T11:00:00", "2017-11-02T12:00:00", "2017-11-02T13:00:00", "2017-11-02T14:00:00", "2017-11-02T15:00:00", "2017-11-02T16:00:00", "2017-11-02T17:00:00", "2017-11-02T18:00:00", "2017-11-02T19:00:00", "2017-11-02T20:00:00", "2017-11-02T21:00:00", "2017-11-02T22:00:00", "2017-11-02T23:00:00", "2017-11-03T00:00:00", "2017-11-03T01:00:00", "2017-11-03T02:00:00", "2017-11-03T03:00:00", "2017-11-03T04:00:00", "2017-11-03T05:00:00", "2017-11-03T06:00:00", "2017-11-03T07:00:00", "2017-11-03T08:00:00", "2017-11-03T09:00:00", "2017-11-03T10:00:00", "2017-11-03T11:00:00", "2017-11-03T12:00:00", "2017-11-03T13:00:00", "2017-11-03T14:00:00", "2017-11-03T15:00:00", "2017-11-03T16:00:00", "2017-11-03T17:00:00", "2017-11-03T18:00:00", "2017-11-03T19:00:00", "2017-11-03T20:00:00", "2017-11-03T21:00:00", "2017-11-03T22:00:00", "2017-11-03T23:00:00", "2017-11-04T00:00:00", "2017-11-04T01:00:00", "2017-11-04T02:00:00", "2017-11-04T03:00:00", "2017-11-04T04:00:00", "2017-11-04T05:00:00", "2017-11-04T06:00:00", "2017-11-04T07:00:00", "2017-11-04T08:00:00", "2017-11-04T09:00:00", "2017-11-04T10:00:00", "2017-11-04T11:00:00", "2017-11-04T12:00:00", "2017-11-04T13:00:00", "2017-11-04T14:00:00", "2017-11-04T15:00:00", "2017-11-04T16:00:00", "2017-11-04T17:00:00", "2017-11-04T18:00:00", "2017-11-04T19:00:00", "2017-11-04T20:00:00", "2017-11-04T21:00:00", "2017-11-04T22:00:00", "2017-11-04T23:00:00"], "y": [-1.114380121231079, -1.101207613945007, -1.043063402175903, -1.2277408838272097, -1.2993509769439695, -1.2025686502456665, -1.0498031377792358, -1.264639377593994, -1.3147271871566772, -1.0548253059387207, -1.3030890226364136, -1.119853138923645, -1.0638914108276367, -0.888178288936615, -1.1275345087051392, -0.9416507482528688, -1.0817251205444336, -0.8538972735404968, -0.7802585363388062, -0.8119070529937744, -0.970384120941162, -0.7368140816688538, -0.7150887846946716, -0.9615399241447448, -1.247339367866516, -1.036447525024414, -1.129603624343872, -1.0258170366287231, -1.168280005455017, -1.1466639041900637, -1.041293382644653, -1.2883615493774414, -1.356373310089111, -1.1307183504104614, -1.0854873657226562, -1.1897168159484863, -1.1143451929092407, -1.06787371635437, -1.1274898052215576, -1.0223543643951416, -1.0048792362213137, -0.9456254243850708, -0.9219006299972534, -0.9371864795684814, -0.9628844261169434, -0.8253697156906128, -0.8615092635154724, -0.9975356459617616, -1.1939489841461182, -1.1691333055496216, -1.226689696311951, -1.1902910470962524, -1.1685158014297483, -1.3183757066726685, -1.10786771774292, -1.225210189819336, -1.2399563789367676, -1.1022696495056152, -1.134260654449463, -1.1681627035140991, -1.1969060897827148, -1.1295216083526611, -1.0715348720550537, -0.965918242931366, -1.2027311325073242, -1.022760987281799, -0.988616704940796, -0.8692268133163452, -0.9481931924819946, -0.9726306200027466, -1.0474603176116943, -1.0022369623184204, -1.080289602279663, -1.0300503969192505, -1.2092972993850708, -1.3480703830718994, -1.332614541053772, -1.2727248668670654, -1.087273359298706, -1.3096799850463867, -1.3441534042358398, -1.3095020055770874, -1.2614798545837402, -1.1490592956542969, -1.1480730772018433, -1.190475344657898, -1.103896975517273, -1.1113706827163696, -1.1662001609802246, -0.7988014221191406, -0.7146113514900208, -0.9570643305778505, -0.7842862606048584, -0.6960272789001465, -0.926305890083313, -0.9584517478942872]}, {"name": "HUFL_prediction", "type": "scatter", "x": ["2017-10-28T00:00:00", "2017-10-28T01:00:00", "2017-10-28T02:00:00", "2017-10-28T03:00:00", "2017-10-28T04:00:00", "2017-10-28T05:00:00", "2017-10-28T06:00:00", "2017-10-28T07:00:00", "2017-10-28T08:00:00", "2017-10-28T09:00:00", "2017-10-28T10:00:00", "2017-10-28T11:00:00", "2017-10-28T12:00:00", "2017-10-28T13:00:00", "2017-10-28T14:00:00", "2017-10-28T15:00:00", "2017-10-28T16:00:00", "2017-10-28T17:00:00", "2017-10-28T18:00:00", "2017-10-28T19:00:00", "2017-10-28T20:00:00", "2017-10-28T21:00:00", "2017-10-28T22:00:00", "2017-10-28T23:00:00", "2017-10-29T00:00:00", "2017-10-29T01:00:00", "2017-10-29T02:00:00", "2017-10-29T03:00:00", "2017-10-29T04:00:00", "2017-10-29T05:00:00", "2017-10-29T06:00:00", "2017-10-29T07:00:00", "2017-10-29T08:00:00", "2017-10-29T09:00:00", "2017-10-29T10:00:00", "2017-10-29T11:00:00", "2017-10-29T12:00:00", "2017-10-29T13:00:00", "2017-10-29T14:00:00", "2017-10-29T15:00:00", "2017-10-29T16:00:00", "2017-10-29T17:00:00", "2017-10-29T18:00:00", "2017-10-29T19:00:00", "2017-10-29T20:00:00", "2017-10-29T21:00:00", "2017-10-29T22:00:00", "2017-10-29T23:00:00", "2017-10-30T00:00:00", "2017-10-30T01:00:00", "2017-10-30T02:00:00", "2017-10-30T03:00:00", "2017-10-30T04:00:00", "2017-10-30T05:00:00", "2017-10-30T06:00:00", "2017-10-30T07:00:00", "2017-10-30T08:00:00", "2017-10-30T09:00:00", "2017-10-30T10:00:00", "2017-10-30T11:00:00", "2017-10-30T12:00:00", "2017-10-30T13:00:00", "2017-10-30T14:00:00", "2017-10-30T15:00:00", "2017-10-30T16:00:00", "2017-10-30T17:00:00", "2017-10-30T18:00:00", "2017-10-30T19:00:00", "2017-10-30T20:00:00", "2017-10-30T21:00:00", "2017-10-30T22:00:00", "2017-10-30T23:00:00", "2017-10-31T00:00:00", "2017-10-31T01:00:00", "2017-10-31T02:00:00", "2017-10-31T03:00:00", "2017-10-31T04:00:00", "2017-10-31T05:00:00", "2017-10-31T06:00:00", "2017-10-31T07:00:00", "2017-10-31T08:00:00", "2017-10-31T09:00:00", "2017-10-31T10:00:00", "2017-10-31T11:00:00", "2017-10-31T12:00:00", "2017-10-31T13:00:00", "2017-10-31T14:00:00", "2017-10-31T15:00:00", "2017-10-31T16:00:00", "2017-10-31T17:00:00", "2017-10-31T18:00:00", "2017-10-31T19:00:00", "2017-10-31T20:00:00", "2017-10-31T21:00:00", "2017-10-31T22:00:00", "2017-10-31T23:00:00"], "y": [-0.8723561763763428, -1.0079503059387207, -0.8817741274833679, -0.9090412855148317, -1.0054376125335691, -1.1146657466888428, -1.0298707485198977, -1.025174856185913, -1.0227417945861816, -1.0035077333450315, -1.012802243232727, -0.9462154507637024, -0.9412500858306884, -0.9205936789512634, -0.9151426553726196, -0.770983874797821, -0.698224663734436, -0.5706535577774048, -0.5476498603820801, -0.5802266001701355, -0.7755035161972046, -0.6567275524139404, -0.651357889175415, -0.7750712037086487, -0.8706333637237549, -1.0629767179489136, -1.0015897750854492, -0.9681465029716492, -1.0077489614486694, -1.0327340364456177, -0.856086015701294, -1.014698505401611, -0.9909955859184264, -0.8547475934028625, -0.9331160187721252, -0.8523315191268921, -1.0531333684921265, -0.8625011444091797, -0.7967876195907593, -0.7197776436805725, -0.7641527652740479, -0.6412737965583801, -0.5404506325721741, -0.7036144733428955, -0.5735074281692505, -0.6679597496986389, -0.7460055351257324, -0.8418104648590088, -0.8829984664916992, -1.097349762916565, -1.052159547805786, -0.9907715916633606, -1.0889906883239746, -1.0586446523666382, -1.0137771368026731, -1.0937504768371582, -1.0829766988754272, -1.01411235332489, -0.981816291809082, -0.991590678691864, -1.1610442399978638, -1.079716682434082, -0.99793803691864, -0.8740617632865906, -0.944052755832672, -0.7086359858512878, -0.7679603695869446, -0.7754336595535278, -0.8193541765213013, -0.8072113990783691, -0.765154242515564, -0.952358603477478, -1.144295573234558, -1.053515911102295, -1.000349521636963, -1.0941377878189087, -1.2575812339782717, -1.1154881715774536, -1.0541685819625854, -1.1759408712387085, -1.1063493490219116, -1.0004912614822388, -1.072750687599182, -1.033949613571167, -0.9886517524719238, -0.9839311838150024, -0.8766805529594421, -0.959240734577179, -0.8343586325645447, -0.7620306015014648, -0.6676030158996582, -0.744025707244873, -0.7385876178741455, -0.6955353021621704, -0.8379168510437012, -0.8545705080032349]}, {"name": "HUFL_prediction", "type": "scatter", "x": ["2017-10-24T00:00:00", "2017-10-24T01:00:00", "2017-10-24T02:00:00", "2017-10-24T03:00:00", "2017-10-24T04:00:00", "2017-10-24T05:00:00", "2017-10-24T06:00:00", "2017-10-24T07:00:00", "2017-10-24T08:00:00", "2017-10-24T09:00:00", "2017-10-24T10:00:00", "2017-10-24T11:00:00", "2017-10-24T12:00:00", "2017-10-24T13:00:00", "2017-10-24T14:00:00", "2017-10-24T15:00:00", "2017-10-24T16:00:00", "2017-10-24T17:00:00", "2017-10-24T18:00:00", "2017-10-24T19:00:00", "2017-10-24T20:00:00", "2017-10-24T21:00:00", "2017-10-24T22:00:00", "2017-10-24T23:00:00", "2017-10-25T00:00:00", "2017-10-25T01:00:00", "2017-10-25T02:00:00", "2017-10-25T03:00:00", "2017-10-25T04:00:00", "2017-10-25T05:00:00", "2017-10-25T06:00:00", "2017-10-25T07:00:00", "2017-10-25T08:00:00", "2017-10-25T09:00:00", "2017-10-25T10:00:00", "2017-10-25T11:00:00", "2017-10-25T12:00:00", "2017-10-25T13:00:00", "2017-10-25T14:00:00", "2017-10-25T15:00:00", "2017-10-25T16:00:00", "2017-10-25T17:00:00", "2017-10-25T18:00:00", "2017-10-25T19:00:00", "2017-10-25T20:00:00", "2017-10-25T21:00:00", "2017-10-25T22:00:00", "2017-10-25T23:00:00", "2017-10-26T00:00:00", "2017-10-26T01:00:00", "2017-10-26T02:00:00", "2017-10-26T03:00:00", "2017-10-26T04:00:00", "2017-10-26T05:00:00", "2017-10-26T06:00:00", "2017-10-26T07:00:00", "2017-10-26T08:00:00", "2017-10-26T09:00:00", "2017-10-26T10:00:00", "2017-10-26T11:00:00", "2017-10-26T12:00:00", "2017-10-26T13:00:00", "2017-10-26T14:00:00", "2017-10-26T15:00:00", "2017-10-26T16:00:00", "2017-10-26T17:00:00", "2017-10-26T18:00:00", "2017-10-26T19:00:00", "2017-10-26T20:00:00", "2017-10-26T21:00:00", "2017-10-26T22:00:00", "2017-10-26T23:00:00", "2017-10-27T00:00:00", "2017-10-27T01:00:00", "2017-10-27T02:00:00", "2017-10-27T03:00:00", "2017-10-27T04:00:00", "2017-10-27T05:00:00", "2017-10-27T06:00:00", "2017-10-27T07:00:00", "2017-10-27T08:00:00", "2017-10-27T09:00:00", "2017-10-27T10:00:00", "2017-10-27T11:00:00", "2017-10-27T12:00:00", "2017-10-27T13:00:00", "2017-10-27T14:00:00", "2017-10-27T15:00:00", "2017-10-27T16:00:00", "2017-10-27T17:00:00", "2017-10-27T18:00:00", "2017-10-27T19:00:00", "2017-10-27T20:00:00", "2017-10-27T21:00:00", "2017-10-27T22:00:00", "2017-10-27T23:00:00"], "y": [-0.7573708295822144, -1.11812162399292, -1.0742759704589844, -1.0217924118041992, -1.162425875663757, -1.1169286966323853, -1.0311871767044067, -1.1504594087600708, -1.1491039991378784, -0.9933449029922484, -1.100246548652649, -0.9200284481048584, -1.0303630828857422, -0.9863337874412536, -0.7568250894546509, -0.7537345886230469, -0.7747013568878174, -0.680601954460144, -0.5827189087867737, -0.7270277738571167, -0.6652269959449768, -0.6808099150657654, -0.6788861751556396, -0.9570900201797484, -1.0581060647964478, -1.1980540752410889, -1.09260356426239, -0.9993208646774292, -1.181342840194702, -1.0971959829330444, -1.0739070177078247, -1.1892178058624268, -1.205440878868103, -1.054973602294922, -0.979215443134308, -0.9992469549179076, -0.993194341659546, -0.945497751235962, -0.7733556032180786, -0.8824097514152527, -0.9184880256652832, -0.6832143068313599, -0.5546004772186279, -0.7443109154701233, -0.8909257650375366, -0.72032630443573, -0.7049378752708435, -0.8605510592460632, -1.2145494222640991, -1.0472010374069214, -1.1606311798095703, -1.0111703872680664, -1.150049924850464, -1.1422120332717896, -1.1100784540176392, -1.0909085273742676, -1.2071890830993652, -1.063998818397522, -1.0559271574020386, -1.0096478462219238, -1.133092164993286, -1.1361849308013916, -1.0428688526153564, -0.8610979318618774, -0.784793496131897, -0.7740598917007446, -0.6805766224861145, -0.7509344816207886, -0.8199552297592163, -0.7061428427696228, -0.8381249308586121, -0.9673633575439452, -1.0186474323272705, -1.1204442977905271, -1.108569860458374, -0.9801048636436462, -1.2778130769729614, -1.3118469715118408, -1.1941338777542114, -1.2001001834869385, -1.1647865772247314, -1.1626189947128296, -0.989681601524353, -0.9705026149749756, -1.0608476400375366, -1.0525836944580078, -1.051902413368225, -0.7437789440155029, -0.8027381300926208, -0.7612025141716003, -0.7247871160507202, -0.8589933514595032, -0.6781456470489502, -0.6119953989982605, -0.6622210741043091, -0.9014444351196288]}], "layout": {"height": 600, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Forecast"}, "width": 1100, "xaxis": {"anchor": "y", "domain": [0, 0.94], "tickangle": -45}, "yaxis": {"anchor": "x", "domain": [0, 1]}, "yaxis2": {"anchor": "x", "overlaying": "y", "side": "right"}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_ts_forecasting(\n", "    test_data,\n", "    forecasts_,\n", "    forecast_columns=[\"HUFL\"],\n", "    timestamp_column=timestamp_column,\n", "    periodicity=\"1h\",\n", "    prediction_length=model.config.prediction_length,\n", "    context_length=context_length,\n", "    plot_start=0,\n", "    plot_end=context_length + model.config.prediction_length * 3,\n", "    num_predictions=3,\n", "    plot_stride=model.config.prediction_length,\n", "    title=\"Forecast\",\n", "    fig_size=(1100, 600),\n", "    plot_type=\"plotly\",\n", "    return_image=False,\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "MUFL", "type": "scatter", "x": ["2017-10-02T16:00:00", "2017-10-02T17:00:00", "2017-10-02T18:00:00", "2017-10-02T19:00:00", "2017-10-02T20:00:00", "2017-10-02T21:00:00", "2017-10-02T22:00:00", "2017-10-02T23:00:00", "2017-10-03T00:00:00", "2017-10-03T01:00:00", "2017-10-03T02:00:00", "2017-10-03T03:00:00", "2017-10-03T04:00:00", "2017-10-03T05:00:00", "2017-10-03T06:00:00", "2017-10-03T07:00:00", "2017-10-03T08:00:00", "2017-10-03T09:00:00", "2017-10-03T10:00:00", "2017-10-03T11:00:00", "2017-10-03T12:00:00", "2017-10-03T13:00:00", "2017-10-03T14:00:00", "2017-10-03T15:00:00", "2017-10-03T16:00:00", "2017-10-03T17:00:00", "2017-10-03T18:00:00", "2017-10-03T19:00:00", "2017-10-03T20:00:00", "2017-10-03T21:00:00", "2017-10-03T22:00:00", "2017-10-03T23:00:00", "2017-10-04T00:00:00", "2017-10-04T01:00:00", "2017-10-04T02:00:00", "2017-10-04T03:00:00", "2017-10-04T04:00:00", "2017-10-04T05:00:00", "2017-10-04T06:00:00", "2017-10-04T07:00:00", "2017-10-04T08:00:00", "2017-10-04T09:00:00", "2017-10-04T10:00:00", "2017-10-04T11:00:00", "2017-10-04T12:00:00", "2017-10-04T13:00:00", "2017-10-04T14:00:00", "2017-10-04T15:00:00", "2017-10-04T16:00:00", "2017-10-04T17:00:00", "2017-10-04T18:00:00", "2017-10-04T19:00:00", "2017-10-04T20:00:00", "2017-10-04T21:00:00", "2017-10-04T22:00:00", "2017-10-04T23:00:00", "2017-10-05T00:00:00", "2017-10-05T01:00:00", "2017-10-05T02:00:00", "2017-10-05T03:00:00", "2017-10-05T04:00:00", "2017-10-05T05:00:00", "2017-10-05T06:00:00", "2017-10-05T07:00:00", "2017-10-05T08:00:00", "2017-10-05T09:00:00", "2017-10-05T10:00:00", "2017-10-05T11:00:00", "2017-10-05T12:00:00", "2017-10-05T13:00:00", "2017-10-05T14:00:00", "2017-10-05T15:00:00", "2017-10-05T16:00:00", "2017-10-05T17:00:00", "2017-10-05T18:00:00", "2017-10-05T19:00:00", "2017-10-05T20:00:00", "2017-10-05T21:00:00", "2017-10-05T22:00:00", "2017-10-05T23:00:00", "2017-10-06T00:00:00", "2017-10-06T01:00:00", "2017-10-06T02:00:00", "2017-10-06T03:00:00", "2017-10-06T04:00:00", "2017-10-06T05:00:00", "2017-10-06T06:00:00", "2017-10-06T07:00:00", "2017-10-06T08:00:00", "2017-10-06T09:00:00", "2017-10-06T10:00:00", "2017-10-06T11:00:00", "2017-10-06T12:00:00", "2017-10-06T13:00:00", "2017-10-06T14:00:00", "2017-10-06T15:00:00", "2017-10-06T16:00:00", "2017-10-06T17:00:00", "2017-10-06T18:00:00", "2017-10-06T19:00:00", "2017-10-06T20:00:00", "2017-10-06T21:00:00", "2017-10-06T22:00:00", "2017-10-06T23:00:00", "2017-10-07T00:00:00", "2017-10-07T01:00:00", "2017-10-07T02:00:00", "2017-10-07T03:00:00", "2017-10-07T04:00:00", "2017-10-07T05:00:00", "2017-10-07T06:00:00", "2017-10-07T07:00:00", "2017-10-07T08:00:00", "2017-10-07T09:00:00", "2017-10-07T10:00:00", "2017-10-07T11:00:00", "2017-10-07T12:00:00", "2017-10-07T13:00:00", "2017-10-07T14:00:00", "2017-10-07T15:00:00", "2017-10-07T16:00:00", "2017-10-07T17:00:00", "2017-10-07T18:00:00", "2017-10-07T19:00:00", "2017-10-07T20:00:00", "2017-10-07T21:00:00", "2017-10-07T22:00:00", "2017-10-07T23:00:00", "2017-10-08T00:00:00", "2017-10-08T01:00:00", "2017-10-08T02:00:00", "2017-10-08T03:00:00", "2017-10-08T04:00:00", "2017-10-08T05:00:00", "2017-10-08T06:00:00", "2017-10-08T07:00:00", "2017-10-08T08:00:00", "2017-10-08T09:00:00", "2017-10-08T10:00:00", "2017-10-08T11:00:00", "2017-10-08T12:00:00", "2017-10-08T13:00:00", "2017-10-08T14:00:00", "2017-10-08T15:00:00", "2017-10-08T16:00:00", "2017-10-08T17:00:00", "2017-10-08T18:00:00", "2017-10-08T19:00:00", "2017-10-08T20:00:00", "2017-10-08T21:00:00", "2017-10-08T22:00:00", "2017-10-08T23:00:00", "2017-10-09T00:00:00", "2017-10-09T01:00:00", "2017-10-09T02:00:00", "2017-10-09T03:00:00", "2017-10-09T04:00:00", "2017-10-09T05:00:00", "2017-10-09T06:00:00", "2017-10-09T07:00:00", "2017-10-09T08:00:00", "2017-10-09T09:00:00", "2017-10-09T10:00:00", "2017-10-09T11:00:00", "2017-10-09T12:00:00", "2017-10-09T13:00:00", "2017-10-09T14:00:00", "2017-10-09T15:00:00", "2017-10-09T16:00:00", "2017-10-09T17:00:00", "2017-10-09T18:00:00", "2017-10-09T19:00:00", "2017-10-09T20:00:00", "2017-10-09T21:00:00", "2017-10-09T22:00:00", "2017-10-09T23:00:00", "2017-10-10T00:00:00", "2017-10-10T01:00:00", "2017-10-10T02:00:00", "2017-10-10T03:00:00", "2017-10-10T04:00:00", "2017-10-10T05:00:00", "2017-10-10T06:00:00", "2017-10-10T07:00:00", "2017-10-10T08:00:00", "2017-10-10T09:00:00", "2017-10-10T10:00:00", "2017-10-10T11:00:00", "2017-10-10T12:00:00", "2017-10-10T13:00:00", "2017-10-10T14:00:00", "2017-10-10T15:00:00", "2017-10-10T16:00:00", "2017-10-10T17:00:00", "2017-10-10T18:00:00", "2017-10-10T19:00:00", "2017-10-10T20:00:00", "2017-10-10T21:00:00", "2017-10-10T22:00:00", "2017-10-10T23:00:00", "2017-10-11T00:00:00", "2017-10-11T01:00:00", "2017-10-11T02:00:00", "2017-10-11T03:00:00", "2017-10-11T04:00:00", "2017-10-11T05:00:00", "2017-10-11T06:00:00", "2017-10-11T07:00:00", "2017-10-11T08:00:00", "2017-10-11T09:00:00", "2017-10-11T10:00:00", "2017-10-11T11:00:00", "2017-10-11T12:00:00", "2017-10-11T13:00:00", "2017-10-11T14:00:00", "2017-10-11T15:00:00", "2017-10-11T16:00:00", "2017-10-11T17:00:00", "2017-10-11T18:00:00", "2017-10-11T19:00:00", "2017-10-11T20:00:00", "2017-10-11T21:00:00", "2017-10-11T22:00:00", "2017-10-11T23:00:00", "2017-10-12T00:00:00", "2017-10-12T01:00:00", "2017-10-12T02:00:00", "2017-10-12T03:00:00", "2017-10-12T04:00:00", "2017-10-12T05:00:00", "2017-10-12T06:00:00", "2017-10-12T07:00:00", "2017-10-12T08:00:00", "2017-10-12T09:00:00", "2017-10-12T10:00:00", "2017-10-12T11:00:00", "2017-10-12T12:00:00", "2017-10-12T13:00:00", "2017-10-12T14:00:00", "2017-10-12T15:00:00", "2017-10-12T16:00:00", "2017-10-12T17:00:00", "2017-10-12T18:00:00", "2017-10-12T19:00:00", "2017-10-12T20:00:00", "2017-10-12T21:00:00", "2017-10-12T22:00:00", "2017-10-12T23:00:00", "2017-10-13T00:00:00", "2017-10-13T01:00:00", "2017-10-13T02:00:00", "2017-10-13T03:00:00", "2017-10-13T04:00:00", "2017-10-13T05:00:00", "2017-10-13T06:00:00", "2017-10-13T07:00:00", "2017-10-13T08:00:00", "2017-10-13T09:00:00", "2017-10-13T10:00:00", "2017-10-13T11:00:00", "2017-10-13T12:00:00", "2017-10-13T13:00:00", "2017-10-13T14:00:00", "2017-10-13T15:00:00", "2017-10-13T16:00:00", "2017-10-13T17:00:00", "2017-10-13T18:00:00", "2017-10-13T19:00:00", "2017-10-13T20:00:00", "2017-10-13T21:00:00", "2017-10-13T22:00:00", "2017-10-13T23:00:00", "2017-10-14T00:00:00", "2017-10-14T01:00:00", "2017-10-14T02:00:00", "2017-10-14T03:00:00", "2017-10-14T04:00:00", "2017-10-14T05:00:00", "2017-10-14T06:00:00", "2017-10-14T07:00:00", "2017-10-14T08:00:00", "2017-10-14T09:00:00", "2017-10-14T10:00:00", "2017-10-14T11:00:00", "2017-10-14T12:00:00", "2017-10-14T13:00:00", "2017-10-14T14:00:00", "2017-10-14T15:00:00", "2017-10-14T16:00:00", "2017-10-14T17:00:00", "2017-10-14T18:00:00", "2017-10-14T19:00:00", "2017-10-14T20:00:00", "2017-10-14T21:00:00", "2017-10-14T22:00:00", "2017-10-14T23:00:00", "2017-10-15T00:00:00", "2017-10-15T01:00:00", "2017-10-15T02:00:00", "2017-10-15T03:00:00", "2017-10-15T04:00:00", "2017-10-15T05:00:00", "2017-10-15T06:00:00", "2017-10-15T07:00:00", "2017-10-15T08:00:00", "2017-10-15T09:00:00", "2017-10-15T10:00:00", "2017-10-15T11:00:00", "2017-10-15T12:00:00", "2017-10-15T13:00:00", "2017-10-15T14:00:00", "2017-10-15T15:00:00", "2017-10-15T16:00:00", "2017-10-15T17:00:00", "2017-10-15T18:00:00", "2017-10-15T19:00:00", "2017-10-15T20:00:00", "2017-10-15T21:00:00", "2017-10-15T22:00:00", "2017-10-15T23:00:00", "2017-10-16T00:00:00", "2017-10-16T01:00:00", "2017-10-16T02:00:00", "2017-10-16T03:00:00", "2017-10-16T04:00:00", "2017-10-16T05:00:00", "2017-10-16T06:00:00", "2017-10-16T07:00:00", "2017-10-16T08:00:00", "2017-10-16T09:00:00", "2017-10-16T10:00:00", "2017-10-16T11:00:00", "2017-10-16T12:00:00", "2017-10-16T13:00:00", "2017-10-16T14:00:00", "2017-10-16T15:00:00", "2017-10-16T16:00:00", "2017-10-16T17:00:00", "2017-10-16T18:00:00", "2017-10-16T19:00:00", "2017-10-16T20:00:00", "2017-10-16T21:00:00", "2017-10-16T22:00:00", "2017-10-16T23:00:00", "2017-10-17T00:00:00", "2017-10-17T01:00:00", "2017-10-17T02:00:00", "2017-10-17T03:00:00", "2017-10-17T04:00:00", "2017-10-17T05:00:00", "2017-10-17T06:00:00", "2017-10-17T07:00:00", "2017-10-17T08:00:00", "2017-10-17T09:00:00", "2017-10-17T10:00:00", "2017-10-17T11:00:00", "2017-10-17T12:00:00", "2017-10-17T13:00:00", "2017-10-17T14:00:00", "2017-10-17T15:00:00", "2017-10-17T16:00:00", "2017-10-17T17:00:00", "2017-10-17T18:00:00", "2017-10-17T19:00:00", "2017-10-17T20:00:00", "2017-10-17T21:00:00", "2017-10-17T22:00:00", "2017-10-17T23:00:00", "2017-10-18T00:00:00", "2017-10-18T01:00:00", "2017-10-18T02:00:00", "2017-10-18T03:00:00", "2017-10-18T04:00:00", "2017-10-18T05:00:00", "2017-10-18T06:00:00", "2017-10-18T07:00:00", "2017-10-18T08:00:00", "2017-10-18T09:00:00", "2017-10-18T10:00:00", "2017-10-18T11:00:00", "2017-10-18T12:00:00", "2017-10-18T13:00:00", "2017-10-18T14:00:00", "2017-10-18T15:00:00", "2017-10-18T16:00:00", "2017-10-18T17:00:00", "2017-10-18T18:00:00", "2017-10-18T19:00:00", "2017-10-18T20:00:00", "2017-10-18T21:00:00", "2017-10-18T22:00:00", "2017-10-18T23:00:00", "2017-10-19T00:00:00", "2017-10-19T01:00:00", "2017-10-19T02:00:00", "2017-10-19T03:00:00", "2017-10-19T04:00:00", "2017-10-19T05:00:00", "2017-10-19T06:00:00", "2017-10-19T07:00:00", "2017-10-19T08:00:00", "2017-10-19T09:00:00", "2017-10-19T10:00:00", "2017-10-19T11:00:00", "2017-10-19T12:00:00", "2017-10-19T13:00:00", "2017-10-19T14:00:00", "2017-10-19T15:00:00", "2017-10-19T16:00:00", "2017-10-19T17:00:00", "2017-10-19T18:00:00", "2017-10-19T19:00:00", "2017-10-19T20:00:00", "2017-10-19T21:00:00", "2017-10-19T22:00:00", "2017-10-19T23:00:00", "2017-10-20T00:00:00", "2017-10-20T01:00:00", "2017-10-20T02:00:00", "2017-10-20T03:00:00", "2017-10-20T04:00:00", "2017-10-20T05:00:00", "2017-10-20T06:00:00", "2017-10-20T07:00:00", "2017-10-20T08:00:00", "2017-10-20T09:00:00", "2017-10-20T10:00:00", "2017-10-20T11:00:00", "2017-10-20T12:00:00", "2017-10-20T13:00:00", "2017-10-20T14:00:00", "2017-10-20T15:00:00", "2017-10-20T16:00:00", "2017-10-20T17:00:00", "2017-10-20T18:00:00", "2017-10-20T19:00:00", "2017-10-20T20:00:00", "2017-10-20T21:00:00", "2017-10-20T22:00:00", "2017-10-20T23:00:00", "2017-10-21T00:00:00", "2017-10-21T01:00:00", "2017-10-21T02:00:00", "2017-10-21T03:00:00", "2017-10-21T04:00:00", "2017-10-21T05:00:00", "2017-10-21T06:00:00", "2017-10-21T07:00:00", "2017-10-21T08:00:00", "2017-10-21T09:00:00", "2017-10-21T10:00:00", "2017-10-21T11:00:00", "2017-10-21T12:00:00", "2017-10-21T13:00:00", "2017-10-21T14:00:00", "2017-10-21T15:00:00", "2017-10-21T16:00:00", "2017-10-21T17:00:00", "2017-10-21T18:00:00", "2017-10-21T19:00:00", "2017-10-21T20:00:00", "2017-10-21T21:00:00", "2017-10-21T22:00:00", "2017-10-21T23:00:00", "2017-10-22T00:00:00", "2017-10-22T01:00:00", "2017-10-22T02:00:00", "2017-10-22T03:00:00", "2017-10-22T04:00:00", "2017-10-22T05:00:00", "2017-10-22T06:00:00", "2017-10-22T07:00:00", "2017-10-22T08:00:00", "2017-10-22T09:00:00", "2017-10-22T10:00:00", "2017-10-22T11:00:00", "2017-10-22T12:00:00", "2017-10-22T13:00:00", "2017-10-22T14:00:00", "2017-10-22T15:00:00", "2017-10-22T16:00:00", "2017-10-22T17:00:00", "2017-10-22T18:00:00", "2017-10-22T19:00:00", "2017-10-22T20:00:00", "2017-10-22T21:00:00", "2017-10-22T22:00:00", "2017-10-22T23:00:00", "2017-10-23T00:00:00", "2017-10-23T01:00:00", "2017-10-23T02:00:00", "2017-10-23T03:00:00", "2017-10-23T04:00:00", "2017-10-23T05:00:00", "2017-10-23T06:00:00", "2017-10-23T07:00:00", "2017-10-23T08:00:00", "2017-10-23T09:00:00", "2017-10-23T10:00:00", "2017-10-23T11:00:00", "2017-10-23T12:00:00", "2017-10-23T13:00:00", "2017-10-23T14:00:00", "2017-10-23T15:00:00", "2017-10-23T16:00:00", "2017-10-23T17:00:00", "2017-10-23T18:00:00", "2017-10-23T19:00:00", "2017-10-23T20:00:00", "2017-10-23T21:00:00", "2017-10-23T22:00:00", "2017-10-23T23:00:00", "2017-10-24T00:00:00", "2017-10-24T01:00:00", "2017-10-24T02:00:00", "2017-10-24T03:00:00", "2017-10-24T04:00:00", "2017-10-24T05:00:00", "2017-10-24T06:00:00", "2017-10-24T07:00:00", "2017-10-24T08:00:00", "2017-10-24T09:00:00", "2017-10-24T10:00:00", "2017-10-24T11:00:00", "2017-10-24T12:00:00", "2017-10-24T13:00:00", "2017-10-24T14:00:00", "2017-10-24T15:00:00", "2017-10-24T16:00:00", "2017-10-24T17:00:00", "2017-10-24T18:00:00", "2017-10-24T19:00:00", "2017-10-24T20:00:00", "2017-10-24T21:00:00", "2017-10-24T22:00:00", "2017-10-24T23:00:00", "2017-10-25T00:00:00", "2017-10-25T01:00:00", "2017-10-25T02:00:00", "2017-10-25T03:00:00", "2017-10-25T04:00:00", "2017-10-25T05:00:00", "2017-10-25T06:00:00", "2017-10-25T07:00:00", "2017-10-25T08:00:00", "2017-10-25T09:00:00", "2017-10-25T10:00:00", "2017-10-25T11:00:00", "2017-10-25T12:00:00", "2017-10-25T13:00:00", "2017-10-25T14:00:00", "2017-10-25T15:00:00", "2017-10-25T16:00:00", "2017-10-25T17:00:00", "2017-10-25T18:00:00", "2017-10-25T19:00:00", "2017-10-25T20:00:00", "2017-10-25T21:00:00", "2017-10-25T22:00:00", "2017-10-25T23:00:00", "2017-10-26T00:00:00", "2017-10-26T01:00:00", "2017-10-26T02:00:00", "2017-10-26T03:00:00", "2017-10-26T04:00:00", "2017-10-26T05:00:00", "2017-10-26T06:00:00", "2017-10-26T07:00:00", "2017-10-26T08:00:00", "2017-10-26T09:00:00", "2017-10-26T10:00:00", "2017-10-26T11:00:00", "2017-10-26T12:00:00", "2017-10-26T13:00:00", "2017-10-26T14:00:00", "2017-10-26T15:00:00", "2017-10-26T16:00:00", "2017-10-26T17:00:00", "2017-10-26T18:00:00", "2017-10-26T19:00:00", "2017-10-26T20:00:00", "2017-10-26T21:00:00", "2017-10-26T22:00:00", "2017-10-26T23:00:00", "2017-10-27T00:00:00", "2017-10-27T01:00:00", "2017-10-27T02:00:00", "2017-10-27T03:00:00", "2017-10-27T04:00:00", "2017-10-27T05:00:00", "2017-10-27T06:00:00", "2017-10-27T07:00:00", "2017-10-27T08:00:00", "2017-10-27T09:00:00", "2017-10-27T10:00:00", "2017-10-27T11:00:00", "2017-10-27T12:00:00", "2017-10-27T13:00:00", "2017-10-27T14:00:00", "2017-10-27T15:00:00", "2017-10-27T16:00:00", "2017-10-27T17:00:00", "2017-10-27T18:00:00", "2017-10-27T19:00:00", "2017-10-27T20:00:00", "2017-10-27T21:00:00", "2017-10-27T22:00:00", "2017-10-27T23:00:00", "2017-10-28T00:00:00", "2017-10-28T01:00:00", "2017-10-28T02:00:00", "2017-10-28T03:00:00", "2017-10-28T04:00:00", "2017-10-28T05:00:00", "2017-10-28T06:00:00", "2017-10-28T07:00:00", "2017-10-28T08:00:00", "2017-10-28T09:00:00", "2017-10-28T10:00:00", "2017-10-28T11:00:00", "2017-10-28T12:00:00", "2017-10-28T13:00:00", "2017-10-28T14:00:00", "2017-10-28T15:00:00", "2017-10-28T16:00:00", "2017-10-28T17:00:00", "2017-10-28T18:00:00", "2017-10-28T19:00:00", "2017-10-28T20:00:00", "2017-10-28T21:00:00", "2017-10-28T22:00:00", "2017-10-28T23:00:00", "2017-10-29T00:00:00", "2017-10-29T01:00:00", "2017-10-29T02:00:00", "2017-10-29T03:00:00", "2017-10-29T04:00:00", "2017-10-29T05:00:00", "2017-10-29T06:00:00", "2017-10-29T07:00:00", "2017-10-29T08:00:00", "2017-10-29T09:00:00", "2017-10-29T10:00:00", "2017-10-29T11:00:00", "2017-10-29T12:00:00", "2017-10-29T13:00:00", "2017-10-29T14:00:00", "2017-10-29T15:00:00", "2017-10-29T16:00:00", "2017-10-29T17:00:00", "2017-10-29T18:00:00", "2017-10-29T19:00:00", "2017-10-29T20:00:00", "2017-10-29T21:00:00", "2017-10-29T22:00:00", "2017-10-29T23:00:00", "2017-10-30T00:00:00", "2017-10-30T01:00:00", "2017-10-30T02:00:00", "2017-10-30T03:00:00", "2017-10-30T04:00:00", "2017-10-30T05:00:00", "2017-10-30T06:00:00", "2017-10-30T07:00:00", "2017-10-30T08:00:00", "2017-10-30T09:00:00", "2017-10-30T10:00:00", "2017-10-30T11:00:00", "2017-10-30T12:00:00", "2017-10-30T13:00:00", "2017-10-30T14:00:00", "2017-10-30T15:00:00", "2017-10-30T16:00:00", "2017-10-30T17:00:00", "2017-10-30T18:00:00", "2017-10-30T19:00:00", "2017-10-30T20:00:00", "2017-10-30T21:00:00", "2017-10-30T22:00:00", "2017-10-30T23:00:00", "2017-10-31T00:00:00", "2017-10-31T01:00:00", "2017-10-31T02:00:00", "2017-10-31T03:00:00", "2017-10-31T04:00:00", "2017-10-31T05:00:00", "2017-10-31T06:00:00", "2017-10-31T07:00:00", "2017-10-31T08:00:00", "2017-10-31T09:00:00", "2017-10-31T10:00:00", "2017-10-31T11:00:00", "2017-10-31T12:00:00", "2017-10-31T13:00:00", "2017-10-31T14:00:00", "2017-10-31T15:00:00", "2017-10-31T16:00:00", "2017-10-31T17:00:00", "2017-10-31T18:00:00", "2017-10-31T19:00:00", "2017-10-31T20:00:00", "2017-10-31T21:00:00", "2017-10-31T22:00:00", "2017-10-31T23:00:00", "2017-11-01T00:00:00", "2017-11-01T01:00:00", "2017-11-01T02:00:00", "2017-11-01T03:00:00", "2017-11-01T04:00:00", "2017-11-01T05:00:00", "2017-11-01T06:00:00", "2017-11-01T07:00:00", "2017-11-01T08:00:00", "2017-11-01T09:00:00", "2017-11-01T10:00:00", "2017-11-01T11:00:00", "2017-11-01T12:00:00", "2017-11-01T13:00:00", "2017-11-01T14:00:00", "2017-11-01T15:00:00", "2017-11-01T16:00:00", "2017-11-01T17:00:00", "2017-11-01T18:00:00", "2017-11-01T19:00:00", "2017-11-01T20:00:00", "2017-11-01T21:00:00", "2017-11-01T22:00:00", "2017-11-01T23:00:00", "2017-11-02T00:00:00", "2017-11-02T01:00:00", "2017-11-02T02:00:00", "2017-11-02T03:00:00", "2017-11-02T04:00:00", "2017-11-02T05:00:00", "2017-11-02T06:00:00", "2017-11-02T07:00:00", "2017-11-02T08:00:00", "2017-11-02T09:00:00", "2017-11-02T10:00:00", "2017-11-02T11:00:00", "2017-11-02T12:00:00", "2017-11-02T13:00:00", "2017-11-02T14:00:00", "2017-11-02T15:00:00", "2017-11-02T16:00:00", "2017-11-02T17:00:00", "2017-11-02T18:00:00", "2017-11-02T19:00:00", "2017-11-02T20:00:00", "2017-11-02T21:00:00", "2017-11-02T22:00:00", "2017-11-02T23:00:00", "2017-11-03T00:00:00", "2017-11-03T01:00:00", "2017-11-03T02:00:00", "2017-11-03T03:00:00", "2017-11-03T04:00:00", "2017-11-03T05:00:00", "2017-11-03T06:00:00", "2017-11-03T07:00:00", "2017-11-03T08:00:00", "2017-11-03T09:00:00", "2017-11-03T10:00:00", "2017-11-03T11:00:00", "2017-11-03T12:00:00", "2017-11-03T13:00:00", "2017-11-03T14:00:00", "2017-11-03T15:00:00", "2017-11-03T16:00:00", "2017-11-03T17:00:00", "2017-11-03T18:00:00", "2017-11-03T19:00:00", "2017-11-03T20:00:00", "2017-11-03T21:00:00", "2017-11-03T22:00:00", "2017-11-03T23:00:00", "2017-11-04T00:00:00", "2017-11-04T01:00:00", "2017-11-04T02:00:00", "2017-11-04T03:00:00", "2017-11-04T04:00:00", "2017-11-04T05:00:00", "2017-11-04T06:00:00", "2017-11-04T07:00:00", "2017-11-04T08:00:00", "2017-11-04T09:00:00", "2017-11-04T10:00:00", "2017-11-04T11:00:00", "2017-11-04T12:00:00", "2017-11-04T13:00:00", "2017-11-04T14:00:00", "2017-11-04T15:00:00", "2017-11-04T16:00:00", "2017-11-04T17:00:00", "2017-11-04T18:00:00", "2017-11-04T19:00:00", "2017-11-04T20:00:00", "2017-11-04T21:00:00", "2017-11-04T22:00:00", "2017-11-04T23:00:00"], "y": [-0.3669891513796287, -0.3494903473487548, -0.23820893289270473, -0.5212167655437373, -0.4353831993279515, -0.24135288860579843, -0.3876319111235963, -0.6389044366712567, -0.8774235083578495, -0.7056968638813277, -0.7835818384508023, -0.6627504378917289, -0.6548018462454182, -0.8853722131449302, -0.6340998007380396, -0.5673071472464294, -0.9282593533711172, -0.5387157958561509, -0.7676846551581806, -0.6357011951947522, -1.0109491901553496, -0.810631081147779, -0.9775529765503144, -0.7263396236252945, -0.5705103887229339, -0.4433317909742623, -0.2969934826930536, -0.3494903473487548, -0.6643520586299811, -0.55301158469206, -0.55301158469206, -0.6039066023279692, -0.7660830344199284, -0.7072391988561687, -0.8710171385456105, -0.8328754616299989, -0.6548018462454182, -0.8917192971937586, -0.7199926527172368, -0.8503744919424121, -1.0093475694170972, -0.6738429852511334, -0.8503744919424121, -0.5466643875024617, -0.6039066023279692, -0.7756332468044911, -0.8201812935323421, -0.7374916830296504, -0.7644814136816765, -0.7772348675427437, -0.55301158469206, -0.9267171315370462, -0.8185796727940898, -0.7708878966346848, -0.6627504378917289, -0.9139636776759784, -1.0157539392293364, -1.123831999068112, -1.1333822114526757, -1.031651235662728, -0.8312738408917466, -1.0427437830221329, -0.7549904870605239, -0.7072391988561687, -0.9012695095783224, -0.6086521787793152, -0.30179834490781043, -0.28109629940043196, -0.5450627667642095, -0.3844881816920423, -0.4321799578514469, -0.4003853649846639, -0.6833931976356961, -0.33993990868265195, -0.30179834490781043, -0.5434611460259572, -0.446475746687356, -0.4846768225071481, -0.378140984502444, -0.4592292005484237, -0.4926254141534589, -0.5673071472464294, -0.599161252158163, -0.6627504378917289, -0.6436500131226027, -0.6961466514967638, -0.4989723850615174, -0.5085228237276203, -0.6324981799997873, -0.5403174165944028, -0.4003853649846639, -0.21436315795377228, -0.4337815785896988, -0.29859510343130585, -0.6468530283175675, -0.30500158638431496, -0.20481271928766948, -0.3272459668665349, -0.29859510343130585, -0.4337815785896988, -0.414681153820573, -0.28429931459539676, -0.46557617145648217, -0.40993557736922703, -0.4576275798101714, -0.6802494682041426, -0.5498676289789663, -0.6150584354507845, -0.6118554202558197, -0.5212167655437373, -0.6738429852511334, -0.38923353186184856, -0.7883274149021483, -0.3637859099031241, -0.1936608861648537, -0.25090310099036195, -0.6230072533786347, -0.5880094190353475, -0.39243677333835314, -0.2588519189182121, -0.2716053727792797, -0.2286587205081421, -0.23975126786754616, -0.3812849402155377, -0.4067325621742622, -0.20481271928766948, -0.1507144601752553, -0.691341789282007, -0.4083339566309747, -1.0459470244986369, -0.4989723850615174, -0.46872012716957584, -0.7374916830296504, -0.5816622218457493, -0.6373028159330044, -0.4719233686460804, -0.6929434100202596, -0.5021163407746111, -0.3494903473487548, -0.1984657483796105, -0.29064651178499507, -0.23975126786754616, -0.24455613008230295, -0.3065436950776167, -0.1857122945185433, -0.2207101288618304, -0.13962191281585123, -0.5912124342303128, -0.4401287757792979, -0.25570796320511835, -0.7040952431430746, -0.5657650385531277, -0.4130795330823203, -0.4878205519387021, -0.5419190373326556, -0.4448741259491037, -0.45282271759541454, -0.3940383940766054, -0.446475746687356, -0.8392224325380574, -0.6182023911638783, -0.30814531581586896, -0.3876319111235963, -0.10936965492390968, -0.24455613008230295, -0.29064651178499507, -0.3733361222876876, -0.3351945585128457, -0.20160970409270465, -0.11417429085712676, -0.06488089395946989, -0.4083339566309747, -0.06327927322121718, -0.08077807725209152, -0.13962191281585123, -0.2604533133749246, -0.3828865609537896, -0.6548018462454182, -0.7072391988561687, -0.5180137503487725, -0.4846768225071481, -0.4258329869433884, -0.37493774302593946, -0.6468530283175675, -0.33833851422593936, -0.25090310099036195, -0.672300650276292, -0.47352498938433274, -0.5943563899434061, -0.5498676289789663, -0.09667548682625288, -0.20955829573901544, -0.27795234368733823, -0.2254557053131764, -0.052127440098402235, -0.2859009353336491, -0.4830752017688954, -0.4973709906048054, -0.21276153721552, -0.3097469365541212, -0.35583731825681286, -0.47672800457929754, -0.5816622218457493, -0.3860895761487548, -0.7803788232558375, -0.45282271759541454, -0.28750255607190134, -0.26519888982627016, -0.6245493620719368, -0.19526250690310637, -0.3494903473487548, -0.4337815785896988, -0.3033999656460623, -0.5403174165944028, -0.7406354124612036, -0.3860895761487548, -0.1825090530420387, -0.1348170506010944, -0.10142083699605912, -0.417884395297078, -0.21115991647726773, -0.16346791403632338, -0.33833851422593936, -0.37019239285613326, -0.4957693698665526, -0.4846768225071481, -0.5148697946356788, -0.5371141751178987, -0.6945450307585119, -0.4862191574819895, -0.8344770823682512, -0.4019869857229162, -0.5848061775588426, -0.16981488494438193, -0.37019239285613326, -0.2588519189182121, -0.24930148025210924, -0.26519888982627016, -0.3876319111235963, -0.30500158638431496, -0.23820893289270473, -0.19526250690310637, -0.12052148804672462, -0.26519888982627016, -0.3256443461282822, -0.2525047217286138, -0.2350059176977399, -0.35904055973331783, -0.5737136301994386, -0.8185796727940898, -0.9012695095783224, -0.92986097410937, -0.8201812935323421, -0.8710171385456105, -0.6754446059893857, -0.6007033608514646, -0.7947336715736176, -0.3462871058722502, -0.6849948183739488, -0.5927547692051538, -0.7644814136816765, -0.2716053727792797, -0.5085228237276203, -0.3192971489386843, -0.2620549341131769, -0.21276153721552, -0.22705709976988936, -0.3272459668665349, -0.2223117496000831, -0.19045787096988925, -0.09192991037490648, -0.3144925130054672, -0.3431431501591565, -0.40993557736922703, -0.4369255343027929, -0.5148697946356788, -0.4846768225071481, -0.417884395297078, -0.3256443461282822, -0.2826976938571445, -0.55301158469206, -0.14757050446216205, -0.1491721252004139, -0.15712094312826486, -0.28750255607190134, -0.2572502981799598, -0.37493774302593946, -0.25090310099036195, -0.1984657483796105, -0.16186629329807112, -0.09827710756450474, -0.4576275798101714, -0.21590526664707357, -0.2286587205081421, -0.24770008579539624, -0.26680051056452286, -0.4051309414360095, -0.4242313662051361, -0.535512554379646, -0.4019869857229162, -0.6277526035484413, -0.4401287757792979, -0.6738429852511334, -0.378140984502444, -0.7358900622913978, -0.18731391525679555, -0.21750688738532625, -0.2000080833544528, -0.35743893899506557, -0.5641634178148754, -0.30179834490781043, -0.24930148025210924, -0.23820893289270473, -0.16506953477457567, -0.13007170043128813, -0.1507144601752553, -0.2525047217286138, -0.20795667500076315, -0.37173472783097466, -0.26519888982627016, -0.3908351526001004, -0.47672800457929754, -0.55301158469206, -0.548266008240714, -0.4957693698665526, -0.4639747769997696, -1.0777416173654202, -1.3957472843597405, -1.63907110512032, -1.5102317140107548, -1.3321579854854046, -1.348055281918796, -1.5229851678718227, -1.5579236033109296, -1.4243980346541996, -1.3846547370003364, -0.7310852000766408, -0.27635072294908597, -0.10622569921081554, -0.1857122945185433, -0.3622438012098224, -0.4067325621742622, -0.2716053727792797, -0.4576275798101714, -0.39243677333835314, -0.4003853649846639, -0.5434611460259572, -0.4512806089021128, -0.4003853649846639, -0.39878374424641166, -0.3431431501591565, -0.29859510343130585, -0.6897403948252944, -0.2350059176977399, -0.3940383940766054, -0.19526250690310637, -0.33833851422593936, -0.2572502981799598, -0.23820893289270473, -0.13962191281585123, -0.1539177016517603, -0.13962191281585123, -0.04263651347725, -0.27635072294908597, -0.1459691100054495, -0.09667548682625288, -0.1984657483796105, -0.27635072294908597, -0.40993557736922703, -0.47827033955413856, -0.4083339566309747, -0.4560259590719187, -0.37493774302593946, -0.5403174165944028, -0.33679617925109795, -0.21115991647726773, -0.42102812472863155, -0.20795667500076315, -0.37173472783097466, -0.16821349048766937, -0.3033999656460623, -0.25570796320511835, -0.20641434002592216, -0.18731391525679555, -0.08552365370343708, -0.10462407847256368, -0.0728294856057807, -0.29859510343130585, -0.11417429085712676, -0.20955829573901544, -0.1459691100054495, -0.33038969629808884, -0.37493774302593946, -0.535512554379646, -0.4258329869433884, -0.6039066023279692, -0.4560259590719187, -0.42897694265648206, -0.4353831993279515, -0.5228183862819896, -1.211326584926331, -1.0634459416702808, -1.0745383758889155, -1.0141523184910843, -1.2431211777931137, -1.2685686866110688, -1.192226047016435, -1.2256222606214695, -1.2049795008775026, -1.017296274204178, -0.9791544841477968, -1.049150152834372, -1.1349838321909276, -1.1397885812649144, -1.1317805907144227, -1.270170307349321, -1.408500738220808, -1.3973489050979926, -1.47683550040572, -1.4529894991852477, -1.4164493298671188, -1.445040794398167, -1.3194639305285174, -1.2160720482369072, -1.2097249641880787, -1.123831999068112, -1.028448107326993, -1.0157539392293364, -1.0634459416702808, -1.2256222606214695, -1.266967179013586, -1.2033778801392496, -1.146135665313743, -1.165176917460228, -1.072996154054844, -1.123831999068112, -1.2319693446702986, -1.1810742138936194, -1.2224190191449658, -1.2828644754469776, -1.4164493298671188, -1.4466424151364192, -1.505486250700179, -1.4339482470387626, -1.487987333528535, -1.4577942482592343, -1.391001821049164, -1.348055281918796, -1.26222171570301, -0.8678732959732867, -0.691341789282007, -0.5896110397735999, -0.6357011951947522, -0.7772348675427437, -0.7644814136816765, -0.842425674014562, -0.7342884415531454, -0.6340998007380396, -0.7342884415531454, -0.7406354124612036, -0.7756332468044911, -0.46872012716957584, -0.24930148025210924, -0.3256443461282822, -0.5148697946356788, -0.4639747769997696, -0.5228183862819896, -0.5434611460259572, -0.8646700544967819, -0.6007033608514646, -0.8090294604095267, -0.354235697518561, -0.6198040119021305, -0.5387157958561509, -0.23975126786754616, -0.1936608861648537, -0.35743893899506557, -0.3622438012098224, -0.3653875306413763, -0.44967898816386054, -0.3669891513796287, -0.25090310099036195, -0.05693207603161977, -0.3144925130054672, -0.25570796320511835, -0.1348170506010944, -0.17141650568263378, -0.5069212029893679, -0.3876319111235963, -0.8901176764555065, -0.4544243383336668, -0.378140984502444, -0.55301158469206, -0.6548018462454182, -0.3113485572923731, -0.7931322771169047, -0.6214056326403828, -0.3001967241695581, -0.5085228237276203, -0.3955805027699075, -0.4385271550410452, -0.3828865609537896, -0.4003853649846639, -0.378140984502444, -0.42102812472863155, -0.2747491022108337, -0.09987872830275744, -0.9473598912810138, -0.9044133521506456, -0.12526683821653128, -0.2223117496000831, -0.2223117496000831, -0.3765393637641918, -0.3733361222876876, -0.47827033955413856, -0.37968331947728545, -0.7549904870605239, -0.4719233686460804, -0.24770008579539624, -0.17936532361048474, -0.5896110397735999, -0.27000375204102744, -0.4926254141534589, -0.2588519189182121, -0.3319913170363411, -0.9426144279704376, -0.3415415294209042, -0.29859510343130585, -0.29064651178499507, -0.21276153721552, -0.37173472783097466, -0.2826976938571445, -0.3129501780306258, -0.19045787096988925, -0.24615775082055527, -0.28109629940043196, -0.3653875306413763, -0.4814735810306435, -0.5307672042098401, -0.4576275798101714, -0.5562145998870248, -0.4592292005484237, -0.3844881816920423, -0.39718212350815935, -0.8519758863991251, -0.5419190373326556, -0.3001967241695581, -0.28750255607190134, -0.4576275798101714, -0.3208987696769366, -0.35583731825681286, -0.3447447708974088, -0.3129501780306258, -0.21276153721552, -0.05058510512356081, -0.6102537995175674, -0.1348170506010944, -0.10462407847256368, -0.2302603412463939, -0.30814531581586896, -0.31769552820043206, -0.4830752017688954, -0.4926254141534589, -0.5737136301994386, -0.3955805027699075, -0.3622438012098224, -0.3033999656460623, -0.3431431501591565, -0.3447447708974088, -0.3192971489386843, -0.42897694265648206, -0.20955829573901544, -0.33993990868265195, -0.39243677333835314, -0.4910237934152062, -0.39878374424641166, -0.2541063424668661, -0.2032113248309565, -0.017188891518525554, -0.28904489104674275, -0.1761620821339802, -0.18731391525679555, -0.2223117496000831, -0.24930148025210924, -0.2684021313027752, -0.414681153820573, -0.5291655834715878, -0.4560259590719187, -0.46872012716957584, -0.36859077211788055, -0.333592937774593, -0.32244110465177805, -0.4130795330823203, -0.3033999656460623, -0.4973709906048054, -0.2747491022108337, -0.3431431501591565, -0.2826976938571445, -0.21115991647726773, -0.21910850812357857, -0.19045787096988925, -0.33038969629808884, -0.13007170043128813, -0.20481271928766948, -0.1666118697494171, -0.17936532361048474, -0.11417429085712676, -0.24615775082055527, -0.3844881816920423, -0.4114779123440685, -0.4941677491283003, -0.4385271550410452, -0.45282271759541454, -0.4894221726769543, -0.38923353186184856, -0.38923353186184856, -0.510064932420922, -0.27955396442559055, -0.19205926542660184, -0.16026467255981883, -0.27955396442559055, -0.28904489104674275, -0.30500158638431496, -0.3351945585128457, -0.4719233686460804, -0.2604533133749246, -0.11731824657022048, -0.20955829573901544, -0.2716053727792797, -0.12847007969303542, -0.1825090530420387, -0.27795234368733823, -0.44173039651754975, -0.40993557736922703, -0.5005740057997697, -0.7008922279481101, -0.4973709906048054, -0.3876319111235963, -0.39243677333835314, -0.3653875306413763, -0.6690974087997874, -0.33679617925109795, -0.3208987696769366, -0.2254557053131764, -0.3510917418054677, -0.42743460768164065, -0.3908351526001004, -0.37968331947728545, -0.29859510343130585, -0.2525047217286138, -0.16186629329807112, -0.3510917418054677, -0.2207101288618304, -0.3510917418054677, -0.28429931459539676, -0.22705709976988936, -0.4576275798101714, -0.516471415373931, -0.5260216277584941, -0.4639747769997696, -0.4926254141534589, -0.44967898816386054, -0.7629390787068351, -0.778836488280996, -0.8678732959732867, -0.32244110465177805, -0.8169780520558375, -0.7120440610709255, -0.28429931459539676, -0.3113485572923731, -0.4385271550410452, -0.2302603412463939, -0.2525047217286138, -0.24455613008230295, -0.16821349048766937, -0.17936532361048474, -0.3113485572923731, -0.13321565614438183, -0.12212310878497731, -0.4242313662051361, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.3637859099031241, -0.4719233686460804, -0.414681153820573, -0.5037179615128634, -0.33679617925109795, -0.4067325621742622, -0.2969934826930536, -0.22705709976988936, -0.19686412764135863, -0.1809669443487366, -0.09347224534974788, -0.37968331947728545, -0.1030224577343114, -0.5260216277584941, -0.5721120094611862, -0.8630684337585296, -0.8519758863991251, -0.2223117496000831, -0.23180267622123535, -0.08077807725209152, -0.3860895761487548, -0.3622438012098224, -0.04898348438530811, -0.19526250690310637, -0.3478887266105021, -0.4083339566309747, -0.4878205519387021, -0.5339702194048049, -0.5657650385531277, -0.5069212029893679, -0.4512806089021128, -0.44173039651754975, -0.44967898816386054, -0.6802494682041426, -0.20481271928766948, -0.4337815785896988, -0.12686845895478358, -0.3065436950776167, -0.2223117496000831, -0.4560259590719187, -0.2525047217286138, -0.20795667500076315, -0.16186629329807112, -0.06648251469772218, -0.3622438012098224, -0.13007170043128813, -0.10142083699605912, -0.15866305182156654, -0.31769552820043206, -0.35743893899506557, -0.4305783371131946, -0.6945450307585119, -0.5864077982970953, -0.5593585556001186, -0.4401287757792979, -0.42102812472863155, -0.3351945585128457, -0.2747491022108337, -0.20481271928766948, -0.5593585556001186, -0.23180267622123535, -0.33038969629808884, -0.28429931459539676, -0.35904055973331783, -0.4305783371131946, -0.1730181264208865, -0.20481271928766948, -0.05533068157490722, -0.1443674892671972, -0.17936532361048474, -0.29224813252324733, -0.38923353186184856, -0.6738429852511334, -0.4353831993279515, -0.6468530283175675, -0.5403174165944028, -0.7008922279481101, -0.4989723850615174, -0.4910237934152062, -0.42897694265648206, -0.3447447708974088, -0.5752559651742799, -0.14757050446216205, -0.21276153721552, -0.08712527444168977, -0.25570796320511835, -0.3208987696769366, -0.3192971489386843, -0.37968331947728545, -0.3812849402155377, -0.3653875306413763, -0.3319913170363411, -0.37493774302593946, -0.6643520586299811, 0.01620732208651013, -0.1459691100054495, -0.21276153721552]}, {"name": "MUFL_prediction", "type": "scatter", "x": ["2017-11-01T00:00:00", "2017-11-01T01:00:00", "2017-11-01T02:00:00", "2017-11-01T03:00:00", "2017-11-01T04:00:00", "2017-11-01T05:00:00", "2017-11-01T06:00:00", "2017-11-01T07:00:00", "2017-11-01T08:00:00", "2017-11-01T09:00:00", "2017-11-01T10:00:00", "2017-11-01T11:00:00", "2017-11-01T12:00:00", "2017-11-01T13:00:00", "2017-11-01T14:00:00", "2017-11-01T15:00:00", "2017-11-01T16:00:00", "2017-11-01T17:00:00", "2017-11-01T18:00:00", "2017-11-01T19:00:00", "2017-11-01T20:00:00", "2017-11-01T21:00:00", "2017-11-01T22:00:00", "2017-11-01T23:00:00", "2017-11-02T00:00:00", "2017-11-02T01:00:00", "2017-11-02T02:00:00", "2017-11-02T03:00:00", "2017-11-02T04:00:00", "2017-11-02T05:00:00", "2017-11-02T06:00:00", "2017-11-02T07:00:00", "2017-11-02T08:00:00", "2017-11-02T09:00:00", "2017-11-02T10:00:00", "2017-11-02T11:00:00", "2017-11-02T12:00:00", "2017-11-02T13:00:00", "2017-11-02T14:00:00", "2017-11-02T15:00:00", "2017-11-02T16:00:00", "2017-11-02T17:00:00", "2017-11-02T18:00:00", "2017-11-02T19:00:00", "2017-11-02T20:00:00", "2017-11-02T21:00:00", "2017-11-02T22:00:00", "2017-11-02T23:00:00", "2017-11-03T00:00:00", "2017-11-03T01:00:00", "2017-11-03T02:00:00", "2017-11-03T03:00:00", "2017-11-03T04:00:00", "2017-11-03T05:00:00", "2017-11-03T06:00:00", "2017-11-03T07:00:00", "2017-11-03T08:00:00", "2017-11-03T09:00:00", "2017-11-03T10:00:00", "2017-11-03T11:00:00", "2017-11-03T12:00:00", "2017-11-03T13:00:00", "2017-11-03T14:00:00", "2017-11-03T15:00:00", "2017-11-03T16:00:00", "2017-11-03T17:00:00", "2017-11-03T18:00:00", "2017-11-03T19:00:00", "2017-11-03T20:00:00", "2017-11-03T21:00:00", "2017-11-03T22:00:00", "2017-11-03T23:00:00", "2017-11-04T00:00:00", "2017-11-04T01:00:00", "2017-11-04T02:00:00", "2017-11-04T03:00:00", "2017-11-04T04:00:00", "2017-11-04T05:00:00", "2017-11-04T06:00:00", "2017-11-04T07:00:00", "2017-11-04T08:00:00", "2017-11-04T09:00:00", "2017-11-04T10:00:00", "2017-11-04T11:00:00", "2017-11-04T12:00:00", "2017-11-04T13:00:00", "2017-11-04T14:00:00", "2017-11-04T15:00:00", "2017-11-04T16:00:00", "2017-11-04T17:00:00", "2017-11-04T18:00:00", "2017-11-04T19:00:00", "2017-11-04T20:00:00", "2017-11-04T21:00:00", "2017-11-04T22:00:00", "2017-11-04T23:00:00"], "y": [-0.43027183413505554, -0.4239028096199035, -0.3690532147884369, -0.4997428357601166, -0.5329204797744751, -0.4790213406085968, -0.3955766558647156, -0.5166265368461609, -0.5195258855819702, -0.3666682243347168, -0.4522668123245239, -0.3611538410186768, -0.34067124128341675, -0.2479216754436493, -0.39184972643852234, -0.27349892258644104, -0.3575812578201294, -0.24542972445487976, -0.25862789154052734, -0.25982367992401123, -0.34846532344818115, -0.19672000408172607, -0.21210411190986633, -0.3549066185951233, -0.5009328722953796, -0.3581227660179138, -0.4333833158016205, -0.3975953161716461, -0.4507734775543213, -0.4515326917171478, -0.37265878915786743, -0.5086995363235474, -0.5445683002471924, -0.3861701786518097, -0.344819039106369, -0.3614605665206909, -0.335959255695343, -0.2975451350212097, -0.3572574257850647, -0.32789498567581177, -0.30043238401412964, -0.3090415894985199, -0.28879401087760925, -0.33547741174697876, -0.3330185115337372, -0.27949750423431396, -0.27701008319854736, -0.3573051989078522, -0.4689963757991791, -0.48532429337501526, -0.4856336712837219, -0.4708799123764038, -0.45742616057395935, -0.5549148321151733, -0.40512314438819885, -0.5013623237609863, -0.47774869203567505, -0.3572198748588562, -0.3780679106712342, -0.3945810794830322, -0.44084662199020386, -0.3909717202186585, -0.34607356786727905, -0.3114209771156311, -0.4446004331111908, -0.35244226455688477, -0.3394583463668823, -0.31284546852111816, -0.32119518518447876, -0.35834288597106934, -0.3828581571578979, -0.3723020851612091, -0.4229162931442261, -0.4060932397842407, -0.4786792695522308, -0.6029704809188843, -0.583959698677063, -0.5073027610778809, -0.43259552121162415, -0.5371720790863037, -0.5276218056678772, -0.4823397696018219, -0.44930917024612427, -0.3680221736431122, -0.3460666537284851, -0.3917277753353119, -0.34714680910110474, -0.3881446123123169, -0.4011445939540863, -0.21286648511886597, -0.18407437205314636, -0.36458781361579895, -0.26754558086395264, -0.2167435586452484, -0.32077550888061523, -0.3368380069732666]}, {"name": "MUFL_prediction", "type": "scatter", "x": ["2017-10-28T00:00:00", "2017-10-28T01:00:00", "2017-10-28T02:00:00", "2017-10-28T03:00:00", "2017-10-28T04:00:00", "2017-10-28T05:00:00", "2017-10-28T06:00:00", "2017-10-28T07:00:00", "2017-10-28T08:00:00", "2017-10-28T09:00:00", "2017-10-28T10:00:00", "2017-10-28T11:00:00", "2017-10-28T12:00:00", "2017-10-28T13:00:00", "2017-10-28T14:00:00", "2017-10-28T15:00:00", "2017-10-28T16:00:00", "2017-10-28T17:00:00", "2017-10-28T18:00:00", "2017-10-28T19:00:00", "2017-10-28T20:00:00", "2017-10-28T21:00:00", "2017-10-28T22:00:00", "2017-10-28T23:00:00", "2017-10-29T00:00:00", "2017-10-29T01:00:00", "2017-10-29T02:00:00", "2017-10-29T03:00:00", "2017-10-29T04:00:00", "2017-10-29T05:00:00", "2017-10-29T06:00:00", "2017-10-29T07:00:00", "2017-10-29T08:00:00", "2017-10-29T09:00:00", "2017-10-29T10:00:00", "2017-10-29T11:00:00", "2017-10-29T12:00:00", "2017-10-29T13:00:00", "2017-10-29T14:00:00", "2017-10-29T15:00:00", "2017-10-29T16:00:00", "2017-10-29T17:00:00", "2017-10-29T18:00:00", "2017-10-29T19:00:00", "2017-10-29T20:00:00", "2017-10-29T21:00:00", "2017-10-29T22:00:00", "2017-10-29T23:00:00", "2017-10-30T00:00:00", "2017-10-30T01:00:00", "2017-10-30T02:00:00", "2017-10-30T03:00:00", "2017-10-30T04:00:00", "2017-10-30T05:00:00", "2017-10-30T06:00:00", "2017-10-30T07:00:00", "2017-10-30T08:00:00", "2017-10-30T09:00:00", "2017-10-30T10:00:00", "2017-10-30T11:00:00", "2017-10-30T12:00:00", "2017-10-30T13:00:00", "2017-10-30T14:00:00", "2017-10-30T15:00:00", "2017-10-30T16:00:00", "2017-10-30T17:00:00", "2017-10-30T18:00:00", "2017-10-30T19:00:00", "2017-10-30T20:00:00", "2017-10-30T21:00:00", "2017-10-30T22:00:00", "2017-10-30T23:00:00", "2017-10-31T00:00:00", "2017-10-31T01:00:00", "2017-10-31T02:00:00", "2017-10-31T03:00:00", "2017-10-31T04:00:00", "2017-10-31T05:00:00", "2017-10-31T06:00:00", "2017-10-31T07:00:00", "2017-10-31T08:00:00", "2017-10-31T09:00:00", "2017-10-31T10:00:00", "2017-10-31T11:00:00", "2017-10-31T12:00:00", "2017-10-31T13:00:00", "2017-10-31T14:00:00", "2017-10-31T15:00:00", "2017-10-31T16:00:00", "2017-10-31T17:00:00", "2017-10-31T18:00:00", "2017-10-31T19:00:00", "2017-10-31T20:00:00", "2017-10-31T21:00:00", "2017-10-31T22:00:00", "2017-10-31T23:00:00"], "y": [-0.3295818865299225, -0.4303319454193115, -0.323388934135437, -0.3588288724422455, -0.4294905960559845, -0.47509828209877014, -0.4355981945991516, -0.42869991064071655, -0.41209858655929565, -0.3593917489051819, -0.35250362753868103, -0.30488333106040955, -0.32435494661331177, -0.28900790214538574, -0.32172632217407227, -0.23627138137817383, -0.19943922758102417, -0.13406428694725037, -0.14729958772659302, -0.1677248775959015, -0.3070647120475769, -0.20533910393714905, -0.1977238953113556, -0.29104751348495483, -0.32058122754096985, -0.4820212423801422, -0.4401291012763977, -0.3817194402217865, -0.4378403425216675, -0.45608338713645935, -0.3489649295806885, -0.42137426137924194, -0.41981691122055054, -0.31348317861557007, -0.3052101731300354, -0.28227466344833374, -0.4012889564037323, -0.30505046248435974, -0.2433802634477615, -0.223413348197937, -0.24670545756816864, -0.20341485738754272, -0.16426372528076172, -0.2715131640434265, -0.21633705496788025, -0.23295995593070984, -0.2773250341415405, -0.35751837491989136, -0.39259615540504456, -0.5066223740577698, -0.4617106020450592, -0.4344673752784729, -0.4856155812740326, -0.47947239875793457, -0.4524945318698883, -0.4938838183879852, -0.4736164212226868, -0.38114362955093384, -0.3589770793914795, -0.37393510341644287, -0.4664826691150666, -0.4209396541118622, -0.3800652325153351, -0.31397196650505066, -0.36729276180267334, -0.27449166774749756, -0.3105567693710327, -0.3089967966079712, -0.33537811040878296, -0.3289809823036194, -0.2822067141532898, -0.4137554168701172, -0.5416671633720398, -0.4786807894706726, -0.4294260442256927, -0.5076782703399658, -0.5913574695587158, -0.4976896643638611, -0.4461187720298767, -0.5473694801330566, -0.4805601239204407, -0.4014628529548645, -0.4139625132083893, -0.3945460915565491, -0.35662153363227844, -0.3626084625720978, -0.3114738166332245, -0.35067248344421387, -0.30896812677383423, -0.2765408754348755, -0.2385675311088562, -0.2744271457195282, -0.3125867247581482, -0.2628542184829712, -0.3388215899467468, -0.35919421911239624]}, {"name": "MUFL_prediction", "type": "scatter", "x": ["2017-10-24T00:00:00", "2017-10-24T01:00:00", "2017-10-24T02:00:00", "2017-10-24T03:00:00", "2017-10-24T04:00:00", "2017-10-24T05:00:00", "2017-10-24T06:00:00", "2017-10-24T07:00:00", "2017-10-24T08:00:00", "2017-10-24T09:00:00", "2017-10-24T10:00:00", "2017-10-24T11:00:00", "2017-10-24T12:00:00", "2017-10-24T13:00:00", "2017-10-24T14:00:00", "2017-10-24T15:00:00", "2017-10-24T16:00:00", "2017-10-24T17:00:00", "2017-10-24T18:00:00", "2017-10-24T19:00:00", "2017-10-24T20:00:00", "2017-10-24T21:00:00", "2017-10-24T22:00:00", "2017-10-24T23:00:00", "2017-10-25T00:00:00", "2017-10-25T01:00:00", "2017-10-25T02:00:00", "2017-10-25T03:00:00", "2017-10-25T04:00:00", "2017-10-25T05:00:00", "2017-10-25T06:00:00", "2017-10-25T07:00:00", "2017-10-25T08:00:00", "2017-10-25T09:00:00", "2017-10-25T10:00:00", "2017-10-25T11:00:00", "2017-10-25T12:00:00", "2017-10-25T13:00:00", "2017-10-25T14:00:00", "2017-10-25T15:00:00", "2017-10-25T16:00:00", "2017-10-25T17:00:00", "2017-10-25T18:00:00", "2017-10-25T19:00:00", "2017-10-25T20:00:00", "2017-10-25T21:00:00", "2017-10-25T22:00:00", "2017-10-25T23:00:00", "2017-10-26T00:00:00", "2017-10-26T01:00:00", "2017-10-26T02:00:00", "2017-10-26T03:00:00", "2017-10-26T04:00:00", "2017-10-26T05:00:00", "2017-10-26T06:00:00", "2017-10-26T07:00:00", "2017-10-26T08:00:00", "2017-10-26T09:00:00", "2017-10-26T10:00:00", "2017-10-26T11:00:00", "2017-10-26T12:00:00", "2017-10-26T13:00:00", "2017-10-26T14:00:00", "2017-10-26T15:00:00", "2017-10-26T16:00:00", "2017-10-26T17:00:00", "2017-10-26T18:00:00", "2017-10-26T19:00:00", "2017-10-26T20:00:00", "2017-10-26T21:00:00", "2017-10-26T22:00:00", "2017-10-26T23:00:00", "2017-10-27T00:00:00", "2017-10-27T01:00:00", "2017-10-27T02:00:00", "2017-10-27T03:00:00", "2017-10-27T04:00:00", "2017-10-27T05:00:00", "2017-10-27T06:00:00", "2017-10-27T07:00:00", "2017-10-27T08:00:00", "2017-10-27T09:00:00", "2017-10-27T10:00:00", "2017-10-27T11:00:00", "2017-10-27T12:00:00", "2017-10-27T13:00:00", "2017-10-27T14:00:00", "2017-10-27T15:00:00", "2017-10-27T16:00:00", "2017-10-27T17:00:00", "2017-10-27T18:00:00", "2017-10-27T19:00:00", "2017-10-27T20:00:00", "2017-10-27T21:00:00", "2017-10-27T22:00:00", "2017-10-27T23:00:00"], "y": [-0.3136693835258484, -0.4825535118579865, -0.4674164056777954, -0.4333106577396393, -0.5270497798919678, -0.48819953203201294, -0.4413444399833679, -0.4635126888751984, -0.5104973316192627, -0.379749596118927, -0.4170100688934326, -0.3244268298149109, -0.3922155201435089, -0.3682058155536651, -0.23117756843566897, -0.246806800365448, -0.25506100058555603, -0.22341850399971008, -0.19540894031524655, -0.27491283416748047, -0.2526005804538727, -0.2228122055530548, -0.24122723937034607, -0.3874010741710663, -0.46399638056755066, -0.5319690108299255, -0.4859629273414612, -0.42637673020362854, -0.5235298871994019, -0.4848613440990448, -0.4495970904827118, -0.5226061344146729, -0.49445998668670654, -0.3915702402591706, -0.35026979446411133, -0.35054218769073486, -0.3614568710327149, -0.35620546340942383, -0.25212904810905457, -0.3176071047782898, -0.335326611995697, -0.1782805621623993, -0.16054674983024597, -0.28552359342575073, -0.3497236967086792, -0.24908339977264404, -0.2239721715450287, -0.3138158321380615, -0.5204830765724182, -0.4421454071998596, -0.479522168636322, -0.4376166760921478, -0.48495158553123474, -0.5087617635726929, -0.448098361492157, -0.4439389705657959, -0.5387447476387024, -0.3998788595199585, -0.404817670583725, -0.33762216567993164, -0.4374875128269195, -0.43563759326934814, -0.39762231707572937, -0.29675185680389404, -0.2422075867652893, -0.2524273097515106, -0.2246539294719696, -0.2558152377605438, -0.34667712450027466, -0.2433091402053833, -0.29714256525039673, -0.40125495195388794, -0.4362913966178894, -0.48764652013778687, -0.4731465280056, -0.42786556482315063, -0.565526008605957, -0.5632251501083374, -0.5216364860534668, -0.5268236398696899, -0.4855500459671021, -0.4695591926574707, -0.36663514375686646, -0.3515389561653137, -0.395008385181427, -0.3925882577896118, -0.3644450306892395, -0.2420940101146698, -0.2421802580356598, -0.2619193494319916, -0.2522519528865814, -0.3459148406982422, -0.22723433375358584, -0.21085217595100403, -0.21771883964538577, -0.3529844284057617]}], "layout": {"height": 600, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Forecast"}, "width": 1100, "xaxis": {"anchor": "y", "domain": [0, 0.94], "tickangle": -45}, "yaxis": {"anchor": "x", "domain": [0, 1]}, "yaxis2": {"anchor": "x", "overlaying": "y", "side": "right"}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_ts_forecasting(\n", "    test_data,\n", "    forecasts_,\n", "    forecast_columns=[\"MUFL\"],\n", "    timestamp_column=timestamp_column,\n", "    periodicity=\"1h\",\n", "    prediction_length=model.config.prediction_length,\n", "    context_length=context_length,\n", "    plot_start=0,\n", "    plot_end=context_length + model.config.prediction_length * 3,\n", "    num_predictions=3,\n", "    plot_stride=model.config.prediction_length,\n", "    title=\"Forecast\",\n", "    fig_size=(1100, 600),\n", "    plot_type=\"plotly\",\n", "    return_image=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_concl\"></a>\n", "# Conclusion\n", "\n", "This notebook showed how to perform inferencing in a Channel Independence PatchTST model. This is the last of three notebooks that were run in sequence using training and test data from the ETTh1 benchmark dataset, which represents sensor data from an electric transformer.\n", "\n", "The first notebook, `01_patch_tst_pretrain.ipynb`, created a pretrained model that was saved in your private storage. The second notebook, `02_patch_tst_fine_tune.ipynb`, loaded the pretrained model and created a fine tuned model, which was also saved in your private storage. This notebook performed inferencing on the fine tuned model. The goal of this demonstration was to forecast the future sensor values (load, oil temperature) of an electric transformer using test data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST3_learn\"></a>\n", "# Learn More\n", "\n", "[This paper](https://arxiv.org/pdf/2211.14730.pdf) provides detailed information on Channel Independence PatchTST, including evaluations of its performance on 8 popular datasets, including Weather, Traffic, Electricity, ILI and 4 Electricity Transformer Temperature datasets (ETTh1, ETTh2, ETTm1, ETTm2). These publicly available datasets have been extensively utilized for benchmarking. We featured one of them (ETTh1) in this notebook.\n", "\n", "If you have any questions or wish to schedule a technical deep dive, contact us by <NAME_EMAIL>."]}, {"cell_type": "markdown", "metadata": {}, "source": ["© 2023 IBM Corporation"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}