{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pretraining a Time Series Model using Channel Independence PatchTST\n", "\n", "<ul>\n", "<li>Contributors: IBM AI Research team and IBM Research Technology Education team\n", "<li>Contact for questions and technical support: <EMAIL>\n", "<li>Provenance: IBM Research\n", "<li>Version: 1.0.0\n", "<li>Release date: \n", "<li>Compute requirements: 4 CPU (preferrably 1 GPU)\n", "<li>Memory requirements: 16 GB\n", "<li>Notebook set: Time Series Foundation Model\n", "</ul>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary\n", "\n", "**Patch Time Series Transformer (PatchTST)** is a new method for long-term forecasting based on Transformer modeling. In PatchTST, a time series is segmented into subseries-level patches that are served as input tokens to Transformer. PatchTST was first proposed in 2023 in [this paper](https://arxiv.org/pdf/2211.14730.pdf). It can achieve state-of-the-art results when compared to other Transformer-based models.\n", "\n", "**Channel Independence PatchTST** is a variant of PatchTST where each channel contains a single univariate time series that shares the same embedding and Transformer weights across all the series.\n", "\n", "This notebook shows how to pretrain a Channel Independence PatchTST model. In this context, pretraining means that the model is trained in a self-supervised way using masking. Individual patches (small segments of the input time series) are masked and the model is trying to reconstruct the missing patches.\n", "\n", "This is the first of three notebooks that should be run in sequence using training and test data from the ETTh1 benchmark dataset, which represents oil temperature in an electric transformer. After running this notebook, a pretrained model will be saved in your private storage. The second notebook, `02_patch_tst_fine_tune.ipynb`, will load the pretrained model and create a fine tuned model, which will be also saved in your private storage. The third notebook, `03_patch_tst_inference.ipynb`, will perform inferencing using the fine tuned model, with a goal of predicting the future sensor values (loads, oil temperature) of an electric transformer."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Table of Contents\n", "\n", "* <a href=\"#TST1_intro\">Channel Independence PatchTST</a>\n", "* <a href=\"#TST1_codes\">Code Samples</a>\n", "    * <a href=\"#TST1_import\">Step 1. Imports</a>\n", "    * <a href=\"#TST1_datast\">Step 2. Load and prepare datasets </a>\n", "    * <a href=\"#TST1_config\">Step 3. Configure the PatchTST model </a>\n", "    * <a href=\"#TST1_trainm\">Step 4. Train model </a>\n", "* <a href=\"#TST1_concl\">Conclusion</a>\n", "* <a href=\"#TST1_learn\">Learn More</a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST1_intro\"></a>\n", "# Channel Independence PatchTST\n", "\n", "**Channel Independence PatchTST** is an efficient design of Transformer-based models for multivariate time series forecasting and self-supervised representation learning. It is demonstrated in the following diagram. It is based on two key components:\n", "\n", "- segmentation of time series into subseries-level patches that are served as input tokens to Transformer\n", "\n", "- channel independence where each channel contains a single univariate time series that shares the same embedding and Transformer weights across all the series.\n", "\n", "Patching design naturally has three-fold benefit: local semantic information is retained in the embedding; computation and memory usage of the attention maps are quadratically reduced given the same look-back window; and the model can attend longer history.\n", "\n", "Channel independence allows each time series to have its own embedding and attention maps while sharing the same model parameters across different channels.\n", "\n", "<div> <img src=\"./data/figures/patchTST.png\" alt=\"Drawing\" style=\"width: 600px;\"/></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST1_codes\"></a>\n", "# Code Samples\n", "\n", "This section includes documentation and code samples to demonstrate the use of the toolkit for pretraining."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST1_import\"></a>\n", "## Step 1. Imports "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from transformers import (\n", "    PatchTSTConfig,\n", "    PatchTSTForPretraining,\n", "    Trainer,\n", "    TrainingArguments,\n", ")\n", "\n", "from tsfm_public.toolkit.dataset import PretrainDFDataset\n", "from tsfm_public.toolkit.time_series_preprocessor import TimeSeriesPreprocessor\n", "from tsfm_public.toolkit.util import select_by_index"]}, {"cell_type": "markdown", "metadata": {}, "source": [" <a id=\"TST1_datast\"></a>\n", "## Step 2. Load and prepare datasets\n", "\n", "\n", " In the next cell, please adjust the following parameters to suit your application:\n", " - dataset_path: path to local .csv file, or web address to a csv file for the data of interest. Data is loaded with pandas, so anything supported by\n", "   `pd.read_csv` is supported: (https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html).\n", " - timestamp_column: column name containing timestamp information, use None if there is no such column\n", " - id_columns: List of column names specifying the IDs of different time series. If no ID column exists, use []\n", " - forecast_columns: List of columns to be modeled\n", " - context_length: The amount of historical data used as input to the model. Windows of the input time series data with length equal to\n", "   context_length will be extracted from the input dataframe. In the case of a multi-time series dataset, the context windows will be created\n", "   so that they are contained within a single time series (i.e., a single ID).\n", " - train_start_index, train_end_index: the start and end indices in the loaded data which delineate the training data.\n", " - eval_start_index, eval_end_index: the start and end indices in the loaded data which delineate the evaluation data.\n", "\n", " The data is first loaded into a Pandas dataframe and split into training and evaluation parts. Then the pandas dataframes are converted\n", " to the appropriate torch dataset needed for training.\n", " \n", " The specific data loaded here is Electricity Transformer Temperature (ETT) data - including load, oil temperature in an electric transformer."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["dataset_path = \"https://raw.githubusercontent.com/zhouhaoyi/ETDataset/main/ETT-small/ETTh1.csv\"\n", "timestamp_column = \"date\"\n", "id_columns = []\n", "forecast_columns = [\"HUFL\", \"HULL\", \"MUFL\", \"MULL\", \"LUFL\", \"LULL\", \"OT\"]\n", "\n", "context_length = 512\n", "\n", "train_start_index = None  # None indicates beginning of dataset\n", "train_end_index = 12 * 30 * 24\n", "\n", "# we shift the start of the evaluation period back by context length so that\n", "# the first evaluation timestamp is immediately following the training data\n", "eval_start_index = 12 * 30 * 24 - context_length\n", "eval_end_index = 12 * 30 * 24 + 4 * 30 * 24"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(\n", "    dataset_path,\n", "    parse_dates=[timestamp_column],\n", ")\n", "\n", "train_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=train_start_index,\n", "    end_index=train_end_index,\n", ")\n", "eval_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=eval_start_index,\n", "    end_index=eval_end_index,\n", ")\n", "\n", "tsp = TimeSeriesPreprocessor(\n", "    timestamp_column=timestamp_column,\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    scaling=True,\n", ")\n", "tsp.train(train_data)\n", "train_dataset = PretrainDFDataset(\n", "    tsp.preprocess(train_data),\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", ")\n", "eval_dataset = PretrainDFDataset(\n", "    tsp.preprocess(eval_data),\n", "    timestamp_column=timestamp_column,\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST1_config\"></a>\n", "## Step 3. Configure the PatchTST model\n", "\n", " The settings below control the different components in the PatchTST model.\n", "  - num_input_channels: the number of input channels (or dimensions) in the time series data. This is\n", "    automatically set to the number for forecast columns.\n", "  - context_length: As described above, the amount of historical data used as input to the model.\n", "  - patch_length: The length of the patches extracted from the context window (of length `context_length`).\n", "  - patch_stride: The stride used when extracting patches from the context window.\n", "  - mask_ratio: The fraction of input patches that are completely masked for the purpose of pretraining the model.\n", "  - d_model: Dimension of the transformer layers.\n", "  - encoder_attention_heads: The number of attention heads for each attention layer in the Transformer encoder.\n", "  - encoder_layers: The number of encoder layers.\n", "  - encoder_ffn_dim: Dimension of the intermediate (often referred to as feed-forward) layer in the encoder.\n", "  - dropout: Dropout probability for all fully connected layers in the encoder.\n", "  - head_dropout: Dropout probability used in the head of the model.\n", "  - pooling_type: Pooling of the embedding. `\"mean\"`, `\"max\"` and `None` are supported.\n", "  - channel_attention: Activate channel attention block in the Transformer to allow channels to attend each other.\n", "  - scaling: Whether to scale the input targets via \"mean\" scaler, \"std\" scaler or no scaler if `None`. If `True`, the\n", "    scaler is set to `\"mean\"`.\n", "  - loss: The loss function for the model corresponding to the `distribution_output` head. For parametric\n", "    distributions it is the negative log likelihood (`\"nll\"`) and for point estimates it is the mean squared\n", "    error `\"mse\"`.\n", "  - pre_norm: Normalization is applied before self-attention if pre_norm is set to `True`. Otherwise, normalization is\n", "    applied after residual block.\n", "  - norm: Normalization at each Transformer layer. Can be `\"BatchNorm\"` or `\"LayerNorm\"`.\n", "\n", " We recommend that you only adjust the values in the next cell."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["patch_length = 12\n", "patch_stride = patch_length"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["config = PatchTSTConfig(\n", "    num_input_channels=tsp.num_input_channels,\n", "    context_length=context_length,\n", "    patch_length=patch_length,\n", "    patch_stride=patch_stride,\n", "    mask_ratio=0.4,\n", "    d_model=128,\n", "    encoder_attention_heads=16,\n", "    encoder_layers=3,\n", "    encoder_ffn_dim=512,\n", "    dropout=0.2,\n", "    head_dropout=0.2,\n", "    pooling_type=None,\n", "    channel_attention=False,\n", "    scaling=\"std\",\n", "    loss=\"mse\",\n", "    pre_norm=True,\n", "    norm=\"batchnorm\",\n", ")\n", "pretraining_model = PatchTSTForPretraining(config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST1_trainm\"></a>\n", "## Step 4. Train model\n", "\n", " Trains the PatchTST model based on the Mask-based pretraining strategy. We recommend that the user keep the settings\n", " as they are below, with the exception of:\n", "  - num_train_epochs: The number of training epochs. This may need to be adjusted to ensure sufficient training.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["CODECARBON : No CPU tracking mode found. Falling back on CPU constant mode.\n", "CODECARBON : Failed to match CPU TDP constant. Falling back on a global constant.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c08dc03da1fd4851ac3fe6f3a2a192f5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3051 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'loss': 0.8531, 'learning_rate': 3.3333333333333335e-05, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "94e5c506a5a644efa130e5cfefc4160b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/46 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Checkpoint destination directory ./checkpoint/pretrain/checkpoint-1017 already exists and is non-empty. Saving will proceed but saved results may be invalid.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.7045533061027527, 'eval_runtime': 1.4714, 'eval_samples_per_second': 1957.984, 'eval_steps_per_second': 31.263, 'epoch': 1.0}\n", "{'loss': 0.795, 'learning_rate': 1.6666666666666667e-05, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f343efc0e43c41aba1f5638d25976a60", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/46 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Checkpoint destination directory ./checkpoint/pretrain/checkpoint-2034 already exists and is non-empty. Saving will proceed but saved results may be invalid.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6895386576652527, 'eval_runtime': 1.2227, 'eval_samples_per_second': 2356.305, 'eval_steps_per_second': 37.622, 'epoch': 2.0}\n", "{'loss': 0.7814, 'learning_rate': 0.0, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "250b62bab76748c8924d664fc84f1fdd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/46 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Checkpoint destination directory ./checkpoint/pretrain/checkpoint-3051 already exists and is non-empty. Saving will proceed but saved results may be invalid.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6782698035240173, 'eval_runtime': 1.2212, 'eval_samples_per_second': 2359.122, 'eval_steps_per_second': 37.667, 'epoch': 3.0}\n", "{'train_runtime': 100.587, 'train_samples_per_second': 242.447, 'train_steps_per_second': 30.332, 'train_loss': 0.8098250357528781, 'epoch': 3.0}\n"]}, {"data": {"text/plain": ["TrainOutput(global_step=3051, training_loss=0.8098250357528781, metrics={'train_runtime': 100.587, 'train_samples_per_second': 242.447, 'train_steps_per_second': 30.332, 'train_loss': 0.8098250357528781, 'epoch': 3.0})"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["training_args = TrainingArguments(\n", "    output_dir=\"./checkpoint/pretrain\",\n", "    per_device_train_batch_size=8,\n", "    per_device_eval_batch_size=64,\n", "    num_train_epochs=3,  # 50,\n", "    evaluation_strategy=\"epoch\",\n", "    save_strategy=\"epoch\",\n", "    save_total_limit=5,\n", "    logging_strategy=\"epoch\",\n", "    load_best_model_at_end=True,\n", "    # max_steps=10,  # For a quick test\n", "    label_names=[\"past_values\"],\n", ")\n", "pretrainer = Trainer(\n", "    model=pretraining_model,\n", "    args=training_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", ")\n", "\n", "pretrainer.train()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save the model"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["pretrainer.save_model(\"model/pretrained\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST1_concl\"></a>\n", "# Conclusion\n", "\n", "This notebook showed how to pretrain a Channel Independence PatchTST model. In this context, pretraining means that the model is trained in a self-supervised way using masking. Individual patches (small segments of the input time series) are masked and the model is trying to reconstruct the missing patches.\n", "\n", "This is the first of three notebooks that should be run in sequence using training and test data from the ETTh1 benchmark dataset, which represents sensor data from an electric transformer.\n", "\n", "The above output shows the performance (training loss and validation loss) of the model during the pretraining process. In this case we are using mean squared error (MSE) as a loss function. As the epochs progress we want performance to improve. We would like to see the training and validation losses decrease rapidly for a few epochs and then converge. Validation loss should be relatively close to training loss. Large differences between training and validation losses may be indicative of overfitting (training much lower than validation) or distribution shift between training and validation datasets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST1_learn\"></a>\n", "# Learn More\n", "\n", "[This paper](https://arxiv.org/pdf/2211.14730.pdf) provides detailed information on Channel Independence PatchTST, including evaluations of its performance on 8 popular datasets, including Weather, Traffic, Electricity, ILI and 4 Electricity Transformer Temperature datasets (ETTh1, ETTh2, ETTm1, ETTm2). These publicly available datasets have been extensively utilized for benchmarking. We featured one of them (ETTh1) in this notebook.\n", "\n", "If you have any questions or wish to schedule a technical deep dive, contact us by <NAME_EMAIL>."]}, {"cell_type": "markdown", "metadata": {}, "source": ["© 2023 IBM Corporation"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}