from helpers import *
from tqdm import tqdm # type: ignore
from ttm_utils import *
from data_postprocess import *
import json
from pandas.tseries.offsets import BDay

default_value =int(config.get(section='default-values', option='default_value'))
eq_ttm_model = config.get(section='es-indexes', option='ttm_model')
eq_ttm_model_metrics = config.get(section='es-indexes', option='ttm_model_metrics')

aieq = config.get(section='geos', option='aieq_geo')
indeq = config.get(section='geos', option='indeq_geo')

metrics_prefix = config.get(section='daily-run', option='metrics_prefix')
logs_prefix = config.get(section='daily-run', option='logs_prefix')
metrics_bucket = config.get(section='daily-run', option='bucket_name')
rolling_period = int(config.get(section='periods', option='rolling_period'))

url_prefix = config.get(section='url', option='url_prefix')

file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "daily-run-parameters.json")

with open(file_path, "r") as json_file:
    daily_run_parameters = json.load(json_file)

def get_metrics(isin, geo, schedular):

    date = datetime.strftime(datetime.now(), f'%Y-%m-%d')
    current_year = int(date.split('-')[0])
    index = eq_ttm_model
    group = get_es_data(isin, [current_year - 1, current_year], index)
    group = group[group["schedular"] == schedular]
    metrics_period = schedular_dict[schedular.lower()]
    group.reset_index(drop=True, inplace=True)
    if len(group) <= rolling_period:
        raise ValueError(f"not enough predictions to compute metrics for isin-> {isin} and schedular-> {schedular}")
    
    actual_column = f"actual_{schedular.lower()}_return"
    prediction_column = f"actual_{schedular.lower()}_return_predictions"
    group[actual_column] = group["closeprice"].pct_change(periods=metrics_period) * 100
    group[prediction_column] = group[prediction_column].shift(metrics_period)
    group[actual_column].fillna(default_value, inplace=True)
    group[prediction_column].fillna(default_value, inplace=True)
    group = calculate_metrics(group, isin, period=rolling_period, prediction_column=prediction_column, actual_column=actual_column)
    if 'lgbm-er-predictions' in group.columns.tolist():
        group.drop(columns=['lgbm-er-predictions'], inplace=True)    
    group['date'] = group['date'].dt.strftime('%Y-%m-%d')
    record = group.iloc[-1]
    
    return record
    


def daily_metrics(geo, schedular):

    if not(geo in [aieq, indeq]):
        raise ValueError(f'invalid geography passed for daily-predictions, geo should be one of [{aieq}, {indeq}], but {geo} passed') 
    import requests # type: ignore
    firms_url =  f'{url_prefix}{geo}'
    geo_json = requests.get(f'{firms_url}').json()
    geo_companies = pd.DataFrame(geo_json['data'][f'masteractivefirms_{geo}'])
    geo_isins = geo_companies['isin'].unique()
    results = []
    failed_isins = []
    for isin in tqdm(geo_isins):
        try:
            record = get_metrics(isin, geo, schedular)
            results.append(record)
            logging.info(f'daily-metrics computation successfull for isin: {isin}')
        except Exception as e:
            failed_isins.append(isin)
            logging.error(f'daily-metrics computation failed for isin: {isin} with error: {e}')
    
    import datetime
    current_date = datetime.date.today()
    date_needed = (current_date-BDay(1)).date().strftime("%Y-%m-%d")
    log_key = logs_prefix + f'all-data/{geo}/{schedular}/{date_needed}.log'
    upload_file_to_s3('ttm.log', metrics_bucket, log_key)
    
    from datetime import datetime
    results = pd.DataFrame(results)
    results.reset_index(drop=True, inplace=True)
    results['updated_at'] = datetime.strftime(datetime.now(), '%Y-%m-%d')
    results = results[results['date'] == date_needed]
    results.reset_index(drop=True, inplace=True)

    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-metrics']['total_isins'] = len(geo_isins)
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-metrics']['successful_runs'] = len(results)
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-metrics']['failed_runs'] = len(geo_isins) - len(results)
    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "daily-run-parameters.json")
    with open(file_path, 'w') as json_file:
        json.dump(daily_run_parameters, json_file, indent=4)
    
    if len(results) == 0:
        return
    metrics_key = metrics_prefix + f'all-data/{geo}/{schedular}/{date_needed}.csv'
    results.dropna(inplace=True)
    results.reset_index(inplace=True, drop=True)
    df2s3(results, metrics_bucket, metrics_key)
    try:
        version_control(results, geo, schedular, mode='metrics')
    except:
        pass
    log_key = logs_prefix + f'all-data/{geo}/{schedular}/{date_needed}.log'
    upload_file_to_s3('ttm.log', metrics_bucket, log_key)
    if (geo==aieq and schedular ==monthly_schedular):
        metrics_key = metrics_prefix + f'{date_needed}.csv'
        df2s3(results, metrics_bucket, metrics_key)
        log_key = logs_prefix + f'{date_needed}.log'
        upload_file_to_s3('ttm.log', metrics_bucket, log_key)
    results.replace({np.nan: None}, inplace=True)
    documents = upload_data_to_elasticsearch(client, results, es_index=eq_ttm_model_metrics, schedular=schedular)

    return results
