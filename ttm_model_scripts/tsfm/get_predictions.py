# prediction-pipeline
from ttm_utils import *
from data_pipeline import *
from data_postprocess import *

import numpy as np # type: ignore
import sys
import json
import traceback
from pandas.tseries.offsets import BDay # type: ignore
from typing import Any
from tqdm import tqdm # type: ignore
from tsfm_public.models.tinytimemixer import TinyTimeMixerForPrediction

url_prefix = config.get(section='url', option='url_prefix')

aieq = config.get(section='geos', option='aieq_geo')
indeq = config.get(section='geos', option='indeq_geo')

preds_prefix = config.get(section='daily-run', option='preds_prefix')
logs_prefix = config.get(section='daily-run', option='logs_prefix')
preds_bucket = config.get(section='daily-run', option='bucket_name')
vc_bucket = config.get(section='version-bucket', option='bucket_name')

eq_ttm_model = config.get(section='es-indexes', option='ttm_model')

default_value = int(config.get(section='default-values', option='default_value'))
default_mode = config.get(section='default-values', option='default_mode')

file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "sector-map.json")

with open(file_path, "r") as json_file:
    sector_map = json.load(json_file)

file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ts-dict.json")

with open(file_path, "r") as json_file:
    ts_dict = json.load(json_file)

file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "daily-run-parameters.json")

with open(file_path, "r") as json_file:
    daily_run_parameters = json.load(json_file)

sector_average_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "sector-average.csv")
sector_average = pd.read_csv(sector_average_path)
sector_average['sector'].fillna('NA', inplace=True)

def get_predictions(isin, geo, schedular):
    
    model_key = model_prefix + f'{geo}/{schedular.lower()}/{individual_model_suffix}/ttm_{isin}.bin'
    model = TinyTimeMixerForPrediction.from_pretrained("ibm/TTM", revision=ttm_model_revision, head_dropout=float(head_dropout_rate))
    
    # check whether individual model is present for the current isin and schedular.
    try:
        model = read_model_from_s3(model, bucket_name, model_key)
        mode = 'individual'
        timestamp = ts_dict.get(model_key, 'NA')
        model_id = f'ttm_{isin}_{timestamp}'
    except:
        logging.warn(f"\033[93mWARNING:\033[0m individual model is not available for given {isin} and {schedular}. reverting to sector-shared models for predictions!")
        
        current_sector = sector_map.get(isin, 'NA')
        sector_model_key = model_prefix + f'{geo}/{schedular.lower()}/{shared_model_suffix}/ttm_{current_sector}.bin'
        timestamp = ts_dict.get(sector_model_key, 'NA')
        model_id = f'ttm_{current_sector}_{timestamp}'
        model = read_model_from_s3(model, bucket_name, sector_model_key)
        mode = 'shared'
        
    trainer = initialize_trainer(model, schedular)
    data, record = get_data(isin, geo, schedular)
    dataset = get_input_data(isin, data, schedular)
    preds_fewshot = trainer.predict(dataset)
    predictions_fewshot = preds_fewshot[0][0]
    final_preds_fewshot = predictions_fewshot[:,0,:]
    final_preds_fewshot = final_preds_fewshot.flatten()
    record['ttm-predictions'] = final_preds_fewshot[0]
    record['mode'] = mode
    record['model-id'] = model_id
    return record

def default_predictions(isin, geo, schedular):

    record = get_latest_record(isin, geo, schedular)
    current_sector = sector_map.get(isin, 'NA')
    average = sector_average[sector_average['sector'] == current_sector]['average'].tolist()[0]
    record['ttm-predictions'] = average
    record['mode'] = 'sector-average'
    record['sector'] =current_sector
    return record

def daily_predictions(geo, schedular):

    if not(geo in [aieq, indeq]):
        raise ValueError(f'invalid geography passed for daily-predictions, geo should be one of [{aieq}, {indeq}], but {geo} passed') 
    import requests # type: ignore
    firms_url =  f"{url_prefix}{geo}"
    geo_json = requests.get(f'{firms_url}').json()
    geo_companies = pd.DataFrame(geo_json['data'][f'masteractivefirms_{geo}'])
    geo_isins = geo_companies['isin'].unique()
    results = []
    isins_left_out = []
    for isin in tqdm(geo_isins):
        try:
            
            record = get_predictions(isin, geo, schedular)
            current_sector = sector_map.get(isin, 'NA')
            record['sector'] = current_sector
            results.append(record)
            ttm_pred = record['ttm-predictions']
            sec_avg = sector_average.loc[sector_average["sector"] == current_sector, 'average']
            num_isins = sector_average.loc[sector_average["sector"] == current_sector, 'num_isins']
            sec_avg =  (sec_avg * num_isins + ttm_pred) / (num_isins + 1)
            sector_average.loc[sector_average["sector"] == current_sector, 'average'] = sec_avg
            sector_average.loc[sector_average["sector"] == current_sector, 'num_isins'] = num_isins + 1

            logging.info(f'daily-predictions successfull for isin: {isin}')
        except Exception as e:
            isins_left_out.append(isin)
            logging.error(f'daily-predictions failed for isin: {isin} with error: {e}')
    
    failed_isins = []
    import datetime
    current_date = datetime.date.today()
    date_needed = (current_date-BDay(1)).date().strftime("%Y-%m-%d")
    for isin in tqdm(isins_left_out):
        try:
            stock_price = get_stock_price(isin, geo_companies, start_date=date_needed, end_date=date_needed)
            if len(stock_price) == 0:
                failed_isins.append(isin)
                continue
            adj_closeprice = stock_price['adj_close']
            record = default_predictions(isin, geo, schedular)
            record['closeprice'] = adj_closeprice.tolist()[0]
            record['model-id'] = f'sector-average_{date_needed}'
            results.append(record)
            logging.info(f'daily-predictions successfull for isin: {isin} using sector-average')
        except Exception as e:
            failed_isins.append(isin)
            logging.error(f'daily-predictions failed for isin: {isin} with error: {e} while filling with sector-average.')
    
    log_key = logs_prefix + f'all-data/{geo}/{schedular}/{date_needed}.log'
    upload_file_to_s3('ttm.log', preds_bucket, log_key)
    
    results = pd.DataFrame(results)
    results.reset_index(drop=True, inplace=True)
    defaults = rectify(geo_isins, schedular)
    defaults['date'] = date_needed
    
    actual_return = f'actual_{schedular.lower()}_return'
    expected_return = f'actual_{schedular.lower()}_return_predictions'
    required_columns = ['isin', 'schedular', 'date', 'closeprice', 'volume', actual_return, expected_return, 'mode', 'sector', 'model-id', 'tag', 'updated_at']
    if len(results) == 0:
        results = pd.DataFrame(columns=required_columns)
    else:
        results.rename(columns={'er-predictions': 'lgbm-er-predictions', 'ttm-predictions': f'actual_{schedular.lower()}_return_predictions'}, inplace=True)
        results.drop(columns=['lgbm-er-predictions'], inplace=True)
    
    results[f'actual_{schedular.lower()}_return_predictions'] = results[f'actual_{schedular.lower()}_return_predictions'].replace({np.nan: None})
    results[f'actual_{schedular.lower()}_return'] = results[f'actual_{schedular.lower()}_return'].replace({np.nan: None})
    
    results = results[results['date'] == date_needed]
    generated_predictions = len(results)
    results = pd.concat([results, defaults[required_columns]]).drop_duplicates(subset=['isin'], keep='first')
    forward_predictions = len(results) - generated_predictions
    results = results[results['date'] == date_needed]
    results.reset_index(drop=True, inplace=True)
    results['mode'].fillna(default_mode, inplace=True)
    results['model-id'].fillna(default_mode, inplace=True)
    results['sector'] = results['isin'].map(lambda x: sector_map.get(x, 'NA'))
    preds_key = preds_prefix + f'all-data/{geo}/{schedular}/{date_needed}.csv'
    df2s3(results, preds_bucket, preds_key)
    try:
        version_control(results, geo, schedular, mode='predictions')
    except:
        pass
    log_key = logs_prefix + f'all-data/{geo}/{schedular}/{date_needed}.log'
    upload_file_to_s3('ttm.log', preds_bucket, log_key)
    if (geo==aieq and schedular ==monthly_schedular):
        preds_key = preds_prefix + f'{date_needed}.csv'
        df2s3(results, preds_bucket, preds_key)
        log_key = logs_prefix + f'{date_needed}.log'
        upload_file_to_s3('ttm.log', preds_bucket, log_key)
    results.replace({np.nan: None}, inplace=True)
    documents = upload_data_to_elasticsearch(client, results, es_index=eq_ttm_model, schedular=schedular)
    
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-predictions']['total_isins'] = len(geo_isins)
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-predictions']['successful_runs'] = len(results)
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-predictions']['failed_runs'] = len(geo_isins) - len(results)
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-predictions']['failed_isins'] = list(set(geo_isins) - set(results['isin'].tolist()))
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-predictions']['forward-filled-isins'] = forward_predictions
    daily_run_parameters[f'{geo}_{schedular.lower()}']['daily-predictions']['predictions == 0'] = len(results[results[f'actual_{schedular.lower()}_return_predictions'] == 0])
    daily_run_parameters['date'] = date_needed
    
    with open(file_path, 'w') as json_file:
        json.dump(daily_run_parameters, json_file, indent=4)

    return results