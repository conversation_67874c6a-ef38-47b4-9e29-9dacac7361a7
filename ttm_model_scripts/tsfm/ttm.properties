[bucket]
bucket_name = micro-ops-output

[daily-run]
bucket_name = portfolio-experiments-test
metrics_prefix = consolidated_er_best_model/v1/daily_run/ttm/metrics/
logs_prefix = consolidated_er_best_model/v1/daily_run/ttm/logs/
preds_prefix=consolidated_er_best_model/v1/daily_run/ttm/predictions/
retraining_queue=test/ttm-production/release-1.0/retraining-queue.csv
data_prefix=test/ttm-production/release-1.0/

[version-bucket]
bucket_name=eq-model-output
model_name=ttm_model

[opensearch_prod]
host=search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com
port=443
key_id=********************
secret=xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV
region=us-east-1
service=es

[url]
aieq_url=http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aieq
tier1_url=http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=tier1
indeq_url=http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=indeq
indt1_url=http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=indt1
url_prefix=http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=
snp_url=http://************:8080//stockhistory/getstocksbyisin
sp_global_url=https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json

[model-hyperparameters]
context_length=1024
forecast_length=96
batch_size=96
scaling=Trues3_ut
fewshot_fraction=1
learning_rate=0.001
epochs=30
head_dropout_rate=0.3
decoder_mode=0
backbone_freeze=False
early_stopping_patience=10
early_stopping_threshold=0.01
ttm_model_revision=1024_96_v1

[aws-s3]
aws_access_key=********************
aws_secret_key=bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/

[model-key]
model_prefix=test/ttm-production/release-1.0/
individual_model_suffix=individual/models
shared_model_suffix=shared/models

[ibm-credentials]
user=<EMAIL>
ibm_apikey=2eTXFIMpFGpB_-cZf_KIoO3UWGNQHC_wvwZwwwPXi1zu
ibm_url=https://us-south.ml.cloud.ibm.com

[schedulars]
monthly_schedular=Monthly
quarterly_schedular=Quarterly
daily_schedular=Daily
weekly_schedular=Weekly

[geos]
indeq_geo=indeq
aieq_geo=aieq

[es-indexes]
er_model=eq_er_model
finance_model=eq_financial_model
ttm_model=eq_ttm_model
cat_er_model=eq_cat_er_model
ttm_model_metrics=eq_ttm_model_metrics

[default-values]
default_value=-100
default_mode=forward-filled

[git-repos]
tsfm_url=https://<EMAIL>/naga-sai-2003/tsfm.git

[periods]
rolling_period=22
monthly-monitor-period=22
weekly-monitor-period=11

[snp-credentials]
snp_function=GDSHE
close_mnemonic=IQ_CLOSEPRICE
period_type=IQ_FY
frequency=Daily
head_auth=Basic ************************************************
content_type=application/json

[data-verification]
threshold = 0.01
start_year=2009