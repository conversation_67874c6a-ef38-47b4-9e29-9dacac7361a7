import sys
from core.pipes.validator_pipe import *
from datetime import datetime, timedelta

if __name__ == '__main__':
    print('arguments', sys.argv)

    if len(sys.argv) <= 3:
        raise ValueError(f'invalid arguments for geo and schedular and env.')
    
    geo, schedular, env = sys.argv[1], sys.argv[2], sys.argv[3]
    dval_pipe = DataValidatorPipe(env)
    
    date = (datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')
    dval_pipe.validate(geo, date)