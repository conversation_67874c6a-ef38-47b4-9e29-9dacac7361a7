from utils  import *
from datetime import datetime
import numpy as np # type: ignore
from typing import List
import requests # type: ignore

snp_url = config.get(section='url', option='snp_url')

monthly_schedular = config.get(section='schedulars', option='monthly_schedular')
quarterly_schedular = config.get(section='schedulars', option='quarterly_schedular')
daily_schedular = config.get(section='schedulars', option='daily_schedular')
weekly_schedular = config.get(section='schedulars', option='weekly_schedular')

indeq = config.get(section='geos', option='indeq_geo')
aieq = config.get(section='geos', option='aieq_geo')

eq_er_model = config.get(section='es-indexes', option='er_model')
eq_cat_er_model = config.get(section='es-indexes', option='cat_er_model')
eq_financial_model = config.get(section='es-indexes', option='finance_model')
eq_ttm_model = config.get(section='es-indexes', option='ttm_model')
context_length = int(config.get(section='model-hyperparameters', option='context_length'))

snp_function = config.get(section='snp-credentials', option='snp_function')
close_mnemonic = config.get(section='snp-credentials', option='close_mnemonic')
period_type=config.get(section='snp-credentials', option='period_type')
frequency=config.get(section='snp-credentials', option='frequency')
head_auth=config.get(section='snp-credentials', option='head_auth')
content_type=config.get(section='snp-credentials', option='content_type')

sp_global_url=config.get(section='url', option='sp_global_url')

days = {
    monthly_schedular.lower(): 22,
    quarterly_schedular.lower(): 66,
    daily_schedular.lower(): 2,
}

def get_data(isin, geo, schedular):
    if geo == indeq:
        
        cols = ['isin', 'date', 'closeprice', 'volume', 'actual_monthly_return_predictions', 'schedular']
        date = datetime.strftime(datetime.now(), f'%Y-%m-%d')
        year = int(date.split('-')[0])
        data = get_es_data(isin, [year-7, year], eq_er_model)
        
        if len(data[data['schedular'] == schedular]):
            data = data[data['schedular'] == schedular]
        else:
            data = data[data['schedular'] == monthly_schedular]
        
        data.reset_index(drop=True, inplace=True)
        data = data[cols]

        dys = days[schedular.lower()]

        # compute true-values for the model.
        actual_return = f'actual_{schedular.lower()}_return'
        data[actual_return] = 100 * data['closeprice'].pct_change(periods=dys)
        data[actual_return] = data[actual_return].shift(-dys)

        if schedular == monthly_schedular:
            data.rename(columns={'actual_monthly_return_predictions': 'er-predictions'}, inplace=True)
        
        elif schedular == quarterly_schedular:
            data.rename(columns={'actual_monthly_return_predictions': 'actual_22_day_preds'}, inplace=True)
            data['actual_44_day_preds'] = data['actual_22_day_preds'].shift(-22)
            data['actual_66_day_preds'] = data['actual_44_day_preds'].shift(-22)
            data['er-predictions'] = 100 * ( (data['actual_22_day_preds']/100 + 1) * (data['actual_44_day_preds']/100 + 1) * (data['actual_66_day_preds']/100 + 1) - 1)

        else:
            raise ValueError(f'invalid schedular passed! schedular must be either {monthly_schedular} or {quarterly_schedular} for {indeq} - geography. but given {schedular}')
        
    elif geo == aieq:
        date = datetime.strftime(datetime.now(), f'%Y-%m-%d')
        year = int(date.split('-')[0])
        data = get_es_data(isin, [year-7, year], eq_er_model)

        if len(data[data['schedular'] == schedular]):
            data = data[data['schedular'] == schedular]
        else:
            data = data[data['schedular'] == monthly_schedular]

        data.reset_index(drop=True, inplace=True)
        data.drop(columns=['schedular'], inplace=True)

        if schedular == monthly_schedular:
            data.rename(columns={'actual_monthly_return_predictions': 'er-predictions'}, inplace=True)
        
        elif schedular == quarterly_schedular:
            data.rename(columns={'actual_monthly_return_predictions': 'actual_22_day_preds'}, inplace=True)
            data['actual_44_day_preds'] = data['actual_22_day_preds'].shift(-22)
            data['actual_66_day_preds'] = data['actual_44_day_preds'].shift(-22)
            data['er-predictions'] = 100 * ( (data['actual_22_day_preds']/100 + 1) * (data['actual_44_day_preds']/100 + 1) * (data['actual_66_day_preds']/100 + 1) - 1)
        
        elif schedular == daily_schedular:
            data.rename(columns={'actual_daily_return_predictions': 'actual_1_day_preds'}, inplace=True)
            data['actual_2_day_preds'] = data['actual_1_day_preds'].shift(-1)
            data['er-predictions'] = 100 * ((data['actual_2_day_preds']/100 + 1) * (data['actual_1_day_preds']/100 + 1) - 1)

        else:
            raise ValueError(f'invalid schedular passed! schedular must be one of [{daily_schedular}, {quarterly_schedular}, {monthly_schedular}] for {aieq} - geography. but given {schedular}')

        data = data[['isin', 'date', 'er-predictions']]
        cols = ['isin', 'date', 'closeprice', 'volume', 'schedular']
        cp_data = get_es_data(isin, [year-7, year], eq_financial_model)
        cp_data = cp_data[cols]

        if len(cp_data[cp_data['schedular'] == schedular]):
            cp_data = cp_data[cp_data['schedular'] == schedular]
        else:
            cp_data = cp_data[cp_data['schedular'] == monthly_schedular]
        
        cp_data['date'] = pd.to_datetime(cp_data['date'], errors='coerce')
        data['date'] = pd.to_datetime(data['date'])

        data = pd.merge(cp_data, data, on=['date', 'isin'], how='left')
        data.reset_index(drop=True, inplace=True)
        data['closeprice'] = data['closeprice'].astype(np.float64)
        data['closeprice'].replace(to_replace=0.0, value=np.nan, inplace=True)
        data['closeprice'].ffill(inplace=True)
        data['closeprice'].bfill(inplace=True)

        dys = days[schedular.lower()]

        # compute true-values for the model.
        actual_return = f'actual_{schedular.lower()}_return'
        data[actual_return] = 100 * data['closeprice'].pct_change(periods=dys)
        data[actual_return] = data[actual_return].shift(-dys)
    
    else:
        raise ValueError(f'invalid geography. geography must be one of [{indeq}, {aieq}], but given {geo}')
    
    actual_return = f'actual_{schedular.lower()}_return'
    expected_return = f'er-predictions'
    cols = ['isin', 'date', 'closeprice', 'volume', 'schedular', actual_return, expected_return]
    data = data[cols]
    data['schedular'] = schedular
    data['tag'] = geo
    data['updated_at'] = datetime.strftime(datetime.now(), '%Y-%m-%d')
    data['date'] = data['date'].dt.strftime('%Y-%m-%d')
    data = data[-(context_length+1):]
    data.reset_index(drop=True, inplace=True)
    record = data.iloc[-1].to_dict()
    data = data.iloc[:-1]
    data.reset_index(drop=True, inplace=True)
    return data, record

def get_latest_record(isin, geo, schedular):

    try:
        date = datetime.strftime(datetime.now(), f'%Y-%m-%d')
        year = int(date.split('-')[0])
        cols = ['isin', 'date', 'closeprice', 'volume', 'schedular']
        cp_data = get_es_data(isin, [year-7, year], eq_financial_model)
        cp_data = cp_data[cols]
        if len(cp_data[cp_data['schedular'] == schedular]):
            cp_data = cp_data[cp_data['schedular'] == schedular]
        else:
            cp_data = cp_data[cp_data['schedular'] == monthly_schedular]
        cp_data.reset_index(drop=True, inplace=True)
        cp_data['date'] = pd.to_datetime(cp_data['date'], errors='coerce')
        cp_data['date'] = cp_data['date'].dt.strftime('%Y-%m-%d')
        cp_data['closeprice'] = cp_data['closeprice'].astype(np.float64)
        cp_data['closeprice'].replace(to_replace=0.0, value=np.nan, inplace=True)
        cp_data['closeprice'].ffill(inplace=True)
        cp_data['closeprice'].bfill(inplace=True)
        actual_return = f'actual_{schedular.lower()}_return'
        expected_return = f'er-predictions'
        cp_data[actual_return] = -100
        cp_data[expected_return] = -100
        cols = ['isin', 'date', 'closeprice', 'volume', 'schedular', actual_return, expected_return]
        record = cp_data.iloc[-1].to_dict()
    except:    
        logging.warning(f'data for this isin: {isin} is not present in both financial-model index and er-model index') 
        record = {'isin': isin, 'schedular': schedular, 'closeprice': 1.0, 'volume': 0.8}
        actual_return = f'actual_{schedular.lower()}_return'
        expected_return = f'er-predictions'
        cols = ['isin', 'date', 'closeprice', 'volume', 'schedular', actual_return, expected_return]
        record['tag'] = geo
        record['updated_at'] = datetime.strftime(datetime.now(), '%Y-%m-%d')
    return record



def get_stock_price(isin, master, start_date='01/01/2004', end_date='03/31/2028' , get_from_api=False):
            # code = masters[masters['isin']==isin]['country_code'].unique()
    tic = master[master['isin']==isin]['tic'].unique()
    exchange = master[master['isin']==isin]['exchange'].unique()
    start_date1 = start_date
    end_date1 = end_date

    stock_url = f'{snp_url}?isin={isin}&startDate={start_date1}&endDate={end_date1}&country_code="USA"'
    data_api = requests.get(stock_url).json()
    if get_from_api:
        try:
            df_api = pd.DataFrame(data_api["data"]["stocks"])[['date', 'close']]
            df_api.sort_values(by='date', inplace=True)
            df_api.rename(columns={'close':'adj_close'}, inplace=True)
            df_api['adj_close'] = df_api['adj_close'].map(float)
        except:
            df_api = pd.DataFrame(columns=['date', 'adj_close'])            
        return df_api
    else:

        data = {    
            "inputRequests": 
            [
                {
                    "function": snp_function,
                    "identifier": f'{tic}:{exchange}',
                    "mnemonic": close_mnemonic,
                    "properties": 
                    {
                        "periodType": period_type,
                        "startDate":start_date,
                        "endDate":end_date,
                        "frequency":frequency
                    }
                }
            ]
        }
        data_json = json.dumps(data)

        url = sp_global_url
        headers = {'Authorization': head_auth,'Content-type': content_type}
        response = requests.post(url, data=data_json, headers=headers)
        try:
            op=json.loads(response.text)
            if 'Rows' not in op['GDSSDKResponse'][0]:    
    #     if ('GDSSDKResponse' not in op) or ('Rows' not in op['GDSSDKResponse'][0]):
                data = {    
                    "inputRequests": 
                    [
                        {
                            "function": snp_function,
                            "identifier": f'{isin}',
                            "mnemonic": close_mnemonic,
                            "properties": 
                            {
                                "periodType": period_type,
                                "startDate":start_date,
                                "endDate":end_date,
                                "frequency":frequency
                            }
                        }
                    ]
                }

                data_json = json.dumps(data)

                url = sp_global_url
                headers = {'Authorization': head_auth,'Content-type': content_type}
                response = requests.post(url, data=data_json, headers=headers)
                op=json.loads(response.text)

            df_snp = pd.DataFrame([row['Row'] for row in op['GDSSDKResponse'][0]['Rows']], columns=['adj_close', 'date'])
            df_snp['adj_close'] = df_snp['adj_close'].map(float)
            df_snp['date'] = pd.to_datetime(df_snp['date']).dt.strftime('%Y-%m-%d') 
            return df_snp
        except Exception as e:
            print(f"isin: {isin}, tic:{tic}, exchange:{exchange}, start:{start_date}, end:{end_date}, exception:{e}")
            return pd.DataFrame(columns=['adj_close', 'date'])
        
def rectify(geo_isins, schedular,):
    defaults = []

    date = datetime.strftime(datetime.now(), f'%Y-%m-%d')
    year = int(date.split('-')[0])

    for isin in geo_isins:
        try:
            data = get_es_data(isin, [year-1, year], eq_ttm_model)
            data = data[data['schedular'] == schedular]
            data.reset_index(drop=True, inplace=True)
            record = data.iloc[-1].to_dict()
            if isinstance(record['closeprice'], List):
                record['closeprice'] = record['closeprice'][0]
            defaults.append(record)
        except:
            continue
    
    defaults = pd.DataFrame(defaults)
    defaults['updated_at'] = date
    return defaults