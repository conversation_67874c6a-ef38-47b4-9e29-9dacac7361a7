"""
TTM Model Scripts - <PERSON><PERSON> Script

Refactored TTM trigger script with improved error handling,
logging, and standardized utilities usage.
"""

import sys
import os
import json
import traceback
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# Import shared utilities package
from shared_utils import (
    load_config, create_model_logger, create_error_handler,
    ErrorSeverity, ModelError, ConfigurationError, ErrorContext
)

# Import existing modules
from get_predictions import *
from get_metrics import *
from monitor_metrics import *


class TTMModelRunner:
    """TTM Model execution orchestrator with improved error handling."""

    def __init__(self, geo: str, scheduler: str):
        """
        Initialize TTM Model Runner.

        Args:
            geo: Geographic region
            scheduler: Scheduler type
        """
        self.geo = geo
        self.scheduler = scheduler

        # Setup script directory
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.parameters_file = os.path.join(self.script_dir, "daily-run-parameters.json")

        # Setup logging
        self.logger = create_model_logger(
            model_name='ttm_model',
            tag=geo,
            scheduler=scheduler,
            run_date=datetime.now().strftime('%Y-%m-%d'),
            log_dir=os.path.join(self.script_dir, 'logs')
        )

        # Setup error handler
        self.error_handler = create_error_handler(self.logger.get_logger())

        self.logger.log_model_start({
            'geo': geo,
            'scheduler': scheduler
        })

    def load_daily_run_parameters(self) -> Dict[str, Any]:
        """Load daily run parameters from JSON file."""
        try:
            with open(self.parameters_file, "r") as json_file:
                return json.load(json_file)
        except Exception as e:
            self.error_handler.handle_error(
                ConfigurationError(f"Failed to load daily run parameters: {e}", ErrorSeverity.HIGH),
                context={"file_path": self.parameters_file}
            )
            return {}

    def save_daily_run_parameters(self, parameters: Dict[str, Any]) -> bool:
        """Save daily run parameters to JSON file."""
        try:
            with open(self.parameters_file, 'w') as json_file:
                json.dump(parameters, json_file, indent=4)
            return True
        except Exception as e:
            self.error_handler.handle_error(
                ModelError(f"Failed to save daily run parameters: {e}", ErrorSeverity.MEDIUM),
                context={"file_path": self.parameters_file}
            )
            return False

    def reset_daily_run_parameters(self) -> bool:
        """Reset daily run parameters for the current geo/scheduler."""
        try:
            parameters = self.load_daily_run_parameters()

            key = f'{self.geo}_{self.scheduler.lower()}'
            if key in parameters:
                # Reset prediction parameters
                if 'daily-predictions' in parameters[key]:
                    parameters[key]['daily-predictions'].update({
                        'total_isins': 0,
                        'successful_runs': 0,
                        'failed_runs': 0,
                        'failed_isins': []
                    })

                # Reset metrics parameters
                if 'daily-metrics' in parameters[key]:
                    parameters[key]['daily-metrics'].update({
                        'total_isins': 0,
                        'successful_runs': 0,
                        'failed_runs': 0
                    })

                return self.save_daily_run_parameters(parameters)

            return True

        except Exception as e:
            self.error_handler.handle_error(
                ModelError(f"Failed to reset daily run parameters: {e}", ErrorSeverity.MEDIUM),
                context={"geo": self.geo, "scheduler": self.scheduler}
            )
            return False

    def execute_predictions(self) -> bool:
        """Execute daily predictions with error handling."""
        try:
            self.logger.info("Starting daily predictions")

            # Send trigger mail
            with ErrorContext(self.error_handler, "trigger_mail"):
                trigger_mail(self.geo, self.scheduler)

            # Execute predictions
            with ErrorContext(self.error_handler, "daily_predictions"):
                daily_predictions(self.geo, self.scheduler)

            self.logger.info("Daily predictions completed successfully")
            return True

        except Exception as e:
            error_body = traceback.format_exc()
            self.logger.error(f"Daily predictions failed: {e}")

            # Send error mail
            try:
                error_mail(self.geo, self.scheduler, error_body, mode='predictions')
            except Exception as mail_error:
                self.logger.error(f"Failed to send error mail: {mail_error}")

            return False

    def execute_metrics(self) -> bool:
        """Execute daily metrics with error handling."""
        try:
            self.logger.info("Starting daily metrics")

            with ErrorContext(self.error_handler, "daily_metrics"):
                daily_metrics(self.geo, self.scheduler)

            self.logger.info("Daily metrics completed successfully")
            return True

        except Exception as e:
            error_body = traceback.format_exc()
            self.logger.error(f"Daily metrics failed: {e}")

            # Send error mail
            try:
                error_mail(self.geo, self.scheduler, error_body, mode='metrics')
            except Exception as mail_error:
                self.logger.error(f"Failed to send error mail: {mail_error}")

            return False

    def execute_monitoring(self) -> bool:
        """Execute monitoring with error handling."""
        try:
            self.logger.info("Starting monitoring")

            with ErrorContext(self.error_handler, "monitor"):
                monitor(self.geo, self.scheduler)

            self.logger.info("Monitoring completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Monitoring failed: {e}")
            return False

    def send_completion_notification(self) -> bool:
        """Send completion notification."""
        try:
            parameters = self.load_daily_run_parameters()

            with ErrorContext(self.error_handler, "geo_schedular_mail"):
                geo_schedular_mail(
                    daily_run_params=parameters,
                    geo=self.geo,
                    schedular=self.scheduler.lower()
                )

            return True

        except Exception as e:
            self.logger.error(f"Failed to send completion notification: {e}")
            return False

    def run(self) -> bool:
        """
        Execute the complete TTM model pipeline.

        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(f"Starting TTM model run for {self.geo} {self.scheduler}")

            # Execute predictions
            predictions_success = self.execute_predictions()

            # Execute metrics
            metrics_success = self.execute_metrics()

            # Send completion notification
            self.send_completion_notification()

            # Reset parameters for next run
            self.reset_daily_run_parameters()

            # Execute monitoring
            monitoring_success = self.execute_monitoring()

            # Overall success
            overall_success = predictions_success and metrics_success and monitoring_success

            # Log completion
            self.logger.log_model_end(overall_success, {
                'predictions_success': predictions_success,
                'metrics_success': metrics_success,
                'monitoring_success': monitoring_success
            })

            return overall_success

        except Exception as e:
            self.error_handler.handle_error(
                ModelError(f"TTM model run failed: {e}", ErrorSeverity.CRITICAL),
                context={"geo": self.geo, "scheduler": self.scheduler}
            )
            return False


def parse_arguments() -> tuple:
    """Parse command line arguments."""
    if len(sys.argv) <= 2:
        raise ValueError('Invalid arguments. Usage: python trigger.py <geo> <scheduler>')

    return sys.argv[1], sys.argv[2]


def main():
    """Main execution function."""
    try:
        # Parse arguments
        geo, scheduler = parse_arguments()

        # Initialize and run TTM model
        runner = TTMModelRunner(geo, scheduler)
        success = runner.run()

        return 0 if success else 1

    except Exception as e:
        print(f"Critical error in TTM model execution: {e}")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)