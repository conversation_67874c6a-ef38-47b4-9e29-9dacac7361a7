from tsfm_public.toolkit.dataset import *

class BaseForecastDFDataset(BaseDFDataset):
    """
    X_{t+1,..., t+p} = f(X_{:t})
    """

    def __init__(
        self,
        data_df: pd.DataFrame,
        group_id: Optional[Union[List[int], List[str]]] = None,
        context_length: int = 1,
        prediction_length: int = 1,
        drop_cols: list = [],
        id_columns: List[str] = [],
        timestamp_column: Optional[str] = None,
        target_columns: List[str] = [],
        observable_columns: List[str] = [],
        control_columns: List[str] = [],
        conditional_columns: List[str] = [],
        static_categorical_columns: List[str] = [],
        frequency_token: Optional[int] = None,
        autoregressive_modeling: bool = True,
        stride: int = 1,
        fill_value: Union[float, int] = 0.0,
        fill_method: str='None',
        level: int= 0,
    ):
        self.frequency_token = frequency_token
        self.target_columns = target_columns
        self.observable_columns = observable_columns
        self.control_columns = control_columns
        self.conditional_columns = conditional_columns
        self.static_categorical_columns = static_categorical_columns
        self.autoregressive_modeling = autoregressive_modeling
        self.fill_method=fill_method
        self.level=level

        id_cols=['er-predictions']
        x_cols = join_list_without_repeat(
            target_columns,
            observable_columns,
            control_columns,
            conditional_columns,
        )
        y_cols = join_list_without_repeat(
            target_columns,
            observable_columns,
            control_columns,
            conditional_columns,
        )

        # check non-autoregressive case
        if len(target_columns) == len(x_cols) and not self.autoregressive_modeling:
            raise ValueError(
                "Non-autoregressive modeling was chosen, but there are no input columns for prediction."
            )

        # masking for conditional values which are not observed during future period
        self.y_mask_conditional = np.array([(c in conditional_columns) for c in y_cols])

        # create a mask of x which masks targets
        self.x_mask_targets = np.array([(c in target_columns) for c in x_cols])

        super().__init__(
            data_df=data_df,
            id_columns=id_columns,
            timestamp_column=timestamp_column,
            x_cols=x_cols,
            y_cols=y_cols,
            id_cols=id_cols,
            context_length=context_length,
            prediction_length=prediction_length,
            group_id=group_id,
            drop_cols=drop_cols,
            stride=stride,
            fill_value=fill_value,
        )

    def __getitem__(self, index):
        # seq_x: batch_size x seq_len x num_x_cols

        time_id = index * self.stride

        seq_x = self.X[time_id : time_id + self.context_length].values
        seq_id=self.index[time_id: time_id+self.context_length].values
        
        for idx in range(1, 1+self.level):
            idx_columns=[f'er-predictions']
            self.x_idx_columns=np.array([(c in idx_columns) for c in self.id_cols])
            seq_x[-idx, self.x_mask_targets]=seq_id[-idx, self.x_idx_columns]
        
        if not self.autoregressive_modeling:
            seq_x[:, self.x_mask_targets] = 0

        # seq_y: batch_size x pred_len x num_x_cols
        seq_y = self.y[
            time_id + self.context_length : time_id + self.context_length + self.prediction_length
        ].values

        seq_y[:, self.y_mask_conditional] = 0
        
        ret = {
            "past_values": np_to_torch(np.nan_to_num(seq_x, nan=self.fill_value)),
            "future_values": np_to_torch(np.nan_to_num(seq_y, nan=self.fill_value)),
            "past_observed_mask": np_to_torch(~np.isnan(seq_x)),
            "future_observed_mask": np_to_torch(~np.isnan(seq_y)),
        }

        if self.datetime_col:
            ret["timestamp"] = self.timestamps[time_id + self.context_length - 1]

        if self.group_id:
            ret["id"] = self.group_id

        if self.frequency_token is not None:
            ret["freq_token"] = torch.tensor(self.frequency_token, dtype=torch.int)

        if self.static_categorical_columns:
            categorical_values = self.data_df[self.static_categorical_columns].values[0, :]
            ret["static_categorical_values"] = np_to_torch(categorical_values)

        return ret

    def __len__(self):
        return (len(self.X) - self.context_length - self.prediction_length) // self.stride + 1
    
def dataset_class(level):
    class CustomForecastDFDataset(BaseConcatDFDataset):
        """
        A :class: `ForecastDFDataset` used for forecasting.

        Args:
            data_df (DataFrame, required): input data
            datetime_col (str, optional): datetime column in the data_df. Defaults to None
            x_cols (list, optional): list of columns of X. If x_cols is an empty list, all the columns in the data_df is taken, except the datatime_col. Defaults to an empty list.
            group_ids (list, optional): list of group_ids to split the data_df to different groups. If group_ids is defined, it will triggle the groupby method in DataFrame. If empty, entire data frame is treated as one group.
            seq_len (int, required): the sequence length. Defaults to 1
            num_workers (int, optional): the number if workers used for creating a list of dataset from group_ids. Defaults to 1.
            pred_len (int, required): forecasting horizon. Defaults to 0.
        """

        def __init__(
            self,
            data: pd.DataFrame,
            id_columns: List[str] = [],
            timestamp_column: Optional[str] = None,
            target_columns: List[str] = [],
            observable_columns: List[str] = [],
            control_columns: List[str] = [],
            conditional_columns: List[str] = [],
            static_categorical_columns: List[str] = [],
            context_length: int = 1,
            prediction_length: int = 1,
            num_workers: int = 1,
            frequency_token: Optional[int] = None,
            autoregressive_modeling: bool = True,
            stride: int = 1,
            fill_value: Union[float, int] = 0.0,
        ):
            # output_columns_tmp = input_columns if output_columns == [] else output_columns

            super().__init__(
                data_df=data,
                id_columns=id_columns,
                timestamp_column=timestamp_column,
                num_workers=num_workers,
                context_length=context_length,
                prediction_length=prediction_length,
                fill_value=fill_value,
                cls=BaseForecastDFDataset,
                stride=stride,
                # extra_args
                target_columns=target_columns,
                observable_columns=observable_columns,
                control_columns=control_columns,
                conditional_columns=conditional_columns,
                static_categorical_columns=static_categorical_columns,
                frequency_token=frequency_token,
                autoregressive_modeling=autoregressive_modeling,
                level=level,
            )
            self.n_inp = 2
            # for forecasting, the number of targets is the same as number of X variables
            self.n_targets = self.n_vars
    return CustomForecastDFDataset