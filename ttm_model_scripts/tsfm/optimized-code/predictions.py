from data_collection import *
import concurrent.futures
from io import BytesIO
from model_props import *
import timeit
from tsfm_public.models.tinytimemixer import TinyTimeMixerForPrediction
from concurrent.futures.thread import *
import concurrent
from tqdm import tqdm
import traceback
import copy

model_prefix = config.get(section='daily-run', option='data_prefix')

sector_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "sector-map.json")
with open(sector_file_path, "r") as json_file:
    sector_map = json.load(json_file)
    
dict_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ts-dict.json")
with open(dict_file_path, "r") as json_file:
    ts_dict = json.load(json_file)

drun_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "daily-run-parameters.json")

es_env = "prod"
es_helper = es_helpers[es_env]

def read_json(file_path):
    with open(file_path, "r") as json_file:
        data = json.load(json_file)
    return data

def write_json(data, file_path):
    try:
        with open(file_path, 'w') as json_file:
            json.dump(data, json_file, indent=4)
        return True
    except:
        return False

def get_model(isin, geo, schedular):
    
    model = TinyTimeMixerForPrediction.from_pretrained("ibm/TTM", revision=ttm_model_revision, head_dropout=head_dropout_rate)
    
    try:
        
        model_key = model_prefix + f'{geo}/{schedular.lower()}/individual/models/ttm_{isin}.bin'
        forecast_model = s3_helper.read_as_model(model, bucket_key, model_key)
        mode = 'individual'
    
    except:
            try:
                sector = sector_map.get(isin, 'NA')
                model_key = model_prefix + f'{geo}/{schedular.lower()}/shared/models/ttm_{sector}.bin'
                forecast_model = s3_helper.read_as_model(model, bucket_key, model_key)
                mode = 'shared'
            except:
                return
            
    return mode, forecast_model, model_key

def get_predictions(actual_month_return, isin, geo, schedular):
    mode, forecast_model, model_key = get_model(isin, geo, schedular)
    if forecast_model is None:
        return
    try:
        ttm_preds = forecast_model(actual_month_return).prediction_outputs[0, 0, 0].detach().numpy().item()
        return ttm_preds, model_key, mode
    except Exception as e:
        print(traceback.format_exc())
        return

def sec_average_predictions(isin, master, schedular, date_needed):
    record = check_snp(isin, master, date_needed)
    record['volume'] = 0.1
    record['mode'] = 'sector-average'
    record['model-id'] = f'sector-average_{date_needed}'
    return record

def sec_average_struct(geo, schedular):

    current_date = datetime.date.today()
    date_needed = (current_date - BDay(1)).date().strftime('%Y-%m-%d')
    current_date = current_date.strftime('%Y-%m-%d')

    master = get_master(geo)
    master_cp = copy.deepcopy(master)
    sec_average_df = pd.DataFrame(master['isin'].parallel_apply(lambda isin: sec_average_predictions(isin, master_cp, schedular, date_needed)).tolist())
    sec_average_df.rename(columns={'adj_close': 'closeprice'}, inplace=True)
    sec_average_df['updated_at'] = current_date
    sec_average_df['sector'] = sec_average_df['isin'].map(sector_map)
    sec_average_df['tag'] = geo
    sec_average_df.dropna(subset=['closeprice', 'sector'], inplace=True)
    return sec_average_df

def daily_predictions(geo, schedular):

    drun = read_json(drun_path)

    start_t = timeit.default_timer()
    current_date = datetime.date.today()
    date_needed = (current_date - BDay(1)).date().strftime('%Y-%m-%d')
    
    model_inputs = input_data(geo, schedular)
    model_inputs  = model_inputs.reset_index(drop=True)

    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = []
        for name, group in model_inputs.iterrows():
            future = executor.submit(get_predictions, group['model-input'], group['isin'], geo, schedular)
            futures.append(future)
        
        model_preds, model_keys, modes = [], [], []
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures), desc='Processing'):
            result = future.result()
            if result:
                model_pred, model_key, mode = result
            else:
                model_pred, model_key, mode = None, None, None
            model_preds.append(model_pred)
            model_keys.append(model_key)
            modes.append(mode)
    
    actual_return = f'actual_{schedular.lower()}_return'
    expected_return = f'actual_{schedular.lower()}_return_predictions'
    required_columns = ['date', 'isin', 'closeprice', 'volume', 'updated_at', expected_return, actual_return, 'mode', 'model-id', 'sector', 'tag']

    model_inputs[expected_return] = model_preds
    model_inputs[actual_return] = -100
    model_inputs['model-key'] = model_keys
    model_inputs['mode'] = modes

    model_inputs = model_inputs.dropna().reset_index(drop=True)
    model_inputs['model-input'] = np.nan
    model_inputs['time-stamp'] = model_inputs['model-key'].parallel_map(lambda model_key: ts_dict.get(model_key, date_needed))
    model_inputs['model-id'] = model_inputs.parallel_apply(lambda row: row['model-key'].split('/')[-1].split('.')[0] + '_' + row['time-stamp'], axis=1)
    model_inputs['sector'] = model_inputs['isin'].map(sector_map)
    model_inputs['tag'] = geo
    model_inputs = model_inputs[required_columns]
    sec_average_map = model_inputs.groupby("sector")[expected_return].mean().to_dict()

    # sec_average_df = sec_average_struct(geo, schedular)
    # sec_average_df[expected_return] = sec_average_df['sector'].map(sec_average_map)
    # sec_average_df[actual_return] = default_value
    # model_inputs = pd.concat([model_inputs, sec_average_df]).drop_duplicates(subset=['isin'], keep='first')
    
    defaults = rectify(geo, schedular)
    defaults['date'] = date_needed
    default_model_id = default_mode + f'_{date_needed}'

    model_generated_predictions = len(model_inputs)
    model_inputs = pd.concat([model_inputs, defaults[required_columns]]).drop_duplicates(subset=['isin'], keep='first')
    model_inputs['mode'].fillna(default_mode)
    model_inputs['model-id'].fillna(default_model_id)

    forward_filled_predictions = len(model_inputs) - model_generated_predictions
    
    end_t = timeit.default_timer()
    predictions_key = data_prefix + f'{geo}/{schedular.lower()}/predictions.csv'
    s3_helper.write_as_dataframe(model_inputs, bucket_key, predictions_key)
    log_key = data_prefix + f'{geo}/{schedular.lower()}/ttm.log'
    s3_helper.upload_file('ttm.log', bucket_key, log_key)

    master = get_master(geo)
    geo_isins = master['isin'].tolist()
    
    drun[f'{geo}_{schedular.lower()}']['daily-predictions']['total_isins'] = len(geo_isins)
    drun[f'{geo}_{schedular.lower()}']['daily-predictions']['successful_runs'] = len(model_inputs)
    drun[f'{geo}_{schedular.lower()}']['daily-predictions']['failed_runs'] = len(geo_isins) - len(model_inputs)
    drun[f'{geo}_{schedular.lower()}']['daily-predictions']['predictions == 0'] = len(model_inputs[model_inputs[expected_return] == 0])
    drun[f'{geo}_{schedular.lower()}']['daily-predictions']['forward-filled-isins'] = forward_filled_predictions
    drun[f'{geo}_{schedular.lower()}']['daily-predictions']['time(s)'] = end_t - start_t
    drun['date'] = date_needed
    drun_bool = write_json(drun, drun_path)

    print(f'parallel processing took {end_t - start_t} seconds for getting predictions for {geo} and {schedular}')
    return model_inputs