import warnings
warnings.filterwarnings("ignore")

from pandarallel import pandarallel
pandarallel.initialize(progress_bar=True, verbose=0)

from utils import *
import requests
import datetime
from model_props import *
from pandas.tseries.offsets import BDay

url_prefix = config.get(section='url', option='url_prefix')

monthly_schedular = config.get(section='schedulars', option='monthly_schedular')
quarterly_schedular = config.get(section='schedulars', option='quarterly_schedular')
daily_schedular = config.get(section='schedulars', option='daily_schedular')
weekly_schedular = config.get(section='schedulars', option='weekly_schedular')

eq_cat_er_model = config.get(section='es-indexes', option='cat_er_model')
bucket_key = config.get(section='bucket', option='bucket_name')
data_prefix = config.get(section='daily-run', option='data_prefix')

context_length = int(config.get(section='model-hyperparameters', option='context_length'))
index = eq_cat_er_model
start_year = int(config.get(section='data-verification', option='start_year'))
threshold = float(config.get(section='data-verification', option='threshold'))

es_schedular_dict = {
    monthly_schedular: monthly_schedular,
    quarterly_schedular: monthly_schedular,
    daily_schedular: daily_schedular,
}

schedular_periods = {
    monthly_schedular: 22,
    weekly_schedular: 5,
    quarterly_schedular: 66,
    daily_schedular: 2,
}

es_schedular_periods = {
    monthly_schedular: 22,
    weekly_schedular: 4,
    quarterly_schedular: 66,
    daily_schedular: 1
}

monthly_period = 22

es_env = "prod"
es_helper = es_helpers[es_env]

def get_data(isin, geo, schedular):
    current_date = datetime.date.today()
    date_needed = (current_date - BDay(1)).date().strftime('%Y-%m-%d')
    current_date = current_date.strftime('%Y-%m-%d')
    current_year = int(current_date.split('-')[0])
    
    try:
        data_key = data_prefix + f'{geo}/{schedular.lower()}/daily-run/{isin}.npy'
        actual_input = s3_helper.read_as_array(bucket_key, data_key)

        data = es_helper.get_es_data(isin, [start_year, current_year], eq_cat_er_model)
        
        es_schedular = es_schedular_dict.get(schedular, monthly_schedular)
        es_period = es_schedular_periods.get(es_schedular, monthly_period)
        period = schedular_periods.get(schedular, monthly_period)
        expected_return = f'actual_{es_schedular.lower()}_return_predictions'
        
        data = data[data['schedular']  == es_schedular]
        data.reset_index(drop=True, inplace=True)
        data.rename(columns={expected_return: 'er-predictions'}, inplace=True)
        
        expected_return = f'actual_{schedular.lower()}_return_predictions'
        actual_return = f'actual_{schedular.lower()}_return'
        data[actual_return] = data['closeprice'].pct_change(periods=period) * 100
        data[actual_return] = data[actual_return].shift(-period)

        if schedular == quarterly_schedular:
            data['er-predictions-1'] = data['er-predictions'].shift(-es_period)
            data['er-predictions-2'] = data['er-predictions-1'].shift(-es_period)
            data['er-predictions'] = 100*((1 + data['er-predictions']/100) * (1 + data['er-predictions-1']/100) * (1 + data['er-predictions-2']/100) - 1)
            
        elif schedular == daily_schedular:
            data['er-predictions-1'] = data['er-predictions'].shfit(-es_period)
            data['er-predictions'] = 100((1 + data['er-predictions']/100) * (1 + data['er-predictions-1']/100))
            
        elif schedular == monthly_schedular:
            pass
        else:
            return
        
        cls = ['date', 'isin', actual_return, 'er-predictions']
        data = data[cls].iloc[-context_length:]
        data = data.reset_index(drop=True)
        data['er-predictions'] = data['er-predictions'].ffill()
        data[actual_return] = data[actual_return].fillna(data['er-predictions'])
        model_input = np.array(data[actual_return].tolist())
        
        s3_helper.write_as_array(model_input, bucket_key, data_key)
        if np.abs(actual_input - model_input).max() > threshold:
            return True
        else:
            return False
    except:
        return False
    
def daily_verification(geo, schedular):
    firms_url =  f"{url_prefix}{geo}"
    geo_json = requests.get(f'{firms_url}').json()
    geo_companies_df = pd.DataFrame(geo_json['data'][f'masteractivefirms_{geo}'])
    geo_companies_df = geo_companies_df[['isin']]

    geo_companies_df['filter'] = geo_companies_df['isin'].parallel_apply(lambda isin: get_data(isin, geo, schedular))
    model_errors = geo_companies_df[geo_companies_df['filter']][['isin']].reset_index(drop=True)

    model_errors_key = data_prefix + f'{geo}/{schedular.lower()}/model-err.csv'
    s3_helper.write_as_dataframe(model_errors, bucket_key, model_errors_key)