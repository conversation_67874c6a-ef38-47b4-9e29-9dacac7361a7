import sys
import traceback
from predictions import *
from metrics import *

drun_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "daily-run-parameters.json")

if __name__ == '__main__':
    print('arguments', sys.argv)

    if len(sys.argv) <= 2:
        raise ValueError(f'invalid arguments for geo and schedular')
    
    geo, schedular = sys.argv[1], sys.argv[2]
    trigger_mail(geo, schedular)
    try:
        daily_predictions(geo, schedular)
    except:
        error_body = traceback.format_exc()
        error_mail(geo, schedular, error_body, mode='predictions')

    try:
        daily_metrics(geo, schedular)
    except:
        error_body = traceback.format_exc()
        error_mail(geo, schedular, error_body, mode='metrics')

drun = read_json(drun_path)  
geo_schedular_mail(daily_run_params=drun, geo=geo, schedular=schedular.lower())

drun[f'{geo}_{schedular.lower()}']['daily-predictions']['total_isins'] = 0
drun[f'{geo}_{schedular.lower()}']['daily-predictions']['successful_runs'] = 0
drun[f'{geo}_{schedular.lower()}']['daily-predictions']['failed_runs'] = 0
drun[f'{geo}_{schedular.lower()}']['daily-predictions']['predictions == 0'] = 0
drun[f'{geo}_{schedular.lower()}']['daily-predictions']['time(s)'] = 0
drun[f'{geo}_{schedular.lower()}']['daily-metrics']['total_isins'] = 0
drun[f'{geo}_{schedular.lower()}']['daily-metrics']['successful_runs'] = 0
drun[f'{geo}_{schedular.lower()}']['daily-metrics']['failed_runs'] = 0
write_json(drun, drun_path)
