from helpers import *
from utils import *
from tqdm import tqdm
import json
from pandas.tseries.offsets import BDay
from datetime import datetime
import pandas as pd
from pandarallel import pandarallel
import traceback
import timeit

logging.basicConfig(level=logging.ERROR)
warnings.filterwarnings("ignore")
pandarallel.initialize(progress_bar=True, verbose=0)

# config for metrics-computations.
default_value =int(config.get(section='default-values', option='default_value'))
eq_ttm_model = config.get(section='es-indexes', option='ttm_model')
eq_ttm_model_metrics = config.get(section='es-indexes', option='ttm_model_metrics')
rolling_period = int(config.get(section='periods', option='rolling_period'))

# geographies supported currently.
aieq = config.get(section='geos', option='aieq_geo')
indeq = config.get(section='geos', option='indeq_geo')

# s3-prefixes.
bucket_key = config.get(section='bucket', option='bucket_name')
data_prefix = config.get(section='daily-run', option='data_prefix')
url_prefix = config.get(section='url', option='url_prefix')

drun_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "daily-run-parameters.json")

es_env = "prod"
es_helper = es_helpers[es_env]

def read_json(file_path):
    with open(file_path, "r") as json_file:
        data = json.load(json_file)
    return data

def write_json(data, file_path):
    try:
        with open(file_path, 'w') as json_file:
            json.dump(data, json_file, indent=4)
        return True
    except:
        return False
        
def get_metrics(isin, schedular):
    
    date = datetime.strftime(datetime.now(), '%Y-%m-%d')
    current_year = int(date.split('-')[0])
    index = eq_ttm_model
    group = es_helper.get_es_data(isin, [current_year - 1, current_year], index)
    group = group[group['schedular'] == schedular]
    metrics_period = schedular_dict.get(schedular, 0)
    group.reset_index(drop=True, inplace=True)
    if len(group) <= rolling_period:
        raise ValueError(f"not enough predictions to compute the metrics for isin -> {isin} and schedular -> {schedular}")
    
    actual_column = f"actual_{schedular.lower()}_return"
    actual_column = f"actual_{schedular.lower()}_return"
    prediction_column = f"actual_{schedular.lower()}_return_predictions"
    group[actual_column] = group["closeprice"].pct_change(periods=metrics_period) * 100
    group[prediction_column] = group[prediction_column].shift(metrics_period)
    group[actual_column].fillna(default_value, inplace=True)
    group[prediction_column].fillna(default_value, inplace=True)
    group = calculate_metrics(group, isin, period=rolling_period, prediction_column=prediction_column, actual_column=actual_column)
    if 'lgbm-er-predictions' in group.columns.tolist():
        group.drop(columns=['lgbm-er-predictions'], inplace=True)    
    group['date'] = group['date'].dt.strftime('%Y-%m-%d')
    record = group.iloc[-1].to_dict()
    return record

def process_isin(isin, schedular):
    try:
        return get_metrics(isin, schedular)
    except:
        return None

def daily_metrics(geo, schedular):

    drun = read_json(drun_path)
    start_t = timeit.default_timer()
    
    if not(geo in [aieq, indeq]):
        raise ValueError(f'invalid geography passed for daily-predictions, geo should be one of [{aieq}, {indeq}], but {geo} passed') 
    import requests  # type: ignore
    firms_url =  f'{url_prefix}{geo}'
    geo_json = requests.get(f'{firms_url}').json()
    geo_companies = pd.DataFrame(geo_json['data'][f'masteractivefirms_{geo}'])
    geo_companies = geo_companies[['isin']]
    geo_isins = geo_companies['isin'].tolist()
    geo_companies = pd.DataFrame({'isin': geo_isins[:100]})

    geo_companies['data'] = geo_companies['isin'].parallel_apply(lambda isin: process_isin(isin, schedular))
    geo_companies = geo_companies.dropna().reset_index(drop=True)
    results = geo_companies['data'].tolist()

    log_key = data_prefix + f'{geo}/{schedular.lower()}/ttm.log'
    s3_helper.upload_file('ttm.log', bucket_key, log_key)

    import datetime
    current_date = datetime.date.today()
    date_needed = (current_date-BDay(1)).date().strftime("%Y-%m-%d")

    results = pd.DataFrame(results)
    results.reset_index(drop=True, inplace=True)
    results['updated_at'] = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
    results = results[results['date'] == date_needed]
    results.reset_index(drop=True, inplace=True)

    drun[f'{geo}_{schedular.lower()}']['daily-metrics']['total_isins'] = len(geo_isins)
    drun[f'{geo}_{schedular.lower()}']['daily-metrics']['successful_runs'] = len(results)
    drun[f'{geo}_{schedular.lower()}']['daily-metrics']['failed_runs'] = len(geo_isins) - len(results)

    write_json(drun, drun_path)
    if len(results) == 0:
        return

    metrics_key = data_prefix + f'{geo}/{schedular.lower()}/metrics.csv'
    results = results.dropna().reset_index(drop=True)
    s3_helper.write_as_dataframe(results, bucket_key, metrics_key)

    return results