from utils import *
from model_props import *
from typing import Optional, List
import numpy as np
import torch
import requests
import datetime
import copy
from pandas.tseries.offsets import BDay

# setting up the initial-configuration.
import logging
import warnings
from pandarallel import pandarallel

logging.basicConfig(level=logging.ERROR)
warnings.filterwarnings("ignore")
pandarallel.initialize(progress_bar=True, verbose=0)


snp_url = config.get(section='url', option='snp_url')
url_prefix = config.get(section='url', option='url_prefix')

monthly_schedular = config.get(section='schedulars', option='monthly_schedular')
quarterly_schedular = config.get(section='schedulars', option='quarterly_schedular')
daily_schedular = config.get(section='schedulars', option='daily_schedular')
weekly_schedular = config.get(section='schedulars', option='weekly_schedular')


eq_cat_er_model = config.get(section='es-indexes', option='cat_er_model')
eq_financial_model = config.get(section='es-indexes', option='finance_model')
eq_ttm_model = config.get(section='es-indexes', option='ttm_model')
eq_er_model = config.get(section='es-indexes', option='er_model')
bucket_key = config.get(section='bucket', option='bucket_name')
data_prefix = config.get(section='daily-run', option='data_prefix')

context_length = int(config.get(section='model-hyperparameters', option='context_length'))
index = eq_er_model

snp_function = config.get(section='snp-credentials', option='snp_function')
close_mnemonic = config.get(section='snp-credentials', option='close_mnemonic')
period_type=config.get(section='snp-credentials', option='period_type')
frequency=config.get(section='snp-credentials', option='frequency')
head_auth=config.get(section='snp-credentials', option='head_auth')
content_type=config.get(section='snp-credentials', option='content_type')

sp_global_url=config.get(section='url', option='sp_global_url')

es_schedular_dict = {
    monthly_schedular: monthly_schedular,
    quarterly_schedular: monthly_schedular,
    daily_schedular: daily_schedular,
}

schedular_periods = {
    monthly_schedular: 22,
    weekly_schedular: 5,
    quarterly_schedular: 66,
    daily_schedular: 2,
}

es_schedular_periods = {
    monthly_schedular: 22,
    weekly_schedular: 4,
    quarterly_schedular: 66,
    daily_schedular: 1
}

monthly_period = 22

default_qdata = pd.DataFrame(columns=['isin', 'date'])
default_qdata.set_index('isin')

es_env = "prod"
es_helper = es_helpers[es_env]

def get_input_data(isin: str,
                                geo: str,
                                schedular: str,
                                current_date: str,
                                date_needed: str,
                                dates: List[str],
                                qdata=default_qdata):
    try:
        period = schedular_periods.get(schedular, monthly_period)

        data_key = data_prefix + f'{geo}/{schedular.lower()}/daily-run/{isin}.npy'
        prev_input = s3_helper.read_as_array(bucket_key, data_key)
        prev_date = qdata.loc[isin]['date']

        sdate = dates[0]
        scloseprice = get_values_from_es(isin, sdate, eq_financial_model, monthly_schedular)['closeprice'].item()
        record = get_values_from_es(isin, date_needed, eq_financial_model, monthly_schedular)[
            ['date', 'isin', 'closeprice', 'volume']
        ].reset_index(drop=True).iloc[0].to_dict()
        ecloseprice = record['closeprice']
        actual_returns = 100 * (ecloseprice - scloseprice) / scloseprice

        es_schedular = es_schedular_dict.get(schedular, monthly_schedular)
        es_period = es_schedular_periods.get(es_schedular, 0)
        if schedular == quarterly_schedular:
            expected_return = f'actual_{es_schedular.lower()}_return_predictions'
            cols = ['date', 'isin', expected_return]

            er_date_needed_1 = dates[period - 1]
            er_data_1 = get_values_from_es(isin, er_date_needed_1, index, es_schedular)[
                cols].reset_index(drop=True).iloc[0].to_dict()[expected_return]
            
            er_date_needed_2 = dates[period - es_period - 1]
            er_data_2 = get_values_from_es(isin, er_date_needed_2, index, es_schedular)[
                cols].reset_index(drop=True).iloc[0].to_dict()[expected_return]
            
            er_date_needed_3 = dates[period - 2 * es_period - 1]
            er_data_3 = get_values_from_es(isin, er_date_needed_3, index, es_schedular)[
                cols].reset_index(drop=True).iloc[0].to_dict()[expected_return]
            
            er_data = 100 * ((1 + er_data_1/100) * (1 + er_data_2/100) * (1 + er_data_3/100) - 1)
        
        elif schedular == monthly_schedular:
            expected_return = f'actual_{schedular.lower()}_return_predictions'
            cols = ['date', 'isin', expected_return]

            er_date_needed = dates[period - 1]
            er_data = get_values_from_es(isin, er_date_needed, index, es_schedular)[
                cols].reset_index(drop=True).iloc[0].to_dict()[expected_return]
        
        elif schedular == daily_schedular:
            expected_return = f'actual_{schedular.lower()}_return_predictions'
            cols = ['date', 'isin', expected_return]

            er_date_needed_1 = dates[period - 1]
            er_data_1 = get_values_from_es(isin, er_date_needed_1, index, es_schedular)[
                cols].reset_index(drop=True).iloc[0].to_dict()[expected_return]
            
            er_date_needed_2 = dates[period - es_period - 1]
            er_data_2 = get_values_from_es(isin, er_date_needed_2, index, es_schedular)[
                cols].reset_index(drop=True).iloc[0].to_dict()[expected_return]
            
            er_data = 100 * ((1 + er_data_1/100) * (1 + er_data_2/100) - 1)

        else:
            return
        
        if prev_date < date_needed:
            if len(prev_input) >= context_length:
                model_input = np.delete(prev_input, 0)
            else:
                model_input = copy.deepcopy(prev_input)

            model_input = np.append(model_input, er_data)
            es_freq = (period // es_period) - 1

            er_data_idx = es_freq * es_period + 1
            model_input[(-er_data_idx):] = er_data
            model_input[-period] = actual_returns
            
            modified = date_needed
            write_arr_to_s3(model_input, bucket_key, data_key)
        else:
            model_input = prev_input
            modified = prev_date
        
        record['updated_at'] = current_date
        record['modified'] = modified

        if len(model_input) < context_length:
            return
        
        model_input = torch.tensor(model_input, dtype=torch.float32)
        model_input = model_input.unsqueeze(-1).unsqueeze(0)
        record['model-input'] = model_input
        return record
    except:
        return
    
def input_data(geo, schedular):
    q_data_key = data_prefix + f'{geo}/{schedular.lower()}/q_data.csv'
    q_data = s32df(bucket_key, q_data_key)
    q_data.set_index('isin', inplace=True)

    geo_companies_df = get_master(geo)
    geo_companies_df = geo_companies_df[['isin']]
    geo_isins = geo_companies_df['isin'].tolist()

    current_date = datetime.date.today()
    date_needed = (current_date - BDay(1)).date().strftime('%Y-%m-%d')
    current_date = current_date.strftime('%Y-%m-%d')
    current_year = int(current_date.split('-')[0])
    idx = 0
    data = pd.DataFrame()
    while data.empty:
        isin = geo_isins[idx]
        data = es_helper.get_es_data(isin, [current_year - 1, current_year], eq_cat_er_model)
        idx += 1
        
    data = data[data['schedular'] == monthly_schedular].reset_index(drop=True)
    data['date'] = data['date'].dt.strftime('%Y-%m-%d')
    dates = data['date'].tolist()
    period = schedular_periods.get(schedular)
    dates = dates[-(period + 1):]

    ttm_data = list(geo_companies_df['isin'].parallel_apply(lambda isin: get_input_data(isin, geo, schedular, current_date, date_needed, dates, q_data)).dropna())
    model_inputs = pd.DataFrame(ttm_data)
    qdata = model_inputs[['isin', 'modified']]
    qdata.rename(columns={'modified': 'date'}, inplace=True)
    qdata = pd.concat([qdata, q_data.reset_index()]).drop_duplicates(subset=['isin'], keep='first').reset_index(drop=True)
    df2s3(qdata, bucket_key, q_data_key)
    return model_inputs

def get_latest_record(isin, schedular, year, index):
    try:
        data = es_helper.get_es_data(isin, [year-1, year], index)
        data = data[data['schedular'] == schedular]
        data.reset_index(drop=True, inplace=True)
        record = data.iloc[-1].to_dict()
        if isinstance(record['closeprice'], List):
            record['closeprice'] = record['closeprice'][0]
        return record
    except:
        return

def rectify(geo, schedular):
    
    geo_companies_df = get_master(geo)
    geo_companies_df = geo_companies_df[['isin']]

    current_date = datetime.date.today()
    date_needed = (current_date - BDay(1)).date().strftime('%Y-%m-%d')
    current_date = current_date.strftime('%Y-%m-%d')
    current_year = int(current_date.split('-')[0])

    defaults = geo_companies_df['isin'].parallel_apply(lambda isin: get_latest_record(isin, schedular, current_year, eq_ttm_model))
    defaults = pd.DataFrame(defaults.dropna().tolist())

    defaults['updated_at'] = current_date
    defaults['mode'] = default_mode
    defaults['model-id'] = default_mode + f'_{date_needed}'
    return defaults

def check_snp(isin, master, date_needed):
    try:
        data = get_stock_price(isin, master, start_date=date_needed, end_date=date_needed)
        if not(data.empty):
            data['isin'] = isin
            return data.iloc[0].to_dict()
        else:
            return {'isin': isin, 'date': date_needed, 'adj_close': None}
    except:
        return {'isin': isin, 'date': date_needed, 'adj_close': None}

def get_stock_price(isin, master, start_date='01/01/2004', end_date='03/31/2028' , get_from_api=False):
            # code = masters[masters['isin']==isin]['country_code'].unique()
    tic = master[master['isin']==isin]['tic'].unique()
    exchange = master[master['isin']==isin]['exchange'].unique()
    start_date1 = start_date
    end_date1 = end_date

    stock_url = f'{snp_url}?isin={isin}&startDate={start_date1}&endDate={end_date1}&country_code="USA"'
    data_api = requests.get(stock_url).json()
    if get_from_api:
        try:
            df_api = pd.DataFrame(data_api["data"]["stocks"])[['date', 'close']]
            df_api.sort_values(by='date', inplace=True)
            df_api.rename(columns={'close':'adj_close'}, inplace=True)
            df_api['adj_close'] = df_api['adj_close'].map(float)
        except:
            df_api = pd.DataFrame(columns=['date', 'adj_close'])            
        return df_api
    else:

        data = {    
            "inputRequests": 
            [
                {
                    "function": snp_function,
                    "identifier": f'{tic}:{exchange}',
                    "mnemonic": close_mnemonic,
                    "properties": 
                    {
                        "periodType": period_type,
                        "startDate":start_date,
                        "endDate":end_date,
                        "frequency":frequency
                    }
                }
            ]
        }
        data_json = json.dumps(data)

        url = sp_global_url
        headers = {'Authorization': head_auth,'Content-type': content_type}
        response = requests.post(url, data=data_json, headers=headers)
        try:
            op=json.loads(response.text)
            if 'Rows' not in op['GDSSDKResponse'][0]:    
    #     if ('GDSSDKResponse' not in op) or ('Rows' not in op['GDSSDKResponse'][0]):
                data = {    
                    "inputRequests": 
                    [
                        {
                            "function": snp_function,
                            "identifier": f'{isin}',
                            "mnemonic": close_mnemonic,
                            "properties": 
                            {
                                "periodType": period_type,
                                "startDate":start_date,
                                "endDate":end_date,
                                "frequency":frequency
                            }
                        }
                    ]
                }

                data_json = json.dumps(data)

                url = sp_global_url
                headers = {'Authorization': head_auth,'Content-type': content_type}
                response = requests.post(url, data=data_json, headers=headers)
                op=json.loads(response.text)

            df_snp = pd.DataFrame([row['Row'] for row in op['GDSSDKResponse'][0]['Rows']], columns=['adj_close', 'date'])
            df_snp['adj_close'] = df_snp['adj_close'].map(float)
            df_snp['date'] = pd.to_datetime(df_snp['date']).dt.strftime('%Y-%m-%d') 
            return df_snp
        except Exception as e:
            return pd.DataFrame(columns=['adj_close', 'date'])

def get_master(geo):

    firms_url =  f"{url_prefix}{geo}"
    geo_json = requests.get(f'{firms_url}').json()
    geo_companies_df = pd.DataFrame(geo_json['data'][f'masteractivefirms_{geo}'])

    return geo_companies_df