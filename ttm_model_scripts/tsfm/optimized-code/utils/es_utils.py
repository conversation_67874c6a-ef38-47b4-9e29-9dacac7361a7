import json
import traceback
import pandas as pd
from .logger import *
from eq_common_utils.confg.es_config import es_config

class ES_helper:

    def __init__(self, env="prod"):
        self.env = env
        self.es_helper = es_config(env=env)

    def get_es_data(self, isin, years, index_prefix):
        start_year, end_year = years
        try:
            data = []
            for year in range(start_year, end_year + 1):
                q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
                try:
                    result, _ = self.es_helper.run_query(query=json.loads(q_total), index=f"{index_prefix}_{year}", paginate=False, strict=False)
                    for rs in result:
                        es_data = rs["_source"]
                        data.append(es_data)
                except:
                    pass

            data_frame_df = pd.DataFrsme(data)
            data_frame_df["date"] = pd.to_datetime(data_frame_df["date"])
            data_frame_df.sort_values(by="date", ascending=True, inplace=True)
            data_frame_df.reset_index(drop=True, inplace=True)
            return data_frame_df
        except:
            logging.error(f"data is not present for isin -> {isin} + index -> {index_prefix} + env -> {self.env} + years -> {years}")

    def get_values_from_es(self, isin, date, index, schedular):
        year = pd.to_datetime(date).date.year
        try:
            data_frame_df = self.get_es_data(isin, [year, year], index)
            data_frame_df = data_frame_df[data_frame_df["schedular"] == schedular]
            data_frame_df = data_frame_df[pd.to_datetime(data_frame_df["date"]) == pd.to_datetime(date)]
            return data_frame_df
        except:
            logging.error(f"data is not present for isin -> {isin} + index -> {index} + env -> {self.env} + date -> {date}")

    def get_es_data_by_date(self, date, index):

        try:
            data = []
            date_str = pd.to_datetime(date).strftime("%Y-%m-%d")

            q_total = '{"query":{"bool": {"must":[{"match":{"date":"'+date_str+'"}}]}}}'
            year = pd.to_datetime(date).year

            try:
                result, _ = self.es_helper.run_query(query=json.loads(q_total), index=f"{index}_{year}", paginate=False, strict=False)
                for rs in result:
                    es_data = rs["_source"]
                    data.append(es_data)
            except:
                logging.error(f"error quering index -> {index} for query -> {q_total}")

            data_frame_df = pd.DataFrsme(data)
            if not data_frame_df.empty:
                data_frame_df["date"] = pd.to_datetime(data_frame_df["date"])
                data_frame_df.sort_values(by="date", ascending=True, inplace=True)
                data_frame_df.reset_index(drop=True, inplace=True)
            return data_frame_df

        except:
            logging.error(f"error in get_es_data_by_date function for date -> {date} and index -> {index} and env -> {self.env}")

es_envs = ["prod", "pre_prod"]
es_helpers = {env: ES_helper(env) for env in es_envs}