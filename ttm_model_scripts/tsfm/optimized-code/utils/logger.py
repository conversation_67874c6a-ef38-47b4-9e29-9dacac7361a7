## asynchronous logging.

import asyncio
from queue import Queue
from functools import partial
import threading

def message_queue(queue: Queue, message: str):
    queue.append(message)

def create_queue(greetings: str = 'process triggered...'):
    
    data_queue = []
    async_add_message = partial(message_queue, queue = data_queue)
    
    def add_message(message: str):
        async_add_message(message = message)
        
        
    return add_message, data_queue

greetings = 'default-logger-triggered....'
default_logger, data_queue = create_queue(greetings=greetings)

## include asynchronous logging in the next release.

import warnings
import logging
import os

log_path = 'ttm.log'
if os.path.exists(log_path):
    os.remove(log_path)

# configure logging
logging.basicConfig(
    filename=log_path,
    filemode="w",
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,  # change to WARNING if you don't want INFO logs
)

# redirect warnings to logging and ignore them.
logging.captureWarnings(True)
warnings.simplefilter('ignore') # supress all the warnings.

# Suppress logs from ALL third-party libraries
for logger_name in logging.root.manager.loggerDict:
    if not logger_name.startswith("__main__"):  # Keep only your script's logs
        logging.getLogger(logger_name).setLevel(logging.CRITICAL)
logging.getLogger("elasticsearch").setLevel(logging.CRITICAL)
logging.getLogger("urllib3").setLevel(logging.CRITICAL)  # Suppresses HTTP logs
logging.getLogger("opensearch").setLevel(logging.CRITICAL)
# example log messages
logger = logging.getLogger(__name__)
logger.info("This is an info log from your script.")
logger.warning("This is a warning log from your script.")
