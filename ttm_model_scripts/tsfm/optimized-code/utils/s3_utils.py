import io
import torch
import traceback
import numpy as np
from eq_common_utils.utils.config.s3_config import s3_config


class S3_helper:

    def __init__(self):

        self.s3_helper = s3_config()

    def read_as_dataframe(self, s3_bucket_key, s3_path_key):
        data_frame_df = self.s3_helper.read_as_dataframe(s3_bucket_key, s3_path_key)
        return data_frame_df

    def write_as_dataframe(self, data_frame_df, s3_bucket_key, s3_path_key):
        done = self.s3_helper.write_advanced_as_df(data_frame_df, s3_bucket_key, s3_path_key)
        return done

    def read_as_array(self, s3_bucket_key, s3_path_key):

        try:
            byte_stream, _ = self.s3_helper.read_as_stream(s3_bucket_key, s3_path_key)
            numpy_array = np.frombuffer(byte_stream)
            return numpy_array
        except:
            pass

    def write_as_array(self, numpy_array, s3_bucket_key, s3_path_key):
        bytes_io = io.BytesIO()
        np.save(bytes_io, numpy_array)
        bytes_io.seek(0)
        done = self.s3_helper.upload_binary_file(bytes_io, s3_bucket_key, s3_path_key)
        return True

    def write_as_model(self, model, s3_bucket_key, s3_path_key):

        with io.BytesIO() as model_buffer:
            torch.save(model.state_dict(), model_buffer)
            model_buffer.seek(0)
            done = self.s3_helper.upload_binary_file(model_buffer, s3_bucket_key, s3_path_key)

        return True

    def read_as_model(self, model, s3_bucket_key, s3_path_key):

        model_buffer = self.s3_helper.read_as_stream(s3_bucket_key, s3_path_key)
        model_buffer.seek(0)

        device = "cuda:0" if torch.cuda.is_available() else "cpu"
        state_dict = torch.load(model_buffer, map_location=torch.device(device))
        model.load_state_dict(state_dict)

        return model

    def upload_file(self, local_key, s3_bucket_key, s3_path_key):
        done = self.s3_helper.upload_file(local_key, s3_bucket_key, s3_path_key)
        return True


s3_helper = S3_helper()