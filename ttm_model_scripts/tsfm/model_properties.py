from utils import *

bucket_name = config.get(section='bucket', option='bucket_name')
model_prefix = config.get(section='model-key', option='model_prefix')
individual_model_suffix = config.get(section='model-key', option='individual_model_suffix')
shared_model_suffix = config.get(section='model-key', option='shared_model_suffix')

# Model Hyperparameters
head_dropout_rate = config.get(section='model-hyperparameters', option='head_dropout_rate')
context_length = config.get(section='model-hyperparameters', option='context_length')
forecast_length = config.get(section='model-hyperparameters', option='forecast_length')
fewshot_fraction = config.get(section='model-hyperparameters', option='fewshot_fraction')
learning_rate = config.get(section='model-hyperparameters', option='learning_rate')
epochs = config.get(section='model-hyperparameters', option='epochs')
scaling = config.get(section='model-hyperparameters', option='scaling')
batch_size = config.get(section='model-hyperparameters', option='batch_size')
backbone_freeze = config.get(section='model-hyperparameters', option='backbone_freeze')
early_stopping_patience = config.get(section='model-hyperparameters', option='early_stopping_patience')
early_stopping_threshold = config.get(section='model-hyperparameters', option='early_stopping_threshold')
ttm_model_revision = config.get(section='model-hyperparameters', option='ttm_model_revision')