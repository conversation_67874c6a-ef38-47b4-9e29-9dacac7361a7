import warnings
warnings.filterwarnings("ignore")

from pandarallel import pandarallel  # type: ignore
pandarallel.initialize(progress_bar=True, verbose=0)

from utils import *
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm  # type: ignore
from datetime import datetime


eq_ttm_model_metrics = config.get(section='es-indexes', option='ttm_model_metrics')
url_prefix = config.get(section='url', option='url_prefix')

monthly_monitor_period = int(config.get(section='periods', option='monthly-monitor-period'))
weekly_monitor_period = int(config.get(section='periods', option='weekly-monitor-period'))

retraining_queue_path = config.get(section='daily-run', option='retraining_queue')
retraining_bucket = config.get(section='bucket', option='bucket_name')

aieq = config.get(section='geos', option='aieq_geo')
indeq = config.get(section='geos', option='indeq_geo')

observed_metrics = [
    'root_mean_squared_error', 
    'mean_directionality', 
    'avg_confidence_score', 
    'accuracy_14_day', 
    'accuracy_22_day', 
    'accuracy_1_day'
]

def get_metrics_data(years, index, geo_companies, schedular):
    def process_isin(isin):
        metrics = get_es_data(isin, years, index)
        if metrics is None:
            return None
        metrics = metrics[metrics['schedular'] == schedular]
        metrics.reset_index(drop=True, inplace=True)
        return metrics

    all_metrics = geo_companies['isin'].parallel_apply(process_isin)
    return all_metrics


def get_worst_models(df, metrics_col, n, check_type):
    def _max_value(m, s, n):
        return m + n * s
    def _min_value(m, s, n):
        return m - n * s
    median = df[metrics_col].median()
    std = df[metrics_col].std()
    if check_type == 'min':
        threshold = _min_value(median, std, n)
    elif check_type == 'max':
        threshold = _max_value(median, std, n)
    summary_df = pd.DataFrame.from_dict({
        'median': [median], 
        'standard deviation': [std], 
        'threshold': [threshold], 
        'metric': [metrics_col]
    })
    data = df[metrics_col]
    
    if check_type == 'min':
        return summary_df, df[df[metrics_col] < threshold]
    elif check_type == 'max':
        return summary_df, df[df[metrics_col] > threshold]
  
def reduce(df, isin, period):
    if df.empty:
        return
    try:
        temp = pd.DataFrame(df[-monthly_monitor_period:][observed_metrics].mean()).T
    except:
        pass

    temp['isin'] = isin
    temp['date'] = df.iloc[-1]['date']
    return temp

def monitor(geo, schedular):
    if not(geo in [aieq, indeq]):
        raise ValueError(f'invalid geography passed for daily-predictions, geo should be one of [{aieq}, {indeq}], but {geo} passed') 
    import requests  # type: ignore
    firms_url =  f'{url_prefix}{geo}'
    geo_json = requests.get(f'{firms_url}').json()
    geo_companies = pd.DataFrame(geo_json['data'][f'masteractivefirms_{geo}'])
    geo_companies = geo_companies[['isin']]

    date = datetime.strftime(datetime.now(), f'%Y-%m-%d')
    current_year = int(date.split('-')[0])
    metrics_years = [current_year - 1, current_year]

    geo_companies['data'] = get_metrics_data(metrics_years, eq_ttm_model_metrics, geo_companies, schedular)
    geo_companies = geo_companies.dropna().reset_index(drop=True)
    
    if geo_companies.empty:
        warnings.warn('no isins are found for the following metrics.')
        return
    
    biweekly_metrics = geo_companies.parallel_apply(lambda row: reduce(row['data'], row['isin'], weekly_monitor_period), axis=1).dropna().reset_index(drop=True).tolist()
    monthly_metrics = geo_companies.parallel_apply(lambda row: reduce(row['data'], row['isin'], monthly_monitor_period), axis=1).dropna().reset_index(drop=True).tolist()
    
    metrics_22d = pd.concat(monthly_metrics)
    metrics_22d = metrics_22d[['date', 'isin'] + observed_metrics].reset_index(drop = True)


    metrics_11d = pd.concat(biweekly_metrics)
    metrics_11d = metrics_11d[['date', 'isin'] + observed_metrics].reset_index(drop = True)

    _, worst_models_rmse_month = get_worst_models(metrics_22d, 'root_mean_squared_error', 2, 'max')
    _, worst_models_dir_month = get_worst_models(metrics_22d, 'mean_directionality', 2, 'min')

    _, worst_models_rmse_week = get_worst_models(metrics_11d, 'root_mean_squared_error', 2, 'max')
    _, worst_models_dir_week = get_worst_models(metrics_11d, 'mean_directionality', 2, 'min')

    common_isins_rmse = set(worst_models_rmse_month['isin'].tolist()) & set(worst_models_rmse_week['isin'].tolist())
    common_isins_dir = set(worst_models_dir_month['isin'].tolist()) & set(worst_models_dir_week['isin'].tolist())
    common_isins = set(common_isins_rmse) | set(common_isins_dir)

    retraining_queue = pd.DataFrame({'isin': list(common_isins)})
    retraining_queue['geo'] = geo
    retraining_queue['schedular'] = schedular
    retraining_queue['submission_date'] = date

    queue = s32df(retraining_bucket, retraining_queue_path)
    queue  = pd.concat([queue, retraining_queue], axis=0)
    queue.reset_index(drop=True, inplace=True)
    queue = queue.drop_duplicates(subset=['isin', 'geo', 'schedular', 'submission_date'], keep='first')

    df2s3(queue, retraining_bucket, retraining_queue_path)
    return queue