from ..utils import *
from .inference_pipe import *
from .metrics_comp_pipe import *
from contextlib import redirect_stdout


script_dir = os.path.dirname(os.path.abspath(__file__))

class DailyRunPipe:
    
    inference_pipe = prediction_pipe
    comp_metrics_pipe = metrics_comp_pipe
    email_worker = email_manager
    
    def __init__(self, env: str="preprod"):
        self.env = env
        self.data_config = globals()[f"{env}_config"]
        self.s3_workers = s3_managers[env]
        self.es_worker = prod_es_manager if self.env == "prod" else preprod_es_manager
        
        DailyRunPipe.inference_pipe = PredictionPipe(env)
        DailyRunPipe.comp_metrics_pipe = MetricsCompPipe(env)
    
    def _post_predictions(self, data_df, isin: str, scheduler: str, date: str):
        s3_worker = self.s3_workers[scheduler.lower()]
        done = s3_worker.write_predictions(data_df, date, isin)
        return done
    
    def _post_metrics(self, data_df, isin: str, scheduler: str, date: str):
        s3_worker = self.s3_workers[scheduler.lower()]
        done = s3_worker.write_metrics(data_df, date, isin)
        return done
    
    def phaseI(self, geo: str, scheduler: str, date: str):
        status = {
            "sucessful_preds": 0,
            "null_preds": 0,
            "zero_preds": 0,
            "individual_preds": 0,
            "sectoravg_preds": 0,
            "sectorbased_preds": 0,
        }
        
        print("status:", status)
        model_predictions_col = f"actual_{scheduler.lower()}_return_predictions"
        try:
            model_predictions_df, traded_isins = DailyRunPipe.inference_pipe.predict(geo, scheduler, date)
            model_predictions_df.apply(
            lambda row: self._post_predictions(
                pd.DataFrame([row]).reset_index(drop=True), row["isin"], scheduler, date
            ),
            axis=1,
            )
            model_predictions_df.replace({np.nan: None}, inplace=True)
            model_predictions_df["date"] = model_predictions_df["date"].ffill().bfill()
            model_predictions_df.to_csv("daily-predictions.csv")
            self.es_worker.save_df_to_es(model_predictions_df, self.data_config.es_store_config.predictions)

            model_predictions_df["temp"] = model_predictions_df["model-identifier"].str.split("_").str[0]
            status["sucessful_preds"] = model_predictions_df[model_predictions_col].notna().sum()
            status["null_preds"] = model_predictions_df[model_predictions_col].isna().sum()
            status["zero_preds"] = (model_predictions_df[model_predictions_col] == 0).sum()

            valid_predictions_df = model_predictions_df[model_predictions_df[model_predictions_col].notna()].reset_index(drop=True)
            status["individual_preds"] = (valid_predictions_df["temp"] == valid_predictions_df["isin"]).sum()
            status["sectoravg_preds"] = (valid_predictions_df["temp"] == "sector").sum()
            status["sectorbased_preds"] = (valid_predictions_df["temp"] == valid_predictions_df["sector"]).sum()

            return status, traded_isins, True
        except Exception as e:
            error_body = (
            f"Unable to fetch predictions for Geography -> {geo} and Scheduler -> {scheduler} "
            f"due to {traceback.format_exc()}"
            )
            self.email_worker.notify_failure(geo, scheduler, error_body)
            return status, 0, False
    
    def phaseII(self, geo: str, scheduler: str, date: str):
        status = {
            "sucessful_metrics": 0,
            "Failed_metrics": 0
        }
        try:
            model_metrics_df = DailyRunPipe.comp_metrics_pipe.compute(geo, scheduler, date)
            model_metrics_df.apply(
                lambda row: self._post_metrics(
                    pd.DataFrame([row]).reset_index(drop=True), 
                    row["isin"], 
                    scheduler, 
                    date
                ), 
                axis=1
            )
            self.es_worker.save_df_to_es(model_metrics_df, self.data_config.es_store_config.metrics)
            
            status["sucessful_metrics"] = len(model_metrics_df)
            return status, True
        except Exception as e:
            error_body = (
                f"Unable to compute metrics for Geography -> {geo} and Scheduler -> {scheduler} "
                f"due to {traceback.format_exc()}"
            )
            self.email_worker.notify_failure(geo, scheduler, error_body)
            return status, False
        
    def forward(self, geo: str, scheduler: str, date: str):
        
        status = {"date": date, "total": len(get_master(geo))}

        self.email_worker.trigger(geo, scheduler, self.env)
        
        phaseI_status, traded_isins, phaseI_success = self.phaseI(geo, scheduler, date)
        phaseII_success = False
        if phaseI_success:
            phaseII_status, phaseII_success = self.phaseII(geo, scheduler, date)
        
        if phaseII_success:
            status = {**status, **phaseI_status, **phaseII_status}
            status["Failed_metrics"] = status["total"] - status["sucessful_metrics"]
            status['no_trade'] = status['total'] - traded_isins
            status['es_records'] = status['sucessful_preds'] + status['null_preds']
            self.email_worker.notify_status(geo, scheduler, status, self.env)
        return phaseII_success
    
    def run(self, geo: str, scheduler: str, date: str):
        
        file_path = os.path.join(script_dir, f"ttm.txt")
        with open(file_path, "w") as f:
            with redirect_stdout(f):
                self.forward(geo, scheduler, date)
                
        s3_worker = self.s3_workers[scheduler.lower()]
        s3_worker.upload_file(file_path, date, prefix=geo)
        
    
dailyrun_pipe = DailyRunPipe()

if __name__ == "__main__":
    data = dailyrun_pipe.run("aieq", "Monthly", "2025-04-11")
    print(data)