from ..metrics import *
from ..pandarallel_setup import *

class MetricsCompPipe:
    
    def __init__(self, env: str = "preprod"):
        self.env = env
        self.config = globals()[f"{env}_config"]
        self.metrics_computer = ModelMetrics(self.config)
    
    def compute(self, geo: str, scheduler: str, date: str):
        master_df = get_master(geo)
        metrics_data = master_df["isin"].parallel_apply(lambda isin: self.metrics_computer._get_metrics(isin, scheduler, date))
        return pd.DataFrame(metrics_data.dropna().tolist())

metrics_comp_pipe = MetricsCompPipe()
if __name__ == "__main__":
    metrics_comp_pipe.compute("aieq", "Monthly", "2025-04-04")