from ..inference import ModelInference
from ..data_extraction import *
from ..utils import *
from ..pandarallel_setup import *
from ..read_yaml import *
import dill
import copy
import traceback
import concurrent
from tqdm import tqdm
from typing import Optional, Callable
from concurrent.futures.thread import *

class PredictionPipe:
    
    def __init__(self, env: str = "preprod"):
        self.env = env
        self.config = globals()[f"{env}_config"]
        self.inference = ModelInference(self.config)
        self.extractor = DataCollector(self.config) # type: ignore
        
    def run_predictions(self, data_df: pd.DataFrame, geo: str, scheduler: str, get_predictions: Callable):
        ttm_predictions = {}
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            for _, row in data_df.iterrows():
                future = executor.submit(get_predictions, row["isin"], geo, scheduler, row["model-inputs"])
                futures.append(future)

            for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures), desc="Processing"):
                isin, result = future.result()
                if result:
                    ttm_predictions[isin] = result
                else:
                    ttm_predictions[isin] = np.nan
                    
        data_df["predictions"] = data_df['isin'].map(lambda isin: ttm_predictions.get(isin, np.nan))
        return data_df
        
    def create_model_id(self, model_key: str):
        model_year = self.config.model_settings.model_year
        [model_type, date, time] = model_key.split("/")[-1].split(".")[0].split("_")[-3:]
        return "_".join([model_type, model_year, date, time])
    
    def predict(self, geo: str, scheduler: str, date: str):
        
        data_df = self.extractor.get_input_data(geo, date, scheduler)
        traded_isins = len(data_df)
        data_df["sector"] = data_df["isin"].map(lambda sector: self.config.resource_config.sector_map.get(sector, "NA"))
        data_df["model_key"] = data_df["isin"].parallel_apply(lambda isin: self.inference.get_individual_model_key(isin, scheduler))
        data_df["individual"]  = data_df["model_key"].parallel_apply(self.inference.check_model)
        typeIII_queries = data_df[data_df["model-inputs"].isna()].reset_index(drop=True)
        
        data_df = data_df[data_df["model-inputs"].notna()].reset_index(drop=True)
        
        typeI_queries = data_df[data_df["individual"]].reset_index(drop=True)
        typeI_queries = self.run_predictions(typeI_queries, geo, scheduler, self.inference.get_solo_predictions)
        
        typeII_queries = data_df[~data_df["individual"]].reset_index(drop=True)
        typeII_queries.drop(columns=["individual"], inplace=True)
        
        if not(typeII_queries.empty):
            typeII_queries["model_key"] = typeII_queries["isin"].parallel_apply(lambda isin: self.inference.get_shared_model_key(isin, scheduler, geo))
            typeII_queries["shared"]  = typeII_queries["model_key"].parallel_apply(self.inference.check_model)
            typeII_queries = typeII_queries[typeII_queries["shared"]].reset_index(drop=True)
            typeII_queries = self.run_predictions(typeII_queries, geo, scheduler, self.inference.get_shared_predictions)
        else:
            typeII_queries["model_key"] = pd.Series(dtype='str')
            typeII_queries['shared'] = pd.Series(dtype='bool')
            typeII_queries['predictions'] = pd.Series(dtype='float')
        
        type_I_II_queries = pd.concat([typeI_queries.drop(columns=["individual"]), typeII_queries.drop(columns=["shared"])])
        #TODO: modify the model-identifier definition to be independent of the model_key
        if not type_I_II_queries.empty:
            type_I_II_queries["model-identifier"] = type_I_II_queries["model_key"].parallel_apply(self.create_model_id)
        else:
            type_I_II_queries["model-identifier"] = pd.Series(dtype=object)
        sector_average = type_I_II_queries.groupby("sector").agg({"predictions": "mean"}).reset_index()
        sector_average = dict(zip(sector_average["sector"].tolist(), sector_average["predictions"].tolist()))
        
        
        typeIII_queries.drop(columns=["individual"], inplace=True)
        typeIII_queries["model_key"] = None
        typeIII_queries["model-identifier"] = f"sector_average"
        typeIII_queries["predictions"] = typeIII_queries["sector"].map(lambda sector: sector_average.get(sector, np.nan))
        
        queries = pd.concat([type_I_II_queries, typeIII_queries]).reset_index(drop=True)
        #TODO: specify the structure of your output dataframe in the config.yaml file.
        drop_columns = ["model-inputs", "model_key"]
        queries = queries.drop(columns=drop_columns)
        
        # Add metadata columns
        queries["tag"] = geo
        queries["schedular"] = scheduler
        queries["updated_at"] = datetime.now().strftime("%Y-%m-%d")

        # Rename predictions column to a more descriptive name
        prediction_column = f"actual_{scheduler.lower()}_return_predictions"
        queries.rename(columns={"predictions": prediction_column}, inplace=True)

        return queries, traded_isins
    
prediction_pipe = PredictionPipe(env="preprod")
if __name__ == '__main__':
    data = prediction_pipe.predict("aieq", "Monthly", "2025-04-04")
    print(data)