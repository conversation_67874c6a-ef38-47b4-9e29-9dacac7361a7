from ..data_correction import *

class DataValidatorPipe:
    
    def __init__(self, env: str = "prod"):
        self.env = env
        self.config = globals()[f"{env}_config"]
        self.data_validator = DataValidator(self.config)
    
    def validate(self, geo: str, date: str):
        self.data_validator.validate_data(geo, date)

data_validator_pipe = DataValidatorPipe()
if __name__ == "__main__":
    data_validator_pipe.validate("aieq", "2025-04-04")