Message Id: 1965c911a18d150f
status: {'sucessful_preds': 0, 'null_preds': 0, 'zero_preds': 0, 'individual_preds': 0, 'sectoravg_preds': 0, 'sectorbased_preds': 0}
using model-key -> ttm_model/monthly/data_dump/2024_Q4/BMG0585R1060/ttm_model_BMG0585R1060_2025-01-13_04:54:04.bin for computing solo-predictions for isin -> BMG0585R1060
using model-key -> ttm_model/monthly/data_dump/2024_Q4/BMG0692U1099/ttm_model_BMG0692U1099_2025-01-19_15:55:44.bin for computing solo-predictions for isin -> BMG0692U1099
using model-key -> ttm_model/monthly/data_dump/2024_Q4/BMG0750C1082/ttm_model_BMG0750C1082_2025-01-19_15:59:20.bin for computing solo-predictions for isin -> BMG0750C1082
using model-key -> ttm_model/monthly/data_dump/2024_Q4/BMG0450A1053/ttm_model_BMG0450A1053_2025-01-19_15:53:18.bin for computing solo-predictions for isin -> BMG0450A1053
reading the model: eq-training-dump
reading the model: eq-training-dump
reading the model: eq-training-dump
using model-key -> ttm_model/monthly/data_dump/2024_Q4/AU0000185993/ttm_model_AU0000185993_2025-01-13_04:54:03.bin for computing solo-predictions for isin -> AU0000185993
using model-key -> ttm_model/monthly/data_dump/2024_Q4/AN8068571086/ttm_model_AN8068571086_2025-01-19_15:48:51.bin for computing solo-predictions for isin -> AN8068571086
using model-key -> ttm_model/monthly/data_dump/2024_Q4/BMG162521014/ttm_model_BMG162521014_2025-01-13_04:54:05.bin for computing solo-predictions for isin -> BMG162521014
reading the model: eq-training-dump
reading the model: eq-training-dump
reading the model: eq-training-dump
using model-key -> ttm_model/monthly/data_dump/2024_Q4/BMG0772R2087/ttm_model_BMG0772R2087_2025-01-13_04:54:05.bin for computing solo-predictions for isin -> BMG0772R2087
reading the model: eq-training-dump
reading the model: eq-training-dump
reading the model: eq-training-dump
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BMG0585R1060
Name: isin, dtype: object
columns ->  closeprice
0    80.79
Name: closeprice, dtype: float64
columns ->  volume
0    0.336706
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Financials
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0    0.564582
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    BMG0585R1060_2024_Q4_2025-01-13_04:54:04
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BMG0692U1099
Name: isin, dtype: object
columns ->  closeprice
0    92.9
Name: closeprice, dtype: float64
columns ->  volume
0    0.501019
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Financials
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0    2.332923
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    BMG0692U1099_2024_Q4_2025-01-19_15:55:44
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BMG0750C1082
Name: isin, dtype: object
columns ->  closeprice
0    30.67
Name: closeprice, dtype: float64
columns ->  volume
0    2.138515
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Materials
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0    0.52427
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    BMG0750C1082_2024_Q4_2025-01-19_15:59:20
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BMG0450A1053
Name: isin, dtype: object
columns ->  closeprice
0    92.13
Name: closeprice, dtype: float64
columns ->  volume
0    1.928391
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Financials
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0    5.1128
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    BMG0450A1053_2024_Q4_2025-01-19_15:53:18
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    AU0000185993
Name: isin, dtype: object
columns ->  closeprice
0    5.63
Name: closeprice, dtype: float64
columns ->  volume
0    11.933636
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Information Technology
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0   NaN
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    AU0000185993_2024_Q4_2025-01-13_04:54:03
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    AN8068571086
Name: isin, dtype: object
columns ->  closeprice
0    33.96
Name: closeprice, dtype: float64
columns ->  volume
0    20.884416
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Energy
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0   -0.857681
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    AN8068571086_2024_Q4_2025-01-19_15:48:51
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BMG162521014
Name: isin, dtype: object
columns ->  closeprice
0    28.26
Name: closeprice, dtype: float64
columns ->  volume
0    0.570259
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Utilities
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0    4.291053
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    BMG162521014_2024_Q4_2025-01-13_04:54:05
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BMG0772R2087
Name: isin, dtype: object
columns ->  closeprice
0    35.67
Name: closeprice, dtype: float64
columns ->  volume
0    0.180944
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    Financials
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0   -2.635562
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    BMG0772R2087_2024_Q4_2025-01-13_04:54:05
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BE6360403164
Name: isin, dtype: object
columns ->  closeprice
0    11.72
Name: closeprice, dtype: float64
columns ->  volume
0   NaN
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    NA
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0   NaN
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    NA_2024_Q4_2025-01-16_17:07:28
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
isin                                  object
closeprice                           float64
volume                               float64
date                                  object
sector                                object
actual_monthly_return_predictions    float64
model-identifier                      object
tag                                   object
schedular                             object
updated_at                            object
dtype: object
columns ->  isin
0    BMG1466R1732
Name: isin, dtype: object
columns ->  closeprice
0    2.01
Name: closeprice, dtype: float64
columns ->  volume
0    6.155824
Name: volume, dtype: float64
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  sector
0    NA
Name: sector, dtype: object
columns ->  actual_monthly_return_predictions
0   NaN
Name: actual_monthly_return_predictions, dtype: float64
columns ->  model-identifier
0    sector_average
Name: model-identifier, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  updated_at
0    2025-04-22
Name: updated_at, dtype: object
save records start
Not enough predictions to compute metrics for ISIN BE6360403164 and scheduler Monthly.
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    AN8068571086
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    33.96
Name: closeprice, dtype: float64
columns ->  volume
0    20.884416
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0   -6.795524
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0   -0.15206
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    9.3774
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    91.27521
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    9.553806
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -14374.747303
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -14374.747303
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -0.995586
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    0.995586
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.998013
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.998013
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0   -0.483826
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    71.428571
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    59.090909
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    51.807657
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    66.115277
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    86.592723
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    AU0000185993
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    5.63
Name: closeprice, dtype: float64
columns ->  volume
0    11.933636
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0    13.114618
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0   -0.200284
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    12.592846
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    208.357496
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    14.434594
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -16201.078564
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -16201.078564
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -1.033233
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    1.033233
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.999885
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.999885
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0    0.372027
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    73.333333
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    22.727273
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    30.296706
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    44.711323
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    0.2
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    BMG0450A1053
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    92.13
Name: closeprice, dtype: float64
columns ->  volume
0    1.928391
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0    6.314483
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0    0.033311
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    5.912458
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    35.416112
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    5.951144
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -21366.384564
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -21366.384564
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -0.994462
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    0.994462
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.996584
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.996584
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0    0.492525
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    71.428571
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    86.363636
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    79.467253
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    79.799322
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    85.747173
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    BMG0585R1060
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    80.79
Name: closeprice, dtype: float64
columns ->  volume
0    0.336706
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0    5.097946
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0   -0.038214
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    5.042244
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    32.146844
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    5.669819
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -27101.003464
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -27101.003464
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -1.008233
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    1.008233
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.999823
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.999823
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0   -0.17347
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    66.666667
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    18.181818
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    45.732008
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    36.481722
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    0.2
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    BMG0692U1099
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    92.9
Name: closeprice, dtype: float64
columns ->  volume
0    0.501019
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0    4.675167
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0    0.010991
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    1.949458
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    4.579816
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    2.14005
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -2521.809665
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -2521.809665
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -0.984863
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    0.984863
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.998231
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.998231
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0   -0.234192
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    53.333333
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    86.363636
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    80.458968
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    80.763011
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    85.59978
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    BMG0750C1082
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    30.67
Name: closeprice, dtype: float64
columns ->  volume
0    2.138515
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0    7.730309
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0   -0.094746
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    5.095043
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    36.53352
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    6.044296
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -24487.500979
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -24487.500979
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -1.018283
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    1.018283
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.999877
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.999877
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0   -0.346705
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    87.5
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    9.090909
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    0.2
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    31.99846
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    0.2
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    BMG0772R2087
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    35.67
Name: closeprice, dtype: float64
columns ->  volume
0    0.180944
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0   -3.732377
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0   -0.047784
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    4.408214
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    31.456636
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    5.608622
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -26637.448261
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -26637.448261
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -0.994996
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    0.994996
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.999914
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.999914
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0   -0.260966
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    68.421053
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    54.545455
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    61.023767
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    63.896443
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    86.122851
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    BMG1466R1732
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    2.01
Name: closeprice, dtype: float64
columns ->  volume
0    6.155824
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0    18.815218
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0   -0.141026
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    19.349819
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    411.807014
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    20.293029
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -80036.202418
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -80036.202418
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -1.010824
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    1.010824
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.999864
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.999864
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0    0.398559
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    63.636364
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    0.0
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    0.2
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    0.2
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    0.2
Name: accuracy_1_day, dtype: float64
isin                                  object
date                                  object
closeprice                           float64
volume                               float64
schedular                             object
tag                                   object
updated_at                            object
actual_monthly_return_predictions    float64
actual_monthly_return                float64
mean_absolute_error                  float64
mean_squared_error                   float64
root_mean_squared_error              float64
r2_score                             float64
adjusted_r2_score                    float64
total_perc_diff                      float64
abs_total_diff                       float64
total_variance_perc_diff             float64
abs_total_variance_perc_diff         float64
correlation_score                    float64
directionality_score                 float64
mean_directionality                  float64
confidence_score                     float64
avg_confidence_score                 float64
accuracy_14_day                      float64
accuracy_22_day                      float64
accuracy_1_day                       float64
dtype: object
columns ->  isin
0    BMG162521014
Name: isin, dtype: object
columns ->  date
0    2025-04-11
Name: date, dtype: object
columns ->  closeprice
0    28.26
Name: closeprice, dtype: float64
columns ->  volume
0    0.570259
Name: volume, dtype: float64
columns ->  schedular
0    Monthly
Name: schedular, dtype: object
columns ->  tag
0    aieq
Name: tag, dtype: object
columns ->  updated_at
0    2025-04-12
Name: updated_at, dtype: object
columns ->  actual_monthly_return_predictions
0    4.468773
Name: actual_monthly_return_predictions, dtype: float64
columns ->  actual_monthly_return
0   -0.004579
Name: actual_monthly_return, dtype: float64
columns ->  mean_absolute_error
0    2.351181
Name: mean_absolute_error, dtype: float64
columns ->  mean_squared_error
0    8.604553
Name: mean_squared_error, dtype: float64
columns ->  root_mean_squared_error
0    2.933352
Name: root_mean_squared_error, dtype: float64
columns ->  r2_score
0   -3064.325568
Name: r2_score, dtype: float64
columns ->  adjusted_r2_score
0   -3064.325568
Name: adjusted_r2_score, dtype: float64
columns ->  total_perc_diff
0   -0.879773
Name: total_perc_diff, dtype: float64
columns ->  abs_total_diff
0    0.879773
Name: abs_total_diff, dtype: float64
columns ->  total_variance_perc_diff
0   -0.999663
Name: total_variance_perc_diff, dtype: float64
columns ->  abs_total_variance_perc_diff
0    0.999663
Name: abs_total_variance_perc_diff, dtype: float64
columns ->  correlation_score
0    0.412935
Name: correlation_score, dtype: float64
columns ->  directionality_score
0    82.352941
Name: directionality_score, dtype: float64
columns ->  mean_directionality
0    86.363636
Name: mean_directionality, dtype: float64
columns ->  confidence_score
0    0.01
Name: confidence_score, dtype: float64
columns ->  avg_confidence_score
0    0.01
Name: avg_confidence_score, dtype: float64
columns ->  accuracy_14_day
0    77.560511
Name: accuracy_14_day, dtype: float64
columns ->  accuracy_22_day
0    81.78654
Name: accuracy_22_day, dtype: float64
columns ->  accuracy_1_day
0    0.2
Name: accuracy_1_day, dtype: float64
save records start
Message Id: 1965c9144621e51d
