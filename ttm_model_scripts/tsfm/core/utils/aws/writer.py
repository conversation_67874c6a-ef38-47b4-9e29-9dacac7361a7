import io
import torch
import numpy as np
from io import Bytes<PERSON>
from eq_common_utils.utils.config.s3_config import s3_config

class S3_Writer(object):

    def __init__(self):
        self.s3_helper = s3_config()

    def write_as_dataframe(self, data_frame_df, s3_bucket_key, s3_path_key):
        done = self.s3_helper.write_advanced_as_df(data_frame_df, s3_bucket_key, s3_path_key)
        return done

    def write_as_array(self, numpy_array, s3_bucket_key, s3_path_key):
        bytes_io: BytesIO = io.BytesIO()
        np.save(bytes_io, numpy_array)
        bytes_io.seek(0)
        done = self.s3_helper.upload_binary_file(bytes_io, s3_bucket_key, s3_path_key)
        return done

    def write_as_model(self, model, s3_bucket_key, s3_path_key):

        with io.BytesIO() as model_buffer:
            torch.save(model.state_dict(), model_buffer)
            model_buffer.seek(0)
            done = self.s3_helper.upload_binary_file(model_buffer, s3_bucket_key, s3_path_key)

        return done

    def upload_file(self, local_key, s3_bucket_key, s3_path_key):
        done = self.s3_helper.upload_file(local_key, s3_bucket_key, s3_path_key)
        return done

s3_writer = S3_Writer()