import io
import torch
import numpy as np
from eq_common_utils.utils.config.s3_config import s3_config

class S3_Reader(object):

    def __init__(self):
        self.s3_helper = s3_config()

    def read_as_dataframe(self, s3_bucket_key, s3_path_key):
        data_frame_df = self.s3_helper.read_as_dataframe(s3_bucket_key, s3_path_key)
        return data_frame_df

    def read_as_array(self, s3_bucket_key, s3_path_key):
        byte_stream, _ = self.s3_helper.read_as_stream(s3_bucket_key, s3_path_key)
        numpy_array = np.frombuffer(byte_stream)
        return numpy_array

    def read_as_model(self, model, s3_bucket_key, s3_path_key):
        model_buffer, _ = self.s3_helper.read_as_stream(s3_bucket_key, s3_path_key)
        model_buffer = io.BytesIO(model_buffer)
        model_buffer.seek(0)

        device = "cuda:0" if torch.cuda.is_available() else "cpu"
        state_dict = torch.load(model_buffer, map_location=torch.device(device))
        model.load_state_dict(state_dict)
        return model
    
    def check_file(self, s3_bucket_key, s3_path_key):
        try:
            self.s3_helper.read_as_stream(s3_bucket_key, s3_path_key)
            return True
        except:
            return False


s3_reader = S3_Reader()
