import os
import json
import requests
import pandas as pd
from ..read_yaml import config

def get_master(geo: str):
    """
    Fetches the master data for a given geographical location.

    Args:
        geo (str): The geographical location for which to fetch the master data.

    Returns:
        dataframe: The master data for the specified geographical location.
    """
    
    master_url = config["urls"]["masters_url"]
    if geo == "indeq":
        geo = "indt1"
    url = f"{master_url}getmasterbytag?tag={geo}"
    response = requests.get(url)
    
    if response.status_code == 200:
        response = response.json()
        master_df = pd.DataFrame(response["data"][f"masteractivefirms_{geo}"])
        if geo == 'aigo':
            complement_df = get_master('aieq')
            master_df = master_df[~master_df['isin'].isin(complement_df['isin'].tolist())].reset_index(drop=True)
        return master_df
    else:
        print(f"Failed to fetch master data for {geo}: {response.status_code}")
        
def prepare_scheduler_periods():
    global scheduler_periods
    scheduler_periods = config["scheduler_periods"]
    
def prepare_old_scheduler_periods():
    global old_scheduler_periods
    old_scheduler_periods = config["old_scheduler_periods"]
    
# Define scheduler_periods as a global variable
scheduler_periods = config["scheduler_periods"]
old_scheduler_periods = config["old_scheduler_periods"]
