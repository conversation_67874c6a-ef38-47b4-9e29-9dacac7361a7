import abc
import pandas as pd
from typing import List

class Generic_es_Manager(abc.ABC):
    
    def __init__(self, env: str):
        pass
    
    @abc.abstractmethod
    def get_es_data(self, isin: str, years: List[int], prefix: str):
        pass
    
    @abc.abstractmethod
    def get_values_from_es(self, isin: str, date: str, index: str, schedular: str):
        pass
    
    @abc.abstractmethod
    def get_es_data_by_date(self, date: str, index: str, format: str):
        pass
    
    @abc.abstractmethod
    def save_df_to_es(self, data: pd.DataFrame, index: str):
        pass