import os
import base64
import yaml
import traceback
from ...read_yaml import config
from typing import List, Optional
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText
from email.message import EmailMessage

SCOPES = ["https://www.googleapis.com/auth/gmail.send"]

class EmailClient:
    def __init__(self, creds_path: str, yaml_path: str):
        """
        Initialize the EmailClient with credentials and configuration paths.

        :param creds_path: Path to the credentials file.
        :param yaml_path: Path to the YAML file containing email configurations.
        """
        self.creds_path = creds_path
        self.yaml_path = yaml_path
        self.ebook = self._load_yaml()
    
    def _load_yaml(self) -> dict:
        """
        Load the YAML file containing email configurations.
        """
        try:
            with open(self.yaml_path, "r") as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"YAML file not found at {self.yaml_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing the YAML file: {e}")
    
    def _get_credentials(self) -> Optional[Credentials]:
        """Retrieve credentials from the specified path."""
        if os.path.exists(self.creds_path):
            return Credentials.from_authorized_user_file(self.creds_path, SCOPES)
        print("Credentials not found...")
        return None
    
    def send_message(
        self,
        to: List[str],
        subject: str,
        message_txt: str,
        attachments: Optional[List[str]] = None,
    ) -> Optional[str]:
        """
        Send an email message.

        :param to: List of recipient email addresses.
        :param subject: Subject of the email.
        :param message_txt: Body of the email.
        :param attachments: List of file paths to attach to the email.
        """
        creds = self._get_credentials()
        if creds is None:
            print("Credentials not found, unable to send an email")
            return None
        
        try:
            service = build("gmail", "v1", credentials=creds)
            message = EmailMessage()
            
            # TODO: analyze the differences between MIMEText and just email_message
            # body = MIMEText(message_txt, "html") (used message_txt directly to get the line-breaks)
            message.set_content(message_txt, subtype="html")
            
            if attachments:
                for attachment in attachments:
                    try:
                        with open(attachment, "rb") as f:
                            file_data = f.read()
                            maintype, subtype = "application", "octet-stream"
                            if "." in attachment:
                                maintype, subtype = "application", attachment.split(".")[-1]
                            message.add_attachment(file_data, maintype=maintype, subtype=subtype, filename=os.path.basename(attachment))
                    except FileNotFoundError:
                        print(f"Attachment file not found: {attachment}")
            
            message["To"] = ", ".join(to)
            message["Subject"] = subject
            
            encoded_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            create_message = {"raw": encoded_message}
            
            # pylint: disable=E1101
            send_message = service.users().messages().send(userId="me", body=create_message).execute()
            print(f"Message Id: {send_message['id']}")
            return send_message['id']
        
        except HttpError as error:
            print(f"An error occurred while sending the amil: {error}")
            traceback.print_exc()
            return None
        
script_dir = os.path.dirname(os.path.abspath(__file__))
yaml_path = os.path.join(script_dir, "address.yaml")
creds_path = os.path.join(script_dir, config["email"]["credentials"])
email_client = EmailClient(creds_path, yaml_path)