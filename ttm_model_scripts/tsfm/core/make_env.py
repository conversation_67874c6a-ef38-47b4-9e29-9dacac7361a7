import subprocess
import os
import packaging.version
import platform

common_req_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'requirements_diff.txt')
def get_req_file_based_on_python_version():
    python_version = platform.python_version()
    print(python_version)
    if packaging.version.parse(python_version)<packaging.version.parse('3.10'):
        requirements_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'requirements_3.9.txt')
    elif packaging.version.parse(python_version)>=packaging.version.parse('3.10'):
        requirements_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'requirements_3.10.txt')
    return requirements_file


def install_dependencies():

    # reading all the dependencies of the users
    with open(common_req_path, 'r') as additional_file:
        additional_requirements = additional_file.read().splitlines()

    #combining both the dependencies
    all_requirements = additional_requirements
    print(all_requirements)

    for requirement in all_requirements:
        try:
            subprocess.check_call(['pip', 'install', requirement])
        except subprocess.CalledProcessError as e:
            print(f"Failed to install {requirement}: {e}")
            try:
                requirement_name = requirement.split("==")[0]
                subprocess.check_call(['pip', 'install', requirement_name])
            except subprocess.CalledProcessError as e:
                print(f"Failed to install {requirement_name}: {e}")
    return True

install_dependencies()