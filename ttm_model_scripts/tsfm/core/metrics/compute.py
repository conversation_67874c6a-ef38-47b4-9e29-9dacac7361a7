from ..utils import *
from ..utils.common import get_master
from ..pandarallel_setup import *
import traceback
from typing import Optional
from numpy_ext import rolling_apply as rolling_apply_ext
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error


def sanitize_closeprice(val):
    if isinstance(val, (float, int)):
        return val
    elif isinstance(val, list) and val:
        return val[0]
    else:
        return np.nan
    

class ModelMetrics:
    """
    A class to compute various model metrics for financial data analysis.
    """

    es_manager = prod_es_manager
    def __init__(self, data_config: Optional[GeneralConfig] = prod_config):
        """
        Initialize the ModelMetrics class with external dependencies.

        Args:
            data_config: Configuration object containing data and metrics settings.
        """
        self.data_config = data_config
        self.eps = 1e-5


    def accuracy_function(self, df_series, coeff=500):
        """
        Accuracy conversion metric to convert daily APE into accuracy for ER.
        """
        return df_series.apply(
            lambda x: 97 / (1 + 20 * np.exp((-coeff / (x + self.eps)))) if x < 100 else max(0, 106.7 - 0.213 * x)
        )

    def calculate_accuracy(self, df_data, prediction_col="predictions", target_col="target", coeff=500):
        """
        Calculate accuracy metrics for predictions and targets.
        """
        if prediction_col not in df_data.columns or target_col not in df_data.columns:
            raise ValueError("Prediction or target column not found in DataFrame.")

        df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]
        df_data['denominator'] = df_data[[target_col, prediction_col]].apply(
            lambda x: max(abs(x[target_col]), abs(x[prediction_col])) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]),
            axis=1
        )
        df_data['daily_ape'] = (abs((df_data[target_col] - df_data[prediction_col]) / df_data['denominator']) * 100).apply(lambda x: min(x, 500))
        df_data['accuracy_1_day'] = self.accuracy_function(df_data['daily_ape'], coeff=coeff)
        df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods=1).mean()) ** 0.5
        df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods=1).mean()) ** 0.5
        df_data.drop(columns=['denominator'], inplace=True)
        return df_data

    def directionality_score_calc(self, prediction_direction, close_direction):
        """
        Calculate the directionality score based on prediction and actual directions.
        """
        directionality_df = pd.DataFrame({
            'prediction_direction': prediction_direction,
            'close_direction': close_direction
        })
        correct_direction_df = directionality_df[
            directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)
        ]
        incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)]
        relaxation_count = len(incorrect_direction_df[
            (abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)
        ])
        if len(directionality_df) == relaxation_count:
            return np.nan
        return (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100

    def compute_metrics(self, df, isin, period, prediction_column="predictions", actual_column="ground_truth", metrics_to_calculate=None, n_features=0):
        """
        Compute various metrics for the given DataFrame.
        """
        if metrics_to_calculate is None:
            metrics_to_calculate = self.data_config.metrics_config.all_metrics

        if len(df) < period:
            print(f"Dataframe size for {isin} too small to calculate metrics.")
            return

        req_columns = self.data_config.metrics_config.req_columns
        if prediction_column not in req_columns:
            req_columns.append(prediction_column)
        if actual_column not in req_columns:
            req_columns.append(actual_column)

        df = df.rename(columns={prediction_column: 'predictions', actual_column: 'actual_returns'})
        metrics_columns = req_columns + metrics_to_calculate

        # Compute metrics
        if 'total_perc_diff' in metrics_columns or 'abs_total_diff' in metrics_columns:
            df['total_perc_diff'] = rolling_apply_ext(
                lambda target, prediction: (target.mean() - prediction.mean()) / prediction.mean(),
                period, df['actual_returns'].values, df['predictions'].values
            )
        if 'abs_total_diff' in metrics_columns:
            df['abs_total_diff'] = abs(df['total_perc_diff'])
        if 'total_variance_perc_diff' in metrics_columns or 'abs_total_variance_perc_diff' in metrics_columns:
            df['total_variance_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.var() - prediction.var()) / prediction.var(), period, df['actual_returns'].values, df['predictions'].values)
        if 'abs_total_variance_perc_diff' in metrics_columns:
            df['abs_total_variance_perc_diff'] = abs(df['total_variance_perc_diff'])
        if 'mean_absolute_error' in metrics_columns:
            df['mean_absolute_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda x: abs(x).mean())
        if 'mean_squared_error' in metrics_columns or 'root_mean_squared_error' in metrics_columns:
            df['mean_squared_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda x: (x ** 2).mean())
        if 'root_mean_squared_error' in metrics_columns:
            df['root_mean_squared_error'] = df['mean_squared_error'] ** 0.5
        if 'r2_score' in metrics_columns or 'adjusted_r2_score' in metrics_columns:
            df['r2_score'] = rolling_apply_ext(r2_score, period, df['actual_returns'].values, df['predictions'].values)
        if 'adjusted_r2_score' in metrics_columns:
            df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features - 1))
        if 'mean_directionality' in metrics_columns:
            df['mean_directionality'] = (df['actual_returns'] * df['predictions']).rolling(period).apply(lambda df_series: 100 * ((df_series) > 0).sum() / period)
        if 'correlation_score' in metrics_columns:
            df['correlation_score'] = rolling_apply_ext(lambda target, prediction: np.corrcoef(target, prediction)[0][1], period, df['actual_returns'].values, df['predictions'].values)
        if 'accuracy_14_day' in metrics_columns or 'accuracy_22_day' in metrics_columns or 'accuracy_1_day' in metrics_columns:
            df = self.calculate_accuracy(df, 'predictions', 'actual_returns')
        if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
            min_confidence = 0.01

            max_values =  df['actual_returns'].rolling(period * 24, min_periods = period).max()
            min_values =  df['actual_returns'].rolling(period * 24, min_periods = period).min()
            filt1 = [df.loc[i, 'predictions'] >= max_values.loc[i] for i in range(len(df))]
            filt2 = [df.loc[i, 'predictions'] <= min_values.loc[i] for i in range(len(df))]
            filt3 = [df.loc[i, 'actual_returns'] >= max_values.loc[i] for i in range(len(df))]
            filt4 = [df.loc[i, 'actual_returns'] <= min_values.loc[i] for i in range(len(df))]

            df['confidence_score'] = [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
            max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_returns"])/(max_values.loc[i] - df.loc[i, "predictions"]) if df.loc[i, "actual_returns"] > df.loc[i, "predictions"] else (df.loc[i, "actual_returns"] - min_values.loc[i]) / (df.loc[i, "predictions"] - min_values.loc[i])) for i in range(len(df))]

        # Average confidence score
        if 'avg_confidence_score' in metrics_columns:
            df['avg_confidence_score'] = df['confidence_score'].rolling(period // 2).mean()

        if 'directionality_score' in metrics_columns:
            directionality_df = pd.DataFrame()
            directionality_df["prediction_direction"] = (df["predictions"] - df['predictions'].shift(1)) / df['predictions'].shift(1)
            directionality_df["close_direction"] = (df["actual_returns"] - df['actual_returns'].shift(1)) / df['actual_returns'].shift(1)
        df['directionality_score'] = rolling_apply_ext(self.directionality_score_calc, period, directionality_df['prediction_direction'], directionality_df['close_direction'])
        df['isin'] = isin
        df = df.rename(columns={'predictions': prediction_column, 'actual_returns': actual_column})
        return df[metrics_columns].reset_index(drop=True)

    def _get_metrics(self, isin, scheduler, date):
        """
        Retrieve and compute metrics for a given ISIN, scheduler, and date.
        """
        year = int(date.split('-')[0])
        model_index = self.data_config.data_sources.model_predictions
        rolling_period = self.data_config.metrics_config.rolling_period
        scheduler_period = scheduler_periods.get(scheduler.lower(), 1)

        predictions_df = ModelMetrics.es_manager.get_es_data(isin, [year - 1, year], model_index)
        if predictions_df is None:
            return None
        predictions_df = predictions_df[predictions_df["schedular"] == scheduler].reset_index(drop=True)
        predictions_df["date"] = pd.to_datetime(predictions_df["date"]).dt.strftime("%Y-%m-%d")
        predictions_df = predictions_df[predictions_df["date"] <= date]

        if len(predictions_df) <= rolling_period:
            print(f"Not enough predictions to compute metrics for ISIN {isin} and scheduler {scheduler}.")
            return

        actual_column = f"actual_{scheduler.lower()}_return"
        prediction_column = f"actual_{scheduler.lower()}_return_predictions"
        predictions_df["closeprice"] = predictions_df["closeprice"].apply(sanitize_closeprice)
        predictions_df[actual_column] = predictions_df["closeprice"].pct_change(periods=scheduler_period, fill_method="pad") * 100
        predictions_df[prediction_column] = predictions_df[prediction_column].shift(scheduler_period)
        predictions_df = predictions_df.ffill()
        predictions_df = predictions_df[~(predictions_df[prediction_column].isna() | predictions_df[actual_column].isna())].reset_index(drop=True)

        metrics_df = self.compute_metrics(predictions_df, isin, rolling_period, prediction_column=prediction_column, actual_column=actual_column)
        if metrics_df is not None:
            metrics_df["date"] = pd.to_datetime(metrics_df["date"]).dt.strftime("%Y-%m-%d")
            return metrics_df.iloc[-1].to_dict()
        else:
            return
    
metrics_computer = ModelMetrics()
if __name__ == "__main__":
    record = metrics_computer.get_metrics("AN8068571086", "Monthly", "2025-04-04")
    print(record)
        
        
        

        
        