from ..utils import *
from ..utils.common import get_master
import traceback
from tsfm_public.models.tinytimemixer import TinyTimeMixerForPrediction
from typing import Optional

#TODO: change the class to pick the model-year automatically.
class ModelInference:
    """
    Class to handle model inference and predictions.
    """
    s3_manager = s3_managers["prod"]["monthly"]
    def __init__(self, data_config: Optional[GeneralConfig] = preprod_config):
        """
        Initialize the ModelInference class with a data configuration.
        Args:
            data_config (Optional[GeneralConfig]): Configuration for data sources.
        """
        self.data_config = data_config
        ModelInference.s3_manager.s3_config = data_config
        
    def get_model_prefix(self, scheduler: str):
        """
        Retrieve the appropriate model key for a specific ISIN, geo, and scheduler.
        Args:
            isin (str): The ISIN code for the model key.
            geo (str): The geographical region for the model key.
            scheduler (str): The scheduler for the model key.
        Returns:
            str: The generated model key.
        """
        
        model_year = self.data_config.model_settings.model_year
        model_prefix = self.data_config.s3_model_prefix
        model_prefix = model_prefix.replace("model_year", model_year).replace("schedular", scheduler.lower())
        return model_prefix
    
    def get_individual_model_key(self, isin: str, scheduler: str):
        
        isin_schedular_hash = f'{isin}_{scheduler.lower()}'
        data_config = self.data_config
        ts_dict = data_config.resource_config.ts_dict
        model_prefix = self.get_model_prefix(scheduler)
        model_key = model_prefix + f"{isin}/ttm_model_{isin}_{ts_dict.get(isin_schedular_hash, '0000-00-00_00:00:00')}.bin"
        return model_key
    
    def get_shared_model_key(self, isin: str, scheduler: str, geo: str, sector: Optional[str] = None):
        if sector is None:
            sector = self.data_config.resource_config.sector_map.get(isin, "NA")
        sector = f"{geo}_{sector}"
        sector_scheduler_hash = f"{sector}_{scheduler.lower()}"
        data_config = self.data_config
        ts_dict = data_config.resource_config.ts_dict
        model_prefix = self.get_model_prefix(scheduler)
        model_key = model_prefix + f"{isin}/ttm_model_{sector}_{ts_dict.get(sector_scheduler_hash, '0000-00-00_00:00:00')}.bin"
        return model_key            
        
    def get_base_model(self):
        data_config = self.data_config
        ttm_model_revision, head_dropout_rate = data_config.model_settings.ttm_model_revision, data_config.model_settings.head_dropout_rate
        ttm_model = TinyTimeMixerForPrediction.from_pretrained("ibm/TTM", 
                                                                                                                revision=ttm_model_revision, 
                                                                                                                head_dropout=head_dropout_rate)
        return ttm_model
    
    def load_model(self, base_model, model_key):
        try:
            ttm_model = self.s3_manager.read_model(base_model, model_key)
            return ttm_model
        except:
            print(f"error loading in the model -> {model_key}")
            traceback.print_exc()
            return None
        
    def check_model(self, s3_model_key):
        return self.s3_manager.check_file(self.data_config.s3_model_bucket_key, s3_model_key)
        
    @staticmethod
    def infer_model(isin, model, inputs):
        if (model is None) or (inputs is None):
            return None
        try:
            model_preds = model(inputs).prediction_outputs[0, 0, 0].detach().numpy().item()
            return model_preds
        except:
            print("model predictions failed for isin: {isin} due to ->")
            traceback.print_exc()
            return None
    
    def get_solo_predictions(self, isin: str, geo: str, scheduler: str, inputs):
        model_key = self.get_individual_model_key(isin, scheduler)
        print(f"using model-key -> {model_key} for computing solo-predictions for isin -> {isin}")
        ttm_model = self.load_model(self.get_base_model(), model_key)
        model_preds = self.infer_model(isin, ttm_model, inputs)
        return isin, model_preds
    
    def get_shared_predictions(self, isin: str, geo: str, scheduler: str, inputs):
        model_key = self.get_shared_model_key(isin, scheduler, geo)
        print(f"using model-key -> {model_key} for computing shared-model-predictions for isin -> {isin}")
        ttm_model = self.load_model(self.get_base_model(), model_key)
        model_preds = self.infer_model(isin, ttm_model, inputs)
        return isin, model_preds
    
model_inference = ModelInference()
if __name__ == '__main__':
    model = model_inference.get_model_prefix("Monthly")
    model_key = model_inference.get_individual_model_key("US0378331005", "Monthly")
    model = model_inference.load_model(model_inference.get_base_model(), model_key)
    print(model)