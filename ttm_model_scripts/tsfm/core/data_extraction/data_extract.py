from ..utils import *
from typing import Optional
from .sources.es_source import ESData
from .sources.s3_source import S3Data
from .sources.snp_source import SnpData
from core.utils.common import get_master
from ..pandarallel_setup import *
import pandas as pd

class DataCollector:
    def __init__(self, data_config: Optional[GeneralConfig] = preprod_config):
        self.data_config = data_config
        self.es_source = ESData(data_config)
        self.s3_source = S3Data(data_config)
        self.snp_source = SnpData(data_config)
        
        self.es_source_ = ESData
        self.s3_source_ = S3Data
        self.snp_source_ = SnpData
    
    def _get_data(self, isin: str) -> Optional[pd.DataFrame]:
        """Fetch data from S3 and validate trade status."""
        try:
            data_frame_df = self.s3_source._get_data(isin)
            return data_frame_df
        except:
            print("data not found!")
            return None
    
    def _process_data(self, data_frame_df: pd.DataFrame, scheduler: str) -> pd.DataFrame:
        """Process the data to ensure it meets the required format."""
        if data_frame_df is None:
            return None
        prediction_column = f"er-predictions-{scheduler.lower()}"
        ground_truth = f"actual-{scheduler.lower()}-return"
        #TODO: remove comments for ignoring the warnings in editor.
        scheduler_period = scheduler_periods.get(scheduler.lower(), 1) # type: ignore
        
        if prediction_column not in data_frame_df.columns.tolist():
            return None
        
        data_frame_df = data_frame_df[["isin", "closeprice", prediction_column]]
        data_frame_df[ground_truth] = data_frame_df["closeprice"].pct_change(periods=scheduler_period, fill_method="pad") * 100
        data_frame_df[ground_truth] = data_frame_df[ground_truth].shift(-scheduler_period)
        data_frame_df[ground_truth] = data_frame_df[ground_truth].fillna(data_frame_df[prediction_column])
        data_frame_df.ffill(inplace=True)
        return data_frame_df
    
    def _get_input_data(self, isin: str, scheduler: str):
        
        data_frame_df = self._get_data(isin)
        if data_frame_df is None or (data_frame_df.empty):
            return None
        data_frame_df = data_frame_df.ffill().bfill()
        ground_truth = f"actual-{scheduler.lower()}-return"
        data_frame_df = self._process_data(data_frame_df, scheduler)
        
        if len(data_frame_df) < self.data_config.model_settings.context_length:
            return None
        model_inputs = data_frame_df[ground_truth].iloc[-self.data_config.model_settings.context_length:].tolist()
        return torch.tensor(model_inputs, dtype=torch.float32)
    
    def _retrieve_inputs(self, isin: str, scheduler: str, date: str, closeprice: float):
        data_frame_df = self._get_data(isin)
        if data_frame_df is None:
            return None
        
        data_frame_df["date"] = pd.to_datetime(data_frame_df["date"]).dt.strftime("%Y-%m-%d")
        data_frame_df = data_frame_df[data_frame_df["date"] < date]
        
        if data_frame_df.empty:
            return None
        
        data_row = data_frame_df.iloc[-1].copy()
        data_row["closeprice"] = closeprice
        data_row["date"] = date
        data_frame_df = pd.concat([data_frame_df, data_row]).drop_duplicates(subset=["date"], keep="first").reset_index(drop=True)
        
        ground_truth = f"actual-{scheduler.lower()}-return"
        data_frame_df = self._process_data(data_frame_df, scheduler)
        
        if len(data_frame_df) < self.data_config.model_settings.context_length:
            return None
        model_inputs = data_frame_df[ground_truth].iloc[-(self.data_config.model_settings.context_length+1):-1].tolist()
        return torch.tensor(model_inputs, dtype=torch.float32).unsqueeze(-1).unsqueeze(0)
    
    def get_input_data(self, geo: str, date: str, schedular: str):
        
        master_df = self.prepare_es_records(geo, date, schedular)
        model_inputs = master_df.parallel_apply(lambda row: self._retrieve_inputs(row["isin"], schedular, date, row["closeprice"]), axis=1)
        output = master_df.copy()
        output["model-inputs"] = model_inputs
        return output
    
    def _prepare_es_records(self, isin: str, date: str, scheduler: str, master: Optional[pd.DataFrame] = None):
        record = self.es_source._get_cp_data(isin, date, scheduler)
        if not(record is None):
            record = record[["isin", "closeprice", "volume", "date"]].iloc[0].to_dict()
            record["date"] = date
            return record
        elif master is not None:
            record = self.snp_source._get_stock_price(isin, master, date)
            if len(record):
                record["date"] = pd.to_datetime(record["date"]).dt.strftime("%Y-%m-%d")
                record["volume"] = np.nan
                return record.iloc[0].to_dict()
        return record
    
    def _record_placeholder(self, isin: str, date: str):
        record = {"isin": isin, "date": date, "closeprice": np.nan, "volume": np.nan}
        return record
    
    def prepare_es_records(self, geo: str, date: str, scheduler: str):
        master_df = get_master(geo)
        master_df["trade-check"] = master_df["isin"].parallel_apply(lambda isin: self.snp_source.check_trade(isin, date))
        master_df = master_df[master_df["trade-check"].isna()].reset_index(drop=True)
        closeprice_df = self.es_source.get_cp_data(geo, date)
        if closeprice_df is not None:
            closeprice_df = closeprice_df[closeprice_df["isin"].isin(master_df["isin"])].reset_index(drop=True)
            closeprice_df["date"] = pd.to_datetime(closeprice_df["date"]).dt.strftime("%Y-%m-%d")
            closeprice_df = closeprice_df[["isin", "closeprice", "volume", "date"]]
        else:
            closeprice_df = pd.DataFrame(columns=["isin", "closeprice", "volume", "date"])
        
        snp_data = master_df[~master_df["isin"].isin(closeprice_df["isin"])]["isin"]
        if len(snp_data):
            snp_data = snp_data.parallel_apply(lambda isin: self.snp_source._get_stock_price(isin, master_df, date))
            snp_df = pd.concat(snp_data.tolist()).reset_index(drop=True)
            snp_df["volume"] = np.nan
        else:
            snp_df = pd.DataFrame()
        
        closeprice_df = pd.concat([closeprice_df, snp_df]).reset_index(drop=True)
        placeholder_data = master_df[~master_df["isin"].isin(closeprice_df["isin"])]["isin"]
        if len(placeholder_data):
            placeholder_data = placeholder_data.parallel_apply(lambda isin: self._record_placeholder(isin, date))
            placeholder_df = pd.DataFrame(placeholder_data.tolist())
        else:
            placeholder_df = pd.DataFrame()
        
        closeprice_df = pd.concat([closeprice_df, placeholder_df]).reset_index(drop=True)
        return closeprice_df
    
    def np_is_nan(self, value):
        try:
            return np.isnan(value)
        except TypeError:
            return False
    
    def get_previous_business_date(self, date: str) -> str:
        """
        Get the previous business date for a given date.

        Args:
            date (str): The reference date.

        Returns:
            str: The previous business date in 'YYYY-MM-DD' format.
        """
        date = pd.to_datetime(date)
        previous_date = date - pd.tseries.offsets.BDay(1)
        return previous_date.strftime("%Y-%m-%d")
    
data_collector = DataCollector()
if __name__ == "__main__":
    data = data_collector.get_input_data("aieq", "2025-04-04", "Monthly")
    print(data)
    