from ...utils import *
from ...pandarallel_setup import *
from ...utils.common import get_master
import requests
import traceback
from datetime import datetime
from typing import Optional

class SnpData:
    
    snp_url = config["urls"]["snp_url"]
    spglobal_url = config["urls"]["spglobal_url"]
    snp_function = config["snp_creds"]["snp_function"]
    close_mnemonic = config["snp_creds"]["close_mnemonic"]
    price_check_mnemonic = config["snp_creds"]["price_check_mnemoic"]
    period_type = config["snp_creds"]["period_type"]
    head_auth = config["snp_creds"]["head_auth"]
    frequency = config["snp_creds"]["frequency"]
    content_type = config["snp_creds"]["content_type"]
    
    def __init__(self, data_config: Optional[GeneralConfig] = prod_config):
        self.data_config = data_config
        
    
    @staticmethod
    def _get_stock_price(isin, master, date, get_from_api=False):
        date = datetime.strptime(date, "%Y-%m-%d").strftime("%m/%d/%Y")
        tic = master[master["isin"] == isin]["tic"].unique()[0]
        exchange = master[master["isin"] == isin]["exchange"].unique()[0]
        
        if get_from_api:
            date = date[-4:] + '-' + date[:2] + '-' + date[3:5]
            start_date, end_date = date, date
            print(f"start-date and end-date are {start_date} and {end_date}")
            
            stock_url = f'{SnpData.snp_url}?isin={isin}&startDate={start_date}&endDate={end_date}&country_code="USA"'
            data_api = requests.get(stock_url).json()
            try:
                api_df = pd.DataFrame(data_api["data"]["stocks"])[["date", "close"]]
                api_df.sort_values(by="date", inplace=True)
                api_df.rename(columns={"close": "adj_close"}, inplace=True)
                api_df["adj_close"] = api_df["adj_close"].map(float)
            except:
                print(f"for isin: {isin} error in getting data from snp api.")
                traceback.print_exc()
                api_df = pd.DataFrame(columns=["date", "adj_close"])
            api_df["isin"] = isin
            api_df.rename(columns={"adj_close": "closeprice"}, inplace=True)
            return api_df
        else:
            data = {
                "inputRequests":
                    [
                        {
                            "function": SnpData.snp_function,
                            "identifier": f'{tic}:{exchange}',
                            "mnemonic": SnpData.close_mnemonic,
                            "properties":
                                {
                                    "periodType": SnpData.period_type,
                                    "startDate": date,
                                    "endDate": date,
                                    "frequency": SnpData.frequency,
                                }
                        }
                    ]
            }
            data_json = json.dumps(data)
            
            url = SnpData.spglobal_url
            headers = {"Authorization": SnpData.head_auth, "Content-Type": SnpData.content_type}
            response = requests.post(url, headers=headers, data=data_json)
            try:
                op = json.loads(response.text)
                if "Rows" not in op['GDSSDKResponse'][0]:
                    data = {
                        "inputRequests":
                            [
                                {
                                    "function": SnpData.snp_function,
                                    "identifier": f'{isin}',
                                    "mnemonic": SnpData.close_mnemonic,
                                    "properties":
                                        {
                                            "periodType": SnpData.period_type,
                                            "startDate": date,
                                            "endDate": date,
                                            "frequency": SnpData.frequency,
                                        }
                                }
                            ]
                    }
                    data_json = json.dumps(data)

                    url = SnpData.spglobal_url
                    headers = {'Authorization': SnpData.head_auth,'Content-type': SnpData.content_type}
                    response = requests.post(url, data=data_json, headers=headers)
                    op=json.loads(response.text)
                
                snp_df = pd.DataFrame([row["Row"] for row in op['GDSSDKResponse'][0]['Rows']], columns=['closeprice', 'date'])
                snp_df["closeprice"] = snp_df["closeprice"].map(float)
                snp_df["date"] = pd.to_datetime(snp_df["date"]).dt.strftime("%Y-%m-%d")
                snp_df["isin"] = isin
                return snp_df
            except:
                print(f'isin: {isin}, tic: {tic}, exchange: {exchange} error in getting data from snp api for date: {date}.')
                traceback.print_exc()
                snp_df = pd.DataFrame(columns=["isin", "date", "closeprice"])
                return snp_df
    
    def get_stock_price(self, geo: str, date: str, get_from_api: bool = False):
        master_df = get_master(geo)
        closeprice_df = master_df["isin"].parallel_apply(lambda _: SnpData._get_stock_price(_, master_df, date, get_from_api=get_from_api))
        return pd.concat(closeprice_df.tolist()).reset_index(drop=True)
    
    @staticmethod
    def check_trade(isin, date):
        data = {
            "inputRequests":
                [
                    {
                        "function": SnpData.snp_function,
                        "identifier": f'{isin}',
                        "mnemonic": SnpData.price_check_mnemonic,
                    }
                ]
        }
        data_json = json.dumps(data)
        headers = {"Authorization": SnpData.head_auth, "Content-Type": SnpData.content_type}
        response = requests.post(SnpData.spglobal_url, headers=headers, data=data_json)
        try:
            resp_json = json.loads(response.text)
            
            if resp_json is None:
                return np.nan
            else:
                try:
                    tmp=pd.json_normalize(resp_json['GDSSDKResponse'])
                    data = tmp['Rows'].iloc[0]
                    date_traded = data[0]['Row'][0]
                    if date_traded == 'Data Unavailable':
                        return 'Last Traded Date Unavailable'
                    else:
                        date_traded = pd.to_datetime(date_traded).strftime("%Y-%m-%d")
                        date_traded = datetime.strptime(date_traded,'%Y-%m-%d')
                        date_yesterday_obj = datetime.strptime(date,'%Y-%m-%d')
                        if date_traded<date_yesterday_obj:
                            return f'Not Traded on {date}'
                        else:
                            return np.nan
                except:
                    traceback.print_exc()
                    return np.nan
        except:
            traceback.print_exc()
            return np.nan
        
snp_source = SnpData()
if __name__ == "__main__":
    print(get_master("aieq")["isin"].parallel_apply(lambda _: snp_source.check_trade(_, "2025-04-04")))