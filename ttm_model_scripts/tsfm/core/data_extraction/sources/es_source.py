from ...utils import *
from ...pandarallel_setup import *
from ...utils.common import get_master
from typing import Optional

class ESData:
    """
    Class to handle data extraction from Elasticsearch.
    """
    es_manager = prod_es_manager
    data_config = prod_config
    
    def __init__(self, data_config: Optional[GeneralConfig] = prod_config):
        """
        Initialize the ESData class with a data configuration.
        Args:
            data_config (Optional[GeneralConfig]): Configuration for data sources.
        """
        self.data_config = data_config
    
    @staticmethod
    def _get_cp_data(*args):
        """
        Retrieve close price data from Elasticsearch.
        Overloaded function to handle different argument combinations.
        Args:
            *args: Variable length argument list.
                - (isin: str, date: str, schedular: str): Retrieve data for a specific ISIN, date, and scheduler.
                - (date: str): Retrieve data for a specific date.
        Returns:
            DataFrame: The close price data retrieved from Elasticsearch.
        """
        data_source = ESData.data_config.data_sources.closeprice
        if len(args) == 3:
            isin, date, schedular = args
            return ESData.es_manager.get_values_from_es(isin, date, data_source, schedular)
        elif len(args) == 1:
            date = args[0]
            return ESData.es_manager.get_es_data_by_date(date, data_source)
        else:
            raise ValueError("Invalid arguments provided to _get_cp_data.")
    
    @staticmethod
    def _get_er_data(isin: str, date: str, schedular: str):
        """
        Retrieve expected return data from Elasticsearch for a specific ISIN and date.
        Args:
            isin (str): The ISIN code for the data to be retrieved.
            date (str): The date for the data to be retrieved.
            schedular (str): The scheduler for the data retrieval.
        Returns:
            DataFrame: The expected return data retrieved from Elasticsearch.
        """
        data_source = ESData.data_config.data_sources.expected_return
        data_frame_df = ESData.es_manager.get_values_from_es(isin, date, data_source, schedular)
        return data_frame_df
    
    @staticmethod
    def _get_er_data(date: str):
        """
        Retrieve expected return data from Elasticsearch for a specific date.
        Args:
            date (str): The date for the data to be retrieved.
        Returns:
            DataFrame: The expected return data retrieved from Elasticsearch.
        """
        data_source = ESData.data_config.data_sources.expected_return
        data_frame_df = ESData.es_manager.get_es_data_by_date(date, data_source)
        return data_frame_df
    
    def get_cp_data(self, geo: str, date: str):
        """
        Retrieve close price data for a specific geographical location and date.
        Args:
            geo (str): The geographical location for the data to be retrieved.
            date (str): The date for the data to be retrieved.
        Returns:
            DataFrame: The filtered close price data.
        """
        master_isins = get_master(geo)["isin"]
        closeprice_df = ESData._get_cp_data(date)
        if closeprice_df is None:
            return None
        filtered_df = closeprice_df[closeprice_df["isin"].isin(master_isins)].drop_duplicates(subset=["isin", "date"], keep="first")
        return filtered_df.reset_index(drop=True)
    
    def get_er_data(self, geo: str, date: str, schedular: str):
        """
        Retrieve expected return data for a specific geographical location and date.
        Args:
            geo (str): The geographical location for the data to be retrieved.
            date (str): The date for the data to be retrieved.
        Returns:
            DataFrame: The filtered expected return data.
        """
        master_isins = get_master(geo)["isin"]
        expected_return_df = ESData._get_er_data(date)
        filtered_df = expected_return_df[expected_return_df["isin"].isin(master_isins)]
        filtered_df = filtered_df[filtered_df["schedular"] == schedular]
        return filtered_df.reset_index(drop=True)

es_data = ESData()

if __name__ == "__main__":
    print(es_data.get_cp_data("aieq", "2025-04-04"))