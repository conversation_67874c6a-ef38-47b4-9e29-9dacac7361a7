from ...utils.common import get_master
from ...utils import *
from ...pandarallel_setup import *
from typing import Optional

class S3Data:
    
    s3_manager = s3_managers["prod"]["monthly"]
    
    def __init__(self, data_config: Optional[GeneralConfig]=prod_config):
        self.data_config = data_config
        S3Data.s3_manager.s3_config = data_config
        
    @staticmethod
    def _get_data(isin):
        """
        Get data from S3 using the S3 manager.
        Args:
            isin (str): The ISIN code for the data to be retrieved.
        Returns:
            DataFrame: The data retrieved from S3.
        """
        data_frame_df = S3Data.s3_manager.read_data(isin)
        return data_frame_df
    
    def get_data(self, geo):
        master_df = get_master(geo)
        data = master_df["isin"].parallel_apply(lambda isin: S3Data._get_data(isin))
        return data.tolist()

s3_data = S3Data()
if __name__ == "__main__":
    print(s3_data.get_data("aieq"))