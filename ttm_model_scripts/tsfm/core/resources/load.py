import os
import json
from ..read_yaml import config

def load_json(file_name: str, base_path: str) -> dict:
    """
    Loads a JSON file from the specified base path.

    Args:
        file_name (str): The name of the JSON file to load.
        base_path (str): The base path where the JSON file is located.

    Returns:
        dict: The loaded JSON data.
    """
    file_path = os.path.join(base_path, file_name)
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"JSON file not found: {file_path}")
    
    with open(file_path, "r") as parser:
        return json.load(parser)

script_dir = os.path.dirname(os.path.abspath(__file__))
process_settings = config["process_settings"]

resources = {}
for resource_key, resource_path in process_settings.items():
    resources[resource_key] = load_json(resource_path, script_dir)