from ...utils import *
from ...pandarallel_setup import *
from ...utils.common import get_master
from ...read_yaml import *
from typing import Optional, List

class ESData_re:
    """
    Class to re-upload the data to s3 for tomorrow's run.
    """
    es_manager = preprod_es_manager
    s3_manager = prod_s3_managers[config["schedulers"]["monthly"].lower()]
    data_config = prod_config
    
    def __init__(self, data_config: Optional[GeneralConfig]=prod_config):
        """
        Initializes the ESData_re class with a data-configuration.
        Args:
            data_config (Optional[GeneralConfig]): configuration for data-sources.
        """
        self.data_config = data_config
    
    @staticmethod
    def _get_data_single_scheduler(isin: str, date: str, scheduler: str):
        """
        Fetches data for a single scheduler and merges it with the date template.
        """
        
        """redefine the es-manager to manage the opensearch limits."""
        es_manager = ProdEsManager()
        
        year, backstep = int(date.split('-')[0]), 9
        data_source = ESData_re.data_config.data_sources.expected_return
        data_frame = es_manager.get_es_data(isin, [year-backstep, year], data_source)

        if data_frame is None or data_frame.empty:
            return None

        data_frame = data_frame[data_frame["schedular"] == scheduler].reset_index(drop=True)
        if data_frame.empty:
            return None

        er_column = f"actual_{scheduler.lower()}_return_predictions"
        data_frame = data_frame[["isin", "date", "closeprice", er_column]]
        data_frame["date"] = pd.to_datetime(data_frame["date"]).dt.strftime("%Y-%m-%d")
        return data_frame
    
    @staticmethod
    def get_data_single_scheduler(geo: str, date: str, scheduler: str):
        
        master_df = get_master(geo)
        data = master_df["isin"].parallel_apply(lambda isin: ESData_re._get_data_single_scheduler(isin, date, scheduler))
        return data

    
re_es_source = ESData_re()
if __name__ == "__main__":
    dates = re_es_source.get_data_single_scheduler("aieq", "2025-04-04")
    print(dates)
        