from .sources import *
from ..read_yaml import *
from typing import Optional

class DataValidator:
    
    s3_manager = prod_s3_managers["monthly"]
    es_source = re_es_source
    
    def __init__(self, data_config: Optional[GeneralConfig] = preprod_config):
        self.data_config = data_config
        DataValidator.s3_manager.s3_config = data_config
    
    def _get_date_template(self, isin, date):
        data_frame_df = DataValidator.es_source._get_data_single_scheduler(isin, date, config["schedulers"]["monthly"])
        if data_frame_df is None:
            return None
        return data_frame_df[["date"]].iloc[-self.data_config.model_settings.buffer_length:].reset_index(drop=True)
    
    @staticmethod
    def safety_check(data_frames: List[Optional[pd.DataFrame]], schedulers: List[str]) -> List[pd.DataFrame]:
        """
        Ensures all data frames have consistent structure by filling missing data with NaN.
        """
        common_data = next((df[["isin", "date", "closeprice"]] for df in data_frames if df is not None), None)
        if common_data is None:
            return []

        safe_data_frames = []
        for idx, data_frame in enumerate(data_frames):
            if data_frame is None:
                scheduler_column = f"er-predictions-{schedulers[idx].lower()}"
                safe_data_frame = common_data.copy()
                safe_data_frame[scheduler_column] = np.nan
            else:
                er_column = f"actual_{schedulers[idx].lower()}_return_predictions"
                scheduler_column = f"er-predictions-{schedulers[idx].lower()}"
                safe_data_frame = data_frame.rename(columns={er_column: scheduler_column})
            
            safe_data_frame = safe_data_frame.ffill()
            safe_data_frame = safe_data_frame[~safe_data_frame["closeprice"].isna()].reset_index(drop=True)
            safe_data_frames.append(safe_data_frame)

        return safe_data_frames
    
    @staticmethod
    def _validate_data(isin: str, date: str, date_template: str):
        
        schedulers = [config["schedulers"]["monthly"], config["schedulers"]["daily"]]
        data_frames =[DataValidator.es_source._get_data_single_scheduler(isin, date, scheduler) for scheduler in schedulers]
        
        data_frames = [pd.merge(date_template, data_frame_df, on=["date"], how="left").reset_index(drop=True) 
                       if data_frame_df is not None else None for data_frame_df in data_frames]
        
        safe_data_frames = DataValidator.safety_check(data_frames, schedulers)
        
        if len(safe_data_frames) <= 0:
            return None
        
        data_frame_df = pd.merge(safe_data_frames[0], safe_data_frames[1], on=["date", "isin", "closeprice"], how="left")
        data_frame_df["er-predictions-bi-monthly"] = data_frame_df["er-predictions-monthly"].shift(-scheduler_periods["monthly"])
        data_frame_df["er-predictions-tri-monthly"] = data_frame_df["er-predictions-bi-monthly"].shift(-scheduler_periods["monthly"])
        data_frame_df["er-predictions-bi-daily"] = data_frame_df["er-predictions-daily"].shift(-old_scheduler_periods["daily"])
        data_frame_df["er-predictions-quarterly"] = 100 * ((data_frame_df["er-predictions-bi-monthly"]/100 + 1) *
                                                                                (data_frame_df["er-predictions-tri-monthly"]/100 + 1) * (data_frame_df["er-predictions-monthly"]/100 + 1) - 1)
        
        data_frame_df["er-predictions-daily"] = 100 * ((data_frame_df["er-predictions-daily"]/100 + 1) * (data_frame_df["er-predictions-bi-daily"]/100 + 1) - 1)
        
        schedulers = config["schedulers"].keys()
        for scheduler in schedulers:
            pr_column = f"actual-{scheduler}-return"
            scheduler_period = scheduler_periods[scheduler]
            data_frame_df[pr_column] = 100 * data_frame_df["closeprice"].pct_change(periods=scheduler_period).shift(-scheduler_period)
            
        done = DataValidator.s3_manager.write_data(data_frame_df, isin)    
        return done
    
    def validate_data(self, geo: str, date: str):
        master_df = get_master(geo)
        
        if len(master_df) <= 0:
            return
        
        run_template_search, template_isin_index = 1, 0
        while run_template_search:
            template_isin = master_df["isin"].iloc[template_isin_index]
            date_template = self._get_date_template(template_isin, date)
            if date_template is not None:
                run_template_search = 0
            template_isin_index += 1
        
        dones = master_df["isin"].parallel_apply(lambda isin: DataValidator._validate_data(isin, date, date_template))
        return dones
    
    
data_validator = DataValidator()

if __name__ == "__main__":
    date_template = data_validator._get_date_template("AN8068571086", "2025-04-04")
    data_frames = data_validator._validate_data("AN8068571086", "2025-04-04", date_template)
    data_validator.validate_data("aieq", "2025-04-04")
    print(data_frames)