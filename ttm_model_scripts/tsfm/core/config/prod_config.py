from .base_config import *
from ..read_yaml import *
from ..resources.load import resources

class ProdConfig(GeneralConfig):
    pass

data_sources = DataSourceConfig(**config["data_sources"])
model_settings = ModelConfig(**config["model_settings"])
resource_config = ResourceConfig(**resources)
metrics_config = MetricsConfig(**config["metrics_settings"])
es_stores = DestESConfig(**config["prod_es_stores"])
prod_config = ProdConfig(**config["prod_version_control"], 
                                                data_sources=data_sources, 
                                                model_settings=model_settings,
                                                resource_config=resource_config,
                                                metrics_config=metrics_config,
                                                es_store_config=es_stores)
