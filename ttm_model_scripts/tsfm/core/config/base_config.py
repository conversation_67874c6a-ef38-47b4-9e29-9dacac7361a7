from typing_extensions import NamedTuple, List
from dataclasses import dataclass

#TODO: simplify the confg
class DataSourceConfig(NamedTuple):
    """configuration for data-sources."""
    closeprice: str
    expected_return: str
    model_predictions: str
    
class ModelConfig(NamedTuple):
    """model-settings"""
    
    model_year: str
    ttm_model_revision: str
    context_length: str
    buffer_length: str
    head_dropout_rate: str
    
    
    @property
    def context_length(self) -> int:
        """Return context_length as an integer."""
        return int(self._context_length)
    
    @property
    def buffer_length(self) -> int:
        """Return buffer_length as an integer."""
        return int(self._buffer_length)
    
    @property
    def head_dropout_rate(self) -> float:
        """Return head_dropout_rate as a float."""
        return float(self._head_dropout_rate)

    @context_length.setter
    def context_length(self, value: str):
        """Set context_length as a string."""
        self._context_length = value
        
    @buffer_length.setter
    def buffer_length(self, value: str):
        """Set buffer_length as a string."""
        self._buffer_length = value
    
    @head_dropout_rate.setter
    def head_dropout_rate(self, value: str):
        """Set head_dropout_rate as a string."""
        self._head_dropout_rate = value
        
class ResourceConfig(NamedTuple):
    """configuration for resources."""
    sector_map: dict
    ts_dict: dict
    
class DestESConfig(NamedTuple):
    """configuration for es-destinations"""
    predictions: str
    metrics: str
    
@dataclass
class MetricsConfig:
    """configuration for metrics"""
    
    req_columns: List[str]
    all_metrics: List[str]
    rolling_period: int
    
    def __post_init__(self):
        if isinstance(self.req_columns, str):
            self.req_columns = list(self.req_columns)
        if isinstance(self.all_metrics, str):
            self.all_metrics = list(self.all_metrics)
        if isinstance(self.rolling_period, str):
            self.rolling_period = int(self.rolling_period)
    
class GeneralConfig(NamedTuple):
    """ general configuration """
    
    s3_bucket: str
    s3_model_bucket_key: str
    s3_destination_data_bucket: str
    s3_source_data_bucket: str
    s3_predictions_prefix: str
    s3_metrics_prefix: str
    s3_model_prefix: str
    s3_features_prefix: str
    s3_source_data_prefix: str
    s3_destination_data_prefix: str
    s3_meta_data_prefix: str
    data_sources: DataSourceConfig
    model_settings: ModelConfig
    resource_config: ResourceConfig
    metrics_config: MetricsConfig
    es_store_config: DestESConfig
    
