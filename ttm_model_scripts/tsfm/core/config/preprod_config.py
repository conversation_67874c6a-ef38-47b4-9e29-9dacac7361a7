from .base_config import *
from ..read_yaml import *
from ..resources.load import resources
class PreprodConfig(GeneralConfig):
    pass

data_sources = DataSourceConfig(**config["data_sources"])
model_settings = ModelConfig(**config["model_settings"])
resource_config = ResourceConfig(**resources)
metrics_config = MetricsConfig(**config["metrics_settings"])
es_stores = DestESConfig(**config["preprod_es_stores"])
preprod_config = PreprodConfig(**config["preprod_version_control"], 
                                                            data_sources=data_sources,
                                                            model_settings=model_settings,
                                                            resource_config=resource_config,
                                                            metrics_config=metrics_config,
                                                            es_store_config=es_stores)
