absl-py==2.1.0
accelerate==1.3.0
aiobotocore==2.21.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aioitertools==0.12.0
aiosignal==1.3.2
alpine==1.0.14
anyio==4.8.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asgiref==3.2.10
asttokens==3.0.0
async-lru==2.0.4
async-timeout==4.0.2
attrs==25.1.0
aws-requests-auth==0.4.3
babel==2.16.0
bcrypt==4.0.0
beautifulsoup4==4.12.3
bleach==6.2.0
boto==2.49.0
boto3==1.21.16
botocore==1.37.1
cached-property==1.5.1
cachetools==5.5.2
certifi==2021.10.8
cffi==1.15.1
chardet==3.0.4
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
comm==0.2.2
contourpy==1.3.0
cramjam==2.9.1
cryptography==38.0.1
cycler==0.12.1
dask==2024.8.0
dask-expr==1.1.10
datasets==3.2.0
debugpy==1.8.12
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.13
dill==0.3.8
dnspython==2.2.1
docutils==0.15.2
elastic-transport==8.1.0
elasticsearch==8.1.0
emoji==1.7.0
et-xmlfile==1.1.0
exceptiongroup==1.2.2
exchange_calendars==4.5.6
executing==2.2.0
expiringdict==1.2.2
fastjsonschema==2.21.1
fastparquet==2024.11.0
filelock==3.17.0
fonttools==4.55.8
fqdn==1.5.1
frozenlist==1.5.0
fsspec==2025.3.2
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
googleapis-common-protos==1.69.2
greenlet==1.1.2
grpcio==1.70.0
gunicorn==20.1.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
huggingface-hub==0.28.0
ibm-cos-sdk==2.11.0
ibm-cos-sdk-core==2.11.0
ibm-cos-sdk-s3transfer==2.11.0
ibm-watson-machine-learning==1.0.333
idna==2.8
importlib_metadata==8.6.1
importlib_resources==6.5.2
injectable==3.4.4
injector==0.18.4
ipykernel==6.29.5
ipython==8.18.1
ipywidgets==8.1.5
isoduration==20.11.0
jedi==0.19.2
Jinja2==3.1.5
jmespath==0.10.0
joblib==1.3.2
json5==0.10.0
jsonpickle==2.1.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.11.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyterlab==4.3.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
kaleido==0.2.1
kiwisolver==1.4.7
korean-lunar-calendar==0.3.1
lazy-object-proxy==1.5.1
locket==1.0.0
lomond==0.3.3
Markdown==3.7
MarkupSafe==3.0.2
matplotlib==3.9.4
matplotlib-inline==0.1.7
mistune==3.1.1
mpmath==1.3.0
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==0.4.3
narwhals==1.24.1
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.2.1
nltk==3.7
notebook==7.3.2
notebook_shim==0.2.4
numpy==1.25.2
numpy-ext==0.9.9
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
openpyxl==3.0.9
opensearch-py==2.0.1
overrides==7.7.0
packaging==24.2
pandarallel==1.6.5
pandas==2.2.3
pandas_market_calendars==4.6.1
pandocfilters==1.5.1
parameters-validation==1.2.0
paramiko==2.11.0
parso==0.8.4
partd==1.4.2
pexpect==4.9.0
pillow==11.1.0
platformdirs==4.3.6
plotly==6.0.0
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.26.1
protobuf==5.29.3
psutil==6.1.1
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==19.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycollect==0.2.3
pycparser==2.21
Pygments==2.19.1
pyluach==2.2.0
pymongo==3.6.1
PyNaCl==1.5.0
pyparsing==3.0.7
pysftp==0.2.9
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==3.2.1
python-snappy==0.6.1
pytz==2021.3
PyYAML==6.0.2
pyzmq==26.2.1
referencing==0.36.2
regex==2022.3.2
requests==2.32.3
requests-aws4auth==1.3.1
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rpds-py==0.22.3
rsa==4.9
s3fs==2025.3.2
s3transfer==0.5.2
safetensors==0.5.2
scikit-learn==0.24.0
scipy==1.9.2
Send2Trash==1.8.3
setuptools-scm==8.1.0
six==1.16.0
sklearn==0.0
sniffio==1.3.1
soupsieve==2.6
sqlparse==0.2.4
stack-data==0.6.3
sympy==1.13.1
tabulate==0.8.9
tensorboard==2.18.0
tensorboard-data-server==0.7.2
terminado==0.18.1
threadpoolctl==3.1.0
tinycss2==1.4.0
tokenizers==0.21.0
tomli==2.2.1
toolz==1.0.0
torch==2.6.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.48.1
triton==3.2.0
# Editable install with no version control (tsfm_public==0.1.dev0+d20250221)
types-python-dateutil==2.9.0.20241206
typing-inspect==0.6.0
typing_extensions==4.12.2
tzdata==2025.1
uri-template==1.3.0
uritemplate==4.1.1
urllib3==1.26.20
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
Werkzeug==3.1.3
widgetsnbextension==4.0.13
wrapt==1.13.3
xlrd==2.0.1
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
