import numpy as np # type: ignore
from numpy_ext import rolling_apply as rolling_apply_ext # type: ignore
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error # type: ignore
from .s3_helper import *


monthly_schedular = config.get(section='schedulars', option='monthly_schedular')
quarterly_schedular = config.get(section='schedulars', option='quarterly_schedular')
daily_schedular = config.get(section='schedulars', option='daily_schedular')
weekly_schedular = config.get(section='schedulars', option='weekly_schedular')

schedular = monthly_schedular.lower()

req_columns = ["isin", "date", "closeprice", "volume", "schedular", "tag", "updated_at"]
all_metrics = ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "confidence_score", "avg_confidence_score", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day"]
schedular_dict = {
    monthly_schedular.lower(): 22,
    weekly_schedular.lower(): 5,
    daily_schedular.lower(): 2,
    quarterly_schedular.lower(): 66,
}

eps = 1e-5
def accuracy_function(df_series, coff = 500):
    '''
    Accuracy conversion metric to convert daily APE into accuracy for ER.
    '''
    return df_series.apply(lambda x: 97/( 1 + 20 * np.exp((-coff/(x+eps)))) if x <  100 else max(0, 106.7 - 0.213 * x))

def calculate_accuracy(df_data, prediction_col = 'predictions', target_col = f'close_change_{schedular.lower()}', coff = 500):
    '''
    This function calculates the accuracy based on actual er and predicted er for an isin for daily, 14 & 22 trading days rolling average.
    Accuracy calculations:
        - Calculate the modified APE for individual data points (modification: divide by max( abs(actual), abs(pred) )) )
        - Convert it to accuracy using 100/1+20*(exp^-500/x) if x < 100 and we linearly degrade to 0 for ape = 500


    Input params (required):
        - date
        - prediction_col : predicted ER value in percentage (prediction for today's date that are generated 1 month back ie. if date is 28 Dec 2023, it'll have the predictions generated on 28 Nov 2023)
        - target_col : target column value in percentage, it's also shifted by 22 days similar to prediction_col
        - coff : coefficient to be used in accuracy function

    Output columns:
        - accuracy_1_day: accuracy calculated for each day
        - accuracy_14_day : accuracy calculated for 14 day rolling window
        - accuracy_22_day : accuracy calculated for 22 day rolling window

    Range of columns:
        - prediction_col : [0, 100]
        - target_col : [0, 100]
        - accuracy: [0, 100]
    '''
    if prediction_col not in df_data.columns:
        raise Exception('Prediction column not in Dataframe')

    if target_col not in df_data.columns:
        raise Exception('Target column not in Dataframe')

    # Remove any nan's in prediction or target cols
    df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

    # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
    df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
    df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

    # Calculate RMS of MAPE over a rolling of 14 days
    df_data['accuracy_1_day'] = accuracy_function(df_data['daily_ape'], coff = coff)

    # Calculate RMS of MAPE over a rolling of 22 days
    df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
    df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5
    df_data.drop(columns=['denominator'], inplace=True)
    return df_data

def directionality_score_calc(prediction_direction, close_direction):
    directionality_df = pd.DataFrame()
    directionality_df['prediction_direction'] = prediction_direction
    directionality_df['close_direction'] = close_direction
    correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)]
    incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)]
    relaxation_count = len(incorrect_direction_df[(abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)])
    if len(directionality_df) == relaxation_count:
        directionality_score = np.nan
    else:
        directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
    return directionality_score


def calculate_metrics(df, isin, period, prediction_column = f'{schedular.lower()}_predictions', actual_column =  f'actual_{schedular.lower()}_returns', metrics_to_calculate = all_metrics, n_features = 0):
    if len(df) < period:
        return f'Dataframe size for {isin} too small to calculate metrics.'

    if prediction_column not in req_columns:
        req_columns.append(prediction_column)
    if actual_column not in req_columns:
        req_columns.append(actual_column)

    df = df.rename(columns = {prediction_column: 'predictions', actual_column: 'actual_returns'})
    metrics_columns = req_columns + metrics_to_calculate


    # Total perc diff
    if 'total_perc_diff' in metrics_columns or 'abs_total_diff' in metrics_columns:
        df['total_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.mean() - prediction.mean()) / prediction.mean(), period, df['actual_returns'].values, df['predictions'].values)

    # Abs total diff
    if 'abs_total_diff' in metrics_columns:
        df['abs_total_diff'] = abs(df['total_perc_diff'])

    # Total variance perc diff
    if 'total_variance_perc_diff' in metrics_columns or 'abs_total_variance_perc_diff' in metrics_columns:
        df['total_variance_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.var() - prediction.var()) / prediction.var(), period, df['actual_returns'].values, df['predictions'].values)

    # Abs total variance perc diff
    if 'abs_total_variance_perc_diff' in metrics_columns:
        df['abs_total_variance_perc_diff'] = abs(df['total_variance_perc_diff'])

    # MAE
    if 'mean_absolute_error' in metrics_columns:
        df['mean_absolute_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: abs(df_series).mean())

    # MSE
    if 'mean_squared_error' in metrics_columns or 'root_mean_squared_error' in metrics_columns:
        df['mean_squared_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: ((df_series ** 2).sum() / period))

    # RMSE
    if 'root_mean_squared_error' in metrics_columns:
        df['root_mean_squared_error'] = df['mean_squared_error'] ** 0.5

    # R2 score
    if 'r2_score' in metrics_columns or 'adjusted_r2_score' in metrics_columns:
        df['r2_score'] = rolling_apply_ext(r2_score, period, df['actual_returns'].values, df['predictions'].values)

    # Adjusted R2 score
    if 'adjusted_r2_score' in metrics_columns:
        df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features - 1))

    # Mean directionality
    if 'mean_directionality' in metrics_columns:
        df['mean_directionality'] = (df['actual_returns'] * df['predictions']).rolling(period).apply(lambda df_series: 100 * ((df_series) > 0).sum() / period)

    # Correlation score
    if 'correlation_score' in metrics_columns:
        df['correlation_score'] = rolling_apply_ext(lambda target, prediction: np.corrcoef(target, prediction)[0][1], period, df['actual_returns'].values, df['predictions'].values)

    # Accuracy
    if "accuracy_14_day" in metrics_columns or "accuracy_22_day" in metrics_columns or "accuracy_1_day"  in metrics_columns:
        df = calculate_accuracy(df, 'predictions', 'actual_returns')

    # Confidence score
    if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
        min_confidence = 0.01

        max_values =  df['actual_returns'].rolling(period * 24, min_periods = period).max()
        min_values =  df['actual_returns'].rolling(period * 24, min_periods = period).min()
        filt1 = [df.loc[i, 'predictions'] >= max_values.loc[i] for i in range(len(df))]
        filt2 = [df.loc[i, 'predictions'] <= min_values.loc[i] for i in range(len(df))]
        filt3 = [df.loc[i, 'actual_returns'] >= max_values.loc[i] for i in range(len(df))]
        filt4 = [df.loc[i, 'actual_returns'] <= min_values.loc[i] for i in range(len(df))]

        df['confidence_score'] = [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
        max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_returns"])/(max_values.loc[i] - df.loc[i, "predictions"]) if df.loc[i, "actual_returns"] > df.loc[i, "predictions"] else (df.loc[i, "actual_returns"] - min_values.loc[i]) / (df.loc[i, "predictions"] - min_values.loc[i])) for i in range(len(df))]

    # Average confidence score
    if 'avg_confidence_score' in metrics_columns:
        df['avg_confidence_score'] = df['confidence_score'].rolling(period // 2).mean()

    if 'directionality_score' in metrics_columns:
        directionality_df = pd.DataFrame()
        directionality_df["prediction_direction"] = (df["predictions"] - df['predictions'].shift(1)) / df['predictions'].shift(1)
        directionality_df["close_direction"] = (df["actual_returns"] - df['actual_returns'].shift(1)) / df['actual_returns'].shift(1)
        df['directionality_score'] = rolling_apply_ext(directionality_score_calc, period, directionality_df['prediction_direction'], directionality_df['close_direction'])
    df['isin'] = isin
    df = df.rename(columns = {'predictions': prediction_column, 'actual_returns': actual_column})
    return df[metrics_columns].reset_index(drop = True)
