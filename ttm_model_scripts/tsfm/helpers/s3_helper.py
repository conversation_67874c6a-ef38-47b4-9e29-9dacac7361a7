import boto3 # type: ignore
import os
from io import StringIO, BytesIO
import pandas as pd # type: ignore
import openpyxl # type: ignore

import configparser

config=configparser.ConfigParser()
properties_path=os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'ttm.properties')
config.read(properties_path)

# S3 helper functions
class s3helper():
    def __init__(self, aws_access_id, aws_access_secret):
        self.s3client = boto3.client('s3',aws_access_key_id=aws_access_id, aws_secret_access_key=aws_access_secret)

    def get_s3filelist(self, bucketname, foldername, days = 'all', filetype = 'csv'):
        paginator = self.s3client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=bucketname, Prefix=foldername)
        file_list = []
        if days == 'all':
            for page in pages:
                file_list += [obj['Key'] for obj in page['Contents']]
            if filetype is not None:
                file_list = [x for x in file_list if x.split('.')[-1] == filetype]
            return file_list
        for page in pages:
            file_list += [obj['Key'] for obj in sorted(page['Contents'], key=lambda obj: int(obj['LastModified'].strftime('%Y%m%d%H%M%S')))]
            file_list = [w.replace(foldername, '') for w in file_list][days:]
        if filetype is not None:
            file_list = [x for x in file_list if x.split('.')[-1] == filetype]
        return file_list

    def download_s3file(self, bucketname,foldername,filename,localfoldername):
        if localfoldername != '':
            if not os.path.exists(localfoldername):
                os.mkdir(localfoldername)
        self.s3client.download_file(bucketname,os.path.join(foldername, filename) , os.path.join(localfoldername, filename))

    def upload_s3file(self, file_name, bucket, object_name):
        response = self.s3client.upload_file(file_name, bucket, object_name)

    def create_s3folder(self, folder_name, bucket_name, folder_location):
        self.s3client.put_object(Bucket=bucket_name, Key=(folder_location + folder_name + '/'))

    def df2s3(self, df, bucket_name, file_name):
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index = False)
        self.s3client.put_object(Body=csv_buffer.getvalue(), Bucket=bucket_name, Key=file_name)

    def s32df(self, bucket_name, file_name):
        csv_obj = self.s3client.get_object(Bucket=bucket_name, Key=file_name)
        csv_string = csv_obj['Body'].read().decode('utf-8')
        df = pd.read_csv(StringIO(csv_string))
        return df

    def s3xl2df(self, bucket_name, file_name, sheet_name = None):
        obj = self.s3client.get_object(Bucket=bucket_name, Key=file_name)
        body = obj['Body'].read()
        if sheet_name is not None:
            data = pd.read_excel(BytesIO(body), sheet_name = sheet_name)
        else:
            data = pd.read_excel(BytesIO(body))
        return data

    def copy_s3file(self, src_bucket, src_file, dst_bucket, dst_file):
        copy_source = {'Bucket': src_bucket, 'Key': src_file}
        self.s3client.copy_object(Bucket=dst_bucket, CopySource=copy_source, Key=dst_file)

    def delete_s3file(self, bucket_name, file_name):
        self.s3client.delete_object(Bucket=bucket_name, Key=file_name)


def getBucketKey(s3path):
    s3path = s3path[len('s3://'):]
    bucket = s3path.split('/')[0]
    key = '/'.join(s3path.split('/')[1:])
    return bucket, key

def read_excel(filename, sheetname):
    df = pd.DataFrame(openpyxl.load_workbook(filename, data_only = True)[sheetname].values)
    df.columns = df.iloc[0]
    return df.drop([0])

## s3 helper object
aws_access_id = config.get(section='aws-s3', option='aws_access_key')
aws_secret_id = config.get(section='aws-s3', option='aws_secret_key')

s3h = s3helper(aws_access_id, aws_secret_id)
