# Elastic Search functions

from opensearchpy.connection.http_requests import RequestsHttpConnection # type: ignore
from aws_requests_auth.aws_auth import AWSRequestsAuth # type: ignore
from opensearchpy.client import OpenSearch # type: ignore
from requests_aws4auth import AWS4Auth # type: ignore
import logging
import os
import io
import pandas as pd # type: ignore
import traceback
import configparser

config=configparser.ConfigParser()
properties_path=os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'ttm.properties')
config.read(properties_path)

host = config.get(section='opensearch_prod', option='host')
port = config.get(section='opensearch_prod', option='port')
key_id = config.get(section='opensearch_prod', option='key_id')
secret = config.get(section='opensearch_prod', option='secret')
region = config.get(section='opensearch_prod', option='region')
service=config.get(section='opensearch_prod', option='service')

def create_index(index_name):

    print("create index",index_name)
    index_body = {
        'settings': {
            'index': {
                'number_of_shards': 4
            }
        }
    }
    try:
        es.indices.create(index_name, body=index_body)
        indices = str(es.indices.get_alias().keys())
    except:
        print(traceback.format_exc())

def connect_openSearch():

    hosts = [f'https://{host}:{port}']


    client = OpenSearch(
        hosts=hosts,
        port=port,
        http_auth=auth,
        connection_class=RequestsHttpConnection
     )
    return client
def get_data_from_s3(s3, bucket_name, filename):
    try:
        s3_response_object = s3.get_object(Bucket=bucket_name, Key=filename)

        object_content = s3_response_object['Body'].read()

        ext = os.path.splitext(filename)[1]
        if ext == ".xlsx":
            excel_object = pd.ExcelFile(object_content)
            df = excel_object.parse()
        else:
            df = pd.read_csv(io.BytesIO(object_content))
        # filename = get_latest_obj(s3, bucket_name, filename)

#         df = pd.read_excel(object_content, engine='xlrd')

    except:
        print(traceback.format_exc())
        return None
    return df
auth = AWSRequestsAuth(aws_access_key=key_id,
                       aws_secret_access_key=secret,
                       aws_host=host,
                       aws_region=region,
                       aws_service=service)
es=connect_openSearch()
global indices
indices=str(es.indices.get_alias().keys())

def get_es_data(isin, dates, index_prefix, schedular = None):
    if schedular is not None:
        schedular = schedular.lower()
    data=[]
    for year in range(dates[0], dates[1] + 1):
        q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
        try:
            result = es.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
            for rs in result['hits']['hits']:
                if f'_{schedular[0]}' in rs['_id']:
                    es_data=rs['_source']
                    data.append(es_data)
        except:
            print(f'ES data for {index_prefix} not present for {year}.')
    df=pd.DataFrame(data)
#     if (schedular != None) and ('schedular' in df.columns):
#         df = df[df['schedular'] == schedular].reset_index(drop = True)
    if len(df) == 0:
        return df
    df['date']=pd.to_datetime(df['date'])
    df.sort_values('date', ascending=True, inplace=True)
    df.reset_index(inplace=True, drop=True)
    return df
