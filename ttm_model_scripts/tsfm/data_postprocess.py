from utils import *
from aws_requests_auth.aws_auth import AWSRequestsAuth # type: ignore
from opensearchpy import OpenSearch, RequestsHttpConnection # type: ignore
from requests_aws4auth import AWS4Auth # type: ignore

host = config.get(section='opensearch_prod', option='host')
port = config.get(section='opensearch_prod', option='port')
key_id = config.get(section='opensearch_prod', option='key_id')
secret = config.get(section='opensearch_prod', option='secret')
region = config.get(section='opensearch_prod', option='region')
service=config.get(section='opensearch_prod', option='service')

vc_bucket = config.get(section='version-bucket', option='bucket_name')
model_name = config.get(section='version-bucket', option='model_name')
eq_ttm_model=config.get(section='es-indexes', option='ttm_model')

def connet_elasticsearch():
    es_auth_prod = AWSRequestsAuth(aws_access_key=key_id,
                        aws_secret_access_key=secret,
                        aws_host=host,
                        aws_region=region,
                        aws_service=service)

    hosts = [f'https://{host}:{port}']

    client = OpenSearch(
        hosts=hosts,
        port=port,
        http_auth=es_auth_prod,
        connection_class=RequestsHttpConnection
    )

    return client

client = connet_elasticsearch()

def upload_data_to_elasticsearch(es_client, data, schedular, es_index=None):
    
    if es_index is None:
        es_index = eq_ttm_model
    
    documents = []
    for index, row in data.iterrows():
        document_id = f"{row['isin']}_{row['date']}_{schedular.lower()[0]}"
        year = row["date"].split('-')[0]
        document = {"index": {"_index": f"{es_index}_{year}", "_id": document_id}}
        data = row.to_dict()
        documents.append(document)
        documents.append(data)
    try:
        response = es_client.bulk(documents)
        return documents, response
    except Exception as e:
        logging.error(f"error in pushing the data to elastic search: {e}")

def version_control(results, geo, schedular, mode='predictions'):

    date = results['date'].tolist()[0]

    for iter, row in results.iterrows():
        
        isin = row['isin']
        row = pd.DataFrame([row.to_dict()])
        data_key = f'{model_name}/{schedular.lower()}/{date}/{mode}/{isin}.csv'
        df2s3(row, vc_bucket, data_key)
    pass