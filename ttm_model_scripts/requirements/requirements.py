import platform
import subprocess
import os
from eq_common_utils.utils.logger_handler import <PERSON><PERSON><PERSON><PERSON><PERSON> as logger # type: ignore
subprocess.call(['pip', 'install','packaging'] )
import packaging.version # type: ignore
import configparser

config=configparser.ConfigParser()
properties_path=os.path.join(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'tsfm'), 'ttm.properties')
config.read(properties_path)

common_req_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'requirements.txt')
tsfm_url = config.get(section='git-repos', option='tsfm_url')
print(common_req_path)

# install third-party libraries-ttm
def install_ttm_dependencies():

    outer_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    tsfm_directory = os.path.join(outer_dir, 'tsfm')
    print(f'tsfm directory is {tsfm_directory}')

    # clone into the tsfm directory
    if os.path.exists(tsfm_directory):
        logger.info("tsfm_directory already exists in the workspace, continuing with this installation.")
    else:
        logger.info("tsfm_directory doesn't exist in the workspace, cloning into the tsfm directory")
        os.chdir(outer_dir)
        subprocess.call(['git', 'clone', '--branch', 'main', f'tsfm_url'])
    # change the directory to the tsfm directory
    os.chdir(tsfm_directory)
    subprocess.call(['pip', 'install', '.[notebooks]'])
    # change the directory to the working directory.
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    print(f'current directory is {os.getcwd()}')

def get_req_file_based_on_python_version():
    python_version = platform.python_version()
    print(python_version)
    if packaging.version.parse(python_version)<packaging.version.parse('3.10'):
        requirements_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'requirements_3.9.txt')
    elif packaging.version.parse(python_version)>=packaging.version.parse('3.10'):
        requirements_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'requirements_3.10.txt')
    print(requirements_file)
    return requirements_file
def install_dependencies():

    # install the ttm-dependencies.
    install_ttm_dependencies()

    template_requirements_file=get_req_file_based_on_python_version()

    # reading all the dependencies of django-template
    with open(template_requirements_file, 'r') as template_file:
        template_requirements = template_file.read().splitlines()

    # reading all the dependencies of the users
    with open(common_req_path, 'r') as additional_file:
        additional_requirements = additional_file.read().splitlines()

    #combining both the dependencies
    all_requirements = template_requirements + additional_requirements
    print(all_requirements)

    for requirement in all_requirements:
        subprocess.call(['pip', 'install',requirement] )
    return True

install_dependencies()
