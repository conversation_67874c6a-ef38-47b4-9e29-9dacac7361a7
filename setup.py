"""
Setup script for DS-Inference-Scripts Main Project
"""

from setuptools import setup, find_packages
import os

# Read the README file for long description
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "DS-Inference-Scripts - Data Science Model Inference Pipeline"

# Read requirements from requirements.txt
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#') and not line.startswith('shared-utils')]
    return []

setup(
    name="ds-inference-scripts",
    version="2.0.0",
    author="EqubotAI Data Science Team",
    author_email="<EMAIL>",
    description="Data Science Model Inference Pipeline for Financial Predictions",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/EqubotAI/DS-Inference-Scripts",
    packages=find_packages(exclude=["tests*", "docs*"]),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=1.0.0",
        ],
        "ml": [
            "tensorflow>=2.13.0",
            "torch>=2.0.0",
            "transformers>=4.30.0",
            "statsmodels>=0.14.0",
        ],
        "viz": [
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
            "plotly>=5.15.0",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json", "*.properties", "*.txt"],
    },
    keywords="finance, machine-learning, data-science, inference, predictions, etf, portfolio",
    project_urls={
        "Bug Reports": "https://github.com/EqubotAI/DS-Inference-Scripts/issues",
        "Source": "https://github.com/EqubotAI/DS-Inference-Scripts",
        "Documentation": "https://github.com/EqubotAI/DS-Inference-Scripts/wiki",
    },
)
