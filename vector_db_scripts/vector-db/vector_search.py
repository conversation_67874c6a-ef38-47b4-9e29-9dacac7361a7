from fastapi import <PERSON><PERSON><PERSON>, Query, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi import <PERSON>AP<PERSON>, BackgroundTasks, HTTPException, Request, Security
from fastapi.security.api_key import APIKeyHeader
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import pickle
import asyncio
import json
import time
from concurrent.futures import ThreadPoolExecutor
import signal
import json
import time
import numpy as np
import regex as re
import base64
import requests
import tldextract
from functools import lru_cache 
from datetime import datetime, timedelta, timezone, date
from typing import Dict, Set, List, Tuple, Optional, Any, Union
from elasticsearch import Elasticsearch
from requests_aws4auth import AWS4Auth
from opensearchpy import OpenSearch, RequestsHttpConnection


app = FastAPI(title="Vector Search API", version="1.0.0")

# Thread pool for running synchronous operations
executor = ThreadPoolExecutor(max_workers=10)

class SearchRequest(BaseModel):
    query: Optional[str] = Field(None, description="Search query string")
    companies: Optional[List[str]] = Field(default=[], description="List of company names to filter")
    start_date: Optional[str] = Field(None, description="Start date in YYYY-MM-DD format")
    end_date: Optional[str] = Field(None, description="End date in YYYY-MM-DD format")
    k: int = Field(default=30, ge=1, le=100, description="Number of results to return")

class SearchResult(BaseModel):
    score: float
    id: str
    date: str
    companies: List[str]
    domain: str
    content: str
    text_matches: Optional[List] = None
    processing_time: float

api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)
security = HTTPBearer()


VECTORS_SEARCH_ENDPOINT = "https://**************:8000/search"
VDB_STATUS_ENDPOINT = "https://**************:8000/status"
LOCAL_VECTOR_STORE_API_KEY = "myveryscretnotsolongcode"


def company_map_get(item):
    i = 0
    while True:
        i+=1
        if item not in COMPANY_MAP or COMPANY_MAP[item] == item:
            return item
        item = COMPANY_MAP[item]
        if i>10:break #something wrong.. there is a loop
    return item
               
with open('company_map.pkl', 'rb') as file:
    COMPANY_MAP = pickle.load(file)


def search_vectors(vector: np.ndarray = None,
                query: str = None,
                companies: List[str] = None,
                start_date: Union[str, datetime, date] = None,
                end_date: Union[str, datetime, date] = None,
                k: int = 10,
                url: str = VECTORS_SEARCH_ENDPOINT) -> dict:
    
    params = {
        'query': query,
        'k': k
    }
    if not (vector is None or not vector.any()):
        params['vector'] = base64.b64encode(vector.tobytes()).decode('ascii')
        params['shape'] = json.dumps(vector.shape)

    # Convert and add optional parameters if present
    if companies:
        params['companies'] = companies
        
    if start_date:
        params['start_date'] = date_to_string(start_date)
            
    if end_date:
        params['end_date'] = date_to_string(end_date)

    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }
    response = requests.get(url, params=params, headers=headers, verify=False)
    
    if response.status_code == 400:
        raise ValueError(response.json()['message'])
    elif response.status_code != 200:
        raise RuntimeError(f"Server error: {response.json()['message']}")
        
    return response.json()


def test_vdb(url: str = VDB_STATUS_ENDPOINT) -> dict:
    
    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }
    response = requests.get(url, headers=headers, verify=False)
    
    if response.status_code == 400:
        return False
    elif response.status_code != 200:
        return False
        
    return True

#print(1, str(test_vdb()))
#exit(1)

def date_to_string(x):
    return x if isinstance(x, str) else x.strftime('%Y-%m-%d')

def find_matches(full_text, start, chars_between, end):
    """Find all matches and their character count differences"""
    pattern = f"{re.escape(start)}(.+?){re.escape(end)}"
    matches = []
    
    for match in re.finditer(pattern, full_text, re.DOTALL):
        middle = match.group(1)
        diff = abs(len(middle) - chars_between)
        matches.append((diff, match.group(0)))
        
    if not matches:
        return ''
            
    min_diff = min(match[0] for match in matches)
    return [m for m in matches if m[0] == min_diff][0][1]


#with LN redirect from moreover
# some of these were added later so need to be cleaned from db.. 
IGNORE_DOMAIN = {
    'reporter.am',
    'etfdailynews.com',
    'theenterpriseleader.com',
    'marketbeat.com',
    'americanbankingnews.com',
    'themarketsdaily.com',
    'thelincolnianonline.com',
    'modernreaders.com',
    'tickerreport.com',
    'thestockobserver.com',
    'com-unik.info',
    'zolmax.com', 
    'transcriptdaily.com',
    'defenseworld.net'
    }


def get_domain(x):
    try:return tldextract.extract(x.strip()).registered_domain
    except:return ""

def connect_elasticsearch_aws():
    _es = None
    _es = Elasticsearch(
        ['https://search-equbot-ln-es-v2v7spplqpcsjwxg5hg3qmhajq.us-east-1.es.amazonaws.com'],
        http_auth=('equbot_es', 'Equb0t!23'),
        scheme="https",
        port=443,timeout=30, max_retries=10, retry_on_timeout=True
    )
    if _es.ping():pass#print('Yay Connect')
    else:print('Awww it could not connect!')
    return _es

def connect_openSearch():
   aws_access_key  = '********************'
   aws_secret_key = 'sjzRbkq5UFYz3Fku3NgO2Qr0r0bUvKhqYvm/jiDd'
   region='us-east-1'
   service='es'
   hosts = ['https://search-unstructured-es-ml3vahhmgvadk5gjdku5ditlzu.us-east-1.es.amazonaws.com:443']
   auth = AWS4Auth(aws_access_key, aws_secret_key, region, service)
   port = 443
   client = OpenSearch(
       hosts=hosts,
       port=port,
       http_auth=auth,
       connection_class=RequestsHttpConnection,
       timeout=50, 
       max_retries=5, 
       retry_on_timeout=True
    )
   return client

@lru_cache(maxsize = 10000) 
def fetch_documents(art_id):
    global printed

    query = {
        "query": {
            "terms": {
                "id": [art_id]
            }
        }
    }

    index='ln_index'
    results = client_es.search(index=index, body=json.dumps(query))
    year = 2025
    index2=f"quantexa_data_{year}"
    results2 = client_os.search(index=index2, body=json.dumps(query))
    #print(results2)
    documents = {}
    domains = {}
    doc_id = None
    for hit in results['hits']['hits']+results2['hits']['hits']:
        source = hit['_source']
        doc_id = source['id']
        if 'url' in source:
            domain = get_domain(source['url'])
        else:
            print('missing domain', str(source)[:333])
            domain = ''
            #continue
        if domain == 'moreover.com':
            if 'source' in source and 'homeUrl' in source['source']:
                domain = get_domain(source['source']['homeUrl'])
            elif not printed:
                printed = True
                print(source['source'])
        if domain in IGNORE_DOMAIN:
            print('skipping', domain)
            continue
        domains[doc_id] = domain
        if 'content' in source:
            content = f"{source['content']}\n\n{source.get('title','')}"
        else:
            content = f"{source['body']}\n\n{source.get('title','')}"
        documents[doc_id] = content
    try:return list(documents.values()).pop(), list(domains.values()).pop()
    except:
        print(doc_id)
        return '',''
    #return documents, domains


async def run_search_with_timeout(search_params: SearchRequest, timeout: int = 130):
    """Run the search operation with timeout"""
    
    def search_task():
        start_time = time.time()
        
        try:
            # Perform the vector search
            results = search_vectors(
                query=search_params.query,
                k=search_params.k * 2, # until i remove accidentally inserted duplicates.,
                companies=search_params.companies if search_params.companies else [],
                start_date=search_params.start_date,
                end_date=search_params.end_date
            )
            
            # Process each result
            processed_results = []
            seen = set()
            for score, metadata, vector in results['results']:
                if not metadata:
                    continue
                if metadata['lc'] not in ['eng','en']:
                    continue
                    
                if (metadata["id"], metadata["text"]) in seen:
                    continue

                # temporary fix, had accidentaly rerun for some shards. 
                seen.add((metadata["id"], metadata["text"]))

                try:
                    content, domain = fetch_documents(metadata["id"])

                    # Process text matches if available
                    if metadata.get('text'):
                        try:
                            start, chars_between, end = metadata['text'].split('|')
                            chars_between = int(chars_between)
                            text_matches = find_matches(content, start, chars_between, end)
                            
                            metadata["content"] = text_matches
                            
                            result_item = {
                                "score": float(score),
                                "metadata": metadata,
                                "vector": vector,
                            }
                            
                        except Exception as e:
                            continue
                    
                    processed_results.append(result_item)
                    
                except Exception as e:
                    continue
            
            processing_time = time.time() - start_time
            
            return {
                "status": "success",
                "results": processed_results,
                "processing_time": processing_time,
                "total_results": len(processed_results)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    # Run the search in thread pool with timeout
    try:
        loop = asyncio.get_event_loop()
        result = await asyncio.wait_for(
            loop.run_in_executor(executor, search_task),
            timeout=timeout
        )
        return result
    except asyncio.TimeoutError:
        return {
            "status": "timeout",
            "error": f"Search operation timed out after {timeout} seconds",
            "processing_time": timeout
        }

@app.get("/search")
async def simple_search(
    query: Optional[str] = Query(None, description="Search query"),
    companies: Optional[str] = Query(None, description="Comma-separated company names"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    k: int = Query(default=10, ge=1, le=100, description="Number of results"),
    timeout: int = Query(default=130, ge=5, le=160, description="Timeout in seconds")
):
    """
    Simple search endpoint with query parameters and real-time response
    """
    # Parse companies from comma-separated string
    company_list = []
    if companies:
        company_list = [c.strip() for c in companies.split(",") if c.strip()]
        company_list = [company_map_get(company) for company in company_list]
    
    search_request = SearchRequest(
        query=query,
        companies=company_list,
        start_date=start_date,
        end_date=end_date,
        k=k
    )
    
    result = await run_search_with_timeout(search_request, timeout=timeout)
    
    if result["status"] == "timeout":
        raise HTTPException(status_code=408, detail=result["error"])
    elif result["status"] == "error":
        raise HTTPException(status_code=500, detail=result["error"])
    
    return result

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        vdb_status = test_vdb()
        return {
            "status": "healthy",
            "vdb_connection": vdb_status,
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }

# Initialize connections when the app starts
@app.on_event("startup")
async def startup_event():
    global client_es, client_os, printed
    printed = False
    try:
        client_es = connect_elasticsearch_aws()
        client_os = connect_openSearch()
        print("Database connections established")
    except Exception as e:
        print(f"Failed to establish database connections: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    executor.shutdown(wait=True)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "vector_search:app", 
        host="0.0.0.0", 
        port=8372,
        ssl_keyfile="key.pem",
        ssl_certfile="cert.pem"
    )

#nohup uvicorn vector_search:app --host 0.0.0.0 --port 8372 > vapi.log 2>&1 &
#for ssl need direct python.. or pass to uvicorn ?
