#TODO fix article_id -> id mapping for all past data in db 'articles' ! then start using that table

import sys, secrets, os, numpy as np, lmdb, threading, uvicorn, signal, logging, warnings, glob, torch, queue
import base64, json, traceback, asyncio, psutil, shutil
import msgpack
import msgpack_numpy as m
import faiss
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from threading import Timer
from time import time, sleep
from enum import Enum
from typing import List, Dict, Any, Optional, Set
#from usearch.index import Index, MetricKind, ScalarKind
from pathlib import Path
from datetime import datetime, date, timedelta
from collections import OrderedDict, deque
from typing import List, Dict, Any, Optional, Set
from contextlib import asynccontextmanager
from fastapi import FastAPI, BackgroundTasks, HTTPException, Request, Security
from fastapi.security.api_key import APIKeyHeader
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from starlette.status import HTTP_403_FORBIDDEN
from pydantic import BaseModel, Field
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from sentence_transformers import SentenceTransformer
m.patch()

LOCAL_VECTOR_STORE_API_KEY = "myveryscretnotsolongcode" # implement proper env variable

api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)
security = HTTPBearer()

model = SentenceTransformer("jinaai/jina-embeddings-v3", trust_remote_code=True)

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.DEBUG)

class ThreadSafeDict:
    """Thread-safe dictionary implementation using a lock."""
    def __init__(self):
        self._dict = {}
        self._lock = threading.Lock()

    def __getitem__(self, key):
        with self._lock:
            return self._dict[key]

    def __setitem__(self, key, value):
        with self._lock:
            self._dict[key] = value

    def __delitem__(self, key):
        with self._lock:
            del self._dict[key]

    def __contains__(self, key):
        with self._lock:
            return key in self._dict
            
    def __len__(self):
        with self._lock:
            return len(self._dict)

    def get(self, key, default=None):
        with self._lock:
            return self._dict.get(key, default)

    def keys(self):
        with self._lock:
            return list(self._dict.keys())

    def values(self):
        with self._lock:
            return list(self._dict.values())

    def items(self):
        with self._lock:
            return list(self._dict.items())

    def pop(self, key, default=None):
        with self._lock:
            return self._dict.pop(key, default)

class ThreadSafeCounter:
    """Thread-safe counter with atomic increments."""
    def __init__(self, initial_value=0):
        self._value = initial_value
        self._lock = threading.Lock()

    def increment(self):
        """Atomically increment and return the new value."""
        with self._lock:
            self._value += 1
            return self._value

    def get_value(self):
        """Get the current value atomically."""
        with self._lock:
            return self._value

def convert_numpy_to_python(obj):
    if isinstance(obj, np.ndarray):
        return convert_numpy_to_python(obj.tolist())
    if isinstance(obj, (np.integer, np.int8, np.int32, np.int64, np.uint8, np.uint32, np.uint64)):
        return int(obj)
    if isinstance(obj, (np.floating, np.float64)):
        return float(obj)
    if isinstance(obj, (np.bool)):
        return bool(obj)
    if isinstance(obj, dict):
        return {key: convert_numpy_to_python(value) for key, value in obj.items()}
    if isinstance(obj, (list, tuple)):
        return [convert_numpy_to_python(item) for item in obj]
    return obj

def compress_deltas(numbers):
    if not numbers: return []
    result = [numbers[0]]
    for i in range(1, len(numbers)):
        result.append(numbers[i] - numbers[i-1])
    return result

def decompress_deltas(deltas):
    if not deltas: return []
    result = [deltas[0]]
    for i in range(1, len(deltas)):
        result.append(result[-1] + deltas[i])
    return result

def compress_ranges(numbers):
    if not numbers: return []
    ranges = []
    start = length = numbers[0]
    count = 1
    
    for num in numbers[1:]:
        if num == length + 1:
            length = num
            count += 1
        else:
            ranges.append((start, count))
            start = length = num
            count = 1
    ranges.append((start, count))
    return ranges

def decompress_ranges(ranges):
    result = []
    for start, count in ranges:
        result.extend(range(start, start + count))
    return result

def embed_sentences(sentences, task = "retrieval.query", matryushka_dim = 512):
    """ tasks: 
        retrieval.query
        retrieval.passage
        separation
        classification
        text-matching
    """
    #logger.info(f"Model device: {next(model.parameters()).device}")
    results = model.encode(sentences, normalize_embeddings=True, show_progress_bar=True, batch_size=32, device='cpu',
    convert_to_numpy=True, task=task, prompt_name=task)
    results = torch.tensor(results).cpu().detach().numpy()
    
    # Clear GPU cache
    if torch.cuda.is_available():
       torch.cuda.empty_cache()

    return results[:, :matryushka_dim]

def binary_quantize(embeddings):
    return (embeddings > 0).astype(np.uint8)

async def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)):
    logger.error(f"Auth token received: {credentials.credentials}")  # Let's see what we're getting
    if credentials.credentials != LOCAL_VECTOR_STORE_API_KEY:
        raise HTTPException(401, "Invalid token")
    return True


@dataclass
class EventMetrics:
    daily_counts: Dict[date, int] = field(default_factory=dict)
    peak_count: int = 0
    peak_insertion_date: date = None
    max_days: int = 28

    def record_activity(self, doc_date: date):
        """Record an activity for a specific date."""
        current_date = date.today()

        # Update or initialize count for the date
        if doc_date not in self.daily_counts:
            self.daily_counts[doc_date] = 0
        self.daily_counts[doc_date] += 1

        current_count = self.daily_counts[doc_date]

        # Update peak if necessary
        if current_count > self.peak_count:
            self.peak_count = current_count
            self.peak_insertion_date = current_date

        # Prune old dates
        # this has no use actually
        #self.daily_counts = {
        #    d: count for d, count in self.daily_counts.items()
        #    if (current_date - self.peak_insertion_date).days < self.max_days or d == self.peak_insertion_date
        #}

    def to_dict(self):
        return {
            'daily_counts': [(d.isoformat(), count) for d, count in self.daily_counts.items()],
            'peak_count': self.peak_count,
            'peak_insertion_date': self.peak_insertion_date.isoformat() if self.peak_insertion_date else None,
            'max_days': self.max_days
        }

    @classmethod
    def from_dict(cls, data):
        metrics = cls(max_days=data.get('max_days', 28))
        metrics.daily_counts = {
            date.fromisoformat(d): count 
            for d, count in data['daily_counts']
        }
        metrics.peak_count = data['peak_count']
        metrics.peak_insertion_date = date.fromisoformat(data['peak_insertion_date']) if data['peak_insertion_date'] else None
        return metrics
        
class ShardStatus(Enum):
    ACTIVE = "active"
    STALE = "stale"
    READ_ONLY = "read_only"

class ShardMetrics:
    def __init__(self, base_path: Path, window_days: int = 28):
        self.base_path = base_path
        self.window_days = window_days
        self.metrics_path = base_path / "shard_metrics.json"
        self._lock = threading.RLock()
        self.shard_metrics: Dict[str, EventMetrics] = {}
        self.shard_status: Dict[str, ShardStatus] = {}
        
        if not self.metrics_path.exists():
            self.save()  # This will create the file with empty dictionaries
        
        # Now load the metrics (either existing or newly created)
        self.load()

    def record_activity(self, week_key: str, doc_date: date) -> None:
        """Record activity for a shard on a specific date."""
        with self._lock:
            if week_key not in self.shard_metrics:
                self.shard_metrics[week_key] = EventMetrics()
                self.shard_status[week_key] = ShardStatus.ACTIVE

            metrics = self.shard_metrics[week_key]
            metrics.record_activity(doc_date)

            # Save metrics after update
            self.save()

    
    def check_shard_status(self, week_key: str, threshold: float = 0.10) -> ShardStatus:
        """Check if a shard should be marked as stale/read-only."""
        with self._lock:
            if week_key not in self.shard_metrics:
                return ShardStatus.ACTIVE

            metrics = self.shard_metrics[week_key]
            
            if not metrics.daily_counts:
                return ShardStatus.ACTIVE

            current_date = date.today()
            
            # If current count is low and many days have passed since peak insertion
            current_count = list(metrics.daily_counts.values())[-1] if metrics.daily_counts else 0
            days_since_peak = (current_date - metrics.peak_insertion_date).days if metrics.peak_insertion_date else 0
            
            # Check for stale status
            if (current_count < (metrics.peak_count * threshold) and days_since_peak >= self.window_days):
                if self.shard_status[week_key] != ShardStatus.READ_ONLY:
                    self.shard_status[week_key] = ShardStatus.STALE
            else:
                self.shard_status[week_key] = ShardStatus.ACTIVE

            return self.shard_status[week_key]

    def mark_shard_read_only(self, week_key: str) -> None:
        """Mark a shard as read-only."""
        with self._lock:
            self.shard_status[week_key] = ShardStatus.READ_ONLY
            self.save()

    def save(self) -> None:
        """Save metrics state to disk."""
        with self._lock:
            data = {
                'window_days': self.window_days,
                'shard_metrics': {
                    week_key: metrics.to_dict()
                    for week_key, metrics in self.shard_metrics.items()
                },
                'shard_status': {
                    week_key: status.value
                    for week_key, status in self.shard_status.items()
                }
            }
            
            # Ensure parent directory exists
            self.metrics_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Use atomic write by writing to temp file first
            temp_path = self.metrics_path.with_suffix('.tmp')
            try:
                with open(temp_path, 'w') as f:
                    json.dump(data, f)
                temp_path.replace(self.metrics_path)  # Atomic replacement
            finally:
                if temp_path.exists():
                    temp_path.unlink()  # Clean up temp file if something went wrong

    def load(self) -> None:
        """Load metrics state from disk."""
        try:
            with open(self.metrics_path, 'r') as f:
                data = json.load(f)
            
            self.window_days = data['window_days']
            self.shard_metrics = {
                week_key: EventMetrics.from_dict(metrics_data)
                for week_key, metrics_data in data['shard_metrics'].items()
            }
            self.shard_status = {
                week_key: ShardStatus(status)
                for week_key, status in data['shard_status'].items()
            }
        except Exception as e:
            logger.error(f"Failed to load metrics: {e}")
            # Initialize empty if load fails
            self.shard_metrics = {}
            self.shard_status = {}


class IndexStatus(Enum):
    READY = "ready"
    SAVING = "saving"
    LOADING = "loading"
    ERROR = "error"

def get_week_key(date_obj: date) -> str:
    """Get ISO week key for sharding."""
    return f"{date_obj.isocalendar()[0]}-{date_obj.isocalendar()[1]}"

def week_folder_vector_data_to_vector_path(path, with_file=True):
    week_folder = Path(str(path).split('vector_data/')[-1])
    new_path = Path('/vectors') / week_folder
    new_path.mkdir(parents=True, exist_ok=True)
    if with_file:
        return new_path / "vectors.faiss"
    else:
        return new_path

class WeekShard:
    def __init__(self, path: Path, vector_dim: int, metrics: ShardMetrics = None):
        self.path = path
        self.vector_dim = vector_dim
        self.current_mem = 4 * 1024 * 1024 * 1024
        self.increment_mem = 1024 * 1024 * 1024 // 2
        self._is_read_only = False
        self._lock = threading.RLock()
        self._is_loaded = False
        self.last_access_time = 0
        self.last_write_time = 0
        self.last_ntotal = 0
        self._status = IndexStatus.READY
        self._index = None
        self._env = None
        self._dbs = {}
        self._write_queue = queue.Queue(maxsize=10)
        self._shutdown_event = threading.Event()
        self._running = threading.Event()
        #self._running.set()  # Start in running state
        self._write_thread = threading.Thread(target=self._process_write_loop, daemon=True)
        #self._write_thread.start()
        self.metrics = metrics
        self._queue_backup_path = str(self.path / "queue.leftover")

    def load(self, search=False):

        if self._is_loaded:
            return
        with self._lock:
            if self._is_loaded:
                return
            try:
                logger.info(f"loading {self.path}")
                self.path.mkdir(exist_ok=True)
                st = time()
                if hasattr(self, 'metrics') and self.metrics is not None:
                    status = self.metrics.check_shard_status(self.path.name[5:])
                    self._is_read_only = status in (ShardStatus.STALE, ShardStatus.READ_ONLY)
                                
                self._status = IndexStatus.LOADING
                # Load vector index
                index_path = week_folder_vector_data_to_vector_path(self.path)

                if index_path.exists():
                    logger.info(f"loading restoring {self.path} {os.path.getsize(index_path)}")
                    self._index = faiss.read_index_binary(str(index_path))
                    logger.info(f"Read index binary successfully {index_path}")
                    logger.info(f"Total vectors: {self._index.ntotal}")
                    self._base_index = self._index.index
                else:
                    logger.info(f"loading initializing {self.path}")

                    # Create base HNSW index for 512-bit binary vectors
                    self._base_index = faiss.IndexBinaryHNSW(self.vector_dim)  # dimension in bits
                    self._base_index.hnsw.efConstruction = 40
                    self._base_index.hnsw.efSearch = 16
                    self._base_index.hnsw.M = 32
                    
                    # Wrap with IDMap for id preservation
                    self._index = faiss.IndexBinaryIDMap2(self._base_index)  # IDMap2 supports reconstruct


                self.last_ntotal = self._index.ntotal

                t = time()
                logger.info(f"loaded vectors {self.path} in {t-st}")
                st = time()
                self._env = lmdb.open(
                    str(self.path / "data.lmdb"),
                    map_size=self.current_mem,  # 4GB total
                    subdir=True,
                    map_async=True,
                    writemap=True,
                    max_dbs=4  # One for each type of data
                )

                self._dbs = {
                    'metadata': self._env.open_db(b'metadata'),
                    'companies': self._env.open_db(b'companies'),
                    'dates': self._env.open_db(b'dates'),
                    'articles': self._env.open_db(b'articles')
                }
                t = time()
                logger.info(f"loaded dbs {self.path} in {t-st}")
                self._is_loaded = True
                self._status = IndexStatus.READY
                logger.info(f"Shard loaded: {self.path}")

            except Exception as e:
                self._status = IndexStatus.ERROR
                logger.error(f"Failed to load shard {self.path}: {e}")
                raise
    
    def unload(self):
        """Unload shard from memory."""
        if not self._is_loaded:
            return
        with self._lock:
            if not self._is_loaded:
                return
            try:
                logger.info(f"stopping run/write process for {str(self.path)}")
                if self._running.is_set():
                    self._running.clear()
                if self._write_thread.is_alive():
                    self._write_thread.join(timeout=5.0)

                self.save()

                if self._env:
                    self._env.close()
                    self._env = None
                self._dbs = {}
                
                self._index = None
                self._is_loaded = False
                self._status = IndexStatus.READY
                logger.info(f"Shard unloaded: {self.path}")
                
            except Exception as e:
                logger.error(f"Failed to unload shard {self.path}: {e}")
                raise

    def add_batch(self, vectors: List[np.ndarray], metadata_list: List[Dict]):
        """Add multiple vectors to shard in a single transaction."""
        #if self._is_read_only:
        #    raise RuntimeError("Cannot add to read-only shard")
            
        if self._shutdown_event.is_set():
            raise RuntimeError("Shard is shutting down")

        if not self._write_thread.is_alive() or not self._running.is_set():
            logger.info(f"starting run/write process for {str(self.path)}")
            self._running.set()  # Reset the running flag
            self._write_thread = threading.Thread(target=self._process_write_loop, daemon=True)
            self._write_thread.start()
                    
        # Record metrics if enabled
        if self.metrics is not None:
            for metadata in metadata_list:
                doc_date = date.fromisoformat(metadata['date'])
                self.metrics.record_activity(self.path.name[5:], doc_date)

        # Generate keys for the batch
        keys = [vector_store.get_next_key() for _ in vectors]

        # Fixed number of retries instead of infinite loop
        MAX_RETRIES = 3
        retry_count = 0

        while retry_count < MAX_RETRIES:
            if self._shutdown_event.is_set():
                raise RuntimeError("Shard is shutting down")
                
            try:
                self._write_queue.put(
                    ('batch', keys, vectors, metadata_list),
                    timeout=5.0  # Longer timeout for batch operations
                )
                logger.info(f"Write batches queue successful {str(self.path)}")
                return
            except queue.Full:
                retry_count += 1
                if retry_count < MAX_RETRIES:
                    logger.warning(f"Write queue full, retry {retry_count}/{MAX_RETRIES} {str(self.path)}")
                    sleep(1.0)  # Fixed sleep between retries
                
        # If we get here, we've exhausted our retries

        logger.warning(f"Write queue full after {MAX_RETRIES} retries, writing to backup file")
        try:
            with open(self._queue_backup_path, 'a', encoding='utf-8') as f:
                # Store as JSON for easy retrieval
                encoded_vectors = []
                for v in vectors:
                    if isinstance(v, np.ndarray):
                        # Convert numpy array to base64 string for JSON storage
                        vector_bytes = v.tobytes()
                        vector_b64 = base64.b64encode(vector_bytes).decode('ascii')
                        encoded_vectors.append({
                            'b64': vector_b64,
                            'shape': v.shape,
                            'dtype': str(v.dtype)
                        })
                    else:
                        encoded_vectors.append(v)
                        
                json_data = {
                    'type': 'batch',
                    'keys': keys,
                    'vectors': encoded_vectors,
                    'metadata': metadata_list,
                    'timestamp': time()
                }
                f.write(json.dumps(json_data) + '\n')
            logger.info(f"Successfully wrote batch to backup file")
            return
        except Exception as e:
            logger.error(f"Failed to write to backup file: {e}")
            
        raise RuntimeError(f"Write queue full after {MAX_RETRIES} retries - consumer may be stuck {str(self.path)}")

    def _process_write_loop(self):
        """Internal method to process write operations."""
        #self.load()
        self.touch()
        self.touch_write()
        
        while not self._shutdown_event.is_set() and self._running.is_set():
            if self._index is None:
                break  # sigterm signal was sent.. discard queue!

            try:
                # Use timeout to check shutdown flag periodically
                item = self._write_queue.get(timeout=1.0)
                logger.info(f"Fetched from queue successful {str(self.path)}")
            except queue.Empty:
                if self._write_queue.qsize() < self._write_queue.maxsize // 2 and os.path.exists(self._queue_backup_path):
                    try:
                        # Check if there's anything to read
                        if os.path.getsize(self._queue_backup_path) > 0:
                            # Read one batch from the backup file
                            with open(self._queue_backup_path, 'r', encoding='utf-8') as f:
                                all_lines = f.readlines()

                            if all_lines:
                                # Process the first line
                                line = all_lines[0]
                                # The remaining lines (skip the first one)
                                remaining_lines = all_lines[1:]
                                
                                
                            if line:
                                json_data = json.loads(line.strip())
                                # Convert lists back to numpy arrays if needed
                                if json_data['type'] == 'batch':
                                    # Decode vectors from their base64 representation
                                    decoded_vectors = []
                                    for v_data in json_data['vectors']:
                                        # Reconstruct numpy array from base64
                                        vector_bytes = base64.b64decode(v_data['b64'].encode('ascii'))
                                        dtype = np.dtype(v_data['dtype'])
                                        shape = tuple(v_data['shape'])
                                        vector = np.frombuffer(vector_bytes, dtype=dtype).reshape(shape)
                                        decoded_vectors.append(vector)
                                    
                                    item_tuple = ('batch', json_data['keys'], decoded_vectors, json_data['metadata'])
                                    try:
                                        self._write_queue.put(item_tuple, timeout=3.0)
                                        logger.info("Successfully added item from backup file to queue")
                                    except:
                                        logger.info("Could not add item from backup file to queue")
                                        continue

                                with open(self._queue_backup_path, 'w', encoding='utf-8') as f:
                                    f.writelines(remaining_lines)

                                logger.info(f"Successfully queued one batch from backup file. {len(remaining_lines)} batches remaining.")
                            else:
                                sleep(1)
                        else:
                            sleep(1)
                    except Exception as e:
                        logger.error(f"Error processing backup file: {e}")
                        sleep(1)
                else:
                    sleep(1)
                continue

            # Handle load/unload outside the lock
            if False and self._index is None: 
                try:
                    self.load()
                    self.touch()
                    self.touch_write()
                except Exception as e:
                    logger.error(f"Failed to load index: {e}")
                    continue

            with self._lock:
                if self._index is None or self._shutdown_event.is_set():
                    # Put the item back in the queue if we're shutting down
                    if not self._shutdown_event.is_set():
                        try:
                            self._write_queue.put(item, timeout=0.5)
                        except queue.Full:
                            logger.error(f"Failed to requeue item during recovery {self.path}")
                    continue

                try:

                    st = time()
                    _, keys, vectors, metadata_list = item
                    self._index.add_with_ids(np.array(vectors, dtype=np.uint8), np.array(keys, dtype=np.uint64))
                    t = time()
                    logger.info(f"Shard {self.path} successfully inserted vectors in {t-st} seconds")
                    st = time()
                    
                    while True:
                        try:
                            with self._env.begin(write=True) as txn:
                                # Pre-collect all company and date keys that need updating
                                company_updates = defaultdict(list)
                                date_updates = defaultdict(list)
                                article_updates = defaultdict(list)

                                # First pass: collect all updates
                                for key, metadata in zip(keys, metadata_list):
                                    key_bytes = str(key).encode()
                                    # Store metadata
                                    txn.put(
                                        key_bytes,
                                        msgpack.packb(metadata),
                                        db=self._dbs['metadata']
                                    )

                                    # Collect company updates
                                    for company in metadata['companies']:
                                        company_updates[company].append(key)

                                    # Collect date updates
                                    date_updates[metadata['date']].append(key)

                                    # Collect article updates
                                    #if 'article_id' in metadata:
                                    #    article_updates[metadata['article_id']].append(key)
                                    
                                    if 'id' in metadata:
                                        article_updates[metadata['id']].append(key)

                                # Second pass: apply all updates
                                for company, new_keys in company_updates.items():
                                    company_key = company.encode()
                                    existing = txn.get(company_key, db=self._dbs['companies'])
                                    if existing:
                                        keys = decompress_deltas(msgpack.unpackb(existing))
                                        keys.extend(new_keys)
                                        txn.put(company_key, 
                                            msgpack.packb(compress_deltas(keys)), 
                                            db=self._dbs['companies'])
                                    else:
                                        txn.put(company_key, 
                                            msgpack.packb(compress_deltas(new_keys)), 
                                            db=self._dbs['companies'])

                                for date_str, new_keys in date_updates.items():
                                    date_key = date_str.encode()
                                    existing = txn.get(date_key, db=self._dbs['dates'])
                                    if existing:
                                        keys = decompress_ranges(msgpack.unpackb(existing))
                                        keys.extend(new_keys)
                                        txn.put(date_key, 
                                            msgpack.packb(compress_ranges(keys)), 
                                            db=self._dbs['dates'])
                                    else:
                                        txn.put(date_key, 
                                            msgpack.packb(compress_ranges(new_keys)), 
                                            db=self._dbs['dates'])

                                for article_id, new_keys in article_updates.items():
                                    article_key = str(article_id).encode()
                                    existing = txn.get(article_key, db=self._dbs['articles'])
                                    if existing:
                                        keys = decompress_deltas(msgpack.unpackb(existing))
                                        keys.extend(new_keys)
                                        txn.put(article_key, 
                                            msgpack.packb(compress_deltas(keys)), 
                                            db=self._dbs['articles'])
                                    else:
                                        txn.put(article_key, 
                                            msgpack.packb(compress_deltas(new_keys)), 
                                            db=self._dbs['articles'])
                            break

                        except lmdb.MapFullError as e:
                            # Release lock before unload/load cycle
                            self._lock.release()
                            self.logger.warning(f"Memory error: {e}, increasing map size and retrying...")
                            self.unload()
                            self.current_mem += self.increment_mem
                            self.load()
                            # Reacquire lock in next loop iteration
                            break

                    t = time()
                    logger.info(f"Shard {self.path} successfully inserted indexes and meta in {t-st} seconds")

                    if self._index.ntotal > self.last_ntotal + 100000:
                        self.save()
                        self.last_ntotal = self._index.ntotal
                    
                except Exception as e:
                    logger.error(f"Error processing batch: {e}")
                    # Don't requeue on error - let caller handle retry
                    sleep(1)
                    continue
        
        # Cleanup on exit
        if self._index is not None:
            self.unload()

    def get_article_vectors(self, article_id: str) -> List[int]:
        """Get all vector keys associated with an article."""
        self.load(True)
        if not self._is_loaded:
            return []
        with self._env.begin() as txn:
            data = txn.get(article_id.encode(), db=self._dbs['articles'])
            if data:
                return decompress_deltas(msgpack.unpackb(data))
            return []
    
    def _get_filtered_keys(self, start_date: date, end_date: date, companies: Optional[List[str]] = None) -> Set[int]:
        """Get keys matching date range and optional companies filter."""
        matching_keys = set()
        
        t0 = time()
        with self._env.begin() as txn:
            cursor = txn.cursor(self._dbs['dates'])
            
            start_str = start_date.isoformat().encode()
            end_str = end_date.isoformat().encode()
            
            found = cursor.set_range(start_str)
            while found:
                key, value = cursor.item()
                if key > end_str:
                    break
                    
                date_keys = decompress_ranges(msgpack.unpackb(value, raw=False))
                matching_keys.update(date_keys)
                
                found = cursor.next()

        logger.info(f"date keys {time() - t0} seconds")
        t0 = time()
        
        if not matching_keys:
            return set()
        
        # Apply company filter if provided
        if companies:
            #company_keys = set()
            company_keys = []
            with self._env.begin() as txn:
                for company in companies:
                    company_data = txn.get(company.encode(), db=self._dbs['companies'])
                    if company_data:
                        #company_keys.update(decompress_deltas(msgpack.unpackb(company_data)))
                        company_keys.append(decompress_deltas(msgpack.unpackb(company_data)))

            company_keys = set(company_keys[0]).intersection(*company_keys[1:]) if company_keys else set()
            # Intersect with date range keys
            matching_keys &= company_keys
        logger.info(f"company keys {time() - t0} seconds")
        
        # another ugly fix bcs of error in patching
        if '2025-6' in str(self.path):
            matching_keys = {num for num in matching_keys if num < 90136351 or num > 90178351}
        return matching_keys

    def search(
        self,
        query_vector: np.ndarray,
        start_date: date,  # Now using date objects and range
        end_date: date,
        top_k: int,
        companies: Optional[List[str]] = None
    ) -> List[tuple]:
        """Search within shard with date range and optional company filtering."""
        self.load(True)
        if not self._is_loaded:
            return []
        self.touch()
        
        # Get filtered keys
        filter_keys = self._get_filtered_keys(start_date, end_date, companies)
        if not filter_keys:
            return []

        results = []

        if not (query_vector is None or (isinstance(query_vector, np.ndarray) and not query_vector.any())): 
            with self._lock:
                # Search with higher limit since we'll filter
                search_k = top_k * 3
                query_vector = query_vector.reshape(1, -1)
                D, I = self._index.search(query_vector, search_k)
            
            key_scores = [(key, score) for key, score in zip(I[0], D[0]) if key in filter_keys]
        else:
            key_scores = [(key, 0) for key in filter_keys]
        #keys = np.array([key for key, score in key_scores], dtype=np.int64)
        #vectors = {key: self._index.reconstruct(int(key)) for key, score in key_scores}
        vectors = {}
        for key, score in key_scores:
            try:
                vectors[key] = self._index.reconstruct(int(key)) 
            except:
                continue
        with self._env.begin() as txn:  # Using single env
            for key, score in key_scores:
                if key not in vectors:continue
                raw_meta = txn.get(str(key).encode(), db=self._dbs['metadata'])  # Using named db
                if raw_meta:
                    metadata = msgpack.unpackb(raw_meta)
                    results.append((score, metadata, vectors[key]))
                    
                    if len(results) >= top_k:
                        break
        
        return results
    
    def search_by_article(
        self,
        article_id: str,
        date: Optional[date] = None
    ) -> List[tuple]:
        """Search for vectors by article ID with optional date filter."""
        self.load(True)
        if not self._is_loaded:
            return []
        self.touch()
        
        results = []
        with self._env.begin() as txn:
            # Get vector keys for article
            article_data = txn.get(article_id.encode(), db=self._dbs['articles'])
            if not article_data:
                return []
            
            article_keys = set(decompress_deltas(msgpack.unpackb(article_data)))
            
            # Apply date filter if provided
            if date:
                date_data = txn.get(date.isoformat().encode(), db=self._dbs['dates'])
                if date_data:
                    date_keys = set(decompress_ranges(msgpack.unpackb(date_data)))
                    article_keys &= date_keys
            
            if not article_keys:
                return []
            keys = np.array(article_keys, dtype=np.int64)
            #vectors = {key: self._index.reconstruct(int(key)) for key, score in keys}
            vectors = {}
            for key, score in keys:
                try:
                    vectors[key] = self._index.reconstruct(int(key)) 
                except:
                    continue
            # Get metadata for matching keys
            for key in article_keys:
                if key not in vectors.keys():continue
                raw_meta = txn.get(str(key).encode(), db=self._dbs['metadata'])
                if raw_meta:
                    metadata = msgpack.unpackb(raw_meta)
                    results.append((0, metadata, vectors[key]))
            
        return results

    def shutdown(self):
        """Graceful shutdown of shard."""
        logger.info(f"Shutting down shard: {self.path}")
        logger.info("Setting shutdown event")
        self._shutdown_event.set()
        
        logger.info(f"Queue size before clearing: {self._write_queue.qsize()}")
        while not self._write_queue.empty():
            try:
                item = self._write_queue.get_nowait()
                # could write to self._queue_backup_path instead of discarding! refactor to have easy way. 
                _, keys, vectors, metadata_list = item
                with open(self._queue_backup_path, 'a', encoding='utf-8') as f:
                    # Store as JSON for easy retrieval
                    encoded_vectors = []
                    for v in vectors:
                        if isinstance(v, np.ndarray):
                            # Convert numpy array to base64 string for JSON storage
                            vector_bytes = v.tobytes()
                            vector_b64 = base64.b64encode(vector_bytes).decode('ascii')
                            encoded_vectors.append({
                                'b64': vector_b64,
                                'shape': v.shape,
                                'dtype': str(v.dtype)
                            })
                        else:
                            encoded_vectors.append(v)
                            
                    json_data = {
                        'type': 'batch',
                        'keys': keys,
                        'vectors': encoded_vectors,
                        'metadata': metadata_list,
                        'timestamp': time()
                    }
                    f.write(json.dumps(json_data) + '\n')
                logger.info(f"Removing item from queue: {item}")
                self._write_queue.task_done()
            except queue.Empty:
                break
        
        logger.info(f"Queue size after clearing: {self._write_queue.qsize()}")
        
        logger.info("Saving shard")
        self.save()
        logger.info("Unloading shard")
        self.unload()

        # Additional cleanup
        self._write_queue = queue.Queue()  # Create new empty queue
        if self._write_thread.is_alive():
            self._write_thread.join(timeout=5.0)  # Wait for write thread with timeout
        logger.info(f"Shutting down shard: {self.path} done")


    def save(self):
        """Save shard state."""
        if not self._is_loaded:
            return
            
        with self._lock:
            if not self._is_loaded:
                return
            
            try:
                self._status = IndexStatus.SAVING
                # Save vector index
                if self._index:
                    index_path = week_folder_vector_data_to_vector_path(self.path)
                    faiss.write_index_binary(self._index, str(index_path))
                
                # Sync LMDB environment
                if self._env:
                    self._env.sync()
                self._status = IndexStatus.READY
                                
            except Exception as e:
                logger.error(f"Failed to save shard {self.path}: {e}")
                raise

    @property
    def is_loaded(self) -> bool:
        return self._is_loaded

    def touch(self):
        """Update last access time."""
        with self._lock:
            self.last_access_time = time()

    def touch_write(self):
        """Update last access time."""
        with self._lock:
            self.last_write_time = time()

class ShardManager:
    def __init__(
        self,
        base_path: Path,
        vector_dim: int,
        pinned_latest_weeks: int = 10
    ):
        self.base_path = base_path
        self.vector_dim = vector_dim
        self.pinned_latest_weeks = pinned_latest_weeks
        
        self._lock = threading.RLock()
        self._status = IndexStatus.LOADING

        self.metrics = ShardMetrics(base_path)

        self.loaded_shards = ThreadSafeDict()
        self.all_shards = ThreadSafeDict()
        self.pinned_shards = ThreadSafeDict()
        
        try:
            logger.info("Discovering shards")
            self._discover_shards()
            self._update_pinned_shards()
            self._status = IndexStatus.READY
        except Exception as e:
            self._status = IndexStatus.ERROR
            logger.error(f"Failed to initialize shard manager: {e}")
            raise

    def shutdown(self):
        """Graceful shutdown of all shards."""
        logger.info("Initiating shard manager shutdown...")
        
        self.metrics.save()

        # Shutdown all loaded shards
        for shard in self.loaded_shards.values():
            shard.shutdown()
    
        logger.info("Shard manager shutdown complete")
    
    def _discover_shards(self):
        """Discover existing shards on disk."""
        for path in self.base_path.glob("week_*"):
            week_key = path.name[5:]  # Remove 'week_' prefix
            self.all_shards[week_key] = WeekShard(path, self.vector_dim, self.metrics)
    
    def _get_latest_week_keys(self) -> List[str]:
        """Get the latest 8 week keys, excluding future weeks."""
        today = date.today()
        current_week = get_week_key(today)
        
        # Get all existing week keys
        existing_keys = sorted(self.all_shards.keys())
        
        # Filter out future weeks
        valid_keys = [
            key for key in existing_keys
            if self._is_week_key_past_or_present(key, current_week)
        ]
        
        # Return last 8 weeks
        return valid_keys[-self.pinned_latest_weeks:]

    def _is_week_key_past_or_present(self, week_key: str, current_week: str) -> bool:
        """Check if a week key is not in the future."""
        week_year, week_num = map(int, week_key.split('-'))
        current_year, current_week = map(int, current_week.split('-'))
        
        if week_year < current_year:
            return True
        elif week_year > current_year:
            return False
        else:
            return week_num <= current_week

    def _update_pinned_shards(self):
        """Update the set of pinned latest shards."""
        latest_keys = self._get_latest_week_keys()
        
        # Unload any no-longer-pinned shards
        current_pinned = set(self.pinned_shards.keys())
        should_be_pinned = set(latest_keys)
        
        for key in current_pinned - should_be_pinned:
            shard = self.pinned_shards.pop(key)
            status = self.metrics.check_shard_status(key)
            shard._is_read_only = status in (ShardStatus.STALE, ShardStatus.READ_ONLY)
            #if key not in self.loaded_shards:  # If not needed in LRU either
            #    shard.unload()
        self._load_pinned_shards()
            
        
    def _load_pinned_shards(self):
        """Update the set of pinned latest shards."""
        latest_keys = self._get_latest_week_keys()
            
        # Load and pin latest shards
        for key in latest_keys:
            if key not in self.pinned_shards and key in self.all_shards:
                shard = self.all_shards[key]
                shard.load()
                self.pinned_shards[key] = shard

    def _purge_accidental_shards(self, week_key_):
        """Delete least recently used unpinned shards if very few datapoints."""
        
        logger.info(f"# loaded {len(self.loaded_shards.keys())}")
        oldest_unpinned_key = None
        oldest_access_time = float('inf')
        
        for week_key in self.loaded_shards.keys():
            if week_key == week_key_:continue
            if week_key in self.pinned_shards.keys():
                continue
            today = date.today()
            if week_key == get_week_key(today):
                continue
            tomorrow = today + timedelta(days=1) # just making sure it covers other time zones
            if week_key == get_week_key(tomorrow):
                continue
                
            shard = self.loaded_shards[week_key]
            if os.path.exists(week_folder_vector_data_to_vector_path(shard.path)) and os.path.getsize(week_folder_vector_data_to_vector_path(shard.path)) > 50000:
                continue
            if os.path.exists(week_folder_vector_data_to_vector_path(shard.path)) and shard._index.ntotal > 300:
                continue
            
            if shard.last_write_time < oldest_access_time and (time() - shard.last_write_time) > 6*3600:
                oldest_access_time = shard.last_write_time
                oldest_unpinned_key = week_key
        
        if oldest_unpinned_key is None:
            return
        
        shard = self.loaded_shards.pop(oldest_unpinned_key)

        # TODO: refactor this
        logger.info(f"removing accidents {oldest_unpinned_key}")

        shard.last_access_time = 0
        shard.last_write_time = 0
        shard.last_ntotal = 0
        self.metrics.shard_metrics[oldest_unpinned_key] = EventMetrics()
        shard.unload()
        shutil.rmtree(str(shard.path))
        shutil.rmtree(str(week_folder_vector_data_to_vector_path(shard.path, False)))

    def _evict_stale_shards(self, week_key_):
        """Unload least recently used unpinned shards if we're over limit."""
        # Calculate how many unpinned shards we can have
        
        ram = psutil.virtual_memory()
        total_ram = ram.total
        available_ram = ram.free
        used_ram = total_ram - available_ram

        ram_threshold = int(total_ram // 4)
        
        max_loaded_shards = max(20, int(ram_threshold // (2 * 1024 * 1024 * 1024)))  # 2GB per shard keep half in ram, else in virtual

        logger.info(f"max loaded shards {max_loaded_shards} {ram_threshold} {used_ram}")

        # If we're within the limit, no need to evict
        if used_ram < ram_threshold and len(self.loaded_shards) < max_loaded_shards:
            return
        
        # Find the oldest unpinned shard to evict
        while used_ram > ram_threshold and len(self.loaded_shards) > max_loaded_shards:
            logger.info(f"# loaded {len(self.loaded_shards.keys())}")
            # Find the oldest unpinned shard
            oldest_unpinned_key = None
            oldest_access_time = float('inf')
            
            for week_key in self.loaded_shards.keys():
                if week_key == week_key_:continue
                # Skip pinned shards
                if week_key in self.pinned_shards.keys():
                    continue
                
                # Get the shard and its last access time
                shard = self.loaded_shards[week_key]
                
                # Compare last access times
                if shard.last_access_time < oldest_access_time:
                    oldest_access_time = shard.last_access_time
                    oldest_unpinned_key = week_key
            
            # If no unpinned shard found, break the loop
            if oldest_unpinned_key is None:
                break

            #if time() - oldest_access_time < 60: # one minute keep at least.. wide burst can knock down the system :(
            #    break
            
            # Unload the oldest unpinned shard
            shard = self.loaded_shards.pop(oldest_unpinned_key)
            shard.unload()
            
    def get_shard(self, week_key: str, load: bool = True, search=False) -> WeekShard:
        """Get shard by week key, optionally loading it."""
        # Create if doesn't exist
        if week_key not in self.all_shards:
            shard_path = self.base_path / f"week_{week_key}"
            shard_path.mkdir(exist_ok=True)
            self.all_shards[week_key] = WeekShard(shard_path, self.vector_dim, self.metrics)

        shard = self.all_shards[week_key]
        
        if load:
            
            # If pinned, just return it
            if week_key in self.pinned_shards:
                shard.touch()
                return self.pinned_shards[week_key]
            
            # If not in loaded shards, load it
            if week_key not in self.loaded_shards:
                shard.load(search)
                if shard._is_loaded:
                    self.loaded_shards[week_key] = shard
            
            if shard._is_loaded:
                shard.touch()
                self._update_pinned_shards()  # Might be a new latest shard
                self._purge_accidental_shards(week_key)
                self._evict_stale_shards(week_key) 
        # free RAM should always be > 8*1GB + extra, so 10GB, so 10%. then i can do evict after updating pinned, not always

        
        return shard

class ShardedVectorStore:
    def __init__(
        self,
        base_path: str,
        vector_dim: int = 512,
        binary_quantize=None,
        embed_sentences=None
    ):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        self.vector_dim = vector_dim
        self.binary_quantize = binary_quantize
        self.embed_sentences = embed_sentences

        self._status = IndexStatus.LOADING
        self._shutdown_event = threading.Event()
        
        self._key_counter_lock = threading.Lock()
        self._key_counter = 0
        
        # Try to load last used key from disk
        counter_file = self.base_path / "key_counter.txt"
        if counter_file.exists():
            try:
                with open(counter_file, 'r') as f:
                    self._key_counter = int(f.read().strip())
            except Exception as e:
                logger.error(f"Failed to load key counter: {e}")
        
        try:
            # Initialize shard manager
            logger.info('Initializing ShardManager')
            self.shard_manager = ShardManager(
                self.base_path,
                vector_dim,
                pinned_latest_weeks=8
            )
            
            # Register shutdown handlers
            signal.signal(signal.SIGINT, self._handle_shutdown)
            signal.signal(signal.SIGTERM, self._handle_shutdown)
            
            self._status = IndexStatus.READY
            
        except Exception as e:
            self._status = IndexStatus.ERROR
            logger.error(f"Failed to initialize vector store: {e}")
            raise

    def _init_search_worker(self):
        """Initialize thread-local storage for search workers."""
        thread = threading.current_thread()
        thread.active_searches = 0


    def get_next_key(self) -> int:
        """Get next unique key in a thread-safe manner."""
        with self._key_counter_lock:
            self._key_counter += 1
            
            # Save counter to disk periodically (every 1000 keys)
            if self._key_counter % 1000 == 0:
                try:
                    with open(self.base_path / "key_counter.txt", 'w') as f:
                        f.write(str(self._key_counter))
                except Exception as e:
                    logger.error(f"Failed to save key counter: {e}")
            
            return self._key_counter

    def add_batch(self, vectors, metadata_list: List[Dict]):
        """Add batch of documents with optimized transactions."""
        # Group by week
        week_groups = defaultdict(list)

        # tmp fix! won't be needed in the future. 
        ignore_folders = [
            '2025-1',
            '2025-2',
            '2025-3',
            '2025-4',
            '2025-5',
            '2025-6',
            '2025-7',
            '2025-8'
        ]

        ignore_folders = []

        for i, metadata in enumerate(metadata_list):
            doc_date = date.fromisoformat(metadata['date'])
            week_key = get_week_key(doc_date)
            if week_key in ignore_folders:continue
            week_groups[week_key].append((i, metadata))

        logger.info(f"adding batches for {', '.join(week_groups.keys())}")
        # Process each week's batch
        for week_key, items in week_groups.items():
            shard = self.shard_manager.get_shard(week_key)
            shard.add_batch([vectors[i] for i, _ in items], 
                        [metadata for _, metadata in items])

    def search(
        self,
        query_text: str,
        query_vector: np.ndarray,
        start_date: date,
        end_date: date,
        companies: Optional[List[str]] = None,
        top_k: int = 10
    ) -> List[Dict]:
        """Thread-safe search across relevant shards."""        
        if self._shutdown_event.is_set():
            raise RuntimeError("System is shutting down")

        if self._status != IndexStatus.READY:
            raise RuntimeError("System not ready for search")
        """Search across relevant shards for given date range."""
        # Generate query vector
        if (query_vector is None or (isinstance(query_vector, np.ndarray) and not query_vector.any())) and query_text:
            start_time = time()
            query_embedding = self.embed_sentences([query_text])
            query_vector = np.packbits(self.binary_quantize(query_embedding), axis=1)[0]#.reshape(1, -1)
            end_time = time()
            logger.info(f"embed time: {end_time - start_time}")
        logger.info("getting relevant shards")
        # Get all relevant shards for date range
        relevant_shards = self._get_shards_for_date_range(start_date, end_date)
        #logger.info(f"Got {relevant_shards}")
        if not relevant_shards:
            return []
            
        start_time = time()
        # Collect and merge results

        if True:
            all_results = []
            for shard in relevant_shards:
                try:
                    t0 = time()
                    shard_start = max(start_date, self._get_shard_start_date(shard))
                    shard_end = min(end_date, self._get_shard_end_date(shard))
                    logger.info(f"shard start end dates {shard}: {shard_start}, {shard_end}")
                    
                    results = shard.search(query_vector, shard_start, shard_end, top_k, companies)
                    all_results.extend(results)
                    logger.info(f"shard {str(shard.path)} {time() - t0} seconds")

                except Exception as e:
                    logger.error(f"Shard search failed: {e}")
                    logger.error(traceback.format_exc())
                    continue
        else:
            # parallelized shards search; not faster ??? 
            def search_shard(shard, query_vector, start_date, end_date, top_k, companies):
                try:
                    shard_start = max(start_date, self._get_shard_start_date(shard))
                    shard_end = min(end_date, self._get_shard_end_date(shard))
                    logger.info(f"shard start end dates {shard}: {shard_start}, {shard_end}")
                    return shard.search(query_vector, shard_start, shard_end, top_k, companies)
                except Exception as e:
                    logger.error(f"Shard search failed: {e}")
                    logger.error(traceback.format_exc())
                    return []

            all_results = []
            with ThreadPoolExecutor() as executor:
                futures = [
                    executor.submit(search_shard, shard, query_vector, start_date, end_date, top_k, companies)
                    for shard in relevant_shards
                ]
                
                for future in as_completed(futures):
                    try:
                        results = future.result()
                        all_results.extend(results)
                    except Exception as e:
                        logger.error(f"Error collecting results: {e}")
                        continue
                
        end_time = time()
        logger.info(f"search time: {end_time - start_time}")
        # Sort by score and return top_k
        return sorted(all_results, key=lambda x: x[0])[:top_k]

    def search_by_article(
        self,
        article_id: str,
        on_date: Optional[date] = None
    ) -> List[Dict]:
        """Search for article across all relevant shards."""
        all_results = []
        
        # If date is provided, only search that week's shard
        start_time = time()
        if on_date:
            week_key = get_week_key(on_date)
            if week_key in self.shard_manager.all_shards:
                shard = self.shard_manager.get_shard(week_key, search=True)
                results = shard.search_by_article(article_id, on_date)
                all_results.extend(results)
        else:
            # Search all shards
            for shard in self.shard_manager.all_shards.values():
                results = shard.search_by_article(article_id, None)
                all_results.extend(results)
        end_time = time()
        logger.info(f"article search time: {end_time - start_time}")
        # Return metadata only
        return [metadata for _, metadata in all_results]
    
    def _get_shards_for_date_range(self, start_date: date, end_date: date) -> List[WeekShard]:
        """Get all shards that might contain documents in date range."""
        relevant_shards = []
        current_date = start_date
        
        while current_date <= end_date:
            week_key = get_week_key(current_date)

            if week_key in self.shard_manager.all_shards:
                shard = self.shard_manager.get_shard(week_key, search=True)
                if shard not in relevant_shards:  # Avoid duplicates
                    relevant_shards.append(shard)
            current_date += timedelta(days=7)
        
        return relevant_shards

    def _get_shard_start_date(self, shard: WeekShard) -> date:
        """Get the start date of a shard's week."""
        week_key = shard.path.name[5:]  # Remove 'week_' prefix
        year, week = map(int, week_key.split('-'))
        # Get the Monday of that week
        jan1 = date(year, 1, 1)
        # If Jan 1 is not Monday, back up to previous Monday
        monday = jan1 + timedelta(days=-jan1.weekday(), weeks=week-1)
        return monday

    def _get_shard_end_date(self, shard: WeekShard) -> date:
        """Get the end date of a shard's week."""
        return self._get_shard_start_date(shard) + timedelta(days=6)
  
    def _handle_shutdown(self, signum, frame):
        """Handle shutdown signals."""
        logger.info("Received shutdown signal...")
        self._shutdown_event.set()
        
        try:
            with open(self.base_path / "key_counter.txt", 'w') as f:
                f.write(str(self._key_counter))
        except Exception as e:
            logger.error(f"Failed to save final key counter state: {e}")
        
        # Shutdown shard manager
        self.shard_manager.shutdown()
                
        logger.info("Shutdown complete")
        sys.exit(0)

    def get_status(self) -> Dict[str, Any]:
        """Get system status."""
        return {
            "status": self._status.value,
            "total_shards": len(self.shard_manager.all_shards),
            "loaded_shards": len(self.shard_manager.loaded_shards),
            "pinned_shards": len(self.shard_manager.pinned_shards),
            "vector_dim": self.vector_dim
        }

def decode_vector(b64_str: str, shape_str: str) -> np.ndarray:
    """Decode base64 vector and reshape."""
    vector_bytes = base64.b64decode(b64_str)
    shape = json.loads(shape_str)
    return np.frombuffer(vector_bytes, dtype=np.uint8).reshape(shape)


@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        global vector_store, vector_worker_thread
        # Note: binary_quantize and embed_sentences functions should be passed here
        vector_store = ShardedVectorStore(
            base_path="vector_data",
            vector_dim=512,
            binary_quantize=binary_quantize,
            embed_sentences=embed_sentences
        )
        # Start worker thread
        vector_worker_thread = threading.Thread(target=process_queue_items, daemon=True)
        vector_worker_thread.start()

        yield
        
    finally:
        logger.info("Waiting for queue to empty...")
        vector_queue.join()  # Wait for all tasks to complete
        
        # Stop worker
        vector_queue.put((None, None))  # Poison pill
        
        if vector_worker_thread.is_alive():
            vector_worker_thread.join()

        if vector_store:
            vector_store._handle_shutdown(None, None)
        pass

vector_queue = queue.Queue(maxsize=10)  # Limit queue size
vector_worker_thread = None

def process_queue_items():
    """Background worker to process queued items."""
    logger.info("Vector worker thread starting...") # Will show during app startup
    while True:
        try:
            vectors, metadata = vector_queue.get()
            if vectors is None:  # Poison pill
                logger.info("Vector worker received shutdown")
                vector_queue.task_done()
                break
                
            logger.info(f"Processing vector batch: shape={vectors.shape}, metadata count={len(metadata)}")
            st = time()
            vector_store.add_batch(vectors, metadata)
            t = time()
            logger.info(f"Successfully processed queue batch {len(vectors)} vectors {t - st} seconds")
                
            vector_queue.task_done()
        except Exception as e:
            logger.error(f"Error processing queue item: {e}")
            logger.error(traceback.format_exc())
            vector_queue.task_done()

            sleep(0.2)

app = FastAPI(lifespan=lifespan)
vector_store: Optional[ShardedVectorStore] = None

@app.get("/status")
async def get_status(authorized: bool = Security(security)):
    return vector_store.get_status()

@app.post("/post")
async def add_vectors(request: Request, credentials: HTTPAuthorizationCredentials = Security(security)):
    try:
        try:
            body = await request.json()
        except Exception as e:
            logger.error("JSON Parse Error:")
            logger.error(traceback.format_exc())
            raise HTTPException(422, str(e))
            
        # Manual validation
        if not all(k in body for k in ['shape', 'vectors', 'metadata']):
            raise HTTPException(422, "Missing required fields: shape, vectors, metadata")

        try:
            shape = body['shape']
            vectors_b64 = body['vectors']
            metadata = body.get('metadata', [])
            
            vector_data = base64.b64decode(vectors_b64.encode('ascii'))
            vectors = np.frombuffer(vector_data, dtype=np.uint8).reshape(shape)
            
        except Exception as e:
            logger.error("Vector Processing Error:")
            logger.error(traceback.format_exc())
            raise HTTPException(422, str(e))
            
        try:
            # If queue is full, return 429 (Too Many Requests)
            try:
                vector_queue.put((vectors, metadata), block=False)
            except queue.Full:
                raise HTTPException(status_code=429, detail="Server is busy. Please try again later.")
            
            return {"status": "success"}
        
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Unexpected Error:")
            logger.error(traceback.format_exc())
            raise HTTPException(500, str(e))
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Unexpected Error:")
        logger.error(traceback.format_exc())
        raise HTTPException(500, str(e))

@app.get("/search")
async def search_vectors(request: Request, authorized: bool = Security(security)):
    if vector_store._status != IndexStatus.READY:
        raise HTTPException(503, "System not ready")
    
    try:
        params = request.query_params

        vector = params.get('vector')
        shape = params.get('shape')
        if vector and shape:
            vector = decode_vector(vector, shape)
            
        search_params = {
            'query': params.get('query'),
            'vector': vector,
            'companies': params.getlist('companies') if params.getlist('companies') else [],
            'start_date': date.fromisoformat(params.get('start_date')) if params.get('start_date') else None,
            'end_date': date.fromisoformat(params.get('end_date')) if params.get('end_date') else None,
            'k': int(params.get('k', 10))
        }

        if search_params['start_date'] is None or search_params['end_date'] is None:
            raise HTTPException(422, "Missing required fields: start_date, end_date")
        
        logger.info(f"params: {search_params}")
        # Direct search without queue
        results = vector_store.search(
            query_text=search_params['query'],
            query_vector=search_params['vector'],
            start_date=search_params['start_date'],
            end_date=search_params['end_date'],
            companies=search_params['companies'],
            top_k=search_params['k']
        )
        
        logger.info(f"Search snippet {str(results)[:333]}")
        return {"results": convert_numpy_to_python(results)}
            
    except Exception as e:
        logger.error("Search Error:")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/article") 
async def search_article(request: Request, authorized: bool = Security(security)):
    if vector_store._status != IndexStatus.READY:
        raise HTTPException(503, "System not ready")
    
    try:
        params = request.query_params
        article_id = params.get('article_id')
        if not article_id:
            raise HTTPException(400, "article_id is required")
            
        search_params = {
            'article_id': article_id,
            'on_date': date.fromisoformat(params.get('on_date')) if params.get('on_date') else None
        }
        
        if search_params['on_date'] is None:
            raise HTTPException(422, "Missing required fields: on_date")

        # Direct article search 
        results = vector_store.search_by_article(
            article_id=search_params['article_id'],
            on_date=search_params['on_date']
        )
        
        logger.info(f"Search snippet article {str(results)[:333]}")
        return {"results": convert_numpy_to_python(results)}
            
    except Exception as e:
        logger.error("Article Error:")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "vdb_app:app", 
        host="0.0.0.0", 
        port=8000,
        ssl_keyfile="key.pem",
        ssl_certfile="cert.pem"
    )
