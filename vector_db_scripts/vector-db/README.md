Vector db that integrates Faiss vector store and LMDB for date and company indexes / filters.


`vector_sentiments.py` is example script fetching sector/topic related data and doing sentiment analysis. 


`vdb_client.py` is example usage for search. Note that search also has to fetch documents from elastic/open search and recreate text from stored fragments. This was necessary so not to store same terrabytes of information again. Do NOT search too wide intervals as it will be loading many shards at once! One shard is 1 week of data.. depending on the boundary, 1 week interval opens 1 or 2 shars.. 1 month opens 5 or 6. 
