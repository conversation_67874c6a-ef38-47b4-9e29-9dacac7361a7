import requests
import json
import time
import warnings
from typing import Optional, List
from fastapi import FastAPI, BackgroundTasks, HTTPException, Request, Security
warnings.filterwarnings("ignore")

LOCAL_VECTOR_STORE_API_KEY = "myveryscretnotsolongcode"

class VectorSearchClient:
    def __init__(self, base_url: str = "https://167.235.88.209:8372/"):
        self.base_url = base_url
        
    def test_health(self):
        """Test if the API is running"""
        try:
            response = requests.get(f"{self.base_url}/health", verify=False)
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"Connection failed: {e}"}
    
    def search_simple(self, query: str, companies: Optional[List[str]] = [], 
                     start_date: Optional[str] = None, end_date: Optional[str] = None, 
                     k: int = 10, timeout: int = 130):
        """Simple GET search"""
        params = {
            "query": query,
            "k": k,
            "timeout": timeout
        }
        
        if companies:
            params["companies"] = ",".join(companies)
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date
            
        headers = {
            'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
        }
        
        try:
            response = requests.get(f"{self.base_url}/search", params=params, headers=headers, timeout=35, verify=False)
            return response.json()
        except requests.exceptions.Timeout:
            return {"error": "Request timed out"}
        except requests.exceptions.RequestException as e:
            return {"error": f"Request failed: {e}"}
    
def main():
    # Initialize client
    client = VectorSearchClient()
    
    print("🚀 Testing Vector Search API")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n1. Health Check:")
    health = client.test_health()
    print(json.dumps(health, indent=2))
    
    if "error" in health:
        print("❌ API is not running. Start it with: uvicorn main:app --reload")
        return
    
    # Test 2: Simple search
    print("\n2. Simple Search (Apple stock):")
    result = client.search_simple(
        query="Trump tarrifs",
        start_date="2025-05-10",
        #companies=["Apple Inc", "Microsoft"],
        end_date="2025-05-13",
        k=10 # keep this few times higher than desired number results! cleanup is happening real time
    )
    
    print(result)
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        print(f"✅ Found {result.get('total_results', 0)} results")
        print(f"⏱️ Processing time: {result.get('processing_time', 0):.2f}s")
        
        # Show first result
        if result.get('results'):
            first_result = result['results'][0]
            print(f"\nFirst result:")
            print(f"  Score: {first_result.get('score', 0):.4f}")
            print(f"  Date: {first_result.get('date', 'N/A')}")
            print(f"  Domain: {first_result.get('domain', 'N/A')}")
            print(f"  Preview: {first_result.get('content', 'N/A')[:100]}...")
        
    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        print(f"✅ Found {result.get('total_results', 0)} results")
        print(f"⏱️ Processing time: {result.get('processing_time', 0):.2f}s")

if __name__ == "__main__":
    main()
