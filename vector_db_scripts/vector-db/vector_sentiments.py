import base64, numpy as np, time, json, regex as re, base64, requests, csv, os
import matplotlib.pyplot as plt, pickle, spacy, nltk, ahocorasick, tldextract, json, pickle
import warnings
from typing import Optional, List
warnings.filterwarnings('ignore')
from functools import lru_cache 
from sklearn.linear_model import LinearRegression
from langdetect import detect
from elasticsearch import Elasticsearch
from opensearchpy import OpenSearch, RequestsHttpConnection
from requests_aws4auth import AWS4Auth
from collections import defaultdict
from sklearn.metrics import r2_score
from datetime import datetime, timedelta, date
from typing import List, Union
import msgpack_numpy as m
m.patch()
nltk.download('wordnet')
nltk.download('vader_lexicon')
nltk.download('stopwords')
nltk.download('punkt')
from nltk.sentiment.vader import SentimentIntensityAnalyzer

from vdb_client import VectorSearchClient

client = VectorSearchClient()
sid = SentimentIntensityAnalyzer()

def analyze_sentiment(texts):
    """
    Analyze financial sentiment in a collection of texts using VADER sentiment analysis
    Args:
        texts: List of text paragraphs to analyze
    Returns:
        Dictionary with sentiment analysis results including compound score and counts of positive, negative, neutral texts
    """
    if not texts:
        return {'score': 0, 'positive': 0, 'negative': 0, 'neutral': 0, 'total': 0}

    positive_count = 0
    negative_count = 0
    neutral_count = 0
    compound_scores = []

    for text in texts:
        scores = sid.polarity_scores(text)
        compound_scores.append(scores['compound'])
        if scores['compound'] >= 0.05:
            positive_count += 1
        elif scores['compound'] <= -0.05:
            negative_count += 1
        else:
            neutral_count += 1

    avg_compound = sum(compound_scores) / len(compound_scores) if compound_scores else 0

    return {
        'score': avg_compound,
        'positive': positive_count,
        'negative': negative_count,
        'neutral': neutral_count,
        'total': len(texts)
    }

def generate_investment_recommendations(sector, window_analysis, company_predictions, sector_prediction):
    """Generate investment recommendations based on sentiment analysis and predictions"""
    recommendations = {
        'sector_outlook': {},
        'buy_recommendations': [],
        'sell_recommendations': [],
        'hold_recommendations': [],
        'watchlist': []
    }
    
    # Sector outlook
    if sector_prediction:
        if sector_prediction['trend'] == 'increasing' and sector_prediction['confidence'] > 0.7:
            outlook = 'positive'
            action = 'overweight'
        elif sector_prediction['trend'] == 'decreasing' and sector_prediction['confidence'] > 0.7:
            outlook = 'negative'
            action = 'underweight'
        else:
            outlook = 'neutral'
            action = 'market weight'
        
        recommendations['sector_outlook'] = {
            'sector': sector,
            'outlook': outlook,
            'action': action,
            'confidence': sector_prediction['confidence'],
            'reasoning': f"Sentiment analysis predicts a {sector_prediction['trend']} trend with {sector_prediction['confidence']:.2f} confidence"
        }
    
    # Company recommendations
    for company, prediction in company_predictions.items():
        # Skip companies with low confidence predictions
        if prediction['confidence'] < 0.6:
            recommendations['watchlist'].append({
                'company': company,
                'reason': f"Insufficient confidence ({prediction['confidence']:.2f}) for a strong recommendation"
            })
            continue
        
        # Calculate average predicted sentiment
        avg_predicted_sentiment = sum(prediction['sentiment_predictions']) / len(prediction['sentiment_predictions'])
        
        # Generate recommendation
        if prediction['trend'] == 'increasing' and prediction['confidence'] > 0.7:
            recommendations['buy_recommendations'].append({
                'company': company,
                'confidence': prediction['confidence'],
                'predicted_sentiment': avg_predicted_sentiment,
                'reasoning': f"Strong positive sentiment trend with slope of {prediction['slope']:.4f}"
            })
        elif prediction['trend'] == 'decreasing' and prediction['confidence'] > 0.7:
            recommendations['sell_recommendations'].append({
                'company': company,
                'confidence': prediction['confidence'],
                'predicted_sentiment': avg_predicted_sentiment,
                'reasoning': f"Strong negative sentiment trend with slope of {prediction['slope']:.4f}"
            })
        else:
            recommendations['hold_recommendations'].append({
                'company': company,
                'confidence': prediction['confidence'],
                'predicted_sentiment': avg_predicted_sentiment,
                'reasoning': f"Stable sentiment trend with slope of {prediction['slope']:.4f}"
            })
    
    # Sort recommendations by confidence
    recommendations['buy_recommendations'].sort(key=lambda x: x['confidence'], reverse=True)
    recommendations['sell_recommendations'].sort(key=lambda x: x['confidence'], reverse=True)
    
    return recommendations

def generate_financial_report(analysis_results, format='text'):
    """Generate a formatted financial report from rolling window analysis results"""
    sector = analysis_results['sector']
    topic = analysis_results['topic']
    recommendations = analysis_results['recommendations']
    
    if format == 'text':
        report = f"FINANCIAL ANALYSIS REPORT: {sector} SECTOR - {topic}\n"
        report += f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Analysis period: {analysis_results['analysis_period']['start']} to {analysis_results['analysis_period']['end']}\n"
        report += f"30-Day Prediction Period: {datetime.strptime(analysis_results['analysis_period']['end'], '%Y-%m-%d') + timedelta(days=1):%Y-%m-%d} to {datetime.strptime(analysis_results['analysis_period']['end'], '%Y-%m-%d') + timedelta(days=30):%Y-%m-%d}\n\n"
        
        # Sector outlook
        if 'sector_outlook' in recommendations and recommendations['sector_outlook']:
            outlook = recommendations['sector_outlook']
            report += "SECTOR OUTLOOK\n"
            report += f"Outlook: {outlook['outlook'].upper()}\n"
            report += f"Recommended action: {outlook['action'].upper()}\n"
            report += f"Confidence: {outlook['confidence']:.2f}\n"
            report += f"Reasoning: {outlook['reasoning']}\n\n"
        
        # Buy recommendations
        report += "BUY RECOMMENDATIONS\n"
        if recommendations['buy_recommendations']:
            for i, rec in enumerate(recommendations['buy_recommendations'], 1):
                report += f"{i}. {rec['company']} (Confidence: {rec['confidence']:.2f})\n"
                report += f"   Predicted sentiment: {rec['predicted_sentiment']:.2f}\n"
                report += f"   Reasoning: {rec['reasoning']}\n"
        else:
            report += "No strong buy recommendations at this time.\n"
        report += "\n"
        
        # Sell recommendations
        report += "SELL RECOMMENDATIONS\n"
        if recommendations['sell_recommendations']:
            for i, rec in enumerate(recommendations['sell_recommendations'], 1):
                report += f"{i}. {rec['company']} (Confidence: {rec['confidence']:.2f})\n"
                report += f"   Predicted sentiment: {rec['predicted_sentiment']:.2f}\n"
                report += f"   Reasoning: {rec['reasoning']}\n"
        else:
            report += "No strong sell recommendations at this time.\n"
        report += "\n"
        
        # Hold recommendations
        report += "HOLD RECOMMENDATIONS\n"
        if recommendations['hold_recommendations']:
            for i, rec in enumerate(recommendations['hold_recommendations'], 1):
                report += f"{i}. {rec['company']} (Confidence: {rec['confidence']:.2f})\n"
                report += f"   Predicted sentiment: {rec['predicted_sentiment']:.2f}\n"
                report += f"   Reasoning: {rec['reasoning']}\n"
        else:
            report += "No hold recommendations at this time.\n"
        report += "\n"
        
        # Watchlist
        report += "WATCHLIST (Insufficient data for strong recommendation)\n"
        if recommendations['watchlist']:
            for i, item in enumerate(recommendations['watchlist'], 1):
                report += f"{i}. {item['company']} - {item['reason']}\n"
        else:
            report += "No companies on watchlist.\n"
        
        return report
    
    elif format == 'dict':
        return analysis_results
    
    else:
        raise ValueError(f"Unsupported format: {format}")

class SentimentCache:
    def __init__(self):
        self.cache = {}  # date -> texts mapping

    def add_texts(self, date, texts):
        """Add or update texts for a specific date"""
        self.cache[date] = texts

    def remove_date(self, date):
        """Remove data for a specific date"""
        if date in self.cache:
            del self.cache[date]

    def get_texts(self, date):
        """Get texts for a specific date"""
        return self.cache.get(date, [])

    def get_all_dates(self):
        """Get a list of all cached dates"""
        return sorted(self.cache.keys())

    def clear(self):
        """Clear the entire cache"""
        self.cache.clear()

class DifferentialSentimentAnalyzer:
    def __init__(self, analyze_sentiment_func):
        """
        Initialize with a function that analyzes sentiment for a list of texts
        
        Args:
            analyze_sentiment_func: Function that takes a list of texts and returns sentiment analysis
        """
        self.analyze_sentiment = analyze_sentiment_func
        self.current_dates = set()  # Set of dates currently in the cache
        self.current_sentiment_score = 0.0  # Running total of sentiment scores
        self.date_sentiments = {}  # Mapping of date to sentiment analysis results
        self.company_date_sentiments = defaultdict(dict)  # Company -> date -> sentiment

    def update_cache(self, new_cache):
        """
        Update the sentiment analysis based on changes to the cache
        
        Args:
            new_cache: Dictionary mapping dates to lists of texts
        """
        new_dates = set(new_cache.keys())
        added_dates = new_dates - self.current_dates
        removed_dates = self.current_dates - new_dates
        
        # Process removed dates first
        for date in removed_dates:
            if date in self.date_sentiments:
                self.current_sentiment_score -= self.date_sentiments[date]['score']
                del self.date_sentiments[date]
        
        # Process added dates
        for date in added_dates:
            texts = new_cache[date]
            if texts:  # Only analyze if we have texts
                sentiment = self.analyze_sentiment(texts)
                self.date_sentiments[date] = sentiment
                self.current_sentiment_score += sentiment['score']
        
        self.current_dates = new_dates
    
    def update_company_sentiment(self, company, date, texts):
        """Update sentiment for a specific company on a specific date"""
        if texts:
            sentiment = self.analyze_sentiment(texts)
            self.company_date_sentiments[company][date] = sentiment
    
    def get_average_sentiment(self):
        """Get the average sentiment score across all dates"""
        if not self.date_sentiments:
            return 0.0
        return self.current_sentiment_score / len(self.date_sentiments)
    
    def get_company_sentiments(self, company):
        """Get all sentiment data for a specific company"""
        return self.company_date_sentiments.get(company, {})

def rolling_window_analysis(sector, topic, end_date, window_size, prediction_days, lookback_days, queries):
    """
    Perform rolling window analysis with caching for efficiency
    
    Args:
        sector: Industry sector to analyze
        topic: Specific topic or theme to focus on
        end_date: End date for analysis (defaults to today)
        window_size: Size of each window in days (default: 30)
        prediction_days: Number of days to predict forward (default: 30)
        lookback_days: Total historical period to analyze (default: 90)
    
    Returns:
        Dictionary with analysis results and predictions
    """
    # Set default end date to today if not specified
    if end_date is None:
        end_date = (datetime.now()- timedelta(days=30)).strftime("%Y-%m-%d") 
    
    # Convert to datetime for calculations
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    start_dt = end_dt - timedelta(days=lookback_days)
    
    # Generate all dates in the analysis period
    all_dates = []
    current_dt = start_dt
    while current_dt <= end_dt:
        all_dates.append(current_dt.strftime("%Y-%m-%d"))
        current_dt += timedelta(days=1)
    
    print(all_dates)
    
    # Initialize cache and differential analyzer
    cache = SentimentCache()
    diff_analyzer = DifferentialSentimentAnalyzer(analyze_sentiment)
    
    # Company tracking
    companies_mentioned = set()
    company_caches = {}
    company_analyzers = {}
    
    # Fetch texts for all dates and add to cache
    for date in all_dates:        
        
        # Retrieve relevant paragraphs for this specific date
        print(date)
        results = []
        for query in queries:
            t=time.time()*1000

            results_ = client.search_simple(
                query=query,
                #vector=np.array([110, 206, 210, 234, 215, 50, 173, 243, 25, 245, 33, 153, 73, 137, 0, 102, 40, 2, 63, 241, 243, 123, 155, 139, 239, 218, 168, 59, 135, 158, 111, 104, 200, 188, 34, 126, 10, 89, 119, 40, 108, 112, 68, 30, 229, 144, 40, 46, 87, 68, 165, 45, 3, 124, 248, 156, 255, 2, 0, 217, 201, 121, 62, 77], dtype=np.uint8),
                k=50,
                companies=[],
                start_date=date,
                end_date=date
            )
            print('time1: ', time.time()*1000-t, len(results_['results']), query)
            for item in results_['results']:
                results.append((item["score"], item["metadata"], item["vector"]))
        #input()
        metadata = None
        texts = []
        all_companies = []
        #print(results['results'])
        for score, metadata, vector in results:
            #print(f"Score1: {score:.4f}, ID: {metadata['id']}, Date: {metadata["date"]}, Companies: {metadata["companies"]}, Vector: {vector}")
            #print(type(metadata["id"]))
            text = metadata["content"]
            companies_ = metadata["companies"]
            texts.append(text)
            all_companies.append(companies_)
        print(len(texts))

        cache.add_texts(date, texts)
        
        # Extract companies mentioned in these texts
        for i, companies in enumerate(all_companies):
            companies_mentioned.update(companies)
            
            # Update company-specific caches
            for company in companies:
                if company not in company_caches:
                    company_caches[company] = SentimentCache()
                    company_analyzers[company] = DifferentialSentimentAnalyzer(analyze_sentiment)
                
                company_texts = [t for t in texts[i] if company.lower() in t.lower()]
                company_caches[company].add_texts(date, company_texts)
    
    # Create rolling windows
    windows = []
    for i in range(len(all_dates) - window_size + 1):
        window_start = all_dates[i]
        window_end = all_dates[i + window_size - 1]
        windows.append({
            'start_date': window_start,
            'end_date': window_end,
            'dates': all_dates[i:i + window_size]
        })
    
    window_analysis = []
    for window in windows:
        window_dates = window['dates']
        window_cache = {date: cache.get_texts(date) for date in window_dates}
        diff_analyzer.update_cache(window_cache)
        avg_sentiment = diff_analyzer.get_average_sentiment()
        company_analysis = {}
        for company in companies_mentioned:
            company_window_cache = {date: company_caches[company].get_texts(date) for date in window_dates}
            company_analyzers[company].update_cache(company_window_cache)
            company_avg_sentiment = company_analyzers[company].get_average_sentiment()
            if company_avg_sentiment != 0:  # Only include if we have sentiment data
                company_analysis[company] = {
                    'avg_sentiment': company_avg_sentiment,
                    'mention_count': sum(1 for date in window_dates if company_caches[company].get_texts(date))
                }
        
        window_analysis.append({
            'start_date': window['start_date'],
            'end_date': window['end_date'],
            'avg_sentiment': avg_sentiment,
            'company_analysis': company_analysis
        })
    
    # Generate predictions for each company
    predictions = {}
    for company in companies_mentioned:
        company_sentiments = []
        for window in window_analysis:
            if company in window['company_analysis']:
                company_sentiments.append({
                    'date': window['end_date'],
                    'score': window['company_analysis'][company]['avg_sentiment']
                })
        
        if len(company_sentiments) >= 10:  # Need enough data points for prediction
            # Prepare data for regression
            dates = [datetime.strptime(s['date'], "%Y-%m-%d").timestamp() for s in company_sentiments]
            scores = [s['score'] for s in company_sentiments]
            
            # Normalize dates for better regression
            min_date = min(dates)
            normalized_dates = [(d - min_date) / 86400 for d in dates]  # Convert to days
            
            X = np.array(normalized_dates).reshape(-1, 1)
            y = np.array(scores)
            
            # Fit linear regression model
            model = LinearRegression()
            model.fit(X, y)
            
            # Generate prediction dates
            last_date = datetime.strptime(all_dates[-1], "%Y-%m-%d")
            prediction_dates = []
            for i in range(1, prediction_days + 1):
                pred_date = last_date + timedelta(days=i)
                prediction_dates.append(pred_date.strftime("%Y-%m-%d"))
            
            # Generate predictions
            pred_x = np.array([(datetime.strptime(d, "%Y-%m-%d").timestamp() - min_date) / 86400 
                              for d in prediction_dates]).reshape(-1, 1)
            pred_y = model.predict(pred_x)
            
            # Calculate confidence based on model fit
            y_pred = model.predict(X)
            r2 = r2_score(y, y_pred)
            confidence = max(0.5, min(0.95, 0.5 + r2 * 0.5))
            
            # Store predictions
            predictions[company] = {
                'dates': prediction_dates,
                'sentiment_predictions': pred_y.tolist(),
                'confidence': confidence,
                'trend': 'increasing' if model.coef_[0] > 0.01 else ('decreasing' if model.coef_[0] < -0.01 else 'stable'),
                'slope': float(model.coef_[0]),
                'r_squared': r2
            }
    
    # Generate sector-wide prediction
    sector_scores = [w['avg_sentiment'] for w in window_analysis]
    
    sector_prediction = None
    if sector_scores:
        # Prepare data for regression
        x = np.array(range(len(sector_scores))).reshape(-1, 1)
        y = np.array(sector_scores)
        
        # Fit model
        model = LinearRegression()
        model.fit(x, y)
        
        # Generate prediction dates
        last_date = datetime.strptime(all_dates[-1], "%Y-%m-%d")
        prediction_dates = []
        for i in range(1, prediction_days + 1):
            pred_date = last_date + timedelta(days=i)
            prediction_dates.append(pred_date.strftime("%Y-%m-%d"))
        
        # Generate predictions
        pred_x = np.array(range(len(sector_scores), len(sector_scores) + prediction_days)).reshape(-1, 1)
        pred_y = model.predict(pred_x)
        
        # Calculate confidence
        y_pred = model.predict(x)
        r2 = r2_score(y, y_pred)
        confidence = max(0.5, min(0.95, 0.5 + r2 * 0.5))
        
        sector_prediction = {
            'dates': prediction_dates,
            'sentiment_predictions': pred_y.tolist(),
            'confidence': confidence,
            'trend': 'increasing' if model.coef_[0] > 0.01 else ('decreasing' if model.coef_[0] < -0.01 else 'stable'),
            'slope': float(model.coef_[0]),
            'r_squared': r2
        }
    
    # Generate investment recommendations
    recommendations = generate_investment_recommendations(
        sector, 
        window_analysis, 
        predictions, 
        sector_prediction
    )
    
    return {
        'sector': sector,
        'topic': topic,
        'analysis_period': {
            'start': all_dates[0],
            'end': all_dates[-1]
        },
        'window_analysis': window_analysis,
        'company_predictions': predictions,
        'sector_prediction': sector_prediction,
        'recommendations': recommendations
    }

if __name__ == "__main__":

    #query = f"Companies in the {sector} sector related to {topic}"
    sector="Technology"
    topic="AI"
    queries = [
        f"Financial performance metrics of companies in the {sector} sector working on {topic}",
        f"Market capitalization and revenue trends for {topic} companies in the {sector} sector",
        f"Year-over-year growth and funding information of {sector} companies specializing in {topic}",
        f"Compare revenue, profit margins, and market share of top {topic} firms in the {sector} sector",
        f"Investor overview: financial highlights for {topic} players in the {sector} industry",
        f"Turnover (revenue) and EBITDA for companies in the {sector} sector focusing on {topic}",
        f"EBITDA, operating margin, and cash flow statements of {topic} firms in {sector}",
        f"Quaterly earnings and guidance for {topic} companies in the {sector} sector",
    ]
    # Run analysis for Technology sector focused on AI
    
    end_date = '2025-05-05'
    #if end_date is None:end_date = (datetime.now()- timedelta(days=30)).strftime("%Y-%m-%d") 
    
    # Run rolling window analysis
    tech_ai_analysis = rolling_window_analysis(
        sector=sector,
        topic=topic,
        end_date=end_date,
        window_size=4,
        prediction_days=3,
        lookback_days=10,
        queries=queries
    )
    
    # Generate report
    report = generate_financial_report(tech_ai_analysis)
    
    tech_ai_analysis = {
        'analysis': tech_ai_analysis,
        'report': report
    }

    print(tech_ai_analysis['report'])

    # Save to JSON file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_filename = f"tech_ai_analysis_{timestamp}.json"
    with open(json_filename, 'w') as json_file:
        json.dump(tech_ai_analysis['analysis'], json_file, indent=2)
    print(f"Analysis saved to {json_filename}")

    # Save to CSV files (normalize nested structure)
    csv_base_filename = f"tech_ai_analysis_{timestamp}"
    os.makedirs(csv_base_filename, exist_ok=True)

    # Save recommendations to CSV
    recommendations = tech_ai_analysis['analysis']['recommendations']
    buy_recs_file = os.path.join(csv_base_filename, "buy_recommendations.csv")
    with open(buy_recs_file, 'w', newline='') as csvfile:
        fieldnames = ['company', 'confidence', 'predicted_sentiment', 'reasoning']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for rec in recommendations['buy_recommendations']:
            writer.writerow(rec)

    sell_recs_file = os.path.join(csv_base_filename, "sell_recommendations.csv")
    with open(sell_recs_file, 'w', newline='') as csvfile:
        fieldnames = ['company', 'confidence', 'predicted_sentiment', 'reasoning']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for rec in recommendations['sell_recommendations']:
            writer.writerow(rec)

    # Save window analysis to CSV
    window_file = os.path.join(csv_base_filename, "window_analysis.csv")
    with open(window_file, 'w', newline='') as csvfile:
        fieldnames = ['start_date', 'end_date', 'avg_sentiment']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for window in tech_ai_analysis['analysis']['window_analysis']:
            writer.writerow({
                'start_date': window['start_date'],
                'end_date': window['end_date'],
                'avg_sentiment': window['avg_sentiment']
            })
