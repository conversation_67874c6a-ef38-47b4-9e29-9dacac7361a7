import json
import time
import numpy as np
import regex as re
import base64
import requests
import tldextract
from functools import lru_cache 
from datetime import datetime, timedelta, timezone, date
from typing import Dict, Set, List, Tuple, Optional, Any, Union
from elasticsearch import Elasticsearch
from requests_aws4auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from opensearchpy import OpenSearch, RequestsHttpConnection

VECTORS_SEARCH_ENDPOINT = "https://167.235.88.209:8000/search"
VDB_STATUS_ENDPOINT = "https://167.235.88.209:8000/status"
LOCAL_VECTOR_STORE_API_KEY = "myveryscretnotsolongcode"

def search_vectors(vector: np.ndarray = None,
                query: str = None,
                companies: List[str] = None,
                start_date: Union[str, datetime, date] = None,
                end_date: Union[str, datetime, date] = None,
                k: int = 10,
                url: str = VECTORS_SEARCH_ENDPOINT) -> dict:
    
    params = {
        'query': query,
        'k': k
    }
    if not (vector is None or not vector.any()):
        params['vector'] = base64.b64encode(vector.tobytes()).decode('ascii')
        params['shape'] = json.dumps(vector.shape)

    # Convert and add optional parameters if present
    if companies:
        params['companies'] = companies
        
    if start_date:
        params['start_date'] = date_to_string(start_date)
            
    if end_date:
        params['end_date'] = date_to_string(end_date)

    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }
    response = requests.get(url, params=params, headers=headers, verify=False)
    
    if response.status_code == 400:
        raise ValueError(response.json()['message'])
    elif response.status_code != 200:
        raise RuntimeError(f"Server error: {response.json()['message']}")
        
    return response.json()


def test_vdb(url: str = VDB_STATUS_ENDPOINT) -> dict:
    
    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }
    response = requests.get(url, headers=headers, verify=False)
    
    if response.status_code == 400:
        return False
    elif response.status_code != 200:
        return False
        
    return True

#print(1, str(test_vdb()))
#exit(1)

def date_to_string(x):
    return x if isinstance(x, str) else x.strftime('%Y-%m-%d')

def find_matches(full_text, start, chars_between, end):
    """Find all matches and their character count differences"""
    pattern = f"{re.escape(start)}(.+?){re.escape(end)}"
    matches = []
    
    for match in re.finditer(pattern, full_text, re.DOTALL):
        middle = match.group(1)
        diff = abs(len(middle) - chars_between)
        matches.append((diff, match.group(0)))
        
    return matches


#with LN redirect from moreover
# some of these were added later so need to be cleaned from db.. 
IGNORE_DOMAIN = {
    'reporter.am',
    'etfdailynews.com',
    'theenterpriseleader.com',
    'marketbeat.com',
    'americanbankingnews.com',
    'themarketsdaily.com',
    'thelincolnianonline.com',
    'modernreaders.com',
    'tickerreport.com',
    'thestockobserver.com',
    'com-unik.info',
    'zolmax.com', 
    'transcriptdaily.com',
    'defenseworld.net'
    }


def get_domain(x):
    try:return tldextract.extract(x.strip()).registered_domain
    except:return ""

def connect_elasticsearch_aws():
    _es = None
    _es = Elasticsearch(
        ['https://search-equbot-ln-es-v2v7spplqpcsjwxg5hg3qmhajq.us-east-1.es.amazonaws.com'],
        http_auth=('equbot_es', 'Equb0t!23'),
        scheme="https",
        port=443,timeout=30, max_retries=10, retry_on_timeout=True
    )
    if _es.ping():pass#print('Yay Connect')
    else:print('Awww it could not connect!')
    return _es

def connect_openSearch():
   aws_access_key  = '********************'
   aws_secret_key = 'sjzRbkq5UFYz3Fku3NgO2Qr0r0bUvKhqYvm/jiDd'
   region='us-east-1'
   service='es'
   hosts = ['https://search-unstructured-es-ml3vahhmgvadk5gjdku5ditlzu.us-east-1.es.amazonaws.com:443']
   auth = AWS4Auth(aws_access_key, aws_secret_key, region, service)
   port = 443
   client = OpenSearch(
       hosts=hosts,
       port=port,
       http_auth=auth,
       connection_class=RequestsHttpConnection,
       timeout=50, 
       max_retries=5, 
       retry_on_timeout=True
    )
   return client

@lru_cache(maxsize = 10000) 
def fetch_documents(art_id):
    global printed

    query = {
        "query": {
            "terms": {
                "id": [art_id]
            }
        }
    }

    index='ln_index'
    results = client_es.search(index=index, body=json.dumps(query))
    year = 2025
    index2=f"quantexa_data_{year}"
    results2 = client_os.search(index=index2, body=json.dumps(query))
    print(results2)
    documents = {}
    domains = {}
    doc_id = None
    for hit in results['hits']['hits']+results2['hits']['hits']:
        source = hit['_source']
        doc_id = source['id']
        if 'url' in source:
            domain = get_domain(source['url'])
        else:
            print('missing domain', str(source)[:333])
            continue
        if domain == 'moreover.com':
            if 'source' in source and 'homeUrl' in source['source']:
                domain = get_domain(source['source']['homeUrl'])
            elif not printed:
                printed = True
                print(source['source'])
        if domain in IGNORE_DOMAIN:
            print('skipping', domain)
            continue
        domains[doc_id] = domain
        if 'content' in source:
            content = f"{source['content']}\n\n{source['title']}"
        else:
            content = source['title']
        documents[doc_id] = content
    try:return list(documents.values()).pop(), list(domains.values()).pop()
    except:
        print(doc_id)
        return '',''
    #return documents, domains

client_es=connect_elasticsearch_aws()
client_os=connect_openSearch()

# actual search 

start = '2025-05-10'
#print((datetime.strptime(start, "%Y-%m-%d") - timedelta(days=30)).strftime("%Y-%m-%d"))
end = '2025-05-13'
#vectors = np.packbits(binary_quantize(embed_sentences_(['this is random Apple Inc query'], task = "retrieval.query")), axis=1)
companies = []#['Valmet Corp']
query = 'stock rose 4 percent today.'
query = 'US stock market'
query = 'Apple stock'
#query = None

t=time.time()*1000

results = search_vectors(
    query=query,
    #vector=np.array([110, 206, 210, 234, 215, 50, 173, 243, 25, 245, 33, 153, 73, 137, 0, 102, 40, 2, 63, 241, 243, 123, 155, 139, 239, 218, 168, 59, 135, 158, 111, 104, 200, 188, 34, 126, 10, 89, 119, 40, 108, 112, 68, 30, 229, 144, 40, 46, 87, 68, 165, 45, 3, 124, 248, 156, 255, 2, 0, 217, 201, 121, 62, 77], dtype=np.uint8),
    k=30, # keep this higher because some domains are filtered
    companies=companies,
    start_date=start,
    end_date=end
)

print('time1: ', time.time()*1000-t, len(results['results']))
#input()
metadata = None
print(results['results'])
for score, metadata, vector in results['results']:
    print(f"Score1: {score:.4f}, ID: {metadata['id']}, Date: {metadata["date"]}, Companies: {metadata["companies"]}, Vector: {vector}")
    if not metadata:continue
    print(type(metadata["id"]))
    content, domain = fetch_documents(metadata["id"])
    if metadata['text']:
        start, chars_between, end = metadata['text'].split('|')
        chars_between = int(chars_between)
        text = find_matches(content, start, chars_between, end)
        print(chars_between, text)
    

