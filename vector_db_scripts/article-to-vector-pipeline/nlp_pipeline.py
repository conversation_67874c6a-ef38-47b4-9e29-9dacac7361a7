"""
article cleanup with sutchit

serp search sources. 

"""

import sys, subprocess, asyncio, time, json, pickle, boto3, aiohttp, aiobotocore, os, aio_pika, random, uuid, tldextract, signal
import struct, threading, torch, shutil, logging, tempfile, regex as re, csv, tarfile, pandas as pd, networkx as nx
import community, pysbd, pprint, hashlib, xxhash, traceback, urllib.parse, unicodedata, multiprocessing, warnings
import stanza
import base64, requests
warnings.filterwarnings('ignore')
import numpy as np 
#import cupy as np
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from cachetools import LRUCache, TTLCache
from typing import List, Optional
from itertools import count
from threading import Lock, RLock
from datasketch import MinHashLSH, MinHash
from tqdm import tqdm
from functools import wraps, lru_cache
from langdetect import detect
from urllib.parse import urlparse
from aiobotocore.session import get_session
from aiobotocore.config import AioConfig
from botocore.config import Config
from node2vec import Node2Vec
from dataclasses import dataclass, field
from io import StringIO, BytesIO
from functools import partial
from uuid import uuid4
from datetime import datetime, timedelta, timezone, date
from botocore.exceptions import ClientError
from typing import Any, Dict, List, Tuple, Union, Optional
from collections import defaultdict, OrderedDict, Counter
from prometheus_client import start_http_server, Summary, Gauge, Counter as PCounter, Histogram, generate_latest
from sentence_transformers import SentenceTransformer
#from sentence_transformers.quantization import semantic_search_usearch
from sentence_transformers.quantization import quantize_embeddings, semantic_search_faiss
from usearch.index import Index, Indexes, MetricKind, ScalarKind, MetricSignature, CompiledMetric
from sklearn.cluster import KMeans, AffinityPropagation
from sklearn.metrics.pairwise import cosine_similarity
from neo4j import GraphDatabase
from logging.handlers import RotatingFileHandler
from FlagEmbedding import BGEM3FlagModel, FlagReranker
from aio_pika.abc import AbstractIncomingMessage
from aio_pika import Message, ExchangeType, DeliveryMode
from transformers import AutoModelForCausalLM, AutoTokenizer
#from usearch_rdict import *
from fastapi.security import APIKeyHeader

VECTORS_POST_ENDPOINT = "https://**************:8000/post"
VECTORS_SEARCH_ENDPOINT = "https://**************:8000/search"
VDB_STATUS_ENDPOINT = "https://**************:8000/status"
ARTICLE_SEARCH_ENDPOINT = "https://**************:8000/article"
LOCAL_VECTOR_STORE_API_KEY = "myveryscretnotsolongcode"

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# global variables, clients, and models
from triplet_extraction_prompt import TRIPLET_EXTRACTION_PROMPT
from pipeline_steps import PIPELINE_STEPS
RANKING_SIGNALS_FILE = "ranking_signals.csv"

collection_name = "" # introduce press releases, quaterly reports, etc.. do NOT merge them, keep separately, easier to scale and filter later

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("nlp_pipeline")
logger.setLevel(logging.INFO)
handler = RotatingFileHandler(f"nlp_pipeline_{collection_name}.log", maxBytes=100000000, backupCount=50)
logger.addHandler(handler)
logger.info(f"Using device: {device}")

RABBITMQ_URL = "amqp://guest:guest@localhost/"

# Global variables
MAX_ADVANCE = 3 # NOT processing too much stuff as results need to be stored somewhere for consumers. pickle on-disk expensive I/O, and RAM limited. 
                # so just always have stuff ready for the next guy in line! 
MAX_REQUEUE_ATTEMPTS = 15
REQUEUE_DELAY = 5000  # milliseconds

# Define metrics
PROCESSED_COUNT = PCounter('messages_processed_total', 'Number of messages processed', ['stage'])
ERROR_COUNT = PCounter('processing_errors_total', 'Number of processing errors', ['stage'])
PROCESSING_TIME = Histogram('message_processing_seconds', 'Time spent processing messages', ['stage'])


#TODO: refactor, tie these data bind parameters to a data source class/config

#S3_BUCKET = 'equbot-ln-archive-own'
S3_BUCKET = 'eapp-design'
S3_PREFIX = 'LN_Search_Data'
FILE_PATH_IDS_FILE = f"bucket_contents.csv"
PROCESSED_RUNS_FILE = f"processed_runs_{collection_name}.pkl"
PROCESSING_RUNS_FILE = f"processing_runs_{collection_name}.pkl"
END_TIME = 1715405892709

S3_BUCKET_Q = 'quantexa-data'
S3_PREFIX_Q = 'quantexa_daily_data/'#'Key': 'quantexa_daily_data/2025-04-05/quantexa_2025-04-05_1.jsonl'
FILE_PATH_IDS_FILE_Q = 'bucket_contents_q.csv'
PROCESSED_RUNS_FILE_Q = f"processed_runs_{collection_name}_q.pkl"
PROCESSING_RUNS_FILE_Q = f"processing_runs_{collection_name}_q.pkl"
END_TIME_Q = '2025-04-13'

S3_BUCKETS = [S3_BUCKET, S3_BUCKET_Q]
S3_PREFIXES = [S3_PREFIX, S3_PREFIX_Q]
FILE_PATH_IDS_FILES = [FILE_PATH_IDS_FILE, FILE_PATH_IDS_FILE_Q]
PROCESSED_RUNS_FILES = [PROCESSED_RUNS_FILE, PROCESSED_RUNS_FILE_Q]
PROCESSING_RUNS_FILES = [PROCESSING_RUNS_FILE, PROCESSING_RUNS_FILE_Q]
END_TIMES = [END_TIME, END_TIME_Q]

START_TIME = time.time()

which = None
which_idx = None

# WARNING.. if fliped while items are in the queue? will mess up stuff! so need to pause new, sleep, then flip!
def flip_dataset():

    global which, which_idx
    global S3_BUCKET, S3_PREFIX, FILE_PATH_IDS_FILE, PROCESSED_RUNS_FILE, PROCESSING_RUNS_FILE, END_TIME

    need_after = False
    if which is not None:
        print('sleeping for 3 minutes')
        need_after = True
        time.sleep(600) # 10 minutes should finish even the largest batches with tens of thousands of items

        with open('ln_or_q.txt','w') as w:
            w.write('ln' if which=='q' else 'q')

    with open('ln_or_q.txt','r') as f:
        which = f.read().strip()

    which_idx = 1 if which == 'q' else 0

    logger.info(f"> Selected processing dataset {which}")

    S3_BUCKET = S3_BUCKETS[which_idx]
    S3_PREFIX = S3_PREFIXES[which_idx]
    FILE_PATH_IDS_FILE = FILE_PATH_IDS_FILES[which_idx]
    PROCESSED_RUNS_FILE = PROCESSED_RUNS_FILES[which_idx]
    PROCESSING_RUNS_FILE = PROCESSING_RUNS_FILES[which_idx]
    END_TIME = END_TIMES[which_idx]
    if need_after:
        after_flip()

flip_dataset()

GLOBAL_IDX_FILE = f"global_idx_{collection_name}.txt"
DUPE_CACHE_FILE = f"dupe_cache_{collection_name}.pkl"

USEARCH_file_path=f"usearch_state_{collection_name}.pkl"
USEARCH_aids_map_path=f"aids_map_{collection_name}.pkl"


aws_access_id = '********************'
aws_access_secret = 'VJPjGoHzfXbsl94JEpy0bSbtDEsv26aQMxuUEUjx'

AWS_ACCESS_KEY_ID = '********************'
AWS_SECRET_ACCESS_KEY = 'bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'

s3_client_kwargs = {
    'aws_access_key_id': AWS_ACCESS_KEY_ID,
    'aws_secret_access_key': AWS_SECRET_ACCESS_KEY,
}

#s3_client = boto3.client('s3', aws_access_key_id=aws_access_id, aws_secret_access_key=aws_access_secret)
#smodel = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2", device=device) 
smodel = SentenceTransformer('all-MiniLM-L6-v2', device=device) # i don't need 'dupes' accross langs, so this more general model works much faster
#GPU sbert 5x faster than BGE-m3, 10x faster than GPU colbert with fastembed, and 60x speed of Finbert! 
#model = BGEM3FlagModel('haophancs/bge-m3-financial-matryoshka', use_fp16=True, dimension=384)
#model = SentenceTransformer('BAAI/bge-m3', device=device)
#model = BGEM3FlagModel('BAAI/bge-m3', use_fp16=True)
model = SentenceTransformer("jinaai/jina-embeddings-v3", trust_remote_code=True)

#GPU: use jina-embeddings-v3 sbert!, 512 dimensions, then quantize
#for dedupe all-MiniLM-L6-v2-flag 

#model = smodel

EMBEDDING_DIMENSION = 512

##triplex_model = AutoModelForCausalLM.from_pretrained("sciphi/triplex", trust_remote_code=True).to('cuda').eval()
##triplex_tokenizer = AutoTokenizer.from_pretrained("sciphi/triplex", trust_remote_code=True)
#neo4j_driver = GraphDatabase.driver("bolt://localhost:7687", auth=("neo4j", "password"))

#usearch_client = create_search_index("embed_store", vector_dim=512)

# clean kill, should have all intermediary cleaned up, except maybe dupes :( so let's test for errors!
# tail -f nohup.out | grep --line-buffered "cleaned tmp" | while read line; do kill -9 $(ps aux | grep nlp_pipeline.py | awk 'NR==1{print $2}'); break;done

# methods

def time_function(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        processing_time = time.time() - start_time
        PROCESSING_TIME.labels(stage=func.__name__).observe(processing_time)
        logger.info(f"{func.__name__} took {processing_time:.2f} seconds at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return result
    return wrapper

def async_timer(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        processing_time = time.time() - start_time
        PROCESSING_TIME.labels(stage=func.__name__).observe(processing_time)
        logger.info(f"{func.__name__} took {processing_time:.2f} seconds at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return result
    return wrapper

@time_function
def send_vectors(vectors: np.ndarray, metadata: List[Dict], url: str = VECTORS_POST_ENDPOINT):
    body = {
        'shape': vectors.shape,
        'metadata': metadata,
        'vectors': base64.b64encode(vectors.tobytes()).decode('ascii')
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }

    attempt = 0
    max_retries = 100
    while attempt <= max_retries:
        try:
            response = requests.post(
                url,
                json=body,
                headers=headers,
                verify=False
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:  # Too Many Requests
                if attempt == max_retries:
                    raise Exception(f"Max retries ({max_retries}) exceeded. Server is still busy.")
                
                # Calculate delay with exponential backoff
                delay = 1 * (2 ** attempt)  # 1s, 2s, 4s, etc.
                print(f"Server is busy. Retrying in {delay} seconds... (Attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)
                attempt += 1
                continue
            
            raise e  # Re-raise other HTTP errors
            
        except Exception as e:
            raise Exception(f"Unexpected error occurred: {str(e)}")


@time_function
def test_vdb(url: str = VDB_STATUS_ENDPOINT) -> dict:
    
    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }
    response = requests.get(url, headers=headers, verify=False)
    
    if response.status_code == 400:
        return False
    elif response.status_code != 200:
        return False
        
    return True


@time_function
def search_vectors(vector: np.ndarray = None,
                  query: str = None,
                  companies: List[str] = None,
                  start_date: Union[str, datetime, date] = None,
                  end_date: Union[str, datetime, date] = None,
                  k: int = 10,
                  url: str = VECTORS_SEARCH_ENDPOINT) -> dict:
    
    params = {
        'query': query,
        'k': k
    }
    if not (vector is None or not vector.any()):
        params['vector'] = base64.b64encode(vector.tobytes()).decode('ascii')
        params['shape'] = json.dumps(vector.shape)

    # Convert and add optional parameters if present
    if companies:
        params['companies'] = companies
        
    if start_date:
        params['start_date'] = date_to_string(start_date)
            
    if end_date:
        params['end_date'] = date_to_string(end_date)

    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }
    response = requests.get(url, params=params, headers=headers, verify=False)
    
    if response.status_code == 400:
        raise ValueError(response.json()['message'])
    elif response.status_code != 200:
        raise RuntimeError(f"Server error: {response.json()['message']}")
        
    return response.json()

@time_function
def search_article(article_id: str = None,
                  on_date: Union[str, datetime, date] = None,
                  url: str = ARTICLE_SEARCH_ENDPOINT) -> dict:
    
    params = {
    }

    # Convert and add optional parameters if present
    if article_id:
        params['article_id'] = article_id
        
    # Handle different date input formats
    if on_date:
        params['on_date'] = date_to_string(on_date)


    headers = {
        'Authorization': f'Bearer {LOCAL_VECTOR_STORE_API_KEY}'
    }

    response = requests.get(url, params=params, headers=headers, verify=False)
    
    if response.status_code == 400:
        raise ValueError(response.json()['message'])
    elif response.status_code != 200:
        raise RuntimeError(f"Server error: {response.json()['message']}")
        
    return response.json()

@time_function
def load_global_idx():
    if os.path.exists(GLOBAL_IDX_FILE):
        with open(GLOBAL_IDX_FILE, 'r') as f:
            return int(f.read().strip())
    return 0

GLOBAL_IDX = load_global_idx()

def save_global_idx():
    with open(GLOBAL_IDX_FILE, 'w') as f:
        f.write(str(GLOBAL_IDX))


lock = threading.Lock()
def increment_global_idx():
    global GLOBAL_IDX
    with lock:
        GLOBAL_IDX += 1
        return GLOBAL_IDX

@time_function
def load_processed_runs():
    if os.path.exists(PROCESSED_RUNS_FILE):
        with open(PROCESSED_RUNS_FILE, 'rb') as f:
            return pickle.load(f)
    return {}

def save_processed_runs(processed_runs):
    with open(PROCESSED_RUNS_FILE, 'wb') as f:
        pickle.dump(processed_runs, f)

@time_function
def load_processing_runs():
    if os.path.exists(PROCESSING_RUNS_FILE):
        with open(PROCESSING_RUNS_FILE, 'rb') as f:
            return pickle.load(f)
    return {}

def save_processing_runs(processing_runs):
    with open(PROCESSING_RUNS_FILE, 'wb') as f:
        pickle.dump(processing_runs, f)

date_and_hour = None # dummy entry

try:
    with open('split.txt','r') as f:
        SPLIT = int(f.read().strip())
except:
    logger.info("NO SPLIT ? ")
    exit(1)

@time_function
def load_file_ids():
    d = {}
    with open(FILE_PATH_IDS_FILE, 'r') as f:
        for line in f:
            if line.startswith(S3_PREFIX):
                fp_size = line.split(',')
                fp = fp_size[0].split('/')[-1]
                size = fp_size[1]
                if fp.endswith('.json'):
                    fp = fp.split('.')[0]
                    try:
                        # 3 GPUs used, therefore % 3 below
                        #if hash(int(fp)) % 3 != SPLIT:continue
                        d[int(fp)] = int(size)
                    except:
                        logger.info(f" load_file_ids fail {fp}")
                        continue
    return d

@time_function
def bin_s3_paths_by_value(s3_file_path_ids, processed_runs, value_threshold=6_000_000):
    # 1701067081680 is last seen in LN, so from now to that moment in past has been done!
    filtered_items = [(k, v) for k, v in sorted(s3_file_path_ids.items()) if k not in processed_runs and k < 1701067081680]
    
    binned_results = []
    current_bin = []
    current_sum = 0
    
    for path, value in filtered_items:
        current_bin.append(path)
        current_sum += value
        if current_sum + value > value_threshold and current_bin:
            binned_results.append(current_bin)
            current_bin = []
            current_sum = 0
            
    if current_bin:
        binned_results.append(current_bin)
        
    return binned_results

skipone = s3_file_path_ids = processed_runs = processing_runs = None

def after_flip():
    global skipone, s3_file_path_ids, processed_runs, processing_runs

    skipone = 0

    s3_file_path_ids = load_file_ids()
    processed_runs = load_processed_runs()

    processing_runs = load_processing_runs()

    # when force killed process without proper cleanup
    for k, file_ids in processing_runs.items():
        for file_id in file_ids:
            if file_id in processed_runs:
                del processed_runs[file_id]
    save_processed_runs(processed_runs)
    save_processing_runs({})

    s3_file_path_ids = bin_s3_paths_by_value(s3_file_path_ids, processed_runs)

after_flip()

logger.info(f"s3 ids:")
logger.info(s3_file_path_ids[:2])
logger.info(s3_file_path_ids[-2:])

s3_semaphore = asyncio.Semaphore(50)  # Adjust based on your needs

@async_timer
async def process_s3_object(s3, key: str, bucket: str, i=True) -> Tuple[Optional[int], Optional[List]]:
    """Process a single S3 object with error handling and semaphore control"""
    async with s3_semaphore:
        try:
            async with (await s3.get_object(Bucket=bucket, Key=key))['Body'] as stream:
                file_content = await stream.read()
            # THESE ARE UGLY FIXES FOR QUANTA SOURCE.. should be normalized in a more decoupled way!
            if not i:
                file_content = file_content.decode('utf-8')
            if i:
                file_id = int(key.split('/')[-1].split('.')[0])
                articles = json.loads(file_content)
            else:
                file_id = key
                articles = {"articles":[json.loads(x) for x in file_content.split("\n") if x.strip()]}
            return file_id, articles["articles"]
        except Exception as e:
            logger.error(f"Error processing {key}: {str(e)}")
            return None, None

#TODO: this is now ugly.. need cleaner tracking of processed stuff, data source dependend
def get_dates(date_str=None, direction="next", num_dates=3):
    # If no date provided, use today's date
    if date_str is None:
        base_date = datetime.now().date()
    else:
        # Parse the input date string
        base_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    
    result = [date_str]
    delta = 1 if direction.lower() == "next" else -1
    
    for i in range(num_dates):
        new_date = base_date + timedelta(days=i * delta)
        new_date = new_date.strftime("%Y-%m-%d")
        result.append(f"quantexa_daily_data/{new_date}/")
    
    return result

@async_timer
async def get_files(s3, run_to_process, direction='next'):
    global processed_runs, skipone

    max_prefix_attempts = 3  # Limit how many prefixes we'll try to prevent infinite loop
    if isinstance(run_to_process, int):
        base_prefix = str(run_to_process)[:-7]
        current_prefix_num = int(base_prefix)
        prefixes = [f"{S3_PREFIX}/{str(current_prefix_num + prefix_increment)}" for prefix_increment in range(max_prefix_attempts)]
    else:
        base_prefix = run_to_process.replace('quantexa_daily_data/','').split('/')[0]
        prefixes = get_dates(base_prefix, direction, max_prefix_attempts)

    #np.mean(np.diff(a))
    #       736451
    #1741702556121
    
    file_ids = []
    all_articles = []
    article_counts = []
    tasks = []
    
    
    async with s3:  # Ensure proper client lifecycle
        for prefix in prefixes:
            print(f"Checking prefix: {prefix}")  # Debug log
            
            paginator = s3.get_paginator('list_objects_v2')
            async for page in paginator.paginate(Bucket=S3_BUCKET, Prefix=prefix):
                for obj in page.get('Contents', []):
                    file_name = obj['Key']
                    if not file_name.endswith('.json') and not file_name.endswith('.jsonl'):
                        continue
                    try:
                        if isinstance(run_to_process, int):
                            file_id = int(file_name.split('/')[-1].split('.')[0])
                        else:
                            file_id = file_name
                            if file_id in processed_runs:
                                continue
                        if direction=='next' and file_id > run_to_process:
                            file_ids.append(file_id)
                            tasks.append(obj)
                            if file_id == file_name:break
                        elif direction!='next' and file_id < run_to_process:
                            file_ids.append(file_id)
                            tasks.append(obj)
                            if file_id == file_name:break
                    except ValueError:
                        continue
            
            # Sort and limit after each prefix check
            paired = list(zip(file_ids, tasks))
            paired.sort()  # Sort ascending to get smallest numbers above run_to_process
            file_ids, tasks = zip(*paired[:100]) if paired else ([], [])
            file_ids = list(file_ids)
            tasks = list(tasks)
            
            if len(file_ids):
                if not isinstance(run_to_process, int):#quant case, single file enough!
                    skipone = 0
                elif len(file_ids) < 20:
                    skipone = 20 - len(file_ids)
                else:
                    skipone = 0
                break

        # Process objects in batches
        BATCH_SIZE = 25
        for i in range(0, len(tasks), BATCH_SIZE):
            batch = tasks[i:i + BATCH_SIZE]
            batch_results = await asyncio.gather(*[
                process_s3_object(s3, obj['Key'], S3_BUCKET, isinstance(run_to_process, int))
                for obj in batch
            ])
            for file_id, articles in batch_results:
                if file_id is not None and articles is not None:
                    all_articles.extend(articles)
                    article_counts.append((file_id, len(articles)))

        logger.info(f"Found {len(file_ids)} present files w/ {len(all_articles)} articles to process after checking prefix {prefix}")

        # Update processing records
        k = max([-1] + list(processing_runs.keys()))
        processing_runs[k+1] = []
        for file_id, cnt in article_counts:
            processed_runs[file_id] = cnt
            processing_runs[k+1].append(file_id)
        save_processed_runs(processed_runs)
        save_processing_runs(processing_runs)
        
        return all_articles
        
@async_timer
async def get_past_files(s3):
    global processed_runs, s3_file_path_ids

    if not s3_file_path_ids:
        return []
    
    file_ids = s3_file_path_ids.pop()
    prefixes = [f"{S3_PREFIX}/{file_id}.json" for file_id in file_ids]
    all_articles = []
    article_counts = []

    async with s3:  # Ensure proper client lifecycle
        # Process objects in batches
        BATCH_SIZE = 25
        for i in range(0, len(prefixes), BATCH_SIZE):
            batch = prefixes[i:i + BATCH_SIZE]
            batch_results = await asyncio.gather(*[
                process_s3_object(s3, key, S3_BUCKET)
                for key in batch
            ])
            
            for file_id, articles in batch_results:
                if file_id is not None and articles is not None:
                    all_articles.extend(articles)
                    article_counts.append((file_id, len(articles)))

        # Update processing records
        k = max([-1] + list(processing_runs.keys()))
        processing_runs[k+1] = []
        for file_id, cnt in article_counts:
            processed_runs[file_id] = cnt
            processing_runs[k+1].append(file_id)
        save_processed_runs(processed_runs)
        save_processing_runs(processing_runs)

        logger.info(f"Found {len(article_counts)} past files w/ {len(all_articles)} articles to process")
        
        return all_articles

@async_timer
async def get_unprocessed_runs(s3):
    global skipone

    if not processed_runs:
        end_time = END_TIME # from 2024-11-01
    else:
        end_time = max(processed_runs.keys())

    logger.info(f"processed_runs len {len(processed_runs)}")
          
    check_time = end_time
    
    data = None
    fl = 0
    #if isinstance(END_TIME, int) -> LN index!
    if SPLIT == 0 and skipone == 0 and not isinstance(END_TIME, int): # only main GPU goes forward, others only backward!
        try:
            logger.info(f"checking greater {check_time}")
            data = await get_files(s3, check_time)
            fl = len(data)
            logger.info(f"present files len {fl}")
        except Exception as e:
            data = None
            logger.error(traceback.format_exc())
            logger.error(e)
    else:
        skipone -= 1
        if skipone < 0:
            skipone = 0

    if data:
        return data
    else:
        if isinstance(END_TIME, int):
            logger.info(f"checking ln smaller {check_time}")
            data = await get_past_files(s3)# get_files(s3, check_time, earlier=True)
        else:
            logger.info(f"checking quant smaller {check_time}")
            # for quant i don't have past.. only few days .. and no file with buckets
            data = await get_files(s3, check_time, 'past')
        logger.info(f"past files len {len(data)} /w future {fl}")
        if data:
            return data
    
    #raise ValueError
    logger.info("No unprocessed hours with files found in the specified range")
    return 'flip'
    #handle_signals(signal.SIGTERM, None)
    #handle_signals(signal.SIGINT, None)
    

@async_timer
async def s32df_article_RequestTimeTooSkewed(date_and_hour: str = None) -> pd.DataFrame:
    logger.info(f"Entered s32 func")

    # Create a new session
    session = get_session()
    
    async with session.create_client('s3', config=AioConfig(max_pool_connections=50), **s3_client_kwargs) as s3:
        logger.info(f"Entered s32 func client")
        all_articles = await get_unprocessed_runs(s3)
        if all_articles:
            logger.info(f"s32 num articles {len(all_articles)}")
        else:
            all_articles = []

    df = pd.json_normalize(all_articles)  # for nested keys
    logger.info(f"DataFrame created with shape: {df.shape}")

    return df#.head(111)  # Return the processed articles

@async_timer
async def s32df_article(date_and_hour: str = None) -> pd.DataFrame:
    global START_TIME

    FLIP_END_TIME = time.time()
    if FLIP_END_TIME - START_TIME > 6*3600:#(8 if which_idx == 0 else 2) * 3600: 
        #q is always quicker, so will flip when done automatically
        # this is for ln to give chance to q!
        logger.info(f"enough of ln, give q a chance!")
        flip_dataset()
        START_TIME = time.time()
    #    handle_signals(signal.SIGTERM, None)
    #    handle_signals(signal.SIGINT, None)

    logger.info(f"Entered s32 func")
    max_retries = 3
    retry_delay = 15  # seconds
    
    all_articles = None
    for attempt in range(max_retries):
        try:
            session = get_session()
            async with session.create_client('s3', config=AioConfig(max_pool_connections=50), **s3_client_kwargs) as s3:
                logger.info(f"Entered s32 func client - attempt {attempt + 1}")
                all_articles = await get_unprocessed_runs(s3)
                if all_articles == 'flip':
                    break
                logger.info(f"s32 num articles {len(all_articles)}")

                df = pd.json_normalize(all_articles)  # for nested keys
                logger.info(f"DataFrame created with shape: {df.shape}")

                return df#.head(111)  # Return the processed articles
                
        except Exception as e:
            logger.error(f"Error in s32df_article attempt {attempt + 1}: {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
                continue
            else:
                logger.error("Max retries reached, raising exception")
                #TODO: trigger shutdown and email of a problem! or just increase delay time.. 
                # but this should not happen again due to re-attempt
                raise
    
    if all_articles == 'flip':
        flip_dataset()
        return pd.DataFrame()
    else:
        # This line shouldn't be reached due to return/raise above, but adding for completeness
        raise Exception("Failed to process articles after max retries")
    
# LN
raw_columns = [
'companies', # parse name, exchange:symbol params; type is always company
    'sequenceId', 'wordCount', 'locations', 
'languageCode',
    'licenses', 
'title', 
'outboundUrls', # parse url redirect values in this list, get host
'content', 
'url', 
'id',
    'dataFormat', 
'estimatedPublishedDate', 
'publishedDate', 
'topics', # parse list dicts for name attr
    'language', 'duplicateGroupId', 'adultLanguage', 'tags',
    'matchingFilters', 
'harvestDate', 
    'indexTerms', 
'sentiment.entities', # parse type:value
    'sentiment.score', 'semantics.events', 'semantics.entities',
'source.name', 
    'source.metrics.mozscape.domainAuthority',
    'source.metrics.mozscape.mozRank',
    'source.metrics.mozscape.externalLinks',
    'source.metrics.mozscape.links',
    'source.metrics.mozscape.pageAuthority', 'source.location.region',
    'source.location.subregion', 'source.location.country',
'source.location.countryCode', 
    'source.category',
'source.editorialRank', 
    'source.id', 'source.feed.name',
    'source.feed.tags', 'source.feed.language',
    'source.feed.rank.inboundLinkCount', 'source.feed.autoTopics',
    'source.feed.inWhiteList', 'source.feed.genre', 'source.feed.id',
    'source.feed.editorialTopics', 'source.feed.dataFormat',
    'source.feed.mediaType', 
'source.homeUrl', 
    'media.logo', 'media.audio',
    'media.video', 'media.images', 'source.publisher', 'source.industry',
'author.name', 
    'source.feed.rank.autoRank',
    'source.feed.rank.autoRankOrder', 'source.feed.description',
    'loginStatus', 'linkedArticles', 'source.feed.generator',
    'source.feed.publishingPlatform', 'source.feed.idFromPublisher',
    'source.feed.imageUrl', 'commentsUrl', 'publishingPlatform.itemId',
    'author.publishingPlatform.userId', 'source.feed.copyright'
]

# QUANT
raw_columns2 = [
'body', 
    'categories', 
    'industries', 
    'characters_count', 
    'clusters',
'entities', # companies are here.. and other stuff, if stock_tickers present and or types == 
            # for each element in list, look for stock_tickers[], body.surface_forms[].text
    'hashtags', 
'id', 
    'keywords', 
'language', 
    'media',
    'paragraphs_count', 
'published_at', 
    'sentences_count', 
'title',
    'words_count', 'license_type', 'author.id', 
'author.name',
'links.permalink', 
    'links.related_stories', 'links.clusters',
    'sentiment.body.polarity', 'sentiment.body.score',
    'sentiment.title.polarity', 'sentiment.title.score', 
'source.domain',
'source.home_page_url', 
    'source.id', 'source.locations', 'source.name',
    'source.rankings.alexa', # OPTIONAL! maybe others are as well!
    'source.scopes', 'summary.sentences'
]    

#@time_function
def parse_outbound_urls(urls: List[str]) -> List[str]:
    #logger.info(f"normalizing links")
    urls_ = []
    for url in urls:
        try:urls_.append(urlparse(url).hostname)
        except:pass
    return urls_

@time_function
def has_cycle(d):
    def check_path(start, path):
        if start in path:  # Found cycle
            return True
            
        if start not in d:  # End of path
            return False
            
        next_val = d[start]
        if next_val == start:  # Ignore self-reference
            return False
            
        return check_path(next_val, path + [start])
        
    return any(check_path(key, []) for key in d)

def save_company_map():
    with open('company_map.pkl', 'wb') as file:
        pickle.dump(COMPANY_MAP, file)

@time_function
def load_company_map():
    try:
        with open('company_map.pkl', 'rb') as file:
            COMPANY_MAP = pickle.load(file)
    except:
        COMPANY_MAP = {}
        with open('company_map.pkl', 'wb') as file:
            pickle.dump(COMPANY_MAP, file)
    gpt_company_map = {}
    with open('gpt_company_names.txt', 'r', encoding='utf-8') as file:
        for line in file:
            names = line.strip().split('\t')
            names = [name.strip() for name in names]+[name.strip('.').strip() for name in names]+[name.replace(', ', ' ').strip() for name in names]+[name.replace(', ', ' ').strip('.').strip() for name in names]
            for name in names:
                gpt_company_map[name] = COMPANY_MAP.get(names[0], names[0])
    logger.info('company')
    try:logger.info(str(has_cycle(COMPANY_MAP)))
    except:pass
    logger.info('gpt')
    try:logger.info(str(has_cycle(gpt_company_map)))
    except:pass
    for vname, name in gpt_company_map.items():
        if vname not in COMPANY_MAP or vname == COMPANY_MAP[vname]:
            COMPANY_MAP[vname] = COMPANY_MAP.get(name, name)
        elif vname in COMPANY_MAP:
            if COMPANY_MAP[vname] != COMPANY_MAP.get(name, name):
                if COMPANY_MAP[vname] not in COMPANY_MAP:
                    COMPANY_MAP[COMPANY_MAP[vname]] = COMPANY_MAP.get(name, name)
                else:
                    try:logger.info(f"MAYBE PROBLEM WITH NAMES: {vname}, {name}, {COMPANY_MAP[vname]}, {COMPANY_MAP[name]}")
                    except:pass
    return COMPANY_MAP

COMPANY_MAP = load_company_map()
R_COMPANY_MAP = defaultdict(list)
for k,v in COMPANY_MAP.items():
    R_COMPANY_MAP[v].append(k)

logger.info(f"len company map {len(COMPANY_MAP)}")

#@time_function
def map_company_names(company):
    # can map ticker to company here, but not name to canonical name! only indirectly.. 
    name = company.get('name', '').strip()
    exchange = company.get('exchange', '').strip()
    symbol = company.get('symbol', '').strip()
    tck = f"{exchange}:{symbol}"
    if symbol and name:
        if tck not in COMPANY_MAP:
            COMPANY_MAP[tck] = name
            R_COMPANY_MAP[name].append(tck)
    if name not in COMPANY_MAP:
        if tck in COMPANY_MAP:
            COMPANY_MAP[name] = COMPANY_MAP[tck]
            R_COMPANY_MAP[COMPANY_MAP[tck]].append(name)
        else:
            COMPANY_MAP[name] = name
            R_COMPANY_MAP[name].append(name)
    return True

def company_map_get(item):
    i = 0
    while True:
        i+=1
        if item not in COMPANY_MAP or COMPANY_MAP[item] == item:
            return item
        item = COMPANY_MAP[item]
        if i>10:break #something wrong.. there is a loop
    return item

#@time_function
def parse_companies(companies: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    #logger.info(f"normalizing corps")
    #companies = [{'name': company.get('name', ''), 'exchange:symbol': f"{company.get('exchange', '')}:{company.get('symbol', '')}"} for company in companies]
    companies = [list({'name': company.get('name', '').strip(), 'exchange:symbol': f"{company.get('exchange', '').strip()}:{company.get('symbol', '').strip()}"}.values()) for company in companies if map_company_names(company)]
    companies = [COMPANY_MAP.get(item, item) for sublist in companies for item in sublist]
    # .replace(' ','_') remained from primitive qdrant text search, should be removed.. # it has ZERO effect on curent code and map structures, so can be removed! because there was no underscore in any original company name!
    companies = list(set([c for c in companies if c and not c.strip().startswith(':') and not c.strip().endswith(':') and c.strip(' :')]))
    if companies and random.randint(1,100)<3:
        logger.info(str(companies))
    return companies
    #return ' '.join(set([c for c in companies if c and not c.startswith(':') and not c.endswith(':') and c.strip()])) #for qdrant
    #return [c for c in companies if c and not c.startswith(':') and not c.endswith(':') and c.strip()]
    
pcres = parse_companies([{"name":"Google Inc."}, {"name":"Yahoo"}])
logger.info(f"parse_companies {pcres}")

cgood = 'businessman software executive banker economist executive entrepreneur industrialist'


# TODO: this should be dumped file from KG query!! This was improvized one time wikipedia analysis, not too much data. 20k
COMPANY_PEOPLE = defaultdict(list)
with open('cats/tt.txt', 'r', encoding='utf-8') as f:
    for line in f:
        a, b = line.strip().replace('_',' ').split('\t')
        if '(' in a:
            if a.split('(')[-1] not in cgood:
                continue
            a = a.split('(')[0].strip()
        if '(' in b:
            if b.split('(')[-1] not in cgood:
                continue
            b = b.split('(')[0].strip()
        COMPANY_PEOPLE[b].append(a)
            
with open('cats/tr.txt', 'r', encoding='utf-8') as f:
    COMPANY_PEOPLE_T = json.load(f)
    for k in list(COMPANY_PEOPLE_T.keys()):
        if '(' in k:
            if k.split('(')[-1] not in cgood:
                del COMPANY_PEOPLE_T[k]
                continue
            ks = k.split('(')[0].strip()
            if ks in COMPANY_PEOPLE_T:
                logger.info(f"AMBIGUITY!, {k}, {ks}")
                del COMPANY_PEOPLE_T[k]
                continue
            COMPANY_PEOPLE_T[ks] = COMPANY_PEOPLE_T[k]
            del COMPANY_PEOPLE_T[k]

for k in COMPANY_PEOPLE_T.keys():
    for lang in COMPANY_PEOPLE_T[k]:
        val = COMPANY_PEOPLE_T[k][lang]
        if '(' in val:
            val = val.split('(')[0].strip()
            COMPANY_PEOPLE_T[k][lang] = val


def expandco(item, lang):
    items = COMPANY_PEOPLE.get(item, [item])
    return [COMPANY_PEOPLE_T.get(item, {}).get(lang, [item]) for item in items]

def flatten(lst):
    result = []
    for item in lst:
        if isinstance(item, list):
            result.extend(flatten(item))
        else:
            result.append(item)
    return result

def process_company_lists(companies, lang):
    """
    Process multiple lists of company names to extract core identifying words.
    """
    common_terms = {'inc', 'incorporated', 'corporation', 'corp', 'limited', 
                   'ltd', 'llc', 'company', 'co', 'international', 'worldwide',
                   'global', 'holdings', 'group', 'plc', 'solutions', 'technologies',
                   'technology', 'systems', 'services', 'industries', 'the', 'and', 'or'}
    
    word_counts = Counter()
    lists_of_companies = [R_COMPANY_MAP.get(company,[company]) for company in companies]

    for company_list in lists_of_companies:
        # Get all words from each company name
        company_list = list(set([company if ':' not in company else company.split(':')[1] for company in company_list]))
        expanded = [expandco(item, lang) for item in company_list]
        company_list = list(set(flatten(expanded)))

        company_words = set(' '.join(company_list).lower().replace(',', ' ').replace('.', ' ').split()) - common_terms
        for word in company_words:
            if len(word)>2:
                word_counts[word]+=1
    core_words = set([word for word, count in word_counts.items() if count == 1])
    single = {}
    for company_list in lists_of_companies:
        for company in company_list:
            for word in company.split():
                if word.lower() in core_words and word not in single:
                    single[word] = company
    #print('SINGLE', single)
    return single

try:
    with open('wikiseen.pkl', 'rb') as file:
            wikiseen = pickle.load(file)
except:
    wikiseen = set()


def find_similar_companies(target_embedding, top_k=5):
    companies, embeddings = company_embeddings
    indices = [idx for idx, c in enumerate(companies) if c in COMPANY_MAP]
    similarities = [(
        companies[idx],
        np.dot(target_embedding, embeddings[idx]) / (np.linalg.norm(target_embedding) * np.linalg.norm(embeddings[idx])),
        idx
    ) for idx in indices]
    
    top = sorted(similarities, key=lambda x: x[1], reverse=True)[:top_k]
    return [t for t, s, _ in top if s > 0.85]

def is_missing_or_singleton(companies):
    # First: if it's a single missing value
    if companies is None or companies is pd.NA or (isinstance(companies, float) and pd.isna(companies)):
        return True

    # Then: if it's a list-like with length 1
    if hasattr(companies, '__len__') and not isinstance(companies, (str, bytes)) and len(companies) == 1:
        return True

    return False

def filter_companies(text, companies, lang, embedding):
    if is_missing_or_singleton(companies):
        return companies
    companies = list(set([company if ':' not in company else company.split(':')[1] for company in companies]))
    [get_wiki_translations(company) for company in companies if company not in wikiseen]
    singles = process_company_lists(companies, lang)
    pattern = r'\b(?:' + '|'.join(re.escape(company) for company in companies) + r')\b'
    matches = re.findall(pattern, text, re.DOTALL)
    results = [match.strip() for match in matches if match.strip()]
    if not results:
        results = find_similar_companies(embedding)
    if False:
        pattern_s = r'\b(?:' + '|'.join(re.escape(company) for company in singles.keys()) + r')\b'
        matches_s = re.findall(pattern_s, text, re.DOTALL)
        results_s = [singles[match.strip()] for match in matches_s if match.strip()]
    return list(set(results))

#@time_function
def parse_topics(topics: List[Dict[str, Any]]) -> List[str]:
    #logger.info(f"normalizing topics")
    return [topic.get('name', '') for topic in topics]

#@time_function
def parse_sentiment_entities(entities: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    #logger.info(f"normalizing sentiment")
    if type(entities)==type([]):return [{'type': entity.get('type', ''), 'value': entity.get('value', '')} for entity in entities]
    else:return []

def merge_from_sentiments(row):
    y = row['companies'] + [{'name': x['value']} for x in (row['sentiment.entities'] if isinstance(row['sentiment.entities'], list) else []) if x['type'] == 'Company']
    #if not y and (row['companies'] or row['sentiment.entities']):
    #    logger.info(f"FFFailed merge: {y}, {row['companies']}, {row['sentiment.entities']}")
    return y

def extract_from_entities(rows):
    return [{'name':row.get('body', {}).get('surface_forms', [{}])[0].get('text', row.get('id')) if row.get('body', {}).get('surface_forms', []) else row.get('id')} for row in rows['entities'] if ('types' in row and 'Company' in row['types']) or ('stock_tickers' in row and row['stock_tickers'])]
    
def load_ranking_signals():
    if os.path.exists(RANKING_SIGNALS_FILE):
        with open(RANKING_SIGNALS_FILE, 'r') as f:
            return pd.DataFrame(json.load(f))
    return pd.DataFrame()

def save_ranking_signals(ranking_signals):
    with open(RANKING_SIGNALS_FILE, 'w') as f:
        json.dump(ranking_signals, f)

@time_function
def append_ranking_signals(df):
    logger.info(f"normalizing ranking")
    ranking_signals = load_ranking_signals()
    if 'host' in ranking_signals:
        rs_prefixed = ranking_signals.rename(columns={col: f'rs_{col}' for col in ranking_signals.columns if col != 'host'})
    elif 'domain' in ranking_signals:
        rs_prefixed = ranking_signals.rename(columns={col: f'rs_{col}' if col != 'domain' else 'host' for col in ranking_signals.columns})
    else:
        return df
    df = df.merge(rs_prefixed, on='host', how='left')
    return df


#with LN redirect from moreover
IGNORE_DOMAIN = {
    'reporter.am',
    'etfdailynews.com',
    'theenterpriseleader.com',
    'marketbeat.com',
    'americanbankingnews.com',
    'themarketsdaily.com',
    'thelincolnianonline.com',
    'modernreaders.com',
    'tickerreport.com',
    'thestockobserver.com',
    'com-unik.info',
    'zolmax.com', 
    'transcriptdaily.com',
    'defenseworld.net'
    } 
 
def load_sj_ranks():
    sj_domain_scores = {}
    with open('domain_median_ranks.csv', 'r', newline='', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile, delimiter=',')
        next(reader)  # Skip the header row
        for row in reader:
            sj_domain_scores[row[0].strip().lower()] = int(float(row[1].strip()))

    bad_domains = set()
    with open('all_analyzed_sj_domains_f.csv', 'r', newline='', encoding='utf-8') as file:
        for line in file:
            bad_domains.add(line.strip().lower())

    for d in ["", "moreover.com"]:
        if d in sj_domain_scores:del sj_domain_scores[d]
        if d in bad_domains:bad_domains.remove(d)

    return sj_domain_scores, bad_domains

SJ_DOMAIN_SCORES, BAD_DOMAINS = load_sj_ranks() # TODO: in future this will be dynamicaly updated. for now quick fix single update

BAD_DOMAINS |= IGNORE_DOMAIN

def get_domain(x):
    try:return tldextract.extract(x.strip()).registered_domain
    except:return ""
    
@async_timer
async def sj_domain_filter(df):
    df = df.rename(columns={
        'links.permalink': 'url',
        'source.home_page_url': 'source.homeUrl'
        })
    if 'url' not in df.columns:
        if len(df) > 0:
            logger.info(f"no url in dataframe?? ")
        return pd.DataFrame()
    df['domain'] = df['url'].apply(get_domain)
    df['domain'] = np.where(df['domain'] == 'moreover.com', 
                        get_domain(df['source.homeUrl']), 
                        df['domain'])
    logger.info(f"full df len {len(df)}")
    logger.info(f"Unique domains: {', '.join(df['domain'].unique())}")
    df = df[~df['domain'].isin(BAD_DOMAINS)]
    logger.info(f" >>>>>>>>>>>>>>>>> after bad domain removal df len {len(df)}")
    return df

#@time_function
def get_host(x):
    #logger.info(f"hosting")
    try:return tldextract.extract(str(x).strip()).fqdn
    except:return ""

def normalize_date(df: pd.DataFrame) -> pd.DataFrame:
    logger.info("Normalizing date")
    date_columns = ['publishedDate', 'estimatePublishedDate', 'harvestDate']
    
    # Initialize 'date' column if it doesn't exist
    if 'date' not in df.columns:
        df['date'] = ''
    
    for col in date_columns:
        if col in df.columns:
            # Convert to string and replace 'nan' with empty string
            df[col] = df[col].astype(str).replace('nan', '')
            
            # Assign to 'date' where 'date' is empty and current column is not empty
            mask = (df['date'] == '') & (df[col] != '')
            df.loc[mask, 'date'] = df.loc[mask, col]
    
    # Safe date parsing with error handling
    def safe_parse_date(date_str):
        if pd.isna(date_str) or date_str == '' or date_str.lower() == 'na':
            return pd.NaT
        
        try:
            return pd.to_datetime(date_str, errors='raise')
        except:
            try:
                # Try with a more flexible parser
                return pd.to_datetime(date_str, format='mixed', errors='raise')
            except Exception as e:
                logger.warning(f"Could not parse date: {date_str}. Error: {str(e)}")
                return pd.NaT
    
    # Convert dates with error handling
    df['date'] = df['date'].apply(safe_parse_date)
    
    # Log any unparseable dates
    invalid_dates = df[df['date'].isna()]['date']
    if len(invalid_dates) > 0:
        logger.warning(f"Found {len(invalid_dates)} unparseable dates")
        
    return df

class WikiTranslationFetcher:
    def __init__(self):
        self.base_url = "https://en.wikipedia.org/w/api.php"
        self.session = requests.Session()
    
    def get_translations(self, title):
        params = {
            "action": "query",
            "format": "json",
            "titles": title,
            "prop": "langlinks",
            "lllimit": "500"  # Max limit per request
        }
    
        response = self.session.get(self.base_url, params=params)
        response.raise_for_status()
        data = response.json()
        
        # Extract page data
        pages = data["query"]["pages"]
        page_id = list(pages.keys())[0]
        
        # Handle non-existent pages
        if page_id == "-1":
            return {}
        
        # Get translations
        translations = {}
        if "langlinks" in pages[page_id]:
            for lang in pages[page_id]["langlinks"]:
                translations[lang["lang"]] = lang["*"]
        
        return translations

wiki_translation_fetcher = WikiTranslationFetcher()

#@time_function
def get_wiki_translations(title):
    global COMPANY_PEOPLE_T, wikiseen
    title = title.strip().replace('_',' ')
    if title in wikiseen or title in COMPANY_PEOPLE_T or COMPANY_MAP.get(title, title) in wikiseen:
        return
    translations = wiki_translation_fetcher.get_translations(title)
    for lang in translations.keys():
        val = translations[lang]
        if '(' in val:
            val = val.split('(')[0].strip()
            translations[lang] = val
    if '(' in title:
        if title.split('(')[-1] in cgood:
            title_s = title.split('(')[0].strip()
            if title_s not in COMPANY_PEOPLE_T:
                COMPANY_PEOPLE_T[title_s] = translations
    else:
        COMPANY_PEOPLE_T[title] = translations
        logger.info(f"{str(translations)[:33]}")
    wikiseen.add(title)
    time.sleep(1)
    if random.randint(1,100)<5:
        with open('cats/tr.txt', 'w', encoding='utf-8') as f:
            json.dump(COMPANY_PEOPLE_T, f, ensure_ascii=False, indent=2)
        with open('wikiseen.pkl', 'wb') as file:
            pickle.dump(wikiseen, file)

start_past = False
start_future = False

@async_timer
async def normalize_articles(df: pd.DataFrame) -> List[Dict[str, Any]]:#, ranking_signals: Dict[str, Any] = None

    global start_past, start_future

    logger.info(f"normalizing started")

    if 'url' not in df.columns or 'languageCode' not in df.columns:
        #quant
        df = df.rename(columns={
            'body': 'content', 
            'language': 'languageCode', 
            'published_at': 'publishedDate', 
            'links.permalink': 'url',
            'source.location': 'source.location.countryCode',
            'source.home_page_url': 'source.homeUrl'
            })

    columns_to_copy = [
        'id', 
        'source.id',
        'url', 
        'publishedDate', 
        'estimatedPublishedDate', 
        'harvestDate',
        'languageCode', 
        'title', 
        'content', 
        'source.name', 
        'source.location.countryCode',
        'source.editorialRank', 
        'source.homeUrl', 
        'author.name',
    ]
    [   # QUANT: 
        #'id', 
        'body', # -> content
        'entities', # -> parse
        'language', # -> languageCode
        'published_at', # -> publishedDate
        #'title', 
        'links.permalink', # -> url
        #'source.domain', 
        'source.location', # -> source.location.countryCode
        'source.home_page_url' # -> source.homeUrl
        #'author.name', 
    ]
    
    columns_to_transform = {
        'outboundUrls': parse_outbound_urls,
        #'companies': merge_from_sentiments,
        #'companies': parse_companies, #this unifies and simplifies.. preceeding company parsings are flattening json infos..
        'topics': parse_topics,
        'sentiment.entities': parse_sentiment_entities,
    }
    columns_to_transform_ = { #companies already in normalized_df!
        'companies': parse_companies, #this unifies and simplifies.. preceeding company parsings are flattening json infos..
    }
    
    # Create a new DataFrame with the copied columns
    #normalized_df = df[columns_to_copy].copy()
    existing_columns = [col for col in columns_to_copy if col in df.columns]
    normalized_df = df[existing_columns].copy()
    normalized_df = normalized_df.reindex(columns=columns_to_copy, fill_value='')
    
    #with pd.option_context('display.max_columns', None, 'display.width', None):
    #    print(df.head())
    #    print(df.tail())
    
    if 'companies' in df.columns and 'sentiment.entities' in df.columns:
        normalized_df['companies'] = df.apply(merge_from_sentiments, axis=1)

    elif 'entities' in df.columns:
        normalized_df['companies'] = df.apply(extract_from_entities, axis=1)

    # Apply transformations to the specified columns
    for col, transform in columns_to_transform.items():
        if col in df.columns:
            normalized_df[col] = df[col].apply(transform)
        else:
            normalized_df[col] = pd.NA

    for col, transform in columns_to_transform_.items():
        if col in normalized_df.columns:
            normalized_df[col] = normalized_df[col].apply(transform)
        else:
            normalized_df[col] = pd.NA

    normalized_df['host'] = normalized_df['url'].apply(get_host)
    normalized_df = normalize_date(normalized_df)
    

    # Another ugly fix.. remove it later. 
    #ignore_dates = ('2025-01-01', '2025-02-20')
    
    date_counts = Counter()
    for date in normalized_df['date']:
        date_str = str(date).split()[0]
        date_counts[date_str] += 1
    if False:
        for k,v in date_counts.most_common(1):
            if v > 100 and k < ignore_dates[0]:
                start_past = True
            elif v > 100 and k > ignore_dates[1]:
                start_future = True
            if k > ignore_dates[0] and k < ignore_dates[1]:
                normalized_df = pd.DataFrame(columns=normalized_df.columns)
                logger.info(f"ignoring dates, skipping")
            elif not start_future and not start_past:
                normalized_df = pd.DataFrame(columns=normalized_df.columns)
                logger.info(f"not started yet, skipping")
            else:
                logger.info(f'not skipping!')
            
    for k,v in date_counts.most_common(1):
        logger.info(f"Article date counts {k}, {v}")

    # Add ranking_signals as a new column
    normalized_df = append_ranking_signals(normalized_df)
    
    # Replace any missing values with pd.NA
    normalized_df = normalized_df.fillna(pd.NA)
    
    save_company_map()

    logger.info(f"normalizing ended")
    return normalized_df

#@time_function
def detect_language_and_split_text(text):
    try:
        language = detect(text)
        #print(f"Detected language: {language}")
    except Exception as e:
        #print(f"Error detecting language: {e}")
        language = "en"  # Fallback to English if detection fails

    try:segmenter = pysbd.Segmenter(language=language, clean=True)
    except:segmenter = pysbd.Segmenter(language="en", clean=True)
    sentences = segmenter.segment(text)
    return sentences

def split_paragraphs_batch(text_batch):
    return [split_into_paragraphs(text) for text in text_batch]

class MultilingualParagraphTokenizer:
    def __init__(self):
        # Unicode line breaks and paragraph separators
        self.paragraph_breaks = [
            '\u2029',  # Paragraph separator
            '\n\n+',   # Multiple newlines
            '\r\n\r\n+',  # Multiple Windows-style newlines
            '(?<=\n)\\s*(?=\n)',  # Empty lines with optional whitespace
        ]
        
        self.break_pattern = '|'.join(self.paragraph_breaks)
        self.splitter = re.compile(self.break_pattern, re.MULTILINE | re.UNICODE)
        
        # List markers across languages
        self.list_markers = r'^[\s]*(?:[•·・\-\*]|\d+[\.\)]|\(\d+\)|\[\d+\])'
        self.list_pattern = re.compile(self.list_markers, re.MULTILINE | re.UNICODE)
        
        # Common noise patterns
        self.noise_patterns = [
            # HTML/XML remnants
            r'^<[^>]+>$',  # Pure HTML tags
            r'^[\s\d.,-_]{1,20}$',  # Just separators or numbers
            r'^\[.*\]$',  # Square bracket content like [image] [file]
            r'^[\d\s]*(?:px|em|rem|%|pt)\s*$',  # CSS measurements
            r'^\s*\{[^\}]*\}\s*$',  # CSS blocks
            r'^\s*@[a-zA-Z]+\s*$',  # @mentions or CSS @rules
            r'^\s*#[a-zA-Z0-9_-]+\s*$',  # Hashtags or CSS IDs
            
            # Common UI elements
            r'^(?:submit|cancel|ok|yes|no|next|prev|continue)$',  # Buttons
            r'^(?:menu|nav|header|footer|copyright)$',  # UI sections
            r'^\s*page\s+\d+\s*(?:of\s+\d+)?\s*$',  # Page numbers
            
            # Dates and times in various formats
            r'^\s*\d{1,4}[-/.]\d{1,2}[-/.]\d{1,4}\s*$',
            r'^\s*\d{1,2}:\d{2}(?::\d{2})?\s*(?:AM|PM|am|pm)?\s*$',
        ]
        self.noise_regex = re.compile('|'.join(self.noise_patterns), re.IGNORECASE | re.UNICODE)
        
        # Common meaningful short phrases
        self.meaningful_shorts = {
            'note:', 'warning:', 'important:', 'tip:', 'caution:', 'danger:',
            'example:', 'summary:', 'conclusion:', 'attention:', 'notice:'
        }

    def normalize_text(self, text: str) -> str:
        """Normalize unicode characters and whitespace"""
        # Normalize unicode to composed form
        text = unicodedata.normalize('NFC', text)
        
        # Replace various unicode whitespace with standard space
        #text = re.sub(r'\s+', ' ', text)
        
        # Normalize newlines
        text = text.replace('\r\n', '\n')
        
        return text.strip()

    @lru_cache(1024*128)
    def is_list_item(self, text: str) -> bool:
        """Check if text starts with a list marker"""
        return bool(self.list_pattern.match(text))

    def is_noise(self, text: str, min_length: int = 50) -> bool:
        """
        Determine if text is likely noise/boilerplate
        
        Args:
            text: Text to check
            min_length: Minimum length for non-list items
        """
        words = detect_and_tokenize_words(text, c_detect(text))

        #text = text.strip().lower()
        
        # Always keep list items
        if self.is_list_item(text):
            return False
            
        # Keep known meaningful short phrases
        # they should be multilingual, but also no guarantee full context is taken!
        #if any(text.startswith(phrase) for phrase in self.meaningful_shorts):
        #    return False
            
        # Check text quality indicators
        word_count = len(words)
        
        # Reject if:
        if len(text) < min_length and word_count < 5 and not self.is_list_item(text):
            unique_words = len(set(words))
            char_ratio = len(re.findall(r'[a-zA-Z\u0400-\u04FF\u4e00-\u9fff]', text)) / len(text) if text else 0
            # Text matches noise patterns
            if self.noise_regex.match(text):
                return True
                
            # Too few unique words relative to length
            if unique_words < 3:
                return True
                
            # Too few actual characters relative to length
            if char_ratio < 0.5:
                return True
                
            # Repetitive character patterns
            char_counts = Counter(text)
            if max(char_counts.values()) / len(text) > 0.4:  # Any character appears too frequently
                return True
        
        return False

    def merge_short_paragraphs(self, paragraphs: List[str], min_length: int = 50) -> List[str]:
        """Merge short paragraphs with the next one if they seem to be part of the same thought"""
        if not paragraphs:
            return []

        result = []
        current = paragraphs[0]
        
        for next_para in paragraphs[1:]:
            # Don't merge list items or meaningful short phrases
            if self.is_list_item(current) or self.is_list_item(next_para):
                # or current[:len(phrase)].lower() in self.meaningful_shorts):
                result.append(current)
                current = next_para
                continue
                
            # If current paragraph is short and doesn't end with sentence-ending punctuation
            if len(current) < min_length and not re.search(r'[.!?。．！？\u3002]\s*$', current):
                current = f"{current} {next_para}"
            else:
                result.append(current)
                current = next_para
        
        result.append(current)
        return result

    async def tokenize(self, text: str, min_length: Optional[int] = 50, 
                merge_short: bool = True, filter_noise: bool = True) -> List[str]:
        """
        Tokenize text into paragraphs
        
        Args:
            text: Input text to tokenize
            min_length: Minimum paragraph length for merging and noise detection
            merge_short: Whether to merge short paragraphs
            filter_noise: Whether to remove noise paragraphs
            
        Returns:
            List of paragraph strings
        """
        if not text:
            return []
            
        # Normalize text
        text = self.normalize_text(text)
        
        # Split into initial paragraphs
        paragraphs = [p.strip() for p in self.splitter.split(text) if p.strip()]
        
        # Filter noise if requested
        if filter_noise:
            paragraphs = [p for p in paragraphs if not self.is_noise(p, min_length)]
        
        # Merge short paragraphs if requested
        #if merge_short:
        #    paragraphs = self.merge_short_paragraphs(paragraphs, min_length)
        
        #paragraphs = await asyncio.gather(*[asyncio.create_task(split_into_paragraphs(p)) for p in paragraphs])
            
        # Create executor inside the function
        #with ThreadPoolExecutor() as executor:
        #    paragraphs_lists = list(executor.map(split_into_paragraphs, paragraphs))
        
        paragraphs_lists = await process_paragraphs(paragraphs)

        paragraphs = [item for sublist in paragraphs_lists for item in sublist]
    
        return paragraphs

paragraph_tokenizer = MultilingualParagraphTokenizer()    

#@async_timer
def tokenize_sentences(row):
    text = row#f"{row['title']} \n {row['content']}"
    return detect_language_and_split_text(text)

#@async_timer
async def process_paragraphs(paragraphs, max_parallel=4, gpu_batch_size=32):
    # Split into chunks for parallel processing
    #logger.info(f"num paragraphs: {len(paragraphs)}")
    chunk_size = len(paragraphs) // max_parallel + 1
    chunks = [paragraphs[i:i + chunk_size] 
             for i in range(0, len(paragraphs), chunk_size)]
    
    async def process_chunk(chunk):
        results = []
        # Process each chunk in GPU-sized batches
        for i in range(0, len(chunk), gpu_batch_size):
            batch = chunk[i:i + gpu_batch_size]
            batch_results = split_into_paragraphs(batch)
            results.extend(batch_results)
            
            if i % (gpu_batch_size * 10) == 0:
                torch.cuda.empty_cache()
        return results

    # Run chunks in parallel
    results = await asyncio.gather(*[
        process_chunk(chunk) for chunk in chunks
    ])
    
    # Flatten results
    return [item for sublist in results for item in sublist]

#@async_timer
def split_into_paragraphs(
    paragraph: str, 
    min_chars: int = 250,
    max_chars: int = 750, # 512..750 will be truncated, so that semantics will be ignored if sbert is used! could mean pool...
) -> List[str]:

    if len(paragraph) <= max_chars:
        return [paragraph]

    sentences = tokenize_sentences(paragraph)
    char_counts = [len(sent) for sent in sentences]
    
    try:
        start_time = datetime.utcnow()
        #logger.info(f"SModel device: {next(smodel.parameters()).device}")
        embeddings = smodel.encode(sentences, normalize_embeddings=True, show_progress_bar=True, batch_size=32, device='cuda',
    convert_to_numpy=True)
        end_time = datetime.utcnow()
        time_diff = (end_time - start_time).total_seconds()
        logger.info(f"smodel embs {time_diff:.2f} seconds for {len(sentences)} sentences")

        similarities = []
        for i in range(len(embeddings) - 1):
            sim = np.dot(embeddings[i], embeddings[i+1])
            similarities.append(sim)
        
        del embeddings
        if random.random() < 0.1:
            torch.cuda.empty_cache()
        
        paragraphs = []
        current_chars = 0
        start_idx = 0
        
        for i, (sim, chars) in enumerate(zip(similarities, char_counts)):
            current_chars += chars
            
            if current_chars > max_chars:
                chunk_sims = similarities[start_idx:i+1]
                best_split = None
                min_sim = float('inf')
                
                for j, chunk_sim in enumerate(chunk_sims):
                    split_idx = start_idx + j + 1
                    chars_before = sum(char_counts[start_idx:split_idx])
                    chars_after = sum(char_counts[split_idx:i+2])
                    
                    if chars_before >= min_chars and chunk_sim < min_sim and chars_after > min_chars:
                        min_sim = chunk_sim
                        best_split = split_idx
                
                if best_split:
                    paragraphs.append(' '.join(sentences[start_idx:best_split]))
                    start_idx = best_split
                    current_chars = sum(char_counts[best_split:i+2])
        
        if start_idx < len(sentences):
            paragraphs.append(' '.join(sentences[start_idx:]))
        
        return paragraphs
    
    except Exception as e:
        logger.error("Full stack trace:")
        logger.error(traceback.format_exc())
        logger.error(e)
        raise e
    
    finally:
        if 'embeddings' in locals():
            del embeddings
        torch.cuda.empty_cache()

@async_timer
async def tokenize_articles(df):
    if df.empty:return df

    batch_size = 100

    texts = [f"{row['title']} \n\n {row['content']}" 
            for _, row in df.iterrows()]
    
    # Process in batches
    batches = [texts[i:i + batch_size] 
              for i in range(0, len(texts), batch_size)]
    
    results = []
    logger.info(f"pre para {datetime.utcnow()}")
    start_time = datetime.utcnow()
    for batch in batches:
        batch_results = await asyncio.gather(*[
            paragraph_tokenizer.tokenize(text) for text in batch
        ])
        results.extend(batch_results)
        torch.cuda.empty_cache()
    end_time = datetime.utcnow()
    time_diff = (end_time - start_time).total_seconds()
    logger.info(f"post para {time_diff:.2f} seconds for {len(results)} sentences")
    
    df['sentences'] = results
    logger.info(f"pre empty sentences drop {len(df)}")
    df = df[df['sentences'].apply(lambda x: len(x) > 0)]
    logger.info(f"post empty sentences drop {len(df)}")
    #logger.info(f"{df.head(2)}")
    #logger.info(f"{df['sentences'].head(2)}")
    #df = df.explode('sentences') # found more elegant way
    #df = df.drop('content', axis=1).reset_index(drop=True)
    #df.to_csv('tmp.tokenized.csv')
    return df

try:
    with open('sample_corpus.pkl', 'rb') as file:
        sample_corpus = pickle.load(file)
except:
    sample_corpus = pd.DataFrame()
    current_time = datetime.utcnow()
    ten_years_ago = current_time - timedelta(days=365*10)
    random_datetimes = [
        ten_years_ago + timedelta(
            days=random.randint(0, (current_time - ten_years_ago).days),
            hours=random.randint(0, 23)
        ) 
        for _ in range(10)
    ]

    # Format the datetimes into the required string format
    random_datehours = sorted([dt.strftime("%Y-%m-%d-%H") for dt in random_datetimes])

    results = []
    for datehour in random_datehours:
        df = asyncio.run(s32df_article(datehour))
        #print(df.head(), len(df), df.columns)
        logger.info(str(len(df)))
        results.append(df.sample(frac=0.1))
        #break
    sample_corpus = pd.concat(results, ignore_index=True)
    logger.info(str(len(sample_corpus)))

    #calibration_embeddings = model.encode(sample_corpus[:1000])
    sample_corpus = normalize_articles(sample_corpus)
    logger.info(str(len(sample_corpus)))
    sample_corpus = asyncio.run(tokenize_articles(sample_corpus))
    logger.info(str(len(sample_corpus)))
    logger.info(str(sample_corpus.head()))

    with open('sample_corpus.pkl', 'wb') as file:
        pickle.dump(sample_corpus, file)

stanza_languages = [
    "af", "am", "ar", "hy", "bg", "bxr", "ca", "zh", "lzh", "cop", "hr", "cs",
    "da", "nl", "en", "et", "fi", "fr", "gl", "de", "got", "el", "he", "hi",
    "hu", "id", "ga", "it", "ja", "ko", "km", "la", "lv", "lt", "olo", "mr",
    "my", "nb", "nn", "cu", "fro", "orv", "fa", "pl", "pt", "ro", "ru", "sr",
    "sk", "sl", "es", "sv", "ta", "te", "th", "tr", "uk", "ur", "vi", "wo",
    "yo", "is", "mt", "sw", "ug"
]

@lru_cache(maxsize=5)  # Cache up to 3 models in memory
def load_stanza_model(lang_code):
    logger.info(f"Loading Stanza model for: {lang_code}")
    stanza.download(lang_code)  # Ensure the model is downloaded
    return stanza.Pipeline(lang_code, processors='tokenize', use_gpu=False)

@lru_cache(maxsize=1024*1024)
def detect_and_tokenize_words(text):
    try:
        try:stanza_lang = detect(text)  # Detect the language
        except:stanza_lang = 'en'
        
        # Load the Stanza model (cached if recently used)
        nlp = load_stanza_model(stanza_lang)
        doc = nlp(text)
        
        # Extract tokens
        tokens = [word.text for sentence in doc.sentences for word in sentence.words]
        return tokens
    except Exception as e:
        return []

CHAR_BASED_LANGS = {'zh', 'ja', 'ko', 'th', 'my', 'lo', 'km'}

def detect_and_tokenize_words(text, detected_lang):
    words = text.strip().split()
    avg_word_length = sum(len(word) for word in words) / len(words)
    
    if detected_lang in CHAR_BASED_LANGS or avg_word_length > 15:
        return list(text)  # Character-level tokenization
    else:
        return text.split()  # Word-level tokenization for other languages

#@time_function
def find_matches(full_text, start, chars_between, end):
    """Find all matches and their character count differences"""
    pattern = f"{re.escape(start)}(.+?){re.escape(end)}"
    matches = []
    
    for match in re.finditer(pattern, full_text, re.DOTALL):
        middle = match.group(1)
        diff = abs(len(middle) - chars_between)
        matches.append((diff, match.group(0)))
        
    return matches
    
#@time_function
def create_fragment(full_text, text, detected_lang):
    """Create fragment with optimal word counts to ensure unique best match"""
        
    words = detect_and_tokenize_words(text, detected_lang)
    
    # Try different word counts from 1 to 5 for both start and end
    best_result = None
    
    for num_words in range(1, 6):
        start = ' '.join(words[:num_words])
        end = ' '.join(words[-num_words:])
        chars_between = len(text) - len(start) - len(end)
        
        matches = find_matches(full_text, start, chars_between, end)
        if not matches:
            continue
            
        min_diff = min(match[0] for match in matches)
        best_matches = [m for m in matches if m[0] == min_diff]
        
        # If we found a unique best match
        if len(best_matches) == 1 or len(set(best_matches))==1 or num_words == 5:
            best_result = '|'.join([start, str(chars_between), end])
            break
        if best_result is None:
            best_result = '|'.join([start, str(chars_between), end])
            
    if best_result is None:
        #logger.info(f"no fragment: {text[:111]}")
        pass
    return best_result or text

@lru_cache(1024*128)
def c_detect(text):
    try:detected_lang = detect(text)
    except:detected_lang = 'en'
    return detected_lang

@async_timer
async def generate_fragments(df):
    if df.empty:return df
    def process_row(row):
        full_text = f"{row['title']}\n\n{row['content']}"
        detected_lang = c_detect(full_text)
        # Process each sentence in the sentences list
        fragments = []
        for sentence in row['sentences']:
            fragment = create_fragment(full_text, sentence, detected_lang)
            fragments.append(fragment)
        return fragments
    
    df['fragments'] = df.apply(process_row, axis=1)
    return df

#@time_function
def binary_quantize(embeddings):
    return (embeddings > 0).astype(np.uint8)
    #scores, indices = corpus_index.search(query_embeddings, k)
    #[[corpus_index.reconstruct(idx.item()) for idx in query_indices] for query_indices in indices]
    
@async_timer
async def binary_quantize_df(df):
    df['quantized_embeddings'] = df['embeddings'].apply(lambda x: binary_quantize(np.array(x)))
    logger.info(f"binarized {str(df.head())}")
    return df

@time_function
def scalar_quantize(embeddings):
    if embeddings.ndim == 1:
        embedding_dim = embeddings.shape[0]
    elif embeddings.ndim == 2:
        embedding_dim = embeddings.shape[1]
    else:
        raise ValueError("Embeddings should be 1D or 2D numpy array")
    
    calibration_embeddings = np.array([ # assumes normalized embeddings !!!
        [-1] * embedding_dim,  # Min values
        [1] * embedding_dim    # Max values
    ])
    
    int8_embeddings = quantize_embeddings(
        embeddings,
        precision="int8",
        calibration_embeddings=calibration_embeddings,
    )
    return int8_embeddings

@time_function
def embed_sentences_(sentences, task = "retrieval.passage", matryushka_dim = 512):
    """ tasks: 
        retrieval.query
        retrieval.passage
        separation
        classification
        text-matching
    """
    #logger.info(f"Model device: {next(model.parameters()).device}")
    results = model.encode(sentences, normalize_embeddings=True, show_progress_bar=True, batch_size=32, device='cuda',
    convert_to_numpy=True, task=task, prompt_name=task)
    results = torch.tensor(results).cpu().detach().numpy()
    
    # Clear GPU cache
    if torch.cuda.is_available():
       torch.cuda.empty_cache()

    return results[:, :matryushka_dim]
   
with open('company_descriptions.pkl','rb') as f:
    company_descriptions = pickle.load(f)

company_descriptions = sorted(company_descriptions.items())
company_embeddings = [x for x, _ in company_descriptions], embed_sentences_([x for _, x in company_descriptions])


@time_function
def embed_sentences(sentences, batch_size=2048):
    results = []
    num_batches = (len(sentences) + batch_size - 1) // batch_size
    
    logger.info(f"Processing {len(sentences)} sentences in {num_batches} batches")
    start_time = datetime.utcnow()
    
    for i in range(0, len(sentences), batch_size):
        batch = sentences[i:i + batch_size]
        batch_start = datetime.utcnow()
        
        try:
            batch_results = embed_sentences_(batch)
                
            results.extend(batch_results)
            
            # Log progress
            batch_time = (datetime.utcnow() - batch_start).total_seconds()
            total_time = (datetime.utcnow() - start_time).total_seconds()
            batch_num = i // batch_size + 1
            
            logger.info(
                f"Batch {batch_num}/{num_batches} complete. "
                f"Batch time: {batch_time:.2f}s, "
                f"Total time: {total_time:.2f}s, "
                f"GPU memory: {torch.cuda.memory_allocated()/1024**2:.2f}MB"
            )
            
        except Exception as e:
            logger.error(f"Error processing batch {i//batch_size + 1}: {str(e)}")
            raise
    
    return np.array(results)

@async_timer
async def embed_df_sentences(df):
    if df.empty:return df
    logger.info(f"HASH {hash(str(df))}")
    all_sentences = [sentence for sentences in df['sentences'] for sentence in sentences]
    logger.info(f"EMBEDDING # sents: {len(all_sentences)}")
    start_time = datetime.utcnow()
    sentence_embeddings = embed_sentences(all_sentences)
    sentence_embeddings = binary_quantize(sentence_embeddings) # here instead of separate pipe
    end_time = datetime.utcnow()
    time_diff = (end_time - start_time).total_seconds()
    logger.info(f"EMBEDD {time_diff:.2f} seconds for {len(all_sentences)} sentences")
    article_sentence_counts = df['sentences'].apply(len).tolist()
    article_start_indices = np.cumsum([0] + article_sentence_counts[:-1])

    article_embeddings = np.empty(len(df), dtype=object)
    
    try:
        for i, count in enumerate(tqdm(article_sentence_counts, desc="Reassigning embeddings to articles")):
            start_idx = article_start_indices[i]
            end_idx = start_idx + count
            article_embeddings[i] = sentence_embeddings[start_idx:end_idx] #['dense_vecs'] for bge-m3, moved to embed.
    except Exception as e:
        logger.error("Full stack trace:")
        logger.error(traceback.format_exc())
        logger.error(e)
        raise e
        
    # Add the article embeddings as a new column in the DataFrame
    df.loc[:, 'embeddings'] = article_embeddings
    
    return df

# Optional: CPU preprocessing function if needed
def preprocess_sentences(sentences):
    # Add any CPU-intensive preprocessing here
    # Example: cleaning, tokenization, etc.
    return sentences

def date_to_string(x):
    return x if isinstance(x, str) else x.strftime('%Y-%m-%d')

def upsert_points_with_retry(points, batch_size=3000, max_retries=3, retry_delay=1):
    if len(points)==0:
        logger.info("0000000000000 ?? ")
        return
    vectors, metadata = zip(*points)
    vectors = np.packbits(np.array(vectors, dtype=np.uint8), axis=1)
    v0 = vectors[0].copy()
    m0 = metadata[0].copy()
    for meta in metadata:
        meta["date"] = str(meta["date"]).split()[0]
        del meta["del"]
    #send_vectors(vectors, metadata)
    for i in range(0, len(vectors), batch_size):
        batch_vectors = vectors[i:i + batch_size]
        batch_metadata = metadata[i:i + batch_size]
        
        #print("COMPANIES:", batch_metadata[0]["companies"])
        # Try up to 3 times
        for retry in range(max_retries):
            try:
                send_vectors(batch_vectors, batch_metadata)
                break  # Success, move to next batch
            except Exception as e:
                if retry == 2:  # Failed all 3 attempts
                    logger.info(f"Failed batch starting at index {i}: {str(e)}")
                else:
                    time.sleep(retry_delay * (i+1))  # Wait 1 second before retry
    logger.info("Common sense check, should find what it inserted!")
    logger.info(f"SEARCH VECTOR RESULTS:, {v0}, {m0}")
    logger.info(str(search_vectors(vector=v0, start_date=m0["date"], end_date=m0["date"], k=3)))
    logger.info('SEARCH TEXT RESULTS:')
    logger.info(str(search_vectors(query=m0["del"], start_date=m0["date"], end_date=m0["date"], k=3)))
    if m0["companies"]:
        logger.info(f"m0: {str(m0)}")
        logger.info('SEARCH COMPANY VECTOR RESULTS:')
        logger.info(f"{str(search_vectors(vector=None, companies=m0["companies"][:1], start_date=m0["date"], end_date=m0["date"], k=3))}")
            
@async_timer
async def store_embeddings(df):#encodings, source_sentences_ids):
    if df.empty:
        if processing_runs.keys():
            k = min(processing_runs.keys())
            del processing_runs[k]
        return df
    points = []
    
    df_exploded = df.explode(['sentences', 'embeddings', 'fragments'])#, 'quantized_embeddings'])#, 'sent_pointers'])
    df_exploded.reset_index(drop=True, inplace=True)
    #q_encodings = df.explode('quantized_embeddings')
    encodings = df_exploded['embeddings']#.tolist()
    sentences = df_exploded['sentences']#.tolist()
    companies = df_exploded['companies']#.tolist()
    fragments = df_exploded['fragments']#.tolist()
    languageCodes = df_exploded['languageCode']
    dates = df_exploded['date']#.tolist()
    source_sentences_ids = df_exploded['id']#.tolist()
    #pointers = df_exploded['sent_pointers']

    date_counts = Counter()
    #for i, (ssid, date, sentence, pointer) in enumerate(zip(source_sentences_ids, dates, sentences, pointers)):
    for i, (ssid, date, sentence, fragment, text_companies, languageCode) in enumerate(zip(source_sentences_ids, dates, sentences, fragments, companies, languageCodes)):
        if dupe_sent_detector.add(sentence):
            #logger.info(f"dupe {sentence[:100]}")
            #logger.info(f"dupe ssid fragment: {ssid} {fragment}")
            continue
        
        sentence_companies = filter_companies(sentence, text_companies, languageCode, encodings[i])
        #sentence_companies = [usearch_client.metadata_store._get_company_id(c) for c in sentence_companies]
        payload = {"id": ssid, "date": date, "text": fragment, "companies": sentence_companies, "del": sentence, "lc": languageCode}#, "frag": pointer} 
        #dense_vector = encodings[i].tolist()#['dense_vecs'] moved to embed code
        #sparse_vector = encodings['sparse_vecs'][i]
        #colbert_vector = np.mean(encodings['colbert_vecs'][i], axis=0).tolist()  # Average ColBERT vectors
        
        date_counts[str(date).split()[0]]+=1
        points.append((encodings[i], payload))
    
    if points:
        save_global_idx()
        for k,v in date_counts.most_common(10):
            logger.info(f"Date counts {k}, {v}")
    
    logger.info("ready to save cache")
    
    with open(DUPE_CACHE_FILE, 'wb') as f:
        pickle.dump(dupe_sent_detector, f)

    logger.info("after pointing")
    if len(points)==0:
        logger.info(f"All sents dupes? {len(points)}")
    else:
        logger.info(f"Not, all sents dupes? {len(points)}")
    # Insert data into the collection

    while not test_vdb():
        time.sleep(3)
        print('Check Vector DB!')

    upsert_points_with_retry(points)
    logger.info("upsert done")

    k = min(processing_runs.keys())
    del processing_runs[k]
    logger.info("cleaned tmp")

    #logger.info(generate_latest().decode())


class SlidingUSearchIndex:
    def __init__(
        self,
        ndim,
        file_path=USEARCH_file_path,
        aids_map_path=USEARCH_aids_map_path
    ):
        self.ndim = ndim
        self.file_path = file_path
        self.aids_map_path = aids_map_path

        self.index = Index(ndim=self.ndim, metric=MetricKind.Hamming, dtype=ScalarKind.B1)

        if os.path.exists(file_path):
            self.index.load(file_path)
        else:
            logger.info(f"No saved index found at {file_path}. Initializing new index.")

        if os.path.exists(aids_map_path):
            with open(aids_map_path, 'rb') as f:
                self.aids_map = pickle.load(f)
        else:
            self.aids_map = {}

        self.last_update = datetime.now()
        self.lock = Lock()

    def save_state(self):
        self.index.save(self.file_path)
        logger.info(f"Saved USearch index to {self.file_path}")
        with open(self.aids_map_path, 'wb') as f:
            pickle.dump(self.aids_map, f)
        logger.info(f"Saved key map to {self.aids_map_path}")

    def check_and_update(self):
        now = datetime.now()
        with self.lock:
            if now - self.last_update > timedelta(days=1):
                
                ten_mil = 15000000  # 10M semantic units for 2+ day window
                current_size = len(self.aids_map)
                
                if current_size > ten_mil:
                    logger.info("SLIDING DEDUPE INDEXES")
                    new_aids_map = {}
                    new_index = self.index.__class__()  # Create new index of same type   
                    new_index = Index(ndim=self.ndim, metric=MetricKind.Hamming, dtype=ScalarKind.B1)                 
                    keys_to_keep = sorted(list(self.aids_map.keys()), reverse=True)[:int(0.8 * ten_mil)]
                    filtered_embeddings = []
                    for k in keys_to_keep:
                        filtered_embeddings.append(self.index[k])
                        new_aids_map[k] = self.aids_map[k]
                    new_index.add(np.array(keys_to_keep, dtype=np.uint64), np.array(filtered_embeddings), copy=True)
                        
                    keys_removed = set(self.aids_map.keys()) - set(keys_to_keep)
                    with open('keys_log.log', 'a') as f:
                        f.write('\n'.join(['del ' + str(k) for k in keys_removed])+'\n')

                    self.aids_map = new_aids_map
                    self.index = new_index
                    logger.info(f"Index updated: kept {len(keys_to_keep)} vectors, removed {len(keys_removed)} vectors")
                    self.last_update = now

    def add_embeddings(self, embeddings_dict):
        self.check_and_update()
        with self.lock:
            keys = []
            embeddings = []
            for tuple_key, embedding in embeddings_dict.items():
                key, aid = tuple_key
                self.aids_map[key] = aid
                keys.append(key)
                embeddings.append(embedding.flatten())
            with open('keys_log.log', 'a') as f:
                f.write('\n'.join(['add '+str(key) for key in keys])+'\n')
            try:
                keys = np.array(keys, dtype=np.uint64)
                embeddings = np.packbits(np.array(embeddings, dtype=np.uint8), axis=1)
                mask = np.array([key not in self.index for key in keys])
                if not np.any(mask):
                    logger.info('DID I RESTART THE PROCESS? ALL EMBS ARE ALREADY IN THE SLIDING INDEX')
                    return  # All keys already exist
                filtered_keys = keys[mask]
                filtered_embeddings = embeddings[mask]    
                self.index.add(filtered_keys, filtered_embeddings, copy=True)
            except Exception as e:
                logger.error(f"Error in semantic_search_usearch add embs: {str(e)}")
                logger.error("Full stack trace:")
                logger.error(traceback.format_exc())
            
    def search(self, query_embeddings, top_k=100):
        #self.check_and_update()
        with self.lock:
            if query_embeddings.ndim == 1:
                query_embeddings = query_embeddings.reshape(1, -1).astype(np.uint8)
            query_embeddings = np.packbits(np.array(query_embeddings, dtype=np.uint8), axis=1)
            results = self.index.search(query_embeddings, top_k)
            return [[(self.aids_map[hit.key], hit.distance) for hit in result] for result in results]


@time_function
def find_similar_sentences(sentence_embeddings, search_limit=100, similarity_threshold=0.2):

    try:
        results = persistent_search.search(sentence_embeddings, search_limit)
        """
        results, _ = semantic_search_usearch(
            sentence_embeddings,
            corpus_index=persistent_search.query_index,
            corpus_precision="binary",
            top_k=search_limit,
            rescore=True,
            rescore_multiplier=4,
            exact=True,
            output_index=False,
        )
        """

    except Exception as e:
        logger.error(f"Error in find_similar_sentences: {str(e)}")
        logger.error("Full stack trace:")
        logger.error(traceback.format_exc())
        logger.error(f"q_sentence_embeddings type: {type(sentence_embeddings)}")
        logger.error(f"q_sentence_embeddings shape: {sentence_embeddings.shape}")
        logger.error(f"q_sentence_embeddings dtype: {sentence_embeddings.dtype}")
        logger.error(f"sentence_embeddings type: {type(sentence_embeddings)}")
        logger.error(f"sentence_embeddings shape: {sentence_embeddings.shape}")
        logger.error(f"sentence_embeddings dtype: {sentence_embeddings.dtype}")
        raise

    similar_sentence_indices = [
        [aid for aid, distance in result if distance <= similarity_threshold]
        for result in results
    ]

    return similar_sentence_indices


@time_function
def find_duplicate_articles(article_sentence_counts, keys, similar_sentence_indices, similarity_threshold=0.8):
    duplicates = []
    article_start_indices = np.cumsum([0] + article_sentence_counts[:-1])
    
    for i, count in enumerate(tqdm(article_sentence_counts)):
        article_similar_sentences = similar_sentence_indices[article_start_indices[i]:article_start_indices[i]+count]
        flat_similar_sentences = [idx for sublist in article_similar_sentences for idx in sublist]
        
        if not flat_similar_sentences:
            continue
        
        #similar_article_indices = np.searchsorted(article_start_indices, flat_similar_sentences, side='right') - 1
        #unique, counts = np.unique(similar_article_indices, return_counts=True)
        
        for article_id, counts in Counter(flat_similar_sentences).most_common():
            similarity_ratio = counts / count
            if similarity_ratio < similarity_threshold:break

            if article_id != keys[article_start_indices[i]] and similarity_ratio >= similarity_threshold:
                duplicates.append((article_id, keys[article_start_indices[i]]))
            
    return duplicates

persistent_search = SlidingUSearchIndex(ndim=EMBEDDING_DIMENSION)  # SBERT here! not any more, now jina
# q_embs have 384, so not compressed truly

@async_timer
async def deduplicate_articles(df, similarity_threshold=0.8, search_limit=100):
    if df.empty:return df
    logger.info("Deduplication started")
    
    df = df.sort_values(by='date', ascending=True)
    df['date'] = pd.to_datetime(df['date'])
    df = df.reset_index(drop=True)
    
    counter = count()
    aid_sentence_embeddings = [(aid, np.array(sentence)) for aid, sentences in zip(df['id'], df['embeddings']) for sentence in sentences]
    corpus_precision = "binary"
    sentence_embeddings = np.array([emb for _, emb in aid_sentence_embeddings])
    #q_sentence_embeddings = quantize_embeddings(sentence_embeddings, precision=corpus_precision)
    #q_sentence_embeddings = binary_quantize(sentence_embeddings)
    q_sentence_embeddings = sentence_embeddings # now all are quantized!
    logger.info(f"Shape of q_sentence_embeddings: {q_sentence_embeddings.shape}")
    #keys = [':'.join([str(increment_global_idx(), str(i))]) for i, _, _ in sentence_embeddings]
    kembs = {(increment_global_idx(), aid): sentence for sentence, (aid, _) in zip(q_sentence_embeddings, aid_sentence_embeddings)}
    #for i, (k,v) in enumerate(kembs.items()):
    #    print(i, k,v)
    #    if i > 3:break
    logger.info('starting adding embs')
    persistent_search.add_embeddings(kembs)
    logger.info('done adding embs')
    persistent_search.save_state()
    logger.info('done saving state')

    logger.info('starting sim search')
    similar_sentence_indices = find_similar_sentences(q_sentence_embeddings, search_limit)
    logger.info('ended sims')
    article_sentence_counts = df['sentences'].apply(len).tolist()
    logger.info('starting dupes')
    duplicate_pairs = find_duplicate_articles(article_sentence_counts, np.array([aid for _, aid in list(kembs.keys())]), similar_sentence_indices, similarity_threshold)
    logger.info('ended dupes')
    
    duplicate_buffer = []

    df['is_duplicate'] = False
    for pair in duplicate_pairs:
        #logger.info(f"DUPLICATE PAIR: {pair[0]} : {pair[1]}")
        #df.loc[pair[1], 'is_duplicate'] = True
        #df[df['id'] == pair[1]]['is_duplicate'] = True
        df.loc[df['id'].astype(str) == str(pair[1]), 'is_duplicate'] = True
        duplicate_buffer.append(f"{pair[0]} : {pair[1]}")

    if duplicate_buffer:
        with open('duplicates.log', 'a') as f:
            f.write('\n'.join(duplicate_buffer) + '\n')

    #logger.info(str(df[df['is_duplicate'] == True]))

    #TODO: store pair info in separate dict.. use counts later
    
    logger.info(f"Deduplication finished, from {len(df)} articles")
    df = df[~df['is_duplicate']].drop('is_duplicate', axis=1).reset_index(drop=True)
    logger.info(f"Deduplication finished, to {len(df)} articles")
    
    return df

### usearch approach above
### END OF DEDUPLICATION LOGIC


class LRUDupes:
   def __init__(self, capacity=10_000_000):
       self.cache = LRUCache(maxsize=capacity)
   def add(self, item) -> bool:
       item_hash = xxhash.xxh128(str(item).lower().encode()).hexdigest()
       is_duplicate = item_hash in self.cache
       self.cache[item_hash] = True
       return is_duplicate

try:
    with open(DUPE_CACHE_FILE, 'rb') as f:
        dupe_sent_detector = pickle.load(f)
except:
    dupe_sent_detector = LRUDupes(capacity=10_000_000)    

logger.info(f"dupe test {dupe_sent_detector.add('dummy input')}")


class Pipeline:
    def __init__(self, max_queue_size=3):
        self.queues = {}
        self.max_queue_size = max_queue_size

    def add_stage(self, config, step):
        for queue in [config['input_queue'], config['output_queue']]:
            if queue not in self.queues:
                self.queues[queue] = asyncio.Queue(maxsize=self.max_queue_size)
        input_queue = self.queues[config['input_queue']]
        output_queue = self.queues[config['output_queue']]
        return Stage(config, step, input_queue, config['process_func'], output_queue, pipeline=self)

class Stage:
    def __init__(self, config, step, input_queue, process_func, output_queue, pipeline):
        self.config = config
        self.step = step
        self.input_queue = input_queue
        self.process_func = globals()[process_func]
        self.output_queue = output_queue
        self.pipeline = pipeline

    async def run(self):
        while True:
            try:
                logger.info(f"{self.step} Pipeline running")
                item = await self.input_queue.get()
                logger.info(f"{self.step} Got item {str(item)[:200]}")

                try:
                    process_task = asyncio.create_task(self.process_func(item))
                    result = await process_task
                    logger.info(f"{self.step} Got result {hash(str(result))}")
                except Exception as e:
                    logger.error(f"{self.step} Processing error: {e}")
                    self.input_queue.task_done()  # Mark task as done even if it failed
                    raise
                    #continue  # Skip to next item

                try:
                    await self.output_queue.put(result)
                except asyncio.QueueFull:
                    logger.warning(f"{self.step} Output queue full, waiting...")
                    await asyncio.sleep(0.3)

                if self.output_queue and self.step == len(PIPELINE_STEPS)-1:
                    await self.output_queue.get()
                if self.step == 1:
                    logger.info(f"Pipeline reposting {self.step} to start")
                    await self.pipeline.queues[PIPELINE_STEPS[0]['input_queue']].put(None)

            except asyncio.CancelledError:
                logger.info(f"{self.step} Stage cancelled")
                break
            except Exception as e:
                logger.error(f"{self.step} Unexpected error in stage: {e}", exc_info=True)
                await asyncio.sleep(1)  # Prevent rapid error loops

async def main():
    logger.info("Main function started")
    pipeline, stages = await create_pipeline(PIPELINE_STEPS)
    running_tasks = set()

    # Start initial tasks
    for stage in stages:
        task = asyncio.create_task(stage.run())
        running_tasks.add(task)
        task.add_done_callback(running_tasks.discard)

    logger.info(f"Created initial {len(running_tasks)} stage tasks")
    await pipeline.queues[PIPELINE_STEPS[0]['input_queue']].put(None)

    try:
        while running_tasks:
            done, _ = await asyncio.wait(running_tasks, return_when=asyncio.FIRST_COMPLETED)
            
            for task in done:
                try:
                    await task
                except Exception as e:
                    logger.error(f"Task error: {e}")

            # Restart any stopped stages
            for stage in stages:
                if not any(t for t in running_tasks if t.get_coro().__name__ == stage.run.__name__):
                    new_task = asyncio.create_task(stage.run())
                    running_tasks.add(new_task)
                    new_task.add_done_callback(running_tasks.discard)
                    logger.info(f"Restarted stage {stage.step}")

    except asyncio.CancelledError:
        logger.info("Main task cancelled")
    finally:
        # Clean up
        for task in running_tasks:
            task.cancel()
        if running_tasks:
            await asyncio.wait(running_tasks)
        
        # Clean up queues
        for queue in pipeline.queues.values():
            while not queue.empty():
                try:
                    queue.get_nowait()
                    queue.task_done()
                except asyncio.QueueEmpty:
                    break
                
        logger.info("removing partially processed items")
        for k, file_ids in processing_runs.items():
            for file_id in file_ids:
                if file_id in processed_runs:
                    del processed_runs[file_id]
        save_processed_runs(processed_runs)
        save_processing_runs({})

        #kill_pt_main_thread()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        #FIXME: actually save outputs from each pipe for the next run! otherwise there is deduplication conflict in LRU, etc.
                
        logger.info("Pipeline shutdown complete")


        with open('ln_or_q.txt','w') as w:
            w.write('ln' if which=='q' else 'q')
        
async def create_pipeline(pipeline_steps):
    logger.info("Creating pipeline")
    pipeline = Pipeline()
    stages = [pipeline.add_stage(config, i) for i, config in enumerate(pipeline_steps)]
    logger.info(f"Pipeline created with {len(stages)} stages")
    return pipeline, stages


def handle_signals(sig, frame):
    logger.info(f"Received signal {sig}")
    
    # Cancel all tasks except the current one
    loop = asyncio.get_running_loop()
    tasks = [t for t in asyncio.all_tasks(loop=loop) if t is not asyncio.current_task()]
    
    if tasks:
        logger.info(f"Cancelling {len(tasks)} pending tasks")
        for task in tasks:
            task.cancel()
        
        # Wait for tasks to acknowledge cancellation
        loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
    
    logger.info("Shutdown complete")
    loop.stop()
    sys.exit(0)  # Explicitly exi

if __name__ == "__main__":
    # Register signal handlers
    signal.signal(signal.SIGTERM, handle_signals)
    signal.signal(signal.SIGINT, handle_signals)
    
    multiprocessing.set_start_method('spawn', force=True)
    logger.info("Script started")
    asyncio.run(main())
    logger.info("Script finished")
    
