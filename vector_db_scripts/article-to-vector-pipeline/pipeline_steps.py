PIPELINE_STEPS = [
    {
        "input_queue": "date_and_hour",
        "process_func": "s32df_article", # each source can have its pipe, super easy to integrate now! 
        "output_queue": "df_articles_all",
    },
    {
        # TODO: here separate logic for bad articles, simply clone this pipe, invert the filter, and run separate batch! use in ML
        # DO IT ON SEPARATE INSTANCE! then can keep db/stores names the same! 
        "input_queue": "df_articles_all",
        "process_func": "sj_domain_filter", 
        "output_queue": "df_articles",
    },
    {
        "input_queue": "df_articles",
        #"additional_inputs": ["ranking_signals"], # domain, author, topical, graph, or maybe these should be dynamic, so attributes in graph
        # moved to within function, loading each time. 
        # we have chicken egg problem here.. initially graph is empty and scores are used to filter out stuff going into the graph!
        # given graph is filled from these analysis, ranks need to enter it from here as well: use file rankings to be updated from graph data regularly
        "process_func": "normalize_articles", # DO NOT normalize contexts with LLM. do it later after clustering and filtering, on much smaller dataset!
        "output_queue": "normalized_articles", # select consistent columns
    },
    {
        "input_queue": "normalized_articles",
        "process_func": "tokenize_articles",
        "output_queue": "tokenized_articles",
    },
    {
        "input_queue": "tokenized_articles",
        "process_func": "embed_df_sentences",
        "output_queue": "df_with_embeddings", # I keep ids / pointers to source sentences+article_ids, to be used later
    },
#    {
#        "input_queue": "df_with_embeddings",
#        "process_func": "binary_quantize_df",
#        "output_queue": "df_with_q_embeddings",
#    },
    {
        "input_queue": "df_with_embeddings",
        #"additional_inputs": ["day_over"], # function day_over checks if full day is in processed_hours keys! otherwise requeue
        # day_over should be at previous step, not first one! currently only first one is stored in json, processed_hours
        # also, duplicates should catch surrounding day, so 48 hours maybe, maybe full range? to reduce false positive, purge at times
        "process_func": "deduplicate_articles",
        "output_queue": "deduplicated_df", # tokenized article contains ranks, so pass them here to next step to calculate weights!
    },
    {
        "input_queue": "deduplicated_df", # i have ids to source sentences+article_ids
        "process_func": "generate_fragments",
        "output_queue": "df_sent_pointers",
    },
    {
        "input_queue": "df_sent_pointers", # i have ids to source sentences+article_ids
        #"additional_inputs": ["quantized_embeddings"],
        "process_func": "store_embeddings",
        "output_queue": "qdrant_updates_log",
    },
]
[
    {
        "input_queue": "deduplicated_df_with_binary_embeds",
        #"additional_inputs": ["tokenized_articles", "day_over"], # function day_over checks if full day is in processed_hours keys! otherwise requeue
        # i now pass full df around, so no need for extra input
        "process_func": "cluster_sentences",
        "output_queue": "clustered_sentences", # tokenized article contains ranks, so pass them here to next step to calculate weights!
    },
    {
        "input_queue": "clustered_sentences", # hourly or daily? hourly is a perfect trade-off between performance and coverage! 
        #"additional_inputs": ["tokenized_articles"],
        "process_func": "calculate_weights",
        "output_queue": "weighted_clusters",
    },
    {
        "input_queue": "weighted_clusters",
        #"additional_inputs": ["finance_concepts"], # make sure top clusters cover all important concepts and companies. 
        # weights are used to filter good if plenty of info, otherwise include available for low resource companies, etc. 
        "process_func": "select_top_clusters",
        "output_queue": "top_clusters",
    },
    {
        "input_queue": "top_clusters",
        "process_func": "diversify_statements", # use MMR or similar. MMR is super slow O(k^3), so improvize. 
        "output_queue": "diversified_cluster_statements",
    },
    {
        "input_queue": "diversified_cluster_statements",
        "process_func": "enrich_contexts",
        "output_queue": "enriched_contexts",
    },
    {
        "input_queue": "enriched_contexts",
        "additional_inputs": ["top_clusters"],
        "process_func": "extract_information",
        "output_queue": "extracted_info",
    },
    {
        "input_queue": "extracted_info",
        "process_func": "embed_triplets",
        "output_queue": "tri_embeddings", # I keep ids / pointers to source sentences+article_ids, to be used later
    },
    {
        "input_queue": "tri_embeddings",
        "process_func": "quantize_tri_embeddings",
        "output_queue": "quantized_tri_embeddings",
    },
    {
        "input_queue": "extracted_info",
        #"additional_inputs": ["quantized_tri_embeddings"],
        "process_func": "update_knowledge_graph",
        "output_queue": "kg_updates_log",
    },
    {
        "input_queue": "dummy_input", # i have ids to source sentences+article_ids
        #additional_inputs": ["finance_concepts"],
        "process_func": "kg_analysis",
        "output_queue": "kg_analysis_log_or_output",
    },
    {
        "input_queue": "dummy_input", # i have ids to source sentences+article_ids
        "process_func": "get_new_and_trendy_kg_concepts",
        "output_queue": "new_and_trendy_concepts",
    },

    #self_improving_prompt -> apply on top selection of referenced sources
    #watson enrichment, apply on top selection
]
