There are a few bigger files on the server itself which are called by the script. Best to fetch them from server as code keeps updating them with new data, i.e. Company name mappings `company_map.pkl`

Also, there is a folder on the server with many experiments and benchmark files. 

At the end of processing, vectors are stored in vector db instance (is replacable, was qdrant, now in-house solution). Vector DB is on a separate instance and posted via REST API. 

Run:

```
nohup python -u nlp_pipeline.py >> nohup.log 2>&1 &
```


Example code fetching data process with this pipeline and stored in the vector db is `https://github.com/EqubotAI/vector-db/blob/main/vector_sentiments.py`
