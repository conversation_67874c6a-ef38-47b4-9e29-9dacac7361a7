import pandas as pd
from eq_common_utils.utils.config.es_config import es_config
from datetime import datetime, timedelta

es_conn = es_config(env='pre')
def get_last_friday():
    today = datetime.now()
    # Calculate days until last Friday (if today is Saturday, it will be 1; if today is Friday, it will be 0)
    days_to_subtract = (today.weekday() - 4) % 7
    # If today is Friday, we want the previous Friday, so add 7
    if days_to_subtract == 0:
        days_to_subtract = 7
    last_friday = today - timedelta(days=days_to_subtract)
    return last_friday.strftime('%Y-%m-%d')

date_friday = get_last_friday()
base_ticker_list = ['TLT', 'EFA', 'EEM', 'BNDX', 'XME', 'SHY', 'MQFIUSTY', 'MQFIUSUS', 'EMB', 'GLD', 'IYR', 'QQQ', 'IWM', 'EWJ', 'TIP', 'MQFIUSTU', 'HYG', 'LQD', 'XLE', 'SPY', 'HSMETYSN']
aigo_ticker_list = [ticker for ticker in base_ticker_list if ticker not in ['TLT', 'SHY']]
aigo8_ticker_list = [ticker for ticker in base_ticker_list if ticker not in ['MQFIUSUS', 'MQFIUSTY', 'MQFIUSTU']]

base_file = f"/home/<USER>/rishij/AIGO/prereview_platform_{date_friday}.xlsx"
aigo_cleaned = f"/home/<USER>/rishij/AIGO/AIGO_WORDCLOUD_{date_friday.replace('-','')}.xlsx"
aigo8_cleaned = f"/home/<USER>/rishij/AIGO/AIGO8_WORDCLOUD_{date_friday.replace('-','')}.xlsx"

base = pd.ExcelFile(base_file)
aigo = pd.ExcelFile(aigo_cleaned)
aigo8 = pd.ExcelFile(aigo8_cleaned)

aigo_names = {'SPY': 'MQFTUSE1',
              'QQQ': 'MQFTUSN1',
              'IWM': 'MQFTUSS1'}

def get_ignore_df(start_date,end_date):
    index = 'concept_ignorelist'
    sources = ["name", "group"]
    query = {
        "query": {
            "bool": {
                "must": {
                    "range": {
                        "updated_at": {
                            "gte": f"{start_date}T00:00:00.000000",
                            "lte": f"{end_date}T23:59:59.999999"
                        }
                    }
                }
            }
        },
        "_source": sources
    }
    results = es_conn.run_query(query,index)
    df = pd.json_normalize(results['hits']['hits'])
    return df

def omission_check():
    failed_tickers_aigo = []
    failed_tickers_aigo8 = []

    ## AIGO Check
    # Iterate through each sheet in aigo Excel
    print('Running AIGO Check')
    for ticker in aigo_ticker_list:
        # Get corresponding sheets from both excel files
        if ticker in list(aigo_names.keys()):
            base_sheet_name = ticker
            aigo_sheet_name = aigo_names[ticker]
        else:
            base_sheet_name = ticker
            aigo_sheet_name = ticker
        aigo_sheet = aigo.parse(aigo_sheet_name)
        base_sheet = base.parse(base_sheet_name)

        if aigo_sheet_name not in aigo.sheet_names:
            print(f"\nSheet {aigo_sheet_name} not present in the AIGO delivery Excel file")
            failed_tickers_aigo.append(ticker)
            continue
        
        # Get rows from base sheet where omit is not NA
        omitted_keywords = base_sheet[base_sheet['omit'].isin(['f', 'x', 'o'])]['keyword'].tolist()

        # Check if any omitted keywords exist in aigo sheet
        overlap = aigo_sheet[aigo_sheet['keyword'].isin(omitted_keywords)]

        if len(overlap) > 0:
            failed_tickers_aigo.append(ticker)
            print(f"\nIssues found in sheet {base_sheet_name}:")
            print("The following keywords should have been omitted:")
            print(overlap[['keyword', 'relevance']])
        else:
            print(f"\nSheet {base_sheet_name} passed validation - no omitted keywords found")
    print('Running AIGO8 Check')
    # Iterate through each sheet in aigo Excel
    for ticker in aigo8_ticker_list:
        sheet_name = ticker
        # Get corresponding sheets from both excel files
        aigo8_sheet = aigo8.parse(sheet_name)
        base_sheet = base.parse(sheet_name)
        if sheet_name not in aigo8.sheet_names:
            print(f"\nSheet {sheet_name} not present in the AIGO8 delivery Excel file")
            failed_tickers_aigo8.append(ticker)
            continue
        
        # Get rows from base sheet where omit is not NA
        omitted_keywords = base_sheet[base_sheet['omit'].isin(['f', 'x', 'o'])]['keyword'].tolist()

        # Check if any omitted keywords exist in aigo sheet
        overlap = aigo8_sheet[aigo8_sheet['keyword'].isin(omitted_keywords)]

        if len(overlap) > 0:
            failed_tickers_aigo8.append(ticker)
            print(f"\nIssues found in sheet {sheet_name}:")
            print("The following keywords should have been omitted:")
            print(overlap[['keyword', 'relevance']])
        else:
            print(f"\nSheet {sheet_name} passed validation - no omitted keywords found")
    return failed_tickers_aigo,failed_tickers_aigo8

def ignorelist_check():
    ignore_failed_ticker = []
    # Get today's date
    end_date = datetime.now().strftime('%Y-%m-%d')
    # Get date from a week ago
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    ignore_df = get_ignore_df(start_date,end_date)
    for ticker in base_ticker_list:
        # Get corresponding sheets from both excel files
        base_sheet = base.parse(ticker)
        # Filter rows where omit is 'x' or 'o'
        omit_rows = base_sheet[base_sheet['omit'].isin(['x', 'o'])]
        if omit_rows.empty:
            print(f'\n{ticker} does not update the ignore list')

        else:
            if ignore_df.empty:
                print(f'Ignore list not updated correctly')
                ignore_failed_ticker.append(ticker)
                continue
            for _, row in omit_rows.iterrows():
                keyword = row['keyword']
                omit_type = row['omit']

                # Check conditions based on omit type
                if omit_type == 'x':
                    # Should be in ignore_df with group 'ALL'
                    if not ((ignore_df['_source.name'] == keyword) & (ignore_df['_source.group'] == 'ALL')).any():
                        print(f"Warning: Keyword '{keyword}' from {ticker} with omit='x' not found in ignore list with group='ALL'")
                        ignore_failed_ticker.append(ticker)
                        continue

                elif omit_type == 'o':
                    # Should be in ignore_df with group=ticker
                    if not ((ignore_df['_source.name'] == keyword) & (ignore_df['_source.group'] == ticker)).any():
                        print(f"Warning: Keyword '{keyword}' from {ticker} with omit='o' not found in ignore list with group='{ticker}'")
                        ignore_failed_ticker.append(ticker)
                        continue
    return ignore_failed_ticker

if __name__=='__main__':
    for ticker in base_ticker_list:
        if ticker not in base.sheet_names:
            print(f"\nSheet {ticker} not present in the pre-review Excel file")

    omission_failed_aigo, omission_failed_aigo8 = omission_check()
    ignore_failed = ignorelist_check()

    print(f'\nNo. of AIGO tickers failed omission check: {len(omission_failed_aigo)}')
    if len(omission_failed_aigo)>0:
        print(f"AIGO failed tickers: {omission_failed_aigo}")
    
    print(f'\nNo. of AIGO8 tickers failed omission check: {len(omission_failed_aigo8)}')
    if len(omission_failed_aigo8)>0:
        print(f"AIGO8 failed tickers: {omission_failed_aigo8}")
    
    print(f'\nNo. of tickers failed Ignore List check: {len(ignore_failed)}')
    if len(omission_failed_aigo8)>0:
        print(f"Ignore List failed tickers: {ignore_failed}")

    

    