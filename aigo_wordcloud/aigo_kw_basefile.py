
import shutil
import pandas as pd
import boto3
import os
import boto3
import json
from pathlib import Path
import threading

from dependencies import scaleserp_kw_processing, quantexa_kw_processing

with open('/home/<USER>/scripts/aigo_wordcloud/config.json', 'r') as f:
    config = json.load(f)

# Create threads
t1 = threading.Thread(target=scaleserp_kw_processing.main)
t2 = threading.Thread(target=quantexa_kw_processing.main)

# Start both threads
t1.start()
t2.start()

# Wait for both to finish
t1.join()
t2.join()

scaleserp_file_name = f'etf_scaleserp_keyword_relevance{pd.Timestamp.today().date()}.xlsx'
scaleserp_base_file_path = config["aigo_kw_basefile_paths"]["scaleserp"] + scaleserp_file_name

quantexa_file_name = f'etf_quantexa_keyword_relevance{pd.Timestamp.today().date()}.xlsx'
quantexa_base_file_path = config["aigo_kw_basefile_paths"]["quantexa"] + quantexa_file_name

output_basefile_name = f'aigo_wordcloud_basefile_{pd.Timestamp.today().date()}.xlsx'
output_basefile_folder = config["aigo_kw_basefile_paths"]["basefile_folder"]
os.makedirs(config["aigo_kw_basefile_paths"]["basefile_folder"], exist_ok=True)

output_basefile_path = config["aigo_kw_basefile_paths"]["basefile_folder"] + output_basefile_name

# Load both Excel files to merge them to make one final file
file1 = pd.ExcelFile(scaleserp_base_file_path)
file2 = pd.ExcelFile(quantexa_base_file_path)

# Get all unique sheet names from both files
all_sheets = set(file1.sheet_names).union(set(file2.sheet_names))

# Dictionary to hold deduplicated DataFrames
cleaned_sheets = {}

for sheet in all_sheets:
    df1 = file1.parse(sheet) if sheet in file1.sheet_names else pd.DataFrame()
    df2 = file2.parse(sheet) if sheet in file2.sheet_names else pd.DataFrame()

    # Combine
    combined_df = pd.concat([df1, df2], ignore_index=True)

    # Deduplicate based on 'keyword' column
    if 'keyword' in combined_df.columns:
        dedup_df = combined_df.drop_duplicates(subset='keyword', keep='first')
    else:
        print(f"Skipping deduplication for sheet '{sheet}' — no 'keyword' column found.")
        dedup_df = combined_df

    # Sort if 'relevance' column exists
    if 'relevance' in dedup_df.columns:
        dedup_df.sort_values(by='relevance', ascending=False, inplace=True)

    cleaned_sheets[sheet] = dedup_df

# Save final deduplicated file
with pd.ExcelWriter(output_basefile_path, engine='openpyxl') as writer:
    for sheet_name, df in cleaned_sheets.items():
        df.to_excel(writer, sheet_name=sheet_name, index=False)

print(f"Cleaned & deduplicated Excel saved to:\n{output_basefile_path}")



#file upload to s3
# Define the file path and S3 target
bucket_name = "eq-pre-model-output"

aws_access_id = '********************'
aws_access_secret = 'bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'
client = boto3.client('s3',aws_access_key_id=aws_access_id,     #client is a low level feature in boto3 and boto3 is an AWS wrapper for pythons
                       aws_secret_access_key=aws_access_secret)

def upload_s3file(file_name, bucket, object_name):

    s3_client = boto3.client('s3',aws_access_key_id=aws_access_id,

         aws_secret_access_key=aws_access_secret)

    response = s3_client.upload_file(file_name, bucket, object_name)

file_name = f'{pd.Timestamp.today().date()}_prereview.xlsx'
upload_s3file(output_basefile_path,
                    bucket_name,
                    f'aigo_delivery/wordcloud/01_base_file/{file_name}')


# upload all intermediate files
s3 = boto3.client('s3',aws_access_key_id=aws_access_id,     #client is a low level feature in boto3 and boto3 is an AWS wrapper for pythons
                       aws_secret_access_key=aws_access_secret)

# ---- CONFIG ----
local_base_path = "/home/<USER>/scripts/aigo_wordcloud/data/deliverable"
folders_to_upload = ['unfiltered_etf_kws_quantexa', 
                     'unfiltered_etf_kws_scaleserp', 
                     'filtered_etf_kws_quantexa',
                    'filtered_etf_kws_scaleserp'         
                    'articles_concepts_quantexa',
                    'articles_concepts_scaleserp',
                    'filtered_etf_kws_scaleserp_bigrams',
                    'filtered_etf_kws_quantexa_bigrams'] 
s3_bucket_name = "eq-pre-model-output"
s3_base_prefix = "aigo_delivery/wordcloud/intermediary_files"  # e.g., "weekly-uploads/"


for folder in folders_to_upload:
    local_folder_path = os.path.join(local_base_path, folder)
    s3_folder_prefix = os.path.join(s3_base_prefix, folder)

    if not os.path.isdir(local_folder_path):
        print(f"Folder not found: {local_folder_path}")
        continue

    for root, dirs, files in os.walk(local_folder_path):
        for file in files:
            local_file_path = os.path.join(root, file)
            # Preserve folder structure inside each S3 folder
            relative_path = os.path.relpath(local_file_path, local_folder_path)
            s3_key = os.path.join(s3_folder_prefix, relative_path)

            print(f"Uploading {local_file_path} → s3://{s3_bucket_name}/{s3_key}")
            s3.upload_file(local_file_path, s3_bucket_name, s3_key)

    # Delete folder after upload
    shutil.rmtree(local_folder_path)
    print(f"Deleted local folder: {local_folder_path}")

print("Upload and cleanup complete.")


# remove all the folders once uploaded to s3
import shutil
shutil.rmtree('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/weekly_excelfile/')
print("Base file local folder deleted, \nProcess Completed")
