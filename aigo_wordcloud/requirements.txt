accelerate==1.7.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
airportsdata==20250523
annotated-types==0.7.0
anthropic==0.52.2
anyio==4.9.0
astor==0.8.1
asttokens==3.0.0
asyncio==3.4.3
attrs==25.3.0
aws-requests-auth==0.4.3
backoff==2.2.1
beautifulsoup4==4.13.3
bitsandbytes==0.46.0
blake3==1.0.5
blis==1.3.0
boto3==1.38.28
botocore==1.38.28
browser-use==0.2.5
cachetools==5.5.2
catalogue==2.0.10
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
cloudpathlib==0.21.1
cloudpickle==3.1.1
colorama==0.4.6
comm==0.2.2
compressed-tensors==0.9.4
confection==0.1.5
contourpy==1.3.1
crontab==1.0.1
cssselect==1.3.0
cupy-cuda12x==13.4.1
cycler==0.12.1
cymem==2.0.11
dataclasses-json==0.6.7
datasets==3.6.0
debugpy==1.8.13
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
einops==0.8.1
elasticsearch==7.13.4
email_validator==2.2.0
en_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl#sha256=1932429db727d4bff3deed6b34cfc05df17794f4a52eeb26cf8928f7c1a0fb85
eq_common_utils @ git+https://github.com/EqubotAI/eq-common-utils.git@969b493ae88093f7604454a2b242e5e47058d85d
et_xmlfile==2.0.0
Events==0.5
exchange_calendars==4.10
executing==2.2.0
expiringdict==1.2.2
faiss-cpu==1.11.0
fastapi==0.115.12
fastapi-cli==0.0.7
fastrlock==0.8.3
feedfinder2==0.0.4
feedparser==6.0.11
filelock==3.18.0
filetype==1.2.0
fonttools==4.56.0
frozenlist==1.5.0
fsspec==2025.3.0
gguf==0.16.3
google-ai-generativelanguage==0.6.18
google-api-core==2.25.0
google-auth==2.40.2
googleapis-common-protos==1.70.0
greenlet==3.1.1
griffe==1.7.3
grpcio==1.72.1
grpcio-status==1.72.1
h11==0.14.0
h2==4.2.0
hdbscan==0.8.40
hf-xet==1.1.2
hpack==4.1.0
httpcore==1.0.8
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.32.2
hyperframe==6.1.0
ibm-cloud-sdk-core==3.23.0
ibm-watson==9.0.0
idna==3.10
importlib_metadata==8.6.1
interegular==0.3.3
ipykernel==6.29.5
ipython==9.0.2
ipython_pygments_lexers==1.1.1
jedi==0.19.2
jieba3k==0.35.1
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter_client==8.6.3
jupyter_core==5.7.2
keybert==0.9.0
kiwisolver==1.4.8
korean-lunar-calendar==0.3.1
langchain==0.3.22
langchain-anthropic==0.3.3
langchain-aws==0.2.19
langchain-community==0.3.21
langchain-core==0.3.49
langchain-deepseek==0.1.3
langchain-google-genai==2.1.2
langchain-ollama==0.3.0
langchain-openai==0.3.11
langchain-text-splitters==0.3.7
langcodes==3.5.0
langdetect==1.0.9
langsmith==0.3.30
language_data==1.3.0
lark==1.2.2
llama_cpp_python==0.3.9
llguidance==0.7.25
llvmlite==0.44.0
lm-format-enforcer==0.10.11
lxml==5.3.2
lxml_html_clean==0.4.2
marisa-trie==1.2.1
markdown-it-py==3.0.0
markdownify==1.1.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.1
matplotlib-inline==0.1.7
mcp==1.9.2
mdurl==0.1.2
mem0ai==0.1.93
mistral_common==1.5.6
monotonic==1.6
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.4.3
multiprocess==0.70.16
murmurhash==1.0.12
mypy-extensions==1.0.0
neo4j==5.28.1
nest-asyncio==1.6.0
networkx==3.4.2
newspaper3k==0.2.8
ninja==********
nltk==3.9.1
numba==0.61.2
numpy==2.2.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
ollama==0.5.1
openai==1.84.0
openai-agents==0.0.17
opencv-python-headless==*********
openpyxl==3.1.5
opensearch==0.9.2
opensearch-py==2.8.0
opentelemetry-api==1.33.1
opentelemetry-exporter-otlp==1.33.1
opentelemetry-exporter-otlp-proto-common==1.33.1
opentelemetry-exporter-otlp-proto-grpc==1.33.1
opentelemetry-exporter-otlp-proto-http==1.33.1
opentelemetry-proto==1.33.1
opentelemetry-sdk==1.33.1
opentelemetry-semantic-conventions==0.54b1
opentelemetry-semantic-conventions-ai==0.4.9
orjson==3.10.16
outlines==0.1.11
outlines_core==0.1.26
packaging==24.2
pandas==2.2.3
pandas_market_calendars==5.0.0
parso==0.8.4
partial-json-parser==0.2.1.1.post5
patchright==1.52.4
peft==0.15.2
pexpect==4.9.0
pillow==11.1.0
platformdirs==4.3.7
playwright==1.52.0
portalocker==2.10.1
posthog==3.25.0
preshed==3.0.9
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.22.0
prompt_toolkit==3.0.50
propcache==0.3.1
proto-plus==1.26.1
protobuf==6.31.1
psutil==7.0.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycountry==24.6.1
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pyee==13.0.0
Pygments==2.19.1
PyJWT==2.10.1
pyluach==2.2.0
pynndescent==0.5.13
pyparsing==3.2.1
pyperclip==1.9.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.3.0
qdrant-client==1.14.2
ray==2.46.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-aws4auth==1.3.1
requests-file==2.1.0
requests-toolbelt==1.0.0
rich==14.0.0
rich-toolkit==0.14.7
rpds-py==0.25.1
rsa==4.9.1
s3transfer==0.13.0
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
screeninfo==0.8.1
sentence-transformers==4.0.2
sentencepiece==0.2.0
setuptools==77.0.3
sgmllib3k==1.0.0
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
soupsieve==2.6
spacy==3.8.4
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.40
srsly==2.5.1
sse-starlette==2.3.6
stack-data==0.6.3
starlette==0.46.2
sympy==1.14.0
tenacity==9.1.2
thinc==8.3.6
threadpoolctl==3.6.0
tiktoken==0.9.0
tinysegmenter==0.3
tldextract==5.2.0
tokenizers==0.21.1
toolz==1.0.0
torch==2.7.0
torchaudio==2.7.0
torchvision==0.22.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.52.3
triton==3.3.0
typer==0.15.4
types-requests==2.32.0.20250602
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2025.1
tzlocal==5.3.1
umap==0.1.1
umap-learn==0.5.7
urllib3==2.4.0
uuid7==0.1.0
uvicorn==0.34.2
uvloop==0.21.0
vllm==0.9.0
wasabi==1.1.3
watchfiles==1.0.5
wcwidth==0.2.13
weasel==0.4.1
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
xformers==0.0.30
xgrammar==0.1.19
XlsxWriter==3.2.3
xxhash==3.5.0
yarl==1.19.0
zipp==3.22.0
zstandard==0.23.0
