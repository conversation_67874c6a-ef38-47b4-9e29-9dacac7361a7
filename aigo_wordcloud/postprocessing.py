import pandas as pd
import boto3
import os
from dependencies.plat_data import clean_excell, segment_aigo, segment_aigo8
from datetime import datetime, timedelta

def get_last_two_fridays():
    today = datetime.today()
    # Going backwards to find the most recent Friday
    days_since_friday = (today.weekday() - 4) % 7
    last_friday = today - timedelta(days=days_since_friday)
    second_last_friday = last_friday - timedelta(days=7)
    return last_friday.date().isoformat(), second_last_friday.date().isoformat()

date,  prev_date = get_last_two_fridays()

os.makedirs('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/prereview', exist_ok=True)
os.makedirs('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/postreview', exist_ok=True)
os.makedirs('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO', exist_ok=True)
os.makedirs('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO8', exist_ok=True)

#aws credentials
aws_access_id = '********************'
aws_access_secret = 'bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'
# download the prereview file 
def download_s3file(bucketname,foldername,filename,localfoldername):

    s3_client = boto3.client('s3',aws_access_key_id=aws_access_id,

         aws_secret_access_key=aws_access_secret)

    if localfoldername != '':

        if not os.path.exists(localfoldername):

            os.mkdir(localfoldername)

    s3_client.download_file(bucketname,os.path.join(foldername, filename) , os.path.join(localfoldername, filename))

# download the curr week's verified prereview file
file_name_curr = f'{date}_prereview.xlsx'
download_s3file('eq-pre-model-output',
                    'aigo_delivery/wordcloud/02_verified_file/',
                    file_name_curr,
                    '/home/<USER>/scripts/aigo_wordcloud/data/deliverable/prereview')

# download last weeks postreview file
file_name_prev = f'deliverable_{prev_date}.xlsx'
download_s3file('eq-pre-model-output',
                    'aigo_delivery/wordcloud/03_postreview_file/',
                    file_name_prev,
                    '/home/<USER>/scripts/aigo_wordcloud/data/deliverable/postreview')
## test



path = f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/prereview/{date}_prereview.xlsx'
delpath = f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/postreview/deliverable_{date}.xlsx'

# this path needs to be changed to s3, and load prereview files from there itself
print(path)
print('started')
clean_excell(path, date, prev_date)
print("clean")
segment_aigo(delpath, date)
print("segmented")
segment_aigo8(delpath, date)



# pusing to s3
sdate = date.replace('-', '')


s3 = boto3.client('s3', aws_access_key_id=aws_access_id , aws_secret_access_key=aws_access_secret )
bucket_name = 'eq-pre-model-output'


# Specify the S3 bucket name and file paths for postreview file
folder_name = 'aigo_delivery/wordcloud/03_postreview_file/'#change name
local_file_name = f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/postreview/deliverable_{date}.xlsx'#change name
s3_file_name = f'deliverable_{date}.xlsx'#change name
print('ready')
# Upload the file to S3
s3.upload_file(local_file_name, bucket_name, folder_name + s3_file_name)



# Specify the S3 bucket name and file paths
folder_name = 'aigo_delivery/wordcloud/04_cleaned_file/aigo/'#change name
local_file_name = f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO/AIGO_WORDCLOUD_{sdate}.xlsx'#change name
s3_file_name = f'AIGO_WORDCLOUD_{sdate}.xlsx'#change name
print('ready')
# Upload the file to S3
s3.upload_file(local_file_name, bucket_name, folder_name + s3_file_name)

# Specify the S3 bucket name and file paths
folder_name = 'aigo_delivery/wordcloud/04_cleaned_file/aigo8/'#change name
local_file_name = f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO8/AIGO8_WORDCLOUD_{sdate}.xlsx'#change name
s3_file_name = f'AIGO8_WORDCLOUD_{sdate}.xlsx'#change name

# Upload the file to S3
s3.upload_file(local_file_name, bucket_name, folder_name + s3_file_name)


# delete the folders from local 
import shutil
shutil.rmtree('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/prereview')
shutil.rmtree('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/postreview')
shutil.rmtree('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO')
shutil.rmtree('/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO8')
