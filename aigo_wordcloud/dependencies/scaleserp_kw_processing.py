# %% [markdown]
# # fallback querying strategy for getting links 

# %%
import openai
import pandas as pd
import os
import pandas as pd
import time
from datetime import datetime, timedelta
import requests
import csv
import newspaper
from newspaper import fulltext
from newspaper import Article
from ibm_watson import NaturalLanguageUnderstandingV1
from ibm_watson.natural_language_understanding_v1 import Features, ConceptsOptions, EntitiesOptions, KeywordsOptions
from ibm_cloud_sdk_core.authenticators import IAMAuthenticator
import os
import re

import pandas as pd
import openai
from openai import OpenAI
import json
import requests
import re
from collections import Counter

from langchain.chat_models import ChatOpenAI
from langchain.schema import (
    HumanMessage,
    SystemMessage
)

import pandas as pd
import os




def get_current_week_dates():
    today = datetime.today()
    start_of_week = today - timedelta(days=today.weekday())  # Monday
    end_of_week = start_of_week + timedelta(days=4)          # Friday
    
    st_dt = start_of_week.strftime('%m/%d/%Y')
    ed_dt = end_of_week.strftime('%m/%d/%Y')
    
    return st_dt, ed_dt



def query(ticker, start_date, end_date):
    try:

        params = {
        'api_key': '7A76A8C2CAE64F7E841C3FE4E2DB55F8',
          'search_type': 'news',
          'q': f'{ticker}',
          'google_domain': 'google.com',
          'location': 'United States',
          'gl': 'us',
          'hl': 'en',
          'lr': 'lang_en',
          'time_period': 'custom',
          'time_period_min': start_date,
          'time_period_max': end_date,
          'sort_by': 'relevance',
          'num': '30',
          'output': 'json'
        }


        # make the http GET request to Scale SERP
        api_result = requests.get('https://api.scaleserp.com/search', params, timeout= 120)

        # print the JSON response from Scale SERP
        #return json.dumps(api_result.json())
        return api_result.json()
    
    except requests.exceptions.Timeout:
        return 'Timeout'


# Function to get links for a keyword string
def fetch_links(keywords, st_dt, ed_dt):
    try:
        r = query(keywords, st_dt, ed_dt)
        if r['search_information']['total_results'] > 0:
            return {item['link'] for item in r['news_results']}
    except Exception as e:
        print(f"Error fetching links: {e}")
    return set()

# Retry strategy
def query_etf_with_fallback(keywords_list, st_dt, ed_dt, min_links=5, max_retries=5, sleep_base=2):
    keyword_str = ", ".join(keywords_list)
    all_links = set()
    
    # First: try with all keywords combined
    for attempt in range(max_retries):
        print(f"\nAttempt {attempt+1} with all keywords: {keyword_str}")
        new_links = fetch_links(keyword_str, st_dt, ed_dt)
        all_links.update(new_links)

        if len(all_links) >= min_links:
            return list(all_links), 'multi-keyword'

        wait = sleep_base ** (attempt + 1)
        print(f"Waiting {wait}s before retry...")
        time.sleep(wait)

    # Fallback: try with each keyword individually
    print(f"\nFallback: trying individual keywords for ETF...")
    for kw in keywords_list:
        print(f"Trying individual keyword: {kw}")
        new_links = fetch_links(kw, st_dt, ed_dt)
        all_links.update(new_links)

        if len(all_links) >= min_links:
            return list(all_links), 'fallback-single'

    return list(all_links), 'fallback-single' if all_links else 'failed'




# IBM Watson NLU setup
def initialize_watson_nlu(api_key, url):
    authenticator = IAMAuthenticator(api_key)
    natural_language_understanding = NaturalLanguageUnderstandingV1(
        version='2021-08-01',
        authenticator=authenticator
    )
    natural_language_understanding.set_service_url(url)
    return natural_language_understanding

# Extract article text using newspaper3k
def extract_article_newspaper(url):
    try:
        article = Article(url)
        article.download()
        article.parse()
        return article.text
    except Exception as e:
        print(f"Error extracting article from {url} using newspaper: {e}")
        return None

# Extract article text using Watson NLU
def extract_article_watson(nlu_service, url):
    try:
        response = nlu_service.analyze(
            url=url,
            features=Features(
                concepts=ConceptsOptions(limit=10),
                entities=EntitiesOptions(emotion=True, sentiment=True, limit=10),
                keywords=KeywordsOptions(limit=10),
            ),
            language='en',
            return_analyzed_text=True
        ).get_result()
        return response.get('analyzed_text', None)
    except Exception as e:
        print(f"Error extracting article from {url} using Watson NLU: {e}")
        return None

# Process the list of URLs
def process_article_links(links, nlu_service):
    articles = []
    
    for link in links:
        article_text = extract_article_newspaper(link)
        
        # If newspaper fails, try Watson NLU
        if not article_text:
            article_text = extract_article_watson(nlu_service, link)
        
        if article_text:
            articles.append({"url": link, "text": article_text})
        else:
            print(f"Failed to extract article from {link}")
    
    return articles



def extract_keywords(text):
    """
    Extracts the keywords from the ChatGPT generated text.
    """
    text = text.lower()
    expression = r".*concepts:(.+?)$"
    if re.search(expression, text):
        keywords = re.sub(expression, r"\1", text, flags=re.S)
        if keywords is not None and len(keywords) > 0:
            return [re.sub(r"\.$", "", k.strip()) for k in keywords.strip().split(',')]
    return []


def write_to_excel(popular_keywords, keyword_data):
    """
    Captures the keywords of each record in one file and then the overall keyword count in another one.
    :param popular_keywords: The keyword counter
    :param keyword_data: Contains the record data and the extracted keywords
    """
    pd.DataFrame(keyword_data).to_excel('keyword_info.xlsx')
    keyword_data = [{'keyword': e[0], 'count': e[1]} for e in popular_keywords.most_common()]
    pd.DataFrame(keyword_data).sort_values(by=['count'], ascending=False).to_excel('popular_keywords.xlsx')


def process_keywords(articles, etf, etf_kws, etf_description):
    """
    Instantiates the object which interfaces with ChatGPT and loops through the records
    capturing the keywords for each records and also counting the occurrence of each of these keywords.
    """
    model_name = "gpt-4o-mini"
    chat = ChatOpenAI(model_name=model_name, temperature=0.5)
    popular_keywords = Counter()
    keyword_data = []
    print(len(articles))
    count = 0
    for item in articles:
        try:
            print(f"processing article: {count + 1}/{len(articles)}")
            # print(record_data)
            print("1111111111111111111111111111111111111111111111111111111111")
            dt_single, answer = extract_keywords_from_chat(chat, item['text'], etf, etf_kws, etf_description)
            extracted_keywords = extract_keywords(answer)
            popular_keywords.update(extracted_keywords)
            # print(i, dt_single, popular_keywords)
            
            keyword_data.append({'articles link': item['url'], 'description': item['text'], 
                                'concepts': ','.join(extracted_keywords)})
            count += 1
        except Exception as e:
            print(f"Error occurred: {e}")

    # Writing the keyword counts to a CSV file
    output_folder = '/home/<USER>/scripts/aigo_wordcloud/data/deliverable/unfiltered_etf_kws_scaleserp'
    os.makedirs(output_folder, exist_ok=True)
    with open(os.path.join(output_folder, f'{etf}_concepts_with_gserp{pd.Timestamp.today().date()}.csv'), mode='w', newline='') as file:
        writer = csv.writer(file)
        
        # Write the header
        writer.writerow(['concept', 'count'])
        
        # Write the keyword data
        for keyword, count in popular_keywords.items():
            writer.writerow([keyword, count])

    return keyword_data


def extract_keywords_from_chat(chat, record_data, ETF, etf_keywords, description):
    """
    Sends a chat question to ChatGPT and returns its output.
    :param chat: The object which communicates under the hood with ChatGPT.
    :param record_data: The tuple with the title, description, id and youtube id
    """
    
    prompt = f'''
            You are a financial analyst focused on thematic investing. 

            The ETF of interest is: **[ETF Name: {ETF}]**  
            ETF Description: **[{description}]**

            Articles were fetched using these keywords: **[{etf_keywords}]**

            Now analyze the given article as a fund manager. Extract top 10 financially relevant **themes, macroeconomic signals, policy events, or market-moving developments** that could affect this ETF's share price. Focus only on **financially actionable and relevant signals**, not general news or promotions.

            **Your task:**  
            Extract the core **financial themes or events** from the article in a **comma-separated list**, and prefix the list with:  
            `thematic concepts:`

            Example output format:  
            `thematic concepts: Federal Reserve rate hike, declining Treasury yields, easing trade tensions, bond market volatility`
        '''


    dt_single = f"{record_data[0]} {record_data[1]}"
    resp = chat([
                SystemMessage(content= prompt),
                HumanMessage(content=record_data)
            ])
    answer = resp.content
    return dt_single,answer



def clean_response_to_json(response_text):
    """Strip markdown and format properly for json.loads"""
    cleaned = re.sub(r"^```json|```$", "", response_text.strip(), flags=re.MULTILINE).strip()
    return json.loads(cleaned)

def process_etf(etf, description, concepts_csv):
    """Create prompt, send request to GPT, return filtered dataframe"""
    concept_df = pd.read_csv(concepts_csv)
    concept_text = "\n".join([f"{row['concept']} ({row['count']})" for _, row in concept_df.iterrows()])
    
    prompt = f"""
        You are an expert in ETF(exchange traded fund) analysis. Based on the following ETF business description:

        "{description}"

        Evaluate the following list of concepts and identify only the ones that would financially impact the ETF in united states country. 

        For each selected concept, provide:
        - The name of the concept (the concept should have financial impact in future, it can be concept, keyword or major events)
        - The relevancy score (based on concept's effectiveness with respect to etf description and ranging strictly between 0.6 and 0.99)
        - A short reason (1-2 lines) explaining how this concept impacts the ETF in united states(while selecting keyword keep in mind it should be relevant to the given ETF)

        Select atleast top 50 concepts(if number of actual concepts are less than 50 then take do not make it 30, instead take all the list of concepts available) that are financially relevant to the ETF description.

        Format your output as a JSON array like this:
        [
        {{"concept": "easing trade tensions", "relevancy score": 0.92, "reason": "Negotiations between the U.S. and China on tariffs could enhance investor sentiment and boost stock prices within the ETF."}},
        {{"concept": "U.S. better-than-expected jobs report", "relevancy score": 0.83, "reason": "A better-than-expected jobs report suggests economic stability, which can support stock market growth and positively impact SPY."}},
        .....  
        ]
        Here is the list of concepts and their counts:
        {concept_text}
        """

    try:
        # # Initialize the OpenAI client (requires openai>=1.0.0)
        client = OpenAI(api_key="***************************************************")
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant for financial data filtering."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.0
        )
        content = response.choices[0].message.content.strip()
        data_json = clean_response_to_json(content)
        return pd.DataFrame(data_json)

    except Exception as e:
        print(f"Failed for {etf}: {e}")
        return pd.DataFrame(columns=["concept", "relevancy score", "reason"])

def run_all_etfs(etf_desc_file, concepts_folder, output_folder):
    etf_df = pd.read_csv(etf_desc_file)

    for _, row in etf_df.iterrows():
        etf = row['ETF']
        desc = row['etf capiq description']
        concept_file = os.path.join(concepts_folder, f"{etf}_concepts_with_gserp{pd.Timestamp.today().date()}.csv")

        if not os.path.exists(concept_file):
            print(f"Concept file not found for {etf}, skipping.")
            continue

        print(f"Processing {etf}...")
        filtered_df = process_etf(etf, desc, concept_file)
        if not filtered_df.empty:
            filtered_df.to_csv(os.path.join(output_folder, f"{etf}_filtered_concepts{pd.Timestamp.today().date()}.csv"), index=False)
            print(f"Saved output for {etf}.\n")
        else:
            print(f"No concepts selected for {etf}.\n")


# Function to generate bigrams using OpenAI
def generate_bigram(phrase):
    # Few-shot examples for prompt
    few_shot_prompt = """You are a concise language model that rewrites economic and financial phrases into meaningful 2-3 tokens (bigrams or trigrams only). Avoid generic terms and preserve key context.

    Examples:
    - "rising metal prices" → "metal inflation"
    - "investment in electric vehicle production" → "EV investment"
    - "environmental regulations on mining practices" → "mining environmental regulations"
    - "global economic recovery post-pandemic" → "post-pandemic recovery"
    - "labor shortages in mining" → "mining labor shortage"
    - "technological disruptions in mining" → "mining technological disruption"

    Now rewrite the following phrases into concise bigrams or trigrams preserving context:"""
    full_prompt = f"{few_shot_prompt}\n- \"{phrase}\" →"
    try:
        response = openai.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.3,
            max_tokens=15,
        )
        output = response.choices[0].message.content.strip()
        output = output.replace("→", "").strip().strip('"')
        return output
    except Exception as e:
        print(f"Error for '{phrase}': {e}")
        return None


# Function to detect entries with multiple quoted or joined phrases
def is_malformed(entry):
    if pd.isna(entry):
        return False
    # Check for quote patterns or multiple spaces between words
    return bool(re.search(r'"\s+"|"\s+|"\s*|\s{2,}', entry))


# Function to compute ranked similarity
def rank_candidates_by_similarity(ticker, candidate_keywords, rel_scores):

    scores = []
    for i in range(len(rel_scores)):
        scores.append((ticker, candidate_keywords[i], rel_scores[i], ''))
        print(scores)
    ranked = sorted(scores, key=lambda x: x[2], reverse=True)
    return pd.DataFrame(ranked, columns=['ticker','keyword', 'relevance', 'omit'])


def main():

    # Configuration
    # Put here your API key
    os.environ["OPENAI_API_KEY"] = '***************************************************'

    # Load data
    df = pd.read_csv('/home/<USER>/scripts/aigo_wordcloud/data/updated_etf_keywords_with_descriptions.csv')
    df['Keywords'] = df['Keywords'].apply(eval)

    # # Define your date range
    st_dt, ed_dt = get_current_week_dates()
    print(f"Start: {st_dt}, End: {ed_dt}")

    # st_dt = '06/02/2025' 
    # ed_dt = '06/06/2025'

    # Main loop over ETFs
    results = []
    for idx, row in df.iterrows():
        keywords = row['Keywords']
        etf = row['ETF']
        print(f"\n===== Processing ETF: {etf} =====")
        links, strategy = query_etf_with_fallback(keywords, st_dt, ed_dt)
        results.append({'ETF': etf, 'Links': links, 'Strategy': strategy})

    # Convert to DataFrame and save
    results_df = pd.DataFrame(results)
    results_df.to_csv(f"/home/<USER>/scripts/aigo_wordcloud/data/deliverable/etf_links_gserp_with_fallback{pd.Timestamp.today().date()}.csv", index=False)
    print("\nAll done. Results saved to etf_links_with_fallback.csv")

    etf_desc_file = "/home/<USER>/scripts/aigo_wordcloud/data/updated_etf_keywords_with_descriptions.csv"
    etf_kw_file = pd.read_csv(etf_desc_file)

    # Loop through each ETF and its links
    for idx, row in results_df.iterrows():
        etf_name = row['ETF']
        etf_kws = etf_kw_file[etf_kw_file['ETF'] == etf_name]['Keywords']
        etf_description = etf_kw_file[etf_kw_file['ETF'] == etf_name]['etf capiq description']

        # print(etf_kws, etf_description)
        etf_links = row['Links']

        try:
            print(f"\nProcessing ETF: {etf_name}")
            
            # Call the main function dynamically for each ETF
            def main_for_etf(links, etf):
                apikey="vfhpPKMWLM9FrwNuF1zIhs0LKPbGbNcuvc1IPqG0fmtW"
                url="https://api.us-south.natural-language-understanding.watson.cloud.ibm.com/instances/1b87995c-4b2a-4d19-ae42-7b6df203639f"


                nlu_service = initialize_watson_nlu(apikey, url)
                articles = process_article_links(links, nlu_service)
                print('article extraction done.')
                concepts_final_list = process_keywords(articles, etf, etf_kws, etf_description)
                print('Keyword extraction done.')
                concepts_final_df = pd.DataFrame(concepts_final_list)

                output_folder = "/home/<USER>/scripts/aigo_wordcloud/data/deliverable/articles_concepts_scaleserp"
                os.makedirs(output_folder, exist_ok=True)
                output_file = os.path.join(output_folder, f"{etf}_articles_concepts_scaleserp_{pd.Timestamp.today().date()}.csv")
                concepts_final_df.to_csv(output_file, index=False)
                print(f"Saved output to {output_file}")

            main_for_etf(etf_links, etf_name)

        except Exception as e:
            print(f"Error processing ETF {etf_name}: {e}")


    # Path to ETF descriptions file
    
    concepts_folder = '/home/<USER>/scripts/aigo_wordcloud/data/deliverable/unfiltered_etf_kws_scaleserp'
    output_folder = "/home/<USER>/scripts/aigo_wordcloud/data/deliverable/filtered_etf_kws_scaleserp"

    # Ensure output folder exists
    os.makedirs(output_folder, exist_ok=True)

    # Run the full batch
    run_all_etfs(etf_desc_file, concepts_folder, output_folder)

    # Set your OpenAI API key
    openai.api_key = "***************************************************"

    # Input and output folder paths
    input_folder = "/home/<USER>/scripts/aigo_wordcloud/data/deliverable/filtered_etf_kws_scaleserp"
    output_folder = "/home/<USER>/scripts/aigo_wordcloud/data/deliverable/filtered_etf_kws_scaleserp_bigrams"
    os.makedirs(output_folder, exist_ok=True)

    # Process all CSV files in the input folder
    for filename in os.listdir(input_folder):
        if filename.endswith(f"{pd.Timestamp.today().date()}.csv"):
            filepath = os.path.join(input_folder, filename)
            print(f"Processing {filename}...")

            try:
                df = pd.read_csv(filepath)
                if "concept" not in df.columns:
                    print(f"Skipping {filename}: No 'concept' column found.")
                    continue

                df["final_concept"] = df["concept"].apply(generate_bigram)
                df = df[~df["final_concept"].apply(is_malformed)].copy()
                # Save to output folder
                output_path = os.path.join(output_folder, filename)
                df.to_csv(output_path, index=False)
                print(f"Saved updated file to: {output_path}")

            except Exception as e:
                print(f"Failed to process {filename}: {e}")

    # Paths
    concept_folder = "/home/<USER>/scripts/aigo_wordcloud/data/deliverable/filtered_etf_kws_scaleserp_bigrams"
    output_excel_folder = '/home/<USER>/scripts/aigo_wordcloud/data/deliverable/weekly_excelfile'
    os.makedirs(output_excel_folder, exist_ok=True)
    output_excel = os.path.join(output_excel_folder, f'etf_scaleserp_keyword_relevance{pd.Timestamp.today().date()}.xlsx')
    # Load the ETF description file
    etf_df = pd.read_csv(etf_desc_file)
    etf_df['Keywords'] = etf_df['Keywords'].apply(eval)  # Ensure list format if read as string

    
    # Create ExcelWriter
    with pd.ExcelWriter(output_excel, engine='openpyxl') as writer:
        for _, row in etf_df.iterrows():
            etf = row['ETF']
            keywords = row['Keywords']
            concept_file = os.path.join(concept_folder, f"{etf}_filtered_concepts{pd.Timestamp.today().date()}.csv")

            if os.path.exists(concept_file):
                concept_df = pd.read_csv(concept_file)
                candidate_keywords = concept_df['final_concept'].dropna().tolist()
                rel_scores = concept_df['relevancy score'].dropna().tolist()
                ranked_df = rank_candidates_by_similarity(etf, candidate_keywords, rel_scores)
                ranked_df.to_excel(writer, sheet_name=etf[:31], index=False)  # Sheet name max length is 31

            else:
                print(f" Concept file not found for ETF: {etf}")

    print(f"Finished! Excel file saved at: {output_excel}")






# run the constructor


if __name__=='__main__':

    main()
