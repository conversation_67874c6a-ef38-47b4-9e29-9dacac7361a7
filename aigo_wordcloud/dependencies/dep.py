from datetime import datetime
import json
import pandas as pd
import requests

#Used in pipeline.py, plat_data.py
today = datetime.today().strftime("%Y-%m-%d")

# Firms: Used in google-api
api_url = 'http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=maxetf'
response = requests.get(f'{api_url}')
response_dict= response.json()
firms = response_dict["data"]["masteractivefirms_maxetf"]
pd.DataFrame(firms).to_csv('etfs.csv')

# While changing the tickers ensure that the first name in the list is the ticker name that you need in the column "Tickers"
# tickers = { 'SPY': ['SPY+ETF','S&P 500','US stock Market'],
#             'BNDX': ['BNDX+ETF','International Bond'],
#             'EEM': ['EEM+ETF','Emerging Market'],
#             'TLT':['TLT+ETF' , '20 Year Treasury'],
#             'TIP':['TIP+ETF' , 'Inflation protected security'],
#             'SHY':['SHY+ETF' , '1-3 Year Treasury'],
#             'LQD':['LQD' , 'Corporate bond' , 'Investment Grade Corporate Bond'],
#             'HYG':['HYG' , 'High Yield Bond'],
#             'GLD':['GLD+ETF' , 'Gold'],
#             'EMB':['EMB+ETF' , 'Emerging Market Bond'],
#             'XLE':['XLE+ETF' , 'Energy Sector'],
#             'QQQ':['QQQ+ETF' , 'Technology Sector'],
#             'IYR':['IYR+ETF' , 'Real Estate market'],
#             'IWM':['IWM+ETF' , 'Russel 2000'],
#             'EWJ':['EWJ+ETF' , 'Japan Market'],
#             'EFA':['EFA+ETF' , 'European Market'],
#             'XME':['XME+ETF' , 'Metal and Mining'],
#             'HSMETYSN':['HSMETYSN' , 'Job market'],
#             'MQFIUSTU':['MQFIUSTU' , '2 Year Treasury'],
#             'MQFIUSTY':['MQFIUSTY' , '10 Year Treasury'],
#             'MQFIUSUS':['MQFIUSUS' , '30 Year Treasury']}

tickers = { 'SPY': ['S&P 500'],
            'BNDX': ['International Bond'],
            'EEM': ['Emerging Market'],
            'TLT':['20 Year Treasury'],
            'TIP':['Inflation protected security'],
            'SHY':['1-3 Year Treasury'],
            'LQD':['Investment Grade Corporate Bond'],
            'HYG':['High Yield Bond'],
            'GLD':['Gold'],
            'EMB':['Emerging Market Bond'],
            'XLE':['Energy Sector'],
            'QQQ':['Technology Sector'],
            'IYR':['Real Estate market'],
            'IWM':['Russel 2000'],
            'EWJ':['Japan Market'],
            'EFA':['European Market'],
            'XME':['Metal and Mining'],
            'HSMETYSN':['Job market'],
            'MQFIUSTU':['Year Treasury'],
            'MQFIUSTY':['10 Year Treasury'],
            'MQFIUSUS':['30 Year Treasury']}

companies= [['Google', 'GOOGL'], ['Apple' ,'AAPL'],
            ['Meta', 'META'], ['Amazon', 'AMZN'],
            ['Uber', 'UBER'], ['Spotify','SPOT'],
            ['Netflix' , 'NFLX']]

#Prohibitted Domains
prohibited_domains = [
    'www.bloomberg.com',
    'seekingalpha.com',
    'reviewbekasi.com',
    'educationnext.com',
    'bcs-express.ru',
    'www.hankyung.com',
    'kabartotabuan.com',
    'exstreamal.com',
    'www.exstreamal.com',
    'www.wsj.com'
]

# print(len(firms))
etf_list=['SPY','BNDX', 'EEM', 'TLT', 'TIP', 'SHY', 'LQD', 'HYG', 'GLD', 'EMB', 'XLE', 'QQQ', 'IYR', 'IWM', 'EWJ', 'EFA', 'XME', 'HSMETYSN', 'MQFIUSTU', 'MQFIUSTY', 'MQFIUSUS']

# who is owning the information model.
# how many articles are being used.