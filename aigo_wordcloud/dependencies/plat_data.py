from dependencies.dep import *
import pandas as pd
import json
from collections import defaultdict
from tqdm import tqdm
import ast
from eq_common_utils.utils.ignore_helper import I<PERSON>reHelper

def onlyconcepts(df):
    final_tmp1=[]
    urls1_rel=df[['ticker','keyword']]
    for t1 in df.ticker.unique():
        tmp_df=urls1_rel[urls1_rel['ticker']==t1]
        final_tmp=[]
        for n in range(len(tmp_df)):
            try:
                for l1 in list(ast.literal_eval(tmp_df.iloc[n][1]).items()):
                    dict_tmp1={}
                    dict_tmp1['ticker']=t1
                    dict_tmp1['keyword']=l1[0]
                    dict_tmp1['relevance']=l1[1]
                    final_tmp.append(dict_tmp1)
            except:
                pass
        final_tmp1.extend(final_tmp)
    newdf = pd.DataFrame(final_tmp1)
    return newdf

def segment(df):
    writer = pd.ExcelWriter(f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/prereview/{today}_prereview.xlsx', engine='xlsxwriter')
    # writer = pd.ExcelWriter(f'plat_data_test.xlsx', engine='xlsxwriter')
    for etf in df.ticker.unique():
        print("working on {}".format(etf))
        try:
            df_tmp2 = df[df['ticker'] == etf]
            df_tmp2 = df_tmp2.drop_duplicates(subset=['keyword'])
            df_tmp2 = df_tmp2.sort_values(by=['relevance'],ascending=False)
            df_tmp2 = df_tmp2[df_tmp2["relevance"]>0.5]
            df_tmp2["normalized_relevance"] = 0
            df_tmp2["omit"] = ''
            df_tmp2 = df_tmp2.head(50)
            df_tmp2.to_excel(writer, sheet_name=etf, index=False)
                
        except Exception as e_segment:
            # print(e_segment)
            pass
    writer.close() 


def previous_df(prev_date, ticker):
    df = pd.read_excel(f"/home/<USER>/scripts/aigo_wordcloud/data/deliverable/postreview/deliverable_{prev_date}.xlsx", sheet_name = f"{ticker}")
    print(f"taking {ticker} from {prev_date}")
    return df


def clean_excell(path,date, prev_date):
    print('cleaning')
    unav = []
    writer = pd.ExcelWriter(f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/postreview/deliverable_{date}.xlsx', engine='xlsxwriter')
    remdfs = []
    for ticker in tqdm(etf_list):
        try:
            try:
                df = pd.read_excel(f"{path}", sheet_name = f"{ticker}")
            except:
                df = previous_df(prev_date, ticker)
            ignored_df= df[pd.notnull(df["omit"])]
            print('working on {}'.format(ticker))
            try:
                ignored_df['omit'] = ignored_df['omit'].str.lower()  
                ignored_df = ignored_df[ignored_df['omit'].isin(['x', 'o'])]
                ignored_df["omit"] = ignored_df["omit"].replace({'x': 'ALL', 'o': ticker})
            except:
                pass
            IgnoreHelper().store_list(ignored_df, "keyword", "omit")
            remdfs.append(ignored_df)
            df = df[pd.isnull(df["omit"])]
            min = df['relevance'].min()
            max= df['relevance'].max()
            df['normalized_relevance'] = df['relevance'].apply(lambda x: 1 + (x- min)*9/(max-min))
            print("leeeeeeeeeeeeeeeeength ",len(df))
            if len(df)<2:
                df = previous_df(prev_date)
            df.to_excel(writer, sheet_name=ticker, index=False)

        except Exception as e1:
            # print(e1)
            unav.append({ticker: e1})
    print(unav)
    writer.close()




def segment_aigo8(path, date):
    notaigo8 = ['MQFIUSUS', 'MQFIUSTY', 'MQFIUSTU']
    cleandate = date.replace('-', '')
    writer = pd.ExcelWriter(f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO8/AIGO8_WORDCLOUD_{cleandate}.xlsx', engine='xlsxwriter')
    for etf in tqdm(etf_list):
        if etf not in notaigo8:
            try:    
                df = pd.read_excel(f"{path}", sheet_name = f"{etf}")
                df = df.sort_values(by=['relevance'],ascending=False)
                df.to_excel(writer, sheet_name=etf, index=False)
            except:
                pass
    
    writer.close()

def segment_aigo(path, date):
    notaigo = ['TLT', 'SHY']
    aigo_names = {'SPY': 'MQFTUSE1',
                  'QQQ':'MQFTUSN1',
                   'IWM': 'MQFTUSS1'}
    cleandate = date.replace('-', '')
    writer = pd.ExcelWriter(f'/home/<USER>/scripts/aigo_wordcloud/data/deliverable/AIGO/AIGO_WORDCLOUD_{cleandate}.xlsx', engine='xlsxwriter')
    for etf in tqdm(etf_list):
        if etf not in notaigo:
            try:
                df = pd.read_excel(f"{path}", sheet_name = f"{etf}")
                if etf in aigo_names:
                    etf = aigo_names[etf]
                    df["ticker"] = etf
                df = df.sort_values(by=['relevance'],ascending=False)
                df.to_excel(writer, sheet_name=etf, index=False)
            except:
                pass
    
    writer.close()