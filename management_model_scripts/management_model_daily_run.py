"""
Management Model Scripts - Daily Run

Refactored management model daily run with improved error handling,
logging, and standardized utilities usage.
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Tuple, Optional
import pandas as pd
from datetime import datetime
from pandas.tseries.offsets import BDay

# Import shared utilities package (installed from Git)
from shared_utils import (
    load_config, validate_common_config, create_model_logger, create_error_handler,
    ErrorSeverity, ModelError, ConfigurationError, ErrorContext
)

# Import existing helpers (to be refactored gradually)
from imports import *
from common_helper import HelperFuncs
from daily_run_helper import DailyHelperFuncs


class ManagementModelRunner:
    """Management Model execution orchestrator with improved error handling."""

    def __init__(self, tag: str, scheduler: str, environment: str):
        """
        Initialize Management Model Runner.

        Args:
            tag: Model tag
            scheduler: Scheduler type
            environment: Environment (dev, staging, prod)
        """
        self.tag = tag
        self.scheduler = scheduler
        self.environment = environment

        # Load configuration
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, 'config.yaml')
        self.config = load_config(config_path)
        self._validate_config()

        # Setup dates
        curr_date = datetime.today().strftime("%Y-%m-%d")
        self.saved_date = (pd.to_datetime(curr_date) - BDay(1)).strftime("%Y-%m-%d")

        # Setup logging
        log_dir = os.path.join(script_dir, self.config.get(f'log_files.{tag}_folder', 'logs'))
        self.logger = create_model_logger(
            model_name='management_model',
            tag=tag,
            scheduler=scheduler,
            run_date=self.saved_date,
            log_dir=log_dir
        )

        # Setup error handler
        self.error_handler = create_error_handler(self.logger.get_logger())

        # Initialize helper connections (backward compatibility)
        self.commonconn = HelperFuncs()
        self.dailyconn = DailyHelperFuncs()

        # Configuration shortcuts
        self.bucket_name = self.config.get('s3_buckets.management_bucket')
        self.versioning_bucket = self.config.get('s3_version_control.bucket')
        self.versioning_filename = self.config.get('s3_version_control.filename_monthly')

        self.logger.log_model_start({
            'tag': tag,
            'scheduler': scheduler,
            'environment': environment,
            'saved_date': self.saved_date
        })

    def _validate_config(self) -> None:
        """Validate configuration requirements."""
        required_keys = [
            's3_buckets.management_bucket',
            's3_version_control.bucket',
            's3_version_control.filename_monthly'
        ]

        if not self.config.validate_required_keys(required_keys):
            raise ConfigurationError("Missing required configuration keys")

    def daily_predictions(self, tag_df: pd.DataFrame, firms: list, date: str, tag: str) -> Tuple[pd.DataFrame, dict]:
        """
        Execute daily predictions with improved error handling and logging.

        Args:
            tag_df: Tag DataFrame
            firms: List of firms
            date: Prediction date
            tag: Model tag

        Returns:
            Tuple of (complete_df, metrics_dict)
        """
        try:
            self.logger.info(f"Starting daily predictions for {len(firms)} firms")

            # Get input data
            with ErrorContext(self.error_handler, "get_input_data"):
                (no_dep_count, no_traded_count, og_daily_data_df, daily_data_df,
                 sector_fill_df, copy_dep_details, deployment_toSubmit) = self.dailyconn.get_input_data(firms, date, tag)

            submitted_pred_count = len(deployment_toSubmit)
            self.logger.info(f"Prepared {submitted_pred_count} deployments for prediction")

            # Get predictions
            with ErrorContext(self.error_handler, "get_predictions"):
                pred_df = self.commonconn.get_predictions(deployment_toSubmit)
                self.logger.debug(f"Prediction results shape: {pred_df.shape}")

            # Calculate failed predictions
            failed_count = pred_df['predictions'].apply(self.commonconn.is_invalid_prediction).sum()

            # Post-processing
            with ErrorContext(self.error_handler, "post_pred_processing"):
                complete_df, no_sector_pred_count, sector_pred_count = self.dailyconn.post_pred_processing(
                    pred_df, copy_dep_details, og_daily_data_df, daily_data_df,
                    sector_fill_df, tag_df, firms, tag, date
                )

            # Calculate metrics
            zero_pred_count = (complete_df['predictions'] == 0).sum()

            metrics = {
                'no_dep_count': no_dep_count,
                'submitted_pred_count': submitted_pred_count,
                'failed_count': failed_count,
                'master_len': len(firms),
                'sector_pred_count': sector_pred_count,
                'no_sector_pred_count': no_sector_pred_count,
                'zero_pred_count': zero_pred_count,
                'no_traded_count': no_traded_count
            }

            # Log prediction summary
            self.logger.log_prediction_summary(
                predictions_generated=submitted_pred_count - failed_count,
                predictions_failed=failed_count
            )

            self.logger.info(f"Daily predictions completed successfully. Generated {len(complete_df)} final predictions")
            return complete_df, metrics

        except Exception as e:
            self.error_handler.handle_error(
                ModelError(f"Daily predictions failed: {e}", ErrorSeverity.HIGH),
                context={
                    "tag": tag,
                    "date": date,
                    "firms_count": len(firms) if firms else 0
                }
            )
            raise

    logs.info(f"Total number of isins in tag {tag}: {len(tag_df)}")
    logs.info(f'Number of isins with no models available: {no_dep_count}')
    logs.info(f'Number of isins submitted for predictions: {submitted_pred_count}')
    logs.info(f'Number of isins with predictions as zero: {zero_pred_count}')
    logs.info(f'Number of isins which were not traded: {no_traded_count}')
    logs.info(f'Number of isins with predictions from sector average: {sector_pred_count}')
    logs.info(f'Number of isins with no sector average available: {no_sector_pred_count}')

    #Calculate Z-score and M-score
    complete_df = complete_df.reset_index(drop=True)
    complete_df['Zscore'], complete_df['M-score'] = commonconn.calculate_score(complete_df['predictions']*100)

    return complete_df, no_dep_count, submitted_pred_count, failed_count, zero_pred_count, no_traded_count, sector_pred_count, no_sector_pred_count

if env == 'prod':
    dates = [datetime.today().strftime("%Y-%m-%d")]
    tag_df, isin_list = commonconn.get_firms_list(tag)
elif env == 'preprod':
    dates = config['parameters']['testing_date']
    tag_df, isin_list = commonconn.get_firms_list(tag)
elif env == 'local':
    dates = config['parameters']['testing_date']
    tag_df, tag_isin_list = commonconn.get_firms_list(tag)
    isin_list = tag_isin_list[0:20]

s3_upload_path, test_upload_path = commonconn.get_upload_paths(tag)

for date in dates:
    #Setting trigger mail
    try:
        body1 = f"Management model run on date {date} has started"
        subject1 = f"Management Model {tag} {schedular} Run Started"
        # commonconn.send_mail(subject1, body1)
        commonconn.send_mail(subject1, body1, errorbool=True)
    except Exception as e:
        logs.error(f"Error while sending trigger mail")

    logs.info(f'run date is: {date}')
    date = (pd.to_datetime(date)-BDay(1)).strftime("%Y-%m-%d")
    logs.info(f'close price date is: {date}')
    job_count = 0
    start_timer = time.time()

    try:
        daily_run_df, no_dep_count, submitted_pred_count, failed_count, zero_pred_count, no_traded_count, sector_pred_count, no_sector_pred_count = daily_predictions(tag_df, isin_list, date, tag)
    except Exception as e:
        logs.error(f"Error while running daily_predictions is: {e}")
        body2 = f"Error while running daily_predictions is: {e}"
        subject2 = f"Error in Management Model {tag} {schedular} run"
        # commonconn.send_mail(subject2, body2)
        commonconn.send_mail(subject2, body2, errorbool=True)

    end_timer = time.time()

    daily_run_df['schedular'] = config['aieq_columns']['schedular']
    daily_run_df['updated_at'] = datetime.now()
    daily_run_df['updated_at'] = pd.to_datetime(daily_run_df['updated_at']).dt.strftime('%Y-%m-%dT%H:%M:%S.%f').str[:-3]
    daily_run_df.reset_index(drop = True, inplace = True)

    print(f'final daily run df is: {daily_run_df}')
    print(daily_run_df.dtypes)
    uploaded_count = len(daily_run_df)

    try:
        if env == 'prod':
            commonconn.s3_client.write_advanced_as_df(daily_run_df, bucketName, f'{s3_upload_path}monthly_prediction_{date}.csv')
            # daily_run_df.parallel_apply(lambda row: commonconn.save_row(row.name, row, versioning_bucket, versioning_filename.replace('date',str(date))), axis=1)
            rows = [(idx, row, versioning_bucket, versioning_filename.replace('date',str(date))) for idx, row in daily_run_df.iterrows()]
            with ThreadPoolExecutor() as executor:
                executor.map(lambda args: commonconn.save_row(*args), rows)
            commonconn.upload_to_ES(daily_run_df)
        elif env == 'preprod':
            commonconn.s3_client.write_advanced_as_df(daily_run_df, bucketName, f"{test_upload_path}monthly_prediction_{date}.csv")
            commonconn.upload_to_ES(daily_run_df)
        elif env == 'local':
            commonconn.s3_client.write_advanced_as_df(daily_run_df, bucketName, f"{test_upload_path}monthly_prediction_{date}.csv")
            print("Final dataframe ready to be uploaded")
    except Exception as e:
        logs.error(f"Error while uploading final data is: {e}")
        body3 = f"Error while uploading final data is: {e}"
        subject3 = f"Error in Management Model {tag} {schedular} run"
        # commonconn.send_mail(subject3, body3)
        commonconn.send_mail(subject3, body3, errorbool=True)

    # Mailing the daily run statistics
    body = f"""For date {date}:
            Total number of isins in tag {tag}: {len(tag_df)}
            Number of isins with no models available = {no_dep_count}
            Number of isins submitted for predictions = {submitted_pred_count}
            Number of isins with failed predictions = {failed_count}
            Number of isins with predictions as zero = {zero_pred_count}
            Number of isins which were not traded =  {no_traded_count}
            Number of isins with predictions from sector average = {sector_pred_count}
            Number of isins with no sector average available = {no_sector_pred_count}
            Number of records uploaded to ES = {uploaded_count}
    """
    subject = f'Management Model {tag} {schedular} Run Statistics'
    commonconn.send_mail(subject, body)

