from imports import *
from common_helper import HelperFuncs

if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()

sys.path.insert(0, os.path.abspath(script_dir))

config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = HelperFuncs()

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")
log_file_path = f"{script_dir}/{config['log_files']['indt1_folder']}/{saved_date}_daily_run.log"

global logs
logs = conn.create_logger(log_file_path)

fin_data_column_list = config['indt1_columns']['fin_column_list']
fin_data_column_list_nodate = config['indt1_columns']['fin_column_list_nodate']

s3_upload_path = config['s3_paths']['indt1_upload_path']
bucketName = config['s3_buckets']['management_bucket']

class Indt1HelperFuncs:
    #Fetches financial raw input data from S3 for daily run
    def get_daily_data_indt1(self, date):
        bucket_name = config['s3_buckets']['financial_bucket']
        folder_name = config['s3_paths']['indt1_finance_input_folder']
        object_key = folder_name + 'data_' + date + '.csv'
        fin_data = conn.s3_client.read_as_dataframe(bucket_name, object_key)[fin_data_column_list]
        return fin_data

    # get quarter on quarter customer life time value:
    def get_customer_life_time_val(self, df):  
        customer_life_time_val = pd.Series(dtype=float)

        for idx, row in df.iterrows():
            last_day_prev_quarter, last_day_prev_year = conn.last_business_days(row['date'])
            curr_year = int(row['date'].split('-')[0])
            isin = row['isin']

            es_df = conn.get_es_data(isin, curr_year, config['es_index']['management_index'], config['aieq_columns']['schedular'])

            # If es_df is not a valid DataFrame or is empty, assign NaN and skip
            if not isinstance(es_df, pd.DataFrame) or es_df.empty:
                print(f"No ES data found for ISIN: {isin}, skipping...")
                customer_life_time_val = pd.concat([customer_life_time_val, pd.Series(np.nan)], ignore_index=True)
                continue

            # Filter to get last available values
            last_q = es_df[es_df['date'] <= last_day_prev_quarter].sort_values(by='date').reset_index(drop=True)
            last_y_q = es_df[es_df['date'] <= last_day_prev_year].sort_values(by='date').reset_index(drop=True)

            if not last_q.empty and not last_y_q.empty and not (last_y_q['total_rev'].iloc[-1] == 0):
                try:
                    pct_change = (last_q['total_rev'].iloc[-1] - last_y_q['total_rev'].iloc[-1]) / last_y_q['total_rev'].iloc[-1]
                    pct_change = (1 + pct_change) ** 2
                    pct_change = (last_q['total_rev'].iloc[-1] - last_q['sga'].iloc[-1]) * pct_change
                except Exception as e:
                    print(f'For ISIN {isin}, error: {e}')
                    pct_change = np.nan
            else:
                pct_change = np.nan

            customer_life_time_val = pd.concat([customer_life_time_val, pd.Series(pct_change)], ignore_index=True)

        # Attach to original dataframe
        df['customer_life_time_val_QoQ'] = customer_life_time_val.reset_index(drop=True)
        print('Done computing customer life time value...')
        return df

    # get foreign tax growth
    def get_foreign_tax_growth(self, df):
        foreign_tax_growth = pd.Series(dtype=float)

        for idx, row in df.iterrows():
            last_day_prev_quarter, last_day_prev_year = conn.last_business_days(row['date'])
            curr_year = int(row['date'].split('-')[0])
            isin = row['isin']

            es_df = conn.get_es_data(isin, curr_year, config['es_index']['management_index'], config['aieq_columns']['schedular'])

            # If invalid or empty, append NaN and skip
            if not isinstance(es_df, pd.DataFrame) or es_df.empty:
                print(f"No ES data found for ISIN: {isin}, skipping...")
                foreign_tax_growth = pd.concat([foreign_tax_growth, pd.Series(np.nan)], ignore_index=True)
                continue

            last_q = es_df[es_df['date'] <= last_day_prev_quarter].sort_values(by='date').reset_index(drop=True)
            last_y_q = es_df[es_df['date'] <= last_day_prev_year].sort_values(by='date').reset_index(drop=True)

            if not last_q.empty and not last_y_q.empty and last_y_q['curr_foreign_taxes'].iloc[-1] != 0:
                try:
                    pct_change = (
                        (last_q['curr_foreign_taxes'].iloc[-1] - last_y_q['curr_foreign_taxes'].iloc[-1]) /
                        last_y_q['curr_foreign_taxes'].iloc[-1]
                    ) * 100
                except Exception as e:
                    print(f'For ISIN {isin}, error: {e}')
                    pct_change = np.nan
            else:
                pct_change = np.nan

            foreign_tax_growth = pd.concat([foreign_tax_growth, pd.Series(pct_change)], ignore_index=True)

        df['foreign_tax_growth_QoQ'] = foreign_tax_growth.reset_index(drop=True)
        print('Done computing foreign tax growth...')
        return df

    # Get daily financial input data for indt1
    def get_daily_data_ES(self, firms, search_date, _schedular):
        search_date_dt_format = datetime.strptime(search_date, '%Y-%m-%d').isoformat() 
        query = {"query": {"bool": {"must": [{"terms": {"isin.keyword": firms}},
                                                        {"match": {"date": search_date_dt_format}}]}}}
        year = search_date.split('-')[0]
        index = f"{config['es_index']['financial_index']}_{year}"
        fin_data = conn.es_client.get_es_hits_as_df(indexes=index, body=query, size=10000)
        fin_data = fin_data[fin_data['schedular'] == _schedular]
        fin_data_column_list = config['indt1_columns']['fin_column_list']
        fin_data = fin_data[fin_data_column_list]
        return fin_data
    
    #Fetches financial input data for daily run and filters on masteractive list
    def get_fin_input(self, firms, date):
        try:
            # og_daily_data_df = self.get_daily_data_indt1(date)
            og_daily_data_df = self.get_daily_data_ES(firms, date, _schedular = config['aieq_columns']['schedular'])
            logs.info(f'number of isins in financial data:- {len(og_daily_data_df)}')
        except:
            print('No data available for ', date)
            body = f'Financial data missing for all indt1 isins for date {date}'
            subject = 'Error in Management Model INDT1 daily run'
            conn.send_mail(subject, body, errorbool=True)
            logs.error(f'Financial data is not available for {date}')
            raise SystemExit('No Input Financial Data Available')
            
        to_keep = [x for x in og_daily_data_df.index if og_daily_data_df.loc[x, 'isin'] in firms]
        og_daily_data_df = og_daily_data_df.loc[to_keep].reset_index(drop = True)
            
        og_daily_data_df['date'] = og_daily_data_df['date'].apply(lambda x: conn.try_convert(x).strftime("%Y-%m-%d"))
        return og_daily_data_df
    
    #Get deployment mapping file and filter on masteractive list
    @staticmethod
    def get_dep_file(firms):
        dep_details = conn.get_deployment_details(bucketName, config['s3_paths']['indt1_deployment_mapping_file'])
        logs.info(f'Length of deployment mapping file: {len(dep_details)}')
        dep_details = dep_details[dep_details['isin'].isin(firms)]
        logs.info(f'len of dep file after filtering based on indt1 isin list: {len(dep_details)}')

        print(f'dep details after filtering is: {dep_details}')
        return dep_details
    
    #Prepares df to be submitted to prediction run helper
    @staticmethod
    def prepare_pred_input(dep_details, daily_data_df):
        dep_details = dep_details[dep_details['isin'].isin(daily_data_df['isin'])]
        pred_input = []
        input_columns = config['indt1_columns']['input_columns']
        pred_input_columns = [x for x in input_columns if x!= 'isin']
        for i in range(len(dep_details)):
            isin = dep_details.iloc[i]['isin']
            single_row = daily_data_df[daily_data_df['isin']==isin]
            single_row = single_row[pred_input_columns]
            single_row.reset_index(drop=True, inplace=True)
            pred_input.append(single_row.values)
        dep_details['test_data'] = pred_input
        dep_details.drop(columns="name", inplace=True)
        deployment_toSubmit = dep_details
        deployment_toSubmit=deployment_toSubmit.rename(columns={'space_id':'deployment_space'})
        deployment_toSubmit = deployment_toSubmit.reset_index(drop=True)
        print(f'dep to submit is: {deployment_toSubmit}')

        return deployment_toSubmit
    
    # Combine prediction output and deployment details, replaces null values in predictions with 0
    @staticmethod
    def merge_dep_to_pred(final, copy_dep_details, daily_data_df, firms):
        isin_merged = pd.merge(final, copy_dep_details[['deployment_id']], on = 'deployment_id')
        isin_merged = isin_merged[isin_merged['isin'].isin(firms)]
        isin_merged = isin_merged.reset_index(drop=True)
        daily_predictions = pd.merge(daily_data_df, isin_merged[['isin','predictions']], how = 'left', on = 'isin')
        daily_predictions['predictions'] = daily_predictions['predictions'].apply(lambda x: 0 if pd.isna(x) else (x[0] if isinstance(x, list) and len(x) != 0 else 0))
        return daily_predictions
    
    # Reverts the forward filled input financial data back to original
    @staticmethod
    def revert_null_values(daily_predictions, og_daily_data_df):
        daily_predictions = daily_predictions.set_index('isin')
        daily_predictions.update(og_daily_data_df.set_index('isin')[fin_data_column_list_nodate])
        daily_predictions = daily_predictions.reset_index()

        daily_predictions = conn.convert_close_change(daily_predictions)
        return daily_predictions

    # Forward fill the failed predictions
    @staticmethod
    def ffill_zero_predictions(df, date):
        daily_predictions = df
        daily_predictions['ER_Copied'] = False
        try:
            prev_bday = (pd.to_datetime(date)-BDay(1)).strftime("%Y-%m-%d")
            logs.info(f'Previous business day used for forward filling: {prev_bday}')
            df_prev_bday = conn.s3_client.read_as_dataframe(bucketName, s3_upload_path + f'monthly_prediction_{prev_bday}.csv')
        except:
            print('******* previous business day, data not availabe for forward filling *******')
            logs.error('Forward filling with previous Bday is not possible')
        
        for idx, row in daily_predictions.iterrows():
            if row['predictions'] == 0:
                prev_pred = df_prev_bday[df_prev_bday['isin'] == row['isin']].reset_index(drop = True)
                if prev_pred.empty:
                    continue
                
                prev_pred = prev_pred['predictions']
                prev_pred = prev_pred.iloc[0]
                daily_predictions.at[idx, 'predictions'] = prev_pred
                daily_predictions.at[idx, 'ER_Copied'] = True
                
        print('|||||||||||||||||||||||||||||||| Forward filling of predictions complete ||||||||||||||||||||||||||||||||')
        return daily_predictions
    
