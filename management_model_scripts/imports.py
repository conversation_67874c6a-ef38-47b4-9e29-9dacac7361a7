import numpy as np 
import requests as req
from pandas import json_normalize
from functools import reduce
import matplotlib.pyplot as plt
import boto3
from pandas.tseries.offsets import BDay
import os, ssl

from datetime import datetime, timedelta, date
from tqdm import tqdm
import io
from io import StringIO 
import threading
import json
import requests
import os
from pandas import json_normalize
from multiprocessing.pool import ThreadPool
import time
import pandas as pd
import logging as logger
from concurrent.futures import ThreadPoolExecutor
import math
from copy import copy
import yaml
import logging

from ibm_watsonx_ai import APIClient
from ibm_watsonx_ai import Credentials as IBM_Creds
from ibm_watsonx_ai.experiment import AutoAI
from ibm_watsonx_ai.deployment import WebService, Batch
from ibm_watsonx_ai.helpers.connections import DataConnection, S3Location

from eq_common_utils.utils.config.es_config import es_config
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.opensearch_helper import OpenSearch
from eq_common_utils.utils.ibm_helper import IBMConnection
from eq_common_utils.ds_scripts.prediction_helper import PredictionHelper as PREDICTIONHelper
from eq_common_utils.utils.logger_handler import LoggerHandler
from eq_common_utils.utils.metrics_helper import MetricsHelper
from eq_common_utils.entities.firms import Firms

import configparser
import argparse

import smtplib
from email.mime.text import MIMEText
import sys

import base64
import mimetypes
from email.message import EmailMessage
from typing import List, Optional, Union

import google.auth
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google.oauth2.credentials import Credentials

import traceback
import warnings
warnings.filterwarnings('ignore')

# from pandarallel import pandarallel
# pandarallel.initialize(progress_bar=True,nb_workers=4)   