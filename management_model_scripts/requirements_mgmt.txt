aiohttp==3.9.5
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.4.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asgiref==3.8.1
asttokens==2.4.1
async-lru==2.0.4
async-timeout==4.0.3
attrs==23.2.0
aws-requests-auth==0.4.3
babel==2.16.0
backoff==2.2.1
bcrypt==4.1.3
beautifulsoup4==4.12.3
bleach==6.1.0
boto3==1.34.139
botocore==1.34.139
build==1.2.1
cachetools==5.3.3
certifi==2024.7.4
cffi==1.17.1
charset-normalizer==3.3.2
chroma-hnswlib==0.7.3
chromadb==0.5.3
click==8.1.7
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.3.0
copulas==0.11.1
croniter==1.4.1
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.5
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.14
dill==0.3.8
distro==1.9.0
dnspython==2.6.1
email_validator==2.2.0
et-xmlfile==1.1.0
Events==0.5
exceptiongroup==1.2.1
executing==2.1.0
fastapi==0.111.0
fastapi-cli==0.0.4
fastjsonschema==2.20.0
filelock==3.15.4
flatbuffers==24.3.25
fonttools==4.53.1
fqdn==1.5.1
fredapi==0.5.2
frozenlist==1.4.1
fsspec==2023.6.0
google-auth==2.31.0
googleapis-common-protos==1.63.2
greenlet==3.0.3
grpcio==1.64.1
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
huggingface-hub==0.23.4
humanfriendly==10.0
ibm-cos-sdk==2.13.6
ibm-cos-sdk-core==2.13.6
ibm-cos-sdk-s3transfer==2.13.6
ibm_watsonx_ai==1.1.8
idna==3.7
importlib_metadata==7.1.0
importlib_resources==6.4.0
ipykernel==6.29.5
ipython==8.27.0
ipywidgets==8.1.5
isoduration==20.11.0
jedi==0.19.1
Jinja2==3.1.4
jmespath==1.0.1
joblib==1.3.2
json5==0.9.25
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.2
jupyter_core==5.7.2
jupyter_scheduler==2.8.0
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.2.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
kiwisolver==1.4.7
kubernetes==30.1.0
langchain==0.1.17
langchain-community==0.0.36
langchain-core==0.1.52
langchain-openai==0.1.6
langchain-text-splitters==0.0.2
langchainhub==0.1.20
langsmith==0.1.83
logger-tt==1.7.3
lomond==0.3.3
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.3
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
mmh3==4.1.0
monotonic==1.6
mpmath==1.3.0
multidict==6.0.5
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
notebook==7.2.2
notebook_shim==0.2.4
numpy==1.26.4
numpy-ext==0.9.9
nvidia-nccl-cu12==2.22.3
oauthlib==3.2.2
onnxruntime==1.18.1
openai==1.35.7
openpyxl==3.1.5
opensearch-py==2.7.1
opentelemetry-api==1.25.0
opentelemetry-exporter-otlp-proto-common==1.25.0
opentelemetry-exporter-otlp-proto-grpc==1.25.0
opentelemetry-instrumentation==0.46b0
opentelemetry-instrumentation-asgi==0.46b0
opentelemetry-instrumentation-fastapi==0.46b0
opentelemetry-proto==1.25.0
opentelemetry-sdk==1.25.0
opentelemetry-semantic-conventions==0.46b0
opentelemetry-util-http==0.46b0
orjson==3.10.1
overrides==7.7.0
packaging==23.2
pandarallel==1.6.5
pandas==2.1.4
pandocfilters==1.5.1
parso==0.8.4
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.2.2
plotly==5.24.1
posthog==3.5.0
prometheus_client==0.20.0
prompt_toolkit==3.0.47
protobuf==4.25.3
psutil==5.9.8
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==17.0.0
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycparser==2.22
pydantic==2.8.0
pydantic_core==2.20.0
Pygments==2.18.0
pyparsing==3.1.4
PyPika==0.48.9
pyproject_hooks==1.1.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==2.0.7
python-multipart==0.0.9
pytz==2023.3
PyYAML==6.0.1
pyzmq==26.2.0
referencing==0.35.1
regex==2024.5.15
requests==2.32.2
requests-aws4auth==1.3.1
requests-oauthlib==2.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.7.1
rpds-py==0.20.0
rsa==4.9
s3transfer==0.10.2
scikit-learn==1.5.2
scipy==1.14.0
Send2Trash==1.8.3
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.31
stack-data==0.6.3
starlette==0.37.2
sympy==1.12.1
tabulate==0.9.0
tenacity==8.4.2
terminado==0.18.1
threadpoolctl==3.5.0
tiktoken==0.7.0
tinycss2==1.3.0
tokenizers==0.19.1
tomli==2.0.1
tornado==6.4.1
tqdm==4.66.4
traitlets==5.14.3
typer==0.12.3
types-python-dateutil==2.9.0.20240821
types-requests==2.32.0.20240622
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
ujson==5.10.0
uri-template==1.3.0
urllib3==2.2.2
uvicorn==0.30.1
uvloop==0.19.0
watchfiles==0.22.0
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==12.0
widgetsnbextension==4.0.13
wrapt==1.16.0
xgboost==2.1.0
yarl==1.9.4
zipp==3.19.2
expiringdict
pandas-market-calendars
ibm_watson_machine_learning
eq-common-utils@git+https://<EMAIL>/EqubotAI/eq-common-utils.git@v0.14
google-api-python-client
google-auth-httplib2
google-auth-oauthlib
