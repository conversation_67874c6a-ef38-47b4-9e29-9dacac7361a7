es_index:
        management_index: eq_management_model
        financial_index: eq_financial_model
        management_metrics_index: eq_management_model_metrics
        preprod_management_index: pre_management_model

ibm:
        url: https://us-south.ml.cloud.ibm.com
        api_key: fMr33QZg4migeleRziRbqEmrCGTbqTGffjudhbfHrSxk

s3_buckets:
        management_bucket: management-model-retraining-bucket
        financial_bucket: financial-model
        job_tracking_bucket: autoai-jobs

masteractive:
        masters_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=

s3_paths:
        aieq_upload_path: Feb_2024_MGMT_Model/AIEQ_daily_run_monthly_predictions/
        indt1_upload_path: Feb_2024_MGMT_Model/INDT1_daily_run_monthly_predictions/
        aigo_upload_path: Feb_2024_MGMT_Model/AIGO_daily_run_monthly_predictions/
        aieq_finance_input_folder: all_data/aieq/monthly/daily_run/raw_data/
        indt1_finance_input_folder: all_data/india/monthly/daily_run/raw_data/
        aigo_finance_input_folder: aigo/daily_predictions/raw_data/
        esg_object_key_aieq: Feb_2024_MGMT_Model/AIEQ_ESG_DATA.csv
        esg_object_key_indt1: Feb_2024_MGMT_Model/INDIAN_ESG_DATA.csv
        esg_object_key_aigo: Feb_2024_MGMT_Model/AIGO_ESG_DATA.csv
        aieq_deployment_mapping_file: Feb_2024_MGMT_Model/ibm_deployment_details/AIEQ_Monthly/management_model_aieq_2024.csv
        indt1_deployment_mapping_file: Feb_2024_MGMT_Model/ibm_deployment_details/INDT1_Monthly/management_model_indt1_2024.csv
        aigo_deployment_mapping_file: Feb_2024_MGMT_Model/ibm_deployment_details/AIGO_Monthly/management_model_aigo_2024.csv
        ibm_job_count_aieq: management-model/aieq_monthly.csv
        ibm_job_count_indt1: management-model/indt1_monthly.csv
        ibm_job_count_aigo: management-model/aigo_monthly.csv
        aieq_metrics_upload_path: Feb_2024_MGMT_Model/metrics/AIEQ/monthly/
        aigo_metrics_upload_path: Feb_2024_MGMT_Model/metrics/AIGO/monthly/
        indt1_metrics_upload_path: Feb_2024_MGMT_Model/metrics/INDT1/monthly/
        aieq_testing_path: Feb_2024_MGMT_Model/prod_script_testing/aieq_monthly_predictions/
        indt1_testing_path: Feb_2024_MGMT_Model/prod_script_testing/indt1_monthly_predictions/
        aigo_testing_path: Feb_2024_MGMT_Model/prod_script_testing/aigo_monthly_predictions/
        aieq_metrics_testing_path: Feb_2024_MGMT_Model/prod_script_testing/aieq_monthly_metrics/
        indt1_metrics_testing_path: Feb_2024_MGMT_Model/prod_script_testing/indt1_monthly_metrics/
        aigo_metrics_testing_path: Feb_2024_MGMT_Model/prod_script_testing/aigo_monthly_metrics/

log_files:
        aieq_folder: Log_Files_Management_AIEQ
        aigo_folder: Log_Files_Management_AIGO
        indt1_folder: Log_Files_Management_INDT1
        aieq_metrics_folder: Metrics_Logs_Management_AIEQ
        aigo_metrics_folder: Metrics_Logs_Management_AIGO
        indt1_metrics_folder: Metrics_Logs_Management_INDT1

mail:
        sender_email: <EMAIL>
        receiver_emails_local: ['<EMAIL>']
        receiver_emails_preprod: ['<EMAIL>', '<EMAIL>']
        receiver_emails_prod: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        receiver_emails_error: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        password: kgzv vlxs gtqs bznn
        scopes: ['https://www.googleapis.com/auth/gmail.send']
        creds_bucket: etf-predictions
        creds_path: preportfolio/gmail_credentials/credentials.json

aieq_columns:
        input_columns: ['date', 'isin', 'est_act_return_assets_ciq', 'ebitda_margin', 'return_equity', 'sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'customer_life_time_val', 'rd_over_revenue', 'cogs_over_revenue', 'foreign_tax_growth', 'env_val', 'social_val', 'gov_val']
        fin_column_list: ['date','isin','est_act_return_assets_ciq','ebitda_margin','return_equity','sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice' ,'close_change_yesterday', 'close_change_weekly', 'close_change_monthly']
        fin_columns_nodate: ['est_act_return_assets_ciq','ebitda_margin','return_equity','sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice' ,'close_change_yesterday', 'close_change_weekly', 'close_change_monthly']
        schedular: Monthly

indt1_columns:
        fin_column_list: ['date','isin','ebitda','ebitda_margin', 'return_equity', 'marketcap', 'sga', 'eps_1yr_ann_growth','cogs','total_rev_1yr_ann_growth','est_act_return_assets_ciq','tev','curr_foreign_taxes','total_rev','sga_margin','rd_exp','closeprice','volume','close_change_yesterday','close_change_weekly','close_change_monthly']
        fin_column_list_nodate: ['ebitda','ebitda_margin', 'return_equity', 'marketcap', 'sga', 'eps_1yr_ann_growth','cogs','total_rev_1yr_ann_growth','est_act_return_assets_ciq','tev','curr_foreign_taxes','total_rev','sga_margin','rd_exp','closeprice','volume','close_change_yesterday','close_change_weekly','close_change_monthly']
        input_columns: ['date', 'isin', 'est_act_return_assets_ciq', 'ebitda_margin', 'return_equity', 'sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'rd_over_revenue', 'cogs_over_revenue', 'customer_life_time_val_QoQ', 'foreign_tax_growth_QoQ', 'env_val', 'social_val', 'gov_val']
        input_columns_nodate: ['est_act_return_assets_ciq', 'ebitda_margin', 'return_equity', 'sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'rd_over_revenue', 'cogs_over_revenue', 'customer_life_time_val_QoQ', 'foreign_tax_growth_QoQ', 'env_val', 'social_val', 'gov_val']

aigo_columns:
        fin_column_list: ['date','isin','est_act_return_assets_ciq','ebitda_margin','return_equity','sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice' ,'close_change_yesterday', 'close_change_weekly', 'close_change_monthly']
        fin_column_list_nodate: ['est_act_return_assets_ciq','ebitda_margin','return_equity','sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice' ,'close_change_yesterday', 'close_change_weekly', 'close_change_monthly']
        input_columns: ['date', 'isin', 'est_act_return_assets_ciq', 'ebitda_margin', 'return_equity', 'sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'rd_over_revenue', 'cogs_over_revenue', 'customer_life_time_val_QoQ', 'foreign_tax_growth_QoQ', 'env_val', 'social_val', 'gov_val']
        input_columns_nodate: ['est_act_return_assets_ciq', 'ebitda_margin', 'return_equity', 'sga_margin', 'eps_1yr_ann_growth', 'total_rev_1yr_ann_growth', 'marketcap', 'sga', 'total_rev', 'tev', 'volume', 'curr_foreign_taxes', 'rd_exp', 'cogs', 'closeprice', 'close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'rd_over_revenue', 'cogs_over_revenue', 'customer_life_time_val_QoQ', 'foreign_tax_growth_QoQ', 'env_val', 'social_val', 'gov_val']

parameters:
        prev_fin_data_ffill_days: 31
        testing_date: ['2025-04-17']
        model_quarter: Q4
        sector_identifier: sector-average
        pred_column_name: predictions
        model_key: management_model

s3_version_control:
        bucket: eq-model-output
        preprod_bucket: eq-pre-model-output
        filename_monthly: management_model/monthly/date/predictions/isin.csv
        metrics_filename_monthly: management_model/monthly/date/metrics/isin.csv