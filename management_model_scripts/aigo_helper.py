from imports import *
from common_helper import HelperFuncs

if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()

sys.path.insert(0, os.path.abspath(script_dir))

config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = HelperFuncs()

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")
log_file_path = f"{script_dir}/{config['log_files']['aigo_folder']}/{saved_date}_daily_run.log"

global logs
logs = conn.create_logger(log_file_path)

fin_data_column_list = config['aigo_columns']['fin_column_list']
fin_data_column_list_nodate = config['aigo_columns']['fin_column_list_nodate']

s3_upload_path = config['s3_paths']['aigo_upload_path']
bucketName = config['s3_buckets']['management_bucket']

class AigoHelperFuncs:
    #Fetches financial raw input data from S3 for daily run
    def get_daily_data_aigo(self, date):
        bucket_name = config['s3_buckets']['financial_bucket']
        folder_name = config['s3_paths']['aigo_finance_input_folder']
        object_key = folder_name + 'data_' + date + '.csv'
        fin_data = conn.s3_client.read_as_dataframe(bucket_name, object_key)[fin_data_column_list]
        return fin_data
    
    # get quarter on quarter customer life time value:
    @staticmethod
    def get_customer_life_time_val(daily_data_df):
        daily_data_df['customer_life_time_val_QoQ'] = np.nan
        
        for idx, row in daily_data_df.iterrows():
            last_day_prev_quarter, last_day_prev_year = conn.last_business_days(row['date'])
            curr_year = int(row['date'].split('-')[0])
            es_df = conn.get_es_data(row['isin'], curr_year, config['es_index']['management_index'], config['aieq_columns']['schedular'])

            if not isinstance(es_df, pd.DataFrame) or es_df.empty:
                continue

            es_df['total_rev'] = pd.to_numeric(es_df['total_rev'], errors='coerce')
            es_df['sga'] = pd.to_numeric(es_df['sga'], errors='coerce')

            last_q = es_df[es_df['date'] <= last_day_prev_quarter].sort_values(by='date').reset_index(drop=True)
            last_y_q = es_df[es_df['date'] <= last_day_prev_year].sort_values(by='date').reset_index(drop=True)

            isin = row['isin']
            if not last_q.empty and not last_y_q.empty and not (float(last_y_q['total_rev'].iloc[-1]) == 0):
                try:
                    pct_change = (float(last_q['total_rev'].iloc[-1]) - float(last_y_q['total_rev'].iloc[-1]))/float(last_y_q['total_rev'].iloc[-1])
                    pct_change = (1+pct_change)**2
                    pct_change = (float(last_q['total_rev'].iloc[-1]) - float(last_q['sga'].iloc[-1])) * pct_change
                    daily_data_df.at[idx, 'customer_life_time_val_QoQ'] = pct_change
                except Exception as e:
                    print(f'For isin {isin}, error is {e}')
        
        print('done with customer life time value...')
        return daily_data_df
    
    # get foreign tax growth
    @staticmethod
    def get_foreign_tax_growth(daily_data_df):
        daily_data_df['foreign_tax_growth_QoQ'] = np.nan
        
        for idx, row in daily_data_df.iterrows():
            last_day_prev_quarter, last_day_prev_year = conn.last_business_days(row['date'])
            curr_year = int(row['date'].split('-')[0])
            es_df = conn.get_es_data(row['isin'], curr_year, config['es_index']['management_index'], config['aieq_columns']['schedular'])
            if es_df.empty:
                continue
                
            last_q = es_df[es_df['date'] <= last_day_prev_quarter]
            last_q = last_q.sort_values(by = 'date').reset_index(drop = True)
            last_y_q = es_df[es_df['date'] <= last_day_prev_year]
            last_y_q = last_y_q.sort_values(by = 'date').reset_index(drop = True)
            
            if not last_q.empty and not last_y_q.empty and not (float(last_y_q['curr_foreign_taxes'].iloc[-1]) == 0):
                pct_change = ((float(last_q['curr_foreign_taxes'].iloc[-1]) - float(last_y_q['curr_foreign_taxes'].iloc[-1]))/float(last_y_q['curr_foreign_taxes'].iloc[-1])) * 100
                daily_data_df.at[idx, 'foreign_tax_growth_QoQ'] = pct_change
        
        print('done with foreign tax growth')
        return daily_data_df
    
    # Get daily financial input data from elastic search for aigo
    def get_daily_data_ES(self, firms, search_date, _schedular):
        search_date_dt_format = datetime.strptime(search_date, '%Y-%m-%d').isoformat() 
        query = {"query": {"bool": {"must": [{"terms": {"isin.keyword": firms}},
                                                        {"match": {"date": search_date_dt_format}}]}}}
        year = search_date.split('-')[0]
        index = f"{config['es_index']['financial_index']}_{year}"
        fin_data = conn.es_client.get_es_hits_as_df(indexes=index, body=query, size=10000)
        fin_data = fin_data[fin_data['schedular'] == _schedular]
        fin_data_column_list = config['aigo_columns']['fin_column_list']
        fin_data = fin_data[fin_data_column_list]
        return fin_data
    
    #Fetches financial input data for daily run and filters on masteractive list
    def get_fin_input(self, firms, date):
        try:
            og_daily_data_df = self.get_daily_data_aigo(date)
            # og_daily_data_df = self.get_daily_data_ES(firms, date, _schedular = config['aieq_columns']['schedular'])
            logs.info(f'number of isins in financial data:- {len(og_daily_data_df)}')
        except:
            print('No data available for ', date)
            body = f'Financial data missing for all aigo isins for date {date}'
            subject = 'Error in Management Model AIGO daily run'
            conn.send_mail(subject, body, errorbool=True)
            logs.error(f'Financial data is not available for {date}')
            raise SystemExit('No Input Financial Data Available')
            
        to_keep = [x for x in og_daily_data_df.index if og_daily_data_df.loc[x, 'isin'] in firms]
        og_daily_data_df = og_daily_data_df.loc[to_keep].reset_index(drop = True)
            
        og_daily_data_df['date'] = og_daily_data_df['date'].apply(lambda x: conn.try_convert(x).strftime("%Y-%m-%d"))
        return og_daily_data_df
    
    #Get deployment mapping file and filter on masteractive list
    @staticmethod
    def get_dep_file(firms):
        dep_details = conn.get_deployment_details(bucketName, config['s3_paths']['aigo_deployment_mapping_file'])
        logs.info(f'Length of deployment mapping file: {len(dep_details)}')
        dep_details = dep_details[dep_details['isin'].isin(firms)]
        logs.info(f'len of dep file after filtering based on indt1 isin list: {len(dep_details)}')

        print(f'dep details after filtering is: {dep_details}')
        return dep_details
    
    #Prepares df to be submitted to prediction run helper
    @staticmethod
    def prepare_pred_input(dep_details, daily_data_df):
        dep_details = dep_details[dep_details['isin'].isin(daily_data_df['isin'])]
        pred_input = []
        input_columns = config['aigo_columns']['input_columns']
        pred_input_columns = [x for x in input_columns if x!= 'isin']
        for i in range(len(dep_details)):
            isin = dep_details.iloc[i]['isin']
            single_row = daily_data_df[daily_data_df['isin']==isin]
            single_row = single_row[pred_input_columns]
            single_row.reset_index(drop=True, inplace=True)
            pred_input.append(single_row.values)
        dep_details['test_data'] = pred_input
        dep_details.drop(columns="name", inplace=True)
        deployment_toSubmit = dep_details
        deployment_toSubmit=deployment_toSubmit.rename(columns={'space_id':'deployment_space'})
        deployment_toSubmit = deployment_toSubmit.reset_index(drop=True)
        print(f'dep to submit is: {deployment_toSubmit}')

        return deployment_toSubmit
    
    # Combine prediction output and deployment details, replaces null values in predictions with 0
    @staticmethod
    def merge_dep_to_pred(final, copy_dep_details, daily_data_df, firms):
        isin_merged = pd.merge(final, copy_dep_details[['deployment_id']], on = 'deployment_id')
        isin_merged = isin_merged[isin_merged['isin'].isin(firms)]
        isin_merged = isin_merged.reset_index(drop=True)
        daily_predictions = pd.merge(daily_data_df, isin_merged[['isin','predictions']], how = 'left', on = 'isin')
        daily_predictions['predictions'] = daily_predictions['predictions'].apply(lambda x: 0 if pd.isna(x) else (x[0] if isinstance(x, list) and len(x) != 0 else 0))
        return daily_predictions
    
    # Reverts the forward filled input financial data back to original
    @staticmethod
    def revert_null_values(daily_predictions, og_daily_data_df):
        daily_predictions = daily_predictions.set_index('isin')
        daily_predictions.update(og_daily_data_df.set_index('isin')[fin_data_column_list_nodate])
        daily_predictions = daily_predictions.reset_index()

        daily_predictions = conn.convert_close_change(daily_predictions)
        return daily_predictions
    
    # Forward fill the failed predictions
    @staticmethod
    def ffill_zero_predictions(df, date):
        daily_predictions = df
        daily_predictions['ER_Copied'] = False
        try:
            prev_bday = (pd.to_datetime(date)-BDay(1)).strftime("%Y-%m-%d")
            logs.info(f'Previous business day used for forward filling: {prev_bday}')
            df_prev_bday = conn.s3_client.read_as_dataframe(bucketName, s3_upload_path + f'monthly_prediction_{prev_bday}.csv')
        except:
            print('******* previous business day, data not availabe for forward filling *******')
            logs.error('Forward filling with previous Bday is not possible')
        
        for idx, row in daily_predictions.iterrows():
            if row['predictions'] == 0:
                prev_pred = df_prev_bday[df_prev_bday['isin'] == row['isin']].reset_index(drop = True)
                if prev_pred.empty:
                    continue
                
                prev_pred = prev_pred['predictions']
                prev_pred = prev_pred.iloc[0]
                daily_predictions.at[idx, 'predictions'] = prev_pred
                daily_predictions.at[idx, 'ER_Copied'] = True
                
        print('|||||||||||||||||||||||||||||||| Forward filling of predictions complete ||||||||||||||||||||||||||||||||')
        return daily_predictions