from imports import *
from common_helper import HelperFuncs

commonconn = HelperFuncs()

parser = argparse.ArgumentParser(prog='Management Metrics Prod Script', description='Fetches tag, schedular and environment from cron job')
parser.add_argument('-t', '--tag')
parser.add_argument('-s', '--schedular')
parser.add_argument('-e', '--environment')
args = parser.parse_args()

schedular = args.schedular
tag = args.tag
env = args.environment

print(f'tag is: {tag}, schedular is: {schedular}, environment is: {env}')

script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")

log_file_path = f"{script_dir}/{config['log_files'][f'{tag}_metrics_folder']}/{saved_date}_metrics_calculation.log"

global logs
logs = commonconn.create_logger(log_file_path)

model_key = config['parameters']['model_key']
bucketName = config['s3_buckets']['management_bucket']

def daily_metrics():
    s3_upload_path, test_upload_path = commonconn.get_upload_paths(tag)
    pred_path = f"{s3_upload_path}monthly_prediction_{saved_date}.csv"
    pred_df = commonconn.s3_client.read_advanced_as_df(bucketName, pred_path)
    traded_count = len(pred_df)
    print(f"pred df is: {pred_df}")

    if env == 'local':
        pred_df = pred_df[490:500]

    isin_list = pred_df['isin'].values.tolist()
    firm_list=[]
    for isin in isin_list:
        firm_list.append(Firms(isin, schedular='Monthly'))
    build_dates = [pd.to_datetime(saved_date)]

    df_metrics = MetricsHelper(commonconn.es_client, 'prod').calculate_metrics(firm_list, build_dates, model_key, primary_key='isin',read_from='es',metrics_to_calculate=None)
    print(f"df metrics is: {df_metrics}")
    succ_metrics = len(df_metrics)

    return df_metrics, traded_count, succ_metrics

# Setting trigger mail
try:
    body1 = f"Management model metrics run for date {saved_date} has started"
    subject1 = f"Management Model {tag} {schedular} Metrics Run Started"
    commonconn.send_mail(subject1, body1)
    # commonconn.send_mail(subject1, body1, errorbool=True)
except Exception as e:
    logs.error(f"Error while sending trigger mail")

try:
    df_metrics, traded_count, succ_metrics = daily_metrics()
except Exception as e:
    logs.error(f"Error while running daily_metrics is: {e}")
    trace = traceback.format_exc()
    body2 = f'''Error while running daily_metrics is: {e}
    Traceback is: {trace}'''
    subject2 = f"Error in Management Model {tag} {schedular} Metrics run"
    commonconn.send_mail(subject2, body2)
    # commonconn.send_mail(subject2, body2, errorbool=True)

try:
    commonconn.upload_metrics_data(df_metrics, tag, saved_date)
except Exception as e:
    logs.error(f"Error while uploading final metrics data is: {e}")
    body3 = f"Error while uploading final metrics data is: {e}"
    subject3 = f"Error in Management Model {tag} {schedular} Metrics run"
    commonconn.send_mail(subject3, body3)
    # commonconn.send_mail(subject3, body3, errorbool=True)

# Mailing the daily metrics run statistics
try:
    body = f"""For date {saved_date}:
            Number of traded isins = {traded_count}
            Number of isins with metrics successfully calculated = {succ_metrics}
            Number of isins with missing metrics = {traded_count - succ_metrics}
    """
    subject = f'Management Model {tag} {schedular} Metrics Run Statistics'
    commonconn.send_mail(subject, body)
except Exception as e:
    logs.error(f"Error while sending stats mail is: {e}")
    print(f"Error while sending stats mail is: {e}")