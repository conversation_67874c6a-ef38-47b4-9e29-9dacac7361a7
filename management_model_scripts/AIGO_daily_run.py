from imports import *
from common_helper import HelperFuncs
from aigo_helper import AigoHelperFuncs

if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()

sys.path.insert(0, os.path.abspath(script_dir))

config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = HelperFuncs()
aigoconn = AigoHelperFuncs()

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")
log_file_path = f"{script_dir}/{config['log_files']['aigo_folder']}/{saved_date}_daily_run.log"

global logs
logs = conn.create_logger(log_file_path)

input_columns = config['aigo_columns']['input_columns']
input_columns_nodate = config['aigo_columns']['input_columns_nodate']

fin_data_column_list = config['aigo_columns']['fin_column_list']
fin_data_column_list_nodate = config['aigo_columns']['fin_column_list_nodate']

s3_upload_path = config['s3_paths']['aigo_upload_path']
bucketName = config['s3_buckets']['management_bucket']
versioning_bucket = config['s3_version_control']['bucket']
versioning_filename = config['s3_version_control']['filename_monthly']

def daily_predictions(firms, date):
    og_daily_data_df = aigoconn.get_fin_input(firms, date)

    dep_details = aigoconn.get_aigo_dep_file(firms)
    copy_dep_details=copy(dep_details)
    missing_isin = set(firms) - set(dep_details['isin'].values)
    no_dep_count = len(missing_isin)

    pred_isin_list = [isin for isin in og_daily_data_df['isin'].values if isin not in missing_isin]
    og_daily_data_df = og_daily_data_df[og_daily_data_df['isin'].isin(pred_isin_list)]

    print(f'og daily data df after filtering is: {og_daily_data_df}')

    daily_data_df = conn.fill_missing_fin_values(og_daily_data_df)
    print(f'daily data df after filling input features is: {daily_data_df}')

    # Calculate on the fly features
    daily_data_df = conn.get_rd_over_revenue(daily_data_df)
    daily_data_df = conn.get_cogs_over_revenue(daily_data_df)
    daily_data_df = aigoconn.QoQ_get_customer_life_time_val(daily_data_df)
    daily_data_df = aigoconn.QoQ_get_foreign_tax_growth(daily_data_df)

    # collect ESG data
    ESG_object_key = config['s3_paths']['esg_object_key_aigo']
    daily_esg_data = conn.s3_client.read_as_dataframe(bucketName, ESG_object_key)
    daily_data_df = pd.merge(daily_data_df,daily_esg_data,on = 'isin', how='left')
    daily_data_df.fillna(0,inplace=True)
    print('*******************************ESG data collected*******************************')

    daily_data_df = conn.convert_close_change(daily_data_df)

    # get the column order
    daily_data_df = daily_data_df[input_columns]
    daily_data_df = conn.preprocessing(daily_data_df)

    deployment_toSubmit = aigoconn.prepare_pred_input(dep_details, daily_data_df)    
    print('*********************Final deployments to submit dataset prepared for predictions***************************')

    final = conn.get_predictions(deployment_toSubmit)
    print(f'get predictions output is {final}')

    daily_predictions = aigoconn.merge_dep_to_pred(final, copy_dep_details, daily_data_df, firms)
    print(f'daily predictions after merging daily data df is: {daily_predictions}')

    # Reverting back the NaN values 
    daily_predictions = aigoconn.revert_null_values(daily_predictions, og_daily_data_df)
    print(f'daily predictions after reverting is {daily_predictions}')

    ###################### forward fill the zero predictions with previous day's values ########################
    daily_predictions = aigoconn.ffill_zero_predictions(daily_predictions, date)

    total_pred_count = len(daily_predictions)
    zero_pred_count = (daily_predictions['predictions'] == 0).sum()
    ffill_pred_count = len(daily_predictions[daily_predictions['ER_Copied']== True])

    logs.info(f'Number of isins with no models available: {no_dep_count}')
    logs.info(f'Number of predictions generated: {total_pred_count}')
    logs.info(f'Number of isins with predictions as zero: {zero_pred_count}')
    logs.info(f'Number of isins for which predictions have been forward filled: {ffill_pred_count}')

    #Calculate Z-score and M-score
    daily_predictions = daily_predictions.reset_index(drop=True)
    daily_predictions['Zscore'], daily_predictions['M-score'] = conn.calculate_score(daily_predictions['predictions']*100)

    # Mailing the daily run statistics
    body = f"""For date {date}:
            Number of isins with no models available = {no_dep_count}
            Total number of predictions generated = {total_pred_count}
            Number of isins with predictions as zero = {zero_pred_count}
            Number of isins for which predictions have been forward filled = {ffill_pred_count}
    """
    subject = 'Management Model AIGO Daily Run Statistics'
    conn.send_email(body, subject)
    
    return daily_predictions

#Get AIGO Masteractive list
try:
    aieq_isin_df = conn.get_master_df('aieq')
    indt1_isin_df = conn.get_master_df('indt1')
    aigo_isin_df = conn.get_master_df('aigo')
    aigo_isin_df = aigo_isin_df[[(x not in aieq_isin_df['isin'].values) & (x not in indt1_isin_df['isin'].values) for x in aigo_isin_df['isin']]].reset_index(drop = True)
    aigo_isin_list = aigo_isin_df['isin'].tolist()
    logs.info(f'length of masteractive aigo isin list: {len(aigo_isin_list)}')
except Exception as e:
    logs.error(f'Error while fetching data from masteractive: {e}')

test_isin_list = aigo_isin_list[0:20]

env = config['environment']['env']
print(f'Environment is: {env}')

if env == 'prod':
    dates = [datetime.today().strftime("%Y-%m-%d")]
elif env == 'preprod':
    dates = config['parameters']['testing_date']
elif env == 'local':
    dates = config['parameters']['testing_date']

for date in dates:
    #Setting trigger mail
    try:
        body1 = f"Management model aigo run on date {date} has started"
        subject1 = f"Management Model AIGO Daily Run Started"
        conn.send_email(body1, subject1)
    except Exception as e:
        logs.error(f"Error while sending trigger mail")

    logs.info(f'run date is: {date}')
    date = (pd.to_datetime(date)-BDay(1)).strftime("%Y-%m-%d")
    logs.info(f'close price date is: {date}')
    job_count = 0
    start_timer = time.time()
    
    try:
        if env == 'prod':
            daily_run_df = daily_predictions(aigo_isin_list, date)
        elif env == 'preprod':
            daily_run_df = daily_predictions(aigo_isin_list, date)
        elif env == 'local':
            daily_run_df = daily_predictions(test_isin_list, date)
    except Exception as e:
        logs.error(f"Error while running daily_predictions is: {e}")

    end_timer = time.time()
    daily_run_df.reset_index(drop = True, inplace = True)

    print(f'final daily run df is: {daily_run_df}')

    if env == 'prod':
        conn.s3_client.write_advanced_as_df(daily_run_df,config['s3_buckets']['management_bucket'], f'{s3_upload_path}monthly_prediction_{date}.csv')
        daily_run_df.parallel_apply(lambda row: conn.save_row(row.name, row, versioning_bucket, versioning_filename.replace('date',str(date))), axis=1)
        conn.upload_to_ES(daily_run_df)
    elif env == 'preprod':
        conn.s3_client.write_advanced_as_df(daily_run_df,config['s3_buckets']['management_bucket'], f"{config['s3_paths']['aigo_testing_path']}monthly_prediction_{date}.csv")
        conn.upload_to_training_ES(daily_run_df)
    elif env == 'local':
        print("Final dataframe ready to be uploaded")
    
    job_tracking_df = conn.s3_client.read_as_dataframe(config['s3_buckets']['job_tracking_bucket'], config['s3_paths']['ibm_job_count_aigo'])
    curr_tracking_data = {'date': date, 'run_duration': (end_timer-start_timer), 'start_time': start_timer, 'end_time': end_timer, 'job_count':job_count} 
    job_tracking_df = pd.concat([job_tracking_df, pd.DataFrame(curr_tracking_data, index=[0])]).reset_index(drop = True)
    conn.s3_client.write_advanced_as_df(job_tracking_df, config['s3_buckets']['job_tracking_bucket'], config['s3_paths']['ibm_job_count_aigo'])