from imports import *

from common_helper import HelperFuncs
from aieq_helper import AieqHelperFuncs
from aigo_helper import AigoHelperFuncs
from indt1_helper import Indt1HelperFuncs

script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)

parser = argparse.ArgumentParser(prog='Management Metrics Prod Script', description='Fetches tag, schedular and environment from cron job')
parser.add_argument('-t', '--tag')
parser.add_argument('-s', '--schedular')
parser.add_argument('-e', '--environment')
args = parser.parse_args()

schedular = args.schedular
tag = args.tag
env = args.environment

commonconn = HelperFuncs()
aieqconn = AieqHelperFuncs()
aigoconn = AigoHelperFuncs()
indt1conn = Indt1HelperFuncs()

bucketName = config['s3_buckets']['management_bucket']

class DailyHelperFuncs:
    # Prepares input data to be submitted for predictions:
    @staticmethod
    def get_input_data(firms, date, tag):
        if tag == 'aieq':
            tagconn = aieqconn
            ESG_object_key = config['s3_paths']['esg_object_key_aieq']
            input_columns = config['aieq_columns']['input_columns']
        elif tag == 'aigo':
            tagconn = aigoconn
            ESG_object_key = config['s3_paths']['esg_object_key_aigo']
            input_columns = config['aigo_columns']['input_columns']
        elif tag == 'indt1':
            tagconn = indt1conn
            ESG_object_key = config['s3_paths']['esg_object_key_indt1']
            input_columns = config['indt1_columns']['input_columns']

        og_daily_data_df = tagconn.get_fin_input(firms, date)
        print(f'og daily data df is: {og_daily_data_df}')

        dep_details = tagconn.get_dep_file(firms)
        dep_details = commonconn.create_model_identifier(dep_details)
        copy_dep_details=copy(dep_details)
        missing_isin = set(firms) - set(dep_details['isin'].values)
        missing_fin_isin = set(firms) - set(og_daily_data_df['isin'].values)
        no_dep_count = len(missing_isin)
        no_traded_count = len(missing_fin_isin)
        sector_fill_isins = list(set(og_daily_data_df['isin'].values) - set(dep_details['isin'].values))

        pred_isin_list = [isin for isin in og_daily_data_df['isin'].values if isin not in missing_isin]
        # og_daily_data_df = og_daily_data_df[og_daily_data_df['isin'].isin(pred_isin_list)]

        daily_data_df = commonconn.fill_missing_fin_values(og_daily_data_df)
        print(f'daily data df after filling input features is: {daily_data_df}')

        # Calculate on the fly features
        daily_data_df = tagconn.get_customer_life_time_val(daily_data_df)
        daily_data_df = commonconn.get_rd_over_revenue(daily_data_df)
        daily_data_df = commonconn.get_cogs_over_revenue(daily_data_df)
        daily_data_df = tagconn.get_foreign_tax_growth(daily_data_df)

        # collect ESG data
        daily_esg_data = commonconn.s3_client.read_as_dataframe(bucketName, ESG_object_key)
        print('*******************************ESG data collected*******************************')

        daily_data_df = pd.merge(daily_data_df,daily_esg_data,on = 'isin', how='left')
        # daily_data_df.fillna(0,inplace=True)

        daily_data_df = commonconn.convert_close_change(daily_data_df)
        
        # get the column order
        daily_data_df = daily_data_df[input_columns]
        daily_data_df = commonconn.preprocessing(daily_data_df)

        # Filter df for predictions and sector avg
        sector_fill_df = daily_data_df[daily_data_df['isin'].isin(sector_fill_isins)]
        print(f"sector fill df is: {sector_fill_df}")
        daily_data_df = daily_data_df[daily_data_df['isin'].isin(pred_isin_list)]
        print(f"daily data df after filtering for predictions is {daily_data_df}")

        deployment_toSubmit = tagconn.prepare_pred_input(dep_details, daily_data_df)  
        print('*********************Final deployments to submit dataset prepared for predictions***************************')

        return no_dep_count, no_traded_count, og_daily_data_df, daily_data_df, sector_fill_df, copy_dep_details, deployment_toSubmit
    
    # Performs required merging and forward filling
    @staticmethod
    def post_pred_processing(pred_df, copy_dep_details, og_daily_data_df, daily_data_df, sector_fill_df, tag_df, firms, tag, date):
        if tag == 'aieq':
            tagconn = aieqconn
        elif tag == 'aigo':
            tagconn = aigoconn
        elif tag == 'indt1':
            tagconn = indt1conn

        daily_predictions_df = commonconn.merge_final_dfs(pred_df, copy_dep_details, daily_data_df)
        print(f'daily predictions after merging daily data df is: {daily_predictions_df}')

        complete_df, no_sector_pred_count, sector_pred_count = commonconn.add_sector_avg(daily_predictions_df, sector_fill_df, tag_df)
        print(f'complete df after adding sector avgs is: {complete_df}')

        # Reverting back the NaN values 
        complete_df = tagconn.revert_null_values(complete_df, og_daily_data_df)
        print(f'daily predictions after reverting is {complete_df}')

        ###################### forward fill the zero predictions with previous day's values ########################
        # daily_predictions = tagconn.ffill_zero_predictions(daily_predictions, date)

        return complete_df, no_sector_pred_count, sector_pred_count