from imports import *

script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)

parser = argparse.ArgumentParser(prog='Management Metrics Prod Script', description='Fetches tag, schedular and environment from cron job')
parser.add_argument('-t', '--tag')
parser.add_argument('-s', '--schedular')
parser.add_argument('-e', '--environment')
args = parser.parse_args()

schedular = args.schedular
tag = args.tag
env = args.environment

# env = config['environment']['env']
scopes = config['mail']['scopes']

bucketName = config['s3_buckets']['management_bucket']

class HelperFuncs:
    def __init__(self):
        self.s3_client = s3_config()
        self.es_client = es_config(env='prod')
        self.training_es_client = es_config(env='pre')
        self.masters_url = config['masteractive']['masters_url']
        self.ibm_connection = IBMConnection(
            config['ibm']['url'],
            config['ibm']['api_key'])
        self._predictionhelper = PREDICTIONHelper(self.ibm_connection)
    
    @staticmethod
    def create_logger(log_file_path):
        log_dir = os.path.dirname(log_file_path)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)  # Create the directory if it doesn't exist
        logger = logging.getLogger('my_logger')
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            logger.removeHandler(handler)
        file_handler = logging.FileHandler(log_file_path, mode='w')  # Use mode='w' for write mode
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger
    
    #Get data from masteractive for a given tag
    def get_master_df(self, tag):
        master_json = requests.get(f"{self.masters_url}{tag}").json() 
        master = pd.DataFrame(master_json["data"][f"masteractivefirms_{tag}"])
        return master

    #Get financial model input data from ES for daily run
    def get_financial_daily_data_ES(self, firms, search_date, _schedular):
        search_date_dt_format = datetime.strptime(search_date, '%Y-%m-%d').isoformat() 
        query = {"query": {"bool": {"must": [{"terms": {"isin.keyword": firms}},
                                                        {"match": {"date": search_date_dt_format}}]}}}
        year = search_date.split('-')[0]
        index = f"{config['es_index']['financial_index']}_{year}"
        fin_data = self.es_client.get_es_hits_as_df(indexes=index, body=query, size=10000)
        fin_data = fin_data[fin_data['schedular'] == _schedular]
        fin_data_column_list = config['aieq_columns']['fin_column_list']
        fin_data = fin_data[fin_data_column_list]
        return fin_data
    
    #Get ES data for given isin and index for past 3 years
    def get_es_data(self, isin, curr_year, index_prefix, _schedular):
        data=[]
        for year in range(curr_year-2, curr_year + 1):
            q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
            es = self.es_client
            result = es.run_query(query=json.loads(q_total),index=f"{index_prefix}_{year}")

            for rs in result['hits']['hits']:
                es_data=rs['_source']
                data.append(es_data)
        df=pd.DataFrame(data)

        if df.empty:
            print(f'No data in ES for {isin}')
            return df
        df_dates = df['date'].apply(lambda x: pd.to_datetime(x))
        unique_years = list(df_dates.dt.year.unique())

        if curr_year not in unique_years:
            return pd.DataFrame()
        df=df[df['schedular']== _schedular]
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(drop=True, inplace=True)
        return df
    
    #Get ES data for an isin list for a given range of dates
    def get_date_range_es_data(self, isin_list, start_date, end_date, index_prefix, _schedular):
        start_year = (pd.to_datetime(start_date)).year
        end_year = (pd.to_datetime(end_date)).year
        data = []
        hits = []
        for year in range(start_year, end_year + 1):
            q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date,
            "lte": end_date}}},{"term": {"schedular.keyword": _schedular}}]}}}
            try:
                response,total_docs = self.es_client.search_with_pagination(index=f"{index_prefix}_{year}",query=q_total,paginate=False,strict=False)
            except Exception as e:
                pass
            for hit in response:
                es_data=hit['_source']
                data.append(es_data)

        es_df = pd.DataFrame(data)
        es_df['date'] = pd.to_datetime(es_df['date'])
        es_df.sort_values('date', ascending=False, inplace=True)
        es_df.reset_index(inplace=True, drop=True)

        return es_df
    
    #Helper function for z score/m score calculation
    @staticmethod        
    def _score_calculation_helper(z_score, min_threshold, max_threshold):
        score = None
        if z_score > max_threshold:
            score = 10
        elif z_score < min_threshold:
            score = 1
        else:
            score = (z_score - min_threshold) / (max_threshold - min_threshold) * 9 + 1
        return score
    
    #Calculate z score and m score
    def calculate_score(self, predictions): # series of predictions (in percentage)
        processed_predictions = predictions.apply(lambda x: max(min(x, 100), -100)) # preprocess_columns
        Zscore = (processed_predictions - processed_predictions.mean())/(processed_predictions.std())
        min_Z_score_threshold = Zscore.median() - 3 * Zscore.std()
        max_Z_score_threshold = Zscore.median() + 3 * Zscore.std()
        score = Zscore.apply(lambda x: self._score_calculation_helper(x, min_Z_score_threshold, max_Z_score_threshold))
        print("************************* Z Score and M-Score calculated *************************************")
        return Zscore, score
    
    #Fetch management data from ES and forward fill predictions from previous business day
    def forward_fill_predictions(self, daily_predictions, curr_bday):
        previous_bday = (pd.to_datetime(curr_bday)-BDay(1)).strftime("%Y-%m-%d")
        print('curr_bday:', curr_bday, 'previous_bday:', previous_bday)
        for idx, row in daily_predictions.iterrows():
            curr_isin = row['isin']
            if row['predictions'] == 0 or row['predictions'] == np.nan:
                print(f'obtained Zero/NaN prediction for the {curr_isin}')
                curr_year = int(row['date'].split('-')[0])
                es_df = self.get_es_data(row['isin'], curr_year, config['es_index']['management_index'], config['aieq_columns']['schedular'])
                daily_predictions.at[idx, 'predictions'] = es_df.loc[es_df['date'] == previous_bday, 'predictions'].iloc[0]
        return daily_predictions
    
    #Fetch entire data from S3 for previous business day and forward fill all data and upload to S3 and ES
    def complete_ffill_data(self):
        s3_upload_path = config['s3_paths']['aieq_upload_path']
        curr_date = datetime.today().strftime("%Y-%m-%d")
        prev_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")
        prev_date_business_date = (pd.to_datetime(curr_date)-BDay(2)).strftime("%Y-%m-%d")
        print('curr_date:', curr_date, ' prev_date:', prev_date,' prev_date_business_date:', prev_date_business_date)
        prev_dt_df = self.s3_client.read_as_dataframe(config['s3_buckets']['management_bucket'], s3_upload_path + f'monthly_prediction_{prev_date_business_date}.csv')
        
        prev_dt_df['date'] = prev_date
        prev_dt_df['ER_Copied'] = True
        self.s3_client.write_advanced_as_df(prev_dt_df, config['s3_buckets']['management_bucket'], f'{s3_upload_path}monthly_prediction_{prev_date}.csv')
        self.upload_to_ES(prev_dt_df, index = config['es_index']['management_index'])

    #Fetch financial input data for previous x days and forward fill input features wherever values are NaN
    def fill_missing_fin_values(self, daily_data_df):
        x = config['parameters']['prev_fin_data_ffill_days']
        today = datetime.today().strftime("%Y-%m-%d")
        start_date = (pd.to_datetime(today) - timedelta(days=x)).isoformat()
        end_date = (pd.to_datetime(today)-timedelta(1)).isoformat()
        fin_index = config['es_index']['financial_index']
        isin_list = daily_data_df['isin'].values.tolist()
        _schedular = config['aieq_columns']['schedular']

        hist_fin_df = self.get_date_range_es_data(isin_list, start_date, end_date, fin_index, _schedular)

        for index, row in daily_data_df.iterrows():
            for col in daily_data_df.columns:
                if pd.isna(row[col]) and col != 'isin' and col != 'date':
                    isin_value = row['isin']
                    
                    filtered_hist_df = hist_fin_df[(hist_fin_df['isin'] == isin_value) & (~hist_fin_df[col].isna())]
                    
                    if not filtered_hist_df.empty:
                        latest_value = filtered_hist_df.iloc[0][col]
                        daily_data_df.at[index, col] = latest_value

        return daily_data_df
    
    #Replaces infinite and NaN values with 0
    @staticmethod
    def preprocessing(df):
        df.replace([np.inf, -np.inf], 0, inplace=True)
        df = df.fillna(0)
        return df
    
    @staticmethod
    def get_rd_over_revenue(df):
        df['rd_over_revenue'] = df['rd_exp'].divide(df['total_rev'])
        print('done with rd over revenue...')
        return df

    @staticmethod
    def get_cogs_over_revenue(df):
        df['cogs_over_revenue'] = df['cogs'].divide(df['total_rev'])
        print('done with cogs over revenue...')
        return df
    
    #Generates predictions from IBM
    def get_predictions(self, deployment_toSubmit):
        df_pred = pd.DataFrame()
        df_pred = self._predictionhelper.prediction_run(deployment_toSubmit)
        return df_pred
    
    #Uploads data to ES
    def upload_to_ES(self, data):
        final_predictions_copy = data
        preprod_index = config['es_index']['preprod_management_index']
        prod_index = config['es_index']['management_index']

        final_predictions_copy['date'] = final_predictions_copy['date'].astype(str)
        final_predictions_copy['updated_at'] = final_predictions_copy['updated_at'].astype(str)
        
        final_predictions_copy.replace({np.inf:np.nan},inplace=True)
        final_predictions_copy.replace({np.nan: None}, inplace=True)

        if env == 'prod':
            print(self.es_client.save_records_v2(final_predictions_copy, prod_index))
        elif env == 'preprod':
            print(self.training_es_client.save_records_v2(final_predictions_copy, preprod_index))

    #Function for mail tracking
    @staticmethod
    def send_email(body, subject):
        sender_email = config['mail']['sender_email']
        if env == 'prod':
            receiver_emails = config['mail']['receiver_emails_prod']
        elif env == 'preprod':
            receiver_emails = config['mail']['receiver_emails_preprod']
        elif env == 'local':
            receiver_emails = config['mail']['receiver_emails_local']
        message = MIMEText(body)
        password = config['mail']['password']
        with smtplib.SMTP('smtp.gmail.com', 587) as smtp:
            smtp.starttls()
            smtp.login(sender_email, password)
            message['From'] = sender_email
            message['Subject'] = subject
            smtp.sendmail(sender_email, receiver_emails, message.as_string())

    # Get credentials for sending mail
    @staticmethod
    def get_credentials():
        if os.path.exists('./token.json'):
            cred = Credentials.from_authorized_user_file('./token.json', scopes)
            return cred
        else:
            return None
            
    # Create an attachment for mail using file path
    @staticmethod
    def attach_file(message, path : str):   
        # determine the data-type of the file
        mime_type, _ = mimetypes.guess_type(path)

        # error check
        if mime_type == None:
            mime_type= 'application/octet-stream'
            
        _type, subtype = mime_type.split('/')

        # read
        with open(path, 'rb') as f:
            data= f.read()

        # attach to message
        message.add_attachment(data,
                            maintype= _type,
                            subtype= subtype,
                            filename= path.split('/')[-1])
        
    
    # Send mail using ds auto info
    def send_mail(self, subject: str, body: str, errorbool = False, attachments: Optional[List[str]] = None) -> None:
        sender_email = config['mail']['sender_email']

        if errorbool:
            recipients = config['mail']['receiver_emails_error']
        else:
            if env == 'prod':
                recipients = config['mail']['receiver_emails_prod']
            elif env == 'preprod':
                recipients = config['mail']['receiver_emails_preprod']
            elif env == 'local':
                recipients = config['mail']['receiver_emails_local']
            
        # error check
        if len(recipients) == 0:
            print('ERROR : There are no recipients')
            sys.exit(0)

        # GET the credentials to send the mail
        cred= self.get_credentials()
        
        # error-check
        if cred == None:
            pass

        # main
        try:
            # preprocessing: convert the list of receipients into a string
            if len(recipients) == 1:
                recipients= recipients[0]
            else:
                recipients= ', '.join(recipients)
            
            # create client
            service= build("gmail", "v1", credentials= cred)
            
            # create the mnssage
            message= EmailMessage()
            message['From']= sender_email
            message['To']= recipients
            message['Subject']= subject
            message.set_content(body)

            # attach files, if there are any
            if attachments != None:
                for path in attachments:
                    # error check
                    if not os.path.isfile(path):
                        print(f'ERROR: The file (to be attached) {path} does not exist.')
                        sys.exit(0)
                    self.attach_file(message, path)

            # encoded message
            encoded_message= base64.urlsafe_b64encode( message.as_bytes() ).decode()

            create_message= {'raw' : encoded_message}
            # pylint: disable= E1101
            send_message= (
                service.users()
                .messages()
                .send(userId= 'me',
                    body= create_message)
                .execute()
            )
            print(f"message id: {send_message['id']}")
        except HttpError as error:
            print(f'ERROR: Failed to send message. Details:\n{error}')

    @staticmethod
    def try_convert(x):
        try:
            dt = datetime.strptime(x, "%Y-%m-%d")
        except:
            dt = datetime.strptime(x, "%Y-%m-%d %H:%M:%S")
        return dt
    
    def get_deployment_details(self, bucket, dep_mapping_path):
        dep_details = self.s3_client.read_as_dataframe(bucket, dep_mapping_path)
        dep_details['isin'] = dep_details['name'].apply(lambda x:x[:-5])
        return dep_details

    @staticmethod
    def convert_close_change(df):
        df['close_change_yesterday'] = df['close_change_yesterday']/100
        df['close_change_weekly'] = df['close_change_weekly']/100
        df['close_change_monthly'] = df['close_change_monthly']/100
        return df
    
    def get_last_q_last_day(self, ref):
        if ref.month < 4:
            return date(ref.year - 1, 12, 31)
        elif ref.month < 7:
            return date(ref.year, 3, 31)
        elif ref.month < 10:
            return date(ref.year, 6, 30)
        return date(ref.year, 9, 30)
    
    def last_business_days(self, date):
        # Convert the input date string to a pandas datetime object
        date = pd.to_datetime(date)
        # Get the last day of the previous quarter
        last_day_prev_quarter = self.get_last_q_last_day(date)
        last_year_date = last_day_prev_quarter.replace(year = last_day_prev_quarter.year - 1)
        return last_day_prev_quarter.strftime('%Y-%m-%d'), last_year_date.strftime('%Y-%m-%d')
    
    def save_row(self, idx, row, bucket_name, filename):
       row_df = row.to_frame().T
       isin  = row_df['isin'].iloc[0]
       filename = filename.replace('isin',isin)                             
       self.s3_client.write_advanced_as_df(row_df, bucket_name, filename)

    #Get masteractive list for a given tag
    def get_firms_list(self, tag):
        aieq_isin_df = self.get_master_df('aieq')
        indt1_isin_df = self.get_master_df('indt1')
        aigo_isin_df = self.get_master_df('aigo')
        aigo_isin_df = aigo_isin_df[[(x not in aieq_isin_df['isin'].values) & (x not in indt1_isin_df['isin'].values) for x in aigo_isin_df['isin']]].reset_index(drop = True)
        aigo_isin_list = aigo_isin_df['isin'].tolist()
        aieq_isin_list = aieq_isin_df['isin'].tolist()
        indt1_isin_list = indt1_isin_df['isin'].tolist()

        if tag == 'aieq':
            return aieq_isin_df, aieq_isin_list
        elif tag == 'aigo':
            return aigo_isin_df, aigo_isin_list
        elif tag == 'indt1':
            return indt1_isin_df, indt1_isin_list
        
    #Get prod and testing s3 paths for uploading
    @staticmethod
    def get_upload_paths(tag):
        if tag == 'aieq':
            s3_upload_path = config['s3_paths']['aieq_upload_path']
            test_upload_path = config['s3_paths']['aieq_testing_path']
        elif tag == 'aigo':
            s3_upload_path = config['s3_paths']['aigo_upload_path']
            test_upload_path = config['s3_paths']['aigo_testing_path']
        elif tag == 'indt1':
            s3_upload_path = config['s3_paths']['indt1_upload_path']
            test_upload_path = config['s3_paths']['indt1_testing_path']

        return s3_upload_path, test_upload_path
    
    # Creates the model identifer column in the correct format
    @staticmethod
    def create_model_identifier(deployments_df):
        deployments_df['training_datetime'] = pd.to_datetime(deployments_df['training_date'], format='%Y-%m-%d_%H:%M:%S')
        deployments_df['model_year'] = deployments_df['training_datetime'].dt.year.astype(str) + f"_{config['parameters']['model_quarter']}"
        deployments_df['training_timestamp'] = deployments_df['training_datetime'].dt.strftime('%Y-%m-%d_%H:%M:%S')

        deployments_df['model_identifier'] = (
            deployments_df['isin'] + "_" + 
            deployments_df['model_year'] + "_" + 
            deployments_df['training_timestamp']
        )

        deployments_df.drop(columns=['model_year', 'training_timestamp', 'training_datetime'], inplace=True)
        return deployments_df
    
    # Merges prediction output and deployment details
    @staticmethod
    def merge_final_dfs(pred_df, copy_dep_details, daily_data_df):
        copy_dep_details.rename(columns={'space_id': 'deployment_space'}, inplace=True)
        isin_merged_df = pred_df[pred_df['deployment_id'].isin(copy_dep_details['deployment_id'])]
        isin_merged_df = isin_merged_df.reset_index(drop=True)
        print(f"isin_merged_df is: {isin_merged_df}")
        daily_predictions = pd.merge(daily_data_df, isin_merged_df[['isin', 'predictions']], how = 'left', on = 'isin')
        print(f"daily predictions after merge 1: {daily_predictions}")
        daily_predictions = pd.merge(daily_predictions, copy_dep_details[['isin', 'deployment_space', 'deployment_id', 'model_identifier']], how = 'left', on = 'isin')
        print(f"daily predictions after merge 2: {daily_predictions}")

        # Reorder columns
        cols = daily_predictions.columns.tolist()

        if 'isin' in cols:
            idx = cols.index('isin') + 1
            reordered_cols = (
                cols[:idx] +
                ['deployment_space', 'deployment_id', 'model_identifier'] +
                [col for col in cols if col not in ['deployment_space', 'deployment_id', 'model_identifier', 'isin', 'date']]
            )
            daily_predictions = daily_predictions[reordered_cols]

        print(f"daily predictions before returning is: {daily_predictions}")

        return daily_predictions
    
    # Fills sector avg prediction for isins with no isin wise models
    @staticmethod
    def add_sector_avg(daily_predictions_df, sector_fill_df, tag_df):
        daily_predictions_df = daily_predictions_df.merge(tag_df[['isin', 'ind_code']], on='isin', how='left')
        daily_predictions_df['sector_code'] = daily_predictions_df['ind_code'].astype(str).str[:2]

        sector_fill_df = sector_fill_df.merge(tag_df[['isin', 'ind_code']], on='isin', how='left')
        sector_fill_df['sector_code'] = sector_fill_df['ind_code'].astype(str).str[:2]

        missing_cols = set(daily_predictions_df.columns) - set(sector_fill_df.columns)
        for col in missing_cols:
            if col == 'model_identifier':
                sector_fill_df[col] = config['parameters']['sector_identifier']
            else:
                sector_fill_df[col] = np.nan
    
        sector_fill_df = sector_fill_df[daily_predictions_df.columns]
        print(f"sector fill df after adding daily predictions df columns: {sector_fill_df}")
        prediction_column = config['parameters']['pred_column_name']

        daily_predictions_df = daily_predictions_df.reset_index(drop=True)
        daily_predictions_df[prediction_column] = daily_predictions_df[prediction_column].apply(lambda x: x[0] if isinstance(x, list) and len(x) == 1 else np.nan)
        # temp_preds = daily_predictions_df[prediction_column].apply(
        #     lambda x: x[0] if isinstance(x, list) and len(x) == 1 else np.nan
        # )        
        # sector_preds = daily_predictions_df.groupby('sector_code')\
        #     .apply(lambda group: temp_preds.loc[group.index].mean())\
        #     .reset_index(name=prediction_column)
        sector_preds = daily_predictions_df.groupby(daily_predictions_df['sector_code'])[prediction_column].mean().reset_index()
        sector_pred_map = dict(zip(sector_preds['sector_code'], sector_preds[prediction_column]))

        # Ensure all sector_codes from the final_predictions are included in the map with NaN if missing
        all_sector_codes = daily_predictions_df['sector_code'].unique()
        for sector_code in all_sector_codes:
            if sector_code not in sector_pred_map:
                sector_pred_map[sector_code] = np.nan

        print(f"sector pred map is: {sector_pred_map}")

        sector_fill_df['predictions'] = sector_fill_df['sector_code'].map(sector_pred_map)
        # sector_fill_df['predictions'] = sector_fill_df['sector_code'].map(lambda x: [sector_pred_map.get(x, np.nan)])
        no_sector_pred_count = sector_fill_df['predictions'].isna().sum()
        sector_pred_count = sector_fill_df['predictions'].notna().sum()

        complete_df = pd.concat([daily_predictions_df, sector_fill_df], ignore_index=True)
        print(f"fin df after concat is: {complete_df}")

        return complete_df, no_sector_pred_count, sector_pred_count
    
    # Checks whether prediction value is NaN or empty list
    @staticmethod
    def is_invalid_prediction(val):
        # Covers pure NaN or None
        if val is None or (isinstance(val, float) and np.isnan(val)):
            return True
        # Covers empty list or list with NaN/None inside
        if isinstance(val, list) and (len(val) == 0 or val[0] is None or (isinstance(val[0], float) and np.isnan(val[0]))):
            return True
        return False
    
    # Uploads all metrics data to ES and S3
    def upload_metrics_data(self, df_metrics, tag, saved_date):
        metrics_upload_path = config['s3_paths'][f"{tag}_metrics_upload_path"]
        test_upload_path = config['s3_paths'][f"{tag}_metrics_testing_path"]

        df_metrics['schedular'] = 'Monthly'
        df_metrics['updated_at'] = datetime.now()
        df_metrics['updated_at'] = pd.to_datetime(df_metrics['updated_at']).dt.strftime('%Y-%m-%dT%H:%M:%S.%f').str[:-3]
        df_metrics['date'] = df_metrics['date'].astype(str)
        df_metrics['updated_at'] = df_metrics['updated_at'].astype(str)
        print(df_metrics.columns.tolist())

        if env == 'prod':
            versioning_bucket = config['s3_version_control']['bucket']
            versioning_filename = config['s3_version_control']['metrics_filename_monthly']
            self.s3_client.write_advanced_as_df(df_metrics, bucketName, f'{metrics_upload_path}monthly_prediction_metrics_{saved_date}.csv')
            rows = [(idx, row, versioning_bucket, versioning_filename.replace('date',str(saved_date))) for idx, row in df_metrics.iterrows()]
            with ThreadPoolExecutor() as executor:
                executor.map(lambda args: self.save_row(*args), rows)
            self.upload_to_ES(df_metrics)
        elif env == 'preprod':
            versioning_bucket = config['s3_version_control']['preprod_bucket']
            versioning_filename = config['s3_version_control']['metrics_filename_monthly']
            self.s3_client.write_advanced_as_df(df_metrics, bucketName, f"{test_upload_path}monthly_prediction_metrics_{saved_date}.csv")
            rows = [(idx, row, versioning_bucket, versioning_filename.replace('date',str(saved_date))) for idx, row in df_metrics.iterrows()]
            with ThreadPoolExecutor() as executor:
                executor.map(lambda args: self.save_row(*args), rows)
            self.upload_to_ES(df_metrics)
        elif env == 'local':
            self.s3_client.write_advanced_as_df(df_metrics, bucketName, f"{test_upload_path}monthly_prediction_metrics_{saved_date}.csv")
            print("Final metrics dataframe ready to be uploaded")