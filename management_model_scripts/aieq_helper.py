from imports import *
from common_helper import HelperFuncs

if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()

sys.path.insert(0, os.path.abspath(script_dir))

config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

conn = HelperFuncs()

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")
log_file_path = f"{script_dir}/{config['log_files']['aieq_folder']}/{saved_date}_daily_run.log"

global logs
logs = conn.create_logger(log_file_path)

input_columns = config['aieq_columns']['input_columns']
fin_data_column_list = config['aieq_columns']['fin_column_list']
fin_columns_nodate = config['aieq_columns']['fin_columns_nodate']

s3_upload_path = config['s3_paths']['aieq_upload_path']
bucketName = config['s3_buckets']['management_bucket']

class AieqHelperFuncs:
    #Fetches financial raw input data from S3 for daily run
    def get_daily_data_s3(self, date):
        bucketName = config['s3_buckets']['financial_bucket']
        folderName = config['s3_paths']['aieq_finance_input_folder']

        object_key = folderName+'input_data_'+date+'.csv'
        
        fin_data = conn.s3_client.read_as_dataframe(bucketName, object_key)[fin_data_column_list]

        return fin_data
    
    # get customer life time val
    @staticmethod
    def get_customer_life_time_val(daily_data_df):
        daily_data_df['customer_life_time_val'] = np.nan
        for idx, row in daily_data_df.iterrows():
            curr_year = int(row['date'].split('-')[0])
            es_df = conn.get_es_data(row['isin'], curr_year, config['es_index']['management_index'], config['aieq_columns']['schedular'])
            if es_df.empty:
                continue
            
            es_df = es_df['total_rev']
            es_df = np.floor(pd.to_numeric(es_df).unique())
            
            total_rev_curr = np.floor(row['total_rev'])
            mask = np.not_equal(es_df, total_rev_curr)
            es_df = es_df[mask]
            
            if len(es_df) == 0 or es_df[-1] == 0 or es_df[-1] == np.nan:
                continue
            total_rev_prev = float(es_df[-1])
            customer_life_time_val = (total_rev_curr - total_rev_prev)/total_rev_prev
            customer_life_time_val = (1 + customer_life_time_val)**2
            customer_life_time_val = (total_rev_curr - row['sga'])* customer_life_time_val
            
            daily_data_df.at[idx, 'customer_life_time_val'] = customer_life_time_val
        return daily_data_df

    # get foreign tax growth
    @staticmethod
    def get_foreign_tax_growth(daily_data_df):
        daily_data_df['foreign_tax_growth'] = np.nan
        for idx, row in daily_data_df.iterrows():
            curr_year = int(row['date'].split('-')[0])
            es_df = conn.get_es_data(row['isin'], curr_year, config['es_index']['management_index'], config['aieq_columns']['schedular'])
            if es_df.empty:
                continue
            
            es_df = es_df['curr_foreign_taxes']
            es_df = pd.to_numeric(es_df).unique().round(1)
            curr_ftax = round(row['curr_foreign_taxes'], 1)
            mask = np.not_equal(es_df, curr_ftax)
            es_df = es_df[mask]
            if len(es_df) == 0 or es_df[-1] == 0 or es_df[-1] == np.nan:
                continue
            ftax_prev = float(es_df[-1])
            foreign_tax_growth = ((curr_ftax-ftax_prev)/ftax_prev)*100
            daily_data_df.at[idx, 'foreign_tax_growth'] = foreign_tax_growth
        
        return daily_data_df
    
    #Handles date formatting issues from financial input data
    def fix_date_format(self, df):
        # df['date'] = df['date'].apply(lambda x: conn.try_convert(x).strftime("%Y-%m-%d"))
        # df['date'] = df['date'].apply(lambda x: datetime.strptime(x, "%Y-%m-%d").strftime("%Y-%m-%d"))
        df['date'] = pd.to_datetime(df['date'], format='%m/%d/%Y').dt.strftime('%Y-%m-%d')
        return df
    
    #Fetches financial input data for daily run and filters on masteractive list
    def get_fin_input(self, firms, date):
        try:
            og_daily_data_df = self.get_daily_data_s3(date)
            # daily_data_df = conn.get_daily_data_ES(firms, date, schedular = config['aieq_columns']['schedular'])
            logs.info(f'number of isins in financial data:- {len(og_daily_data_df)}')
        except:
            print('No data available for ', date)
            body = f'Financial data missing for all aieq isins for date {date}'
            subject = 'Error in Management Model AIEQ daily run'
            conn.send_mail(subject, body, errorbool=True)
            logs.error(f'Financial data is not available for {date}')
            raise SystemExit('No Input Financial Data Available')
            
        to_keep = [x for x in og_daily_data_df.index if og_daily_data_df.loc[x, 'isin'] in firms]
        og_daily_data_df = og_daily_data_df.loc[to_keep].reset_index(drop = True)
            
        # og_daily_data_df = self.fix_date_format(og_daily_data_df)
        og_daily_data_df['date'] = og_daily_data_df['date'].apply(lambda x: conn.try_convert(x).strftime("%Y-%m-%d"))
        return og_daily_data_df
    
    #Get deployment mapping file and filter on masteractive list
    @staticmethod
    def get_dep_file(firms):
        dep_details = conn.get_deployment_details(bucketName, config['s3_paths']['aieq_deployment_mapping_file'])
        logs.info(f'Length of deployment mapping file: {len(dep_details)}')
        dep_details = dep_details[dep_details['isin'].isin(firms)]
        logs.info(f'len of dep file after filtering based on aieq isin list: {len(dep_details)}')

        print(f'dep details after filtering is: {dep_details}')
        return dep_details
    
    #Prepares df to be submitted to prediction run helper
    @staticmethod
    def prepare_pred_input(dep_details, daily_data_df):
        dep_details = dep_details[dep_details['isin'].isin(daily_data_df['isin'])]
        pred_input = []
        pred_input_columns = [x for x in input_columns if x!= 'isin']
        for i in range(len(dep_details)):
            isin = dep_details.iloc[i]['isin']
            single_row = daily_data_df[daily_data_df['isin']==isin]
            single_row = single_row[pred_input_columns]
            single_row.reset_index(drop=True, inplace=True)
            pred_input.append(single_row.values)
        dep_details['test_data'] = pred_input
        dep_details.drop(columns="name", inplace=True)
        deployment_toSubmit = dep_details
        deployment_toSubmit=deployment_toSubmit.rename(columns={'space_id':'deployment_space'})
        deployment_toSubmit = deployment_toSubmit.reset_index(drop=True)
        print(f'dep to submit is: {deployment_toSubmit}')

        return deployment_toSubmit
    
    # Combine prediction output and deployment details, replaces null values in predictions with 0
    @staticmethod
    def merge_dep_to_pred(final, copy_dep_details, daily_data_df, firms):
        isin_merged = pd.merge(final, copy_dep_details[['deployment_id']], on = 'deployment_id')
        isin_merged = isin_merged[isin_merged['isin'].isin(firms)]
        isin_merged = isin_merged.reset_index(drop=True)
        daily_predictions = pd.merge(daily_data_df, isin_merged[['isin','predictions']], how = 'left', on = 'isin')
        daily_predictions['predictions'] = daily_predictions['predictions'].apply(lambda x: 0 if pd.isna(x) else (x[0] if isinstance(x, list) and len(x) != 0 else 0))
        return daily_predictions
    
    # Reverts the forward filled input financial data back to original
    @staticmethod
    def revert_null_values(daily_predictions, og_daily_data_df):
        daily_predictions = daily_predictions.set_index('isin')
        daily_predictions.update(og_daily_data_df.set_index('isin')[fin_columns_nodate])
        daily_predictions = daily_predictions.reset_index()

        daily_predictions = conn.convert_close_change(daily_predictions)
        return daily_predictions
    
    # Forward fill the failed predictions
    @staticmethod
    def ffill_zero_predictions(df, date):
        daily_predictions = df
        daily_predictions['ER_Copied'] = False
        try:
            prev_bday = (pd.to_datetime(date)-BDay(1)).strftime("%Y-%m-%d")
            logs.info(f'Previous business day used for forward filling: {prev_bday}')
            df_prev_bday = conn.s3_client.read_as_dataframe(bucketName, s3_upload_path + f'monthly_prediction_{prev_bday}.csv')
        except:
            print('******* previous business day, data not availabe for forward filling *******')
            logs.error('Forward filling with previous Bday is not possible')
        
        for idx, row in daily_predictions.iterrows():
            if row['predictions'] == 0:
                prev_pred = df_prev_bday[df_prev_bday['isin'] == row['isin']].reset_index(drop = True)
                if prev_pred.empty:
                    continue
                
                prev_pred = prev_pred['predictions']
                prev_pred = prev_pred.iloc[0]
                daily_predictions.at[idx, 'predictions'] = prev_pred
                daily_predictions.at[idx, 'ER_Copied'] = True
                
        print('|||||||||||||||||||||||||||||||| Forward filling of predictions complete ||||||||||||||||||||||||||||||||')
        return daily_predictions