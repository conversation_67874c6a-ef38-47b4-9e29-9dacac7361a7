# Modules
from utilities import *

# This sets the layout of the streamlit app to "wide"
st.set_page_config(layout= "wide")

warnings.simplefilter("ignore", UserWarning)

# Global constants
color_palette = px.colors.qualitative.Plotly

years = pd.Series(reversed(range(2008, 2026)))
months = pd.Series((range(1, 13, 1)))
months = pd.to_datetime(months, format='%m').dt.month_name().str.slice(stop= 3)

name1 = 'best model' 
name2 = 'ER model'
name3 = 'Financial model'
name4 = 'Information model'
name5 = 'CatBoost model'
name6 = 'LSTM+Attention model'
name7 = 'TTM model'
# GET today's date
today = datetime.datetime.now().date()
past = today - datetime.timedelta(weeks=1)

# Initialize session state for filters and data
if 'filters' not in st.session_state:
    st.session_state.filters = {
        'start_date': past,
        'end_date': today,
        'schedular': 'Monthly',
        'tags': [],
        'selected_isins': [],
        'weekly_display': []  # Track selected weekly display option
    }
if 'data' not in st.session_state:
    st.session_state.data = None

# MAIN FILTERS UI
start, _, end = st.columns([4, 1, 4])

# Start Date Selection
with start:
    st.markdown("<h6 style='text-align: center;'>Start date</h6>", unsafe_allow_html=True)
    day, month, year = st.columns(3)
    
    dd = st.session_state.filters['start_date'].day
    mm = st.session_state.filters['start_date'].strftime("%b")
    yy = st.session_state.filters['start_date'].year
    
    with year:
        y1 = int(st.selectbox('Select a year', years, key='year1', index=int(years[years == yy].index[0])))
    with month:
        m1 = st.selectbox('Select a month', months, key='month1', index=int(months[months == mm].index[0]))
        m1 = months[months == m1].index[0] + 1
    with day:
        num_days = get_days_in_month(y1, m1)
        d1 = st.selectbox('Select Day', pd.Series(range(1, num_days + 1)), key='day1', index=dd-1)

# End Date Selection
with end:
    st.markdown("<h6 style='text-align: center;'>End date</h6>", unsafe_allow_html=True)
    day, month, year = st.columns(3)
    dd = st.session_state.filters['end_date'].day
    mm = st.session_state.filters['end_date'].strftime("%b")
    yy = st.session_state.filters['end_date'].year
    
    with year:
        y2 = int(st.selectbox('Select a year', years, key='year2', index=int(years[years == yy].index[0])))
    with month:
        m2 = st.selectbox('Select a month', months, key='month2', index=int(months[months == mm].index[0]))
        m2 = months[months == m2].index[0] + 1
    with day:
        num_days = get_days_in_month(y2, m2)
        d2 = st.selectbox('Select Day', pd.Series(range(1, num_days + 1)), key='day2', index=dd-1)

# Store selected dates (not applied yet)
start_date_selected = datetime.datetime(year=y1, month=m1, day=d1).date()
end_date_selected = datetime.datetime(year=y2, month=m2, day=d2).date()

# Error check
if end_date_selected <= start_date_selected:
    st.stop()

# Horizon Selection
_, sched, _ = st.columns([4, 2, 4])
with sched:
    st.text('')
    st.text('')
    sc = ['Daily', 'Monthly']
    st.markdown("<h6 style='text-align: center;'>Investment Horizon</h6>", unsafe_allow_html=True)
    schedular_selected = st.selectbox('Select Horizon', sc, 
                                     index=sc.index(st.session_state.filters['schedular']), 
                                     key='schsel')

# Additional Filters
st.text('')
st.text('')

_, ll, cc, rr = st.columns([1, 6, 6, 6])
z12 = ['aigo', 'aieq', 'aipex', 'indeq', 'indt1', 'mega_cap']
z12.sort()

# Weekly Results Options - Now matches Universe/ISIN style
with ll:
    weekly_checkbox =st.checkbox("Aggregate Results")
    if weekly_checkbox:
        weekly_options = st.multiselect("Select Type",options=["All Results", "Weekly Results"],default=[],key='weekly_options')
    else: 
        weekly_options =[]
    
    # Ensure only one option is selected
    if len(weekly_options) > 1:
        st.warning("Please select only one option")
        weekly_options = [weekly_options[-1]]  # Keep only the last selected option
        st.session_state.weekly_options = weekly_options

# Universe Selection
with cc:
    universe_checkbox = st.checkbox('Universe', key='t8')
    if universe_checkbox:
        tags_selected = st.multiselect("Choose Universe", z12, st.session_state.filters['tags'])
    else:
        tags_selected = []

# ISIN Selection
with rr:
    isin_checkbox = st.checkbox('Select Tickers', key='Selected_ISINS')
    if isin_checkbox:
        if st.session_state.data and 'tic-company' in next(iter(st.session_state.data.values())).columns:
            y = list(next(iter(st.session_state.data.values()))['tic-company'].drop_duplicates().values)
            selected_isins = st.multiselect("Choose Tickers", y, st.session_state.filters['selected_isins'])
        else:
            selected_isins = []
            st.warning("Data not loaded yet or no ISINs available")
    else:
        selected_isins = []

# GO Button
_, go_button_col, _ = st.columns([4, 2, 4])
with go_button_col:
    st.text('')
    go_button = st.button('Go', key='go_button')

# PROCESS DATA ONLY WHEN GO IS CLICKED OR FIRST RUN
if 'data_loaded' not in st.session_state or go_button:
    # Update all filters in session state
    st.session_state.filters = {
        'start_date': start_date_selected,
        'end_date': end_date_selected,
        'schedular': schedular_selected,
        'tags': tags_selected if universe_checkbox else [],
        'selected_isins': selected_isins if isin_checkbox else [],
        'weekly_display': weekly_options[0] if weekly_options else None
    }

    with st.spinner('Loading and processing data...'):
        # GET data from ES
        all_data = get_data(st.session_state.filters['start_date'],
                          st.session_state.filters['end_date'],
                          st.session_state.filters['schedular'])
        
        # Preprocess data
        dfs = preprocess_data(all_data)
        [best, fin, info, cat, la, ttm, er] = dfs
        dfs = {'best': best, 'er': er, 'fin': fin, 'info': info, 'cat': cat, 'la': la, 'ttm': ttm}

        # Get reference data
        i2c = get_master_active_firms()
        tmp12 = i2c[i2c['processedconm'].isna()]['conm']
        for i, conm in i2c[i2c['processedconm'].isna()]['conm'].items():
            i2c.loc[i, 'processedconm'] = conm
        i2c['tic-company'] = i2c['tic'] + "-" + i2c["processedconm"]
        i2c.rename(columns={'processedconm': 'company'}, inplace=True)
        i2t = extract_tags(i2c)

        # Merge with reference data
        for k in dfs.keys():
            if dfs[k].empty:
                continue
            dfs[k] = pd.merge(dfs[k], i2c, on='isin', how='left')
            dfs[k] = pd.merge(dfs[k], i2t, on='isin', how='left')

        # Apply universe filter if enabled
        if st.session_state.filters['tags']:
            tmp = dict()
            for k in dfs.keys():
                if dfs[k].empty:
                    continue
                tmp[k] = pd.Series([False] * len(dfs[k]))
                dfs[k].reset_index(inplace=True, drop=True)
            
            for x in st.session_state.filters['tags']:
                for k in tmp.keys():
                    if dfs[k].empty:
                        continue
                    tmp[k] = tmp[k] | dfs[k][x].astype(bool)

            for k in tmp.keys():
                if dfs[k].empty:
                    continue
                dfs[k] = dfs[k][tmp[k]]

        # Apply ISIN filter if enabled
        if st.session_state.filters['selected_isins']:
            for k in dfs.keys():
                if dfs[k].empty:
                    continue
                dfs[k] = dfs[k][dfs[k]['tic-company'].isin(st.session_state.filters['selected_isins'])]

        # Apply weekly display options if selected
        if st.session_state.filters['weekly_display'] == "All Results":
            # Calculate metrics across entire date range (ignoring weeks)
            for k in dfs.keys():
                if dfs[k].empty:
                    continue
                if 'date' in dfs[k].columns:
                    # Filter data for the selected date range
                    mask = (dfs[k]['date'] >= start_date_selected) & (dfs[k]['date'] <= end_date_selected)
                    dfs[k] = dfs[k][mask]
                    
                    # Calculate mean values across all dates for each ISIN
                    columns_to_keep = ['isin', 'rmse', 'mean_dir']
                    if 'base_rmse' in dfs[k].columns and 'base_mean_dir' in dfs[k].columns:
                        columns_to_keep.extend(['base_rmse', 'base_mean_dir'])
                    
                    # Group by ISIN and calculate mean across all dates
                    dfs[k] = dfs[k][columns_to_keep].groupby('isin').mean().reset_index()
                    
                    # Add date range info as a single "Date" label
                    date_range_str = f"{start_date_selected.strftime('%Y-%m-%d')} to {end_date_selected.strftime('%Y-%m-%d')}"
                    dfs[k]['Date'] = date_range_str
                    
                    # Merge with reference data
                    dfs[k] = pd.merge(dfs[k], i2c, on='isin', how='left')
                    dfs[k].sort_values(by=['isin'], inplace=True)
        
        elif st.session_state.filters['weekly_display'] == "Weekly Results":
            # Original weekly aggregation logic
            def calculate_weekly_mean(df):
                def week_number(date):
                    x = (date - datetime.datetime(date.year, date.month, 1).date()).days // 7 + 1
                    if x > 4:
                        x = 4
                    return f"{months[date.month-1]} Week {x}"
                
                if 'date' not in df.columns:
                    return df
                
                df['week_number'] = df['date'].apply(week_number)
                
                columns_to_keep = ['isin', 'rmse', 'mean_dir', 'week_number']
                if 'base_rmse' in df.columns and 'base_mean_dir' in df.columns:
                    columns_to_keep.extend(['base_rmse', 'base_mean_dir'])
                
                df1 = df[columns_to_keep]
                mean_values = df1.groupby(['isin', 'week_number']).mean()
                return mean_values.reset_index()
            
            for k in dfs.keys():
                if dfs[k].empty:
                    continue
                dfs[k] = calculate_weekly_mean(dfs[k])
                dfs[k].rename(columns={'week_number': 'Date'}, inplace=True)
                dfs[k] = pd.merge(dfs[k], i2c, on='isin', how='left')
                dfs[k].sort_values(by=['Date'], inplace=True)

        # Store all data in session state
        st.session_state.data = dfs
        st.session_state.i2c = i2c
        st.session_state.i2t = i2t
        st.session_state.data_loaded = True
        st.session_state.last_updated = datetime.datetime.now()

# Always use data from session state for display
if st.session_state.data:
    dfs = st.session_state.data
    i2c = st.session_state.i2c
    i2t = st.session_state.i2t
    
    # Compute metrics for display
    try:
        _max_dir_, _max_rmse_ = determine_max(dfs)
    except KeyError as e:
        _max_dir_, _max_rmse_ = None, None

    # Show last updated time
    if 'last_updated' in st.session_state:
        st.caption(f"Last updated: {st.session_state.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")

st.text("")

# # GET today's date
# today = datetime.datetime.now().date()
# past  = today - datetime.timedelta(weeks= 1)

# # MAIN
# # creates 3 columns of the specified widths
# start, _, end = st.columns([4, 1, 4])

# # Accepts two inputs (start & end dates)
# with start:
#     # Creates a list of days, months & years, for the user to select from
#     st.markdown("<h6 style='text-align: center;'>Start date</h6>", unsafe_allow_html=True)
#     day, month, year = st.columns(3)

#     dd = past.day
#     mm = past.strftime("%b")
#     yy = past.year
#     with year:
#         y1 = int( st.selectbox('Select a year', years, key= '1', index= int(years[years == yy].index[0])) )
#     with month:
#         m1 = st.selectbox('Select a month', months, key= '2', index= int(months[months == mm].index[0]))
#         m1 = months[months == m1].index[0] + 1
#     with day:
#         num_days = get_days_in_month(y1, m1)
#         d1 = st.selectbox('Select Day', pd.Series(range(1, num_days + 1)), key= '3', index= dd-1)

# with end:
#     st.markdown("<h6 style='text-align: center;'>End date</h6>", unsafe_allow_html=True)
#     day, month, year = st.columns(3)
#     dd = today.day
#     mm = today.strftime("%b")
#     yy = today.year
#     with year:
#         y2 = int(st.selectbox('Select a year', years, key= '4', index= int(years[years == yy].index[0])))
#     with month:
#         m2 = st.selectbox('Select a month', months, key= '5', index= int(months[months == mm].index[0]))
#         m2 = months[months == m2].index[0] + 1
#     with day:
#         num_days = get_days_in_month(y2, m2)
#         d2 = st.selectbox('Select Day', pd.Series(range(1, num_days + 1)), key= '6', index= dd-1)

# # converts input string to a datetime object
# start_date = datetime.datetime(year= y1, month= m1, day= d1).date() 
# end_date   = datetime.datetime(year= y2, month= m2, day= d2).date()

# # error check
# if end_date <= start_date:
#     st.stop()

# _, sched, _ = st.columns([4, 2, 4])
# with sched:
#     st.text('')
#     st.text('')
#     sc = ['Daily', 'Monthly']
#     st.markdown("<h6 style='text-align: center;'>Schedular</h6>", unsafe_allow_html=True)
#     schedular = st.selectbox('Select a Schedular', sc, index= 1, key= 'schsel')

# # GET data from ES
# all_data = get_data(start_date, end_date, schedular)
# dfs = preprocess_data(all_data)

# # compute the maximun directionality & rmse amongst all datasets
# #_max_dir_, _max_rmse_ = determine_max(dfs)

# # store the pointer to each dataset
# [best, fin, info, cat, la, ttm, er] = dfs
# dfs = {'best': best, 'er' : er, 'fin' : fin, 'info': info, 'cat' : cat, 'la' : la, 'ttm' : ttm}        

# # downloads the file 'all_masteractivefirms.xlsx'
# i2c = get_master_active_firms()

# tmp12 = i2c[i2c['processedconm'].isna()]['conm']
# idx = tmp12.index
# for i, conm in i2c[i2c['processedconm'].isna()]['conm'].items():
#     i2c.loc[i, 'processedconm'] = conm
    
# i2c['tic-company'] = i2c['tic'] + "-" + i2c["processedconm"]
# i2c.rename(columns= {'processedconm' : 'company'}, inplace= True)

# # Creates a dataframe, which can maps each ISIN to tags amongst ['aigo', 'aieq', 'aipex', 'tier1', 'indeq', 'indt1']
# i2t = extract_tags(i2c)

# # Associates each ISIN with: 1. the company's name & 2. its tags
# with st.spinner('Pre-processing the dataset'):

#     for k in dfs.keys():
#         if dfs[k].empty:
#             continue
#         dfs[k] = pd.merge(dfs[k], i2c, on= 'isin', how='left')
#         dfs[k] = pd.merge(dfs[k], i2t, on= 'isin', how = 'left')    
    
# # creates two newlines, i.e., '\n'
# st.text('')
# st.text('')

# _, ll, cc, rr = st.columns([1, 6, 6, 6])
# z12 = ['aigo', 'aieq', 'aipex', 'indeq', 'indt1', 'mega_cap']
# z12.sort()
# with cc:
#     if st.checkbox('Select tags', key= 't8'):
#         # Selects the ISINs which belong to the specified tags from each DataFrame
#         tags = st.multiselect("Choose tags", z12, [])
#         # If the list is empty
#         if not tags:
#             pass
#         else:
#             tmp = dict()
#             for k in dfs.keys():
#                 if dfs[k].empty:
#                         continue
#                 tmp[k] = pd.Series( [False] * len(dfs[k]))
#                 dfs[k].reset_index(inplace= True, drop= True)
            
#             for x in tags:
#                 for k in tmp.keys():
#                     if dfs[k].empty:
#                         continue
#                     tmp[k] = tmp[k] | dfs[k][x].astype(bool)

#             for k in tmp.keys():
#                 if dfs[k].empty:
#                         continue
#                 dfs[k] = dfs[k][tmp[k]]
#     else:
#         pass
        
# with rr:
    
#     if st.checkbox('Select ISINs', key= 'Selected_ISINS'):
#         # error-check
#         _df_= None
#         for k, v in dfs.items():
#             if not v.empty:
#                 _df_= v
#                 break

#         if _df_ is not None:
                
#             y = list(_df_['tic-company'].drop_duplicates().values)
            
#             list_of_isin = st.multiselect("Choose isins", y, [])
#             # If the list is empty
#             if not list_of_isin:
#                 pass
#             else:
#                 for k in dfs.keys():
#                     if dfs[k].empty:
#                         continue
#                     dfs[k] = dfs[k][dfs[k]['tic-company'].isin(list_of_isin)]
#         else:
#             st.error('Feature Unavailable due to absence of data')
            
#     else:
#         pass

# # compute the maximun directionality & rmse amongst all datasets
# _max_dir_, _max_rmse_ = determine_max(dfs)


# with ll:        
#     if st.checkbox('Aggregate Weekly Results', key= 'aggreagated_results'):
            
#         def calculate_weekly_mean(df, flag= False):
#             # Define a function to calculate the week number across months
#             def week_number(date):
#                 x = (date - datetime.datetime(date.year, date.month, 1).date()).days // 7 + 1
#                 if x > 4:
#                     x = 4
#                 y = f"{months[date.month-1]} Week {x}"
#                 return y
                
            
#             # Calculate week number and store it in a new column
#             df['week_number'] = df['date'].apply(week_number)
            
#             if flag:
#                 df1 = df[['isin', 'rmse', 'base_rmse', 'mean_dir', 'base_mean_dir', 'week_number', 'date']]
#             else:
#                 df1 = df[['isin', 'rmse', 'mean_dir', 'week_number', 'date']]
            
#             # Group by 'name' and 'week_number', then calculate the mean
#             # I cannot calculate the mean of the column 'date'
#             df2 = df1.drop(['date'], axis= 1, inplace= False)
#             mean_values = df2.groupby(['isin', 'week_number']).mean()
            
#             # Reset index to make it easier to work with
#             mean_values.reset_index(inplace=True)
            
#             return mean_values
        
#         # Calculate weekly mean values
#         for k in dfs.keys():
#             if dfs[k].empty:
#                 continue
#             _= """if k == 'er':
#                 flag = True
#             else:
#                 flag = False"""
#             dfs[k] = calculate_weekly_mean(dfs[k]) #, flag= flag)
#             dfs[k].rename(columns= {'week_number' : 'Date'}, inplace= True)
#             dfs[k] = pd.merge(dfs[k], i2c, on= 'isin', how='left')
#             dfs[k].sort_values(by= ['Date'], inplace= True)
            
#     else:
#         pass 


# st.text("")        


# creates a title
st.markdown("<h3 style='text-align: center;'>Directionality ISIN count</h3>", unsafe_allow_html=True)

lc, rc = st.columns(2)
st.text("")
lc1, rc1 = st.columns(2)
st.text("")
lc10, rc10 = st.columns(2)
st.text("")
lc14, rc14 = st.columns(2)


figures = dict()
# Creates plots by using PlotLy
# Projects each PlotLy chart onto the webapp
with lc:
    st.markdown(f"<h5 style='text-align: center;'>{name1}</h5>", unsafe_allow_html=True)
    figures['best_1'] = plot_directionality(dfs['best']  , _max_dir_)
    if figures['best_1']:
        st.plotly_chart(figures['best_1'])
    else:
        st.error('')
        
with rc:
    st.markdown(f"<h5 style='text-align: center;'>{name2}</h5>", unsafe_allow_html=True)
    figures['er_1'] = plot_directionality(dfs['er']  , _max_dir_)
    if figures['er_1']:
        st.plotly_chart(figures['er_1'])
    else:
        st.error('')

with lc1:
    st.markdown(f"<h5 style='text-align: center;'>{name3}</h5>", unsafe_allow_html=True)
    figures['fin_1'] = plot_directionality(dfs['fin'] , _max_dir_)
    
    if figures['fin_1']:
        st.plotly_chart(figures['fin_1'])
    else:
        st.error('')
        
with rc1:
    st.markdown(f"<h5 style='text-align: center;'>{name4}</h5>", unsafe_allow_html=True)
    figures['info_1'] = plot_directionality(dfs['info'], _max_dir_)
    
    if figures['info_1']:
        st.plotly_chart(figures['info_1'])
    else:
        st.error('')
        
with lc10:
    st.markdown(f"<h5 style='text-align: center;'>{name5}</h5>", unsafe_allow_html=True)
    figures['cat_1'] = plot_directionality(dfs['cat'], _max_dir_)
    if figures['cat_1']:
        st.plotly_chart(figures['cat_1'])
    else:
        st.error('')
        
with rc10:
    st.markdown(f"<h5 style='text-align: center;'>{name6}</h5>", unsafe_allow_html=True)
    figures['la_1'] = plot_directionality(dfs['la'], _max_dir_)
    if figures['la_1']:
        st.plotly_chart(figures['la_1'])
    else:
        st.error('')

with lc14:
    st.markdown(f"<h5 style='text-align: center;'>{name7}</h5>", unsafe_allow_html=True)
    figures['ttm_1'] = plot_directionality(dfs['ttm'], _max_dir_)
    if figures['ttm_1']:
        st.plotly_chart(figures['ttm_1'])
    else:
        st.error('')
    
# creates a horizontal line
st.divider()

#
st.markdown("<h3 style='text-align: center;'>RMSE distribution</h3>", unsafe_allow_html=True)

lc2, rc2 = st.columns(2)
st.text("")
lc3, rc3 = st.columns(2)
st.text("")
lc11, rc11 = st.columns(2)
st.text("")
lc15, rc15 = st.columns(2)




with lc2:
    st.markdown(f"<h5 style='text-align: center;'>{name1}</h5>", unsafe_allow_html=True)
    figures['best_2'] = plot_histogram(dfs['best'], _max_rmse_)
    if figures['best_2']:
        st.plotly_chart(figures['best_2'])
    else:
        st.error('')
        

with rc2:
    st.markdown(f"<h5 style='text-align: center;'>{name2}</h5>", unsafe_allow_html=True)
    figures['er_2'] = plot_histogram(dfs['er'], _max_rmse_)
    if figures['er_2']:
        st.plotly_chart(figures['er_2'])
    else:
        st.error('')


with lc3:
    st.markdown(f"<h5 style='text-align: center;'>{name3}</h5>", unsafe_allow_html=True)
    figures['fin_2'] = plot_histogram(dfs['fin'], _max_rmse_)
    if figures['fin_2']:
        st.plotly_chart(figures['fin_2'])
    else:
        st.error('')

with rc3:
    st.markdown(f"<h5 style='text-align: center;'>{name4}</h5>", unsafe_allow_html=True)
    figures['info_2'] = plot_histogram(dfs['info'], _max_rmse_)
    if figures['info_2']:
        st.plotly_chart(figures['info_2'])
    else:
        st.error('')

with lc11:
    st.markdown(f"<h5 style='text-align: center;'>{name5}</h5>", unsafe_allow_html=True)
    figures['cat_2'] = plot_histogram(dfs['cat'], _max_rmse_)
    if figures['cat_2']:
        st.plotly_chart(figures['cat_2'])
    else:
        st.error('')

with rc11:
    st.markdown(f"<h5 style='text-align: center;'>{name6}</h5>", unsafe_allow_html=True)
    figures['la_2'] = plot_histogram(dfs['la'], _max_rmse_)
    if figures['la_2']:
        st.plotly_chart(figures['la_2'])
    else:
        st.error('')

with lc15:
    st.markdown(f"<h5 style='text-align: center;'>{name7}</h5>", unsafe_allow_html=True)
    figures['ttm_2'] = plot_histogram(dfs['ttm'], _max_rmse_)
    if figures['ttm_2']:
        st.plotly_chart(figures['ttm_2'])
    else:
        st.error('')

st.divider()

#
st.markdown("<h3 style='text-align: center;'>Best RMSE</h3>", unsafe_allow_html=True)

bar, _, _ = st.columns([1, 2, 2])
with bar:
    x = bar.number_input('Select the no. of ISINs to display', min_value= 0, max_value= 200, value= 5, key='bar3')

lc18, rc18 = st.columns(2)
st.text("")
st.text("")
lc19, rc19 = st.columns(2)
st.text("")
st.text("")
lc20, rc20 = st.columns(2)
st.text("")
st.text("")
lc21, rc21 = st.columns(2)

# Selcts 'n' rows in the dataframe, with the largest values under the column 'rmse'
# Create a new DataFrame with the unique values and their corresponding counts
a = isins_with_the_best_rmse(dfs['best'], x, i2c)
b = isins_with_the_best_rmse(dfs['er'], x, i2c)

#f = isins_with_the_best_rmse(dfs['ttm'], x, i2c)

with lc18:
    st.markdown(f"<h5 style='text-align: center;'>{name1}</h5>", unsafe_allow_html=True)
    st.table(a)
    st.download_button(label="Download as CSV", data= a.to_csv(index=True), file_name='best_rmse2.csv', mime='text/csv')
    
with rc18:
    st.markdown(f"<h5 style='text-align: center;'>{name2}</h5>", unsafe_allow_html=True)
    st.table(b)
    st.download_button(label="Download as CSV", data= b.to_csv(index=True), file_name='er_rmse2.csv', mime='text/csv')


with lc19:
    c = isins_with_the_best_rmse(dfs['fin'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name3}</h5>", unsafe_allow_html=True)
    st.table(c)
    st.download_button(label="Download as CSV", data= c.to_csv(index=True), file_name='fin_rmse2.csv', mime='text/csv')
    
with rc19:
    d = isins_with_the_best_rmse(dfs['info'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name4}</h5>", unsafe_allow_html=True)
    st.table(d)
    st.download_button(label="Download as CSV", data= d.to_csv(index=True), file_name='info_rmse2.csv', mime='text/csv')

with lc20:
    st.markdown(f"<h5 style='text-align: center;'>{name5}</h5>", unsafe_allow_html=True)


    e = isins_with_the_best_rmse(dfs['cat'], x, i2c)
    st.table(e)
    st.download_button(label="Download as CSV", data= e.to_csv(index=True), file_name='cat_rmse2.csv', mime='text/csv')
    
with rc20:
    st.markdown(f"<h5 style='text-align: center;'>{name6}</h5>", unsafe_allow_html=True)

    f = isins_with_the_best_rmse(dfs['la'], x, i2c)
    st.table(f)
    st.download_button(label="Download as CSV", data= f.to_csv(index=True), file_name='la_rmse2.csv', mime='text/csv')

with lc21:
    st.markdown(f"<h5 style='text-align: center;'>{name7}</h5>", unsafe_allow_html=True)

    g = isins_with_the_best_rmse(dfs['ttm'], x, i2c)
    st.table(g)
    st.download_button(label="Download as CSV", data= g.to_csv(index=True), file_name='ttm_rmse2.csv', mime='text/csv')
        
st.divider()

#

st.markdown("<h3 style='text-align: center;'>Best Directionality</h3>", unsafe_allow_html=True)

bar4, _, _ = st.columns([1, 2, 2])
with bar4:
    x = bar4.number_input('Select the no. of ISINs to display', min_value= 0, max_value= 50, value= 5, key= 'dir2')

lc22, rc22 = st.columns(2)
st.text("")
st.text("")
lc23, rc23 = st.columns(2)
st.text("")
st.text("")
lc24, rc24 = st.columns(2)
st.text("")
st.text("")
lc25, rc25 = st.columns(2)

# Selcts 'n' rows in the dataframe, with the smallest values under the column 'mean_directionality'
a = isins_with_the_best_directionality(dfs['best'], x, i2c)
b = isins_with_the_best_directionality(dfs['er'], x, i2c)


with lc22:
    st.markdown(f"<h5 style='text-align: center;'>{name1}</h5>", unsafe_allow_html=True)
    st.table(a) #st.table(a.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= a.to_csv(index=True), file_name='best_dir2.csv', mime='text/csv')
    
with rc22:
    st.markdown(f"<h5 style='text-align: center;'>{name2}</h5>", unsafe_allow_html=True)
    st.table(b) #st.table(b.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= b.to_csv(index=True), file_name='er_dir2.csv', mime='text/csv')

with lc23:
    c = isins_with_the_best_directionality(dfs['fin'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name3}</h5>", unsafe_allow_html=True)
    st.table(c) #st.table(a.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= c.to_csv(index=True), file_name='fin_dir2.csv', mime='text/csv')
    
with rc23:
    d = isins_with_the_best_directionality(dfs['info'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name4}</h5>", unsafe_allow_html=True)
    st.table(d) #st.table(b.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= d.to_csv(index=True), file_name='info_dir2.csv', mime='text/csv')

with lc24:
    st.markdown(f"<h5 style='text-align: center;'>{name5}</h5>", unsafe_allow_html=True)

    e = isins_with_the_best_directionality(dfs['cat'], x, i2c)
    st.table(e) 
    st.download_button(label="Download as CSV", data= e.to_csv(index=True), file_name='cat_dir2.csv', mime='text/csv')
    
with rc24:
    st.markdown(f"<h5 style='text-align: center;'>{name6}</h5>", unsafe_allow_html=True)

    f = isins_with_the_best_directionality(dfs['la'], x, i2c)
    st.table(f) 
    st.download_button(label="Download as CSV", data= f.to_csv(index=True), file_name='la_dir2.csv', mime='text/csv')

with lc25:
    st.markdown(f"<h5 style='text-align: center;'>{name7}</h5>", unsafe_allow_html=True)

    g = isins_with_the_best_directionality(dfs['ttm'], x, i2c)
    st.table(g)
    st.download_button(label="Download as CSV", data= g.to_csv(index=True), file_name='ttm_dir2.csv', mime='text/csv')

st.divider()

#
st.markdown("<h3 style='text-align: center;'>Worst RMSE</h3>", unsafe_allow_html=True)

bar, _, _ = st.columns([1, 2, 2])
with bar:
    x = bar.number_input('Select the no. of ISINs to display', min_value= 0, max_value= 50, value= 5, key='bar1')

lc4, rc4 = st.columns(2)
st.text("")
st.text("")
lc5, rc5 = st.columns(2)
st.text("")
st.text("")
lc12, rc12 = st.columns(2)
st.text("")
st.text("")
lc16, rc16 = st.columns(2)

# Selcts 'n' rows in the dataframe, with the largest values under the column 'rmse'
# Create a new DataFrame with the unique values and their corresponding counts
a = isins_with_the_worst_rmse(dfs['best'], x, i2c)
b = isins_with_the_worst_rmse(dfs['er'], x, i2c)

#f = isins_with_the_worst_rmse(dfs['ttm'], x, i2c)

with lc4:
    st.markdown(f"<h5 style='text-align: center;'>{name1}</h5>", unsafe_allow_html=True)
    st.table(a)
    st.download_button(label="Download as CSV", data= a.to_csv(index=True), file_name='best_rmse.csv', mime='text/csv')
    
with rc4:
    st.markdown(f"<h5 style='text-align: center;'>{name2}</h5>", unsafe_allow_html=True)
    st.table(b)
    st.download_button(label="Download as CSV", data= b.to_csv(index=True), file_name='er_rmse.csv', mime='text/csv')


with lc5:
    c = isins_with_the_worst_rmse(dfs['fin'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name3}</h5>", unsafe_allow_html=True)
    st.table(c)
    st.download_button(label="Download as CSV", data= c.to_csv(index=True), file_name='fin_rmse.csv', mime='text/csv')
    
with rc5:
    d = isins_with_the_worst_rmse(dfs['info'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name4}</h5>", unsafe_allow_html=True)
    st.table(d)
    st.download_button(label="Download as CSV", data= d.to_csv(index=True), file_name='info_rmse.csv', mime='text/csv')

with lc12:
    st.markdown(f"<h5 style='text-align: center;'>{name5}</h5>", unsafe_allow_html=True)


    e = isins_with_the_worst_rmse(dfs['cat'], x, i2c)
    st.table(e)
    st.download_button(label="Download as CSV", data= e.to_csv(index=True), file_name='cat_rmse.csv', mime='text/csv')
    
with rc12:
    st.markdown(f"<h5 style='text-align: center;'>{name6}</h5>", unsafe_allow_html=True)

    f = isins_with_the_worst_rmse(dfs['la'], x, i2c)
    st.table(f)
    st.download_button(label="Download as CSV", data= f.to_csv(index=True), file_name='la_rmse.csv', mime='text/csv')

with lc16:
    st.markdown(f"<h5 style='text-align: center;'>{name7}</h5>", unsafe_allow_html=True)

    g = isins_with_the_worst_rmse(dfs['ttm'], x, i2c)
    st.table(g)
    st.download_button(label="Download as CSV", data= g.to_csv(index=True), file_name='ttm_rmse.csv', mime='text/csv')
        
st.divider()

#

st.markdown("<h3 style='text-align: center;'>Worst Directionality</h3>", unsafe_allow_html=True)

bar2, _, _ = st.columns([1, 2, 2])
with bar2:
    x = bar2.number_input('Select the no. of ISINs to display', min_value= 0, max_value= 50, value= 5, key= 'dir')

lc6, rc6 = st.columns(2)
st.text("")
st.text("")
lc7, rc7 = st.columns(2)
st.text("")
st.text("")
lc13, rc13 = st.columns(2)
st.text("")
st.text("")
lc17, rc17 = st.columns(2)

# Selcts 'n' rows in the dataframe, with the smallest values under the column 'mean_directionality'
a = isins_with_the_worst_directionality(dfs['best'], x, i2c)
b = isins_with_the_worst_directionality(dfs['er'], x, i2c)


with lc6:
    st.markdown(f"<h5 style='text-align: center;'>{name1}</h5>", unsafe_allow_html=True)
    st.table(a) #st.table(a.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= a.to_csv(index=True), file_name='best_dir.csv', mime='text/csv')
    
with rc6:
    st.markdown(f"<h5 style='text-align: center;'>{name2}</h5>", unsafe_allow_html=True)
    st.table(b) #st.table(b.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= b.to_csv(index=True), file_name='er_dir.csv', mime='text/csv')

with lc7:
    c = isins_with_the_worst_directionality(dfs['fin'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name3}</h5>", unsafe_allow_html=True)
    st.table(c) #st.table(a.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= c.to_csv(index=True), file_name='fin_dir.csv', mime='text/csv')
    
with rc7:
    d = isins_with_the_worst_directionality(dfs['info'], x, i2c)
    st.markdown(f"<h5 style='text-align: center;'>{name4}</h5>", unsafe_allow_html=True)
    st.table(d) #st.table(b.style.map(highlight_common)) #.style.map(highlight_common))
    st.download_button(label="Download as CSV", data= d.to_csv(index=True), file_name='info_dir.csv', mime='text/csv')

with lc13:
    st.markdown(f"<h5 style='text-align: center;'>{name5}</h5>", unsafe_allow_html=True)

    e = isins_with_the_worst_directionality(dfs['cat'], x, i2c)
    st.table(e) 
    st.download_button(label="Download as CSV", data= e.to_csv(index=True), file_name='cat_dir.csv', mime='text/csv')
    
with rc13:
    st.markdown(f"<h5 style='text-align: center;'>{name6}</h5>", unsafe_allow_html=True)

    f = isins_with_the_worst_directionality(dfs['la'], x, i2c)
    st.table(f) 
    st.download_button(label="Download as CSV", data= f.to_csv(index=True), file_name='la_dir.csv', mime='text/csv')

with lc17:
    st.markdown(f"<h5 style='text-align: center;'>{name7}</h5>", unsafe_allow_html=True)

    g = isins_with_the_worst_directionality(dfs['ttm'], x, i2c)
    st.table(g)
    st.download_button(label="Download as CSV", data= g.to_csv(index=True), file_name='ttm_dir.csv', mime='text/csv')

    
_ = """
# This conditional-statement, checks the values of the 'session-states', to display (or hide) the charts, which are specficied under this if-condition
if st.session_state.Selected_ISINS and (len(list_of_isin) > 0) and not st.session_state.aggreagated_results:
    st.divider()
    st.markdown("<h3 style='text-align: center;'>Recent predictions</h3>", unsafe_allow_html=True)

    bar3, _, _ = st.columns([1, 2, 2])
    with bar3:
        x = bar3.number_input('Select the no. of predictions to display', min_value= 0, max_value= 50, value= 5, key= 'pred')

    lc8, rc8 = st.columns(2)
    st.text("")
    lc9, rc9 = st.columns(2)
    
    n= len(dfs['er'])
    dfs['er']['date'] = dfs['er']['date']
    
    with lc8:
        st.markdown(f"<h5 style='text-align: center;'>{name1}</h5>", unsafe_allow_html=True)
        a= dfs['er'].iloc[-x:][['isin', 'tic', 'company', 'date', 'predicted_er']].iloc[::-1].reset_index(drop= True)
        a.index += 1
        st.table(a)
        st.download_button(label="Download as CSV", data= a.to_csv(index=True), file_name='best_pred_er.csv', mime='text/csv')
        
    with rc8:
        st.markdown(f"<h5 style='text-align: center;'>{name2}</h5>", unsafe_allow_html=True)
        b= dfs['er'].iloc[-x:][['isin', 'tic', 'company', 'date', 'base_predicted_er']].iloc[::-1].reset_index(drop= True)
        b.rename({'base_predicted_er':'predicted_er'}, axis= 1, inplace= True)
        b.index += 1
        st.table(b)
        st.download_button(label="Download as CSV", data= b.to_csv(index=True), file_name='base_pred_er.csv', mime='text/csv')

    with lc9:
        st.markdown(f"<h5 style='text-align: center;'>{name3}</h5>", unsafe_allow_html=True)
        n= len(dfs['fin'])
        dfs['fin']['date'] = dfs['fin']['date']
        if 'predicted_er' in dfs['fin'].columns:
            c= dfs['fin'].iloc[-x:][['isin', 'tic', 'company', 'date', 'predicted_er']].iloc[::-1].reset_index(drop= True)
            c.index += 1
            st.table(c)
            st.download_button(label="Download as CSV", data= c.to_csv(index=True), file_name='fin_pred_er.csv', mime='text/csv')

    with rc9:
        st.markdown(f"<h5 style='text-align: center;'>{name4}</h5>", unsafe_allow_html=True)
        n= len(dfs['fin'])
        dfs['info']['date'] = dfs['info']['date']
        d= dfs['info'].iloc[-x:][['isin', 'tic', 'company', 'date', 'predicted_er']].iloc[::-1].reset_index(drop= True)
        d.index += 1
        st.table(d)
        st.download_button(label="Download as CSV", data= d.to_csv(index=True), file_name='info_pred_er.csv', mime='text/csv')
        """
