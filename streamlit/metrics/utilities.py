# MODULES
import streamlit as st

import numpy as np
import pandas as pd
import json
import io

import datetime
import calendar
import warnings

import plotly.express as px
import plotly.graph_objects as go

import requests
import boto3
from aws_requests_auth.aws_auth import AWSRequestsAuth
from opensearchpy.client import OpenSearch
from opensearchpy.connection.http_requests import RequestsHttpConnection

import config

# FUNCTION DEFINITONS
#test++++
# Returns the no. of days in a month (for a specific year)
def get_days_in_month(year, month):
    return calendar.monthrange(year, month)[1]


# creates a 'client' to access the data on OpenSearch
# Reference: https://opensearch-project.github.io/opensearch-py/api-ref/clients/opensearch_client.html
def create_client(hosts, es_auth_prod):

    client = OpenSearch(
                        hosts= hosts,
                        port= 443,
                        http_auth=es_auth_prod,
                        connection_class=RequestsHttpConnection
                     )

    return client


#This decorator is used to cache [recently] downloaded-data. Consequently, a local copy of the dataset is available, i.e., it does not have to be re-downloaded
_ = """
downloads the data from OpenSearch, stored as 'index_name', from 'start_date' till 'end_date'
Returns [a section of] the dataset [from start_date till end_date] in the JSON format
"""
def download_dataset(_client, index_name, query):
    
    # this list will store the data
    all_data = []

    # Initialize the scroll
    initial_query = query

    try:
        initial_response = _client.search(
            index=index_name,
            body=json.dumps(initial_query),
            scroll='2m',  # Scroll context is valid for 2 minutes
            size= 1000
        )
    except:
        return []

    # Get the scroll ID and initial batch of hits
    scroll_id = initial_response['_scroll_id']
    hits = initial_response['hits']['hits']

    while hits:
        # Collects the current batch of hits
        all_data.extend(hits)

        # Fetch the next batch of results
        scroll_response = _client.scroll(
            scroll_id=scroll_id,
            scroll='2m'
        )

        # Update the scroll ID and hits
        scroll_id = scroll_response['_scroll_id']
        hits = scroll_response['hits']['hits']

    return all_data

"""@st.cache_data
# Returns excel files, which were stored locally
def read_map(path):
    return pd.read_excel( path, usecols= ['isin', 'tic', 'processedconm'], header= 0)"""


def mean_(x):
    return sum(x)/len(x)


"""
GETs data from ElasticSearch (AWS), from multiple indices (which are specified within this function as a list called 'indices')
"""
@st.cache_data
def get_data(start_date, end_date, schedular):

    # 1. Establish connection with AWS
    """
    'AWSRequestsAuth' is a class from the 'aws_requests_auth' library,
    which is used for *authenticating* HTTP requests
    to AWS services using AWS credentials.
    This class allows me to securely *sign* requests in accordance with AWS's Signature Version 4 signing process
    
    reference: https://github.com/davidmuller/aws-requests-auth
    """

    cred= config.cred_1
    es_auth_cred = AWSRequestsAuth(
                            aws_access_key= cred['aws_access_key'],
                            aws_secret_access_key= cred['aws_secret_access_key'],
                            aws_host= cred['aws_host'],
                            aws_region= cred['aws_region'],
                            aws_service= cred['aws_service'])
        
    # create a 'client' to access the data on OpenSearch    
    hosts = [cred['host']]
    client = create_client(hosts, es_auth_cred)

    # These are the indices, from where I will fetch the data
    indices = [ 'eq_best_model_metrics_' ,
                'eq_financial_model_metrics_' ,
                'eq_information_model_metrics_' ,
                'eq_cat_er_model_metrics_' ,
                'eq_attention_model_metrics_' ,
                'eq_ttm_model_metrics_',
                'eq_er_model_metrics_'
              ]

    # Only these columns shall be download
    best_col   = ['date','isin', 'schedular', 'rmse', 'mean_dir'] #['date','isin', 'schedular', 'rmse', 'base_rmse', 'mean_dir', 'base_mean_dir'] #, 'mean_conf', 'base_mean_conf', 'predicted_er', 'base_predicted_er']
    fin_col  = ['date', 'isin', 'schedular', 'root_mean_squared_error', 'mean_directionality'] #, 'avg_confidence_score','monthly_predictions']
    info_col = ['date', 'isin', 'schedular', 'root_mean_squared_error', 'mean_directionality'] #, 'avg_confidence_score', 'monthly_predictions']
    cat_col = ['date', 'isin', 'schedular', 'root_mean_squared_error', 'mean_directionality'] #, 'avg_confidence_score', 'monthly_predictions']
    la_col = ['date', 'isin', 'schedular', 'root_mean_squared_error', 'mean_directionality'] #, 'avg_confidence_score', 'monthly_predictions', 'schedular']
    ttm_col = ['date', 'isin', 'schedular',  'root_mean_squared_error', 'mean_directionality'] #, 'avg_confidence_score', 'monthly_predictions', 'schedular']
    er_col= ['date', 'isin', 'schedular', 'root_mean_squared_error', 'mean_directionality']
    columns = [best_col, fin_col, info_col, cat_col, la_col, ttm_col, er_col]

    if len(indices) != len(columns):
        st.error('Mismatch in the function get_data(start_date, end_date): The columns (to be downloaded, per model) have not been specified (for all models).')
        st.stop()

    start = start_date.isoformat()
    end = end_date.isoformat()
    
    all_data = []
    for i in range(len(indices)):
        if start_date.year == end_date.year:
            query = {'query' : {'bool' : {'must' : [{'term' : { 'schedular.keyword' : {'value' : schedular}}}, {'range' : {'date' : {'gte' : start, 'lt' : end}}} ]}}, '_source' : columns[i]} #, 'size' : 100}
            data = download_dataset(client, indices[i] + str(start_date.year), query)
            
        else:
            last = datetime.datetime(year= start_date.year, month= 12, day= 31).date().isoformat()
            first = datetime.datetime(year= end_date.year, month= 1, day= 1).date().isoformat()
            
            query1 = {'query' : {'bool' : {'must' : [{'term' : { 'schedular.keyword' : {'value' : schedular}}}, {'range' : {'date' : {'gte' : start, 'lt' : last}}} ]}}, '_source' : columns[i]} #, 'size' : 100}
            data = download_dataset(client, indices[i] + str(start_date.year), query1)

            query2 = {'query' : {'bool' : {'must' : [{'term' : { 'schedular.keyword' : {'value' : schedular}}}, {'range' : {'date' : {'gte' : first, 'lt' : end}}} ]}}, '_source' : columns[i]} #, 'size' : 100}
            data2 = download_dataset(client, indices[i] + str(end_date.year), query2)

            data.extend(data2)
        
        all_data.append(data)
        

    # fetch the data
    

    return all_data

"""
Returns: a list of pandas-dataframes
Function: After retreiving data from OpenSearch, each JSON file is converted into a dataframe
"""
@st.cache_data
def preprocess_data(all_data):

    # This list shall store the dataframes
    dfs = []

    count = 0
    # Extract the data from the JSON file, into a DataFrame
    for data in all_data:
        
        # Extract the data from the JSON file
        lst = []
        for d in data:
            lst.append(d['_source'])

        # Load the data into a DataFrame
        df = pd.DataFrame(lst)

        if df.empty:
            count += 1
        else:
            # preprocessing
            df = df[df['date'].notna()]
            
            #df = df[ df['schedular'] == schedular]
            #df.drop(['schedular'], axis=1, inplace= True)
            
            df['date'] = pd.to_datetime(df['date'], format= 'mixed').dt.date
            df['Date'] = df['date'].apply(func= lambda x: x.strftime('%b %d, %Y'))
            
            df.sort_values(by= ['date', 'isin'], inplace= True)

        dfs.append(df)

    if len(dfs) == count:
        st.stop()

    # rename columsn for consistency
    mapper = {'root_mean_squared_error':'rmse', 'mean_directionality':'mean_dir', 'avg_confidence_score':'mean_conf', 'monthly_predictions':'predicted_er'}
    for i in range(1, len(dfs)):
        dfs[i].rename(mapper, axis= 1, inplace= True)        
    
    return dfs

@st.cache_data
# GETs the file 'all_masteractivefirms.xlsx' from S3
def get_master_active_firms():

    """
    cred= config.cred_2
    
    bucket_name = cred['bucket_name']
    object_key = cred['object_key']
    s3 = boto3.client('s3', aws_access_key_id= cred['aws_access_key_id'], aws_secret_access_key= cred['aws_secret_access_key'])
    obj = s3.get_object(Bucket= bucket_name, Key= object_key)
    body = obj['Body'].read()
    data = pd.read_excel(io.BytesIO(body))
    """
    all_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all').json()
    data = pd.DataFrame(all_json["data"]["masteractivefirms_all"])
    return data

@st.cache_data
# GETs the data stored at the index 'eq_platform_master_2024' from ElasticSearch
def get_eq_platform_master_2024(start_date, end_date):

    cred= config.cred_3
    
    # 1. establish connection
    es_auth_prod = AWSRequestsAuth(
                    aws_access_key= cred['aws_access_key'],
                    aws_secret_access_key= cred['aws_secret_access_key'],
                    aws_host= cred['aws_host'],
                    aws_region= cred['aws_region'],
                    aws_service= cred['aws_service'])

    hosts = [cred['host']]
    
    _client = create_client(hosts, es_auth_prod)
    
    start_date = start_date.isoformat()
    end_date = end_date.isoformat()

    # 2. download data
    index_name = 'eq_platform_master_' + str(start_date.year)
    query= {"query": {"bool":{"must":[{"term":{"schedular.keyword":{"value":"Monthly"}}}, {"regexp":{"tags.keyword":{"value":".*aieq.*"}}}, {"range": {"date": {"gte": start_date, "lt": end_date}}}]}}, "_source":['isin','scaled_er_1', 'date', 'accuracy']} #, "size": 100}
    data = download_dataset(_client, index_name, query, start_date, end_date)

    # 3. extract the data
    lst = []
    for x in data:
        lst.append( x['_source'] )
    df = pd.DataFrame(lst)

    # preprocess
    df['date'] = pd.to_datetime(df['date']).dt.date
    rmv = []
    for col in df.columns:
        x = df[col].nunique()
        if x == 1:
            rmv.append(col)
    if rmv:
        df.drop(rmv, axis= 1, inplace= True)
    
    return df
    
"""
Argument: 'i2c' == a dataframe, created via a LEFT-JOIN on, the data (from an index) & the master_active_firms
Function: Identifies the ISINs which belong to the set of pre-specified tags (which are stored in the variable 'toi')
Returns: A pandas-dataframe, which is a mapping from an ISIN to the pre-specified tags
"""
@st.cache_data
def extract_tags(i2c):
    columns = ['isin', 'tags']
    
    i2c = i2c[i2c['tags'].notna()][columns]
    i2c.reset_index(inplace= True, drop= True)
    
    i2t = {}
    for i, row in i2c.iterrows():
        i2t[row.iloc[0]] = row.iloc[1].split(',')
    
    toi = ['aigo', 'aieq', 'aipex', 'tier1', 'indeq', 'indt1', 'mega_cap']
    toi.sort()
    _toi_ = set(toi)

    # Creates a boolean-numpy-array (1 or 0) to associate each ISIN with its tags
    ar = np.zeros( (len(i2c), len(toi)) )
    
    i = 0
    for isin, tags in i2t.items():
        for tag in tags:
            if tag in _toi_:
                if tag == 'aieq':
                    ar[i, 0] = 1
                elif tag == 'aigo':
                    ar[i, 1] = 1
                elif tag == 'aipex':
                    ar[i, 2] = 1
                elif tag == 'indeq':
                    ar[i, 3] = 1
                elif tag == 'indt1':
                    ar[i, 4] = 1
                elif tag == 'mega_cap':
                    ar[i, 5] = 1

        i += 1
    
    i2t = pd.DataFrame(i2c['isin'], columns= ['isin'])
    i2t[toi] = pd.DataFrame(ar, columns= [toi])
    
    return i2t

@st.cache_data
def s32df(bucket_name, file_name):

    cred= config.cred_4
    s3_client = boto3.client('s3',\
                             aws_access_key_id= cred['aws_access_key'], \
                             aws_secret_access_key= cred['aws_secret_key'])
    csv_obj = s3_client.get_object(Bucket= bucket_name,\
                                   Key=file_name)
    csv_string = csv_obj['Body'].read().decode('utf-8')
    df = pd.read_csv( StringIO(csv_string) )
    
    return df

@st.cache_data
# Returns a list, of files stored at the specified S3 bucket
def list_files(bucket_name, prefix= ''):
    # Lists all files in an S3 bucket, at a specific location [ie, folder, aka prefix]

    cred= config.cred_4

    session = boto3.Session(aws_access_key_id= cred['aws_access_key'],
                            aws_secret_access_key= cred['aws_secret_key'])
    s3 = session.client('s3')
    response = s3.list_objects_v2(Bucket= bucket_name, Prefix= prefix)
    if 'Contents' in response:
        files = [obj['Key'] for obj in response['Contents']]
        return files
    else:
        return []

"""
GETs files from an S3 bucket, between the specified dates
Comment: The date must be specified in the name of the file
"""
@st.cache_data
def filter_s3_files(files, start_date, end_date):
    # returns 'files' (ie, paths) within the timeline (start_date, end_date)
    def func(date):
        return datetime.datetime.strptime(date, '%Y-%m-%d').date()  

    ptrn = r'(\d{4}-\d{2}-\d{2})\.csv$'
    dates = []
    paths = []
    for file in files:
        x = re.search(ptrn, file)
        if x:
            paths.append(file)
            dates.append(x.group(1))

    dates = list(map(func, dates))

    # Selct the files within the time-line
    for i in range( len(dates) ):
        if dates[i] >= start_date:
            paths = paths[i:]
            dates = dates[i:]
            break
    for j in range( len(dates) ):
        if dates[j] > end_date:
            paths= paths[:j]
            break

    return paths


# Returns the maximum directionality & RMSE from the input-list of DataFrames; These values are used to fix the scale of each plot
def determine_max(dfs):
    # determine max-value
    _max_dir_  = 0
    _max_rmse_ = 0

    # to determine the "index" with the issue
    count = -1
    for _, df in dfs.items():
        count += 1
        
        if df.empty:
            #st.write(f'{count}th DataFrame is empty')
            continue

        cols= df.columns
        dates = df['date'].unique()
        # error-check 1
        if 'mean_dir' in set(cols):
            for date in dates:
                tmp= df[df['date'] == date]['mean_dir']
                # error-check 2
                if sum(tmp.isna()):
                    pass
                else:
                    tmp = np.histogram(tmp)
                    tmp = tmp[0].max()
                    if tmp > _max_dir_:
                        _max_dir_ = tmp

        # error-check 1
        if 'rmse' in set(cols):
            # error-check 2
            if sum(df['rmse'].isna()):
                #st.write(f'{count}th DataFrame has issues in the column RMSE')
                pass
            else:
                tmp = np.histogram(df['rmse'], bins= 400)
                tmp = tmp[0].max()
                if tmp > _max_rmse_:
                    _max_rmse_ = tmp

    er = dfs['er']
    if not er.empty:
        dates = er['date'].unique()
        for date in dates:
            
            tmp = np.histogram(er[er['date'] == date]['mean_dir'])
            tmp = tmp[0].max()
            if tmp > _max_dir_:
                _max_dir_ = tmp
    
        tmp = np.histogram(er['rmse'], bins= 400)
        tmp = tmp[0].max()
        if tmp > _max_rmse_:
            _max_rmse_ = tmp
    
    _= """
    # round-off
    tmp = len(str(_max_))-2
    tmp = 10**tmp
    _max_ = (_max_//tmp + 5) * tmp
    """
    
    if _max_dir_ > 1000:
        _max_dir_ = 1000
    else:
        _max_dir_ += 10
    if _max_rmse_ > 4000:
        _max_rmse_ = _max_rmse_ // 10
    else:
        _max_rmse_ += 100

    return _max_dir_, _max_rmse_

_= """def plot_directionality(df, y_max, x_axis= 'mean_dir'):

    #if df.empty or ( x_axis not in set(df.columns) ):
        #return None

   # fig = px.histogram(df, x= x_axis, color= 'Date', barmode='group', color_discrete_sequence= color_palette, nbins= 11, range_x= [-9, 109], range_y= [0, y_max])
    #fig.update_layout(xaxis_title='Directionality', yaxis_title='count', xaxis = dict(tickmode = 'linear', tick0 = 0, dtick = 10))
    #return fig

def plot_directionality(df, y_max, x_axis='mean_dir'):
    if df.empty or (x_axis not in set(df.columns)):
        return None

    # Calculate the average of mean_directionality per Date
    avg_directionality = df.groupby("Date")["mean_dir"].mean().round(2)
    
    # Count unique ISINs per Date
    isin_count = df.groupby("Date")["isin"].nunique()

    # Create a new column with formatted labels: "Date (Avg: value, Count: N)"
    df["Date_Label"] = df["Date"].astype(str) + " (Avg: " + df["Date"].map(avg_directionality).astype(str) + \
                       ", Count: " + df["Date"].map(isin_count).astype(str) + ")"

    # Plot histogram with updated labels
    fig = px.histogram(df, x=x_axis, color="Date_Label", barmode="group",
                       color_discrete_sequence=color_palette, nbins=11,
                       range_x=[-9, 109], range_y=[0, y_max])

    fig.update_layout(xaxis_title='date', yaxis_title='count',
                      xaxis=dict(tickmode='linear', tick0=0, dtick=10))

    return fig

#def plot_histogram(df, _max_rmse_, xaxis= 'rmse'):
#    if df.empty or ( xaxis not in set(df.columns) ):
#        return None

 #   df1= df[df[xaxis] <= 150]
 #   if df1.empty:
 #       df1= df
        
    return px.histogram(df1, x= xaxis, marginal= 'violin', opacity= 0.5, range_x= [-10, 150], range_y= [0, _max_rmse_]) #, title= name1)
"""
def plot_directionality(df, y_max, x_axis='mean_dir'):
    if df.empty or (x_axis not in set(df.columns)):
        return None
        
    df = df.dropna(subset=[x_axis]).copy()

    # Calculate the average of mean_directionality per Date
    avg_directionality = df.groupby("Date")["mean_dir"].mean().round(2)
    
    # Count unique ISINs per Date
    isin_count = df.groupby("Date")["isin"].nunique()
    df = df.copy()
    # Create a new column with formatted labels: "Date (Avg: value, Count: N)"
    df["Date_Label"] = df["Date"].astype(str) + " (Avg: " + df["Date"].map(avg_directionality).astype(str) + \
                       ", Count: " + df["Date"].map(isin_count).astype(str) + ")"

    # Plot histogram with updated labels
    fig = px.histogram(df, x=x_axis, color="Date_Label", barmode="group",
                       color_discrete_sequence=color_palette, nbins=11,
                       range_x=[-9, 109], range_y=[0, y_max])

    fig.update_layout(xaxis_title='directionality', yaxis_title='count',
                      xaxis=dict(tickmode='linear', tick0=0, dtick=10))

    return fig

def plot_histogram(df, _max_rmse_, xaxis='rmse'):
    if df.empty or (xaxis not in df.columns):
        return None
    df = df.dropna(subset=[xaxis]).copy()

    # Filter RMSE values for better visualization
    df1 = df[df[xaxis] <= 150] if not df[df[xaxis] <= 150].empty else df

    # Compute mean RMSE for reference
    mean_rmse = df1[xaxis].mean()

    # Create histogram with improved aesthetics
    fig = px.histogram(df1, x=xaxis, 
                       marginal='box',  # Change violin to box for better clarity
                       opacity=0.7, 
                       nbins=50,  # Adjust bin count for readability
                       color_discrete_sequence=['#1f77b4'])  # Use a distinct color

    # Add a vertical line to indicate the mean RMSE
    fig.add_trace(go.Scatter(
        x=[mean_rmse, mean_rmse], 
        y=[0, _max_rmse_], 
        mode="lines",
        name=f"Mean: {mean_rmse:.2f}",
        line=dict(color="red", width=2, dash="dash")
    ))

    # Improve layout readability
    fig.update_layout(
        xaxis_title="RMSE",
        yaxis_title="Count",
        xaxis=dict(showgrid=True, gridcolor='lightgrey'),
        yaxis=dict(showgrid=True, gridcolor='lightgrey'),
        legend=dict(
            title="Legend",
            font=dict(size=12),
            bgcolor="rgba(255,255,255,0.7)"
        )
    )

    return fig


def isins_with_the_worst_rmse(df, x, i2c, er= False):

    if df.empty:
        if er:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse']), pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse'])
        else:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse'])

    if 'rmse' not in set(df.columns):
        return pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse'])
    
    grouped = df.groupby('isin')
    
    a = grouped['rmse'].unique()
    a = pd.DataFrame({'isin': a.index, 'rmse': a.apply(mean_)}).nlargest(n= x, columns= 'rmse')
    
    a.reset_index(drop= True, inplace= True)
    a = pd.merge(a, i2c, on= 'isin', how='left') 
    a.index += 1
    a = a.loc[:, ['isin', 'tic', 'company', 'rmse']]
    
    if er:
        b = grouped['base_rmse'].unique()
        b = pd.DataFrame({'isin': b.index, 'rmse': b.apply(mean_)}).nlargest(n= x, columns= 'rmse')
        b.reset_index(drop= True, inplace= True)
        b = pd.merge(b, i2c, on= 'isin', how='left')  
        b.index += 1
        b = b[['isin', 'tic', 'company', 'rmse']]
        
        return a, b

    return a

def isins_with_the_worst_directionality(df, x, i2c, er= False):

    if df.empty:
        if er:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality']), pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality'])
        else:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality'])

    if 'mean_dir' not in set(df.columns):
        return pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality'])
    
    grouped = df.groupby('isin')
    
    a = grouped['mean_dir'].unique()
    
    # Create a new DataFrame with the unique values and their corresponding counts
    a = pd.DataFrame({'isin': a.index, 'mean_directionality': a.apply(mean_)}).nsmallest(n= x, columns= 'mean_directionality')
    a['mean_directionality'] = a['mean_directionality'].apply(round)
    a.reset_index(drop= True, inplace= True)
    a = pd.merge(a, i2c, on= 'isin', how='left')  
    a.index += 1
    a = a[['isin', 'tic', 'company', 'mean_directionality']]
    
    if er:
        b = grouped['base_mean_dir'].unique()
        b = pd.DataFrame({'isin': b.index, 'mean_directionality': b.apply(mean_)}).nsmallest(n= x, columns= 'mean_directionality')
        b['mean_directionality'] = b['mean_directionality'].apply(round)
        b.reset_index(drop= True, inplace= True)
        b = pd.merge(b, i2c, on= 'isin', how='left') 
        b.index += 1
        b = b[['isin', 'tic', 'company', 'mean_directionality']]
        
        return a, b

    return a

def isins_with_the_best_rmse(df, x, i2c, er= False):

    if df.empty:
        if er:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse']), pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse'])
        else:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse'])

    if 'rmse' not in set(df.columns):
        return pd.DataFrame(columns= ['isin', 'tic', 'company', 'rmse'])

    # jugaad
    df= df[df.loc[:, 'rmse'] > 0]
    
    grouped = df.groupby('isin')
    
    a = grouped['rmse'].unique()
    a = pd.DataFrame({'isin': a.index, 'rmse': a.apply(mean_)}).nsmallest(n= x, columns= 'rmse')
    
    a.reset_index(drop= True, inplace= True)
    a = pd.merge(a, i2c, on= 'isin', how='left') 
    a.index += 1
    a = a.loc[:, ['isin', 'tic', 'company', 'rmse']]
    
    if er:
        b = grouped['base_rmse'].unique()
        b = pd.DataFrame({'isin': b.index, 'rmse': b.apply(mean_)}).nsmallest(n= x, columns= 'rmse')
        b.reset_index(drop= True, inplace= True)
        b = pd.merge(b, i2c, on= 'isin', how='left')  
        b.index += 1
        b = b[['isin', 'tic', 'company', 'rmse']]
        
        return a, b

    return a


def isins_with_the_best_directionality(df, x, i2c, er= False):

    if df.empty:
        if er:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality']), pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality'])
        else:
            return pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality'])

    if 'mean_dir' not in set(df.columns):
        return pd.DataFrame(columns= ['isin', 'tic', 'company', 'mean_directionality'])
    
    grouped = df.groupby('isin')
    
    a = grouped['mean_dir'].unique()
    
    # Create a new DataFrame with the unique values and their corresponding counts
    a = pd.DataFrame({'isin': a.index, 'mean_directionality': a.apply(mean_)}).nlargest(n= x, columns= 'mean_directionality')
    a['mean_directionality'] = a['mean_directionality'].apply(round)
    a.reset_index(drop= True, inplace= True)
    a = pd.merge(a, i2c, on= 'isin', how='left')  
    a.index += 1
    a = a[['isin', 'tic', 'company', 'mean_directionality']]
    
    if er:
        b = grouped['base_mean_dir'].unique()
        b = pd.DataFrame({'isin': b.index, 'mean_directionality': b.apply(mean_)}).nlargest(n= x, columns= 'mean_directionality')
        b['mean_directionality'] = b['mean_directionality'].apply(round)
        b.reset_index(drop= True, inplace= True)
        b = pd.merge(b, i2c, on= 'isin', how='left') 
        b.index += 1
        b = b[['isin', 'tic', 'company', 'mean_directionality']]
        
        return a, b

    return a
    
# GLOBAL VARIABLES
color_palette = px.colors.qualitative.Plotly 
