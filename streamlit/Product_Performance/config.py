import boto3
s3 = boto3.client('s3', 
                  aws_access_key_id= "********************",
                  aws_secret_access_key= "bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/", 
                    region_name='us-east-1') #-----------------S3 Credentials



BUCKET_NAME = "eq-model-input"

config = {
    "AIEQ": {
        "product": "product_performance_dashboard/AIEQ/AIEQ.csv",
        "benchmark": "product_performance_dashboard/AIEQ/SPY.csv",
        "name": "AIEQ AI Powered Equity ETF",
        "benchmark_name": "SPY",
        "has_benchmark": True
    },
    "AIPEX": {
        "product": "product_performance_dashboard/AIPEX/AIPEX.csv",
        "benchmark": "product_performance_dashboard/AIPEX/IWB.csv",
        "name": "AIPEX AI Index",
        "benchmark_name": "IW<PERSON>",
        "has_benchmark": True
    },
    "AIGO8": {
        "product": "product_performance_dashboard/AIGO8/AIGO8.csv",
        "benchmark": "product_performance_dashboard/S&P_benchmark/S&P 500_7%.csv",
        "name": "AIGO8 AI Index",
        "benchmark_name": "S&P 500 Daily Risk Control 7% Index",
        "has_benchmark": True
    },
    "AIGO": {
        "product": "product_performance_dashboard/AIGO/AIGO.csv",
        "benchmark": "product_performance_dashboard/S&P_benchmark/S&P 500_5%.csv",
        "name": "AIGO AI Index",
        "benchmark_name": "S&P 500 Daily Risk Control 5% Index",
        "has_benchmark": True
    },
    "BNPXTRAI": {
        "product": "product_performance_dashboard/BNP/BNP.csv",
        "benchmark": "product_performance_dashboard/S&P_benchmark/S&P 500_5%.csv",
        "name": "BNP AI Index",
        "benchmark_name": "S&P 500 Daily Risk Control 5% Index",
        "has_benchmark": True
    },
    "DB Foresight Index": {
        "product": "product_performance_dashboard/DB/DB.csv",
        "benchmark": "product_performance_dashboard/S&P_benchmark/S&P 500_5%.csv",
        "name": "DB AI Index",
        "benchmark_name": "S&P 500 Daily Risk Control 5% Index",
        "has_benchmark": True
    }
    
}

