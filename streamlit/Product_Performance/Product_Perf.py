from config import *
import streamlit as st
import pandas as pd
import numpy as np
import boto3
from datetime import datetime, timedelta, date
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
import io
import boto3

def initialize_session_state():
    if 'data_loaded' not in st.session_state:
        st.session_state['data_loaded'] = False
    
    if 'current_start_date' not in st.session_state:
        today = date.today()
        st.session_state['current_start_date'] = today - timedelta(days=30)
    
    if 'current_end_date' not in st.session_state:
        st.session_state['current_end_date'] =(date.today()- timedelta(days=2))#date.today()
    
    if 'all_data' not in st.session_state:
        st.session_state['all_data'] = {}
    
    if 'last_update_time' not in st.session_state:
        st.session_state['last_update_time'] = None

@st.cache_data(ttl=3600)
def fetch_s3_data(bucket_name, file_path):
    try:
        response = s3.get_object(Bucket=bucket_name, Key=file_path)
        data = pd.read_csv(io.BytesIO(response['Body'].read()))
        date_col = None
        price_col = None
        
        for col in data.columns:
            if col.lower() in ['date', 'datetime', 'timestamp']:
                date_col = col
                break
        
        for col in data.columns:
            if col.lower() in ['closeprice', 'close', 'price', 'adj close', 'adjusted_close']:
                price_col = col
                break
        
        if date_col is None or price_col is None:
            return None
        data = data.rename(columns={date_col: 'Date', price_col: 'closeprice'})
        data['Date'] = pd.to_datetime(data['Date'])
        data['closeprice'] = pd.to_numeric(data['closeprice'], errors='coerce')
        data = data.dropna(subset=['Date', 'closeprice'])
        
        return data[['Date', 'closeprice']].sort_values('Date').reset_index(drop=True)        
    except Exception as e:
        st.error(f"Error fetching data: {str(e)}")
        return None

def validate_date_range(start_date, end_date):
    if start_date >= end_date:
        return False, "Start date must be before end date"
    
    if end_date > (date.today()- timedelta(days=2)):
        return False, "End date cannot be in the future"
    
    if start_date < date(2020, 1, 1):
        return False, "Start date cannot be before 2020"
    
    return True, ""

def calculate_performance_with_difference(product_df, benchmark_df, start_date, end_date):
    if product_df is None or benchmark_df is None:
        return None
    product_filtered = product_df[
        (product_df['Date'].dt.date >= start_date) & 
        (product_df['Date'].dt.date <= end_date)
    ].copy().sort_values('Date').reset_index(drop=True)
    
    benchmark_filtered = benchmark_df[
        (benchmark_df['Date'].dt.date >= start_date) & 
        (benchmark_df['Date'].dt.date <= end_date)
    ].copy().sort_values('Date').reset_index(drop=True)
    
    if product_filtered.empty or benchmark_filtered.empty:
        return None
    product_filtered['cumulative_return'] = ((product_filtered['closeprice'] / product_filtered['closeprice'].iloc[0]) - 1) * 100
    benchmark_filtered['cumulative_return'] = ((benchmark_filtered['closeprice'] / benchmark_filtered['closeprice'].iloc[0]) - 1) * 100

    merged = pd.merge(product_filtered[['Date', 'cumulative_return']], 
                     benchmark_filtered[['Date', 'cumulative_return']], 
                     on='Date', 
                     suffixes=('_product', '_benchmark'))

    merged['difference'] = merged['cumulative_return_product'] - merged['cumulative_return_benchmark']  
    #merged['difference'] = merged['cumulative_return_benchmark']- merged['cumulative_return_product']  
    return merged

def find_closest_date_data(data_df, target_date, max_days_back=10):
    if data_df is None or data_df.empty:
        return None, None
    
    target_datetime = pd.to_datetime(target_date)
    for days_back in range(max_days_back + 1):
        check_date = target_datetime - pd.Timedelta(days=days_back)
        matching_rows = data_df[data_df['Date'].dt.date == check_date.date()]
        if not matching_rows.empty:
            return matching_rows.iloc[0]['closeprice'], matching_rows.iloc[0]['Date']
    
    return None, None

def get_last_available_date(data_df):
    if data_df is None or data_df.empty:
        return None
    return data_df['Date'].max().date()

def calculate_static_metrics(full_product_df, has_benchmark=False, full_benchmark_df=None, end_date=None):
    if full_product_df is None or full_product_df.empty:
        return None, None
    
    
    if end_date is None:
        last_available_date = get_last_available_date(full_product_df)
        if last_available_date is None:
            return None, None
        current_date_obj = last_available_date
    else:
       
        if hasattr(end_date, 'date'):
            current_date_obj = end_date.date()
        else:
            current_date_obj = end_date
    
   
    current_price, actual_end_date = find_closest_date_data(full_product_df, current_date_obj)
    if current_price is None:
        return None, None
    
    def get_period_return(start_date, current_price, data_df):
        start_price, actual_start_date = find_closest_date_data(data_df, start_date)
        if start_price is None:
            return None, None
        return ((current_price / start_price) - 1) * 100, actual_start_date.date()
    
    
    mtd_start = current_date_obj.replace(day=1)
    mtd_return, mtd_actual_start = get_period_return(mtd_start, current_price, full_product_df)
    
   
    current_month = current_date_obj.month
    if current_month <= 3:
        qtd_start = date(current_date_obj.year - 1, 12, 31)
    elif current_month <= 6:
        qtd_start = date(current_date_obj.year, 3, 31)
    elif current_month <= 9:
        qtd_start = date(current_date_obj.year, 6, 30)
    else:
        qtd_start = date(current_date_obj.year, 9, 30)
    
    qtd_return, qtd_actual_start = get_period_return(qtd_start, current_price, full_product_df)

   
    ytd_start = date(current_date_obj.year, 1, 1)
    ytd_return, ytd_actual_start = get_period_return(ytd_start, current_price, full_product_df)


    try:
        if current_date_obj.month == 1:
            one_month_start = date(current_date_obj.year - 1, 12, current_date_obj.day)
        else:
            one_month_start = date(current_date_obj.year, current_date_obj.month - 1, current_date_obj.day)
    except ValueError:  
        if current_date_obj.month == 1:
            one_month_start = date(current_date_obj.year - 1, 12, 28)
        else:
            one_month_start = date(current_date_obj.year, current_date_obj.month - 1, 28)
    
    one_month_return, one_month_actual_start = get_period_return(one_month_start, current_price, full_product_df)


    try:
        if current_date_obj.month <= 3:
            one_quarter_start = date(current_date_obj.year - 1, current_date_obj.month + 9, current_date_obj.day)
        else:
            one_quarter_start = date(current_date_obj.year, current_date_obj.month - 3, current_date_obj.day)
    except ValueError:  
        if current_date_obj.month <= 3:
            one_quarter_start = date(current_date_obj.year - 1, current_date_obj.month + 9, 28)
        else:
            one_quarter_start = date(current_date_obj.year, current_date_obj.month - 3, 28)
    
    one_quarter_return, one_quarter_actual_start = get_period_return(one_quarter_start, current_price, full_product_df)


    try:
        one_year_start = date(current_date_obj.year - 1, current_date_obj.month, current_date_obj.day)
    except ValueError:  
        one_year_start = date(current_date_obj.year - 1, current_date_obj.month, current_date_obj.day - 1)
    one_year_return, one_year_actual_start = get_period_return(one_year_start, current_price, full_product_df)

    try:
        three_years_start = date(current_date_obj.year - 3, current_date_obj.month, current_date_obj.day)
    except ValueError:  
        three_years_start = date(current_date_obj.year - 3, current_date_obj.month, current_date_obj.day - 1)
    three_years_return, three_years_actual_start = get_period_return(three_years_start, current_price, full_product_df)
    
 
    try:
        five_years_start = date(current_date_obj.year - 5, current_date_obj.month, current_date_obj.day)
    except ValueError: 
        five_years_start = date(current_date_obj.year - 5, current_date_obj.month, current_date_obj.day - 1)
    five_years_return, five_years_actual_start = get_period_return(five_years_start, current_price, full_product_df)
    
    calculation_dates = {
        'MTD': {'start': mtd_actual_start, 'end': current_date_obj},
        'QTD': {'start': qtd_actual_start, 'end': current_date_obj},
        'YTD': {'start': ytd_actual_start, 'end': current_date_obj},
        '1 M': {'start': one_month_actual_start, 'end': current_date_obj},
        '1 Q': {'start': one_quarter_actual_start, 'end': current_date_obj},
        '1 Y': {'start': one_year_actual_start, 'end': current_date_obj},
        '3 Y': {'start': three_years_actual_start, 'end': current_date_obj},
        '5 Y': {'start': five_years_actual_start, 'end': current_date_obj}
    }
    
    if has_benchmark and full_benchmark_df is not None and not full_benchmark_df.empty:
 
        current_bench_price, _ = find_closest_date_data(full_benchmark_df, current_date_obj)
        if current_bench_price is None:
            current_bench_price = full_benchmark_df['closeprice'].iloc[-1]
        
        mtd_bench, _ = get_period_return(mtd_start, current_bench_price, full_benchmark_df)
        qtd_bench, _ = get_period_return(qtd_start, current_bench_price, full_benchmark_df)
        ytd_bench, _ = get_period_return(ytd_start, current_bench_price, full_benchmark_df)
        one_month_bench, _ = get_period_return(one_month_start, current_bench_price, full_benchmark_df)
        one_quarter_bench, _ = get_period_return(one_quarter_start, current_bench_price, full_benchmark_df)
        one_year_bench, _ = get_period_return(one_year_start, current_bench_price, full_benchmark_df)
        three_years_bench, _ = get_period_return(three_years_start, current_bench_price, full_benchmark_df)
        five_years_bench, _ = get_period_return(five_years_start, current_bench_price, full_benchmark_df)

        mtd_diff = mtd_return - mtd_bench if None not in [mtd_return, mtd_bench] else None
        qtd_diff = qtd_return - qtd_bench if None not in [qtd_return, qtd_bench] else None
        ytd_diff = ytd_return - ytd_bench if None not in [ytd_return, ytd_bench] else None
        one_month_diff = one_month_return - one_month_bench if None not in [one_month_return, one_month_bench] else None
        one_quarter_diff = one_quarter_return - one_quarter_bench if None not in [one_quarter_return, one_quarter_bench] else None
        one_year_diff = one_year_return - one_year_bench if None not in [one_year_return, one_year_bench] else None
        three_years_diff = three_years_return - three_years_bench if None not in [three_years_return, three_years_bench] else None
        five_years_diff = five_years_return - five_years_bench if None not in [five_years_return, five_years_bench] else None
        
        metrics = {
            'MTD': {'product': mtd_return, 'benchmark': mtd_bench, 'diff': mtd_diff},
            'QTD': {'product': qtd_return, 'benchmark': qtd_bench, 'diff': qtd_diff},
            'YTD': {'product': ytd_return, 'benchmark': ytd_bench, 'diff': ytd_diff},
            '1 M': {'product': one_month_return, 'benchmark': one_month_bench, 'diff': one_month_diff},
            '1 Q': {'product': one_quarter_return, 'benchmark': one_quarter_bench, 'diff': one_quarter_diff},
            '1 Y': {'product': one_year_return, 'benchmark': one_year_bench, 'diff': one_year_diff},
            '3 Y': {'product': three_years_return, 'benchmark': three_years_bench, 'diff': three_years_diff},
            '5 Y': {'product': five_years_return, 'benchmark': five_years_bench, 'diff': five_years_diff}
        }
    else:
        metrics = {
            'MTD': {'product': mtd_return},
            'QTD': {'product': qtd_return},
            'YTD': {'product': ytd_return},
            '1 M': {'product': one_month_return},
            '1 Q': {'product': one_quarter_return},
            '1 Y': {'product': one_year_return},
            '3 Y': {'product': three_years_return},
            '5 Y': {'product': five_years_return}
        }
    
    return metrics, calculation_dates

def load_data_for_date_range(start_date, end_date):
    all_data = {}
    
    for product, product_config in config.items():

        product_data = fetch_s3_data(BUCKET_NAME, product_config["product"])
  
        benchmark_data = None
        if product_config.get("has_benchmark") and "benchmark" in product_config:
            benchmark_data = fetch_s3_data(BUCKET_NAME, product_config["benchmark"])
        
        all_data[product] = {
            "product": product_data,
            "benchmark": benchmark_data,
            "config": product_config
        }
    
    return all_data

def render_dashboard_content(start_date, end_date, all_data):
    
    st.markdown("---")
    
    for product, data in all_data.items():
        product_config = data["config"]
        product_df = data["product"]
        benchmark_df = data["benchmark"]
        
        if product_df is None or product_df.empty:
            st.error(f" {product}: Product data is missing or empty")
            continue
        
        if product_config.get("has_benchmark") and (benchmark_df is None or benchmark_df.empty):
            st.error(f" {product}: Benchmark data is missing or empty")
            continue
        
        
        with st.container():
            st.markdown(
                    f"<div style='text-align: center; font-weight: bold; font-size:24px;'>{product} vs {product_config['benchmark_name']}</div>",
                unsafe_allow_html=True
                )

            chart_col, metrics_col = st.columns([2, 1])
            
            with chart_col:
                if product_config.get("has_benchmark") and benchmark_df is not None:
                    performance_data = calculate_performance_with_difference(
                        product_df, benchmark_df, start_date, end_date
                    )
                    
                    if performance_data is not None:
                        final_diff = performance_data['difference'].iloc[-1]
                        final_diff_bps = final_diff * 100  # Convert to bps
                        diff_color = 'green' if final_diff >= 0 else 'red'
                        fill_color = 'rgba(0,255,0,0.1)' if final_diff >= 0 else 'rgba(255,0,0,0.1)'
                        
                        
                        product_end_data = product_df[product_df['Date'].dt.date == end_date]
                        benchmark_end_data = benchmark_df[benchmark_df['Date'].dt.date == end_date]
                        
                        product_close_price = product_end_data['closeprice'].iloc[0] if not product_end_data.empty else None
                        benchmark_close_price = benchmark_end_data['closeprice'].iloc[0] if not benchmark_end_data.empty else None
                        
                        
                        if product_close_price is not None:
                            st.markdown(" ") 
                            st.markdown(f"**As of {end_date} Close Price ({product}):** {product_close_price:.2f}")
                        if benchmark_close_price is not None:
                            st.markdown(f"**As of {end_date} Close Price ({product_config['benchmark_name']}):** {benchmark_close_price:.2f}")
                        
                        fig = make_subplots(specs=[[{"secondary_y": True}]])
                        
                        fig.add_trace(
                            go.Scatter(
                                x=performance_data['Date'],
                                y=performance_data['cumulative_return_product'],
                                mode='lines',
                                name=f"{product}",
                                line=dict(color='blue', width=2)
                            ),
                            secondary_y=False,
                        )
 
                        fig.add_trace(
                            go.Scatter(
                                x=performance_data['Date'],
                                y=performance_data['cumulative_return_benchmark'],
                                mode='lines',
                                name=f"{product_config['benchmark_name']} (Benchmark)",
                                line=dict(color='orange', width=2)
                            ),
                            secondary_y=False,
                        )
                        fig.add_trace(
                            go.Scatter(
                                x=performance_data['Date'],
                                y=performance_data['difference'] * 100,  
                                mode='lines',
                                name="Outperformance (bps)",
                                line=dict(color=diff_color, width=2),
                                fill='tozeroy',
                                fillcolor=fill_color
                            ),
                            secondary_y=True,
                        )
                        
                        fig.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5)
                        
                        #fig.update_xaxes(title_text="Date")
                        
                        
                        primary_range = [performance_data[['cumulative_return_product', 'cumulative_return_benchmark']].min().min(),
                                       performance_data[['cumulative_return_product', 'cumulative_return_benchmark']].max().max()]
                        secondary_range = [performance_data['difference'].min() * 100, performance_data['difference'].max() * 100]  # Convert to bps
                        
                       
                        primary_max = max(abs(primary_range[0]), abs(primary_range[1]))
                        secondary_max = max(abs(secondary_range[0]), abs(secondary_range[1]))
                        
                        fig.update_yaxes(
                            title_text="Performance (%)", 
                            secondary_y=False,
                            range=[-primary_max * 1.1, primary_max * 1.1]
                        )
                        fig.update_yaxes(
                            title_text="Outperformance (bps)", 
                            secondary_y=True,
                            range=[-secondary_max * 1.1, secondary_max * 1.1]
                        )
                        
                        fig.update_layout(
                            # title={
                            #     #'text': f"{product} vs {product_config['benchmark_name']}",
                            #     'x': 0.5,
                            #     'xanchor': 'center'
                            # },
                            height=500, 
                            legend=dict(
                                orientation="h",
                                yanchor="top",
                                y=-0.2,
                                xanchor="center",
                                x=0.5
                            ),
                            margin=dict(b=50)
                        )
                        st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.warning(f"No data available for the selected date range")
                else:
                    product_filtered = product_df[
                        (product_df['Date'].dt.date >= start_date) & 
                        (product_df['Date'].dt.date <= end_date)
                    ].copy().sort_values('Date').reset_index(drop=True)
                    
                    if not product_filtered.empty:
                        product_filtered['cumulative_return'] = ((product_filtered['closeprice'] / product_filtered['closeprice'].iloc[0]) - 1) * 100
                        
                    
                        product_end_data = product_df[product_df['Date'].dt.date == end_date]
                        product_close_price = product_end_data['closeprice'].iloc[0] if not product_end_data.empty else None
                        
                        
                        # if product_close_price is not None:
                        #     st.markdown(f"**as of {end_date} Close Price ({product}):** {product_close_price:.2f}")
                        
                        fig = go.Figure()
                        fig.add_trace(go.Scatter(
                            x=product_filtered['Date'],
                            y=product_filtered['cumulative_return'],
                            mode='lines',
                            line=dict(color='blue'),
                            fill='tozeroy',
                            fillcolor='rgba(0,0,255,0.1)'
                        ))
                        
                        fig.update_layout(
                            title={
                                'text': f"{product} Performance",
                                'x': 0.5,
                                'xanchor': 'center'
                            },
                            xaxis_title="Date",
                            yaxis_title="Cumulative Return (%)",
                            height=400
                        )
                        st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.warning(f"No data available for the selected date range")
            
            with metrics_col:
                st.markdown(" ")
                st.markdown(" ")
                st.markdown(" ")
                st.markdown(" ")
                st.markdown(f"As of - {end_date}")

                metrics, calculation_dates = calculate_static_metrics(
                    product_df,
                    product_config.get("has_benchmark"),
                    benchmark_df,end_date
                )
                
                if metrics and calculation_dates:
                    metric_data = []
                    for period in ["MTD", "QTD", "YTD", "1 M", "1 Q","1 Y", "3 Y", "5 Y"]:
                        row = {"Period": period}
                        # if calculation_dates[period]['start'] and calculation_dates[period]['end']:
                        #     row["Date Range"] = f"{calculation_dates[period]['start'].strftime('%Y-%m-%d')} to {calculation_dates[period]['end'].strftime('%Y-%m-%d')}"
                        # else:
                        #     row["Date Range"] = "-"
                        row[f"{product} returns"] = f"{metrics[period]['product']:.2f}%" if metrics[period]['product'] is not None else "-"
                        
                        if product_config.get("has_benchmark"):
                            benchmark_name = product_config.get('benchmark_name', 'Benchmark')
                            row[f"{benchmark_name} returns"] = f"{metrics[period]['benchmark']:.2f}%" if metrics[period]['benchmark'] is not None else "-"
                            row["Outperformance"] = f"{metrics[period]['diff']:.2f}%" if metrics[period]['diff'] is not None else "-"
                        
                        metric_data.append(row)
                    
                    st.dataframe(pd.DataFrame(metric_data), hide_index=True, use_container_width=True)
                else:
                    st.warning("Unable to calculate performance metrics")
        
        st.markdown("---")