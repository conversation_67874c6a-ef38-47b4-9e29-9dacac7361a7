from Product_Perf import *

warnings.filterwarnings('ignore')
st.set_page_config(layout="wide")
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .filter-section {
        background-color: #f8f9fa;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        border: 1px solid #e9ecef;
    }
    .product-section {
        background-color: white;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stButton > button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 5px;
        font-size: 1.1rem;
        font-weight: bold;
        width: 100%;
    }
    .metric-table {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-top: 1rem;
    }
    .date-selector {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    .status-success {
        color: #28a745;
        font-weight: bold;
    }
    .status-warning {
        color: #ffc107;
        font-weight: bold;
    }
    .status-error {
        color: #dc3545;
        font-weight: bold;
    }
    .centered-title {
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def apply_overlay_sidebar_css():
    """Apply CSS to make sidebar overlay instead of pushing content"""
    st.markdown("""
    <style>
    /* Make sidebar overlay instead of pushing content */
    .css-1d391kg, .st-emotion-cache-1d391kg {
        position: fixed !important;
        left: -21rem !important;
        transition: left 0.3s ease !important;
        z-index: 1000 !important;
        background-color: white !important;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
    }
    
    /* When sidebar is expanded */
    .css-1d391kg.css-1aumxhk, .st-emotion-cache-1d391kg.st-emotion-cache-1aumxhk {
        left: 0 !important;
    }
    
    /* Main content area should not move */
    .css-18e3th9, .st-emotion-cache-18e3th9 {
        margin-left: 0 !important;
        padding-left: 1rem !important;
    }
    
    /* Sidebar toggle button positioning */
    .css-1rs6os, .st-emotion-cache-1rs6os {
        position: fixed !important;
        left: 1rem !important;
        top: 1rem !important;
        z-index: 1001 !important;
        background-color: #ff4b4b !important;
        color: white !important;
        border: none !important;
        border-radius: 4px !important;
        padding: 0.5rem !important;
        cursor: pointer !important;
    }
    
    /* Alternative selectors for different Streamlit versions */
    [data-testid="stSidebar"] {
        position: fixed !important;
        left: -21rem !important;
        transition: left 0.3s ease !important;
        z-index: 1000 !important;
        background-color: white !important;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
    }
    
    [data-testid="stSidebar"][aria-expanded="true"] {
        left: 0 !important;
    }
    
    [data-testid="collapsedControl"] {
        position: fixed !important;
        left: 1rem !important;
        top: 1rem !important;
        z-index: 1001 !important;
    }
    
    /* Dropdown menus should appear above sidebar */
    .css-select-container, .st-emotion-cache-select-container,
    [data-baseweb="select"], [data-testid="stSelectbox"] > div,
    .css-1wa3eu0-placeholder, .st-emotion-cache-1wa3eu0-placeholder {
        z-index: 1002 !important;
    }
    
    /* Dropdown options menu */
    .css-26l3qy-menu, .st-emotion-cache-26l3qy-menu,
    [data-baseweb="popover"], .css-1n76uvr, .st-emotion-cache-1n76uvr {
        z-index: 1003 !important;
    }
    
    /* Main app content */
    .main .block-container {
        padding-left: 1rem !important;
        max-width: none !important;
    }
    
    /* Ensure graphs don't get pushed */
    [data-testid="stAppViewContainer"] > .main {
        margin-left: 0 !important;
    }
    </style>
    """, unsafe_allow_html=True)

def main():
    apply_overlay_sidebar_css()
    
    initialize_session_state()
    
    #today = date.today()
    today = (date.today())- timedelta(days=2)
    default_start = today - timedelta(days=30)
    
    with st.sidebar:
        
        
        # Start date section
        st.markdown("**Chart Start Date**")
        start_col1, start_col2, start_col3 = st.columns(3)
        
        with start_col1:
            start_day = st.selectbox(
                "Day", 
                range(1, 32), 
                key="start_day", 
                index=default_start.day-1,
                label_visibility="collapsed"
            )
        with start_col2:
            start_month = st.selectbox(
                "Month", 
                ["Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                key="start_month",
                index=default_start.month-1,
                label_visibility="collapsed"
            )
        with start_col3:
            start_year = st.selectbox(
                "Year", 
                range(2020, today.year+2), 
                key="start_year", 
                index=default_start.year-2020,
                label_visibility="collapsed"
            )
        
        try:
            start_date = date(
                start_year, 
                ["Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].index(start_month)+1, 
                start_day
            )
        except ValueError:
            start_date = date(
                start_year, 
                ["Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].index(start_month)+1, 
                1
            )

        # End date section
        st.markdown("**As of Date**")
        end_col1, end_col2, end_col3 = st.columns(3)
        
        with end_col1:
            end_day = st.selectbox(
                "Day", 
                range(1, 32), 
                key="end_day", 
                index=today.day-1,
                label_visibility="collapsed"
            )
        with end_col2:
            end_month = st.selectbox(
                "Month", 
                ["Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                key="end_month",
                index=today.month-1,
                label_visibility="collapsed"
            )
        with end_col3:
            end_year = st.selectbox(
                "Year", 
                range(2020, today.year+2), 
                key="end_year", 
                index=today.year-2020,
                label_visibility="collapsed"
            )
        
        try:
            end_date = date(
                end_year, 
                ["Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].index(end_month)+1, 
                end_day
            )
        except ValueError:
            end_date = date(
                end_year, 
                ["Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                 "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].index(end_month)+1, 
                1
            )

        # GO button
        st.markdown("---")
        go_button = st.button("Go", key="go_button", type="primary", use_container_width=True)
    
    if st.session_state['data_loaded']:
        st.info(f" data from {st.session_state['current_start_date'].strftime('%Y-%m-%d')} to {st.session_state['current_end_date'].strftime('%Y-%m-%d')}")
    
    if go_button:
        is_valid, error_msg = validate_date_range(start_date, end_date)
        if not is_valid:
            st.error(f" {error_msg}")
            if st.session_state['data_loaded'] and st.session_state['all_data']:
                render_dashboard_content(
                    st.session_state['current_start_date'], 
                    st.session_state['current_end_date'], 
                    st.session_state['all_data']
                )
            return
        with st.spinner("Loading data..."):
            new_all_data = load_data_for_date_range(start_date, end_date)
            st.session_state['all_data'] = new_all_data
            st.session_state['current_start_date'] = start_date
            st.session_state['current_end_date'] = end_date
            st.session_state['data_loaded'] = True
            st.session_state['last_update_time'] = datetime.now()
  
        render_dashboard_content(start_date, end_date, new_all_data)
        
    elif st.session_state['data_loaded'] and st.session_state['all_data']:
        render_dashboard_content(
            st.session_state['current_start_date'], 
            st.session_state['current_end_date'], 
            st.session_state['all_data']
        )
    else:
        default_start = today - timedelta(days=30)
        default_end = today
        
        with st.spinner("Loading data..."):
            default_data = load_data_for_date_range(default_start, default_end)

            st.session_state['all_data'] = default_data
            st.session_state['current_start_date'] = default_start
            st.session_state['current_end_date'] = default_end
            st.session_state['data_loaded'] = True
            st.session_state['last_update_time'] = datetime.now()
        render_dashboard_content(default_start, default_end, default_data)

if __name__ == "__main__":
    main()
