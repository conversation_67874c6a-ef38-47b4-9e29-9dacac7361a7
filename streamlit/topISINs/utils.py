# MODULES
import streamlit as st
from eq_common_utils.utils.opensearch_helper import OpenSearch
import boto3
from io import StringIO
import pandas as pd
import numpy as np
import datetime
import calendar
import time

import json

from aws_requests_auth.aws_auth import AWSRequestsAuth
from opensearchpy.connection.http_requests import RequestsHttpConnection

import config


# FUNCTION DEFINITIONS
def establish_connection(credentials):
    """
    Desc: Creates an OpenSearch client. It establishes a connection to an OpenSearch cluster.
    Argument: 'credentials' is a dictionary which contains the following keys.
    'host' : IP address of the cluster
    'port' : the port on which the service is running
    'key_id' & 'secret' are used for authentication (& possibly encryption)
    'region' : specify the region where the cluster is located
    """
    client =  OpenSearch(host  = credentials['host'],
                         port  = credentials['port'],
                         key_id= credentials['key_id'],
                         secret= credentials['secret'],
                         region= credentials['region'])
    
    return client

def str2date(x):
    # This function converts a string into a date
    try:
        return datetime.datetime.strptime(x, '%Y-%m-%d').date()
    except:
        try:
            return datetime.datetime.strptime(x, '%Y-%m-%dT%H:%M:%S').date()
        except:
            raise Exception('In the column \'date\', there are formats other than [\'%Y-%m-%d\', \'%Y-%m-%dT%H:%M:%S\']')

def s32df(bucket_name, file_name):

    cred= config.cred_1

    aws_access_key = cred['aws_access_key']
    aws_secret_key = cred['aws_secret_key']

    s3_client = boto3.client('s3',\
                             aws_access_key_id= aws_access_key, \
                             aws_secret_access_key= aws_secret_key)
    csv_obj = s3_client.get_object(Bucket= bucket_name,\
                                   Key=file_name)
    csv_string = csv_obj['Body'].read().decode('utf-8')
    df = pd.read_csv( StringIO(csv_string) )
    
    return df

def utils_get_eq_platform_master(client, start_date, end_date, schedular):

    # specify the index
    index = 'eq_platform_master_' + str(end_date.year)

    # create the query
    start_date = start_date.isoformat()
    end_date = end_date.isoformat()
    cols = ['date', 'ticker', 'isin', 'er', 'diff_perc', 'scaled_er_1', 'tags', 'conm']
    query= {"query": {"bool":{"must":[{"term":{"schedular.keyword":{"value":schedular}}}, {"range": {"date": {"gte": start_date, "lt": end_date}}}]}}, "_source": cols}
    
    # download
    df = client.get_es_hits_as_df(indexes= index, body= query, size= 10000)

    if len(df) == 0:
        st.error('No data available for this date')
        st.stop()

    # preprocess
    df['date'] = df['date'].apply(str2date)
    df.sort_values(by= ['date'], inplace= True)
    df.reset_index(inplace= True, drop= True)
    
    return df
    

def get_eq_platform_master(start_date, end_date, schedular):

    # specify the credentials to access the data
    credentials = config.cred_2

    # establish a connection with OpenSearch
    client = establish_connection(credentials)

    if start_date.year == end_date.year:
        df= utils_get_eq_platform_master(client, start_date, end_date, schedular)
    elif start_date.year < end_date.year:
        start_tmp= datetime.datetime(year= end_date.year, month= 1, day= 1).date()
        end_tmp  = datetime.datetime(year= start_date.year, month= 12, day= 31).date()
        
        df1= utils_get_eq_platform_master(client, start_date, end_tmp, schedular)
        df2= utils_get_eq_platform_master(client, start_tmp, end_date, schedular)
        
        df= pd.concat([df1, df2], axis= 0)
    else:
        st.stop()

    
    return df

def extract_tags(df):
    
    columns = ['isin', 'tags']
    
    df = df[ df['tags'].notna() ][columns]
    df = df.drop_duplicates(subset='isin')
    df.reset_index(inplace= True, drop= True)
    
    i2t = {}
    j = 0
    for i, row in df.iterrows():
        # maps 'isin' to a list of 'tags'
        i2t[row.iloc[0]] = row.iloc[1].split(',')
    
    toi = ['aieq', 'aipex']
    toi.sort()
    _toi_ = set(toi)
    
    ar = np.zeros( (len(df), len(toi)) )
    i = 0
    for isin, tags in i2t.items():
        for tag in tags:
            if tag in _toi_:
                if tag == 'aieq':
                    ar[i, 0] = 1
                elif tag == 'aipex':
                    ar[i, 1] = 1
        i += 1
    
    i2t = pd.DataFrame(df['isin'], columns= ['isin'])
    i2t[toi] = pd.DataFrame(ar, columns= [toi])

    return i2t

#This decorator is used to cache [recently] downloaded-data. Consequently, a local copy of the dataset is available, i.e., it does not have to be re-downloaded
_ = """
downloads the data from OpenSearch, stored as 'index_name', from 'start_date' till 'end_date'
Returns [a section of] the dataset [from start_date till end_date] in the JSON format
"""
@st.cache_data(ttl=600)
def download_dataset(_client, index_name, query):
    
    # this list will store the data
    all_data = []

    # Initialize the scroll
    initial_query = query

    try:
        initial_response = _client.search(
            index=index_name,
            body=json.dumps(initial_query),
            scroll='2m',  # Scroll context is valid for 2 minutes
            size= 10000
        )
    except:
        return []

    # Get the scroll ID and initial batch of hits
    scroll_id = initial_response['_scroll_id']
    hits = initial_response['hits']['hits']

    while hits:
        # Collects the current batch of hits
        all_data.extend(hits)

        # Fetch the next batch of results
        scroll_response = _client.scroll(
            scroll_id=scroll_id,
            scroll='2m'
        )

        # Update the scroll ID and hits
        scroll_id = scroll_response['_scroll_id']
        hits = scroll_response['hits']['hits']

    

    return all_data

@st.cache_data(ttl=600)
def get_best_er_model(start_date, end_date, schedular):

    from opensearchpy.client import OpenSearch


    # 1. Establish connection with AWS
    """
    'AWSRequestsAuth' is a class from the 'aws_requests_auth' library,
    which is used for *authenticating* HTTP requests
    to AWS services using AWS credentials.
    This class allows me to securely *sign* requests in accordance with AWS's Signature Version 4 signing process
    
    reference: https://github.com/davidmuller/aws-requests-auth
    """

    cred= config.cred_3
    es_auth_cred = AWSRequestsAuth(
                            aws_access_key= cred['aws_access_key'],
                            aws_secret_access_key= cred['aws_secret_access_key'],
                            aws_host= cred['aws_host'],
                            aws_region= cred['aws_region'],
                            aws_service= cred['aws_service'])
        
    # create a 'client' to access the data on OpenSearch    
    hosts = [cred['host']]
    client = OpenSearch(
                        hosts= hosts,
                        port= 443,
                        http_auth=es_auth_cred,
                        connection_class=RequestsHttpConnection
                     )

    # These are the indices, from where I will fetch the data
    indices = [ 'eq_best_model_metrics_' ,
              ]

    # Only these columns shall be download
    er_col   = ['date','isin', 'schedular', 'mean_dir', 'closeprice']
    columns = [er_col]

    if len(indices) != len(columns):
        st.error('Mismatch in the function get_data(start_date, end_date): The columns (to be downloaded, per model) have not been specified (for all models).')
        st.stop()

    start = start_date.isoformat()
    end = end_date.isoformat()

    # GET data
    all_data = []
    for i in range(len(indices)):
        if start_date.year == end_date.year:
            query = {'query' : {'bool' : {'must' : [{'term' : { 'schedular.keyword' : {'value' : schedular}}}, {'range' : {'date' : {'gte' : start, 'lt' : end}}} ]}}, '_source' : columns[i]} #, 'size' : 100}
            data = download_dataset(client, indices[i] + str(start_date.year), query)
            
        else:
            last = datetime.datetime(year= start_date.year, month= 12, day= 31).date().isoformat()
            first = datetime.datetime(year= end_date.year, month= 1, day= 1).date().isoformat()
            
            query1 = {'query' : {'bool' : {'must' : [{'term' : { 'schedular.keyword' : {'value' : schedular}}}, {'range' : {'date' : {'gte' : start, 'lt' : last}}} ]}}, '_source' : columns[i]} #, 'size' : 100}
            data = download_dataset(client, indices[i] + str(start_date.year), query1)

            query2 = {'query' : {'bool' : {'must' : [{'term' : { 'schedular.keyword' : {'value' : schedular}}}, {'range' : {'date' : {'gte' : first, 'lt' : end}}} ]}}, '_source' : columns[i]} #, 'size' : 100}
            data2 = download_dataset(client, indices[i] + str(end_date.year), query2)

            data.extend(data2)
        
        all_data.append(data)

    # 3 extract data
    # This list shall store the dataframes
    dfs = []

    count = 0
    # Extract the data from the JSON file, into a DataFrame
    for data in all_data:
        
        # Extract the data from the JSON file
        lst = []
        for d in data:
            lst.append(d['_source'])

        # Load the data into a DataFrame
        df = pd.DataFrame(lst)

        if df.empty:
            count += 1
        else:
            # preprocessing
            df = df[df['date'].notna()]
            
            #df = df[ df['schedular'] == schedular]
            #df.drop(['schedular'], axis=1, inplace= True)
            
            df['date'] = pd.to_datetime(df['date'], format= 'mixed').dt.date
            df['Date'] = df['date'].apply(func= lambda x: x.strftime('%b %d, %Y'))
            
            df.sort_values(by= ['date', 'isin'], inplace= True)
            df.reset_index(inplace= True, drop= True)

        dfs.append(df)

    if len(dfs) == count:
        st.stop()
    
    return dfs[0]

@st.cache_data(ttl=600)
def get_SPY(start_date, end_date):
    cred= config.cred_4
    bucket= cred['bucket']
    path = cred['path']

    df = s32df(bucket, path)

    df['date'] = df['date'].apply(str2date)

    df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
    df.reset_index(inplace= True, drop= True)

    return df

@st.cache_data(ttl=600)
def get_data(start_date, end_date, schedular):

    # this includes the 'end_date', in the downloaded data
    end_date += datetime.timedelta(days= 1)

    # DataFrame 1
    pm = get_eq_platform_master(start_date, end_date, schedular)

    """
    To compute the 'actual-monthly-returns' from the 'close-price', I need data
    of the previous 22 business days (per ISIN)
    """
    tmp = start_date - pd.tseries.offsets.BDay(152)
    tmp = tmp.date()

    # DataFrame 2
    spy = get_SPY(tmp, end_date)

    # DataFrame 3
    """
    Context: If the dataset spans 2 months, then the no. of rows >= 100k
    Issue: 100k is the 'rate-limit'
    Solution: Consequently, I download it over 2 steps
    """
    if (end_date - tmp).days < 32:
        
        er = get_best_er_model(start_date, end_date, schedular)
    else:
        
        #st.write(f'{tmp}, {start_date}, {end_date}')
        er1 = get_best_er_model(tmp, start_date, schedular)
        er2 = get_best_er_model(start_date, end_date, schedular)
        er = pd.concat([er1, er2], axis= 0)

        count = 0
        for i, g in er.groupby(['isin']):
            n= len(g['date'].unique())
            if n > 22:
                count += 1
        #st.write(f'{count}, {er['isin'].nunique()}')
    
    dfs = [pm, er, spy]
    return dfs

@st.cache_data(ttl=600)
def preprocess_data(dfs, start_date, end_date, schedular):
    
    [pm, er, spy] = dfs
    
    # ER: to calculate the 'actual_monthly_return' from 'closeprice'
    er.sort_values(by=['isin', 'date'], inplace= True)
    er.reset_index(inplace= True, drop= True)
    if schedular == 'Monthly':
        er['actual_monthly_returns'] = er.groupby('isin')['closeprice'].pct_change(periods= 22, fill_method= None) * 100
        #er['actual_monthly_returns']= er['actual_monthly_returns'].shift(periods= -22)
    elif schedular == 'Daily':
        er['actual_daily_returns'] = er.groupby('isin')['closeprice'].pct_change(periods= 1, fill_method= None) * 100
        #er['actual_daily_returns']= er['actual_daily_returns'].shift(periods= -1)

    # SPY: to calculate the 'actual_monthly_return' from 'close price'
    spy.sort_values(by=['date'], inplace= True)
    spy.reset_index(inplace= True, drop= True)
    if schedular == 'Monthly':
        spy['SPY_actual_monthly_returns'] = spy['close_price'].pct_change(periods=22, fill_method= None) * 100
        #spy['SPY_actual_monthly_returns']= spy['SPY_actual_monthly_returns'].shift(periods= -22)
    elif schedular == 'Daily':
        spy['SPY_actual_daily_returns'] = spy['close_price'].pct_change(periods= 1, fill_method= None) * 100
        #spy['SPY_actual_daily_returns']= spy['SPY_actual_daily_returns'].shift(periods= -1)
    

    # 'rectification'
    if (end_date - start_date).days >= 1:
        if schedular == 'Monthly':
            tmp = spy[spy['SPY_actual_monthly_returns'].notna()]['SPY_actual_monthly_returns']
            spy['SPY_actual_monthly_returns'] = tmp.mean()
        elif schedular == 'Daily':
            tmp = spy[spy['SPY_actual_daily_returns'].notna()]['SPY_actual_daily_returns']
            spy['SPY_actual_daily_returns'] = tmp.mean()

    #st.dataframe(er)


    # filter data
    spy = spy[(spy['date'] >= start_date) & (spy['date'] <= end_date)]
    er  =  er[(er['date']  >= start_date) & (er['date']  <= end_date)]

    if len(er) == 0:
        st.error('No data available for this date')
        st.stop()

    if len(spy) == 0:
        st.error('No data available for this date')
        st.stop()

    # Creates 2 columns, to identify whether an ISIN belongs to 'aieq' or 'aipex'
    i2t = extract_tags(pm)
    pm = pd.merge(left= pm, right= i2t, how= 'left', on= ['isin'])
    #st.text(pm.columns)
    pm.reset_index(inplace= True, drop= True)
    pm.drop(labels= ['tags'], axis= 1, inplace= True)
    df = pd.merge(left= pm, right= er, how= 'left', on= ['isin', 'date'])
    df[['aieq', 'aipex']] = df[['aieq', 'aipex']].astype('bool')
    df = df[df['aieq'] | df['aipex']]

    col1 = None
    col2 = None
    col3 = None
    if schedular == 'Monthly':
        col1 = 'actual_monthly_returns'
        col2 = 'SPY_actual_monthly_returns'
        col3 = 'SPY Monthly Returns'
    elif schedular == 'Daily':
        col1 = 'actual_daily_returns'
        col2 = 'SPY_actual_daily_returns'
        col3 = 'SPY Daily Returns'
            
    namesake= {'scaled_er_1' : 'ER Scaled by Confidence', 'er' : 'ER', 'ticker': 'Ticker', 'aieq': 'Part of AIEQ Universe', 'aipex': 'Part of AIPEX Universe', col1 : 'Actual Return', 'isin': 'ISIN', 'conm' : 'Company', 'mean_dir' : 'Directionally Correct'}
    df.rename(namesake, axis= 1, inplace= True)

    
    df['Actual Return'] = df['Actual Return'].apply(rectify)
    df['ER'] =  df['ER'] * 100
    df['Delta'] = df['ER'] - df['Actual Return']
    df['Delta'] = df['Delta'].apply(lambda x: abs(x))

    spy = spy[['date', col2]]
    df = pd.merge(left= df, right= spy, how= 'inner', on= ['date'])
    df.rename({col2 : col3}, axis= 1, inplace= True)
    df['Outperformed SPY'] = df.apply(compare_with_SPY, args= (col3, ), axis= 1)

    #st.dataframe(df['date'].unique())
    
    cols = ['Ticker', 'ISIN', 'Company', 'ER', 'Actual Return', 'Delta', col3, 'Outperformed SPY', 'Directionally Correct', 'ER Scaled by Confidence', 'Part of AIEQ Universe', 'Part of AIPEX Universe']
    df = df[cols]
    #df['Directionally Correct'] = df['Directionally Correct'].astype('int')

    df.sort_values(by= ['ER Scaled by Confidence'], ascending= False, inplace= True)
    df.reset_index(inplace= True, drop= True)
    df.index += 1

    return df

def compare_with_SPY(row, col):
    
    if np.isnan(row['Actual Return']) or np.isnan(row[col]):
        return None

    x = round(row['Actual Return'], 4)
    y = round(row[col], 4)
    
    if x > y:
        return True
    else:
        return False

def get_days_in_month(year, month):
    # Returns the no. of days in a month (for a specific year)
    return calendar.monthrange(year, month)[1]

def aggregate_results(df):
    df = df.groupby(['Ticker', 'ISIN', 'Company']).mean()
    df[['Outperformed SPY', 'Part of AIEQ Universe', 'Part of AIPEX Universe']] = df[['Outperformed SPY', 'Part of AIEQ Universe', 'Part of AIPEX Universe']].astype('bool')
    df.sort_values(by= ['ER Scaled by Confidence'], ascending= False, inplace= True)
    df.reset_index(inplace= True)
    df.index += 1
    return df

def unclick_monthly():
    if st.session_state.amr == True:
        st.session_state.amr = False

def unclick_weekly():
    if st.session_state.awr == True:
        st.session_state.awr = False
        
def select_week(year, month, num):
    if num == 1:
        start_date = datetime.datetime(year, month, 1).date()
        end_date = datetime.datetime(year, month, 8).date()
    if num == 2:
        start_date = datetime.datetime(year, month, 8).date()
        end_date = datetime.datetime(year, month, 15).date()
    if num == 3:
        start_date = datetime.datetime(year, month, 15).date()
        end_date = datetime.datetime(year, month, 22).date()
    if num == 4:
        start_date = datetime.datetime(year, month, 22).date()
        end_date = datetime.datetime(year, month+1, 1).date() - datetime.timedelta(days= 1)
    
    return (start_date, end_date)

def rectify(x):
    if x == inf:
        return nan
    else:
        return x

# global variables for the function rectufy()
nan = float('nan')
inf = float('inf')
