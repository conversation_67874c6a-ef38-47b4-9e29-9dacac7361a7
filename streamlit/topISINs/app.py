from utils import *

st.set_page_config(layout= "wide")

st.markdown(f"<h3 style='text-align: center;'>Top performing ISINs</h3>", unsafe_allow_html=True)
st.text('')
st.text('')
if 'amr' not in st.session_state:
    st.session_state.amr = False

today = datetime.datetime.now().date()
mm = today.strftime("%b")

years = pd.Series(reversed(range(2008, 2026)))
months = pd.Series((range(1, 13, 1)))
months = pd.to_datetime(months, format='%m').dt.month_name().str.slice(stop= 3)
_, date, _ = st.columns([1, 6, 1])
with date:
    year, month, day, sched = st.columns(4)
    with year:
        y1 = int( st.selectbox('Select a year', years, key= 'YY'))
    with month:
        m1 = st.selectbox('Select a month', months, key= 'mm', index= int(months[months == mm].index[0]))
        m1 = months[months == m1].index[0] + 1
    with day:
        #if 'awr' not in st.session_state:
        #    pass
        #elif st.session_state.awr:
        #    d1 = st.selectbox('Select a week', pd.Series(range(1, 5)), key= 'ww')
        #else:
        if not st.session_state.amr:
            num_days = get_days_in_month(y1, m1)
            d1 = st.selectbox('Select a day', pd.Series(range(1, num_days + 1)), key= 'dd')
    with sched:
        schedular = st.selectbox('Select a Schedular', ['Daily', 'Monthly'], index= 1)

_, l, r, _ = st.columns(4)
with l:
    if st.checkbox('Aggregate Monthly Results', key= 'amr', on_change= unclick_weekly):
       pass

with r:
    if st.checkbox('Aggregate Weekly Results', key= 'awr', on_change= unclick_monthly):
        pass

if 'button_clicked' not in st.session_state:
    st.session_state.button_clicked = False

st.text('')
    
_, button, _ = st.columns([6, 4, 6])
with button:
    if st.session_state.button_clicked or st.button('Run Query', key= 'btn'):
        st.session_state.button_clicked = True
        _="""if m1 == 1:
            st.error('Currently, queries cannot span multiple years')
            st.stop()"""

        if not st.session_state.amr:
            start_date = datetime.datetime(year= y1, month= m1, day= d1).date()
            end_date = start_date #+ datetime.timedelta(days= 1)
        else:
            start_date= datetime.datetime(y1, m1, 1).date()
            end_date= datetime.datetime(y1, m1 +1, 1).date() - datetime.timedelta(days= 1)

        if st.session_state.awr:
            start_date -= datetime.timedelta(days= 6)
            st.write(f'Timeline:   {months[start_date.month-1]} {start_date.day}   till   {months[end_date.month-1]} {end_date.day}')
    

        dfs = get_data(start_date, end_date, schedular)
        df = preprocess_data(dfs, start_date, end_date, schedular)
        
        if st.session_state.awr or st.session_state.amr:
            df = aggregate_results(df)
            # must recompute (compares the average values)
            if schedular == 'Monthly':
                col = 'SPY Monthly Returns'
            elif schedular == 'Daily':
                col = 'SPY Daily Returns'
            df['Outperformed SPY'] = df.apply(compare_with_SPY, args= (col, ), axis= 1)
    else:
        st.session_state.button_clicked = False
        st.stop()

bar, _, _ = st.columns([2, 3, 3])
with bar:
    x = st.number_input('Select the no. of ISINs to display', min_value= 0, max_value= len(df), value= 20, key= 'bar1')
                                  
st.table(df.iloc[:x, ])
st.download_button(label="Download as CSV", data= df.to_csv(index=True), file_name='metrics.csv', mime='text/csv')