from config import *

def process_financial_data(df, columns, start_date, end_date):
    
    df_processed = df.copy()
    
    for col in ['queued_on', 'trained_on']:
        if col in df_processed.columns:
            df_processed[col] = pd.to_datetime(df_processed[col], errors='coerce', infer_datetime_format=True)
            mask = df_processed[col].isna()
            if mask.any():
                for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S', '%d-%m-%Y']:
                    try:
                        df_processed.loc[mask, col] = pd.to_datetime(
                            df_processed.loc[mask, col], format=fmt, errors='coerce'
                        )
                        mask = df_processed[col].isna()
                        if not mask.any():
                            break
                    except:
                        continue
                        
    mask_queued = (df_processed['queued_on'] >= start_date) & (df_processed['queued_on'] <= end_date)
    mask_trained = (df_processed['trained_on'] >= start_date) & (df_processed['trained_on'] <= end_date)
    
    df_filtered = df_processed[mask_queued | mask_trained]
    
    daily_stats = []
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    for date in date_range:
        stats = {'date': date, 'queued': 0, 'trained': 0, 'failed': 0}

        if 'queued_on' in df_processed.columns:
            stats['queued'] = len(df_processed[df_processed['queued_on'].dt.date == date.date()])
        
        if 'trained_on' in df_processed.columns:
            trained_mask = (df_processed['trained_on'].dt.date == date.date()) & (df_processed['failed'].isna() | (df_processed['failed'] == ''))
            stats['trained'] = len(df_processed[trained_mask])

        if 'failed' in df_processed.columns:
            failed_mask = (df_processed['queued_on'].dt.date == date.date()) & (df_processed['failed'].notna() & (df_processed['failed'] != ''))
            stats['failed'] = len(df_processed[failed_mask])
        
        daily_stats.append(stats)
    
    return pd.DataFrame(daily_stats)

def process_information_data(df, columns, start_date, end_date):
    df_processed = df.copy()
 
    if 'trained_at' in df_processed.columns:
        df_processed['trained_at'] = pd.to_datetime(df_processed['trained_at'], errors='coerce')

        df_processed = df_processed[
            (df_processed['trained_at'] >= start_date) & 
            (df_processed['trained_at'] <= end_date)
        ]
    
    daily_stats = []
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    for date in date_range:
        stats = {'date': date, 'queued': 0, 'trained': 0, 'failed': 0}
        
        if 'trained_at' in df_processed.columns:
            stats['trained'] = len(df_processed[df_processed['trained_at'].dt.date == date.date()])
        
        daily_stats.append(stats)
    
    return pd.DataFrame(daily_stats)

def process_er_data(df, columns, start_date, end_date):

    df_processed = df.copy()
    
    for col in ['queue_date', 'training_date']:
        if col in df_processed.columns:
            df_processed[col] = pd.to_datetime(df_processed[col], errors='coerce')
   
    if 'queue_date' in df_processed.columns:
        df_processed = df_processed[
            (df_processed['queue_date'] >= start_date) & 
            (df_processed['queue_date'] <= end_date)
        ]
    
    daily_stats = []
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    for date in date_range:
        stats = {'date': date, 'queued': 0, 'trained': 0, 'failed': 0}
        
        if 'queue_date' in df_processed.columns:
            stats['queued'] = len(df_processed[df_processed['queue_date'].dt.date == date.date()])
        
        if 'training_date' in df_processed.columns and 'status' in df_processed.columns:
            stats['trained'] = len(df_processed[
                (df_processed['training_date'].dt.date == date.date()) & 
                (df_processed['status'] != 'model_error')
            ])
        
        if 'status' in df_processed.columns:
            failed_on_date = df_processed[
                (df_processed['queue_date'].dt.date == date.date()) & 
                (df_processed['status'] == 'model_error')
            ]
            stats['failed'] = len(failed_on_date)
        
        daily_stats.append(stats)
    
    return pd.DataFrame(daily_stats)

def create_bar_chart(df, model_name, tag):
    fig = make_subplots(specs=[[{"secondary_y": False}]])
    
    fig.add_trace(
        go.Bar(
            x=df['date'],
            y=df['queued'],
            name='Queued',
            marker_color='#636EFA'
        )
    )
    
    fig.add_trace(
        go.Bar(
            x=df['date'],
            y=df['trained'],
            name='Trained',
            marker_color='#00CC96'
        )
    )
    
    fig.add_trace(
        go.Bar(
            x=df['date'],
            y=df['failed'],
            name='Failed',
            marker_color='#EF553B'
        )
    )
    
    fig.update_layout(
        title=f'{model_name.title()} Model - {tag.upper()}',
        xaxis_title='Date',
        yaxis_title='Count',
        barmode='group',
        height=400,  
        width=600, 
        showlegend=True,
        margin=dict(l=50, r=50, t=60, b=50)  
    )
    
    return fig

def get_models_for_tag(tag, scheduler="Monthly"):
    if scheduler == "Quarterly":
       
        quarterly_models = {
            "aieq": ["xgb_er"],
            "indeq": ["xgb_er"],
            "aigo": []  
        }
        return quarterly_models.get(tag, [])
    else:
        monthly_models = {
            "aieq": ["financial", "information", "er", "cat_er"],
            "indeq": ["financial", "er", "cat_er"],
            "aigo": ["financial", "er"]
        }
        return monthly_models.get(tag, [])

def load_tag_data(tag, start_date, end_date, scheduler="Monthly", year=None):

    models = get_models_for_tag(tag, scheduler)
    all_data = {}
    all_processed_data = {}
    
    for model_name in models:
        try:
            config = get_file_path_and_config(model_name, tag, year, scheduler)
            if config is None:
                continue
                
            df = load_data_from_s3(config['bucket_name'], config['filepath'])
            if df is not None:
                if model_name == "financial":
                    processed_df = process_financial_data(df, config['columns'], 
                                                       pd.Timestamp(start_date), pd.Timestamp(end_date))
                elif model_name == "information":
                    processed_df = process_information_data(df, config['columns'], 
                                                          pd.Timestamp(start_date), pd.Timestamp(end_date))
                else: 
                    processed_df = process_er_data(df, config['columns'], 
                                                 pd.Timestamp(start_date), pd.Timestamp(end_date))
                
                all_data[model_name] = df
                all_processed_data[model_name] = processed_df
                
        except Exception as e:
            st.warning(f"Could not load data for {model_name}: {str(e)}")
            continue
    
    return all_data, all_processed_data