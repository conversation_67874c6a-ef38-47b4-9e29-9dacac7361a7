import streamlit as st
import boto3
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import io
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

@st.cache_resource
def get_s3_client():
    return boto3.client(
    's3', 
    aws_access_key_id= "********************",
    aws_secret_access_key= "bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/",
    region_name='us-east-1'
)


def get_file_path_and_config(model_name, tag, year=None, scheduler="Monthly"):
    if year is None:
        year = datetime.now().year

    config = {}
    
    if model_name == "financial":
        config['bucket_name'] = "financial-model"
        config['columns'] = ["queued_on", "trained_on", "failed"]
        
        if tag == "aieq":
            config['filepath'] = 'all_data/aieq/monthly/model_training_details/training_queue.csv'
        elif tag == "indeq":
            config['filepath'] = 'all_data/india/monthly/model_training_details/training_queue.csv'
        elif tag == "aigo":
            config['filepath'] = 'all_data/aigo/monthly/model_training_details/training_queue.csv'
        else:
            return None
            
    elif model_name == "information":
        config['bucket_name'] = "micro-ops-output"
        config['filepath'] = "new_info_model/v1/live_deployment_details/lstm.csv"
        config['columns'] = ["trained_at"]
        
    elif model_name in ["er", "cat_er", "xgb_er"]:
        config['bucket_name'] = "portfolio-experiments-test"
        config['columns'] = ["queue_date", "training_date", "status"]
        
        if model_name == "cat_er":
            if tag == "aieq":
                config['filepath'] = f"aieq_historical_catboost/Monthly/{year-1}/queue_data/q_data.csv"
            elif tag == "indeq":
                config['filepath'] = f"india_historical_catboost/Monthly/{year-1}/queue_data/q_data.csv"
                
        elif model_name == "er":
            if tag == "aieq":
                config['filepath'] = f"aieq_historical/Monthly/{year-1}/queue_data/q_data.csv"
            elif tag == "aigo":
                config['filepath'] = f"aigo_historical/Monthly/{year-1}/queue_data/q_data.csv"
            elif tag == "indeq":
                config['filepath'] = f"india_historical/Monthly/{year-1}/queue_data/q_data.csv"
                
        elif model_name == "xgb_er":
            frequency = "Quarterly" if scheduler == "Quarterly" else "Monthly"
            if tag == "aieq":
                config['filepath'] = f"aieq_historical_xgboost/{frequency}/{year-1}/queue_data/q_data.csv"
            elif tag == "indeq":
                config['filepath'] = f"india_historical_xgboost/{frequency}/{year-1}/queue_data/q_data.csv"
    
    return config

@st.cache_data
def load_data_from_s3(bucket_name, filepath):
    s3 = get_s3_client()
    
    try:
        s3_response_object = s3.get_object(Bucket=bucket_name, Key=filepath)
        object_content = s3_response_object['Body'].read()
        
        if "xlsx" in filepath:
            try:
                df = pd.read_excel(io.BytesIO(object_content), engine='xlrd')
            except:
                df = pd.read_excel(io.BytesIO(object_content), engine='openpyxl')
        else:
            df = pd.read_csv(io.BytesIO(object_content))
            
        return df
    except Exception as e:
        st.error(f'Error in reading file from S3: {e}')
        return None