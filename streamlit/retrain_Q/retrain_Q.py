# MODULES
import pandas as pd
import datetime
import re
import streamlit as st
import time
import matplotlib.pyplot as plt
import plotly.express as px
from tqdm import tqdm
from io import StringIO
import boto3


# FUNCTION DEFINITIONS
@st.cache_data
def s32df(bucket_name, file_name):

    # Creates a [session & a] client
    s3_client = boto3.client('s3',\
                             aws_access_key_id= aws_access_key, \
                             aws_secret_access_key= aws_secret_key)

    # Retrieves a dictionary from S3
    csv_obj = s3_client.get_object(Bucket= bucket_name,\
                                   Key=file_name)
    
    # Extracts the "Body" [i.e., the information] & converts to a string
    csv_string = csv_obj['Body'].read().decode('utf-8')
     
    # converts the string to a DataFrame
    df = pd.read_csv( StringIO(csv_string) )
    
    return df

# Lists all files in an S3 bucket, at a specific location [ie, folder, aka prefix]
def list_files(bucket_name, prefix= ''):

    # Create a session using your AWS credentials
    session = boto3.Session(aws_access_key_id=aws_access_key,
                            aws_secret_access_key=aws_secret_key)
    
    # Create an S3 client
    s3 = session.client('s3')
    
    # List objects within the specified bucket and prefix
    response = s3.list_objects_v2(Bucket= bucket_name, Prefix= prefix)
    
    # Extract and return the filenames
    if 'Contents' in response:
        files = [obj['Key'] for obj in response['Contents']]
        return files
    else:
        return []

def show_queue(combined_df):

    fig = px.histogram(combined_df, x='queued_on', color='source', barmode='group', color_discrete_sequence= color_palette)
    fig.update_layout(xaxis_title='date', yaxis_title='count')

    return fig

def show_trained(combined_df):

    tmp = combined_df['trained_on'].notnull()

    df = combined_df[tmp]
    
    #tmp = combined_df[combined_df['trained_on'] > combined['queued_on']]

    fig = px.histogram(df, x='trained_on', color='source', barmode='group', color_discrete_sequence= color_palette)
    fig.update_layout(xaxis_title='date', yaxis_title='count')

    return fig

def show_pending1(combined_df):

    tmp = combined_df['trained_on'].isna()
    df = combined_df[tmp]

    fig = px.histogram(df, x= 'queued_on', color='source', barmode='group', color_discrete_sequence=color_palette, cumulative= True)
    fig.update_layout(xaxis_title='date', yaxis_title='count')

    return fig

def show_pending2(combined_df):
    tmp = combined_df['trained_on'].isna()
    df = combined_df[tmp]

    fig = px.histogram(df, x='queued_on', color='source', barmode='stack', color_discrete_sequence=color_palette, cumulative= True)
    fig.update_layout(xaxis_title='date', yaxis_title='count')

    return fig

def show_failed(combined_df):

    tmp = combined_df['status'].notna()
    df = combined_df[tmp]

    fig = px.histogram(df, x= 'queued_on', color='source', barmode='group', color_discrete_sequence=color_palette)
    fig.update_layout(xaxis_title='date', yaxis_title='count')

    return fig
# functions to preprocess individual data
def preprocess_ER(df):
    # These columns contain dates as strings
    cols =  ['queue_date', 'timestamp', 'training_date']

    # Converts these strings to 'datetime' objects
    format1 = '%Y-%m-%d %H:%M:%S'
    format2 = '%Y-%m-%d_%H:%M:%S'
    for i in range( len(cols) ):
        if i != 1:
            df[ cols[i] ] = pd.to_datetime(df[ cols[i] ], format= format1, errors='coerce')
        else:
            df[ cols[i] ] = pd.to_datetime(df[ cols[i] ], format= format2, errors='coerce')

    namesake = {'training_date': 'trained_on', 'queue_date': 'queued_on'}
    df.rename(namesake, axis= 1, inplace= True)

    df.drop(['timestamp'], axis= 1, inplace= True)
        # Selects those rows, for which ('training_date' > 'queued_date')
    #tmp = (df[cols[2]] > df[cols[0]])
    #df = df[tmp]
    
    return df

# functions to preprocess individual data
def preprocess_financial_1(df):
    # These columns contain dates as strings
    cols =  ['queued_on', 'trained_on']

    # Converts these strings to 'datetime' objects
    _format_ = '%Y-%m-%d'
    for col in cols:
        df[col] = pd.to_datetime(df[col], format= _format_, errors='coerce')

    namesake = {'failed': 'status'}
    df.rename(namesake, axis= 1, inplace= True)

    df.drop(['folder_location'], axis= 1, inplace= True)
    
    # Selects those rows, for which ('training_date' > 'queued_date')
    #tmp = (df[cols[2]] > df[cols[0]])
    #df = df[tmp]
    
    return df

def preprocess_financial_2(df):
    # These columns contain dates as strings
    cols =  ['queued_on', 'trained_on']

    # Converts these strings to 'datetime' objects
    _format_ = '%Y-%m-%d'
    for col in cols:
        df[col] = pd.to_datetime(df[col], format= _format_, errors='coerce')

    namesake = {'failed': 'status'}
    df.rename(namesake, axis= 1, inplace= True)

    df.drop(['folder_location'], axis= 1, inplace= True)

    # Selects those rows, for which ('training_date' > 'queued_date')
    #tmp = (df[cols[2]] > df[cols[0]])
    #df = df[tmp]

    

    return df


def preprocess_ESG (df):

    namesake = {'queue_date': 'queued_on'}
    df.rename(namesake, axis= 1, inplace= True)

    df['queued_on'] = pd.to_datetime(df['queued_on'])

    return df

def preprocess_amit(df):

    
    namesake = {'training_date': 'trained_on', 'queue_date': 'queued_on'}
    df.rename(namesake, axis= 1, inplace= True)

    df['queued_on'] = pd.to_datetime(df['queued_on'])
            
    return df

def preprocess_tanmay(df):
    df.drop(['folder_location'], axis= 1, inplace= True)
    
    namesake = {'failed':'status'}
    df.rename(namesake, axis=1, inplace= True)

    df['queued_on'] = pd.to_datetime(df['queued_on'])

    return df
    
def preprocess_INFO(df):

    df['queued_on'] = pd.to_datetime(df['queued_on'])
    #df.drop(['timestamp'], axis= 1, inplace= True)
    
    n = len(df)
    for col in df.columns:
        m = sum(df[col].isna()) 
        if m == n:
            df.drop([col], axis= 1, inplace= True)
            
    return df


# Get data
@st.cache_data
def get_etf_data():
    
    bucket = 'etf-predictions'
     
    files = [
        'Monthly/aigo/training_queue/aigo_training_queue.csv',
        'Monthly/sector/training_queue/sector_training_queue.csv',
        'Monthly/db/training_queue/db_training_queue.csv'
        ]
    
    dfs= []
    
    for file in files:
        df = s32df(bucket, file)

        df = preprocess_amit(df)
    
        dfs.append(df)

    return dfs

@st.cache_data
def get_er_AutoAI():
    bucket = 'portfolio-experiments-test'
    paths = ['Old_ER_Retrained/AIGO_monthly/model_training_details/training_queue.csv', 'Old_ER_Retrained/model_training_details/training_queue.csv']
    
    dfs = []
    for path in paths:
        df = s32df(bucket, path)
        df = preprocess_tanmay(df)
        dfs.append(df)

    return dfs

@st.cache_data    
def get_info_Q():

    def get_names(paths):
    
        pattern = r'/([^/]+)\.csv$'
        filenames = [re.search(pattern, path).group(1) for path in paths]
    
        return filenames
    
    bucket = 'micro-ops-output'
    path = 'new_info_model/v1/training_queue/'

    # determine the paths of the files
    paths = list_files(bucket, path)

    filenames = get_names(paths)

    tmp = zip(paths, filenames)

    # downloads the files
    dfs = []
    for path, filename in tmp:
        df = s32df(bucket, path)
        df['year'] = int(filename)
        dfs.append(df)

    

    # Collects all dataframes into 1
    df = pd.concat(dfs)
    
    namesake = {'trained_at': 'trained_on', 'queue_date': 'queued_on', 'remarks':'status'}
    df.rename(namesake, axis= 1, inplace= True)

    df.reset_index(inplace= True, drop= True)

    return df

# GLOBAL VARIABLES
st.set_page_config(layout= "wide")
date_format = "%d-%b" #"%d-%m-%Y"
color_palette = px.colors.qualitative.Plotly

# MAIN

# 1 GET DATA
aws_access_key = '********************'
aws_secret_key = 'bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'
s3 = boto3.client('s3', aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key)

buckets = []
financial_1  = ['financial-model']
financial_2 = ['financial-model-data-collection'] # 'financial-model', 
ER= ['portfolio-experiments-test']
ESG  = ['micro-ops-output']
buckets.extend(financial_1)
buckets.extend(financial_2)
buckets.extend(ER)
buckets.extend(ESG )
 
files = []
financial_1 = ['queue_details/aieq/monthly/training_queue.csv']
financial_2 = ['aieqMonthly_model_training_details/training_queue.csv'] # 'queue_details/aieq/monthly/training_queue.csv', 
ER = ['aieq_historical/Monthly/2023/queue_data/q_data.csv']
ESG  = ['macro-historical-data/training_queue/macro_training_queue.csv']
files.extend(financial_1)
files.extend(financial_2)
files.extend(ER)
files.extend(ESG )


tmp = zip(buckets, files)

dfs= []

s = time.time()
for i, (bucket, file) in tqdm(enumerate(tmp)):
    # read
    df = s32df(bucket, file)
    # store
    dfs.append(df)

dfs.extend(get_etf_data())
dfs.extend(get_er_AutoAI())
dfs.append(get_info_Q())
e = time.time()

st.text(f'Time req. to get_data: {round(e-s)} sec')

# 2 PREPROCESS DATA

model_names = ['financial_1', 'financial_2', 'ER', 'ESG ', 'ETF_AIGO', 'ETF_Sector', 'ETF_DB', 'ETF_AIGO_2', 'BNP', 'INFO']

dfs[0] = preprocess_financial_1(dfs[0])
dfs[1] = preprocess_financial_2(dfs[1])
dfs[2] = preprocess_ER(dfs[2])
dfs[3] = preprocess_ESG (dfs[3])
dfs[-1] = preprocess_INFO(dfs[-1])

# 3 CONCATENATE THE DATA

tmp = [df.assign(source= model_names[i]) for i, df in enumerate(dfs)]
combined_df = pd.concat(tmp, axis=0)

combined_df['queued_on'] = combined_df['queued_on'].apply(lambda x: x.date())

# 4 SELECT DATA TO VIEW

tmp1 = combined_df['queued_on'].apply(lambda date: date.year)
tmp2 = combined_df['queued_on'].apply(lambda date: date.month)
combined_df = combined_df[(tmp1 > 2023) & (tmp2 > 3)]

# 5 PLOT THE CHARTS

fig1 = show_queue(combined_df)
st.plotly_chart(fig1)

fig2 = show_trained(combined_df)
st.plotly_chart(fig2)

fig3 = show_pending1(combined_df)
st.plotly_chart(fig3)

fig4 = show_pending2(combined_df)
st.plotly_chart(fig4)

fig5 = show_failed(combined_df)
st.plotly_chart(fig5)
