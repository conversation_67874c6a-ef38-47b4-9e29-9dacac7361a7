from utilities import *


def main():
    st.set_page_config(
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    st.markdown("""
    <style>
    /* Ensure full width container */
    .main .block-container {
        max-width: 100% !important;
        padding-left: 2rem !important;
        padding-right: 2rem !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    
    col1, spacer1, col2 = st.columns([4, 1, 4])
    
    with col1:
        st.markdown("<h6 style='text-align: center;'>Start date</h6>", unsafe_allow_html=True)
        start_col1, start_col2, start_col3 = st.columns(3)
        with start_col1:
            start_day = st.selectbox("Select Day", range(1, 32), index=29, key="start_day")
        with start_col2:
            start_month = st.selectbox("Month", ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],index=4, key="start_month")
        with start_col3:
            start_year = st.selectbox("Year", range(2020, 2026), index=5, key="start_year")
    
    with col2:
        st.markdown("<h6 style='text-align: center;'>End date</h6>", unsafe_allow_html=True)
        end_col1, end_col2, end_col3 = st.columns(3)
        with end_col1:
            end_day = st.selectbox("Day", range(1, 32), index=5, key="end_day")
        with end_col2:
            end_month = st.selectbox("Month", 
                                   ["Jan", "Feb", "Mar", "Apr", "May", "Jun", 
                                    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], 
                                   index=5, key="end_month")
        with end_col3:
            end_year = st.selectbox("Year", range(2020, 2026), index=5, key="end_year")

    st.markdown("")
    st.markdown("")
    
    _, scheduler_tag_col, _ = st.columns([1, 2, 1])
    with scheduler_tag_col:
        sched_col, tag_col = st.columns(2)
        
        with sched_col:
            selected_scheduler = st.selectbox("Choose Horizon:", ["Monthly", "Quarterly"], 
                                            key="scheduler", help="Select the scheduler frequency")
        
        with tag_col:
            selected_tag = st.selectbox("Choose a tag:", ["aieq", "indeq", "aigo"], 
                                       key="tag", help="Select the model tag to display")
    
    available_models = get_models_for_tag(selected_tag, selected_scheduler)
    month_map = {"Jan": 1, "Feb": 2, "Mar": 3, "Apr": 4, "May": 5, "Jun": 6,
                 "Jul": 7, "Aug": 8, "Sep": 9, "Oct": 10, "Nov": 11, "Dec": 12}
    try:
        start_date = datetime(start_year, month_map[start_month], start_day).date()
        end_date = datetime(end_year, month_map[end_month], end_day).date()
    except ValueError:
        st.error("Invalid date selection. Please check your date inputs.")
        return
        
    year = 2024
    
    _, button_col, _ = st.columns([2, 1, 2])
    with button_col:
        load_data_button = st.button("Go", use_container_width=True)
    
    if load_data_button:
        if not available_models:
            st.error(f"No models available for {selected_tag} with {selected_scheduler} scheduler")
            return
            
        with st.spinner(f"Loading {selected_scheduler} data for {selected_tag} models..."):
            all_data, all_processed_data = load_tag_data(selected_tag, start_date, end_date, selected_scheduler, year)
        
        if all_processed_data:
            model_names = list(all_processed_data.keys())
            
            for i in range(0, len(model_names), 2):
                if i + 1 < len(model_names):
                    chart_col1, chart_col2 = st.columns(2)
                    
                    with chart_col1:
                        model_name = model_names[i]
                        processed_df = all_processed_data[model_name]
                        fig = create_bar_chart(processed_df, model_name, selected_tag)
                        st.plotly_chart(fig, use_container_width=True)
                    
                    with chart_col2:
                        model_name = model_names[i + 1]
                        processed_df = all_processed_data[model_name]
                        fig = create_bar_chart(processed_df, model_name, selected_tag)
                        st.plotly_chart(fig, use_container_width=True)
                else:
                    model_name = model_names[i]
                    processed_df = all_processed_data[model_name]
                    fig = create_bar_chart(processed_df, model_name, selected_tag)
                    st.plotly_chart(fig, use_container_width=True)
                st.markdown("")
        
        else:
            st.error("No data could be loaded. Please check your date range and try again.")

if __name__ == "__main__":
    main()