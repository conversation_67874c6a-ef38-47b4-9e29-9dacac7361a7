# 🔧 **Git Repository Setup Guide for DS_Utils**

## 🚨 **Current Issue**

The Git repository at `https://github.com/EqubotAI/DS_Utils.git` exists but is not yet configured as an installable Python package. It needs a `setup.py` or `pyproject.toml` file to be pip-installable.

## ✅ **Solution: Setup DS_Utils as Installable Package**

### **1. Required Files for Git Repository**

The DS_Utils repository needs these files to be pip-installable:

#### **setup.py** (Recommended)
```python
"""
Setup script for DS_Utils - Shared Utilities Package
"""

from setuptools import setup, find_packages
import os

# Read the README file for long description
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "DS_Utils - Shared Utilities for Data Science Inference Scripts"

# Read requirements
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="shared-utils",
    version="1.0.0",
    author="EqubotAI Data Science Team",
    author_email="<EMAIL>",
    description="Shared utilities package for DS-Inference-Scripts",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/EqubotAI/DS_Utils",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=1.0.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.10.0",
        ],
    },
    include_package_data=True,
    package_data={
        "shared_utils": ["*.txt", "*.yaml", "*.yml", "*.json"],
    },
    entry_points={
        "console_scripts": [
            "ds-utils-test=shared_utils.cli:test_utilities",
        ],
    },
    keywords="data-science, utilities, configuration, logging, email, s3, error-handling",
    project_urls={
        "Bug Reports": "https://github.com/EqubotAI/DS_Utils/issues",
        "Source": "https://github.com/EqubotAI/DS_Utils",
        "Documentation": "https://github.com/EqubotAI/DS_Utils/wiki",
    },
)
```

#### **requirements.txt**
```txt
# Core dependencies for shared_utils package
PyYAML>=6.0.2
pandas>=2.1.4
numpy>=1.24.0
configparser>=7.0.0
boto3>=1.34.137
aws-requests-auth>=0.4.3
google-api-python-client>=2.166.0
google-auth>=2.40.3
google-auth-httplib2>=0.2.0
googleapis-common-protos>=1.70.0
logging>=*******
typing-extensions>=4.8.0
expiringdict>=1.2.2
pandas-market-calendars>=4.3.0
openpyxl>=3.1.0
eq-common-utils@git+https://<EMAIL>/EqubotAI/eq-common-utils.git@v0.14
```

#### **MANIFEST.in**
```txt
include README.md
include requirements.txt
include LICENSE
recursive-include shared_utils *.py
recursive-include shared_utils *.yaml
recursive-include shared_utils *.yml
recursive-include shared_utils *.json
recursive-include shared_utils *.txt
```

#### **README.md**
```markdown
# DS_Utils - Shared Utilities Package

Shared utilities package for DS-Inference-Scripts providing common functionality for:

- Configuration management
- Logging utilities
- Email operations
- S3 operations
- Error handling framework

## Installation

```bash
pip install git+https://github.com/EqubotAI/DS_Utils.git
```

## Usage

```python
from shared_utils import (
    load_config, create_model_logger, create_email_sender,
    create_s3_manager, create_error_handler, ErrorSeverity
)
```
```

### **2. Expected Repository Structure**

```
DS_Utils/
├── setup.py                    # Package setup configuration
├── requirements.txt            # Package dependencies
├── MANIFEST.in                 # Package manifest
├── README.md                   # Package documentation
├── LICENSE                     # License file
│
└── shared_utils/               # Main package directory
    ├── __init__.py            # Package initialization
    ├── config_utils.py        # Configuration utilities
    ├── logging_utils.py       # Logging utilities
    ├── email_utils.py         # Email utilities
    ├── s3_utils.py           # S3 utilities
    ├── error_handling.py     # Error handling
    └── cli.py                # CLI interface
```

## 🔄 **Temporary Workaround**

Until the Git repository is properly set up, you can use one of these approaches:

### **Option 1: Local Installation**
```bash
# Clone the repository
git clone https://github.com/EqubotAI/DS_Utils.git
cd DS_Utils

# Add the setup.py file (copy from above)
# Then install in development mode
pip install -e .
```

### **Option 2: Direct Path Installation**
```bash
# Clone and add to Python path
git clone https://github.com/EqubotAI/DS_Utils.git
export PYTHONPATH="${PYTHONPATH}:/path/to/DS_Utils"
```

### **Option 3: Update Requirements**
Update `requirements.txt` to comment out the Git package temporarily:
```txt
# Shared utilities package (install manually until Git repo is configured)
# shared-utils @ git+https://github.com/EqubotAI/DS_Utils.git
```

## 🚀 **Next Steps**

1. **Add setup.py to DS_Utils repository**
2. **Add requirements.txt with dependencies**
3. **Add MANIFEST.in for package files**
4. **Test installation**: `pip install git+https://github.com/EqubotAI/DS_Utils.git`
5. **Update DS-Inference-Scripts**: Once working, use the Git package

## 📞 **Support**

Once the repository is properly configured with setup.py, the installation will work seamlessly:

```bash
# This will work once setup.py is added to the repository
pip install git+https://github.com/EqubotAI/DS_Utils.git

# Test the installation
python -c "import shared_utils; print('✅ shared_utils working!')"
```

The DS-Inference-Scripts codebase is already updated and ready to use the Git package once it's properly configured!
