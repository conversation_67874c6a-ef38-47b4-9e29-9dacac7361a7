# Shared Utils Package Installation Guide

This guide explains how to install and use the `ds-inference-shared-utils` package that has been created from the shared_utils folder.

## 📦 Package Overview

The shared utilities have been packaged into a proper Python package with the following structure:
```
shared_utils/
├── __init__.py          # Package initialization with convenient imports
├── setup.py             # Package setup configuration
├── pyproject.toml       # Modern Python packaging configuration
├── requirements.txt     # Package dependencies
├── README.md           # Package documentation
├── MANIFEST.in         # Files to include in package
├── config_utils.py     # Configuration management utilities
├── logging_utils.py    # Logging framework
├── email_utils.py      # Email operations
└── error_handling.py   # Error handling framework
```

## 🚀 Installation Methods

### Method 1: Development Installation (Recommended)
This method installs the package in "editable" mode, so changes to the source code are immediately reflected.

```bash
# Navigate to the shared_utils directory
cd shared_utils

# Install in development mode
pip install -e .
```

### Method 2: Standard Installation
```bash
# Navigate to the shared_utils directory
cd shared_utils

# Install normally
pip install .
```

### Method 3: Install from Git Repository (Future)
Once the repository is properly set up, you can install directly from Git:
```bash
pip install git+https://github.com/EqubotAI/DS-Inference-Scripts.git#subdirectory=shared_utils
```

## 📋 Prerequisites

- Python >= 3.8
- pip (Python package installer)
- All dependencies will be automatically installed from requirements.txt

## 🔧 Usage After Installation

### Before (Old Method)
```python
# Old way with sys.path manipulation
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / 'shared_utils'))

from shared_utils.config_utils import load_config
from shared_utils.logging_utils import create_model_logger
from shared_utils.email_utils import create_email_sender
from shared_utils.error_handling import create_error_handler
```

### After (New Package Method)
```python
# New way with proper package imports
from shared_utils import (
    load_config, create_model_logger, create_email_sender, create_error_handler,
    ErrorSeverity, ModelError, ConfigurationError
)

# Or import specific modules
from shared_utils import config_utils, logging_utils, email_utils, error_handling
```

## 🧪 Verification

To verify the installation worked correctly:

```python
# Test basic import
try:
    import shared_utils
    print(f"✅ Shared utils installed successfully! Version: {shared_utils.__version__}")
except ImportError as e:
    print(f"❌ Installation failed: {e}")

# Test specific imports
try:
    from shared_utils import load_config, create_model_logger
    print("✅ Core functions imported successfully!")
except ImportError as e:
    print(f"❌ Import failed: {e}")
```

## 🔄 Migration Steps

### For Existing Scripts

1. **Remove sys.path manipulation**:
   ```python
   # Remove these lines
   import sys
   from pathlib import Path
   sys.path.insert(0, str(Path(__file__).parent.parent / 'shared_utils'))
   ```

2. **Update imports**:
   ```python
   # Replace individual imports with package imports
   from shared_utils import (
       load_config, create_model_logger, create_email_sender,
       create_error_handler, ErrorSeverity, ModelError
   )
   ```

3. **Test the script** to ensure it works with the new imports.

### Scripts Already Updated

The following scripts have been updated to use the new package imports:
- `best_model_scripts/main.py`
- `best_model_scripts/helper_functions.py`
- `best_model_scripts/email_notification.py`
- `etf_model_scripts/etf_models_trigger_main_refactored.py`
- `etf_model_scripts/deliveryscripts/unified_delivery_manager.py`
- `management_model_scripts/management_model_daily_run.py`
- `lstm_model_scripts/lstm_model_daily_run.py`
- `fin_model_scripts/Financial_Model_Prediction_Script.py`
- `ttm_model_scripts/tsfm/trigger.py`
- `info_model_scripts/data_collection/lexisnexis.py`

## 🐛 Troubleshooting

### Common Issues

1. **ImportError: No module named 'shared_utils'**
   - Ensure you've installed the package: `pip install -e shared_utils/`
   - Check your Python environment is correct

2. **ModuleNotFoundError for dependencies**
   - Install dependencies: `pip install -r shared_utils/requirements.txt`
   - Or reinstall the package: `pip install -e shared_utils/`

3. **Permission errors during installation**
   - Use virtual environment: `python -m venv venv && source venv/bin/activate`
   - Or install with user flag: `pip install --user -e shared_utils/`

### Verification Commands

```bash
# Check if package is installed
pip list | grep ds-inference-shared-utils

# Check package location
python -c "import shared_utils; print(shared_utils.__file__)"

# Test import in Python
python -c "from shared_utils import load_config; print('Success!')"
```

## 📚 Next Steps

1. Install the package using one of the methods above
2. Test the installation with the verification steps
3. Update any remaining scripts that still use sys.path manipulation
4. Consider setting up automated testing for the package

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all prerequisites are met
3. Create an issue in the repository with error details
