ibm:
  api_key: XHCG_3TowFlKr2DExm75l41dcnaNnFGwHEvWaKI74MCv
  location: eu-de
  url: https://{location}.ml.cloud.ibm.com
  crn: "crn:v1:bluemix:public:pm-20:{location}:a/4f872a4264a50cbd6da50add5eb35fd8:8e2ee632-8bd2-4e10-8f3d-17e4f1503b52::"

s3:
  bucket_name: micro-ops-output
  input_temp_folder: risk_model/etf_volatility/temporary_data/
  prediction_temp_folder: risk_model/etf_volatility/historical_predictions_new/
  prediction_folder: risk_model/etf_volatility/historical_predictions/
  metrics_temp_folder: risk_model/etf_volatility/historical_metrics_new/
  metrics_folder: risk_model/etf_volatility/historical_metrics/

  model_details:
    deployment_details: risk_model/etf_volatility/training_details/deployment_details.csv
    queued_file: risk_model/etf_volatility/training_details/training_queue.csv
    space_details: risk_model/etf_volatility/training_details/training_space.csv

schedular2days:
  quarterly: 66
  monthly: 22
  weekly: 5
  daily: 1

gmail_creds:
  bucket: etf-predictions
  folder: preportfolio/gmail_credentials/credentials.json
  scopes: [https://www.googleapis.com/auth/gmail.send]
  to_mail_ids:
    trigger_mail: [<EMAIL>, <EMAIL>]
    success_mail: [<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>]
    error_mail: [<EMAIL>, <EMAIL>]

common:
  etfs: [GLD, SHY, SPY, TLT, XLB, XLC, XLE, XLF, XLI, XLK, XLRE, XLU, XLV, XLY, XME]
  target_column: actual_monthly_volatility
  prediction_column: predictions
  prediction_batch_size: 3
  customer_index_data_etf: 
    etf: DBEEUNGF
    columns: [date, bci_usa, cci_usa, cli_usa]
  sector_mapping: 
    XLC: communication_services
    XLE: energy
    XLF: financials
    XLV: health_care
    XLI: industrials
    XLB: materials
    XLRE: real_estate
    XLK: information_technology
    XLU: utility
    XME: materials
    XLY: consumer_discretionary
    GLD: macro_bond
    SHY: macro_bond
    TLT: macro_bond
    SPY: all_inds
  etf2isin:
    GLD: US78463V1070
    SHY: US4642874576
    SPY: US78462F1030
    TLT: US4642874329
    XLB: US81369Y1001
    XLC: US81369Y8527
    XLE: US81369Y5069
    XLF: US81369Y6059
    XLI: US81369Y7040
    XLK: US81369Y8030
    XLRE: US81369Y8600
    XLU: US81369Y8865
    XLV: US81369Y2090
    XLY: US81369Y4070
    XME: US78464A7550
  column_mapping: 
    common_columns: [ADX, APO, CCI, DX, MACDFIX_0, MACDFIX_1, MACDFIX_2, MACD_0, MACD_1, MACD_2, MFI, MOM, PPO, STOCHF_0, STOCHF_1, STOCHRSI_0, STOCHRSI_1, STOCH_0, STOCH_1, ULTOSC, WILLR, close_price, daily_close_change, daily_volume_change, date, etf_kw_neg, etf_kw_pos, isin, lstm_day1, lstm_day2, lstm_day3, lstm_day4, lstm_day5, lstm_day6, lstm_day7, marketcap, monthly_close_change, monthly_volume_change, mp1d, mp1m, mp1q, mp1w, mp1y, mp6m, quarterly_close_change, quarterly_volume_change, rsi_div_adj, tic, unemployment_rate, us_cpi_pct_change, us_gdp_pct_change, us_ppi_pct_change, vol_1yr, vol_2yr, vol_3mth, vol_5yr, vol_6mth, volume, weekly_close_change, weekly_volume_change, year_high, year_low]
    partial_columns:
      GLD: [treasury_1mo, treasury_2mo, treasury_3mo, treasury_4mo, treasury_6mo, treasury_1yr, treasury_2yr, treasury_3yr, treasury_5yr, treasury_7yr, treasury_10yr, treasury_20yr, treasury_30yr, yield_maturity, opinion_survey, inflation_rate, cboe_gold_etf_volatility_index, personal_consumption_expenditure, macro_pred_usa_equity, gold_commodity_close_price, gld_futures_price, Production_Mine, Production_Refinery_Primary, Production_Refinery_Secondary, Imports, Exports, Consumption_reported, Stocks_yearend_Treasury, Price_dollars_per_ounce, Employment_mine_and_mill, Net_import_percent]
      SHY: [dividend_yield, treasury_4mo, treasury_6mo, treasury_1yr, treasury_2yr, treasury_3yr, treasury_spread_10yr2yr, breakeven_inflation, fwd_inflation, ep_uncertainty_index, yield_maturity, opinion_survey, inflation_rate, treasury_change, payems, reporate, macro_pred_usa_bond, spx, snp_US_Treasury_Bond_1_3_Year]
      TLT: [dividend_yield, treasury_20yr, treasury_30yr, treasury_spread_10yr2yr, breakeven_inflation, fwd_inflation, ep_uncertainty_index, yield_maturity, opinion_survey, inflation_rate, treasury_change, payems, reporate, macro_pred_usa_bond, spx]
      remaining: [dividend_yield, Mscore, holdings_kw_neg, gdp_forecasted, treasury_spread_10yr2yr, ep_uncertainty_index, Fscore, future_fedfund_rate, pe_excl_rolledup, fwd_inflation, breakeven_inflation, shares_outstanding, spx, cboe_volatility_index, macro_kw_pos, ERscore, us_corporate_effective_yield, price_sales_rolledup, pbv_rolledup, beta_1yr, daily_value_traded, beta_2yr, macro_kw_neg, Iscore, holdings_kw_pos, total_rev_rolledup, federal_funds_interest_rate, macro_pred_usa_equity, treasury_10yr, treasury_1mo, treasury_1yr, treasury_20yr, treasury_2mo, treasury_2yr, treasury_30yr, treasury_3mo, treasury_3yr, treasury_4mo, treasury_5yr, treasury_6mo, treasury_7yr]
    extra_columns:
      XLE: [global_price_index, cpi_commodity_usa, wti_prices, ixe, crude_oil_futures_price, natural_gas_futures_price]
    ignore_columns:
      etfs: [GLD, XME]
      columns: [dji]
    customer_index: [bci_usa, cci_usa, cli_usa] 
  merge_on: 
    space_deployment: [deployment_space, deployment_id]
    location_year: [input_location, year]
  final_results_columns: [input_location, year, predictions]
  deployment_details_columns: [name, year, deployment_id, space_id]
  deployment_2_submit_columns: 
    column_list: [space_id, deployment_id, year, input_location]
    rename_dict: 
      space_id: deployment_space
  prediction_max_allowed_time: 3
  failed_max_retries: 3
  default_schedular: Monthly
  model_name: etf_risk_model
  es_index_prefix:
    predictions: eq_etf_risk_model
    metrics: eq_etf_risk_model_metrics
  metrics_period: 22
  metrics_list: [mean_absolute_error, mean_squared_error, root_mean_squared_error, r2_score, adjusted_r2_score, total_perc_diff, abs_total_diff, total_variance_perc_diff, abs_total_variance_perc_diff, correlation_score, directionality_score, mean_directionality, accuracy_14_day, accuracy_22_day, accuracy_1_day, confidence_score, avg_confidence_score] 
  metrics_basic_columns: [date, isin, actual_returns, predictions]
  metrics_rename_columns:
    isin: etf
    actual_returns: actual_volatility
  temporary_folder: static
  diff: 0.0001 # Accurate upto 4 decimal places
  tab_spaces: 8
