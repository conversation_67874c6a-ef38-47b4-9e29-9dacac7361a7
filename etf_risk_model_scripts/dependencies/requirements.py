# Python script to install the dependencies

import subprocess
import sys
import os

class packageInstaller():
    def __init__(self, filepath='requirement.txt'):
        self.filepath = filepath
        self.failed_packages = []
        self.installed_count = 0

    def install_package(self, package):
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"Successfully installed: {package}")
            self.installed_count += 1
        except subprocess.CalledProcessError as e:
            print(f"Failed to install {package}. Error: {e}")
            self.failed_packages.append({'package': package, 'error': {e}})
        except Exception as e:
            print(f"Unexpected error while installing {package}: {e}")
            self.failed_packages.append({'package': package, 'error': {e}})

    def read_requirements(self):
        abspath = '/'.join(os.path.abspath(__file__).split('/')[:-1])
        os.chdir(abspath)
        if not os.path.exists(self.filepath):
            print(f"Error: {self.filepath} not found.")
            return []
        with open(self.filepath, "r") as file:
            lines = file.readlines()
        requirements = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith("#"): # Ignore comments
                requirements.append(line)
        return requirements

    def install_dependencies(self):
        print(f'Reading {self.filepath.split('/')[-1]} file.')
        requirements = self.read_requirements()

        if not requirements:
            print(f'No {self.filepath.split('/')[-1]} found or file is empty.')
            return
        for pkg in requirements:
            self.install_package(pkg)
        return self.failed_packages, self.installed_count


if __name__ == '__main__':
    filename = 'requirements.txt'
    pkg_inst = packageInstaller(filename)
    results, n_installed = pkg_inst.install_dependencies()
    if n_installed == 0:
        print('Not able to install any package.')
    else:
        print(f'Packages installed {n_installed}/{n_installed + len(results)}.')
    if results is not None:
        print('Failed packages:')
        print(results)