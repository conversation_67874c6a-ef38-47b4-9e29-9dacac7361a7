import pandas as pd
from pandas.tseries.offsets import BDay
import numpy as np
import os
import sys
import warnings
import time
import datetime
import traceback
from helper_functions import Helpers, get_volatility, predictionHelper
from email_notification import AnalysisNotification
warnings.filterwarnings('ignore')


def trigger_prediction_run(prediction_input_deployments, pred_column, helper_object, bucket_name, prediction_folder, wml_credentials, max_allowed_time = 5, max_retries = 2):
    n_retry = 0
    all_final_results_list = []
    while(n_retry <= max_retries):
        if len(prediction_input_deployments) == 0:
            print('No deployments to fetch predictions for.')
            return
        print('Deployments to submit shape:', prediction_input_deployments.shape)
        ph = predictionHelper(helper_object, wml_credentials)
        start = time.time()
        prediction_results = ph.trigger_predict(prediction_input_deployments, max_run_duration = max_allowed_time * 60)
        end = time.time()
        print('Total time taken for the prediction jobs to run', round((end-start)/60, 2),'mins.')
        merge_columns = helper_object.config['common']['merge_on']
        final_results_columns = helper_object.config['common']['final_results_columns']
        final_results = pd.merge(prediction_results, prediction_input_deployments,  how='left', left_on=merge_columns['space_deployment'], right_on=merge_columns['space_deployment'])[final_results_columns]
        all_final_results_list.append(final_results[final_results[pred_column].notna()])
        final_results = final_results[final_results[pred_column].isna()].reset_index(drop = True)
        if len(final_results) != 0:
            n_retry += 1
            print(f'Retry #{n_retry}.')
            prediction_input_deployments = pd.merge(final_results.drop(columns=[pred_column]), prediction_input_deployments, on=merge_columns['location_year'], how='left').reset_index(drop = True)
        else:
            print("Prediction jobs completed for all deployments to submit.")
            break
    all_final_results = pd.concat(all_final_results_list, axis=0).reset_index(drop = True)
    faulty_isins = []
    for file_location, df in all_final_results.groupby('input_location'):
        # combine predictions with input data
        data = helper_object.read_input_data(file_location, True)
        data['date'] = pd.to_datetime(data['date'])
        data[pred_column] = np.nan
        for year in df['year']:
            try:
                data.loc[(data['date'] >= pd.to_datetime(f'01-01-{int(year) + 1}')) & (data['date'] <= pd.to_datetime(f'31-12-{int(year) + 1}')), pred_column] = df.loc[df['year'] == year, pred_column].values[0] 
            except Exception as e:
                print('Issue:', e)
                continue
        # save data to s3 
        isin = file_location.split('/')[-1].split('.')[0]
        if data[pred_column].notna().sum() == 0:
            faulty_isins.append(isin)
            continue
        filename = os.path.join(prediction_folder, file_location.split('/')[-1])
        helper_object.s3conf.write_advanced_as_df(data, bucket_name, filename)
        print(f"File {file_location.split('/')[-1]} saved to s3.")


def main(today, schedular):
    hf = Helpers(today, schedular)
    # Keys and location variables assignment
    api_key = hf.config['ibm']['api_key'] # api key for ibm-api-key-30-05-23
    location = hf.config['ibm']['location']
    url = hf.config['ibm']['url']
    wml_credentials = {
        "apikey": api_key,
        "url": url.format(location=location)
    }
    crn = hf.config['ibm']['crn'].format(location=location)
    s3_dep_details_loc = hf.config['s3']['model_details']['deployment_details']
    s3_queued_file_loc = hf.config['s3']['model_details']['queued_file']
    s3_default_space_id_loc = hf.config['s3']['model_details']['space_details']
    bucket_name = hf.config['s3']['bucket_name']
    input_folder = hf.config['s3']['input_temp_folder']
    prediction_folder = hf.config['s3']['prediction_temp_folder']
    original_prediction_folder = hf.config['s3']['prediction_folder']
    metrics_folder = hf.config['s3']['metrics_temp_folder']
    original_metrics_folder = hf.config['s3']['metrics_folder']
    target_column = hf.target_column
    prediction_column = hf.prediction_column
    customer_index_data_etf = hf.config['common']['customer_index_data_etf']['etf']
    customer_index_data = hf.get_etf_data(customer_index_data_etf)[hf.config['common']['customer_index_data_etf']['columns']]
    customer_index_data['date'] = pd.to_datetime(customer_index_data['date'])
    customer_index_data.drop_duplicates(subset=['date'], inplace=True)
    period = hf.period
    start_date, end_date = hf.get_start_end_dates()

    ### Prediction Run ###

    # Prepare the input data for inference
    print("Start and End Dates:", str(start_date), str(end_date))
    temp_data_path = os.path.join(input_folder, '{etf}.csv')
    for etf in hf.etf_list:
        print(f'Preparing data for ETF: {etf}')
        all_cols = hf.get_col_list(etf) + [target_column]
        df = hf.get_etf_data(etf)
        df['date'] = pd.to_datetime(df['date'])
        df = pd.merge(df, customer_index_data, on = 'date', how = 'left')
        initial_cols = list(set(all_cols).intersection(set(df.columns)))
        df = df[initial_cols]
        df = df.drop_duplicates(subset = ['date']).sort_values(by = ['date']).reset_index(drop = True)
        # Correlation columns
        corr_cols = [hf.correlation_col_prefix + x for x in df.columns if hf.correlation_col_suffix in x]
        for col in corr_cols:
            sent_col = col[len(hf.correlation_col_prefix):]
            df[col] = np.nan
            for i in range(period, len(df)):
                df.loc[i, col] = df.loc[df.index[i - period: i], sent_col].corr(df.loc[df.index[i - period: i], f'{hf.schedular.lower()}_close_change'])
        df = df.ffill().fillna(0)
        df = df.set_index('date')
        df[target_column] = get_volatility(df[['close_price']], period)
        df.reset_index(inplace = True)
        df[target_column] = df[target_column].shift(-period)
        df = df[(df['date'] <= pd.to_datetime(end_date)) & (df['date'] >= pd.to_datetime(start_date))][all_cols].reset_index(drop=True)
        # Save the input data file to temporary s3 folder
        hf.s3conf.write_advanced_as_df(df, bucket_name, temp_data_path.format(etf=etf))
    # Create the deployment file to fetch the predictions from IBM
    def _create_deployment_file(year):
        deployment_df = hf.s3conf.read_as_dataframe(bucket_name, s3_dep_details_loc)
        deployment_df = deployment_df[(deployment_df['year'] == year) & (deployment_df['expiration_date'].isna())][hf.config['common']['deployment_details_columns']]
        deployment_df['input_location'] = deployment_df['name'].apply(lambda x: f"s3://{bucket_name}/{input_folder}{x}.csv")
        deployment_toSubmit = deployment_df[hf.config['common']['deployment_2_submit_columns']['column_list']].rename(columns=hf.config['common']['deployment_2_submit_columns']['rename_dict'])
        return deployment_toSubmit
    model_year = hf.run_date.year - 1
    deployment_toSubmit = _create_deployment_file(model_year)
    print('Deployment to submit dataframe shape:', deployment_toSubmit.shape)
    # Get the prediction data in batches
    unique_files = list({x.split('/')[-1] for x in deployment_toSubmit['input_location']})
    print("Number of unqiue files:", len(unique_files))
    n_batches = hf.config['common']['prediction_batch_size']
    batch_size = int(len(unique_files) / n_batches) + 1
    max_allowed_time = hf.config['common']['prediction_max_allowed_time']
    max_retries = hf.config['common']['failed_max_retries']
    # Run the batch prediction jobs
    for k in range(n_batches):
        print('Batch #', k + 1)
        current_files = unique_files[batch_size * k: min(batch_size * (k + 1), len(unique_files))]
        current_deployment_toSubmit = deployment_toSubmit[[x.split('/')[-1] in current_files for x in deployment_toSubmit['input_location']]].reset_index(drop=True)
        trigger_prediction_run(current_deployment_toSubmit, prediction_column, hf, bucket_name, prediction_folder, wml_credentials, max_allowed_time, max_retries)
    # Merge predictions to the original path
    for etf in hf.etf_list:
        csv = etf + '.csv'
        file = os.path.join(prediction_folder, csv)
        original_file = os.path.join(original_prediction_folder, csv)
        try:
            df = hf.s3conf.read_as_dataframe(bucket_name, file)
        except:
            print("No prediction found for etf", etf)
        og_df = hf.s3conf.read_as_dataframe(bucket_name, original_file)
        og_df = pd.concat([og_df, df[og_df.columns]], axis=0).drop_duplicates(subset=['date'], keep='last').sort_values(by=['date']).reset_index(drop=True).ffill().fillna(0) 
        # Recalculate the target column before uploding the historical data
        og_df['date'] = pd.to_datetime(og_df['date'])
        og_df = og_df.set_index('date')
        og_df[target_column] = get_volatility(og_df[['close_price']], period)
        og_df.reset_index(inplace = True)
        og_df[target_column] = og_df[target_column].shift(-period)
        hf.s3conf.write_advanced_as_df(og_df, bucket_name, original_file)
        print("Data updated in historical file for etf:", etf)
    # Delete the temporary files from s3
    def _delete_folder_data(bucket_name, folder_name):
        filelist = [path_obj['Key'] for path_obj in hf.s3conf.get_objects_in_range(bucket_name, folder_name)]
        if filelist != 0:
            for file in filelist:
                hf.delete_s3_file(bucket_name, file)
            print(f"Files deleted from", folder_name.split('/')[-2], ":", len(filelist))
    _delete_folder_data(bucket_name, prediction_folder)
    _delete_folder_data(bucket_name, input_folder)
    # Write prediction data to ES and S3 Versioned
    hf.save_es_n_vs3(bucket_name, s3_dep_details_loc, original_prediction_folder, model_year, 'predictions')

    ### Metrics Run ###

    # Calculate the feature mapping
    feature_mapping = {}
    for etf in hf.config['common']['sector_mapping'].keys():
        n_features = len(hf.get_col_list(etf))
        feature_mapping[etf] = n_features
    # Metrics calculation input parameters
    years = [today.year -2, today.year] # years to fetch data for
    data_source = 's3'
    parallel_workers = 120
    n_etf_features = feature_mapping # number of input features in etf volatility models
    # Fetch the data
    data_dict = hf.read_data_parallely(hf.etf_list,
                                read_from = data_source,
                                pred_col = prediction_column,
                                n_workers = parallel_workers,
                                bucket_name = bucket_name,
                                path_loc = original_prediction_folder,
                                schedular = schedular,
                                actual_column = target_column)
    # Calculate the metrics
    for etf in data_dict.keys():  
        df = data_dict[etf]
        if len(df) == 0:
            print(f"Data not available for {etf}.")
            continue
        metrics_df = hf.calculate_metrics(df, 
                                    etf, 
                                    prediction_column=f'{schedular.lower()}_predictions',
                                    actual_column=f'actual_{schedular.lower()}_volatility', 
                                    n_features=n_etf_features)
        if type(metrics_df) == str:
            print(metrics_df)
            continue
        metrics_df['schedular'] = schedular.capitalize()
        metrics_df.rename(columns = hf.config['common']['metrics_rename_columns'], inplace = True)
        hf.s3conf.write_advanced_as_df(metrics_df, bucket_name, os.path.join(metrics_folder, f'{etf}.csv')) # save metrics to s3
    # Merge metrics to the original path
    for etf in hf.etf_list:
        csv = etf + '.csv'
        metrics_file = os.path.join(metrics_folder, csv)
        metrics_original_file = os.path.join(original_metrics_folder, csv)
        df = hf.s3conf.read_as_dataframe(bucket_name, metrics_file)
        try:
            og_df = hf.s3conf.read_as_dataframe(bucket_name, metrics_original_file)
            og_df = pd.concat([og_df, df[og_df.columns]], axis=0).drop_duplicates(subset=['date'], keep='first').sort_values(by=['date']).reset_index(drop=True).ffill().fillna(0) 
        except:
            og_df = df.reset_index(drop=True)
        og_df['date'] = pd.to_datetime(og_df['date'])
        hf.s3conf.write_advanced_as_df(og_df, bucket_name, metrics_original_file)
        print("Data updated in historical metrics file for etf:", etf)
    # Delete the temporary metrics files from s3
    _delete_folder_data(bucket_name, metrics_folder)
    # Write metrics data to ES and S3 Versioned
    hf.save_es_n_vs3(bucket_name, s3_dep_details_loc, original_metrics_folder, model_year, 'metrics')
    


if __name__ == '__main__':
    schedular = sys.argv[1]
    schedular = schedular.capitalize()
    try:
        date = sys.argv[2]
        date = datetime.date(*map(int, date.split('-')))
    except:
        date = (datetime.date.today() - BDay(1)).date()
    schedular = "Monthly"
    # Calling the main funtions
    print(f'Running for {schedular.capitalize()} schedular for date {date.strftime('%Y-%m-%d')}.')
    sender_name = 'sanchit sayala'
    # Initialize the EMail notification object and send trigger mail
    an = AnalysisNotification(date, schedular, sender_name)
    trigger_mail_subject = f"[ETF-Risk Model] Daily script run started for {schedular.capitalize()} scheduler for date {date} [EOM]"
    to_recipient = 'trigger_mail'
    mail_id = an.send_mail(trigger_mail_subject, "", [], to_recipient)
    if mail_id is not None:
        print('Successfully send trigger mail, ID:', mail_id)
    else:
        print('Trigger E-mail not sent.')
    # Call the main function
    try:
        main(date, schedular)
        an.prepare_data_and_send_mail()
    except Exception as e:
        traceback_error = traceback.format_exc()
        error_message = f"Daily run script status: Failed.\n\nError: \n{e} \n\nTraceback message: \n{traceback_error}"
        an.send_error_mail(error_message)