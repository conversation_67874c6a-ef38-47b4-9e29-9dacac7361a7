# Imports
import numpy as np
import os
import pandas as pd
import datetime
import yaml
import json
from sklearn.metrics import r2_score
from concurrent.futures import ThreadPoolExecutor 
from numpy_ext import rolling_apply
import time
from copy import deepcopy
from ibm_watson_machine_learning.deployment import Batch
from ibm_watson_machine_learning import APIClient
from pandas.tseries.offsets import BDay
from urllib3 import connectionpool, poolmanager
from multiprocessing.pool import ThreadPool
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config

# Additional methods using s3_config class
def download_s3_object(s3_client, bucketname, s3_filepath, local_folder):
    '''
    Function to download s3 file to local directory
    '''
    if local_folder != '' and local_folder is not None:
        if not os.path.exists(local_folder):
            os.mkdir(local_folder)
    else:
        raise Exception('Invalid local folder name.')
    filename = s3_filepath.strip().split('/')[-1]
    try:
        local_filepath = os.path.join(local_folder, filename)
        s3_client.download_file(bucketname, s3_filepath, local_filepath)
        return local_filepath
    except Exception as e:
        print('Issue while downloading file from s3. Error:', e)

def upload_s3_object(s3_client, bucketname, s3_folder, local_filepath):
    '''
    Funtion to upload local file to s3 folder
    '''
    if not os.path.exists(local_filepath):
        raise Exception('File not found.')
    filename = local_filepath.strip().split('/')[-1]
    object_name = os.path.join(s3_folder, filename)
    try:
        response = s3_client.upload_file(local_filepath, bucketname, object_name)
    except Exception as e:
        print('Issue while uploading file to s3. Error:', e)

class s3_versioned():
    def __init__(self, date, model_name, schedular, model_year, target_column, prediction_column, get_column_function, upload_type = 'predictions', n_workers=120):
        self.date = date
        self.model_name = model_name
        self.schedular = schedular.lower()
        self.model_year = model_year
        self.n_workers = n_workers
        self.bucket = 'eq-model-output'
        self.upload_type = upload_type
        self.path = f'{self.model_name}/{self.schedular}/{self.date}/{self.upload_type}/'
        self.s3conf = s3_config()
        self.get_col_list = get_column_function
        print('Date:', self.date, 'Model name:', self.model_name, 'Schedular:', self.schedular, 'Upload type:', self.upload_type)

    def update_column_data(self, df, bucket_name, s3_dep_details_loc):
        if 'schedular' not in df.columns:
            df['schedular'] = self.schedular.capitalize()
        df = df[pd.to_datetime(df['date']) == pd.to_datetime(self.date)].reset_index(drop=True)
        timestamp = datetime.datetime.today().strftime('%Y-%m-%d_%H:%M:%S')
        df['updated_at'] = timestamp 
        # Setting the model identifier column values
        deployment_df = self.s3conf.read_as_dataframe(bucket_name, s3_dep_details_loc)
        deployment_df['training_date'] = deployment_df['training_date'].apply(lambda x: x if len(x.split('-')[0]) == 4 else ('-').join(x.split('-')[-1::-1]))
        deployment_df = deployment_df.loc[deployment_df['year'] == self.model_year, ['name', 'training_date']]
        df = pd.merge(df, deployment_df, left_on='etf', right_on='name')
        df['model_identifier'] = df.apply(lambda row: f'{row['etf']}_{self.model_year}_{row['training_date']}', axis=1)
        return df.drop(columns=['name', 'training_date'])

    def dataframe_upload(self, df, prediction_column, target_column):
        list_of_data = []
        list_of_filepaths = []
        for etf, data in df.groupby('etf'):
            if self.upload_type == 'predictions':
                columns = self.get_col_list(etf) + ['etf', prediction_column, target_column, 'updated_at', 'model_identifier', 'schedular']
            else:
                columns = data.columns
            list_of_data.append(data[columns])
            list_of_filepaths.append(f'{self.path}{etf}.csv')
        n_calls = len(list_of_filepaths)
        with ThreadPoolExecutor(max_workers=self.n_workers) as exe:
            exe.map(self.s3conf.write_advanced_as_df, list_of_data, [self.bucket] * n_calls, list_of_filepaths)

    def dataframe_download(self, etf_list):
        list_of_filepaths = [os.path.join(self.path, etf + '.csv') for etf in etf_list]
        n_calls = len(list_of_filepaths)
        with ThreadPoolExecutor(max_workers=self.n_workers) as exe:
            list_of_data = exe.map(self.s3conf.read_advanced_as_df, [self.bucket] * n_calls, list_of_filepaths)
        list_of_data = [data for data in list_of_data if not data.empty]
        df = pd.concat(list_of_data)
        df = df[pd.to_datetime(df['date']) == pd.to_datetime(self.date)].reset_index(drop=True)
        if 'index' in df.columns:
            df.drop(columns=['index'], inplace=True)
        return df


class Helpers():
    def __init__(self, run_date, schedular, env='prod'):
        # Read config file
        self.run_date = run_date
        self.schedular = schedular
        self.s3conf = s3_config()
        self.esconf = es_config(env=env)
        with open('config.yaml', 'rt') as f:
            conf = yaml.safe_load(f)
        self.config = conf
        self.etf_list = self.config['common']['etfs']
        self.etf_isin_mapping = self.config['common']['etf2isin']
        self.correlation_col_prefix = 'correl_'
        self.correlation_col_suffix = '_kw'
        etf_column_list = {}
        for etf in self.etf_list:
            etf_column_list[etf] = self.get_col_list(etf)
        self.etf_column_list = etf_column_list
        self.target_column = self.config['common']['target_column']
        self.prediction_column = self.config['common']['prediction_column']
        self.period = self.config['schedular2days'][self.schedular.lower()]
        self.model_name = self.config['common']['model_name']

    def get_etf_data(self, etf):
        try:
            df = self.s3conf.read_as_dataframe('etf-predictions',f'Monthly/aigo/predictions/{etf}_predictions.csv')
        except:
            try:
                df = self.s3conf.read_as_dataframe('etf-predictions',f'Monthly/db/predictions/{etf}_predictions.csv')
            except:
                try:
                    df = self.s3conf.read_as_dataframe('etf-predictions',f'Monthly/sector/predictions/{etf}_predictions.csv')
                except:
                    try:
                        df = self.s3conf.read_as_dataframe('etf-predictions',f'Monthly/new_BNP_Paribas/Client/Final_data/{etf}.csv')
                    except Exception as e:
                        print(f"Error reading data for ETF: {etf} for S3.")
        return df    

    def get_col_list(self, etf):
        sector_mapping = deepcopy(self.config['common']['sector_mapping'])
        common_cols = deepcopy(self.config['common']['column_mapping']['common_columns'])
        partial_col_mapping = deepcopy(self.config['common']['column_mapping']['partial_columns'])
        if etf in partial_col_mapping.keys():
            partial_cols = partial_col_mapping[etf]
        else: # for rest sector types
            partial_cols = partial_col_mapping['remaining']
        # benchmark
        extra_columns = self.config['common']['column_mapping']['extra_columns']
        ignore_columns = self.config['common']['column_mapping']['ignore_columns']
        if etf in extra_columns.keys():
            partial_cols += extra_columns[etf]
        elif etf not in ignore_columns['etfs']:
            partial_cols += ignore_columns['columns']
        sector_cols = [f'{sector_mapping[etf]}_kw_pos', f'{sector_mapping[etf]}_kw_neg']
        # customer index
        customer_index = self.config['common']['column_mapping']['customer_index']
        # combine all columns
        columns = common_cols + partial_cols + sector_cols + customer_index
        corr_cols = [self.correlation_col_prefix + x for x in columns if self.correlation_col_suffix in x]
        columns = columns + corr_cols
        return columns

    def read_input_data(self, data_location, include_actual = False):
        if 's3://' in data_location:
            bucket, key = getBucketKey(data_location)
            data = self.s3conf.read_as_dataframe(bucket, key)
            isin = key.split('/')[-1].split('.')[0]
            column_list = self.get_col_list(isin)
            if include_actual:
                column_list.append(self.target_column)
            return data[column_list]
        else:
            raise Exception('Invaild path.')
    
    def get_start_end_dates(self):
        # Traverse through all the files and get the last dates of each
        prediction_bucket = self.config['s3']['bucket_name']
        prediction_folder = self.config['s3']['prediction_folder']
        df_last_date = self.run_date
        for etf in self.etf_list:
            df = self.s3conf.read_as_dataframe(prediction_bucket, os.path.join(prediction_folder, etf))
            df['date'] = pd.to_datetime(df['date'])
            df_last_date = min(df_last_date, df['date'].max().date())
        start_date = df_last_date.strftime('%Y-%m-%d')
        end_date = self.run_date.strftime('%Y-%m-%d')
        return start_date, end_date
    
    def delete_s3_file(self, bucket_name, object_name): 
        # Method to delete s3 files
        self.s3conf._s3Client.delete_object(Bucket=bucket_name, Key=object_name)

    # Elastic Search data fetch functions
    def get_es_data(self, isin: str, years, index_prefix):
        schedular = self.schedular
        if schedular is not None:
            schedular = schedular.capitalize()
        data=[]
        for year in range(years[0], years[1] + 1):
            q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
            try:
                result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
                for rs in result['hits']['hits']:
                    es_data=rs['_source']
                    data.append(es_data)
            except:
                print(f'ES data for {index_prefix} not present for {year}.')
        df=pd.DataFrame(data)
        if (schedular != None) and ('schedular' in df.columns):
            df = df[df['schedular'] == schedular].reset_index(drop = True)
        if len(df) == 0:
            return df
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        return df


    def get_es_data_datewise(self, index_prefix: str, date: str | None = None, isin_list=None):
        schedular = self.schedular
        if isin_list is None:
            isin_list = self.isin_list
        if date is None:
            date = self.run_date.strftime('%Y-%m-%d')

        year = int(date.split('-')[0])
        if schedular is not None:
            schedular = schedular.capitalize()
        q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}}, {"range": {"date": {"gte": date, "lte": date}}}]}}}
        try:
            result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=json.dumps(q_total), size=10000,request_timeout=6000)
            hits = result['hits']['hits']
            df = pd.json_normalize(hits)
            df.rename(columns={col: col.replace('_source.', '') for col in df.columns}, inplace = True)
            df = df.loc[:, (~df.columns.str.startswith('_')) & (df.columns != "sort")]
        except Exception as e:
            print('Error:', e)
            raise Exception(f'Issue fetching data for {index_prefix}_{year}.')
        if (schedular != None) and ('schedular' in df.columns):
            df = df[df['schedular'] == schedular].reset_index(drop = True)
        if len(df) == 0:
            return df
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        return df


    def get_es_data_in_range(self, isin_list, start_date, end_date, index_prefix, as_dict=True):
        schedular = self.schedular
        if schedular is not None:
            schedular = schedular.capitalize()
        years = [int(start_date.split('-')[0]), int(end_date.split('-')[0])]
        data = []
        if years[0] != years[1]:
            for year in range(years[0], years[1] + 1):
                if year == years[0]:
                    temp_start_date = start_date
                    temp_end_date = f'{year}-12-31'
                elif year == years[1]:
                    temp_start_date = f'{year}-01-01'
                    temp_end_date = end_date
                else:
                    temp_start_date = f'{year}-01-01'
                    temp_end_date = f'{year}-12-31'
                if schedular is not None:
                    q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": temp_start_date, "lte": temp_end_date}}}, {"match": {"schedular.keyword": schedular}}]}},"sort": [{"date": {"order": "desc"}}]}
                else:
                    q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": temp_start_date, "lte": temp_end_date}}}]}},"sort": [{"date": {"order": "desc"}}]}
                try:
                    result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
                    for rs in result['hits']['hits']:
                        es_data=rs['_source']
                        data.append(es_data)
                except Exception as e:
                    print(f'ES data for {index_prefix} not present for isins: {isin_list} for {year}.')
                    print('Error:', e)
        else:
            year = years[0]
            if schedular is not None:
                q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date, "lte": end_date}}}, {"match": {"schedular.keyword": schedular}}]}},"sort": [{"date": {"order": "desc"}}]}
            else:
                q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date, "lte": end_date}}}]}},"sort": [{"date": {"order": "desc"}}]}
            try:
                result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
                for rs in result['hits']['hits']:
                    es_data=rs['_source']
                    data.append(es_data)
            except Exception as e:
                print(f'Error fetching ES data for {index_prefix} not present for isins: {isin_list} for {year}.')
                print('Error:', e)
        df=pd.DataFrame(data)
        
        if df.empty:
            print(f'No data found in the range [{start_date}, {end_date}] in ES for given list of {len(isin_list)} isins: \n{isin_list}')
            return None
        
        if as_dict:
            df_dict = {}
            for isin, data in df.groupby('isin'):
                data['date'] = pd.to_datetime(data['date'])
                data = data.sort_values(['date'], ascending=True).reset_index(drop=True)
                df_dict[isin] = data
            return df_dict
        else:
            df['date']=pd.to_datetime(df['date'])
            df.sort_values(['isin', 'date'], ascending=True, inplace=True)
            df.reset_index(inplace=True, drop=True)
            return df

    def get_close_change(self, company, period, years):
        single_date_response = self.get_capiq_close(years, company.values[0].tolist(), id_type='tid')
        if failed_check(single_date_response['GDSSDKResponse']):
            single_date_response = self.get_capiq_close(years, company.values[0].tolist(), id_type='tic:exg')
            if failed_check(single_date_response['GDSSDKResponse']):
                single_date_response = self.get_capiq_close(years, company.values[0].tolist(), id_type='isin')
        try:
            snp_closes = pd.DataFrame({'date': pd.to_datetime(single_date_response['GDSSDKResponse'][0]['Headers']), 'closeprice_adj': single_date_response['GDSSDKResponse'][0]['Rows'][0]['Row']})
            snp_closes["closeprice_adj"] = snp_closes["closeprice_adj"].astype(float)
            snp_closes['close_change'] = snp_closes["closeprice_adj"].ffill().pct_change(periods=period) * 100
            snp_closes.drop(columns = 'closeprice_adj', inplace = True)
            return snp_closes
        except:
            print("Couldn't fetch prices from S&P API.")
            return pd.DataFrame()

    def read_data(self, isin, years, read_from, pred_col, schedular, es_index, bucket_name, path_loc, local_folder, actual_column):
        default_schedular = self.config['common']['default_schedular']
        schedular_dict = self.config['schedular2days']
        try:
            if read_from == 'es': # fetching data from es
                if actual_column is not None:
                    temp = self.get_es_data(isin, years, es_index, schedular)[['date', pred_col, actual_column]] 
                else:
                    temp = self.get_es_data(isin, years, es_index, schedular)[['date', pred_col]] 
            elif read_from == 's3': # fetching data from s3
                if actual_column is not None:
                    temp = self.s3conf.read_as_dataframe(bucket_name, os.path.join(path_loc, isin + '.csv'))[['date', pred_col, actual_column]] 
                else:
                    temp = self.s3conf.read_as_dataframe(bucket_name, os.path.join(path_loc, isin + '.csv'))[['date', pred_col]] 
            else: # fetching data from local
                if actual_column is not None:
                    temp = pd.read_csv(os.path.join(local_folder, isin))[['date', pred_col, actual_column]] 
                else:
                    temp = pd.read_csv(os.path.join(local_folder, isin))[['date', pred_col]] 
            if schedular == None or schedular == '':
                schedular = default_schedular
        except Exception as e:
            print(f'Issue reading data from {read_from}:')
        try:
            temp['date'] = pd.to_datetime(temp['date'])
            temp = temp.sort_values(by = 'date').reset_index(drop = True)
            temp.rename(columns = {pred_col:f'{schedular.lower()}_predictions', 
                                actual_column: f'actual_{schedular.lower()}_volatility'}, 
                    inplace = True)
            temp[f'{schedular.lower()}_predictions'] = temp[f'{schedular.lower()}_predictions'].shift(schedular_dict[schedular.lower()])
            temp[f'actual_{schedular.lower()}_volatility'] = temp[f'actual_{schedular.lower()}_volatility'].shift(schedular_dict[schedular.lower()])
            temp = temp[(temp['date'] >= pd.to_datetime(f'{years[0]}-01-01')) & (temp['date'] <= pd.to_datetime(f'{years[1]}-12-31'))]    
            temp = temp.drop_duplicates(subset = 'date').sort_values(by = 'date').reset_index(drop = True)
            temp.reset_index(drop = True, inplace = True)
            temp = temp.ffill().fillna(0)
            temp[f'{schedular.lower()}_predictions'] = temp[f'{schedular.lower()}_predictions'].astype(float, errors="ignore")
            temp[f'actual_{schedular.lower()}_volatility'] = temp[f'actual_{schedular.lower()}_volatility'].astype(float, errors="ignore")
            print(f'Data collected for isin {isin}.')
        except Exception as e:
            print('Error:', e)
            temp = pd.DataFrame()
        return isin, temp

    def read_data_parallely(self, etf_list=None, read_from='es', pred_col='predictions', n_workers=100, **kwargs): # function to read historical data
        '''
        'kwargs' keys-
            'es_index': index of Elastic Search to fetch data from (if read_from is 'es')
            'bucket_name': name of S3 bucket where data is stored (if read_from is 's3')
            'path_loc': S3 folder location to fetch data from (if read_from is 's3')
            'local_folder': path for folder to fetch data from (if data needs to be collected from local)
            'schedular': 'daily', 'weekly', 'monthly' or 'quarterly'
            'actual_column': 'column name containing the target data'
        '''
        if 'schedular' in kwargs.keys():
            schedular = kwargs['schedular'].lower()
        else:
            schedular = None
        if etf_list is None:
            etf_list = self.etf_list
        years = [self.run_date.year - 3 if self.run_date.month < 3 else self.run_date.year - 2, self.run_date.year] # years to fetch data for
        if read_from == 'es': # fetching data from es
            es_index = kwargs['es_index']
            bucket_name = None
            path_loc = None
            local_folder = None
        elif read_from == 's3':
            bucket_name = kwargs['bucket_name']
            path_loc = kwargs['path_loc']
            es_index = None
            local_folder = None
        else:
            local_folder = kwargs['local_folder']
            es_index = None
            bucket_name = None
            path_loc = None
        n_calls = len(etf_list)
        if 'actual_column' in kwargs.keys():
            actual_column = kwargs['actual_column']
        else:
            actual_column = None
        with ThreadPoolExecutor(max_workers=n_workers) as exe:
            dfs = exe.map(self.read_data, 
                        etf_list, 
                        [years] * n_calls, 
                        [read_from] * n_calls, 
                        [pred_col] * n_calls, 
                        [schedular] * n_calls, 
                        [es_index] * n_calls, 
                        [bucket_name] * n_calls, 
                        [path_loc] * n_calls, 
                        [local_folder] * n_calls,
                        [actual_column] * n_calls)
        data_dict = {}
        for isin, data in dfs:
            data_dict[isin] = data
        return data_dict
    
    def accuracy_function(self, df_series, coff = 500):
        '''
        Accuracy conversion metric to convert daily APE into accuracy for ER.
        '''
        return df_series.apply(lambda x: 97/( 1 + 20 * np.exp((-coff/x))) if x <  100 else max(0, 106.7 - 0.213 * x))
    
    def calculate_accuracy(self, df_data, prediction_col = 'predictions', target_col = 'close_change_monthly', coff = 500):
        '''
        This function calculates the accuracy based on actual er and predicted er for an isin for daily, 14 & 22 trading days rolling average.
        Accuracy calculations:
            - Calculate the modified APE for individual data points (modification: divide by max( abs(actual), abs(pred) )) )
            - Convert it to accuracy using 100/1+20*(exp^-500/x) if x < 100 and we linearly degrade to 0 for ape = 500
            

        Input params (required):
            - date
            - prediction_col : predicted ER value in percentage (prediction for today's date that are generated 1 month back ie. if date is 28 Dec 2023, it'll have the predictions generated on 28 Nov 2023)
            - target_col : target column value in percentage, it's also shifted by 22 days similar to prediction_col
            - coff : coefficient to be used in accuracy function

        Output columns:
            - accuracy_1_day: accuracy calculated for each day
            - accuracy_14_day : accuracy calculated for 14 day rolling window
            - accuracy_22_day : accuracy calculated for 22 day rolling window

        Range of columns:
            - prediction_col : [0, 100]
            - target_col : [0, 100]
            - accuracy: [0, 100]
        '''
        if prediction_col not in df_data.columns:
            raise Exception('Prediction column not in Dataframe')

        if target_col not in df_data.columns:
            raise Exception('Target column not in Dataframe')
        
        # Remove any nan's in prediction or target cols
        df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

        # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
        df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
        df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

        # Calculate RMS of MAPE over a rolling of 14 days
        df_data['accuracy_1_day'] = self.accuracy_function(df_data['daily_ape'], coff = coff)

        # Calculate RMS of MAPE over a rolling of 22 days
        df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
        df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5
        df_data.drop(columns=['denominator'], inplace=True)
        return df_data

    def directionality_score_calc(self, prediction_direction, close_direction):
        directionality_df = pd.DataFrame()
        directionality_df['prediction_direction'] = prediction_direction
        directionality_df['close_direction'] = close_direction
        correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)] 
        incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)] 
        relaxation_count = len(incorrect_direction_df[(abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)]) 
        if len(directionality_df) == relaxation_count:
            directionality_score = np.nan
        else:
            directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
        return directionality_score


    def calculate_metrics(self, df, isin, prediction_column = 'monthly_predictions', actual_column =  'actual_monthly_returns', metrics_to_calculate = None, n_features = 0):
        period = self.config['common']['metrics_period']
        if metrics_to_calculate is None:
            metrics_to_calculate = self.config['common']['metrics_list']

        if len(df) < period:
            return f'Dataframe size for {isin} too small to calculate metrics.'
        
        df = df.rename(columns = {prediction_column: 'predictions', actual_column: 'actual_returns'})
        metrics_columns = self.config['common']['metrics_basic_columns'] + metrics_to_calculate
        
        # Total perc diff
        if 'total_perc_diff' in metrics_columns or 'abs_total_diff' in metrics_columns:
            df['total_perc_diff'] = rolling_apply(lambda target, prediction: (target.mean() - prediction.mean()) / prediction.mean(), period, df['actual_returns'].values, df['predictions'].values)
            
        # Abs total diff
        if 'abs_total_diff' in metrics_columns:
            df['abs_total_diff'] = abs(df['total_perc_diff'])
        
        # Total variance perc diff
        if 'total_variance_perc_diff' in metrics_columns or 'abs_total_variance_perc_diff' in metrics_columns:
            df['total_variance_perc_diff'] = rolling_apply(lambda target, prediction: (target.var() - prediction.var()) / prediction.var(), period, df['actual_returns'].values, df['predictions'].values)
            
        # Abs total variance perc diff
        if 'abs_total_variance_perc_diff' in metrics_columns:
            df['abs_total_variance_perc_diff'] = abs(df['total_variance_perc_diff'])
        
        # MAE
        if 'mean_absolute_error' in metrics_columns:
            df['mean_absolute_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: abs(df_series).mean())
        
        # MSE
        if 'mean_squared_error' in metrics_columns or 'root_mean_squared_error' in metrics_columns:
            df['mean_squared_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: ((df_series ** 2).sum() / period))
        
        # RMSE
        if 'root_mean_squared_error' in metrics_columns:
            df['root_mean_squared_error'] = df['mean_squared_error'] ** 0.5
            
        # R2 score
        if 'r2_score' in metrics_columns or 'adjusted_r2_score' in metrics_columns:
            df['r2_score'] = rolling_apply(r2_score, period, df['actual_returns'].values, df['predictions'].values)
                    
        # Adjusted R2 score
        if 'adjusted_r2_score' in metrics_columns:
            if type(n_features) == int:
                df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features - 1))
            elif type(n_features) == dict:
                df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features[isin] - 1))
            else:
                df['adjusted_r2_score'] = df['r2_score']
        
        # Mean directionality
        if 'mean_directionality' in metrics_columns:
            df['mean_directionality'] = (df['actual_returns'] * df['predictions']).rolling(period).apply(lambda df_series: 100 * ((df_series) > 0).sum() / period)
        
        # Correlation score
        if 'correlation_score' in metrics_columns:
            df['correlation_score'] = rolling_apply(lambda target, prediction: np.corrcoef(target, prediction)[0][1], period, df['actual_returns'].values, df['predictions'].values) 
            
        # Confidence score
        if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
            min_confidence = 0.01
            
            max_values =  df['actual_returns'].rolling(period * 24, min_periods = period).max()
            min_values =  df['actual_returns'].rolling(period * 24, min_periods = period).min()
            filt1 = [df.loc[i, 'predictions'] >= max_values.loc[i] for i in range(len(df))]
            filt2 = [df.loc[i, 'predictions'] <= min_values.loc[i] for i in range(len(df))]
            filt3 = [df.loc[i, 'actual_returns'] >= max_values.loc[i] for i in range(len(df))]
            filt4 = [df.loc[i, 'actual_returns'] <= min_values.loc[i] for i in range(len(df))]

            df['confidence_score'] = [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
            max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_returns"])/(max_values.loc[i] - df.loc[i, "predictions"]) if df.loc[i, "actual_returns"] > df.loc[i, "predictions"] else (df.loc[i, "actual_returns"] - min_values.loc[i]) / (df.loc[i, "predictions"] - min_values.loc[i])) for i in range(len(df))]

        # Average confidence score
        if 'avg_confidence_score' in metrics_columns:
            df['avg_confidence_score'] = df['confidence_score'].rolling(period // 2).mean()

        # Directionality score
        if 'directionality_score' in metrics_columns:
            directionality_df = pd.DataFrame()
            directionality_df["prediction_direction"] = (df["predictions"] - df['predictions'].shift(1)) / df['predictions'].shift(1)
            directionality_df["close_direction"] = (df["actual_returns"] - df['actual_returns'].shift(1)) / df['actual_returns'].shift(1)
            df['directionality_score'] = rolling_apply(self.directionality_score_calc, period, directionality_df['prediction_direction'], directionality_df['close_direction']) 
                    
        # Accuracy
        if "accuracy_14_day" in metrics_columns or "accuracy_22_day" in metrics_columns or "accuracy_1_day"  in metrics_columns:
            df = self.calculate_accuracy(df, 'predictions', 'actual_returns')

        df['isin'] = isin
        return df[metrics_columns][period:].reset_index(drop = True)


    def es_upload(self, df, year, es_index_prefix, drop_duplicates='isin'):
        # Filter out the data accourding to the year
        df['date'] = pd.to_datetime(df['date'])
        df = df[df['date'].apply(lambda x: x.year) == year].reset_index(drop=True)
        es_index = f"{es_index_prefix}_"+str(year)
        print('Index_name:', es_index)
        # to check and create index if not already present
        indices=str(self.esconf.client.indices.get_alias().keys())
        if es_index not in indices:
            self.esconf.create_index(es_index)
        if 'schedular' not in df.columns:
            df['schedular'] = self.schedular.capitalize()
        df['date'] = df['date'].astype(str)
        df = df.drop_duplicates(subset=[drop_duplicates]).reset_index(drop=True)
        if df.empty:
            raise Exception('File is empty!')
        df.replace({np.nan: None}, inplace = True)
        documents = []
        for index, row in df.iterrows():
            doc_id = f"{row['isin']}_{row['date']}_{self.schedular.lower()[0]}"
            document = {"index": {"_index": es_index, "_id": doc_id}}
            data = row.to_dict()
            documents.append(document)
            documents.append(data)
        response=self.esconf.client.bulk(documents)
        if response['errors']:
            print('Error during ES upload for', self.run_date.strftime('%Y-%m-%d'))
        else:
            print('ES Upload successful.')

    def save_es_n_vs3(self, bucket_name, s3_dep_details_loc, upload_data_folder, model_year, upload_type):
        df_list = []
        for etf in self.etf_list:
            df = self.s3conf.read_as_dataframe(bucket_name, os.path.join(upload_data_folder, etf + '.csv'))
            df['etf'] = etf
            df['isin'] = self.etf_isin_mapping[etf]
            df['date'] = pd.to_datetime(df['date'])
            df_list.append(df[df['date'] == pd.to_datetime(self.run_date)])
        df_today = pd.concat(df_list, axis=0).reset_index(drop=True)
        if df_today.empty:
            print('No data to upload.')
            return
        # ES Upload
        es_index_prefix = self.config['common']['es_index_prefix']['predictions'] if upload_type == 'predictions' else self.config['common']['es_index_prefix']['metrics']
        self.es_upload(df_today, self.run_date.year, es_index_prefix)
        # VS3 Upload
        s3v = s3_versioned(self.run_date, self.model_name, self.schedular, model_year, self.target_column, self.prediction_column, self.get_col_list, upload_type)
        df_today = s3v.update_column_data(df_today, bucket_name, s3_dep_details_loc)
        s3v.dataframe_upload(df_today, self.prediction_column, self.target_column)


class predictionHelper():
    def __init__(self, helper_object, wml_credentials, prediction_batch_size = 120):
        self.data_store_dict = {}
        self.batch_run_size = prediction_batch_size
        self.hf = helper_object
        self.wml_credentials = wml_credentials

    def del_jobs(self, job_id, wmlclient):
        try:
            status = wmlclient.deployments.delete_job(job_id, hard_delete=True)
            return f'{job_id}:{status}'
        except Exception as e:
            print(f'job:{job_id}:{str(e)}')

    def create_deployment_jobs(self, deployment_id, year, input_location, wmlclient, service):
        try:
            if type(input_location) == str or input_location is None:
                if input_location is None or input_location.strip() == '':
                    print(f"Input data not available for: {deployment_id}")
                    raise Exception("Input data not available")
                else:
                    try:
                        if input_location in self.data_store_dict.keys():
                            test_data = self.data_store_dict[input_location]
                        else:
                            test_data = self.hf.read_input_data(input_location)
                            self.data_store_dict[input_location] = test_data
                        test_data['date'] = pd.to_datetime(test_data['date'])
                    except Exception as e:
                        print(f"Input data not available or unable to read for: {deployment_id}")
                        print(e)
                        raise Exception("Input data not available or unable to read")
                    test_data = test_data[(test_data['date'] >= pd.to_datetime(f'01-01-{int(year) + 1}')) & (test_data['date'] <= pd.to_datetime(f'31-12-{int(year) + 1}'))].reset_index(drop = True)
            else:
                test_data = input_location
            print(input_location)
            test_data['date'] = test_data['date'].astype(str)

            service.get(deployment_id)
            model_id = wmlclient.deployments.get_details(deployment_uid=deployment_id)['entity']['asset']['id']
            input_schema = wmlclient.repository.get_model_details(model_id)['entity']['schemas']['input'][0]['fields']   
            input_columns = list(pd.DataFrame(input_schema)['name'].values)
            test_data = test_data[input_columns]
            test_data = test_data.replace(np.inf, np.nan).replace(-np.inf, np.nan).ffill().fillna(0)
            job_payload_ref = {wmlclient.deployments.ScoringMetaNames.INPUT_DATA: [{'fields': [], 'values': test_data.values.tolist()}]}
            job = wmlclient.deployments.create_job(deployment_id, meta_props=job_payload_ref) # Creates the scoring/prediction job
            job_id = wmlclient.deployments.get_job_uid(job)
            print(f'deployment id: {deployment_id} job id: {job_id}.')
            return [deployment_id, job_id]
        except Exception as e:
            print(f'for deployment_id: {deployment_id}, error is {e}')
            return [deployment_id, '']

    def get_state(self, job_id, wmlclient):
        status = ''
        values=[]
        try:
            # Get only the status and predictions output from get_job_details
            job_details = wmlclient.deployments.get_job_details(job_id)#, include='predictions,status')
            status =  job_details['entity']['scoring']['status']['state']
            if status=='completed':
                values = [value[0] for value in job_details['entity']['scoring']['predictions'][0]['values']]
            return [job_id, status, values]
        except Exception as e:
            return [job_id, 'error', []]

    def trigger_predict(self, deployment_toSubmit, max_run_duration = 60):
        delete_pool = ThreadPool(24)
        all_jobs_status = []
        all_jobs_created = []
        all_jobs_deleted = []
        start = time.perf_counter()
        for space_id in deployment_toSubmit['deployment_space'].unique().tolist():
            wmlclient = APIClient(self.wml_credentials)
            wmlclient.set.default_space(space_id)
            service = Batch(source_wml_credentials=self.wml_credentials, source_space_id=space_id)
            print(f"starting for space: {space_id}")
            delete_results = [] 
            job_status = []     
            jobs_deleted = []   
            run_duration = 0
            space_start = time.perf_counter()
            deployments = deployment_toSubmit[deployment_toSubmit['deployment_space']==space_id]
            print(f"length of deployment list: {deployments.shape[0]}")
            jobs_to_submit=self.batch_run_size
            jobs_submitted=0
            jobs_executing=0
            no_new_jobs_counter = 0 # added
            prev_jobs_executing = 0 # added
            j=0
            while (( jobs_to_submit > 0 ) or ( jobs_executing > 0 )) and ( run_duration < max_run_duration * 60) and no_new_jobs_counter < 50: # changed
                jobs_to_submit=min(deployments.shape[0]-jobs_submitted,jobs_to_submit)
                to_fetch_list=()
                if ( jobs_to_submit > 0 ) :   
                    deployment_jobs = deployments.iloc[jobs_submitted:(jobs_submitted+jobs_to_submit)].copy()
                    deployment_jobs = deployment_jobs[['deployment_id', 'year', 'input_location']].copy()
                    job_created = []
                    job_create_start = time.perf_counter()
                    create_results = []
                    print(f'jobs_to_submit: {jobs_to_submit}, iteration:{j+1}, submitted: {jobs_submitted}, jobs_to_submit_actual: {deployment_jobs.shape[0]}, total_jobs: {deployments.shape[0]}')
                    thread_pool_size = jobs_to_submit if jobs_to_submit <= 100 else 100
                    pool = ThreadPool(thread_pool_size)
                    for deployment in deployment_jobs.values.tolist():
                        create_results.append(pool.apply_async(self.create_deployment_jobs, deployment+[wmlclient, service]))

                    pool.close()
                    pool.join()
                    job_created = [r.get() for r in create_results]
                    all_jobs_created.extend(job_created)
                    job_create_finish = time.perf_counter()
                    print(f'create jobs finished in {round((job_create_finish-job_create_start), 2)} seconds(s)')
                    print(f'number of jobs created: {len(job_created)}')
                    print(f'number of jobs created with error : {len([job for job in job_created if job is None])}')
                    print(f'number of jobs created with no job_id: {len([job for job in job_created if job[1]==""])}')
                    job_created_df = pd.DataFrame(columns=['deployment_id', 'job_id'], data=job_created)
                    to_fetch_list=job_created_df[['job_id']].values.tolist()            
                wait_time = 5 # 0 if jobs_to_submit > 0 else 5 # changed
                no_new_jobs_counter = no_new_jobs_counter+1 if jobs_executing == prev_jobs_executing else 0 # added
                prev_jobs_executing = jobs_executing #added
                print(f'waiting for {wait_time} sec ...')
                time.sleep(wait_time)
                print('done waiting')
                job_fetch_start = time.perf_counter()
                fetch_results = []
                pool = ThreadPool(24)
                if len(to_fetch_list) > 0  :  
                    to_fetch_list.extend([job[0]] for job in job_status if job[1] in ["queued", "running"])
                else:
                    to_fetch_list = ([job[0]] for job in job_status if job[1] in ["queued", "running"])
                for job in to_fetch_list:
                    fetch_results.append(pool.apply_async(self.get_state, job+[wmlclient]))
                pool.close()
                pool.join()
                job_status = [r.get() for r in fetch_results]
                all_jobs_status.extend([job for job in job_status if job[1] in ["completed", "failed"]])
                job_fetch_finish = time.perf_counter()
                print(f'fetch jobs finished in {round((job_fetch_finish-job_fetch_start), 2)} seconds(s)')
                print(f'number of jobs fetched: {len(job_status)}')
                print(f'number of jobs fetched and completed: {len([job for job in job_status if job[1] in ["completed"]])}')
                print(f'number of jobs fetched and queued: {len([job for job in job_status if job[1] in ["queued"]])}')
                print(f'number of jobs fetched and failed: {len([job for job in job_status if job[1] in ["failed"]])}')
                print(f'number of jobs fetched and running: {len([job for job in job_status if job[1] in ["running"]])}')
                print(f'number of jobs fetched otherwise:{len([job for job in job_status if job[1] not in ["completed", "queued", "failed", "running"]])}')
                job_fetched_df = pd.DataFrame(columns=['job_id', 'status', 'values'], data=job_status)

                # Delete completed and failed jobs
                job_list = [job[0] for job in job_fetched_df[['job_id', 'status']].values.tolist() if (job[1] in ["completed", "failed"])]
                if ( len(job_list) > 0):     
                    for job in job_list: 
                        delete_results.append(delete_pool.apply_async(self.del_jobs,(job, wmlclient,)) )
                job_delete_finish = time.perf_counter()
                jobs_submitted+=jobs_to_submit
                jobs_to_submit=len(job_list)
                jobs_executing=len([job for job in job_status if job[1] in ["queued", "running"]])
                j+=1
                print(f'total time taken in this loop, {j}: {round((job_delete_finish-job_create_start), 2)} sec(s)\n')
                run_duration = ( job_delete_finish - start )
                print(f'current run duration : {run_duration} jobs_executing : {jobs_executing} submitted:{jobs_submitted},total_jobs:{len(deployments)}\n')
            if (jobs_executing > 0):     
                for job in [job for job in job_status if job[1] in ["queued", "running"]]:
                    delete_results.append(delete_pool.apply_async(self.del_jobs,(job[0], wmlclient,)) )        
            jobs_deleted = [res for res in delete_results if res is not None]
            all_jobs_deleted.extend(jobs_deleted)
            print(f'total time taken in this space: {round((job_delete_finish-space_start)/60, 2)} min(s)\n')
            print(f'length of all jobs created:{len(all_jobs_created)}')
            print(f'length of all jobs status:{len(all_jobs_status)}')
            print(f'length of all jobs deleted:{len(all_jobs_deleted)}')
        # Wait for all deletes to finish
        delete_pool.close()
        delete_pool.join()
        print(f"length of all_jobs_status is {len(all_jobs_status)}")
        df_all_jobs_status = pd.DataFrame(all_jobs_status, columns=['job_id', 'status', 'predictions'])
        df_all_jobs_created = pd.DataFrame(all_jobs_created, columns=['deployment_id', 'job_id'])
        result_df = pd.merge(df_all_jobs_created, df_all_jobs_status, how='left', on='job_id')
        result_df = pd.merge(deployment_toSubmit[['deployment_space', 'deployment_id']], result_df, on='deployment_id', how = 'left')
        print(f'total time taken:{round((job_delete_finish-start)/60, 2)} min(s)')
        return result_df


# Additional functions
def get_start_date(ipo, original_st):
    if ipo is None or ipo == np.nan or str(ipo) == 'nan':
        return original_st
    try: 
        ipo = pd.to_datetime(ipo)
        original_st = pd.to_datetime(original_st)
    except:
        return original_st
    start_date = original_st.strftime('%m/%d/%Y') if ipo < original_st else ipo.strftime('%m/%d/%Y')
    return start_date

def get_end_date(original_ed):
    return datetime.date.today().strftime('%m/%d/%Y') if datetime.date.today() < pd.to_datetime(original_ed).date() else original_ed

def failed_check(GDSSDKResponse):
    if GDSSDKResponse[0]['ErrMsg'] == 'InvalidIdentifier' or GDSSDKResponse[0]['ErrMsg'] == 'Input Arguments Missing':
        return True
    if np.sum([y == 'Data Unavailable' for y in [x['Rows'][0]['Row'][0] for x in GDSSDKResponse]]) / len(GDSSDKResponse) >= 0.75: 
        return True
    return False

def get_volatility(df, diff):
    df = df.loc[[x for x in df.index if BDay().is_on_offset(x)]].ffill()
    sq_ln_change = df.rolling(2).apply(lambda x: (np.log(x[1] / x[0])), raw = False)
    log_volatility = sq_ln_change.rolling(diff).std() * (252 ** 0.5)
    return log_volatility

def getBucketKey(s3path):
    s3path = s3path[len('s3://'):]
    bucket = s3path.split('/')[0]
    key = '/'.join(s3path.split('/')[1:])
    return bucket, key

def patch_https_connection_pool(**constructor_kwargs):
    """
    This allows to override the default parameters of the
    HTTPConnectionPool constructor.
    For example, to increase the poolsize to fix problems
    with "HttpSConnectionPool is full, discarding connection"
    call this function with maxsize=16 (or whatever size
    you want to give to the connection pool)
    """
    class MyHTTPSConnectionPool(connectionpool.HTTPSConnectionPool):
        def __init__(self, *args,**kwargs):
            kwargs.update(constructor_kwargs)
            super(MyHTTPSConnectionPool, self).__init__(*args,**kwargs)
    poolmanager.pool_classes_by_scheme['https'] = MyHTTPSConnectionPool
patch_https_connection_pool(maxsize=100)