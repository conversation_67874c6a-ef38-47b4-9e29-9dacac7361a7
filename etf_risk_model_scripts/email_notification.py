import base64
import os
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText
from email.message import EmailMessage
from helper_functions import Helpers, download_s3_object

class AnalysisNotification():
    def __init__(self, date, schedular, sender_name=None):
        self.hf = Helpers(date, schedular)
        self.gmail_creds = self.hf.config['gmail_creds']
        self.temp_folder = self.hf.config['common']['temporary_folder']

        # Email text initializations
        self.mail_header = f"Daily run script status: Complete."
        sender_details = "Best Regards, \n" + ' '.join([x.capitalize() for x in sender_name.split()]) if sender_name is not None else "Thanks"
        self.mail_footer = "Feel free to reach out in case of any help or clarification. \n\n" + sender_details
        self.error_mail_footer = "The team will look into the issue and get back once the error is fixed. \n\n" + sender_details
        self.mail_subject = f"Script Run Notificaion : ETF-Risk Model ({self.hf.schedular.capitalize()}) daily run : Date {self.hf.run_date}"
        self.error_mail_subject = f"Scheduled daily run failed: ETF-Risk Model ({self.hf.schedular.capitalize()}) daily run : Date {self.hf.run_date}"


    def get_credentials(self, path_name):
        '''
        Method to get the credentials from local temporary credential file.
        '''
        creds = None
        creds_path = path_name
        SCOPES = self.gmail_creds['scopes']
        try:
            if os.path.exists(creds_path):
                creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
        except Exception as e:
            print('Issue reading credential file:', e)
        return creds


    def download_temp_json(self):
        '''
        Method to download the temporary credential json file
        '''
        if not os.path.exists(self.temp_folder):
            os.mkdir(self.temp_folder)
        bucketname = self.gmail_creds['bucket']
        s3_filepath = self.gmail_creds['folder']
        local_folder = self.temp_folder
        local_path = download_s3_object(self.hf.s3conf._s3Client, bucketname, s3_filepath, local_folder)

        return local_path


    def delete_temp_data(self, filepaths):
        '''
        Method to delete all the temporary files created
        '''
        for filepath in filepaths:
            if os.path.exists(filepath):
                os.remove(filepath)
                print(f'Deleted file "{filepath.split('/')[-1]}".')
        extra_data = os.path.join(self.temp_folder, '.ipynb_checkpoints')
        if os.path.exists(extra_data):
            os.rmdir(extra_data)
        # Delete the temp folder if empty
        print(os.listdir(self.temp_folder))
        if os.path.exists(self.temp_folder) and len(os.listdir(self.temp_folder)) == 0:
            os.rmdir(self.temp_folder)
            print(f'"{self.temp_folder}" folder deleted since found empty.')


    def send_mail(self, subject: str, message_text: str, attachments: list = None, to_recipient: str = None):
        '''
        Method to send the data and files over mail.
        '''
        credential_filepath = self.download_temp_json()

        credentials = self.get_credentials(credential_filepath)
        filenames = [attachment.split('/')[-1] for attachment in attachments]
        
        if to_recipient is None:
            to_recipient = 'success_mail'
        to = self.gmail_creds['to_mail_ids'][to_recipient]

        if len(to) == 0:
            print('No recipient mail id found. Mail not sent.')
            return
        
        if credentials == None:
            return print("Credentials not found.")
        try:
            service = build('gmail', 'v1', credentials=credentials)
            message = EmailMessage()
            tab_spaces = self.hf.config['common']['tab_spaces']
            html = f"<html><head></head><body>{message_text.replace('\n', '<br>').replace('\t', '&nbsp;' * tab_spaces)}</body></html>"
            body = MIMEText(html, 'html')
            message.set_content(body)
            if attachments:
                for i, attachment in enumerate(attachments):
                    with open(attachment, 'rb') as content_file:
                        content = content_file.read()
                        message.add_attachment(content, maintype='application', subtype=(
                            attachment.split('.')[1]), filename=filenames[i])
            message['To'] = to
            message['Subject'] = subject
    
            # encoded message
            encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
                .decode()
    
            create_message = {
                'raw': encoded_message
            }
            send_message = (service.users().messages().send(userId="me", body=create_message).execute())
            return send_message["id"]
        except HttpError as error:
            print(F'An error occurred while sending mail: {error}')
            send_message = None

    def prepare_mail_content(self):
        '''
        Method to prepare the subject, the body and the attachment files for the mail.
        '''   
        message = ""
        message += self.mail_header

        subject = self.mail_subject
        message += '\n' + self.mail_footer

        to_recipient = 'success_mail'

        files = {}
        
        return subject, message, files, to_recipient


    def prepare_data_and_send_mail(self):
        # Prepare the subject, mail body and the attchment files
        subject, mail_body, files, to_recipient = self.prepare_mail_content()
        
        attachments = []
        for key in files.keys():
            file = files[key]
            if not os.path.exists(self.temp_folder):
                os.mkdir(self.temp_folder)
            filepath = os.path.join(self.temp_folder, key+'.csv')
            attachments.append(filepath)
            file.to_csv(filepath, index=False)
            print(f'File "{key}" saved locally.')
        
        print(f'Subject: {subject}\n')
        print(f'Body: \n{mail_body}\n')

        # Download the latest credentials from s3
        credential_filepath = self.download_temp_json()
        
        # Send the mail
        mail_id = self.send_mail(subject, mail_body, attachments, to_recipient)
        if mail_id is not None:
            print('Successfully send mail, ID:', mail_id)
        else:
            print('E-mail not sent.')
        
        # Delete static files
        files_to_delete = [credential_filepath, *attachments]
        self.delete_temp_data(files_to_delete)


    def send_error_mail(self, error_message):
        # Download the latest credentials from s3
        credential_filepath = self.download_temp_json()

        subject = self.error_mail_subject
        mail_body = error_message + '\n\n' + self.error_mail_footer
        attachments = []

        mail_id = self.send_mail(subject, mail_body, attachments, 'error_mail')
        if mail_id is not None:
            print('Successfully send error mail, ID:', mail_id)
        else:
            print('E-mail not sent.')
