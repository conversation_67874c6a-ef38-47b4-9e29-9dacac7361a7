# DS-Inference-Scripts

A comprehensive data science inference pipeline for financial modeling and predictions, supporting multiple model types and deployment strategies.

## 🏗️ **Project Overview**

This repository contains production-ready inference scripts for various financial models including:

- **Best Model Scripts**: Model comparison and selection for AIEQ/AIGO portfolios
- **ER Model Scripts**: Earnings return prediction models
- **Financial Model Scripts**: Financial data-based prediction models  
- **Information Model Scripts**: News sentiment and information-based models
- **ETF Model Scripts**: Exchange-traded fund prediction models
- **Management Model Scripts**: Management quality assessment models
- **Phase Model Scripts**: Market phase detection models
- **LSTM Model Scripts**: Long short-term memory neural network models
- **TTM Model Scripts**: Time series foundation models (TinyTimeMixer)
- **Macro Model Scripts**: Macroeconomic indicator models
- **RORO Model Scripts**: Risk-on/Risk-off market regime models
- **Shared Utilities Package**: Standardized utilities for configuration, logging, email, and error handling

## 📁 **Directory Structure**

```
DS-Inference-Scripts/
├── best_model_scripts/          # Model comparison and selection
├── er_model_scripts/            # Earnings return predictions
├── fin_model_scripts/           # Financial model predictions
├── info_model_scripts/          # Information/sentiment models
├── etf_model_scripts/           # ETF prediction models
├── management_model_scripts/    # Management quality models
├── phase_model_scripts/         # Market phase detection
├── lstm_model_scripts/          # LSTM neural networks
├── ttm_model_scripts/           # Time series foundation models
├── macro_model_scripts/         # Macroeconomic models
├── roro_model_scripts/          # Risk regime models
├── aigo_signals/                # AIGO signal generation
├── aigo_wordcloud/              # Word cloud analysis
├── explainability_scripts/      # Model explainability (SHAP)
├── portfolio_optimization_scripts/ # Portfolio optimization
├── vector_db_scripts/           # Vector database operations
├── streamlit/                   # Streamlit dashboards
├── delivery/                    # Client delivery scripts
└── Model_Obser/                 # Model monitoring
```

## 🚀 **Quick Start**

### Prerequisites

- Python 3.9, 3.10, or 3.11
- AWS credentials configured
- IBM Watson ML credentials
- S3 access for data storage

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd DS-Inference-Scripts
   ```

2. **Install dependencies for specific models:**
   ```bash
   # For best model scripts
   cd best_model_scripts
   pip install -r requirements.txt
   
   # For ETF models
   cd ../etf_model_scripts
   pip install -r requirements.txt
   ```

3. **Install Shared Utilities Package:**
   ```bash
   # Install the shared utilities package
   cd shared_utils
   pip install -e .
   ```

4. **Configure credentials:**
   - Update configuration files in each model directory
   - Ensure AWS and IBM credentials are properly set

## 📦 **Shared Utilities Package**

The `shared_utils` package provides standardized utilities across all model scripts:

### Features
- **Configuration Management**: Unified YAML configuration loading with validation
- **Logging Framework**: Consistent logging with automatic rotation and context
- **Email Operations**: Multi-provider email support with templates and attachments
- **Error Handling**: Structured error hierarchy with severity levels and recovery

### Usage
```python
from shared_utils import (
    load_config, create_model_logger, create_email_sender,
    create_error_handler, ErrorSeverity
)

# Load configuration
config = load_config('config.yaml')

# Setup logging
logger = create_model_logger('my_model', 'daily', 'daily', '2024-01-01')

# Setup error handling
error_handler = create_error_handler(logger)
```

For detailed installation and usage instructions, see [SHARED_UTILS_INSTALLATION_GUIDE.md](SHARED_UTILS_INSTALLATION_GUIDE.md).

### Basic Usage

**Run Best Model Analysis:**
```bash
cd best_model_scripts
python main.py aieq monthly 2024-01-15
```

**Run ETF Model Pipeline:**
```bash
cd etf_model_scripts
python etf_models_trigger_main.py
```

**Run Information Model:**
```bash
cd info_model_scripts
python lexisnexis.py aieq
```

## 📊 **Model Types & Schedulers**

### Supported Tags (Universes)
- `aieq`: AI Equity portfolio
- `aigo`: AI Growth Opportunities
- `indt1`: India Tier 1
- `tier1`: US Tier 1
- `aifint`: AI Financial Technology
- `indsec`: India Securities

### Scheduler Types
- `daily`: Daily predictions and metrics
- `monthly`: Monthly model runs
- `quarterly`: Quarterly analysis
- `weekly`: Weekly updates (limited models)

## 🔧 **Configuration Management**

Each model directory contains its own configuration:

- **YAML configs**: `config.yaml` files for most models
- **Properties files**: Some ETF scripts use `.properties` format
- **JSON configs**: Credential files and specific configurations

### Environment Variables
Set the following environment variables:
```bash
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret
export IBM_API_KEY=your_ibm_key
```

## 📈 **Monitoring & Logging**

- **Model Monitoring**: `Model_Obser/` directory contains monitoring scripts
- **Logs**: Each model generates logs in respective directories
- **Email Notifications**: Automated success/failure notifications
- **Metrics Tracking**: Performance metrics stored in ElasticSearch

## 🧪 **Testing**

Currently, testing infrastructure is limited. Recommended approach:

1. **Unit Tests**: Test individual functions and utilities
2. **Integration Tests**: Test end-to-end model pipelines
3. **Data Quality Tests**: Validate input/output data integrity

## 📚 **Documentation**

- **Model-specific READMEs**: Check individual directories
- **API Documentation**: Available for TTM models
- **Configuration Guides**: In respective model directories

## 🤝 **Contributing**

1. Follow existing code patterns in each directory
2. Update configuration files appropriately
3. Add proper error handling and logging
4. Test thoroughly before deployment

## 🔒 **Security Notes**

- **Credentials**: Never commit credentials to version control
- **API Keys**: Store in secure configuration management
- **S3 Access**: Use IAM roles with minimal required permissions

## 📞 **Support**

For issues and questions:
- Check model-specific documentation
- Review log files for error details
- Contact the data science team

## 🏷️ **Version Information**

- **Python**: 3.9+ required
- **Key Dependencies**: pandas, numpy, scikit-learn, boto3
- **Cloud Services**: AWS S3, IBM Watson ML, ElasticSearch

---

**Note**: This is a production system handling financial data. Always test changes in development environments before deploying to production.
