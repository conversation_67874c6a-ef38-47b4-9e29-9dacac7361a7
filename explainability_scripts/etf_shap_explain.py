## xgboost_shap_explain

from utils import *
from shap_helper import *
import concurrent.futures
from datetime import datetime,timedelta
from pandas.tseries.offsets import BDay

import warnings
warnings.filterwarnings("ignore", category=UserWarning)

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.getcwd())

current_year = datetime.now().year

AWS_S3_BUCKET = 'histetfdata'
AWS_S3_BUCKET_SHAP = 'micro-ops-output'
s3_folder = 'etf_shapely/lgbm_data'

file_save_path = f'test/SHAP/etf/{current_year}'
es_path = 'eq_etf_model'
prod_es_path = 'eq_platform_master'
cutoff_year = 2024 #datetime.now().year - 1
schedular = 'Monthly'
es_index = f'shap_signal_strength_{es_path}'

etfs =  ['XLI','XLV','DBEEEMGF','MQFIUSTU','SHY','HYG','TLT','EMB','XLB','DBEEURGF','XLE','XLC','XLY','IYR','MQFIUSUS','MQFIUSTY','QQQ','LQD','HSMETYSN','XLP','XLRE','DBEEUNGF','XLU','IWM','XLK','EWJ','EFA','DBEETGFU','GLD','BNDX','TIP','SPY','HSMETYV3','EEM','DBEEUGFT','DBDRUS20','DBDRUS10','XME','XLF','DBDRUS02','DBRCOYGC']

excluded_columns = ['date','isin','tic','actual_monthly_returns']

to = ['<EMAIL>','<EMAIL>']

def get_data_model(etf):
    '''
    Getting and preparing data for particular etf
    Calls the get_date_features
    '''
    try:
        model= read_pickle(AWS_S3_BUCKET,f'{s3_folder}/model/{etf}_lightgbm_model_{cutoff_year}.pkl')
        scaler = read_pickle(AWS_S3_BUCKET,f'{s3_folder}/scaler/{etf}_scaler_{cutoff_year}.pkl')    
        scaler_features = scaler.feature_names_in_
        return model,scaler,scaler_features
    except Exception as e:
        print(f"Cannot calculate for isin:{etf} due to exception:{e}")

def get_explanation(etf,df,predction_data):
    '''Get summary reports for a given isin on given date'''
    
    print(f"Starting for etf {etf}")
    FLAG = 1
    isin = etf_dict[etf]
    data = df[df['isin']==isin]
    data.drop_duplicates(subset=['date'],keep='first',inplace=True)
    data.reset_index(inplace=True)
    if data.empty:
        print(f"Data empty for isin {isin}")
        return 
    model,scaler,scaler_features = get_data_model(etf) or (None, None, None)
    try:
        final_pred = predction_data[predction_data['isin']==isin]['er'].item()*100
        prediction = data['monthly_predictions'].item()
        data = scaler.transform(data[scaler_features])
        local_pred = model.predict(data)[0]
    except Exception as e:
        print(f"{isin} Exception: {e}")
        return 

    try:
        explainer = shap.TreeExplainer(model)
        shap_summary = get_summary(isin,explainer,data,scaler_features)
    except Exception as e:
        print(f"Cannot generate report due to {e}")
        return 
    
    base_value = shap_summary['Shap_Base_Value'].item()
    shap_value_cols = [col for col in shap_summary.columns if col.startswith('Shap_Value_')]
    total_shap_sum = shap_summary[shap_value_cols].sum(axis=1).item()
    scaling_factor = (final_pred - base_value) / total_shap_sum
    shap_summary[shap_value_cols] *= scaling_factor
    shap_total = shap_summary[shap_value_cols].sum(axis=1).item()
    shap_pred = base_value + shap_total

    if abs(final_pred-shap_pred)>0.1:
        FLAG = 0

    print(f"Finished for isin {isin}")
    return shap_summary,shap_pred,final_pred,local_pred,prediction,FLAG

if __name__ == "__main__":
    etf_dict = get_etf_dict()
    isins = [v for k,v in etf_dict.items() if k in etfs]
    date = (datetime.today()- BDay(1)).strftime('%Y-%m-%d')
    print(f"Calculating for {date}")

    send_mail(f"Explainability: ETFs Monthly Triggered {date}", 
                         f"Explainability ETFs Monthly daily run has started for {date}\nRunning for {len(isins)} isins")
    
    df = get_es_data_date(es_prod, isins, date, es_path, schedular='Monthly')
    prediction_data = get_es_data_date(es_train, isins, date, prod_es_path, schedular='Monthly')

    if df is None or df.empty or prediction_data is None or prediction_data.empty:
        print(f"No data available for {date}")
        prev_date = (datetime.strptime(date, '%Y-%m-%d') - BDay(1)).strftime('%Y-%m-%d')
        try:
            s3_key = f'{file_save_path}/report/shap_summary/{prev_date}.csv'
            
            response = s3.get_object(Bucket=AWS_S3_BUCKET_SHAP, Key=s3_key)
            csv_content = response['Body'].read().decode('utf-8')
            shap_summary = pd.read_csv(StringIO(csv_content))
            
            shap_summary['forward_fill'] = True
            shap_summary['date_f'] = prev_date

            try:
                file_prefix = 'ETF_Monthly'
                
                send_mail(f"Forward Fill Alert - SHAP Summary for {file_prefix}:{date}", 
                         f"No data available for {date}. Forward filled SHAP summary from {prev_date} has been uploaded.",to=to)
                print("Forward fill triggered - email notification sent")
            except Exception as email_error:
                print(f"Email notification failed: {email_error}")
        except Exception as e:
            print(f"Error in forward filling: {e}")
            sys.exit(1)
    else:
        print(f"Starting \nTotal etfs: {len(isins)}")
        get_explanations = lambda x: get_explanation(x, df, prediction_data)
        with concurrent.futures.ThreadPoolExecutor(7) as executor:
            report = list(executor.map(get_explanations, etfs))
            
        if report:
            shap_summary = pd.concat([rep[0] for rep in report if rep])
            shap_summary['shap_sum'] = [rep[1] for rep in report if rep]
            shap_summary['final_pred'] = [rep[2] for rep in report if rep]
            shap_summary['local_pred'] = [rep[3] for rep in report if rep]
            shap_summary['autoai_pred'] = [rep[4] for rep in report if rep]
            shap_summary['Success_Flag'] = [rep[5] for rep in report if rep]
            shap_summary.index.name = 'isin'
            shap_summary.reset_index(inplace=True)

    write_to_s3_bucket(shap_summary, AWS_S3_BUCKET_SHAP, f'{file_save_path}/report/shap_summary/{date}.csv')
    shap_summary = convert_report_es(shap_summary,date,schedular)
    es_prod.save_records_v2(shap_summary,es_index)
    send_mail(f"Explainability: ETFs Monthly Completed {date}", 
                         f"Explainability ETFs Monthly daily run has finished for date {date}\nRecords Saved :{shap_summary.shape[0]}")
    print("Finished")   