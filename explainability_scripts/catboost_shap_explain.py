## catboost_shap_explain

from utils import *
from shap_helper import *
import concurrent.futures
from datetime import datetime,timedelta
from pandas.tseries.offsets import BDay

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.getcwd())

current_year = datetime.now().year

AWS_S3_BUCKET = 'portfolio-experiments-test'
AWS_S3_BUCKET_DATA = 'eq-model-output'
AWS_S3_BUCKET_SHAP = 'micro-ops-output'
s3_folder = 'aieq_historical_catboost/Monthly'
s3_data_path  = 'explainability_inputs/monthly'
train_data_path = 'aieq_training_catboost/Monthly/combined/features'

file_save_path = f'test/SHAP/catboost/{current_year}'
es_path = 'eq_cat_er_model'
cutoff_year = 2024 #datetime.now().year - 1
schedular = 'Monthly'
es_index = f'shap_signal_strength_{es_path}'

q_file = f'aieq_historical_catboost/Monthly/{cutoff_year}/queue_data/q_data.csv'
qdata =read_file(f'{AWS_S3_BUCKET}',f'{q_file}')
qdata = qdata[qdata['status']=='completed']
qdata['training_date'] = pd.to_datetime(qdata['timestamp'],format='%Y-%m-%d_%H:%M:%S')
ts_dict = dict(zip(qdata['isin'],qdata['timestamp']))

exclude_columns = ['isin', 'date','closeprice']

to = ['<EMAIL>','<EMAIL>','<EMAIL>']

derived_feature_map = {
    'date_year_sin': 'date_year',
    'date_month_sin': 'date_month',
    'date_week_sin': 'date_week',
    'date_day_sin': 'date_day',
    'date_dayofyear_sin': 'date_dayofyear'
}

def get_date_features(df):
    df['date'] = pd.to_datetime(df['date'])
    df['date_year'] = df['date'].dt.year - 2000             # Year adjusted by subtracting 2000
    df['date_month'] = df['date'].dt.month                  # Month
    df['date_week'] = df['date'].dt.isocalendar().week      # Week of the year (ISO)
    df['date_day'] = df['date'].dt.day                      # Day of the month
    df['date_dayofweek'] = df['date'].dt.dayofweek          # Day of the week (0=Monday, 6=Sunday)
    df['date_dayofyear'] = df['date'].dt.dayofyear          # Day of the year
    df['date_quarter'] = df['date'].dt.quarter              # Quarter of the year

    df['date_week'] = df['date_week'].astype('float64')
    return df

def get_derived_features(df):
    features = {
    'date_year': 67,
    'date_month': 13,
    'date_week': 53,
    'date_day': 31,
    'date_dayofyear': 367,
    }

    for col, divisor in features.items():
        if col in df.columns:
            df[f'{col}_sin'] = np.sin(2 * np.pi * df[col] / divisor)
    return df

def make_prediction(input,model,overall_imp_features,feature_dict,scaler):
    '''
    Modified prediction function for shap explainer
    Used to caluclate input feature importance directly by including 
    feature engineering inside the predict function 
    '''
    df = input.copy(deep=True)
    df = get_derived_features(df)
    final_df=pd.DataFrame()
    for idx,k in enumerate(overall_imp_features):
        try:
            final_df[k]=eval(feature_dict[k])
        except:
            final_df[k]=np.nan
    final_df_scaled_values=scaler.transform(final_df)
    final_scaled_df=pd.DataFrame(final_df_scaled_values,columns=final_df.columns)
    return model.predict(final_scaled_df) 

def get_features_used(feature_dict,overall_imp_features):
    '''
    Function to get the list of input features used from the
    dict of derived features. Removes some of the custom features also
    '''
    pattern = r'df\["(.*?)"\]'
    # Find all matches }
    feature_mapping  = {}
    for (feat_name,feat_def) in feature_dict.items():
        if feat_name in overall_imp_features:
            matches = re.findall(pattern, feat_def)
            feature_mapping[feat_name] = matches
    features_used = set()
    for feats in  feature_mapping:
        for feat in feature_mapping[feats]:
            features_used.add(feat)
    features = set()
    for feat in features_used:
        if feat in derived_feature_map:
            features.add(derived_feature_map[feat])
        else:
            features.add(feat)
    
    return list(features)

def get_data_model(isin):
    '''
    Getting and preparing data for particular isin
    Calls the get_date_features
    '''
    try:
        
        df = read_file(AWS_S3_BUCKET,f'{train_data_path}/{isin}.csv')
        df.drop_duplicates(subset=['date'],keep='first',inplace=True)
        df = get_date_features(df)
        df = get_derived_features(df)
        overall_imp_features = read_pickle(AWS_S3_BUCKET,f'{s3_folder}/{cutoff_year}/predictions/{isin}/overall_imp_features_{isin}_{ts_dict[isin]}.pkl')
        feature_dict = read_pickle(AWS_S3_BUCKET,f'{s3_folder}/{cutoff_year}/predictions/{isin}/overall_feature_dict_{isin}_{ts_dict[isin]}.pkl')
        bst = read_pickle(AWS_S3_BUCKET,f'{s3_folder}/{cutoff_year}/predictions/{isin}/final_catboost_model_{isin}_{ts_dict[isin]}.pkl')
        scaler = read_pickle(AWS_S3_BUCKET,f'{s3_folder}/{cutoff_year}/predictions/{isin}/min_max_scaler_{isin}_{ts_dict[isin]}.pkl')
        scaler.clip = False
        predict = lambda x: make_prediction(x,bst,overall_imp_features,feature_dict,scaler)
        feature_used = get_features_used(feature_dict,overall_imp_features)
        return df,predict,feature_used
    except Exception as e:
        print(f"Cannot calculate for isin:{isin} due to exception:{e}")
        return None,None,None


def get_explanation(isin,date,data_isin):
    '''Get summary reports for a given isin on given date'''
    print(f"Starting for isin {isin}")
    FLAG = 1
    error = ""
    df, predict,feature_used = get_data_model(isin)
    if df is None:
        print(f"No data for isin {isin} in S3")
        return 
    data = data_isin[data_isin['isin']==isin]

    if data.empty:
        print(f"Data empty for isin {isin}")
        return 
    training_date = (qdata[qdata['isin']==isin]['training_date']-BDay(30)).dt.strftime('%Y-%m-%d').item()
    df= df[(df['date']<training_date)]
    try:
        prediction = data['actual_monthly_return_predictions'].item()    
        df = df[feature_used]
        data = data[feature_used]
    except:
        print(f"{isin} does not have all features")
        return 
    
    try:
        explainer = shap.PermutationExplainer(predict,df)
        shap_summary = get_summary(isin,explainer,data,feature_used)
    except Exception as e:
        print(f"Cannot generate report due to {e}")
        return 
    shap_pred = shap_summary['Shap_Base_Value'].item() + shap_summary[[col for col in shap_summary.columns if col.startswith('Shap_Value_')]].sum(axis=1).item()
    local_pred = predict(data)[0]
    if abs(prediction-shap_pred)>0.1:
        FLAG = 0
        if (prediction-local_pred)>=0.1:
            error += '. Online prediction does not match'
        else:
            error+=" .Catboost shap value not matching"
    print(f"Finished for isin {isin}")
    return shap_summary,FLAG,error,shap_pred,prediction,local_pred

if __name__ == "__main__":
    isins = qdata['isin'].to_list()
    print(f"Starting \nTotal isins: {len(isins)}")
    date = (datetime.today()- BDay(1)).strftime('%Y-%m-%d')
    print(f"Calculating for {date}")

    send_mail(f"Explainability: CATBOOST Monthly Triggered {date}", 
                         f"Explainability CATBOOST Monthly daily run has started for {date}\nRunning for {len(isins)} isins")
    
    try:
        data = read_file(AWS_S3_BUCKET_DATA, f'{s3_data_path}/{date}/cat_er_data.csv')
    except Exception as e:
        print(f"Error reading data file: {e}")
        data = None
    
    if data is None or data.empty:
        print(f"No data available for {date}")
        prev_date = (datetime.strptime(date, '%Y-%m-%d') - BDay(1)).strftime('%Y-%m-%d')
        try:
            s3_key = f'{file_save_path}/report/shap_summary/{prev_date}.csv'
            
            response = s3.get_object(Bucket=AWS_S3_BUCKET_SHAP, Key=s3_key)
            csv_content = response['Body'].read().decode('utf-8')
            shap_summary = pd.read_csv(StringIO(csv_content))
            
            shap_summary['forward_fill'] = True
            shap_summary['date_f'] = prev_date

            try:
                file_prefix = 'CATBOOST'
                
                send_mail(f"Forward Fill Alert - SHAP Summary for {file_prefix} :{date}", 
                         f"No data available for {date}. Forward filled SHAP summary from {prev_date} has been uploaded.",to=to)
                print("Forward fill triggered - email notification sent")
            except Exception as email_error:
                print(f"Email notification failed: {email_error}")
        except Exception as e:
            print(f"Error in forward filling: {e}")
            sys.exit(1)
    else:
        get_explanations = lambda x: get_explanation(x, date, data)
        with concurrent.futures.ThreadPoolExecutor(7) as executor:
            report = list(executor.map(get_explanations, isins))
            
        if report:
            shap_summary = pd.concat([rep[0] for rep in report if rep])
            shap_summary['Success_Flag'] = [rep[1] for rep in report if rep]
            shap_summary['ERROR'] = [rep[2] for rep in report if rep]
            shap_summary['shap_sum'] = [rep[3] for rep in report if rep]
            shap_summary['final_pred'] = [rep[4] for rep in report if rep]
            shap_summary['local_pred'] = [rep[5] for rep in report if rep]
            shap_summary.index.name = 'isin'
            shap_summary.reset_index(inplace=True)
            shap_summary = shap_summary.replace({float('nan'): None})

    write_to_s3_bucket(shap_summary, AWS_S3_BUCKET_SHAP, f'{file_save_path}/report/shap_summary/{date}.csv')
    shap_summary = convert_report_es(shap_summary, date, schedular)
    es_prod.save_records_v2(shap_summary, es_index)
    send_mail(f"Explainability: CATBOOST Monthly Completed {date}", 
                         f"Explainability CATBOOST Monthly daily run has finished for date {date}\nRecords Saved :{shap_summary.shape[0]}")
    print("Finished")