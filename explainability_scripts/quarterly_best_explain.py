## best_er_shap_explain
from utils import *
from shap_helper import *
import concurrent.futures
from datetime import datetime,timedelta
from pandas.tseries.offsets import BDay

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.getcwd())

current_year = datetime.now().year

AWS_S3_BUCKET = 'portfolio-experiments-test'
AWS_S3_BUCKET_SHAP = 'micro-ops-output'
file_save_path = f'test/SHAP/best_model_quarterly/{current_year}'

test_es_path = 'eq_best_model'
schedular = 'Quarterly'
es_index = f'shap_signal_strength_{test_es_path}'

cutoff_year = 2024 #datetime.now().year - 1
q_file = f'aieq_historical_xgboost/Quarterly/{cutoff_year}/queue_data/q_data.csv'
qdata =read_file(f'{AWS_S3_BUCKET}',f'{q_file}')
qdata = qdata[qdata['status']=='completed']
qdata['training_date'] = pd.to_datetime(qdata['timestamp'],format='%Y-%m-%d_%H:%M:%S')
ts_dict = dict(zip(qdata['isin'],qdata['timestamp']))

to = ['<EMAIL>','<EMAIL>','<EMAIL>']

def get_smoothening_gradients():
    gradient_size = 5
    multiplier = 1 / (np.e * (np.e ** gradient_size - 1) / (np.e - 1))
    smoothening_gradient = [multiplier * (np.e ** (i + 1)) for i in range(gradient_size)]
    return smoothening_gradient[::-1]

smoothening_gradient =  get_smoothening_gradients()

def get_best_model_summary(isin,scores,base_value):
    '''Generates report for best_er SHAP explanations'''
    N = len(scores)
    cols = get_shap_format(N)
    model_shap_summary = pd.DataFrame(columns = cols)
    shap_sum = sum(abs(scores))
    sorted_index = abs(scores).argsort()[::-1]
    top_contributions = scores[sorted_index]
    row = []
    row.append(base_value)
    for j in range(N):
        row.append(scores.index[sorted_index[j]])
        row.append(top_contributions[j])
        row.append(top_contributions[j]/shap_sum*100)
    model_shap_summary.loc[isin] = row
    model_shap_summary.index.name = 'isin'
    return model_shap_summary

def get_explanation(isin,dates,shap_report,final_predictions):
    '''Get summary reports for a given isin on given date'''
    print(f"Starting for isin {isin}")
    try:
        
        shap_isin = shap_report[shap_report['isin']==isin]
        final_isin = final_predictions[final_predictions['isin']==isin]
        final_pred = final_isin[final_isin['date']==dates[0]]['predicted_er'].item()
        base_values = []
        scaled_scores = []
        predictions = []
        FLAG = 1
        error = ""
        
        expected_dates = pd.to_datetime(dates)
        shap_isin = process_isin_data(shap_isin, expected_dates)
        final_isin = process_isin_data(final_isin, expected_dates)

        for i,date in enumerate(dates):
            shap_rep = shap_isin[shap_isin['date']==date]
            shap_rep.drop('date',axis=1,inplace=True)
            raw_pred = final_isin[final_isin['date']==date]['raw_er'].item()
            feature_scores  = get_shap_series(shap_rep)
            xgboost_shap = sum(feature_scores)+shap_rep['Shap_Base_Value'].item()
                
            if abs(raw_pred-xgboost_shap)>0.1:
                FLAG = 0
                error = 'raw er does not match xgboost value'
            predictions.append(raw_pred*smoothening_gradient[i])
            base_values.append(shap_rep['Shap_Base_Value'].item()*smoothening_gradient[i])
            scaled_scores.append(feature_scores*smoothening_gradient[i])
        
        smoothened_scores = pd.concat(scaled_scores, axis=1).fillna(0).sum(axis=1)
        base_value = sum(base_values)
        smoothened_prediction = sum(predictions)
        shap_summary = get_best_model_summary(isin,smoothened_scores,base_value)
        shap_sum = sum(smoothened_scores)+base_value
        
        if abs(shap_sum-final_pred)>0.1 and FLAG: # If flag was not hit before, don't update the main error
            FLAG = 0
            error = "SHAP_SUM does not match final prediction"
            if abs(smoothened_prediction-final_pred)>0:
                error = "smoothened prediction does not match final prediction"
        print(f"Finished for isin {isin}")
        
        return shap_summary,FLAG,error,shap_sum,final_pred
    except Exception as e:
        print(f"Failed for isin {isin} due to Exception: {e}")


if __name__ == "__main__":
    isins = qdata['isin'].to_list()
    print(f"Starting \nTotal isins: {len(isins)}")
    
    date = (datetime.today() - BDay(1))
    print(f"Calculating for {date.strftime('%Y-%m-%d')}")

    send_mail(f"Explainability: BEST Quarterly Triggered {date}", 
                         f"Explainability BEST Quarterly daily run has started for {date}\nRunning for {len(isins)} isins")
    
    dates = []
    shap_reports = []
    test_data = []

    for i in range(5):
        curr_date = (date - BDay(i)).strftime('%Y-%m-%d')
        dates.append(curr_date)
        xgboost_path  = f"test/SHAP/xgboost/{curr_date.split('-')[0]}/report/shap_summary"
        shap_report = read_file(AWS_S3_BUCKET_SHAP,f'{xgboost_path}/{curr_date}.csv')
        if shap_report is not None and not shap_report.empty:  
            shap_report['date'] = curr_date
        shap_reports.append(shap_report)
        test_data.append(get_es_data_date(es_prod,isins,curr_date,test_es_path,schedular))

    date = date.strftime('%Y-%m-%d')
    if (len(shap_reports) > 0 and shap_reports[0] is None):
        print("Data missing \nForward filling")
        prev_date = (datetime.strptime(date, '%Y-%m-%d') - BDay(1)).strftime('%Y-%m-%d')
        try:
            s3_key = f'{file_save_path}/report/shap_summary/{prev_date}.csv'
            
            response = s3.get_object(Bucket=AWS_S3_BUCKET_SHAP, Key=s3_key)
            csv_content = response['Body'].read().decode('utf-8')
            shap_summary = pd.read_csv(StringIO(csv_content))
            
            shap_summary['forward_fill'] = True
            shap_summary['date_f'] = prev_date

            try:
                file_prefix = 'BEST_Quarterly'
                
                send_mail(f"Forward Fill Alert - SHAP Summary for {file_prefix}:{date}", 
                         f" No data available for {date}. Forward filled SHAP summary from {prev_date} has been uploaded.",to=to)
                print(f" Forward fill triggered - email notification sent")
            except Exception as email_error:
                print(f"Email notification failed: {email_error}")
        except Exception as e:
            print(f"Error in forward filling: {e}")
    else:     
        shap_report = pd.concat([x for x in shap_reports if x is not None])
        final_predictions = pd.concat([x for x in test_data if x is not None])
        
        get_explanations = lambda x : get_explanation(x,dates,shap_report,final_predictions)
        with concurrent.futures.ThreadPoolExecutor(7) as executor:
            report = list(executor.map(get_explanations, isins))
        if report:
            shap_summary = pd.concat([ rep[0] for rep in report if rep])
            shap_summary['Success_Flag'] = [rep[1] for rep in report if rep]
            shap_summary['ERROR'] = [ rep[2] for rep in report if rep]
            shap_summary['shap_sum'] =  [rep[3] for rep in report if rep]
            shap_summary['final_pred'] = [ rep[4] for rep in report if rep]
            shap_summary.index.name = 'isin'
            shap_summary.reset_index(inplace=True)
            shap_summary = shap_summary.replace({float('nan'): None})
    write_to_s3_bucket(shap_summary, AWS_S3_BUCKET_SHAP, f'{file_save_path}/report/shap_summary/{date}.csv')
    shap_summary = convert_report_es(shap_summary,date,schedular)
    es_prod.save_records_v2(shap_summary,es_index)
    send_mail(f"Explainability: BEST Quarterly Completed {date}", 
                         f"Explainability BEST Quarterly daily run has finished for date {date}\nRecords Saved :{shap_summary.shape[0]}")
    print("Finished")   