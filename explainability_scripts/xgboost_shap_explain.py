## xgboost_shap_explain

from utils import *
from shap_helper import *
import concurrent.futures
from datetime import datetime,timedelta
from pandas.tseries.offsets import BDay

import warnings
warnings.filterwarnings("ignore", category=UserWarning)

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.getcwd())

current_year = datetime.now().year

AWS_S3_BUCKET = 'portfolio-experiments-test'
AWS_S3_BUCKET_DATA = 'eq-model-output'
AWS_S3_BUCKET_SHAP = 'micro-ops-output'
s3_folder = 'aieq_historical_xgboost/Quarterly'
s3_data_path  = 'explainability_inputs/quarterly'

file_save_path = f'test/SHAP/xgboost/{current_year}'
es_path = 'eq_xgb_er_model'
cutoff_year = 2024 #datetime.now().year - 1
schedular = 'Quarterly'
es_index = f'shap_signal_strength_{es_path}'

q_file = f'aieq_historical_xgboost/Quarterly/{cutoff_year}/queue_data/q_data.csv'
qdata =read_file(f'{AWS_S3_BUCKET}',f'{q_file}')
qdata = qdata[qdata['status']=='completed']
qdata['training_date'] = pd.to_datetime(qdata['timestamp'],format='%Y-%m-%d_%H:%M:%S')
ts_dict = dict(zip(qdata['isin'],qdata['timestamp']))

exclude_scaling = ['date_year_sin', 'date_month_sin', 'date_week_sin', 'date_day_sin', 'date_dayofyear_sin']
exclude_columns = ['isin', 'date','closeprice','actual_quarterly_return_predictions']

to = ['<EMAIL>','<EMAIL>','<EMAIL>']

def scale_df(df, scaler, feature_used):
    scaling_features = [feature for feature in feature_used if feature not in exclude_scaling]
    final_df = df[scaling_features].copy() 
    final_df_scaled_values = scaler.transform(final_df)
    final_scaled_df = pd.DataFrame(final_df_scaled_values, columns=scaling_features)
    
    for feature in exclude_scaling:
        if feature in feature_used:
            final_scaled_df[feature] = df[feature]

    return final_scaled_df[feature_used]

def get_data_model(isin):
    '''
    Getting and preparing data for particular isin
    Calls the get_date_features
    '''
    try:
        model = read_pickle(AWS_S3_BUCKET,f'{s3_folder}/{cutoff_year}/predictions/{isin}/final_xgboost_model_{isin}_{ts_dict[isin]}.pkl')
        scaler = read_pickle(AWS_S3_BUCKET,f'{s3_folder}/{cutoff_year}/predictions/{isin}/min_max_scaler_{isin}_{ts_dict[isin]}.pkl')
        scaler.clip = False
        feature_used = list(model.feature_names_in_)
        return model,feature_used,scaler
    except Exception as e:
        print(f"Cannot calculate for isin:{isin} due to exception:{e}")

def get_explanation(isin,date,df_isin):
    '''Get summary reports for a given isin on given date'''
    print(f"Starting for isin {isin}")
    FLAG = 1
    error = ""
    df = df_isin[df_isin['isin']==isin]

    if df is None:
        print(f"No data for isin {isin} in S3")
        return 
    df.sort_values(by='date',inplace=True)
    df.drop_duplicates(subset=['date'],keep='first',inplace=True)
    df.reset_index(inplace=True)

    ffill_dates = (datetime.strptime(date,'%Y-%m-%d') - timedelta(22)).strftime('%Y-%m-%d')
    mask = df["date"] > ffill_dates
    columns_to_fill = df.columns.difference(exclude_columns)
    df.loc[mask, columns_to_fill] = df.loc[mask, columns_to_fill].ffill()

    data = df[(df['date']==date)]

    if data.empty:
        print(f"Data empty for isin {isin}")
        return 

    model,feature_used,scaler = get_data_model(isin) or (None, None, None)
    try:
        prediction = data['actual_quarterly_return_predictions'].item()    
        data = scale_df(data,scaler,feature_used)
    except Exception as e:
        print(f"{isin} Exception: {e}")
        return 
    
    try:
        explainer = shap.TreeExplainer(model)
        shap_summary = get_summary(isin,explainer,data,feature_used)
    except Exception as e:
        print(f"Cannot generate report due to {e}")
        return 
    
    shap_pred = shap_summary['Shap_Base_Value'].item() + shap_summary[[col for col in shap_summary.columns if col.startswith('Shap_Value_')]].sum(axis=1).item()
    local_pred = model.predict(data)[0]
    if abs(prediction-shap_pred)>0.1:
        FLAG = 0
        if abs(prediction-local_pred)>=0.1:
            error = "Online prediction does not match"
        else:
            error = "Xgboost shap value not matching"
    print(f"Finished for isin {isin}")
    return shap_summary,FLAG,error,shap_pred,prediction,local_pred

if __name__ == "__main__":
    isins = qdata['isin'].to_list()
    print(f"Starting \nTotal isins: {len(isins)}")
    date = (datetime.today()- BDay(1)).strftime('%Y-%m-%d')

    send_mail(f"Explainability: XGBOOST Quarterly Triggered {date}", 
                         f"Explainability XGBOOST Quarterly daily run has started for {date}\nRunning for {len(isins)} isins")

    data = read_file(AWS_S3_BUCKET_DATA, f'{s3_data_path}/{date}/xgb_er_data.csv')
        
    if data is None or data.empty:
        print(f"No data for isin {date} in S3")
        prev_date = (datetime.strptime(date, '%Y-%m-%d') - BDay(1)).strftime('%Y-%m-%d')
        try:
            s3_key = f'{file_save_path}/report/shap_summary/{prev_date}.csv'
            
            response = s3.get_object(Bucket=AWS_S3_BUCKET_SHAP, Key=s3_key)
            csv_content = response['Body'].read().decode('utf-8')
            shap_summary = pd.read_csv(StringIO(csv_content))
            
            shap_summary['forward_fill'] = True
            shap_summary['date_f'] = prev_date

            try:
                file_prefix = 'XGBOOST'
                send_mail(f"Forward Fill Alert - SHAP Summary for {file_prefix}: {date}", 
                         f" No data available for {date}. Forward filled SHAP summary from {prev_date} has been uploaded.",to=to)
                print(f" Forward fill triggered - email notification sent")
            except Exception as email_error:
                print(f"Email notification failed: {email_error}")
        except Exception as e:
            print(f"Error in forward filling: {e}")
            sys.exit()
    else:
        print(f"Calculating for {date}") 
        get_explanations = lambda x : get_explanation(x,date,data)
        with concurrent.futures.ThreadPoolExecutor(10) as executor:
            report = list(executor.map(get_explanations, isins))
        if report:
            shap_summary = pd.concat([rep[0] for rep in report if rep])
            shap_summary['Success_Flag'] = [rep[1] for rep in report if rep]
            shap_summary['ERROR'] = [ rep[2] for rep in report if rep]
            shap_summary['shap_sum'] =  [rep[3] for rep in report if rep]
            shap_summary['final_pred'] = [ rep[4] for rep in report if rep]
            shap_summary['local_pred'] = [ rep[5] for rep in report if rep]
            shap_summary.index.name = 'isin'
            shap_summary.reset_index(inplace=True)
            shap_summary = shap_summary.replace({float('nan'): None})

    write_to_s3_bucket(shap_summary, AWS_S3_BUCKET_SHAP, f'{file_save_path}/report/shap_summary/{date}.csv')
    shap_summary = convert_report_es(shap_summary,date,schedular)
    es_prod.save_records_v2(shap_summary,es_index)
    send_mail(f"Explainability: XGBOOST Quarterly Completed {date}", 
                         f"Explainability XGBOOST Quarterly daily run has finished for date {date}\nRecords Saved :{shap_summary.shape[0]}")
    print("Finished")