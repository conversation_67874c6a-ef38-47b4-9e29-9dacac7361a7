# shap_explanations

Contains all production code related to the SHAP Explanations.


## Implementation
Currently Implemented for:
1. LGBM
2. CatBoost


## Components 
1. Model specific  
    - Builder - create a shap explainer for each isin and uploads to S3. (To be removes)
    - Explain - Collects today's data and isin's explainer and generates a report.
2. utils.py   
    Helper functions for collecting and processing data
3. shap_helper.py   
    Helper functions specifically for shap report generation


TODO
- Refactor builder functionality
- Streamline data collection and report generation functions
- Implement Best_er
- Add documentation