## xgboost_shap_explain

from utils import *
from shap_helper import *
import concurrent.futures
from datetime import datetime,timedelta
from pandas.tseries.offsets import BDay
import warnings
warnings.filterwarnings("ignore", category=UserWarning)

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.getcwd())

current_year = datetime.now().year

AWS_S3_BUCKET_SHAP = 'micro-ops-output'

monthly_etf_path = f'test/SHAP/etf/{current_year}'
quarterly_etf_path = f'test/SHAP/etf_quarterly/{current_year}'

monthly_file_save_path = f'test/SHAP/multi_asset_monthly/{current_year}'
quarterly_file_save_path = f'test/SHAP/multi_asset_quarterly/{current_year}'

es_path = 'eq_etf_model'
prod_es_path = 'eq_platform_master'
cutoff_year = 2024 #datetime.now().year - 1
es_index = f'shap_signal_strength_{es_path}'

etfs =  ['BNDX','SPY','EFA','EEM','IWM']

to = ['<EMAIL>','<EMAIL>']

asset_isin_map = {
    'AOK': 'US4642898831',
    'AOM': 'US4642898757',
    'AOA':'US4642898591',
    'AOR':'US4642898674'}

asset_weights = {
    'AOK': {'BNDX': 69.92, 'SPY': 17.5, 'EFA': 7.97, 'EEM': 3.11, 'IWM': 0.48},
    'AOM': {'BNDX': 59.42, 'SPY': 23.8, 'EFA': 10.6, 'EEM': 4.15, 'IWM': 0.65},
    'AOA': {'BNDX': 19.62, 'SPY': 47.14, 'EFA': 21.01, 'EEM': 8.22, 'IWM': 1.28},
    'AOR': {'BNDX': 39.63, 'SPY': 34.79, 'EFA': 16.2, 'EEM': 6.19, 'IWM': 1.02}}

    
def get_explanation(data,prediction_data):
    '''Get weighted summary SHAP reports for each asset'''
    results = []
    for asset_name, weights in asset_weights.items():
        try:
            asset_isin = asset_isin_map[asset_name]
            final_pred = prediction_data[prediction_data['isin']==asset_isin]['er'].item()*100
            shap_series_list = []
            total_weight = 0
            base_value = 0
            for ticker, weight in weights.items():
                isin = etf_dict.get(ticker)

                shap_row = data[data['isin'] == isin]
                if shap_row.empty:
                    continue

                shap_series = get_shap_series(shap_row)  
                weighted_series = shap_series * weight
                total_weight += weight

                base_value += shap_row['Shap_Base_Value'].item()*weight
                shap_series_list.append(weighted_series)

            base_value = base_value/total_weight
            
            if shap_series_list and total_weight > 0:
                scores = pd.concat(shap_series_list, axis=1).fillna(0).sum(axis=1) / total_weight
                shap_sum = sum(scores)+base_value
                shap_df = get_summary_from_series(asset_isin,scores,base_value)
                shap_df['final_pred'] = final_pred
                shap_df['Success_Flag'] = 1
                if abs(shap_sum-final_pred)>0.1:
                    shap_df['Success_Flag'] = 0

                results.append(shap_df)
        except Exception as e:
            print(f"Failed for asset {asset_name} due to {e}")
    shap_summary = pd.concat(results)
    shap_summary.reset_index(inplace=True)
    return shap_summary

if __name__ == "__main__":
    etf_dict = get_etf_dict()
    isins = [v for k,v in etf_dict.items() if k in etfs]
    date = (datetime.today() - BDay(1)).strftime('%Y-%m-%d')
    print(f"Calculating for {date}")

    send_mail(f"Explainability: Multi-Asset Monthly+Quarterly Triggered {date}", 
                         f"Explainability Multi-Asset Monthly+Quarterly daily run has started for {date}\nRunning for {len(isins)} isins")

    try:
        monthly_shap_report = read_file(AWS_S3_BUCKET_SHAP, f'{monthly_etf_path}/report/shap_summary/{date}.csv')
        quarterly_shap_report = read_file(AWS_S3_BUCKET_SHAP, f'{quarterly_etf_path}/report/shap_summary/{date}.csv')
        
        prediction_data = get_es_data_date(es_train, list(asset_isin_map.values()), date, prod_es_path)
        prediction_monthly = prediction_data[prediction_data['schedular']=='Monthly']
        prediction_quarterly = prediction_data[prediction_data['schedular']=='Quarterly']

        monthly_summary = get_explanation(monthly_shap_report, prediction_monthly)
        quarterly_summary = get_explanation(quarterly_shap_report, prediction_quarterly)

    except Exception as e:
        print(f"Error processing data: {e}")
        prev_date = (datetime.strptime(date, '%Y-%m-%d') - BDay(1)).strftime('%Y-%m-%d')
        try:
            monthly_s3_key = f'{monthly_file_save_path}/report/shap_summary/{prev_date}.csv'
            quarterly_s3_key = f'{quarterly_file_save_path}/report/shap_summary/{prev_date}.csv'
            
            monthly_response = s3.get_object(Bucket=AWS_S3_BUCKET_SHAP, Key=monthly_s3_key)
            monthly_csv = monthly_response['Body'].read().decode('utf-8')
            monthly_summary = pd.read_csv(StringIO(monthly_csv))
            monthly_summary['forward_fill'] = True
            monthly_summary['date_f'] = prev_date

            quarterly_response = s3.get_object(Bucket=AWS_S3_BUCKET_SHAP, Key=quarterly_s3_key)
            quarterly_csv = quarterly_response['Body'].read().decode('utf-8')
            quarterly_summary = pd.read_csv(StringIO(quarterly_csv))
            quarterly_summary['forward_fill'] = True
            quarterly_summary['date_f'] = prev_date

            try:
                file_prefix = 'Multi_Asset'
                
                send_mail(
                    f"Forward Fill Alert - ETF Reports for {file_prefix}:{date}", 
                    f"No data available for {date}. Forward filled reports from {prev_date} have been uploaded.", to = to
                )
                print("Forward fill triggered - email notification sent")
            except Exception as email_error:
                print(f"Email notification failed: {email_error}")

        except Exception as fill_error:
            print(f"Error in forward filling: {fill_error}")
            sys.exit(1)

    monthly_summary = monthly_summary.replace({float('nan'): None})
    quarterly_summary = quarterly_summary.replace({float('nan'): None})
    
    write_to_s3_bucket(monthly_summary, AWS_S3_BUCKET_SHAP, f'{monthly_file_save_path}/report/shap_summary/{date}.csv')
    monthly_summary = convert_report_es(monthly_summary,date,'Monthly')
    es_prod.save_records_v2(monthly_summary,es_index)

    write_to_s3_bucket(quarterly_summary, AWS_S3_BUCKET_SHAP, f'{quarterly_file_save_path}/report/shap_summary/{date}.csv')
    quarterly_summary = convert_report_es(quarterly_summary,date,'Quarterly')
    es_prod.save_records_v2(quarterly_summary,es_index)
    send_mail(f"Explainability: Multi-Asset Monthly+Quarterly Completed {date}", 
                         f"Explainability Multi-Asset Monthly+Quarterly daily run has finished for date {date}\nRecords Saved :{shap_summary.shape[0]}")
    print("Finished")