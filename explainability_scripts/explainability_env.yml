name: explainability_env.yml
channels:
  - defaults
  - conda-forge
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - _py-xgboost-mutex=2.0=cpu_0
  - absl-py=2.1.0=pyhd8ed1ab_0
  - aiohappyeyeballs=2.4.0=pyhd8ed1ab_0
  - aiohttp=3.10.5=py39h8cd3c5a_1
  - aiosignal=1.3.1=pyhd8ed1ab_0
  - alsa-lib=1.2.8=h166bdaf_0
  - anyio=4.6.0=pyhd8ed1ab_1
  - argon2-cffi=23.1.0=pyhd8ed1ab_0
  - argon2-cffi-bindings=21.2.0=py39h8cd3c5a_5
  - arrow=1.3.0=pyhd8ed1ab_0
  - asttokens=2.4.1=pyhd8ed1ab_0
  - astunparse=1.6.3=pyhd8ed1ab_0
  - async-lru=2.0.4=pyhd8ed1ab_0
  - async-timeout=4.0.3=pyhd8ed1ab_0
  - atk-1.0=2.38.0=h04ea711_2
  - attr=2.5.1=h166bdaf_1
  - attrs=24.2.0=pyh71513ae_0
  - aws-requests-auth=0.4.3=pyhd8ed1ab_0
  - babel=2.14.0=pyhd8ed1ab_0
  - beautifulsoup4=4.12.3=pyha770c72_0
  - bleach=6.1.0=pyhd8ed1ab_0
  - blinker=1.8.2=pyhd8ed1ab_0
  - boto3=1.35.25=pyhd8ed1ab_0
  - botocore=1.35.25=pyge38_1234567_0
  - brotli=1.1.0=hb9d3cd8_2
  - brotli-bin=1.1.0=hb9d3cd8_2
  - brotli-python=1.1.0=py39hf88036b_2
  - bzip2=1.0.8=h4bc722e_7
  - c-ares=1.33.1=heb4867d_0
  - ca-certificates=2025.2.25=h06a4308_0
  - cached-property=1.5.2=hd8ed1ab_1
  - cached_property=1.5.2=pyha770c72_1
  - cachetools=5.5.0=pyhd8ed1ab_0
  - cairo=1.16.0=ha61ee94_1014
  - catboost=1.2.7=py39hf3d152e_0
  - certifi=2025.1.31=py39h06a4308_0
  - cffi=1.17.1=py39h15c3d72_0
  - charset-normalizer=3.3.2=pyhd8ed1ab_0
  - click=8.1.7=unix_pyh707e725_0
  - cloudpickle=3.0.0=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - comm=0.2.2=pyhd8ed1ab_0
  - contourpy=1.3.0=py39h74842e3_1
  - cryptography=41.0.7=py39he6105cc_1
  - cuda-version=12.6=h7480c83_3
  - cycler=0.12.1=pyhd8ed1ab_0
  - dbus=1.13.6=h5008d03_3
  - debugpy=1.8.5=py39hf88036b_1
  - decorator=5.1.1=pyhd8ed1ab_0
  - defusedxml=0.7.1=pyhd8ed1ab_0
  - dill=0.3.8=py39h06a4308_0
  - entrypoints=0.4=pyhd8ed1ab_0
  - et_xmlfile=1.1.0=pyhd8ed1ab_0
  - exceptiongroup=1.2.2=pyhd8ed1ab_0
  - exchange-calendars=4.5.5=pyhd8ed1ab_0
  - executing=2.1.0=pyhd8ed1ab_0
  - expat=2.6.3=h5888daf_0
  - expiringdict=1.2.2=pyhd8ed1ab_0
  - fftw=3.3.10=nompi_hf1063bd_110
  - flatbuffers=22.12.06=hcb278e6_2
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_2
  - fontconfig=2.14.2=h14ed4e7_0
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - fonttools=4.54.0=py39h8cd3c5a_0
  - fqdn=1.5.1=pyhd8ed1ab_0
  - fredapi=0.5.2=pyhd8ed1ab_0
  - freetype=2.12.1=h267a509_2
  - fribidi=1.0.10=h36c2ea0_0
  - frozenlist=1.4.1=py39h8cd3c5a_1
  - gast=0.4.0=pyh9f0ad1d_0
  - gdk-pixbuf=2.42.10=h05c8ddd_0
  - gettext=0.22.5=he02047a_3
  - gettext-tools=0.22.5=he02047a_3
  - giflib=5.2.2=hd590300_0
  - glib=2.80.2=hf974151_0
  - glib-tools=2.80.2=hb6ce0ca_0
  - google-api-core=2.24.2=pyhd8ed1ab_0
  - google-api-python-client=2.165.0=pyhff2d567_0
  - google-auth=2.35.0=pyhff2d567_0
  - google-auth-httplib2=0.2.0=pyhd8ed1ab_1
  - google-auth-oauthlib=0.4.6=pyhd8ed1ab_0
  - google-pasta=0.2.0=pyhd8ed1ab_1
  - googleapis-common-protos=1.69.2=pyhd8ed1ab_0
  - graphite2=1.3.13=h59595ed_1003
  - graphviz=8.0.3=h2e5815a_0
  - grpcio=1.51.1=py39h8c60046_0
  - gst-plugins-base=1.22.0=h4243ec0_2
  - gstreamer=1.22.0=h25f0c4b_2
  - gstreamer-orc=0.4.40=hb9d3cd8_0
  - gtk2=2.24.33=h90689f9_2
  - gts=0.7.6=h977cf35_4
  - h11=0.14.0=pyhd8ed1ab_0
  - h2=4.1.0=pyhd8ed1ab_0
  - h5py=3.9.0=nompi_py39h4dfffb9_100
  - harfbuzz=6.0.0=h8e241bc_0
  - hdf5=1.14.0=nompi_hb72d44e_103
  - hpack=4.0.0=pyh9f0ad1d_0
  - httpcore=1.0.5=pyhd8ed1ab_0
  - httplib2=0.22.0=pyhd8ed1ab_1
  - httpx=0.27.2=pyhd8ed1ab_0
  - hyperframe=6.0.1=pyhd8ed1ab_0
  - ibm-cloud-sdk-core=3.18.0=pyhd8ed1ab_0
  - ibm-watson=8.0.0=pyhd8ed1ab_0
  - icu=70.1=h27087fc_0
  - idna=3.10=pyhd8ed1ab_0
  - importlib-metadata=8.5.0=pyha770c72_0
  - importlib-resources=6.4.5=pyhd8ed1ab_0
  - importlib_metadata=8.5.0=hd8ed1ab_0
  - importlib_resources=6.4.5=pyhd8ed1ab_0
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=8.18.1=pyh707e725_3
  - ipywidgets=8.1.5=pyhd8ed1ab_0
  - isoduration=20.11.0=pyhd8ed1ab_0
  - jack=1.9.22=h11f4161_0
  - jedi=0.19.1=pyhd8ed1ab_0
  - jinja2=3.1.4=pyhd8ed1ab_0
  - jmespath=1.0.1=pyhd8ed1ab_0
  - jpeg=9e=h0b41bf4_3
  - json5=0.9.25=pyhd8ed1ab_0
  - jsonpointer=3.0.0=py39hf3d152e_1
  - jsonschema=4.23.0=pyhd8ed1ab_0
  - jsonschema-specifications=2023.12.1=pyhd8ed1ab_0
  - jsonschema-with-format-nongpl=4.23.0=hd8ed1ab_0
  - jupyter=1.1.1=pyhd8ed1ab_0
  - jupyter-lsp=2.2.5=pyhd8ed1ab_0
  - jupyter_client=8.6.3=pyhd8ed1ab_0
  - jupyter_console=6.6.3=pyhd8ed1ab_0
  - jupyter_core=5.7.2=pyh31011fe_1
  - jupyter_events=0.10.0=pyhd8ed1ab_0
  - jupyter_server=2.14.2=pyhd8ed1ab_0
  - jupyter_server_terminals=0.5.3=pyhd8ed1ab_0
  - jupyterlab=4.2.5=pyhd8ed1ab_0
  - jupyterlab_pygments=0.3.0=pyhd8ed1ab_1
  - jupyterlab_server=2.27.3=pyhd8ed1ab_0
  - jupyterlab_widgets=3.0.13=pyhd8ed1ab_0
  - keras=2.11.0=pyhd8ed1ab_0
  - keras-preprocessing=1.1.2=pyhd8ed1ab_0
  - keyutils=1.6.1=h166bdaf_0
  - kiwisolver=1.4.7=py39h74842e3_0
  - korean_lunar_calendar=0.3.1=pyhd8ed1ab_0
  - krb5=1.20.1=h81ceb04_0
  - lame=3.100=h166bdaf_1003
  - lcms2=2.15=hfd0df8a_0
  - ld_impl_linux-64=2.43=h712a8e2_0
  - lerc=4.0.0=h27087fc_0
  - libabseil=20220623.0=cxx17_h05df665_6
  - libaec=1.1.3=h59595ed_0
  - libasprintf=0.22.5=he8f35ee_3
  - libasprintf-devel=0.22.5=he8f35ee_3
  - libblas=3.9.0=24_linux64_openblas
  - libbrotlicommon=1.1.0=hb9d3cd8_2
  - libbrotlidec=1.1.0=hb9d3cd8_2
  - libbrotlienc=1.1.0=hb9d3cd8_2
  - libcap=2.67=he9d0100_0
  - libcblas=3.9.0=24_linux64_openblas
  - libclang=15.0.7=default_h127d8a8_5
  - libclang13=15.0.7=default_h5d6823c_5
  - libcups=2.3.3=h36d4200_3
  - libcurl=8.1.2=h409715c_0
  - libdb=6.2.32=h9c3ff4c_0
  - libdeflate=1.17=h0b41bf4_0
  - libedit=3.1.20191231=he28a2e2_2
  - libev=4.33=hd590300_2
  - libevent=2.1.10=h28343ad_4
  - libexpat=2.6.3=h5888daf_0
  - libffi=3.4.2=h7f98852_5
  - libflac=1.4.3=h59595ed_0
  - libgcc=14.1.0=h77fa898_1
  - libgcc-ng=14.1.0=h69a702a_1
  - libgcrypt=1.11.0=h4ab18f5_1
  - libgd=2.3.3=h695aa2c_1
  - libgettextpo=0.22.5=he02047a_3
  - libgettextpo-devel=0.22.5=he02047a_3
  - libgfortran=14.1.0=h69a702a_1
  - libgfortran-ng=14.1.0=h69a702a_1
  - libgfortran5=14.1.0=hc5f4f2c_1
  - libglib=2.80.2=hf974151_0
  - libgomp=14.1.0=h77fa898_1
  - libgpg-error=1.50=h4f305b6_0
  - libgrpc=1.51.1=h30feacc_0
  - libiconv=1.17=hd590300_2
  - liblapack=3.9.0=24_linux64_openblas
  - libllvm14=14.0.6=hcd5def8_4
  - libllvm15=15.0.7=hadd5161_1
  - libnghttp2=1.58.0=h47da74e_0
  - libnsl=2.0.1=hd590300_0
  - libogg=1.3.5=h4ab18f5_0
  - libopenblas=0.3.27=pthreads_hac2b453_1
  - libopus=1.3.1=h7f98852_1
  - libpng=1.6.43=h2797004_0
  - libpq=15.3=hbcd7760_1
  - libprotobuf=3.21.12=hfc55251_2
  - librsvg=2.54.4=h7abd40a_0
  - libsndfile=1.2.2=hc60ed4a_1
  - libsodium=1.0.18=h36c2ea0_1
  - libsqlite=3.46.0=hde9e2c9_0
  - libssh2=1.11.0=h0841786_0
  - libstdcxx=14.1.0=hc0a3c3a_1
  - libstdcxx-ng=14.1.0=h4852527_1
  - libsystemd0=253=h8c4010b_1
  - libtiff=4.5.0=h6adf6a1_2
  - libtool=2.4.7=he02047a_1
  - libudev1=253=h0b41bf4_1
  - libuuid=2.38.1=h0b41bf4_0
  - libvorbis=1.3.7=h9c3ff4c_0
  - libwebp-base=1.4.0=hd590300_0
  - libxcb=1.13=h7f98852_1004
  - libxgboost=1.7.6=h6a678d5_0
  - libxkbcommon=1.5.0=h79f4944_1
  - libxml2=2.10.3=hca2bb57_4
  - libzlib=1.2.13=h4ab18f5_6
  - lightgbm
  - llvmlite=0.43.0=py39h6a678d5_0
  - lz4-c=1.9.4=hcb278e6_0
  - markdown=3.6=pyhd8ed1ab_0
  - markupsafe=2.1.5=py39h8cd3c5a_1
  - matplotlib=3.9.1=py39hf3d152e_1
  - matplotlib-base=3.9.1=py39h0565ad7_2
  - matplotlib-inline=0.1.7=pyhd8ed1ab_0
  - mistune=3.0.2=pyhd8ed1ab_0
  - mpg123=1.32.6=h59595ed_0
  - multidict=6.1.0=py39h8cd3c5a_0
  - munkres=1.1.4=pyh9f0ad1d_0
  - mysql-common=8.0.33=hf1915f5_6
  - mysql-libs=8.0.33=hca2cd23_6
  - nbclient=0.10.0=pyhd8ed1ab_0
  - nbconvert-core=7.16.4=pyhd8ed1ab_1
  - nbformat=5.10.4=pyhd8ed1ab_0
  - ncurses=6.5=he02047a_1
  - nest-asyncio=1.6.0=pyhd8ed1ab_0
  - notebook=7.2.2=pyhd8ed1ab_0
  - notebook-shim=0.2.4=pyhd8ed1ab_0
  - nspr=4.35=h27087fc_0
  - nss=3.100=hca3bf56_0
  - numba=0.60.0=py39h0320e7d_0
  - numpy=1.26.4=py39h474f0d3_0
  - oauthlib=3.2.2=pyhd8ed1ab_0
  - openjpeg=2.5.0=hfec8fc6_2
  - openpyxl=3.1.5=py39h79730dd_1
  - opensearch-py=2.5.0=pyhd8ed1ab_0
  - openssl=3.1.7=hb9d3cd8_0
  - opt_einsum=3.3.0=pyhc1e730c_2
  - overrides=7.7.0=pyhd8ed1ab_0
  - packaging=24.1=pyhd8ed1ab_0
  - pandarallel=1.6.4=py39h06a4308_0
  - pandas=2.2.3=py39h3b40f6f_1
  - pandas-market-calendars=4.4.0=pyhd8ed1ab_0
  - pandas_market_calendars=4.4.0=pyha770c72_0
  - pandocfilters=1.5.0=pyhd8ed1ab_0
  - pango=1.50.14=hd33c08f_0
  - parso=0.8.4=pyhd8ed1ab_0
  - pcre2=10.43=hcad00b1_0
  - pexpect=4.9.0=pyhd8ed1ab_0
  - pickleshare=0.7.5=py_1003
  - pillow=9.4.0=py39h2320bf1_1
  - pip=24.2=pyh8b19718_1
  - pixman=0.43.2=h59595ed_0
  - pkgutil-resolve-name=1.3.10=pyhd8ed1ab_1
  - platformdirs=4.3.6=pyhd8ed1ab_0
  - plotly=5.24.1=pyhd8ed1ab_0
  - ply=3.11=pyhd8ed1ab_2
  - prometheus_client=0.21.0=pyhd8ed1ab_0
  - prompt-toolkit=3.0.47=pyha770c72_0
  - prompt_toolkit=3.0.47=hd8ed1ab_0
  - proto-plus=1.26.1=pyhd8ed1ab_0
  - protobuf=4.21.12=py39h227be39_0
  - psutil=6.0.0=py39h8cd3c5a_1
  - pthread-stubs=0.4=hb9d3cd8_1002
  - ptyprocess=0.7.0=pyhd3deb0d_0
  - pulseaudio=16.1=hcb278e6_3
  - pulseaudio-client=16.1=h5195f5e_3
  - pulseaudio-daemon=16.1=ha8d29e2_3
  - pure_eval=0.2.3=pyhd8ed1ab_0
  - py-xgboost=1.7.6=py39h06a4308_0
  - pyasn1=0.6.1=pyhd8ed1ab_1
  - pyasn1-modules=0.4.1=pyhd8ed1ab_0
  - pycparser=2.22=pyhd8ed1ab_0
  - pygments=2.18.0=pyhd8ed1ab_0
  - pyjwt=2.9.0=pyhd8ed1ab_1
  - pyluach=2.2.0=pyhd8ed1ab_0
  - pyopenssl=23.2.0=pyhd8ed1ab_1
  - pyparsing=3.1.4=pyhd8ed1ab_0
  - pyqt=5.15.9=py39h52134e7_5
  - pyqt5-sip=12.12.2=py39h3d6467e_5
  - pysocks=1.7.1=pyha2e5f31_6
  - python=3.9.18=h0755675_0_cpython
  - python-dateutil=2.9.0=pyhd8ed1ab_0
  - python-fastjsonschema=2.20.0=pyhd8ed1ab_0
  - python-flatbuffers=24.3.25=pyh59ac667_0
  - python-graphviz=0.20.3=pyh717bed2_0
  - python-json-logger=2.0.7=pyhd8ed1ab_0
  - python-tzdata=2024.2=pyhd8ed1ab_0
  - python_abi=3.9=5_cp39
  - pytz=2024.1=pyhd8ed1ab_0
  - pyu2f=0.1.5=pyhd8ed1ab_0
  - pyyaml=6.0.2=py39h8cd3c5a_1
  - pyzmq=26.2.0=py39h4e4fb57_1
  - qhull=2020.2=h434a139_5
  - qt-main=5.15.8=h5d23da1_6
  - re2=2022.06.01=h27087fc_1
  - readline=8.2=h8228510_1
  - referencing=0.35.1=pyhd8ed1ab_0
  - requests=2.32.3=pyhd8ed1ab_0
  - requests-oauthlib=2.0.0=pyhd8ed1ab_0
  - retrying=1.3.3=pyhd3eb1b0_2
  - rfc3339-validator=0.1.4=pyhd8ed1ab_0
  - rfc3986-validator=0.1.1=pyh9f0ad1d_0
  - rpds-py=0.20.0=py39he612d8f_1
  - rsa=4.9=pyhd8ed1ab_0
  - s3transfer=0.10.2=pyhd8ed1ab_0
  - scikit-learn=1.5.2=py39h4b7350c_1
  - scipy=1.13.1=py39haf93ffa_0
  - send2trash=1.8.3=pyh0d859eb_0
  - setuptools=74.1.2=pyhd8ed1ab_0
  - shap=0.45.1=cpu_py39h853acf6_0
  - sip=6.7.12=py39h3d6467e_0
  - six=1.16.0=pyh6c4a22f_0
  - slicer=0.0.8=pyhd8ed1ab_0
  - snappy=1.1.10=hdb0a2a9_1
  - sniffio=1.3.1=pyhd8ed1ab_0
  - soupsieve=2.5=pyhd8ed1ab_1
  - sqlite=3.46.0=h6d4b2fc_0
  - stack_data=0.6.2=pyhd8ed1ab_0
  - tenacity=9.0.0=pyhd8ed1ab_0
  - tensorboard=2.11.2=pyhd8ed1ab_0
  - tensorboard-data-server=0.6.1=py39h3ccb8fc_4
  - tensorboard-plugin-wit=1.8.1=pyhd8ed1ab_0
  - tensorflow=2.11.0=cpu_py39h4655687_0
  - tensorflow-base=2.11.0=cpu_py39h9b4020c_0
  - tensorflow-estimator=2.11.0=cpu_py39hf050123_0
  - termcolor=2.4.0=pyhd8ed1ab_0
  - terminado=0.18.1=pyh0d859eb_0
  - threadpoolctl=3.5.0=pyhc1e730c_0
  - tinycss2=1.3.0=pyhd8ed1ab_0
  - tk=8.6.13=noxft_h4845f30_101
  - toml=0.10.2=pyhd8ed1ab_0
  - tomli=2.0.1=pyhd8ed1ab_0
  - toolz=0.12.1=pyhd8ed1ab_0
  - tornado=6.4.1=py39h8cd3c5a_1
  - tqdm=4.66.5=pyhd8ed1ab_0
  - traitlets=5.14.3=pyhd8ed1ab_0
  - types-python-dateutil=2.9.0.20240906=pyhd8ed1ab_0
  - typing-extensions=4.12.2=hd8ed1ab_0
  - typing_extensions=4.12.2=pyha770c72_0
  - typing_utils=0.1.0=pyhd8ed1ab_0
  - tzdata=2024a=h8827d51_1
  - unicodedata2=15.1.0=py39hd1e30aa_0
  - uri-template=1.3.0=pyhd8ed1ab_0
  - uritemplate=4.1.1=pyhd8ed1ab_1
  - urllib3=1.26.19=pyhd8ed1ab_0
  - wcwidth=0.2.13=pyhd8ed1ab_0
  - webcolors=24.8.0=pyhd8ed1ab_0
  - webencodings=0.5.1=pyhd8ed1ab_2
  - websocket-client=1.8.0=pyhd8ed1ab_0
  - werkzeug=3.0.4=pyhd8ed1ab_0
  - wheel=0.44.0=pyhd8ed1ab_0
  - widgetsnbextension=4.0.13=pyhd8ed1ab_0
  - wrapt=1.16.0=py39h8cd3c5a_1
  - xcb-util=0.4.0=h516909a_0
  - xcb-util-image=0.4.0=h166bdaf_0
  - xcb-util-keysyms=0.4.0=h516909a_0
  - xcb-util-renderutil=0.3.9=h166bdaf_0
  - xcb-util-wm=0.4.1=h516909a_0
  - xgboost=1.7.6=py39h06a4308_0
  - xkeyboard-config=2.38=h0b41bf4_0
  - xlsxwriter=3.2.0=pyhd8ed1ab_0
  - xorg-kbproto=1.0.7=hb9d3cd8_1003
  - xorg-libice=1.1.1=hd590300_0
  - xorg-libsm=1.2.4=h7391055_0
  - xorg-libx11=1.8.4=h0b41bf4_0
  - xorg-libxau=1.0.11=hb9d3cd8_1
  - xorg-libxdmcp=1.1.3=hb9d3cd8_1
  - xorg-libxext=1.3.4=h0b41bf4_2
  - xorg-libxrender=0.9.10=h7f98852_1003
  - xorg-renderproto=0.11.1=hb9d3cd8_1003
  - xorg-xextproto=7.3.0=hb9d3cd8_1004
  - xorg-xproto=7.0.31=hb9d3cd8_1008
  - xz=5.2.6=h166bdaf_0
  - yaml=0.2.5=h7f98852_2
  - yarl=1.9.4=py39h8cd3c5a_1
  - zeromq=4.3.5=h59595ed_1
  - zipp=3.20.2=pyhd8ed1ab_0
  - zlib=1.2.13=h4ab18f5_6
  - zstandard=0.23.0=py39h08a7858_1
  - zstd=1.5.6=ha6fb4c9_0
  - pip:
      - altair==4.2.2
      - feedzai-altair-theme==1.1.3
      - joblib==1.3.2
      - numpy-ext==0.9.9
      - seaborn==0.13.2
      - timeshap==1.0.4
prefix: /home/<USER>/miniconda3/envs/explainability_env.yml
