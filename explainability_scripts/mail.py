import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON>Multipart

def send_mail(subject, body, to = None):
    try:
        SMTP_SERVER = "smtp.gmail.com"
        SMTP_PORT = 587 
        SENDER_EMAIL = "<EMAIL>"
        SENDER_PASSWORD ="vies rtlt mxwb qyap" 
        RECIPIENTS = ["<EMAIL>",'<EMAIL>','<EMAIL>','<EMAIL>']
        if to:
            RECIPIENTS += to

        msg = MIMEMultipart()
        msg['From'] = SENDER_EMAIL
        msg['To'] = ", ".join(RECIPIENTS)
        msg['Subject'] = subject

        msg.attach(MIMEText(body, 'plain'))

        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls() 
            server.login(SENDER_EMAIL, SENDER_PASSWORD)
            server.sendmail(SENDER_EMAIL, RECIPIENTS, msg.as_string())
        
        print(f"Email sent successfully to {RECIPIENTS}")
        return True
        
    except Exception as e:
        print(f"Failed to send email: {str(e)}")
        return False
    

