import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import shap

from io import BytesIO,StringIO
import pickle
import os
import re

import datetime
import xlsxwriter
import sys

import boto3
import requests
from eq_common_utils.utils.opensearch_helper import OpenSearch
from mail import *
import warnings
warnings.filterwarnings("ignore")

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.getcwd())

AWS_ACCESS_KEY_ID = '********************'
AWS_SECRET_ACCESS_KEY = 'bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'
AWS_S3_BUCKET = 'portfolio-experiments-test'
AWS_S3_BUCKET_PATH = 's3://portfolio-experiments-test/er_single_model/Monthly/combined/features/'
data_path = 'aieq_training/Monthly'

current_year = datetime.datetime.now().year
cutoff_year = current_year

s3 = boto3.client('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)

es_prod = OpenSearch(
            host='search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com',
            port=443,
            key_id='********************',
            secret='xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV',
            region = 'us-east-1')

es_train = OpenSearch(
            host='search-training-data-7llze3ehbf3ry4hu5rwlu662ye.us-east-1.es.amazonaws.com',
            port=443,
            key_id='********************',
            secret='oVD9ZhQRCGz74RzQpIqqiyIvyIXIpRIElO+8ftvb',
            region = 'us-east-1')

def safe_read_decorator(desc="file"):
    def decorator(read_func):
        def wrapper(*args, **kwargs):
            try:
                return read_func(*args, **kwargs)
            except Exception as e:
                print(f"Failed to read {desc}: {e}")
                return None
        return wrapper
    return decorator
        
@safe_read_decorator(desc="file from S3")
def get_csv_data_from_s3(bucket_name, object_key): # object key of the file to read
    s3 = boto3.client('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)
    csv_obj = s3.get_object(Bucket=bucket_name, Key=object_key)
    body = csv_obj['Body']
    csv_string = body.read().decode('utf-8')
    df = pd.read_csv(StringIO(csv_string))
    return df

def write_to_s3_bucket(dataframe, bucket_name, filename):
    csv_buffer = StringIO()
    dataframe.to_csv(csv_buffer,index=False)
    s3_resource = boto3.resource('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key= AWS_SECRET_ACCESS_KEY)
    s3_resource.Object(bucket_name, filename).put(Body=csv_buffer.getvalue())
    return "File stored in s3 successfully"

def get_isins(tag, verbose = False):
    if tag not in ['tier1','aieq','aigo','etf','bnpetf']:
        print("tag invalid, pass from : ['tier1','aieq','aigo','etf',bnpetf]")
        return
    try:
        isins = requests.get(f'http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag={tag}').json() 
        isins_df = pd.DataFrame(isins["data"][f"masteractivefirms_{tag}"])  # convert the master list to pandas dataframe
        isin_list = isins_df['isin'].tolist()
        if verbose:
          print(f"{len(isin_list)} {tag} isins returned")
    except Exception as e:
        sys.exit(f"Cannot fetch list of {tag} isins due to exception {e}")
    
    return isin_list

def get_etf_dict():
    etf = requests.get(f'http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=etf').json() 
    etf_isins_df = pd.DataFrame(etf["data"][f"masteractivefirms_etf"])  # convert the master list to pandas dataframe
    etf_dict = dict(zip(etf_isins_df['tic'], etf_isins_df['isin']))
    return etf_dict
    
def get_bnp_dict():
    bnp = requests.get(f'http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=bnpetf').json() 
    bnp_isins_df = pd.DataFrame(bnp["data"][f"masteractivefirms_bnpetf"])  
    bnp_dict = dict(zip(bnp_isins_df['tic'], bnp_isins_df['isin']))
    return bnp_dict

@safe_read_decorator(desc="S3 data")
def read_file(bucket,file):
    response = s3.get_object(Bucket=bucket, Key=file)
    content = response['Body'].read()
    df = pd.read_csv(BytesIO(content))
    return df

@safe_read_decorator(desc="S3 data")
def read_pickle(bucket,key):
    response = s3.get_object(Bucket=bucket, Key=key)
    pickle_data = response['Body'].read()
    unpickled_data = pickle.loads(pickle_data)
    return unpickled_data

@safe_read_decorator(desc="S3 data")
def read_explainer_from_s3(bucket_name, s3_key):
    buffer = BytesIO()
    # Download the explainer file from S3
    s3.download_fileobj(bucket_name, s3_key, buffer)
    buffer.seek(0)  # Move to the beginning of the buffer
    return buffer

def upload_explainer_to_s3(explainer, bucket_name, s3_key):
    buffer = BytesIO()
    explainer.save(buffer, model_saver='.save', masker_saver='.save')
    buffer.seek(0)  # Move to the beginning of the buffer
    # Upload the buffer directly to S3
    s3.upload_fileobj(buffer, bucket_name, s3_key)
    print(f'Successfully uploaded explainer at s3://{bucket_name}/{s3_key}')

def get_completed_isins(AWS_S3_BUCKET_SAVE,file_save_path):
    isins = get_s3_keys_from_bucket(AWS_S3_BUCKET_SAVE,f'{file_save_path}/explainer')
    completed_isins_list = []
    for i in range(len(isins)):
      completed_isins_list.append(isins['key'][i].split('/')[-1].split('.')[0])
    return completed_isins_list

def get_s3_keys_from_bucket(bucket_name, folder_path, sort_by_timestamp= True , sort_in_ascending = False):
    s3 = boto3.resource('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)
    s3_bucket_obj = s3.Bucket(bucket_name)
    key_list = []
    time_stamp_list = []
    try:
        for i, object_summary in enumerate(s3_bucket_obj.objects.filter(Prefix = folder_path)):
            try:
                key_list.append(object_summary.key)
                time_stamp_list.append(object_summary.last_modified)
            except Exception as e:
                print(f'exception in for loop {e}')
    except Exception as e:
        print(f'Exception as outer loop{e}')
    keys_df = pd.DataFrame(list(zip(key_list, time_stamp_list)), columns= ['key', 'timestamp'])
    keys_df=keys_df[keys_df['key']!=folder_path]
    
    if sort_by_timestamp:
        keys_df.sort_values(by=["timestamp"], ascending=sort_in_ascending, inplace=True)
    else:
        keys_df.sort_values(by=["key"], ascending= sort_in_ascending, inplace=True)
        
    return keys_df

def get_indices_for_date_range(index_name,start_date, end_date):
    start_year = datetime.datetime.strptime(start_date, "%Y-%m-%d").year
    end_year = datetime.datetime.strptime(end_date, "%Y-%m-%d").year
    return ",".join([f"{index_name}_{year}" for year in range(start_year, end_year + 1)])

@safe_read_decorator(desc="ES data")
def get_es_data(es,isin,es_path,schedular=None):
    indices = get_indices_for_date_range(es_path,'2009-01-01', f'{current_year}-12-31')
    if schedular is not None:
      query = {
            "query": {
              "bool": {
                "must": [
                  {
                    "match": {
                      "isin": isin
                    }
                  },
                  {
                      "match":{
                        "schedular" : schedular
                      }
                  }
                ]
              }
            }
          }
    else:
      query = {
                "query": {
                    "match": {
                    "isin": isin
                    }
                }
            }

    data = []
    try:
      response,_ = es.search_with_pagination(index=indices,query=query,paginate=False,strict=False)
    except Exception as e:
        print(f"Troube reading data from es due to {e}")
        return
    for hit in response:
        es_data=hit['_source']
        data.append(es_data)
    if data:
      df=pd.DataFrame(data)
      df['date']=pd.to_datetime(df['date'])
      df.sort_values('date', ascending=True, inplace=True)
      df.reset_index(inplace=True, drop=True)
    else:
      return
    return df
    
@safe_read_decorator(desc="ES data")
def get_es_data_date(es,isins,date,es_path,schedular=None):
    year = date.split('-')[0]
    if schedular is not None:
      query = {
            "query": {
              "bool": {
                "must": [
                  {
                    "terms": {
                      "isin.keyword": isins
                    }
                  },
                  {
                      "match":{
                        "schedular" : schedular
                      }
                  },
                  {
                      "match":{
                        "date" : date
                      }
                  }
                ]
              }
            }
          }
    else:
      query = {
            "query": {
              "bool": {
                "must": [
                  {
                    "terms": {
                      "isin.keyword": isins
                    }
                  },
                  {
                      "match":{
                        "date" : date
                      }
                  }
                ]
              }
            }
          }
    
    
    df = es.get_es_hits_as_df([f'{es_path}_{year}'],body=query,size=10000)
    return df

@safe_read_decorator(desc="ES data")
def get_es_data_date_range(es,isins,start_date,end_date,es_path,schedular=None):
    indices = get_indices_for_date_range(es_path,start_date, end_date)
    if schedular is not None:
      query = {
            "query": {
              "bool": {
                "must": [
                  {
                    "terms": {
                      "isin.keyword": isins
                    }
                  },
                  {
                    "match": {
                      "schedular": schedular
                    }
                  },
                  {
                    "range": {
                      "date": {
                        "gte": start_date,
                        "lte": end_date
                      }
                    }
                  }
                ]
              }
            }
          }
    else:
      query = {
            "query": {
              "bool": {
                "must": [
                  {
                    "terms": {
                      "isin.keyword": isins
                    }
                  },
                  {
                    "range": {
                      "date": {
                        "gte": start_date,
                        "lte": end_date
                      }
                    }
                  }
                ]
              }
            }
          }
    
    data = []
    try:
      response,_ = es.search_with_pagination(index=indices,query=query,paginate=False,strict=False)
    except Exception as e:
        return
    for hit in response:
        es_data=hit['_source']
        data.append(es_data)
    if data:
      df=pd.DataFrame(data)
      df['date']=pd.to_datetime(df['date'])
      df.sort_values('date', ascending=True, inplace=True)
      df.reset_index(inplace=True, drop=True)
    else:
       return 
    return df

def get_features_used(feature_dict,overall_imp_features):
    '''
    Function to get the list of input features used from the
    dict of derived features
    '''
    pattern = r'df\["(.*?)"\]'
    # Find all matches }
    feature_mapping  = {}
    for (feat_name,feat_def) in feature_dict.items():
        if feat_name in overall_imp_features:
            matches = re.findall(pattern, feat_def)
            feature_mapping[feat_name] = matches

    features_used = set()
    for feats in  feature_mapping:
        for feat in feature_mapping[feats]:
            features_used.add(feat)

    features_used = list(features_used)
    return features_used

def make_prediction(df,model,overall_imp_features,feature_dict,scaler):
    # Need df for eval(feature_dict)
    '''
    Modified prediction function for shap explainer
    Used to caluclate input feature importance directly by including 
    feature engineering inside the predict function 
    '''
    final_df=pd.DataFrame()
    for idx,k in enumerate(overall_imp_features):
      try:
          final_df[k]=eval(feature_dict[k])
      except:
          final_df[k]=np.nan
    final_df_scaled_values=scaler.transform(final_df)
    final_scaled_df=pd.DataFrame(final_df_scaled_values,columns=final_df.columns)
    return model.predict(final_scaled_df)

def process_isin_data(df, dates):

    df = df.copy()
    df['date'] = pd.to_datetime(df['date'])
    
    # Set date as index and reindex to expected dates
    expected_dates = pd.to_datetime(dates)
    df = df.set_index('date').reindex(expected_dates)
    
    # Forward fill missing values
    df_filled = df.ffill()
    
    # Reset index to restore 'date' as column
    df_filled = df_filled.reset_index()
    df_filled.rename(columns={'index': 'date'}, inplace=True)
    
    return df_filled