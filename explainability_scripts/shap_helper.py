import pandas as pd
'''
Helper funtions related to the Report generated for SHAP
'''

def get_shap_format(N_TOP_FEATURES):
    cols = [ ]
    cols.append('Shap_Base_Value')
    for i in range(N_TOP_FEATURES):
        cols.append(f'Feature_Name_{i+1}')
        cols.append(f'Shap_Value_{i+1}')
        cols.append(f'Signal_Value_{i+1}')
    return cols

def get_shap_diff_format(N_TOP_FEATURES):
    cols_diff = [ ]
    cols_diff.append(f'Prediction Difference')
    cols_diff.append(f'Prediction Today')
    cols_diff.append(f'Prediction yesterday')
    cols_diff.append('Shap_Base_Value')
    for i in range(N_TOP_FEATURES):
        cols_diff.append(f'Feature_Name_{i+1}')
        cols_diff.append(f'Feature Value Today_{i+1}')
        cols_diff.append(f'Feature Value Yesterday_{i+1}')
        cols_diff.append(f'Shap_Value_{i+1}')
        cols_diff.append(f'Signal_Value_{i+1}')
    return cols_diff

def get_summary(isin,explainer,data,features):
    '''Generates report for SHAP explanations'''
    N = len(features)
    cols = get_shap_format(N)
    model_shap_summary = pd.DataFrame(columns = cols)
    shap_output= explainer(data)
    shap_value = shap_output.values.reshape(-1)
    shap_sum = sum(abs(shap_value))
    sorted_index = abs(shap_value).argsort()[::-1]
    top_contributions = shap_value[sorted_index]
    row = []
    row.append(shap_output.base_values[0])
    for j in range(N):
        row.append(features[sorted_index[j]])
        row.append(top_contributions[j])
        row.append(top_contributions[j]/shap_sum*100)
    model_shap_summary.loc[isin] = row
    model_shap_summary.index.name = 'isin'
    return model_shap_summary

def get_diff_summary(isin,data_today,data_prev,explainer,predict,features):
    '''Generates report for SHAP difference explanations'''
    N = len(features)
    cols_diff = get_shap_diff_format(N)
    prediction_today = predict(data_today)
    prediction_yesterday = predict(data_prev)
    prediction_diff = prediction_today-prediction_yesterday
    shap_today = explainer(data_today)
    shap_yesterday = explainer(data_prev)

    shap_diff_summary = pd.DataFrame(columns = cols_diff)
    shap_value_diff_output = shap_today-shap_yesterday
    shap_value_diff = shap_value_diff_output.values.reshape(-1)
    shap_sum = sum(abs(shap_value_diff))
    sorted_index = abs(shap_value_diff).argsort()[::-1]
    top_contributions = shap_value_diff[sorted_index]
    row = []
    row.append(prediction_diff[0])
    row.append(prediction_today[0])
    row.append(prediction_yesterday[0])
    row.append(shap_value_diff_output.base_values[0])
    for i in range(N):
        feat = features[sorted_index[i]]
        row.append(feat)  
        row.append(data_today.iloc[0][feat])
        row.append(data_prev.iloc[0][feat])
        row.append(top_contributions[i])
        row.append(top_contributions[i]/shap_sum*100)
    shap_diff_summary.loc[isin] = row
    shap_diff_summary.index.name = 'isin'
    return shap_diff_summary

def get_shap_series(shap_report):
    '''
    Extracts feature names and shao values from the shap report 
    Returns: Pandas series mapping base feature name to shap value
    '''
    num_features = int(shap_report.filter(like="Shap", axis=1).columns[-1].split('_')[-1])
    feature_dict = {}
    
    for i in range(1,num_features+1):
        name_col = f'Feature_Name_{i}'
        value_col = f'Shap_Value_{i}'
        if name_col in shap_report.columns and value_col in shap_report.columns:
            feature_name = shap_report[name_col].item()
            if pd.notna(feature_name):
                feature_value = shap_report[value_col].item()
                feature_dict[feature_name] = feature_value
        
    feature_scores = pd.Series(feature_dict)
    return feature_scores

def get_summary_from_series(isin,scores,base_value):
    '''Generates report for best_er SHAP explanations'''
    N = len(scores)
    cols = get_shap_format(N)
    model_shap_summary = pd.DataFrame(columns = cols)
    shap_sum = sum(abs(scores))
    sorted_index = abs(scores).argsort()[::-1]
    top_contributions = scores[sorted_index]
    row = []
    row.append(base_value)
    for j in range(N):
        row.append(scores.index[sorted_index[j]])
        row.append(top_contributions[j])
        row.append(top_contributions[j]/shap_sum*100)
    model_shap_summary.loc[isin] = row
    model_shap_summary.index.name = 'isin'
    model_shap_summary['shap_sum'] = sum(scores)+base_value
    return model_shap_summary

def convert_report_es(shap_report, date, schedular):
    documents = []
    isins = shap_report['isin'].unique()
    for isin in isins:
        shap_rep = shap_report[shap_report['isin'] == isin]
        feature_scores = get_shap_series(shap_rep)
        base_value = shap_rep['Shap_Base_Value'].item()
        doc = { 'date': date,
                'isin': isin,
                'schedular': schedular,
                'base_value': base_value,
                **feature_scores.to_dict() }
        documents.append(doc)
    return pd.DataFrame(documents)