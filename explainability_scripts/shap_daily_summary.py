from utils import *
import os
import sys
import base64
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText
from email.message import EmailMessage
from datetime import datetime,timedelta
from pandas.tseries.offsets import BDay

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.getcwd())

SCOPES = ['https://www.googleapis.com/auth/gmail.send']
models = ['best_er','best_model_quarterly','catboost','etf','xgboost','etf_bnp','etf_quarterly','multi_asset_monthly','multi_asset_quarterly']

etf =['XLI','XLV','DBEEEMGF','MQFIUSTU','SHY','HYG','TLT','EMB','XLB','DBEEURGF','XLE','XLC','XLY','IYR','MQFIUSUS','MQFIUSTY','QQQ','LQD','HSMETYSN','XLP','XLRE','DBEEUNGF','XLU','IWM','XLK','EWJ','EFA','DBEETGFU','GLD','BNDX','TIP','SPY','HSMETYV3','EEM','DBEEUGFT','DBDRUS20','DBDRUS10','XME','XLF','DBDRUS02','DBRCOYGC']
etf_quarterly = ['BNDX', 'EEM', 'EFA', 'EMB', 'EWJ', 'GLD', 'HYG', 'IWM', 'IYR', 'LQD','QQQ', 'SHY', 'SPY', 'TIP', 'TLT', 'XLB', 'XLC', 'XLE', 'XLF', 'XLI','XLK', 'XLP', 'XLRE', 'XLU', 'XLV', 'XLY', 'XME']
etf_bnp = ['BNPIFJP','BNPIFEU','BNPIFUS','BNPIFU10','BNPIFCN','BNPIFJ10','BNPIFE10','BNPIG0GC','BNPIDSBU','BNPIFEM']
multi_asset_monthly = multi_asset_quarterly = ['AOK','AOA','AOM','AOR'] 

AWS_S3_BUCKET_SHAP = 'micro-ops-output'
s3_shap_path  = 'test/SHAP'
year = datetime.now().year

def get_credentials():
    creds = None
    creds_path='credentials.json'
    if os.path.exists(creds_path):
        creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds


def SendMessage(sender, to, subject,message_text,attachments: list=None):
    credentials = get_credentials()
    if credentials== None:
        return print("credentials not found, Generate credentials")
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        html = message_text
        body=MIMEText(html, 'html')
        message.set_content(body)
        if attachments:
            for attachment in attachments:
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype=(
                        attachment.split('.')[1]), filename=attachment)
        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None


def save_model_summary_to_excel(df_list, sheet_names, file_name):
    """
    Saves multiple DataFrames to an Excel file, each in a separate sheet.

    Args:
        df_list (list): List of pandas DataFrames.
        sheet_names (list): List of sheet names corresponding to each DataFrame.
        file_name (str): Output Excel file name (e.g., 'output.xlsx').
    """
    with pd.ExcelWriter(file_name, engine='xlsxwriter') as writer:
        for df, sheet in zip(df_list, sheet_names):
            df.to_excel(writer, sheet_name=sheet, index=False)

if __name__=='__main__':
    
    isins = get_isins('aieq')
    
    print(f"Starting \nTotal isins: {len(isins)}")
    num_aieq_isins = len(isins)

    date = (datetime.today() - BDay(1)).strftime('%Y-%m-%d')
    print(f"Calcualting for {date}")

    sender="<EMAIL>"
    to = ['<EMAIL>','<EMAIL>','<EMAIL>']

    try:
        model_info = []
        fail_counts = {}
        success_counts = {}
        skipped_counts = {}
        for model in models:
            try:
                data = read_file(AWS_S3_BUCKET_SHAP,f'{s3_shap_path}/{model}/{year}/report/shap_summary/{date}.csv')
                if model not in ['etf','etf_bnp','etf_quarterly']:
                    failed_isins = data[data['Success_Flag']==0]
                    if model in ['catboost','xgboost']:
                        model_info.append(failed_isins[['isin','Success_Flag','ERROR','final_pred','shap_sum','local_pred']])
                    elif model in ['multi_asset_monthly','multi_asset_quarterly'] :
                        model_info.append(failed_isins[['isin','Success_Flag','final_pred','shap_sum']])
                    else:
                        model_info.append(failed_isins[['isin','Success_Flag','ERROR','final_pred','shap_sum']])
                    fail_counts[model] = failed_isins.shape[0]
                    success_counts[model] = data['Success_Flag'].sum()
                    skipped_counts[model] = max(num_aieq_isins - success_counts[model] - fail_counts[model], 0)
                else:
                    model_info.append(pd.DataFrame())
                    success_counts[model] = data.shape[0]
                    fail_counts[model] = len(eval(model)) - success_counts[model]
                    skipped_counts[model] = fail_counts[model]

            except Exception as e:
                print(f"Can't read for {model} on {date} due to {e}")
                print(AWS_S3_BUCKET_SHAP,f'{s3_shap_path}/{model}/{year}/report/shap_summary/{date}.csv')
                fail_counts[model] = "Check Failed"
                success_counts[model] = "Check Failed"
                model_info.append(pd.DataFrame())
        
        save_model_summary_to_excel(model_info, models, f'shap_run_summary.xlsx')

        subject = f"SHAP_SUMMARY-{date}"
        html = f"""\
                <html>
                    <head>
                        <style>
                            table {{
                                width: 50%;
                                border-collapse: collapse;
                            }}
                            th {{
                                background-color: #4CAF50;
                                color: white;
                                border: 1px solid black;
                                padding: 8px;
                                text-align: center;
                            }}
                            td {{
                                border: 1px solid black;
                                padding: 8px;
                                text-align: center;
                            }}
                        </style>
                    </head>
                    <body>
                        <p>Hi,</p>
                        <p>SHAP run summary and failed ISINs attached below.</p>
                        <ul>
                            <li><strong>Total AIEQ ISINs present:</strong> {num_aieq_isins}</li>
                            <li><strong>Total ETFs present:</strong> {len(set(etf) | set(etf_bnp) | set(etf_quarterly))}</li>
                        </ul>
                        <br>
                        <table>
                            <tr>
                                <th>Model</th>
                                <th>Success Count</th>
                                <th>Failure Count</th>
                                <th>Skipped Count</th>
                            </tr>
                """

        for model in models:
            success = success_counts.get(model, 0)  
            failure = fail_counts.get(model, 0)  
            skipped = skipped_counts.get(model, 0)  
            html += f"""\
                <tr>
                    <td>{model}</td>
                    <td>{success}</td>
                    <td>{failure}</td>
                    <td>{skipped}</td>
                </tr>
            """

        html += """\
            </table>
            <br>
            <p>Thanks</p>
        </body>
        </html>
        """
        to += ['<EMAIL>','<EMAIL>','<EMAIL>']
        SendMessage(sender, to, subject,html,[f'shap_run_summary.xlsx'])

    except Exception as e:
        print("Error: ",e)
        
        subject = f"FAILURE: SHAP SUMMARY FAILED on {date}"
        html = """\
                <html>
                <head>
                    <title>SHAP Run Summary</title>
                </head>
                <body>
                    <p>Hi,</p>
                    <p>SHAP run summary script did not run today. Please check.</p>
                    <br>
                    <p>Thanks,</p>
                </body>
                </html>
                """
        SendMessage(sender, to, subject,html)
