from aws_requests_auth.aws_auth import AWSRequestsAuth
import boto3


auth_es_tr = AWSRequestsAuth(aws_access_key='********************',
                                aws_secret_access_key='oVD9ZhQRCGz74RzQpIqqiyIvyIXIpRIElO+8ftvb',
                                aws_host='search-training-data-7llze3ehbf3ry4hu5rwlu662ye.us-east-1.es.amazonaws.com',
                                aws_region='us-east-1',
                                aws_service='es')       #---- training elastic search Credentials



s3 = boto3.client('s3', 
                  aws_access_key_id= "********************",
                  aws_secret_access_key= "bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/", 
                    region_name='us-east-1') #-----------------S3 Credentials