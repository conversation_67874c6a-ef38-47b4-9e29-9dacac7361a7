import pandas as pd
from datetime import datetime, timedelta
from eq_common_utils.utils.config.s3_config import s3_config
import os
from Sftp import upload_file_ftp
from utilities import *

parser = argparse.ArgumentParser(prog='Aigo-Signals', description="Script for Aigo Signals")
parser.add_argument('-e', '--env')
args = parser.parse_args()
env = args.env

# upload to the bucket
if env == 'prod':
    bucket_name ='eq-model-output'
elif env == 'pre':
    bucket_name ='eq-pre-model-output'
else:
    print("invalid environment")
    sys.exit()

def get_last_friday():
    today = datetime.now()
    # Calculate days until last Friday (if today is Saturday, it will be 1; if today is Friday, it will be 0)
    days_to_subtract = (today.weekday() - 4) % 7
    # If today is Friday, we want the previous Friday, so add 7
    if days_to_subtract == 0:
        days_to_subtract = 7
    last_friday = today - timedelta(days=days_to_subtract)
    return last_friday.strftime('%Y-%m-%d')

ticker_name_maping = {
    'AIGO':{
        "BNDX": "International Bonds",
        "EEM": "Emerging Markets",
        "XLE": "Energy",
        "TIP": "TIPS",
        "MQFTUSE1": "US Large Cap Equity",
        "MQFTUSN1": "US Technology Equity",
        "HSMETYV3": "HSBC MacroEconomic Treasury Yield",
        "LQD": "US Corporate Bonds",
        "IYR": "US Real Estate",
        "MQFTUSS1": "US Small Cap Equity",
        "HYG": "High Yield Corporate Bonds",
        "GLD": "Gold",
        "EWJ": "Japan Equity",
        "EMB": "Emerging Markets Bonds",
        "EFA": "Developed Markets",
        "XME": "Metals and Mining",
        "MQFIUSTU": "2-Year US Treasuries",
        "MQFIUSTY": "10-Year US Treasuries",
        "MQFIUSUS": "30-Year US Treasuries"},
    'AIGO8':{
        "BNDX": "Vanguard Charlotte Funds - Vanguard Total International Bond ETF",
        "EEM": "iShares Inc. - iShares MSCI Emerging Markets ETF",
        "XLE": "The Select Sector SPDR Trust - The Energy Select Sector SPDR Fund",
        "TLT": "iShares Trust - iShares 20+ Year Treasury Bond ETF",
        "TIP": "iShares Trust - iShares TIPS Bond ETF",
        "SPY": "SPDR S&P 500 ETF Trust",
        "SHY": "iShares Trust - iShares 1-3 Year Treasury Bond ETF",
        "QQQ": "Invesco QQQ Trust Series",
        "METYS": "HSBC METYS INDEX",
        "LQD": "iShares Trust - iShares iBoxx $ Investment Grade Corporate Bond ETF",
        "IYR": "iShares Trust - iShares U.S. Real Estate ETF",
        "IWM": "iShares Trust - iShares Russell 2000 ETF",
        "HYG": "iShares Trust - iShares iBoxx $ High Yield Corporate Bond ETF",
        "GLD": "SPDR Gold Trust",
        "EWJ": "iShares Inc. - iShares MSCI Japan ETF",
        "EMB": "iShares Trust - iShares J.P. Morgan USD Emerging Markets Bond ETF",
        "EFA": "iShares Trust - iShares MSCI EAFE ETF",
        "XME": "SPDR Series Trust - SPDR S&P Metals & Mining ETF"}}

def check_ticker_and_security_mapping(df, file_type):
    passed = True
    log = ""
    failed_ticker_list = set()
    mapping = ticker_name_maping[file_type]
    mismatches = []
    missing = []
    for idx, row in df.iterrows():
        ticker = row['Ticker']
        security_name = row['Security Name']
        expected = mapping.get(ticker)
        if expected is None:
            # This ticker is not in the mapping
            log += f"Ticker {ticker} not found in {file_type} mapping.\n"
            print(f"Ticker {ticker} not found in {file_type} mapping.")
            failed_ticker_list.add(ticker)
            missing.append((idx, ticker, security_name, expected))
            passed=False
            continue
        if security_name != expected:
            failed_ticker_list.add(ticker)
            passed=False
            mismatches.append((idx, ticker, security_name, expected))
    if mismatches:
        passed=False
        print(f"Mismatches found in {file_type} dataframe:")
        log += f"Mismatches found in {file_type} dataframe:\n"
        for idx, ticker, sec_name, expected in mismatches:
            log += f"Row {idx}: Ticker {ticker}, Security Name '{sec_name}' does not match expected '{expected}'\n"
            print(f"Row {idx}: Ticker {ticker}, Security Name '{sec_name}' does not match expected '{expected}'")
    elif missing:
        passed=False
        print(f"Missing ticker found in {file_type} dataframe:")
        log += f"Missing ticker found in {file_type} dataframe:\n"
        for idx, ticker, sec_name, expected in missing:
            log += f"Row {idx}: Ticker {ticker}, Security Name '{sec_name}' not present in ticker security mapping\n"
            print(f"Row {idx}: Ticker {ticker}, Security Name '{sec_name}' not present in ticker security mapping")
    else:
        log += f"All ticker mappings in {file_type} dataframe are correct.\n\n"
        print(f"All ticker mappings in {file_type} dataframe are correct.")
    return log, failed_ticker_list, passed

def check_signals(df,file_type):
    log,failed_ticker_list, passed = check_ticker_and_security_mapping(df, file_type)
    print(f'Checking {file_type} file')
    for ticker in list(ticker_name_maping[file_type].keys()):
        if ticker not in df['Ticker'].unique():
            print(f'Ticker {ticker} not present in the {file_type} file')
            log += f"Ticker {ticker} not present in the {file_type} file\n\n"
            failed_ticker_list.add(ticker)
            passed = False
            continue
        
        pos_tic_df = df[(df['Ticker'] == ticker) & (df['variance'] == 'positive')]
        if len(pos_tic_df[pos_tic_df['E Signal Strength'].notna()]) < 5:
            log += f"No. of positive signals for ticker {ticker}<5\n"
            print(f'No. of positive signals for ticker {ticker}<5')
            passed = False
            failed_ticker_list.add(ticker)

        if len(pos_tic_df[pos_tic_df['E Signal Strength'] <= 0]) >0:
            log += f"Value of positive signal for {ticker} <=0\n"
            print(f'Value of positive signal for {ticker} <=0')
            passed = False
            failed_ticker_list.add(ticker)
        
        neg_tic_df = df[(df['Ticker'] == ticker) & (df['variance'] == 'negative')]
        if len(neg_tic_df[neg_tic_df['G Signal Strength'].notna()]) < 5:
            log += f"No. of negative signals for ticker {ticker}<5\n"
            print(f'No. of negative signals for ticker {ticker}<5')
            passed = False
            failed_ticker_list.add(ticker)

        if len(neg_tic_df[neg_tic_df['G Signal Strength'] <= 0]) >0:
            log += f"Value of negative signal for {ticker} <=0\n"
            print(f'Value of negative signal for {ticker} <=0')
            passed = False
            failed_ticker_list.add(ticker)

    return list(failed_ticker_list), log, passed

if __name__=='__main__':
    s3_conn = s3_config()
    date_last_friday = get_last_friday()
    aigo_file_path = f"aigo_delivery/signals/AIGO_SIGNALS_{date_last_friday.replace('-','')}.xlsx"
    aigo8_file_path = f"aigo_delivery/signals/AIGO8_SIGNALS_{date_last_friday.replace('-','')}.xlsx"
    aigo_df = s3_conn.read_as_dataframe(bucket_name,aigo_file_path)
    aigo8_df = s3_conn.read_as_dataframe(bucket_name,aigo8_file_path)

    failed_ticker_aigo,log_aigo,passed_aigo = check_signals(aigo_df,'AIGO')
    print(f'No. of failed tickers AIGO: {len(failed_ticker_aigo)}')
    if len(failed_ticker_aigo)>0:
        print(f'Failed tickers AIGO: {failed_ticker_aigo}')

    failed_ticker_aigo8,log_aigo8,passed_aigo8 = check_signals(aigo8_df,'AIGO8')
    print(f'No. of failed tickers AIGO8: {len(failed_ticker_aigo8)}')
    if len(failed_ticker_aigo8)>0:
        print(f'Failed tickers AIGO8: {failed_ticker_aigo8}')

    content = f"Date last Friday: {date_last_friday}\n\n"
    content += f"AIGO file path: {aigo_file_path}\n"
    content += f"AIGO8 file path: {aigo8_file_path}\n\n"
    content += "Ticker list:\n"
    for key, tickers in ticker_name_maping.items():
        content += f"{key}: {', '.join(tickers)}\n"
    content += f"\nFailed tickers AIGO: {failed_ticker_aigo}\n\n"
    content += log_aigo
    content += f"\nFailed tickers AIGO8: {failed_ticker_aigo8}\n\n"
    content += log_aigo8
    if passed_aigo8 and passed_aigo and (len(failed_ticker_aigo) == 0) and (len(failed_ticker_aigo8) == 0):
        content += f"\nTest status: Passed"
        if env == 'prod':
            try:
                upload_file_ftp(date_last_friday,bucket_name)
            except Exception as e:
                content += f'error while uploading file to ftp: {e}'
        else:
            print('File updated on pre-prod bucket')
            content += f'File updated on pre-prod bucket not on ftp'
    else:
        content += f"\nTest status: Failed"
        email_subject = "AIGO Signals Verification Failed"
        email_body = "AIGO Signals Verification Failed\nPlease check the logs and delivery file"
        recipient_list = config['mailing']['receiver_email']
        send_email(email_subject,email_body,recipient_list)

    results_path = os.path.join(script_dir, "results.txt")

    # Write logs to the script dir.
    with open(results_path, "w") as text_file:
        text_file.write(content)
    s3_conn.upload_file(results_path,bucket_name,f"aigo_delivery/signals/logs/{date_last_friday}.txt")

    print("Content written to results.txt")