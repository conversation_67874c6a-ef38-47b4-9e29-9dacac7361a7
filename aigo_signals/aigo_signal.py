from utilities import * 

parser = argparse.ArgumentParser(prog='Aigo-Signals', description="Script for Aigo Signals")
parser.add_argument('-e', '--env')
args = parser.parse_args()
env = args.env

# upload to the bucket
if env == 'prod':
    bucket_name_upload ='eq-model-output'
elif env == 'pre':
    bucket_name_upload ='eq-pre-model-output'
else:
    print("invalid environment")
    sys.exit()

masteractive_df = pd.DataFrame(isin_data)

es_df_mapping = fetch_feature_mapping(auth_es_tr)

df_shap = fetch_shap_data_from_s3(date)

csv_files = ['AIGO_SIGNALS', 'AIGO8_SIGNALS']
combined_df = pd.read_csv(f'COMBINED_TICKERS.csv')


for csv_file in csv_files:
    df_csv = combined_df.copy()
    
    if csv_file == "AIGO_SIGNALS":
        if 'Ticker_1' in df_csv.columns and 'Security Name_1' in df_csv.columns:
            df_csv = df_csv.rename(columns={
                'Ticker_1': 'tic',
                'Security Name_1': 'security_name'
            })
            df_csv = df_csv.dropna(subset=['tic'])
            
    elif csv_file == "AIGO8_SIGNALS":
        if 'Ticker_2' in df_csv.columns and 'Security Name_2' in df_csv.columns:
            df_csv = df_csv.rename(columns={
                'Ticker_2': 'tic',
                'Security Name_2': 'security_name'
            })
            df_csv = df_csv.dropna(subset=['tic'])
    
    if df_csv.empty:
        print(f"Warning: No valid tickers found in {csv_file}")
        continue

    result_df = process_shap_analysis(masteractive_df, es_df_mapping, df_csv, df_shap)
    output_filename = f"aigo_delivery/signals/{csv_file}_{formatted_date}.xlsx"  # Changed to xlsx

    excel_buffer = BytesIO()
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        result_df.to_excel(writer, index=False)
    
    try:
        s3.put_object(Bucket=bucket_name_upload, Key=output_filename, Body=excel_buffer.getvalue())
        print(f"File successfully uploaded to S3: s3://{bucket_name_upload}/{output_filename}")
        print(f"Total rows in output: {len(result_df)}")
    except Exception as e:
        print(f"Error uploading to S3: {str(e)}")



#----mail part

subject = f"AIGO8 and AIGO Signal File with SHAP - {date}"
sender_email=config['mailing']['sender_email']
receiver_email = config['mailing']['receiver_email']
msg = MIMEMultipart("alternative")
msg["Subject"] = subject
msg["From"] = sender_email
msg["To"] = ", ".join(receiver_email)

html_content = f"""
<html>
  <body>
    <p>Hi Team,</p>
    <p>Please find attached the SHAP analysis reports for AIGO8 Signals and AIGO Signals.</p>
    
    <p>The files have also been uploaded to S3:</p>
    <ul>
      <li><strong>AIGO Signals:</strong> s3://{bucket_name_upload}/aigo_delivery/signals/AIGO_SIGNALS_{formatted_date}.xlsx</li>
      <li><strong>AIGO8 Signals:</strong> s3://{bucket_name_upload}/aigo_delivery/signals/AIGO8_SIGNALS_{formatted_date}.xlsx</li>
    </ul>
    
    <p>Regards,<br>Garima Yadav</p>
  </body>
</html>
"""
msg.attach(MIMEText(html_content, "html"))

files_to_attach = [
    f"aigo_delivery/signals/AIGO_SIGNALS_{formatted_date}.xlsx",
    f"aigo_delivery/signals/AIGO8_SIGNALS_{formatted_date}.xlsx"
]

for file_key in files_to_attach:
    try:
        response = s3.get_object(Bucket=bucket_name_upload, Key=file_key)
        file_content = response['Body'].read()
        
        part = MIMEBase("application", "octet-stream")
        part.set_payload(file_content)
        encoders.encode_base64(part)
        
        filename = file_key.split('/')[-1]
        part.add_header(
            "Content-Disposition",
            f"attachment; filename={filename}",
        )
        msg.attach(part)
        print(f"Attached {filename} from S3")
        
    except Exception as e:
        print(f"Error attaching {file_key}: {str(e)}")

try:
    with smtplib.SMTP(config['mailing']['smtp_server'], config['mailing']['smtp_port']) as server:
        server.starttls()
        server.login(config['mailing']['email'], config['mailing']['app_password'])
        server.sendmail(sender_email, receiver_email, msg.as_string())
    print("Email sent successfully with attachments")
except Exception as e:
    print(f"Error sending email: {e}")