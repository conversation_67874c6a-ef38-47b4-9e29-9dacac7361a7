import pandas as pd
from eq_common_utils.utils.sftp_helper import Sftp
import tempfile
import os
from utilities import *

def upload_file_ftp(date,bucket_name):
    s3_client = s3_config()._s3Client
    host = config['ftp']['host']
    port = config['ftp']['port']
    usr = config['ftp']['username']
    pwd = config['ftp']['password']
    sftp = Sftp(host, usr, pwd,port)
    sftp.connect()
    print("SFTP object:", sftp) 
    print("Connection status:", sftp.connection)
    aigo_filename = f"AIGO_SIGNALS_{date.replace('-','')}.xlsx"
    aigo8_filename = f"AIGO8_SIGNALS_{date.replace('-','')}.xlsx"
    s3_path = 'aigo_delivery/signals'
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        aigo_local_file_path = os.path.join(temp_dir, aigo_filename)
        aigo8_local_file_path = os.path.join(temp_dir, aigo8_filename)
        aigo_s3_file_path = f'{s3_path}/{aigo_filename}'
        aigo8_s3_file_path = f'{s3_path}/{aigo8_filename}'
        # Download the xlsx files from S3 into the temp directory
        s3_client.download_file(Bucket=bucket_name, Key=aigo_s3_file_path, Filename=aigo_local_file_path)
        s3_client.download_file(Bucket=bucket_name, Key=aigo8_s3_file_path, Filename=aigo8_local_file_path)

        try:
            if sftp.connection is None:
                raise Exception("connection fail")

            print(f"Uploading {aigo_filename}...")
            sftp.upload(aigo_local_file_path, f"/test/{aigo_filename}")

            print(f"Uploading {aigo8_filename}...")
            sftp.upload(aigo8_local_file_path, f"/test/{aigo8_filename}")

            print("successfull ")

        except Exception as e:
            print(f"Err: {str(e)}")

    remote_dir = "/test" 
    files = list(sftp.listdir(remote_dir))
    print(f" Files in '{remote_dir}':", files)