import pandas as pd
import numpy as np
import requests
import json
import os
from datetime import datetime, timedelta
from pandas.tseries.offsets import BDay
import argparse
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from io import StringIO
from io import BytesIO 
from credentials import * 
import smtplib
import sys
import yaml
from dateutil.relativedelta import relativedelta, FR
from eq_common_utils.utils.config.s3_config import s3_config

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")
env_path = os.path.join(script_dir, ".env")
with open(config_path,'r') as f:
    config = yaml.safe_load(f)

#handle date
date = (datetime.now() + relativedelta(weekday=FR(-1))).strftime('%Y-%m-%d')
print(date)
date_obj = datetime.strptime(date, "%Y-%m-%d")
formatted_date =date_obj.strftime("%Y%m%d")
print(formatted_date)



etf_json = requests.get("http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=etf").json()
etf = pd.DataFrame(etf_json["data"]["masteractivefirms_etf"])

if 'isin' in etf.columns:
    isin_data = etf[['isin','tic']]
else:
    print("Column 'isin' not found in the DataFrame")
# masteractive_df = pd.DataFrame(isin_data)
#print(masteractive_df)

def send_email(subject, body, recipient_list):
    # Sender email address
    from_email = config['mailing']['email']
    app_password = config['mailing']['app_password']

    # Set up the MIME object
    msg = MIMEMultipart()
    msg['From'] = from_email
    msg['To'] = ', '.join(recipient_list)  # Join multiple email addresses with a comma
    msg['Subject'] = subject

    # Attach the email body
    msg.attach(MIMEText(body, 'plain'))
    
    try:
        # Establish a connection to the SMTP server
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            server.starttls()
            server.login(from_email, app_password)
    
            # Send the email
            server.sendmail(from_email, recipient_list, msg.as_string())

    except Exception as e:
        print('Failure sending email, due to exception:', e)



def fetch_feature_mapping(auth_es):
    index_name = "features_mapping" 
    query = {
        "_source": ["definition"],
        "size": 10000,
        "query": {
            "match_all": {} 
        }
    }
    try:
        url = f"https://{auth_es.aws_host}/{index_name}/_search"
        response = requests.post(
            url,
            auth=auth_es,
            json=query,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        hits = response.json()['hits']['hits']
        if not hits:
            print(f"No documents found in index: {index_name}")
            return pd.DataFrame()
        data = []
        for hit in hits:
            data.append({
                '_id': hit.get('_id'),
                'definition': hit.get('_source', {}).get('definition')
            })
            
        return pd.DataFrame(data)
        
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            print(f"Index not found: {index_name}")
        else:
            print(f"HTTP Error: {str(e)}")
        return pd.DataFrame()
    except Exception as e:
        print(f"Error fetching data from ES: {str(e)}")
        return pd.DataFrame()

# es_df_mapping = fetch_feature_mapping(auth_es_tr)
#print(es_df_mapping )




def fetch_shap_data_from_s3(date):
    bucket_name = 'micro-ops-output'
    s3_key = f'test/SHAP/etf/2025/report/shap_summary/{date}.csv'    
    response = s3.get_object(Bucket=bucket_name, Key=s3_key)
    df_shap = pd.read_csv(response['Body'])
    
    # Ensure ISIN column is properly named
    if 'isin' not in df_shap.columns and 'ISIN' in df_shap.columns:
        df_shap = df_shap.rename(columns={'ISIN': 'isin'})
    
    return df_shap
# df_shap = fetch_shap_data_from_s3(date)
#print(df_shap)




# def read_csv_file(csv_file):
#     df_csv = pd.read_csv(csv_file)
#     if 'Ticker' in df_csv.columns:
#         df_csv = df_csv.rename(columns={'Ticker': 'tic'})
#     return df_csv



def process_shap_analysis(masteractive_df, es_df_mapping, df_csv, df_shap):
    result_rows = []
    unique_tickers = df_csv['tic'].unique()
    
    for ticker in unique_tickers:
        csv_row = df_csv[df_csv['tic'] == ticker].iloc[0]        
        security_name = csv_row['security_name']        
        lookup_ticker = ticker
        
        if ticker == 'METYS':
            lookup_ticker = 'HSMETYSN'
        elif ticker == 'MQFTUSE1':
            lookup_ticker = 'SPY'
        elif ticker == 'MQFTUSN1':
            lookup_ticker = 'QQQ'
        elif ticker =="MQFTUSS1":
            lookup_ticker ='IWM'
        
        isin_match = masteractive_df[masteractive_df['tic'] == lookup_ticker]
        
        if isin_match.empty:
            print(f"Warning: No ISIN found for ticker {lookup_ticker} (original: {ticker})")
            continue
            
        isin = isin_match.iloc[0]['isin']
        shap_row = df_shap[df_shap['isin'] == isin]
        
        if shap_row.empty:
            print(f"Warning: No SHAP data found for ISIN {isin} (ticker: {ticker})")
            continue
            
        shap_data = shap_row.iloc[0]
        shap_values = []
        feature_names = []
        
        for i in range(1, len(df_shap)):
            shap_col = f'Shap_Value_{i}'
            feature_col = f'Feature_Name_{i}'
            
            if shap_col in shap_data and feature_col in shap_data:
                if pd.notna(shap_data[shap_col]) and pd.notna(shap_data[feature_col]):
                    shap_values.append(float(shap_data[shap_col]))
                    feature_names.append(str(shap_data[feature_col]))
        
        if not shap_values:
            print(f"Warning: No valid SHAP values found for ISIN {isin} (ticker: {ticker})")
            continue
            
        shap_array = np.array(shap_values)
        features_array = np.array(feature_names)

    
        
        positive_mask = shap_array > 0
        positive_indices = np.where(positive_mask)[0]
        top_positive_idx = positive_indices[np.argsort(np.abs(shap_array[positive_indices]))[::-1]][:5] if len(positive_indices) > 0 else []
        
        negative_mask = shap_array < 0
        negative_indices = np.where(negative_mask)[0]
        top_negative_idx = negative_indices[np.argsort(np.abs(shap_array[negative_indices]))[::-1]][:5] if len(negative_indices) > 0 else []
        


        
        positive_definitions = []
        positive_shap_vals = []
        for idx in top_positive_idx:
            feature_name = features_array[idx]
            shap_val = shap_array[idx]
            definition_match = es_df_mapping[es_df_mapping['_id'] == feature_name]
            definition = definition_match.iloc[0]['definition'] if not definition_match.empty else feature_name
            positive_definitions.append(definition)
            positive_shap_vals.append(shap_val)
        
        negative_definitions = []
        negative_shap_vals = []
        for idx in top_negative_idx:
            feature_name = features_array[idx]
            shap_val = abs(shap_array[idx])  
            definition_match = es_df_mapping[es_df_mapping['_id'] == feature_name]
            definition = definition_match.iloc[0]['definition'] if not definition_match.empty else feature_name
            negative_definitions.append(definition)
            negative_shap_vals.append(shap_val)
   
        positive_definitions.extend([''] * (5 - len(positive_definitions)))
        positive_shap_vals.extend([''] * (5 - len(positive_shap_vals)))
        negative_definitions.extend([''] * (5 - len(negative_definitions)))
        negative_shap_vals.extend([''] * (5 - len(negative_shap_vals)))
  
        for i in range(5):
            result_rows.append({
                'Security Name': security_name,
                'Ticker': ticker, 
                'variance': "positive",
                'Positive Drivers': positive_definitions[i],
                'E Signal Strength': positive_shap_vals[i],
                'Negative Drivers': '',
                'G Signal Strength': ''
            })
            
        for i in range(5):
            result_rows.append({
                'Security Name': security_name,
                'Ticker': ticker,
                'variance': "negative",
                'Positive Drivers': '',
                'E Signal Strength': '',
                'Negative Drivers': negative_definitions[i],
                'G Signal Strength': negative_shap_vals[i]
            })
            
    return pd.DataFrame(result_rows)




