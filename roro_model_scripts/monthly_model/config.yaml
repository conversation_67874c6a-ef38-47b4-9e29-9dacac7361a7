s3:
  versioning:
    bucket_name: eq-model-output
    model_name: roro_model
  bucket_name: micro-ops-output
  deployment_details_path: test/rishij/Risk_On_Risk_Off/monthly_model/deployment_details.csv
  inference_data_path: test/rishij/Risk_On_Risk_Off/monthly_model/data/inference_data.csv
  training_data_path: test/rishij/Risk_On_Risk_Off/monthly_model/data/training_data.csv
  model_base_path: test/rishij/Risk_On_Risk_Off/monthly_model/models
  output_path: test/rishij/Risk_On_Risk_Off/monthly_model/predictions
  input_features_list_path: test/rishij/Risk_On_Risk_Off/monthly_model/input_feature_list/USA_monthly_features.pkl
  raw_data:
    raw_data_path: test/rishij/Risk_On_Risk_Off/monthly_model/data/raw_data
    treasury_yield_path: treasuryYieldUSA.csv
    macro_bond_path: macro_predictions_Bond.csv
    macro_equity_path: macro_predictions_Equity.csv
    spy_path: SPY.csv
    tlt_path: TLT.csv
    shy_path: SHY.csv
    labels_path: labels.csv
    treasury_bond_volume_excel_path: US-Treasury-Securities-Statistics-SIFMA.xlsx
    treasury_bond_volume_csv_path: Treasury_Bond_vol.csv
    combined_file_path: combined.csv
  us_bond_path: scraper/US-bond/
  scaler_path: model/std_scaler.bin
  input_columns_file: input_columns.pkl
  model_path: model/model.keras
open_search:
  ron_roff_prediction_index: eq_ron_roff_model
  macro_model_prediction_index: eq_macro_model
constants:
  alpha: 1e-15
features:
  fred_url: https://fred.stlouisfed.org/graph/fredgraph.csv
  fred_list: 
    - GVZCLS
    - fedfunds
    - T10Y2Y
    - T5YIFR
    - BAMLC0A4CBBBEY
    - USEPUINDXD

  yf_list: 
    - GC=F

  snp_list: 
    - SPY
    - SHY
    - TLT
snp:
  url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json
  authorization: Basic ************************************************
  content_type: application/json
  closeprice:
    mnemonic: IQ_CLOSEPRICE_ADJ
    return: AdjClose
  volume:
    mnemonic: IQ_VOLUME
    return: Volume
  function: GDSHE
country_codes:
  usa: USA
valid_country_codes: ['USA']
freqs:
  Monthly: 21
  Semi-Monthly: 11
  Weekly: 5
input_features:
  renamed_input_columns:
    FEDFUNDS: "Fed Fund Rate"
    T10Y2Y: "10Y-2Y Treasury Rate"
    T5YIFR: "5Y, 5Y Forward Inflation Expectation Rate"
    BAMLC0A4CBBBEY: "ICE BofA BBB US Corporate Index Effective Yield"
    USEPUINDXD: "Eco Policy Uncertainity Index US"
    GVZCLS: "CBOE Gold ETF Volatility Index"
    GC=F_Close: "Gold Futures (Close)"
    GC=F_Volume: "Gold Futures (Volume)"
    SPY_Volume: "SPY Volume Daily"
    SHY_Volume: "SHY Volume Daily"
    TLT_Volume: "TLT Volume Daily"
    Treasury Bond Volume: "Treasury Bond Volume"
    Macro Economic Prediction ER Bond: "Macro Economic Prediction ER Bond"
    Macro Economic Prediction ER Equity: "Macro Economic Prediction ER Equity"
  target_input_columns:
    target: "Target (1-rON 0-rOFF)"
  treasury_bond_volume:
    Total: "Treasury Bond Volume"
    "Unnamed: 0": Year
  macro_model:
    bond:
      predictions: "Macro Economic Prediction ER Bond"
    equity:
      predictions: "Macro Economic Prediction ER Equity"
  yf_columns:
    - "Date"
    - "Close"
    - "Volume"
data_start_date: "2002-07-26"
local_artifact_path: /home/<USER>/rishij/github/ds-inference-scripts/roro-dev/DS-Inference-Scripts/roro_model_scripts/trained_monthly_models