from utils import *

warnings.filterwarnings('ignore')
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")
bucket_name = config['s3']['bucket_name']
with open(config_path,'r') as f:
    config = yaml.safe_load(f)

def upload_models(month,year,model_identifier, timestamp, artifacts,s3_prefix):
    s3_conn = s3_config()
    formatted_timestamp = datetime.strptime(timestamp, "%Y-%m-%d_%H:%M:%S").replace(tzinfo=timezone.utc).isoformat(sep=' ')
    new_deployment_dict = {'month':[f'{year}_{month}'],
                           'model_identifier':[model_identifier],
                           'timestamp': [formatted_timestamp],
                           'model_s3_path':[f"{s3_prefix}/model/{model_identifier}_model.keras"],
                           'input_features_s3_path':[f"{s3_prefix}/{model_identifier}_input_features.pkl"],
                           'scaler_s3_path':[f"{s3_prefix}/model/{model_identifier}_std_scaler.bin"]}
    new_deployment_df = pd.DataFrame(new_deployment_dict)
    deployment_datails_df = s3_conn.read_as_dataframe(bucket_name,config['s3']['deployment_details_path'])

    if f'{year}_{month}' not in list(deployment_datails_df['month']):
        deployment_datails_df = pd.concat([deployment_datails_df,new_deployment_df])
    else:
        deployment_datails_df.set_index('month',inplace=True)
        new_deployment_df.set_index('month',inplace=True)
        deployment_datails_df.update(new_deployment_df)
        deployment_datails_df.reset_index(drop=False,inplace=True)
    
    folder_path = artifacts
    print(folder_path)
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            local_path = os.path.join(root, filename)
            relative_path = os.path.relpath(local_path, folder_path)
            s3_key = os.path.join(s3_prefix, relative_path).replace('\\', '/')
            print(f'Uploading {local_path} to s3://{bucket_name}/{s3_key}')
            s3_conn.upload_file(local_path, bucket_name, s3_key)
    s3_conn.write_advanced_as_df(deployment_datails_df,bucket_name,config['s3']['deployment_details_path'])

def train_model_monthly(period,month,year,country_code):
    s3_conn = s3_config()
    target_column = config['input_features']['target_input_columns']['target']
    artifacts = f"{config['local_artifact_path']}/{year}_{month}"

    if not os.path.exists(artifacts):
        os.mkdir(artifacts)
        os.mkdir(f'{artifacts}/model')
    
    df = s3_conn.read_as_dataframe(config['s3']['bucket_name'],config['s3']['training_data_path'])
    df['date'] = pd.to_datetime(df['date'])
    df = df[df['date']>=config['data_start_date']]
    if month == 1:
        df = df[df['date']<= f'{year-1}-12-31']
    else:
        df = df[df['date']< f'{year}-{month}-01']

    business_days = pd.bdate_range(df['date'].min(), df['date'].max())
    df = df[df['date'].isin(business_days)]
    indices_to_keep = list(set(pd.to_datetime(pd.bdate_range(df['date'].min(), df['date'].max()))).intersection(set(df['date'])))
    df = df.drop_duplicates(subset = 'date').set_index('date').loc[indices_to_keep].sort_index()
    targets = df[target_column][~df[target_column].isna()]
    first = targets.index[0]
    last = targets.index[-1]
    df = df.loc[first: last, :]
    df.ffill(inplace= True)
    df.fillna(0, inplace = True)
    input_columns = read_pkl(config['s3']['bucket_name'],config['s3']['input_features_list_path'])

    timestamp = datetime.now(ZoneInfo("Asia/Kolkata")).strftime("%Y-%m-%d_%H:%M:%S")
    if month==1:
        model_identifier = f"{country_code}_{year-1}_12_{timestamp}"
    if month<=10:
        model_identifier = f"{country_code}_{year}_0{month-1}_{timestamp}"
    else:
        model_identifier = f"{country_code}_{year}_{month-1}_{timestamp}"
    with open(f'{artifacts}/{model_identifier}_input_features.pkl','wb') as f:
        pickle.dump(input_columns,f)
    
    # standardisation
    scaler = StandardScaler()
    scaler.fit(df[input_columns].values)
    input_data_values = scaler.transform(df[input_columns].values)
    print(input_data_values.shape)
    
    # save the scaler object
    dump(scaler, f'{artifacts}/model/{model_identifier}_std_scaler.bin', compress=True)
    
    # prepare training data
    data_X = []
    data_y = []
    for i in tqdm.tqdm(range(period, (len(df)+1))):
        data_X.append(input_data_values[i - period: i])
        data_y.append(df.iloc[i-1][target_column])

    data_X, data_y = np.array(data_X), np.array(data_y)
    print('X shape:', data_X.shape)
    print('Y shape:', data_y.shape)

    filepath = f'{artifacts}/model/{model_identifier}_model.keras'
    checkpoint = tf.keras.callbacks.ModelCheckpoint(filepath=filepath, 
                             monitor = 'accuracy',
                             verbose = 1, 
                             save_best_only = True,
                             save_weights_only = False,
                             mode = 'max')
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='accuracy',
        min_delta=0.001,           # Minimum change to qualify as an improvement
        patience=10,               # Number of epochs with no improvement after which training will be stopped
        verbose=1,
        mode='max',
        restore_best_weights=True  # Restore model weights from the epoch with the best value of the monitored metric
    )
    X_train = data_X
    y_train = data_y
    model = tf.keras.Sequential([
        # Convolutional layer
        tf.keras.layers.Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=(X_train.shape[1], X_train.shape[2])),
        
        # Additional Conv1D layer
        tf.keras.layers.Conv1D(filters=128, kernel_size=3, activation='relu'),

        # LSTM layers
        tf.keras.layers.LSTM(128, activation='relu', return_sequences=True),
        tf.keras.layers.LSTM(64, activation='relu'),

        # Dropout layers
        tf.keras.layers.Dropout(0.2),
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.4),
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dropout(0.4),
        tf.keras.layers.Dense(16, activation='relu'),

        # Output layer
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    print(model.summary())
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    history = model.fit(X_train, y_train, epochs=100, batch_size=32, callbacks = [checkpoint,early_stopping])
    
    s3_prefix= f"{config['s3']['model_base_path']}/{year}_{month}"
    upload_models(month,year,model_identifier,timestamp,artifacts,s3_prefix)
    if os.path.exists(artifacts):
        shutil.rmtree(artifacts)
    return history

if __name__=='__main__':
    current_year = datetime.today().year
    current_month = datetime.today().month
    parser = argparse.ArgumentParser(
                    prog='Risk-On Risk-Off Training',
                    description='Train Risk-On Risk-Off model for current month')
    parser.add_argument('-c', '--country_code')
    args = parser.parse_args()
    country_code = args.country_code
    if country_code not in config['valid_country_codes']:
        print('Invalid Country Code')
        sys.exit()
    context_length = 22
    for year in range(current_year,current_year+1):
        for month in range(current_month,current_month+1):
            results = train_model_monthly(context_length, month,year,country_code)
    
    print("Training complete for all months in the year range.")
