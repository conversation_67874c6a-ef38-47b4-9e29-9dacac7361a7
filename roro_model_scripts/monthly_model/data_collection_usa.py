from utils import *
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")

with open(config_path,'r') as f:
    config = yaml.safe_load(f)

raw_data_folder = config['s3']['raw_data']['raw_data_path']
fred_list = config['features']['fred_list']
yf_list = config['features']['yf_list']
snp_list = config['features']['snp_list']
bucket_name = config['s3']['bucket_name']
us_bond_path = config['s3']['us_bond_path']
inference_data_path = config['s3']['inference_data_path']
training_data_path = config['s3']['training_data_path']

start_year = int(config['data_start_date'][:4])
end_year = datetime.today().year

def fetch_raw_data(raw_data_folder):
    s3_conn = s3_config()
    # Fetch data from fred
    for query in fred_list:
        fred_scraper(query, bucket_name, raw_data_folder)
    
    # Fetch data from Yahoo finance
    for query in yf_list:
        yf_scraper(query, bucket_name, raw_data_folder)

    # Fetch data from S&P
    for query in snp_list:
        snp_scraper(query, bucket_name, raw_data_folder)
    
    # fetch US Bond Data
    latest_file_key = s3_conn.get_latest_obj(bucket_name,us_bond_path)['Key']
    treasury_yield_df = s3_conn.read_as_dataframe(bucket_name,latest_file_key)
    treasury_yield_df['Date'] = pd.to_datetime(treasury_yield_df['Date'].apply(lambda x: re.sub('\n', '', x)))
    treasury_yield_df = treasury_yield_df.loc[(treasury_yield_df['Date'] >= config['data_start_date']), :]
    treasury_yield_df = treasury_yield_df.set_index('Date')
    s3_conn.write_advanced_as_df(treasury_yield_df,bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['treasury_yield_path']}")

    # Fetch Macro Model Data
    entries = []
    for year in range(start_year,end_year+1):
        res_year = get_macro_model_data(year,config['country_codes']['usa'])
        for i in res_year['hits']['hits']:
            entries.append(i['_source'])
    macro_df = pd.DataFrame(entries)
    macro_bond_df = macro_df[macro_df['type'] == 'Bond']
    macro_equity_df = macro_df[macro_df['type'] == 'Equity']

    s3_conn.write_advanced_as_df(macro_bond_df,bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['macro_bond_path']}")
    s3_conn.write_advanced_as_df(macro_equity_df,bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['macro_equity_path']}")

    # Create Labels for training
    spy_df = s3_conn.read_as_dataframe(bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['spy_path']}")
    tlt_df = s3_conn.read_as_dataframe(bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['tlt_path']}")
    tlt_df['Date'] = pd.to_datetime(tlt_df['Date'],format = '%m/%d/%Y')
    spy_df['Date'] = pd.to_datetime(spy_df['Date'],format = '%m/%d/%Y')
    tlt_df.rename(columns = {'AdjClose':'TLT_AdjClose'},inplace=True)
    spy_df.rename(columns = {'AdjClose':'SPY_AdjClose'},inplace=True)
    merged_df = pd.merge(spy_df,tlt_df,on=['Date'],how='left')
    merged_df['TLT Monthly Return'] = merged_df['TLT_AdjClose'].pct_change(periods=22).shift(-22)
    merged_df['SPY Monthly Return'] = merged_df['SPY_AdjClose'].pct_change(periods=22).shift(-22)
    merged_df =merged_df.dropna()
    merged_df['target'] = merged_df.apply(get_labels,axis=1)
    s3_conn.write_advanced_as_df(merged_df,bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['labels_path']}")

def combine_raw_data(raw_data_folder,date):
    s3_conn = s3_config()

    # Read Label Data
    label_df = s3_conn.read_as_dataframe(bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['labels_path']}")[['Date','target']]
    label_df['Date'] = pd.to_datetime(label_df['Date'])
    start_date = label_df['Date'].min()
    dates = pd.date_range(start_date, datetime.today())
    combined_data_df = pd.DataFrame(dates, columns = ['Date']).set_index('Date')
    
    # Read Macro Data
    macro_bond_df = s3_conn.read_as_dataframe(bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['macro_bond_path']}")
    macro_equity_df = s3_conn.read_as_dataframe(bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['macro_equity_path']}")
    macro_bond_df.rename(columns={'date':'Date','predictions':config['input_features']['macro_model']['bond']['predictions']},inplace=True)
    macro_equity_df.rename(columns={'date':'Date','predictions':config['input_features']['macro_model']['equity']['predictions']},inplace=True)
    macro_bond_df.drop(columns = ['type'],inplace=True)
    macro_equity_df.drop(columns = ['type'],inplace=True)
    macro_bond_df['Date'] = pd.to_datetime(macro_bond_df['Date'],format='mixed')
    macro_equity_df['Date'] = pd.to_datetime(macro_equity_df['Date'],format='mixed')
    macro_bond_df = macro_bond_df.set_index('Date')
    macro_equity_df = macro_equity_df.set_index('Date')
    combined_data_df = combined_data_df.join(macro_bond_df)
    combined_data_df = combined_data_df.join(macro_equity_df)
    combined_data_df.loc[:, config['input_features']['macro_model']['bond']['predictions']].replace(0.0, np.nan, inplace=True)
    combined_data_df.loc[:, config['input_features']['macro_model']['equity']['predictions']].replace(0.0, np.nan, inplace=True)

    # Read Fred Data
    for file in fred_list:
        df = s3_conn.read_as_dataframe(bucket_name,f'{raw_data_folder}/{file}.csv')
        df.rename(columns={'observation_date': 'Date'},inplace=True)
        df['Date'] = pd.DatetimeIndex(df['Date'])
        df = df.set_index('Date')
        combined_data_df = combined_data_df.join(df)

    # Read Yahoo Finance Data
    for file in yf_list:
        df = s3_conn.read_as_dataframe(bucket_name,f'{raw_data_folder}/{file}.csv')[config['input_features']['yf_columns']]
        df['Date'] = df['Date'].str[:10]
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.set_index('Date')
        df.columns = [file + '_' + x for x in df.columns]
        combined_data_df = combined_data_df.join(df)

    # Read S&P Data
    for file in snp_list:
        df = s3_conn.read_as_dataframe(bucket_name,f'{raw_data_folder}/{file}.csv')
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.set_index('Date')
        df.columns = [file + '_' + x for x in df.columns]
        combined_data_df = combined_data_df.join(df)

    # Read Treasury Yield Data
    df = s3_conn.read_as_dataframe(bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['treasury_yield_path']}")
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.set_index('Date')
    df.columns = ['treasuryYield_' + x for x in df.columns]
    combined_data_df = combined_data_df.join(df)

    # Read Treasury Bond Volume And update csv file with new available data from excel file
    file_obj, _ = s3_conn.read_as_stream(bucket=bucket_name,path=f"{raw_data_folder}/{config['s3']['raw_data']['treasury_bond_volume_excel_path']}")
    df1 = pd.read_excel(io=io.BytesIO(file_obj), sheet_name='Trading Volume', header=8)
    df1 = df1.rename(columns=config['input_features']['treasury_bond_volume'])
    df1.loc[df1['Year']==f'YTD {end_year}','Year'] = end_year
    df1['Year'] = pd.to_numeric(df1['Year'],errors='coerce')
    df1 = df1.dropna(subset=['Year'])
    df1['Year'] = df1['Year'].astype(int)
    df1 = df1[list(config['input_features']['treasury_bond_volume'].values())]
    df2 =  s3_conn.read_as_dataframe(bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['treasury_bond_volume_csv_path']}")
    df_new = df1[~df1['Year'].isin(df2['Year'].to_list())]
    if len(df2)!=0:
        df2 = pd.concat([df2,df_new],ignore_index=True)
    df1 = df1.set_index('Year')
    df2 = df2.set_index('Year')
    df2.update(df1)
    df2 = df2.reset_index(drop=False)
    s3_conn.write_advanced_as_df(df2,bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['treasury_bond_volume_csv_path']}")
    df2 = df2.set_index('Year')
    df_dict = df2.to_dict()[list(config['input_features']['treasury_bond_volume'].values())[0]]
    combined_data_df[list(config['input_features']['treasury_bond_volume'].values())[0]] = [df_dict[x.year] if x.year <= end_year else np.nan for x in combined_data_df.index]
    combined_data_df = combined_data_df.join(label_df.set_index('Date'))
    s3_conn.write_advanced_as_df(combined_data_df,bucket_name,f"{raw_data_folder}/{config['s3']['raw_data']['combined_file_path']}")

    input_data = pd.DataFrame(combined_data_df.index, columns = ['Date']).set_index('Date')
    isBusinessDay = input_data.index.to_series().apply(is_business_day)
    input_data = input_data.loc[isBusinessDay]
    for ticker in config['features']['snp_list']:
        input_data = process_ticker(ticker,combined_data_df,input_data,isBusinessDay)
    
    combined_data_df['GVZCLS'][combined_data_df['GVZCLS'] == '.'] = np.nan
    combined_data_df['GVZCLS'] = combined_data_df['GVZCLS'].astype(float)
    input_data = input_data.join(get_returns(combined_data_df['GVZCLS'],isBusinessDay)).rename(columns = {'GVZCLS': 'VIX Monthly Return'})
    input_data.loc[(input_data['VIX Monthly Return'] == 0.0), 'VIX Monthly Return'] = np.nan
    
    ty_dict = {x: 'Treasury Yield USA (' + x[len('treasuryYield_'):] + ')' for x in combined_data_df.columns if 'treasuryYield_' in x}
    for col in ty_dict.keys():
        combined_data_df[col] = [float(str(x).strip()) if (str(x).strip() != 'nan' and str(x).strip() != 'N/A' and str(x).strip() != '') else np.nan for x in combined_data_df[col]]
        combined_data_df[col] = combined_data_df[col].ffill()
    input_data = input_data.join(combined_data_df[list(ty_dict.keys())].rename(columns = ty_dict))
    input_data['Month_of_year'] = input_data.index.month
    input_data['Month_of_year'] = input_data['Month_of_year'].astype(float)
    input_data = input_data.join(combined_data_df[list(config['input_features']['renamed_input_columns'].keys())].ffill(axis = 0).rename(columns = config['input_features']['renamed_input_columns']))
    input_data = input_data.join(combined_data_df[list(config['input_features']['target_input_columns'].keys())].rename(columns = config['input_features']['target_input_columns']))
    non_float_cols = input_data.columns[input_data.dtypes != float]
    for col in tqdm.tqdm(non_float_cols):
        input_data[col] = [float(x) if check_floats(x) else np.nan for x in input_data[col]]
    
    input_data.reset_index(drop=False,inplace=True)
    input_data.rename(columns={'Date':'date'},inplace=True)
    s3_conn.write_advanced_as_df(input_data[input_data['date']<date],bucket_name,inference_data_path)
    input_data = input_data.dropna(subset = [config['input_features']['target_input_columns']['target']])
    s3_conn.write_advanced_as_df(input_data[input_data['date']<date],bucket_name,training_data_path)
if __name__=='__main__':
    date_today = datetime.strftime(datetime.today(),format='%Y-%m-%d')
    fetch_raw_data(raw_data_folder)
    combine_raw_data(raw_data_folder, date_today)