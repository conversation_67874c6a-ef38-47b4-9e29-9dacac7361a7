from utils import *
script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")

with open(config_path,'r') as f:
    config = yaml.safe_load(f)

bucket_name = config['s3']['bucket_name']
inference_data_path = config['s3']['inference_data_path']
model_base_path = config['s3']['model_base_path']
output_path = config['s3']['output_path']

def gen_predictions(start_date,end_date,year,month,period,input_data_df,country_code):
    s3_conn = s3_config()
    deployment_details_df = s3_conn.read_as_dataframe(config['s3']['bucket_name'],config['s3']['deployment_details_path'])
    input_col_path = deployment_details_df.loc[deployment_details_df['month'] == f'{year}_{month}','input_features_s3_path'].values[0]
    file_obj, _ = s3_conn.read_as_stream(bucket_name, input_col_path)
    input_features = pickle.loads(file_obj)
    scaler_path = deployment_details_df.loc[deployment_details_df['month'] == f'{year}_{month}','scaler_s3_path'].values[0]
    binary_data, _ = s3_conn.read_as_stream(bucket_name, scaler_path)
    scaler = joblib.load(io.BytesIO(binary_data))
    input_data_df = input_data_df[input_features]
    input_data, indices = input_data_prep(input_data_df, period,scaler)
    print('Data shape:', input_data.shape)

    model_file_path = deployment_details_df.loc[deployment_details_df['month'] == f'{year}_{month}','model_s3_path'].values[0]
    file_obj, _ = s3_conn.read_as_stream(bucket_name, model_file_path)
    model_buffer = io.BytesIO(file_obj)
    with tempfile.NamedTemporaryFile(delete=True, suffix=".keras") as temp_file:
        temp_file.write(model_buffer.getvalue())
        temp_file.flush()
        model = keras.models.load_model(temp_file.name)
    probs = model.predict(input_data)
    predictions = np.array([1.0 if x >= 0.5 else 0.0 for x in probs])

    pred_df = pd.DataFrame({'date': indices[21:21+len(predictions)], 'prediction': predictions, 'probability': [x[0] for x in probs][:len(predictions)]})
    final_df = pd.merge(pred_df,input_data_df,how='left',on=['date'])
    daily_output_file_name = f'{output_path}/daily/{year}_{month}.csv'
    s3_conn.write_advanced_as_df(final_df, bucket_name, daily_output_file_name)

    alpha = float(config['constants']['alpha'])
    pred_df['month'] = [get_year_month(x, offset = 0) for x in pred_df['date']]
    pred_df.drop(columns = 'date', inplace = True)
    pred_df = pred_df.groupby('month').mean().reset_index()
    pred_df['prediction'] = pred_df['probability'].apply(lambda x: round(x + alpha))
    aggregate_output_file_name = f'{output_path}/aggregate/{year}_{month}.csv'
    s3_conn.write_advanced_as_df(pred_df, bucket_name, aggregate_output_file_name)

    es_client = es_config(env='prod')
    es_df = final_df.copy()
    if len(es_df) != 0:
        es_df['date'] = pd.to_datetime(es_df['date'])
        es_df = es_df.sort_values('date')
        es_df['aggregate_probability_mtd'] = es_df.groupby('date')['probability'].transform(lambda x: es_df[es_df['date'] <= x.name]['probability'].mean())
        es_df['aggregate_prediction_mtd'] = (es_df['aggregate_probability_mtd'] >= 0.5).astype(int)
        es_df = es_df[(es_df['date'] >= start_date) & (es_df['date'] <= end_date)]
        es_df.loc[:,'schedular']='Monthly'
        es_df.loc[:,'updated_at']= datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        es_df['updated_at'] = es_df['updated_at'].astype(str)
        es_df['date'] = es_df['date'].astype(str)
        es_df.loc[:,'country_code'] = country_code
        es_client.save_records_v2(es_df,config['open_search']['ron_roff_prediction_index'],primary_column='country_code',date_column='date',unique_columns=['country_code', 'date', 'schedular'])

        deployment_details_df = s3_conn.read_as_dataframe(config['s3']['bucket_name'],config['s3']['deployment_details_path'])
        version_df = es_df.copy()
        version_df['model_identifier'] = deployment_details_df[deployment_details_df['month'] == f'{year}_{month}']['model_identifier'].to_list()[0]
        version_df.apply(lambda x: save_row(x, 'predictions'), axis=1)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(
                    prog='Risk-On Risk-Off Inference',
                    description='Generate predictions for Risk-On Risk-Off model')
    parser.add_argument('-c', '--country_code')
    parser.add_argument('-s', '--start_date', default=(datetime.today()-timedelta(1)).strftime(format = '%Y-%m-%d'))
    parser.add_argument('-e', '--end_date', default=(datetime.today()-timedelta(1)).strftime(format = '%Y-%m-%d'))
    args = parser.parse_args()
    country_code = args.country_code
    start_date = args.start_date
    end_date = args.end_date
    if not is_valid_date_format(start_date,'%Y-%m-%d'):
        print('Invalid start date format')
    elif not is_valid_date_format(end_date,'%Y-%m-%d'):
        print('Invalid end date format')
    elif country_code not in config['valid_country_codes']:
        print('Invalid Country Code')
    elif datetime.strptime(start_date,'%Y-%m-%d')>datetime.strptime(end_date,'%Y-%m-%d'):
        print('Start date is after the end date')
    else:
        r = split_date_range_by_month(start_date,end_date)
        period = 22
        for date_range in r:
            month_start_date = date_range[0]
            month_end_date = date_range[1]
            year = date_range[2]
            month = date_range[3]
            input_data_df = data_prep(year,month,period,inference_data_path)
            gen_predictions(month_start_date,month_end_date,year,month,period,input_data_df,country_code)