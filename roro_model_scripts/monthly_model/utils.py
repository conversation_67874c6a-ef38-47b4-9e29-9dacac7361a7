import pandas as pd
from datetime import datetime, timedelta, timezone
from eq_common_utils.utils.config.s3_config import s3_config
import os
import yaml
import pickle
import joblib
import io
import tqdm
import numpy as np
from tensorflow import keras
from eq_common_utils.utils.config.es_config import es_config
import tempfile
import argparse
import requests
from io import StringIO
import yfinance as yf
import json
import re
from pandas.tseries.offsets import BusinessDay as BDay
import warnings
from sklearn.preprocessing import StandardScaler
import tensorflow as tf
from joblib import dump
from zoneinfo import ZoneInfo
import shutil
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")

with open(config_path,'r') as f:
    config = yaml.safe_load(f)

bucket_name = config['s3']['bucket_name']

freqs = config['freqs']

def read_pkl(bucket_name,path):
    s3_conn = s3_config()
    l,p = s3_conn.read_as_stream(bucket_name,path)
    l = pickle.load(io.BytesIO(l))
    return l

def save_row(row, data_type):
    '''
    This function is used for version control
    row -> row to be saved
    data_type -> predictions or metrics (str)
    '''

    if not isinstance(row['date'], str):
        date = pd.to_datetime(row['date']).strftime("%Y-%m-%d")
    else:
        date = row['date']
    
    assert data_type in ['predictions', 'metrics']

    model_key = config['s3']['versioning']['model_name']
    bucket_name = config['s3']['versioning']['bucket_name']
    file_name = f'{model_key}/{row["schedular"].lower()}/{date}/{data_type}/{row["country_code"]}.csv'
    
    row_df = row.to_frame().T
    s3_config().write_advanced_as_df(row_df, bucket_name, file_name)

def is_valid_date_format(date_string: str, date_format: str) -> bool:
    try:
        datetime.strptime(date_string, date_format)
        return True
    except ValueError:
        return False

def split_date_range_by_month(start: str, end: str, date_format: str = "%Y-%m-%d"):
    start_date = datetime.strptime(start, date_format)
    end_date = datetime.strptime(end, date_format)

    result = []
    current = start_date

    while current <= end_date:
        # First day of next month
        next_month = (current.replace(day=28) + timedelta(days=4)).replace(day=1)
        last_day_this_month = next_month - timedelta(days=1)

        # Define the end of the current segment
        segment_end = min(last_day_this_month, end_date)

        # Extract year and month
        year = current.year
        month = current.month

        # Append tuple
        result.append((current.strftime(date_format), segment_end.strftime(date_format), year, month))

        # Move to the next segment
        current = segment_end + timedelta(days=1)

    return result

def data_prep(year,month,period,inference_data_path):
    s3_conn = s3_config()
    input_data_df = s3_conn.read_as_dataframe(bucket_name, inference_data_path)
    input_data_df.reset_index(drop=True,inplace=True)
    input_data_df['date'] = pd.to_datetime(input_data_df['date'])
    input_data_df = input_data_df.sort_values(by='date')
    input_data_df = input_data_df.ffill().fillna(0)

    business_days = pd.bdate_range(input_data_df['date'].min(), input_data_df['date'].max())
    input_data_df = input_data_df[input_data_df['date'].isin(business_days)]
    indices_to_keep = list(set(pd.to_datetime(pd.bdate_range(input_data_df['date'].min(), input_data_df['date'].max()))).intersection(set(input_data_df['date'])))
    input_data_df = input_data_df.drop_duplicates(subset = 'date').set_index('date').loc[indices_to_keep].sort_index()
    input_data_df.reset_index(drop=False,inplace=True)
    
    if month == 12:
        start_date = f'{year}-12-01'
    else:
        start_date = f'{year}-{month}-01'
    start_idx = input_data_df[input_data_df['date'] >= start_date].index[0]
    # Select the 21 rows before that date

    input_data_df = input_data_df.iloc[start_idx - (period-1):]
    if month == 12:
        input_data_df = input_data_df[(input_data_df['date']< f'{year+1}-01-01')]
    else:
        input_data_df = input_data_df[(input_data_df['date']< f'{year}-{month+1}-01')]

    input_data_df = input_data_df.set_index('date')
    return input_data_df

def input_data_prep(df, period,scaler):
    input_data_values = scaler.transform(df)
    data_X = []
    for i in tqdm.tqdm(range(period, (len(input_data_values)+1))):
        data_X.append(input_data_values[i - period: i])
    data_X = np.array(data_X)
    return data_X, df.index

def get_year_month(date, offset = 0):
    month = date.month + offset
    year = date.year
    if month > 12:
        month = month - 12
        year = year + 1
    return str(year) + '-' + ( '0' + str(month) if month < 10 else str(month))

def fred_scraper(query, bucket_name, s3_path):
    s3_conn = s3_config()
    fred_url = config['features']['fred_url']
    print(f'Getting historical data for {query}.')
    url = f'{fred_url}?id={query}'
    
    try:
        r = requests.get(url, allow_redirects=True)
        r.raise_for_status()  # Raise an error for bad responses (4xx and 5xx)
    except requests.exceptions.RequestException as e:
        return f'Error fetching data: {e}'
    
    df = pd.read_csv(StringIO(r.text))
    
    s3_full_path = f'{s3_path}/{query}.csv'
    s3_conn.write_advanced_as_df(df, bucket_name, s3_full_path)

def yf_scraper(query, bucket_name, s3_path):
    s3_conn = s3_config()
    print(f'Getating historical data for {query}.')
    tic = yf.Ticker(query)
    data = tic.history(period='max')
    if(data.empty):
        return 'No data found for query.'
    s3_full_path = f'{s3_path}/{query}.csv'
    s3_conn.write_advanced_as_df(data, bucket_name, s3_full_path)

def get_close(data):
    """
        Function to get yield from S&P
        returns: yield
    """
    url = config['snp']['url']
    headers = {'Authorization': config['snp']['authorization'],'Content-type': config['snp']['content_type']}
    data_json = json.dumps(data)
    response = requests.post(url, data=data_json, headers=headers)
    resp_json=json.loads(response.text)
    if len(resp_json['GDSSDKResponse'][0]['ErrMsg']) > 0:
        return None
    else:
        return resp_json
    
def snp_scraper(query, bucket_name, s3_path):
    s3_conn = s3_config()
    print(f'Getting historical data for {query}.')
    start_date = '1/1/2002'
    data = {
        "inputRequests": [
        {
            "function": config['snp']['function'],
            "identifier": query,
            "mnemonic": config['snp']['closeprice']['mnemonic'],
            "properties": {
                "startDate": start_date
            }
        },
        {
            "function": config['snp']['function'],
            "identifier": query,
            "mnemonic": config['snp']['volume']['mnemonic'],
            "properties": {
                "startDate": start_date
            }
        }
      ]
    }
    
    response = get_close(data)
    if response is None:
        return 'No data found for query.'

    adj_close = [x['Row'][0] for x in response['GDSSDKResponse'][0]['Rows']]
    dates = [x['Row'][1] for x in response['GDSSDKResponse'][0]['Rows']]
    df1 = pd.DataFrame({
        'AdjClose': adj_close,
        'Date': dates
    }) 
    df1.set_index('Date', inplace = True)
    vol = [x['Row'][0] for x in response['GDSSDKResponse'][1]['Rows']]
    dates = [x['Row'][1] for x in response['GDSSDKResponse'][1]['Rows']]
    df2 = pd.DataFrame({
            'Volume': vol,
            'Date': dates
        }) 
    df2.set_index('Date', inplace = True)
    df = df1.join(df2)
    s3_full_path = f'{s3_path}/{query}.csv'
    s3_conn.write_advanced_as_df(df, bucket_name, s3_full_path)

def get_labels(row):
    if row['SPY Monthly Return']>row['TLT Monthly Return']:
        return 1
    else:
        return 0

def is_business_day(date):
    return BDay().is_on_offset(date)

def get_macro_model_data(year,country):
    es = es_config(env='prod')
    qs = {
      "_source": ["type", "date", "predictions"],
      "query": {
        "bool": {
          "must": [
            {
                "match": {
                    "country_code": country  # Single value, as match can work with analyzed fields
                }
            }, 
            {
              "range": {
                "date": {
                  "gte": f"{year}-01-01",
                  "lte": f"{year}-12-31"
                }
              }
            }
          ]
        }
      }
    }
    response = es.run_query(query=qs, index=[f"{config['open_search']['macro_model_prediction_index']}_{year}"], size=5000)
    return response

def get_returns(df,isBusinessDay, freq = freqs['Monthly']):
    df = df.loc[isBusinessDay].ffill()
    daily_returns = df.pct_change()
    monthly_returns = daily_returns.rolling(freq + 1).apply(lambda x: (1 + x).prod() - 1)
    return monthly_returns

def get_volatility(df,isBusinessDay, freq = freqs['Monthly']):
    df = df.loc[isBusinessDay].ffill()
    sq_ln_change = df.rolling(2).apply(lambda x: (np.log(x.iloc[1] / x.iloc[0]))**2, raw = False)
    log_volatility = sq_ln_change.rolling(freq + 1).std() * (252 ** 0.5)
    return log_volatility

def check_floats(string):
    if string[0] == '-':
        string = string[1:]
    return string.replace('.','',1).isdigit()

def process_ticker(ticker, combined_data_df, input_data,isBusinessDay):
    input_data = input_data.join(get_returns(combined_data_df[f"{ticker}_AdjClose"],isBusinessDay,freqs['Monthly'])).rename(columns = {f"{ticker}_AdjClose": f"{ticker} Monthly Return"}) 
    input_data = input_data.join(get_returns(combined_data_df[f"{ticker}_AdjClose"],isBusinessDay, freqs['Weekly'])).rename(columns = {f"{ticker}_AdjClose": f"{ticker} Weekly Return"}) 
    input_data = input_data.join(get_volatility(combined_data_df[f"{ticker}_AdjClose"],isBusinessDay)).rename(columns = {f"{ticker}_AdjClose": f"{ticker} Monthly Volatility"}).ffill()  
    input_data = input_data.join(get_volatility(combined_data_df[f"{ticker}_AdjClose"],isBusinessDay, freqs['Semi-Monthly'])).rename(columns = {f"{ticker}_AdjClose": f"{ticker} Semi-Monthly Volatility"}) 
    input_data = input_data.join(pd.DataFrame(input_data[f"{ticker} Monthly Volatility"].pct_change(freqs['Monthly'])).rename(columns = {f"{ticker} Monthly Volatility": f"{ticker} Monthly Volatility Change"})) 
    
    temp_df = combined_data_df[f"{ticker}_Volume"]
    temp_df = temp_df[temp_df.index[temp_df.index.map(isBusinessDay)]].ffill()
    input_data = input_data.join(pd.DataFrame(temp_df.rolling(freqs['Monthly']).mean()).rename(columns = {f"{ticker}_Volume": f"{ticker} Monthly Average Volume"}))
    input_data = input_data.join(pd.DataFrame(temp_df.rolling(freqs['Semi-Monthly']).mean()).rename(columns = {f"{ticker}_Volume": f"{ticker} Semi-Monthly Average Volume"})) 
    return input_data