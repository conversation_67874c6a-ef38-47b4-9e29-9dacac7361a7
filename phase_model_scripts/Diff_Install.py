#!/usr/bin/env python
# coding: utf-8



import subprocess
import pkg_resources
import sys
import os
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))


def parse_diff_file(diff_file_path):
    with open(diff_file_path, 'r') as file:
        lines = file.readlines()
    
    # Filter out comments and blank lines
    packages = [
        line.strip() for line in lines
        if line.strip() and not line.startswith('#')
    ]
    return packages


def is_package_installed(package_spec):
    try:
        pkg_resources.require(package_spec)
        return True
    except (pkg_resources.DistributionNotFound, pkg_resources.VersionConflict):
        return False


def install_package(package_spec):
    try:
        subprocess.check_call(["pip", "install", package_spec])
        print(f"Successfully installed {package_spec}")
    except subprocess.CalledProcessError as e:
        print(f"Failed to install {package_spec}: {e}")


def install_from_diff(diff_file_path):
    packages = parse_diff_file(diff_file_path)
    for package in packages:
        if is_package_installed(package):
            print(f"{package} is already installed. Skipping...")
        else:
            print(f"Installing {package}...")
            install_package(package)


# Example usage
if __name__ == "__main__":
    diff_file_path = f"{script_dir}/requirements_diff.txt"  # Replace with your diff file path
    install_from_diff(diff_file_path)



