tags:
    aieq_tag: aieq
    aigo_tag: aigo
    aiego_tag: aiego
    ust1_tag: tier1
    indiat1_tag: indt1
    india_tag: indeq
    aifint_tag: aifint
    indsec_tag: indsec
    
snp_creds:
    content_type: application/json
    head_auth: Basic ************************************************
    spglobal_url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json
    single_date_fn: GDSP
    daterange_fn: GDST
    resp_key: GDSSDKResponse
    single_request_limit: 500
    phase_n_days: 70
    mnem: IQ_CLOSEPRICE
    trade_check_mnem : IQ_PRICEDATE
  
url:
  masters_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=
  
prediction_credentials: 
  api_key: TU-lJAQslyN1aQfhe8MepQxkcoc9kEYN6gQoLYuFN3oG
  dallas_url: https://us-south.ml.cloud.ibm.com
  
s3_paths:
  bucket_name: phase-model
  fin_bucket_name: financial-model
  old_bucket: financial-model-data-collection
  test_s3_versioning_bucket: eq-pre-model-output
  autoai_job_bucket: autoai-jobs
  map_file: adhoc/country_identifier_mapping_new.csv
  deployment_filename: tag/schedular/country/deployment_file.csv
  aieq_deployment_filename_monthly: aieq/monthly/country/deployment_file.csv
  aieq_deployment_filename_daily: aieq/daily/country/deployment_file.csv
  aigo_deployment_filename_monthly: aigo/monthly/country/deployment_file.csv
  phase_folder: phase_data/daily_run_schedular
  daily_run_foldername_monthly: phase_data_aigo/daily_run_monthly
  daily_run_foldername_daily: phase_data_aigo/daily_run_daily
  daily_run_filename_monthly: daily_runs/daily_run_monthly/phase_predictions_date.csv
  daily_run_filename_monthly_temp: temp/daily_runs/daily_run_monthly/phase_predictions_date.csv
  daily_run_filename_daily: daily_runs/daily_run_daily/phase_predictions_date.csv
  daily_run_filename_daily_temp: temp/daily_runs/daily_run_daily/phase_predictions_date.csv
  jobcount_filename_monthly: phase-model/monthly.csv
  jobcount_filename_daily: phase-model/daily.csv
  daily_run_logs_filename_monthly: adhoc/log_files/monthly/date_daily_run.log
  daily_run_logs_filename_daily: adhoc/log_files/daily/date_daily_run.log
  s3_versioning_bucket: eq-model-output 
  s3_versioning_pred_filename_monthly: phase_model/monthly/date/predictions/isin.csv
  s3_versioning_pred_filename_daily: phase_model/daily/date/predictions/isin.csv
  s3_versioning_pred_filename_monthly_temp: temp/phase_model/monthly/date/predictions/isin.csv
  s3_versioning_pred_filename_daily_temp: temp/phase_model/daily/date/predictions/isin.csv
  s3_versioning_metrics_filename_monthly: phase_model/monthly/date/metrics/isin.csv
  s3_versioning_metrics_filename_daily: phase_model/daily/date/metrics/isin.csv
  s3_versioning_metrics_filename_monthly_temp: temp/phase_model/monthly/date/metrics/isin.csv
  s3_versioning_metrics_filename_daily_temp: temp/phase_model/daily/date/metrics/isin.csv
  aieq_metrics_s3_path_monthly: phase_data/historical_metrics/aieq/monthly
  aieq_metrics_s3_path_daily: phase_data/historical_metrics/aieq/daily
  aigo_metrics_s3_path_monthly: phase_data/historical_metrics/aigo/monthly
  aieq_metrics_s3_path_monthly_temp: temp/phase_data/historical_metrics/aieq/monthly
  aieq_metrics_s3_path_daily_temp: temp/phase_data/historical_metrics/aieq/daily
  aigo_metrics_s3_path_monthly_temp: temp/phase_data/historical_metrics/aigo/monthly

environments:
  pre_prod_env: pre-prod
  prod_env: prod
  run_env: prod
  
index:
  preprod_phase_index: pre_phase_model
  phase_model_index: eq_phase_model
  phase_metrics_index: eq_phase_model_metrics
  preprod_phase_metrics_index: pre_phase_model_metrics
  
phase:
  identifiers:
    SPHQ:ARCA: sphq_close
    VLUE:BATS: vlue_close
    MTUM:BATS: mtum_close
    IWF:ARCA: growth_close
  model_ids:
    iwf: country_G
    iwf_corr: country_G_B
    mtum: country_M
    mtum_corr: country_M_B
    sphq: country_Q
    sphq_corr: country_Q_B
    vlue: country_V
    vlue_corr: country_V_B 
    

columns:
   date_col: date
   isin_col: isin
   tic_col: tic
   exchange_col: exchange
   cp_col: closeprice
   cp_adj_col: closeprice_adj
   deployment_space_col: deployment_space
   space_id_col: space_id
   deployment_id_col: deployment_id
   pred_col: predictions
   status_col: status
   name_col: name
   er_copied_col: ER_Copied
   year_col: year
   training_date_col: training_date
   expiration_date_col: expiration_date
   test_data_col: test_data
   id_col: id
   phase_model_cols: ['vlue_er', 'mtum_er', 'iwf_er', 'sphq_er', 'vlue_corr_er', 'mtum_corr_er', 'iwf_corr_er', 'sphq_corr_er']
   auto_ai_jobs_cols: ['date','start_time','end_time','total_job_count','failed_job_count', 'success_job_count']
   cols_to_drop_from_dep: ['rmse', 'feature_importance', 'estimator']
   cols_to_keep_for_dep: ['deployment_space','deployment_id','test_data']
   id_and_mnemonic_cols: ['tic','exchange','isin','mnemonic_list_d','mnemonic_list_q','ipodate']
   benchmark_renaming_dict: {'spy_close_price': 'benchmark_closeprice',                                                         'nifty_close_price': 'benchmark_closeprice'}
   
schedulars:
    schedular_m: "Monthly"
    schedular_d: "Daily"
    schedular_w: "Weekly"
    
script_trigger_paths:
    venv_python: "/home/<USER>/scripts/envs/phase_env/bin/python"
    prediction_script_path: "/home/<USER>/scripts/phase_model_scripts/Trigger.py"    
    metrics_script_path: "/home/<USER>/scripts/phase_model_scripts/Phase_Model_Metrics_Daily_Run.py" 
       
id_mapping: {
                'vlue': 'country_V',
                'vlue_corr': 'country_V_B',
                'mtum': 'country_M',
                'mtum_corr': 'country_M_B',
                'growth': 'country_G',
                'iwf': 'country_G',
                'growth_corr': 'country_G_B',
                'iwf_corr': 'country_G_B',
                'quality': 'country_Q',
                'sphq': 'country_Q',
                'quality_corr': 'country_Q_B',
                'sphq_corr': 'country_Q_B'
            } 
            
es_id_mapping: {'sphq':'Q', 'sphq_corr':'Q_B', 'vlue':'V', 'vlue_corr':'V_B', 'mtum':'M', 'mtum_corr':'M_B', 'iwf':'G', 'iwf_corr':'G_B'}                    
aieq_country_code: "USA"
ind_country_code: "IND"
ind_benchmark_id: "^NSEI"
n_bday: 1
job_fail_threshold: 50
weekday: Monday
logger_path: folder/Log_Files_schedular/date_daily_run.log
cp_adj_mnemonic: IQ_CLOSEPRICE_ADJ
start_date: '01/01/2015'
look_back_period: 22
schedular_dict:
    Monthly: 22
    Weekly: 5
    Daily: 1
    
all_metrics: ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "confidence_score", "avg_confidence_score", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day"]    
    

email_credentials:
    sender_email: <EMAIL>
    receiver_emails: ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
    receiver_emails_for_trigger: [ '<EMAIL>', '<EMAIL>'] 
    receiver_emails_for_failure: [ '<EMAIL>', '<EMAIL>', '<EMAIL>'] 
    receiver_emails_for_less_hits: ['<EMAIL>','<EMAIL>','<EMAIL>']
    smtp_credential: bnfd pfpc hehm klpq
    server_address: smtp.gmail.com
    port_no: 587
    
common_gmail:
    scope: ['https://www.googleapis.com/auth/gmail.send']
    gmail_cred_bucket: etf-predictions
    gmail_cred_file: preportfolio/gmail_credentials/credentials.json
    
sender_name: "Sandra P <<EMAIL>>"    