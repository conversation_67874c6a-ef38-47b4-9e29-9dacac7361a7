#!/usr/bin/env python
# coding: utf-8



import argparse
import os
import sys
from Imports import *





if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='Phase Prod Script', description="Script to run data_collection with one parameter.")
    parser.add_argument('-s', '--schedular')
    
    args = parser.parse_args()
    
    schedular = args.schedular



# try:
if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()
sys.path.insert(0, os.path.abspath(script_dir))
config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)


conn = DataCollectionHelper()
conn.schedular = schedular
ph = predictionHelper()

def create_logger(log_file_path):
    log_dir = os.path.dirname(log_file_path)
    print(log_dir)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)  # Create the directory if it doesn't exist
    logger = logging.getLogger('my_logger')
    logger.setLevel(logging.DEBUG)
    for handler in logger.handlers:
        logger.removeHandler(handler)
    file_handler = logging.FileHandler(log_file_path, mode='w')  # Use mode='w' for write mode
    formatter = logging.Formatter('%(levelname)s: %(message)s')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    return logger


env = config['environments']['run_env']
print(env)
num = config['n_bday']
current_date = date.today()
saved_date = (current_date - BDay(num)).date()
log_file_path = config['logger_path'].replace('folder',script_dir).replace('date',str(saved_date)).replace('schedular',conn.schedular.capitalize())
global logger
logger = create_logger(log_file_path)

today_date = datetime.today().date() - BDay(num)
today = today_date.strftime("%m/%d/%Y")
date_string = (pd.to_datetime(today)+timedelta(1)).strftime("%Y-%m-%d")
logger.info(f"Data collection run for {today}")

es_prod = es_config(env='prod')
es_pre_prod = es_config(env='pre')

if env == 'prod':
    receivers_list = config['email_credentials']['receiver_emails_for_trigger']
else:
    #for testing purpose
    receivers_list = conn.sender_email
try:  
    subject = f'Phase model {schedular.capitalize()} Data Collection and Prediction run started'
    message_text = f"Phase model {schedular.capitalize()} Data Collection and Prediction run for {str(pd.to_datetime(today).date())} has started"
    conn.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)
except:
     logger.error("Error in sending starting mail")

conn.log_file_path = log_file_path
if schedular.lower() == config['schedulars']['schedular_m'].lower():
    conn.logs_filename = config['s3_paths']['daily_run_logs_filename_monthly']
elif schedular.lower() == config['schedulars']['schedular_d'].lower():   
    conn.logs_filename = config['s3_paths']['daily_run_logs_filename_daily']



try:
    build_dates = [today_date]
    mapping_file  = conn.s3helper.read_as_dataframe(conn.bucket_name,config['s3_paths']['map_file'])
    country_codes = list(mapping_file['country_code'])
    if schedular.lower() == conn.schedular_d.lower():
        country_codes = [config['aieq_country_code']]
    dep_list, datas = conn.prepare_data(country_codes, build_dates, schedular, ph)
    pred_df_final=ph.fetch_prediction(datas, dep_list, schedular)
    pred_df_final[config['columns']['er_copied_col']]=False
    dep_df = pd.DataFrame()
    for i in dep_list:
        dep_df = pd.concat([dep_df,i])
    pred_merged_df = pd.merge(pred_df_final, dep_df[['id','model_year',ph.training_date_col]], on='id',how='left')
    pred_merged_df.loc[pred_merged_df[ph.pred_col].isnull(), config['columns']['er_copied_col']] = True
    pred_merged_df['model_identifier'] = (
    pred_merged_df[ph.id_col].astype(str) + "_" +
    pred_merged_df['model_year'].astype(str) + "_" +
    pd.to_datetime(pred_merged_df[ph.training_date_col]).dt.strftime('%Y-%m-%d_%H:%M:%S')
)
except Exception as e:
    logger.error(f'Error in preparing input and fetching prediction, {e}')
    try:
        conn.failure_mail(f"Error in preparing input and fetching prediction: {e}", schedular, conn.log_file_path, conn.logs_filename, env)
    except Exception as e:
        logger.error(f"Error in preparing input and fetching prediction and error in sending failure mail, {e}")      
    raise SystemExit('Stop right there')   




try:
    q_group = pred_merged_df.groupby(conn.date_col)
    for name, group in q_group:
        file_date=pd.to_datetime(name).strftime("%Y-%m-%d")
        print(file_date)
        if env == 'prod':
            s3_version_bucket_name = config['s3_paths']['s3_versioning_bucket']
            daily_run_bucket_name = config['s3_paths']['bucket_name']
            es_index = config['index']['phase_model_index']
            es = es_prod
            if schedular.lower() == conn.schedular_m.lower():
                s3_version_filename = config['s3_paths']['s3_versioning_pred_filename_monthly']
                daily_run_filename = config['s3_paths']['daily_run_filename_monthly']
            elif schedular.lower() == conn.schedular_d.lower():
                s3_version_filename = config['s3_paths']['s3_versioning_pred_filename_daily'] 
                daily_run_filename = config['s3_paths']['daily_run_filename_daily']
        elif env == 'pre-prod':
            s3_version_bucket_name = config['s3_paths']['test_s3_versioning_bucket']
            daily_run_bucket_name = config['s3_paths']['old_bucket']
            es_index = config['index']['preprod_phase_index']
            es = es_pre_prod
            if schedular.lower() == conn.schedular_m.lower():
                s3_version_filename = config['s3_paths']['s3_versioning_pred_filename_monthly']
                daily_run_filename = config['s3_paths']['daily_run_filename_monthly_temp']
            elif schedular.lower() == conn.schedular_d.lower():
                s3_version_filename = config['s3_paths']['s3_versioning_pred_filename_daily']
                daily_run_filename = config['s3_paths']['daily_run_filename_daily_temp']
        s3_version_filename = s3_version_filename.replace('date',file_date)
        daily_run_filename = daily_run_filename.replace('date',file_date)
        conn.s3helper.write_advanced_as_df(group, daily_run_bucket_name, daily_run_filename)
        documents = []
        group[conn.date_col] = group[conn.date_col].dt.strftime('%Y-%m-%d')
        group['schedular'] = schedular.capitalize()
        group['updated_at'] = datetime.now()
        sch = schedular.capitalize()
        for index, row in group.iterrows():
            row = row.dropna()
            filename = s3_version_filename.replace(conn.isin_col,row['id'])
            conn.s3helper.write_advanced_as_df(pd.DataFrame([row]), s3_version_bucket_name, filename)
            doc_id = f"{row['id']}_{row[conn.date_col]}_{sch[0].lower()}"
            year = pd.to_datetime(row[conn.date_col]).year
            i_name = f'{es_index}_{year}'
            document = {"index": {"_index": i_name, "_id": doc_id}}
            data = row.to_dict()
            documents.append(document)
            documents.append(data)
    response = es.client.bulk(documents)
    if response['errors']:
        logger.error(f'Error in saving data to ES, {response["errors"]}')
except Exception as e:
    logger.error(f'Error in saving data, {e}')  
    try:
        conn.failure_mail(f"Error in saving data: {e}", schedular, conn.log_file_path, conn.logs_filename, env)
    except Exception as e:
        logger.error(f"Error in saving data and error in sending failure mail: {e}")      
    raise SystemExit('Stop right there')                    




try:
    if env == 'prod':
        receivers_list = conn.receiver_emails
    else:    
        #for testing purpose           
        receivers_list = conn.sender_email  
    subject = f'Phase {schedular.capitalize()} model daily run has been completed'
    message_text = f"""
<html>
<body>
<p>Phase {schedular.capitalize()} model run for {str(pd.to_datetime(today).date())} has been successfully completed.</p>

<p><strong>Summary:</strong><br>
  &nbsp;&nbsp;&nbsp;- Total Number of Models: {pred_merged_df['id'].nunique()}<br>
  &nbsp;&nbsp;&nbsp;- Number of Successful Runs: {pred_merged_df[ph.pred_col].notna().sum()}<br>
  &nbsp;&nbsp;&nbsp;- Number of Failed Runs: {pred_merged_df[ph.pred_col].isna().sum()}<br>  
  &nbsp;&nbsp;&nbsp;- Number of Models with 0 as Prediction: {pred_merged_df[pred_merged_df[ph.pred_col] == 0]['id'].nunique()}<br>   
</p>

<p>Records for {pred_merged_df['id'].nunique()} ISINs have been added to Elasticsearch.</p>
</body>
</html>
"""
    conn.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)
except Exception as e:
    logger.error(f"Error in sending mail regarding run completion: {e}")




conn.s3helper.upload_file(log_file_path, conn.bucket_name, conn.logs_filename.replace('date',str(datetime.now())))





