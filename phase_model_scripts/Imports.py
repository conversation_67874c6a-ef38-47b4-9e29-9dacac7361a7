#!/usr/bin/env python
# coding: utf-8


from concurrent.futures import ThreadPoolExecutor
from datetime import datetime,date,timedelta
from urllib.request import urlopen, Request
from pathlib import Path
from io import StringIO
import io
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
from pandarallel import pandarallel
pandarallel.initialize(progress_bar=True,nb_workers=4)
import numpy as np
import requests
import logging
import boto3
import json
import time
import warnings
import traceback
import yfinance as yf
warnings.filterwarnings("ignore")
from pandas.tseries.offsets import BDay
from multiprocessing.pool import ThreadPool
import os, ssl
from fredapi import Fred
from collections import defaultdict
# from tqdm.notebook import tqdm, IProgress
from tqdm import tqdm
from eq_common_utils.utils.opensearch_helper import OpenSearch
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from ibm_watsonx_ai import APIClient, Credentials
from ibm_watsonx_ai.experiment import AutoAI
from ibm_watsonx_ai.deployment import WebService, Batch
from eq_common_utils.ds_scripts.prediction_helper import PredictionHelper as BulkPrediction
from eq_common_utils.utils.ibm_helper import IBMConnection
from ibm_watsonx_ai.helpers.connections import DataConnection, S3Location
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from numpy_ext import rolling_apply as rolling_apply_ext
import yaml
import sys
import smtplib
import argparse
from email.mime.text import MIMEText
import base64
from google.oauth2.credentials import Credentials as GoogleCredentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.message import EmailMessage


if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)


global logger
logger = logging.getLogger('my_logger')


class DataCollectionHelper:
    def __init__(self):
        self.s3helper = s3_config()
        self.masters_url = config['url']['masters_url']
        self.spglobal_url = config['snp_creds']['spglobal_url']
        self.head_auth = config['snp_creds']['head_auth']
        self.content_type = config['snp_creds']['content_type']
        self.mnem = config['snp_creds']['mnem']
        self.bucket_name = config['s3_paths']['bucket_name']
        self.phase_model_index = config['index']['phase_model_index']
        self.phase_model_cols = config['columns']['phase_model_cols']
        self.cols_to_drop_from_dep = config['columns']['cols_to_drop_from_dep']
        self.cols_to_keep_for_dep = config['columns']['cols_to_keep_for_dep']
        self.id_and_mnemonic_cols = config['columns']['id_and_mnemonic_cols']
        self.date_col = config['columns']['date_col']
        self.isin_col = config['columns']['isin_col']
        self.tic_col = config['columns']['tic_col']
        self.exchange_col = config['columns']['exchange_col']
        self.sender_email = config['email_credentials']['sender_email']
        self.receiver_emails = config['email_credentials']['receiver_emails']
        self.smtp_credential = config['email_credentials']['smtp_credential']
        self.server_address = config['email_credentials']['server_address']
        self.port_no = config['email_credentials']['port_no']
        self.receiver_emails_for_less_hits = config['email_credentials']['receiver_emails_for_less_hits']
        self.single_date_fn = config['snp_creds']['single_date_fn']
        self.daterange_fn = config['snp_creds']['daterange_fn']  
        self.resp_key = config['snp_creds']['resp_key']
        self.schedular_m = config['schedulars']['schedular_m']
        self.schedular_d = config['schedulars']['schedular_d']
        self.aieq_country_code = config['aieq_country_code']
        self.s3_versioning_bucket = config['s3_paths']['s3_versioning_bucket']
        self.s3_versioning_pred_filename_daily = config['s3_paths']['s3_versioning_pred_filename_daily']
        self.s3_versioning_pred_filename_daily_temp = config['s3_paths']['s3_versioning_pred_filename_daily_temp']       
    
    def get_master_df(self,tag):
        master_json = requests.get(f'{self.masters_url}{tag.lower()}').json()
        master_df = pd.DataFrame(master_json["data"][f"masteractivefirms_{tag.lower()}"])
        return master_df
    
    def get_capiq_daterange_phase_data(self, id_list, build_date_minmax, country):
        try:
            url = self.spglobal_url
            headers = {'Authorization': self.head_auth,'Content-type': self.content_type}
            input_list = []
            for identifier in list(eval(id_list).keys()):
                start_date = config['start_date']
                end_date = build_date_minmax[1].strftime("%m/%d/%Y")
                body_req = {
                    "function": config['snp_creds']['daterange_fn'],
                    "identifier": identifier,
                    "mnemonic": self.mnem,
                    "properties": {
                        "startDate": start_date,  # startdate
                        "endDate": end_date  # enddate
                    }
                }
                input_list.append(body_req)
            data = {"inputRequests": input_list}
            data_json = json.dumps(data)
            response = requests.post(url, data=data_json, headers=headers)
            try:
                resp_json = json.loads(response.text)
            except Exception as e:
                logger.error(f'Error in getting response from S&P for {country}, {e}') 
            values = list(eval(id_list).values())
            for i in range(len(resp_json['GDSSDKResponse'])):
                phase = []
                for j in range(len(resp_json['GDSSDKResponse'][i]['Headers'])):
                    try:
                        phase.append([resp_json['GDSSDKResponse'][i]['Headers'][j], resp_json['GDSSDKResponse'][i]['Rows'][0]['Row'][j]])
                        value = values[i]
                    except Exception as e:
                        logger.error(f'Error in processing the response from S&P for {country}, {e}') 
                phase_df = pd.DataFrame(columns=[self.date_col, value], data=phase)
                phase_df = phase_df[phase_df[self.date_col] != '']
                if i == 0:
                    phase_data = phase_df.copy()
                else:
                    phase_data = pd.merge(phase_data, phase_df[[self.date_col, value]], how='outer', on=self.date_col)
            phase_data[self.date_col] = pd.to_datetime(phase_data[self.date_col])
            return phase_data
        except Exception as e:
            logger.error(f'Error in getting phase data from s&p for {country}, {e}')
            
    def process_equity_data(self, phase_data, equity_name, benchmark_column, build_date, schedular):
        schedular_dict = config['schedular_dict']
        period = schedular_dict[schedular.capitalize()]
        equity_data1 = phase_data[[self.date_col, f'{equity_name}_close_price', benchmark_column]]
        equity_data1[f'{schedular.lower()}_close_pct_change'] = equity_data1[f'{equity_name}_close_price'].astype(
            float).pct_change(periods=period) * 100
        equity_data1 = equity_data1[equity_data1[self.date_col] <= pd.to_datetime(build_date)]
        equity_data = equity_data1.iloc[[-1]][['date', f'{equity_name}_close_price', f'{schedular.lower()}_close_pct_change']]
        equity_data[self.date_col] = pd.to_datetime(build_date)
        equity_corr_data = equity_data1.iloc[[-1]][
            [self.date_col, f'{equity_name}_close_price', f'{schedular.lower()}_close_pct_change', benchmark_column]]
        equity_corr_data[self.date_col] = pd.to_datetime(build_date)
        return equity_data, equity_corr_data  
    
    def ind_benchmark(self, build_dates):
        st_dt = (min(build_dates) - timedelta(days=config['look_back_period'])).strftime("%Y-%m-%d")
        en_dt = (max(build_dates) + timedelta(days=1)).strftime("%Y-%m-%d")
        benchmark_data = yf.download(config['ind_benchmark_id'], interval='1d', start=st_dt, end=en_dt, auto_adjust = False)
        benchmark_data = benchmark_data.reset_index()
        benchmark_data.columns = benchmark_data.columns.get_level_values(0)
        benchmark_data[self.date_col.capitalize()] = pd.to_datetime(benchmark_data['Date'])
        benchmark_data = benchmark_data.rename(columns={'Close': 'benchmark_closeprice', self.date_col.capitalize(): self.date_col})
        benchmark_data = benchmark_data[[self.date_col, 'benchmark_closeprice']]
        return benchmark_data
    
    def prepare_data(self, country_codes, build_dates, schedular, ph):
        try:
            dep_lists = []
            datas = []
            map_bucket = self.bucket_name
            map_file = config['s3_paths']['map_file']
            mapping = self.s3helper.read_as_dataframe(map_bucket, map_file)
            schedular_dict = config['schedular_dict']
            period = schedular_dict[schedular.capitalize()]
            for country in country_codes:
                try:
                    logger.info(f'Starting data preparation for {country}') 
                    id_list = mapping[mapping['country_code'] == country]['identifiers'].iloc[0]
                    temp_dict = eval(id_list).copy()
                    temp_dict.pop('NSEI', None)
                    id_list_for_snp = str(temp_dict)
                    phase_complete = self.get_capiq_daterange_phase_data(id_list_for_snp, [min(build_dates), max(build_dates)], country)
                    phase_complete[self.date_col]=pd.to_datetime(phase_complete[self.date_col])
                    phase_complete.sort_values(by=[self.date_col], inplace=True)
                    if pd.to_datetime(max(build_dates)) not in pd.to_datetime(phase_complete[self.date_col].values): 
                        phase_complete.loc[len(phase_complete), self.date_col] = pd.to_datetime(max(build_dates))   
                    if pd.to_datetime(max(build_dates)) == pd.to_datetime(datetime.today().date() - BDay(1)):
                        row = phase_complete[phase_complete[self.date_col] == pd.to_datetime(max(build_dates))]
                        nan_columns = [col for col in eval(id_list_for_snp).values() if col in phase_complete.columns and pd.isna(row.iloc[0][col])]
                        filtered_mapping = {k: v for k, v in eval(id_list_for_snp).items() if v in nan_columns}
                        if len(filtered_mapping)>0:
                            logger.info(f'Checking last trading date for {country} for {filtered_mapping}')
                        result = self.get_last_trading_date(max(build_dates), filtered_mapping)
                        for col, should_nan in result.items():
                            if should_nan and col in phase_complete.columns:
                                phase_complete.loc[phase_complete[self.date_col] == pd.to_datetime(max(build_dates)), col] = np.nan
                    else:
                        logger.info(f'Skipping checking last trading date for {country} since running for a historical date')
                    phase_complete[self.date_col]=pd.to_datetime(phase_complete[self.date_col])
                    phase_complete.sort_values(by=[self.date_col], inplace=True)
                    phase_complete = phase_complete.ffill()
                    if country == config['ind_country_code']:
                        bench_data_complete = self.ind_benchmark(build_dates)
                except Exception as e:
                    logger.error(f'Error in fetching phase data from capiq {country}, {e}')       
                for build_date in build_dates:    
                    try:
                        phase_data = phase_complete[phase_complete[self.date_col] <= pd.to_datetime(build_date)]
                        if country == config['aieq_country_code']:
                            benchmark_column = 'spy_close_price'
                        elif country == config['ind_country_code']:
                            benchmark_column = 'benchmark_closeprice'
                            bench_data = bench_data_complete[bench_data_complete[self.date_col] <= pd.to_datetime(build_date)]
                            phase_data = phase_data.merge(bench_data, how='left', on=self.date_col)
                        else:
                            benchmark_column = 'benchmark_closeprice'
                        if f'sphq_close_price' in phase_data.columns:
                            sphq_data, sphq_corr_data = self.process_equity_data(phase_data, 'sphq', benchmark_column, build_date, schedular)
                        if f'vlue_close_price' in phase_data.columns:
                            vlue_data, vlue_corr_data = self.process_equity_data(phase_data, 'vlue', benchmark_column, build_date, schedular)
                        if f'mtum_close_price' in phase_data.columns:
                            mtum_data, mtum_corr_data = self.process_equity_data(phase_data, 'mtum', benchmark_column, build_date, schedular)
                        if f'iwf_close_price' in phase_data.columns:
                            iwf_data, iwf_corr_data = self.process_equity_data(phase_data, 'iwf', benchmark_column, build_date, schedular)

                        equity_dict = {}

                        if f'vlue_close_price' in phase_data.columns:
                            equity_dict['vlue'] = vlue_data
                            equity_dict['vlue_corr'] = vlue_corr_data
                            vlue_data2 = vlue_data.copy()
                            vlue_corr_data2 = vlue_corr_data.copy()
                            vlue_data2['id'] = f'{country}_V'
                            vlue_corr_data2['id'] = f'{country}_V_B'
                            vlue_corr_data2['benchmark_used'] = list(eval(mapping[mapping['country_code'] == country]['identifiers'].iloc[0]).keys())[-1]
                            vlue_corr_data2.rename(columns=config['columns']['benchmark_renaming_dict'], inplace=True)
                            datas.append(vlue_data2)
                            datas.append(vlue_corr_data2)
                        if f'mtum_close_price' in phase_data.columns:
                            equity_dict['mtum'] = mtum_data
                            equity_dict['mtum_corr'] = mtum_corr_data
                            mtum_data2 = mtum_data.copy()
                            mtum_corr_data2 = mtum_corr_data.copy()
                            mtum_data2['id'] = f'{country}_M'
                            mtum_corr_data2['id'] = f'{country}_M_B'
                            mtum_corr_data2['benchmark_used'] = list(eval(mapping[mapping['country_code'] == country]['identifiers'].iloc[0]).keys())[-1]
                            mtum_corr_data2.rename(columns=config['columns']['benchmark_renaming_dict'], inplace=True)
                            datas.append(mtum_data2)
                            datas.append(mtum_corr_data2)
                        if f'iwf_close_price' in phase_data.columns:
                            equity_dict[f'iwf'] = iwf_data
                            equity_dict[f'iwf_corr'] = iwf_corr_data
                            iwf_data2 = iwf_data.copy()
                            iwf_corr_data2 = iwf_corr_data.copy()
                            iwf_data2['id'] = f'{country}_G'
                            iwf_corr_data2['id'] = f'{country}_G_B'
                            iwf_corr_data2['benchmark_used'] = list(eval(mapping[mapping['country_code'] == country]['identifiers'].iloc[0]).keys())[-1]
                            iwf_corr_data2.rename(columns=config['columns']['benchmark_renaming_dict'], inplace=True)
                            datas.append(iwf_data2)
                            datas.append(iwf_corr_data2)
                        if f'sphq_close_price' in phase_data.columns:
                            equity_dict[f'sphq'] = sphq_data
                            equity_dict[f'sphq_corr'] = sphq_corr_data
                            sphq_data2 = sphq_data.copy()
                            sphq_corr_data2 = sphq_corr_data.copy()
                            sphq_data2['id'] = f'{country}_Q'
                            sphq_corr_data2['id'] = f'{country}_Q_B'
                            sphq_corr_data2['benchmark_used'] = list(eval(mapping[mapping['country_code'] == country]['identifiers'].iloc[0]).keys())[-1]
                            sphq_corr_data2.rename(columns=config['columns']['benchmark_renaming_dict'], inplace=True)
                            datas.append(sphq_data2)
                            datas.append(sphq_corr_data2)
                        country_name = mapping[mapping['country_code'] == country]['country_name'].iloc[0]
                        dep_list = ph.create_dep_to_submit(equity_dict, build_date, country_name, country, schedular)
                        if dep_list is not None:
                            dep_lists.append(dep_list)
                            logger.info(f'Finished data preparation for {country}')
                    except Exception as e:
                        logger.error(f'Error in preparing data for {country}, {e}')
            return dep_lists, datas
        except Exception as e:
            return [], []
            logger.error(f'Error in preparing data, {e}')
    
    def convert(self, row):
        try:
            row = row[0]['Row'][0]
            if row == 'Data Unavailable' or row == 'NaN':
                row = np.nan
            row = float(row)
            return row
        except Exception as e:
            row = np.nan
            return row
                              
    def convert2float(self, value):
        try:
            return float(value)
        except:
            return np.nan 
                                                                                                  
    def get_es_data(self, es, start_date, end_date, isin_list, q_total, index_prefix):
        start_year = (pd.to_datetime(start_date)).year
        end_year = (pd.to_datetime(end_date)).year
        try:
            data = []
            hits = []
            for year in range(start_year, end_year + 1):
                try:
                    response,total_docs = es.search_with_pagination(index=f"{index_prefix}_{year}",query=q_total,paginate=False,strict=False)
                except Exception as e:
                    logger.error(f"Error in response from es, {e}")
                    pass
                for hit in response:
                    es_data=hit['_source']
                    data.append(es_data)
            df=pd.DataFrame(data)
            df[self.date_col]=pd.to_datetime(df[self.date_col])
            df.sort_values(self.date_col, ascending=True, inplace=True)
            df.reset_index(inplace=True, drop=True)
            return df
        except Exception as e:
            logger.error(f"Error in get_es_data, {e}")                         
                                     
    def revert_nan(self, row, nan_dict):
        if row['isin'] in nan_dict:
            columns = nan_dict[row['isin']]
            if isinstance(columns, list): 
                for col in columns:
                    row[col] = np.nan  
            else:  
                row[columns] = np.nan
        return row 
                                     
    def save_row(self, idx, row, bucket_name, filename):
        row_df = row.to_frame().T
        isin  = row_df['isin'].iloc[0]
        filename = filename.replace('isin',isin)                             
        self.s3helper.write_advanced_as_df(row_df, bucket_name, filename)    
        
    def get_last_trading_date(self, date, id_dict): 
        url = self.spglobal_url  
        headers = {
            'Authorization': self.head_auth,
            'Content-type': self.content_type
        }

        result = {}

        for isin, col_name in id_dict.items():
            data = {
                "inputRequests": [
                    {
                        "function": config['snp_creds']['single_date_fn'],
                        "identifier": isin.strip(),  # remove leading/trailing spaces
                        "mnemonic": config['snp_creds']['trade_check_mnem']
                    }
                ]
            }

            data_json = json.dumps(data)

            try:
                response = requests.post(url, data=data_json, headers=headers)
                resp_json = json.loads(response.text)

                if resp_json is None:
                    result[col_name] = True
                    continue

                try:
                    tmp = pd.json_normalize(resp_json['GDSSDKResponse'])
                    data_rows = tmp['Rows'].iloc[0]
                    date_traded = data_rows[0]['Row'][0]

                    if date_traded == 'Data Unavailable':
                        result[col_name] = False
                    else:
#                         date_traded = datetime.strptime(date_traded, '%Y-%m-%d')
                        date_traded = pd.to_datetime(date_traded).tz_localize(None)
                        date_obj = pd.to_datetime(date)

                        result[col_name] = date_traded >= date_obj
                except Exception as e_inner:
                    logger.info(f"Parsing error for {isin}: {e_inner}")
                    result[col_name] = True  # Defaulting to True on parsing error

            except Exception as e_outer:
                logger.info(f"Request error for {isin}: {e_outer}")
                result[col_name] = True  # Defaulting to True on request error

        return result    
                                     
    def failure_mail(self, error, schedular, log_file_path, log_file_s3_path, env):
        num = config['n_bday']
        log_path_today = log_file_s3_path.replace('date',str(datetime.now())) 
        current_date = date.today()
        saved_date = (current_date - BDay(num)).date()                             
        sender_email = self.sender_email     
        if env == 'prod':
            receivers_list = config['email_credentials']['receiver_emails_for_failure'] 
        elif env == 'pre-prod':
            receivers_list = self.sender_email
        subject = f'Phase model {schedular.capitalize()} run failed'
        message_text = f"""
<html>
<body>
<p>Phase model <strong>{schedular.capitalize()}</strong> run for <strong>{saved_date}</strong> was interrupted due to the following error:</p>

<p>{error}</p>

<p>For detailed logs, please refer to the log file at:<br>
<strong>Bucket:</strong> {self.bucket_name}<br>
<strong>Path:</strong> {log_path_today}
</p>
</body>
</html>
"""
        self.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)                                    
        self.s3helper.upload_file(log_file_path, self.bucket_name, log_path_today)
                                                                                                            
    def starting_mail(self, schedular, today, receiver_emails, process):
        sender_email = self.sender_email 
        message = MIMEText(f"Phase model {schedular.capitalize()} {process} run for {today} has started")
        password = self.smtp_credential                            
        with smtplib.SMTP(self.server_address, self.port_no) as smtp:
            smtp.starttls()
            smtp.login(sender_email, password)
            message['From'] = sender_email
            message['Subject'] = f'Phase model {schedular.capitalize()} {process} run started'
            smtp.sendmail(sender_email, receiver_emails, message.as_string()) 
            
    def get_credentials(self):
        SCOPES = config['common_gmail']['scope']
        creds = None
 
        response = self.s3helper._s3Client.get_object(Bucket=config['common_gmail']['gmail_cred_bucket'], Key=config['common_gmail']['gmail_cred_file'])
        credentials_data = json.loads(response['Body'].read().decode("utf-8"))
        creds = GoogleCredentials.from_authorized_user_info(info=credentials_data, scopes=SCOPES)
        return creds                                 
                                     
    def SendMessage(self, sender, to, subject, filename, message_text, attachments: list = None):
        credentials = self.get_credentials()
        if credentials == None:
            return print("credentials not found, Generate credentials")
        try:
            service = build('gmail', 'v1', credentials=credentials)
            message = EmailMessage()
            html = message_text
            body = MIMEText(html, 'html')
            message.set_content(body)
            if attachments:
                for i,attachment in enumerate(attachments):
                    with open(attachment, 'rb') as content_file:
                        content = content_file.read()
                        message.add_attachment(content, maintype='application', subtype=(
                            attachment.split('.')[1]), filename=filename[i])
            message['To'] = to
            message['From'] = sender
            message['Subject'] = subject

            encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
                .decode()

            create_message = {
                'raw': encoded_message
            }
            send_message = (service.users().messages().send(
                userId="me", body=create_message).execute())
            print(F'Message Id: {send_message["id"]}')
        except HttpError as error:
            logger.error(F'An error occurred: {error}')
            send_message = None              
                                     
    def patch_https_connection_pool(self, **constructor_kwargs):
        """
        This allows to override the default parameters of the
        HTTPConnectionPool constructor.
        For example, to increase the poolsize to fix problems
        with "HttpSConnectionPool is full, discarding connection"
        call this function with maxsize=16 (or whatever size
        you want to give to the connection pool)
        """
        from urllib3 import connectionpool, poolmanager
        class MyHTTPSConnectionPool(connectionpool.HTTPSConnectionPool):
            def __init__(self, *args,**kwargs):
                kwargs.update(constructor_kwargs)
                super(MyHTTPSConnectionPool, self).__init__(*args,**kwargs)
        poolmanager.pool_classes_by_scheme['https'] = MyHTTPSConnectionPool
          
                                                                  
class predictionHelper():
    def __init__(self, prediction_batch_size = 300):  # keep less for historical predictions
        self.batch_run_size = prediction_batch_size
        self.job_count = 0
        self.job_failed = 0
        self.job_success = 0
        self.failed_df = pd.DataFrame()                          
        self.ibm_api_key = config['prediction_credentials']['api_key']
        self.ibm_url = config['prediction_credentials']['dallas_url']
        self.wml_credentials = {"apikey": self.ibm_api_key, "url": self.ibm_url}
        self.wmlclient = APIClient(self.wml_credentials)
        self.auto_ai_jobs_cols = config['columns']['auto_ai_jobs_cols']
        self.autoai_job_bucket = config['s3_paths']['autoai_job_bucket']
        self.deployment_space_col = config['columns']['deployment_space_col']
        self.deployment_id_col = config['columns']['deployment_id_col']
        self.pred_col = config['columns']['pred_col']
        self.status_col = config['columns']['status_col']
        self.name_col = config['columns']['name_col']
        self.year_col = config['columns']['year_col']
        self.training_date_col = config['columns']['training_date_col']
        self.test_data_col = config['columns']['test_data_col']
        self.id_col = config['columns']['id_col']
        self.date_col = config['columns']['date_col']
        self.deployment_bucket = config['s3_paths']['bucket_name']
        self.deployment_file = config['s3_paths']['deployment_filename']
        self.aieq_deployment_file_monthly = config['s3_paths']['aieq_deployment_filename_monthly']
        self.aieq_deployment_file_daily = config['s3_paths']['aieq_deployment_filename_daily']
        self.aigo_deployment_file_monthly = config['s3_paths']['aigo_deployment_filename_monthly']
        self.s3helper = s3_config()
        self.schedular_m = config['schedulars']['schedular_m']
        self.schedular_d = config['schedulars']['schedular_d']
                                              
        
    def create_dep_to_submit(self, equity_dict, build_date, country_name, country, schedular):
        try:
            if country_name.lower() == config['aieq_country_code'].lower():
                deployment_file = self.deployment_file.replace('tag', config['tags']['aieq_tag']).replace('country', f'{country_name}').replace('schedular', schedular.lower())
            else:
                deployment_file = self.deployment_file.replace('tag', config['tags']['aigo_tag']).replace('country', f'{country_name}').replace('schedular', schedular.lower())
            dep = self.s3helper.read_as_dataframe(self.deployment_bucket, deployment_file)
            latest_years = dep.groupby(self.name_col)[self.year_col].transform('max')
            dep = dep[dep[self.year_col] == latest_years]
            if config['columns']['expiration_date_col'] in dep.columns.tolist():
                dep = dep.loc[dep[config['columns']['expiration_date_col']].isnull()]
            dep = dep[~dep[self.name_col].str.contains('usmv')]
            for col in config['columns']['cols_to_drop_from_dep']:
                if col in dep.columns:
                    dep.drop(columns=[col], inplace=True)
            id_mapping = config['id_mapping'].copy() 
            for k, v in id_mapping.items():
                id_mapping[k] = v.replace("country", country)
               
            pred_input = []
            for i in range(len(dep)):
                if country_name in dep.iloc[i][self.name_col]:
                    isin = dep.iloc[i][self.name_col].split(f'{country_name}_')[1]
                else:
                    isin = dep.iloc[i][self.name_col]
                etf_data = equity_dict[isin].copy()
                if country == config['aieq_country_code']:
                    if schedular == config['schedulars']['schedular_w']:
                        if 'benchmark_closeprice' in list(etf_data.columns):
                            col = etf_data.pop('benchmark_closeprice')
                            etf_data.insert(2, 'benchmark_closeprice', col)
                    else:
                        if 'spy_close_price' in list(etf_data.columns):
                            col = etf_data.pop('spy_close_price')
                            etf_data.insert(2, 'spy_close_price', col)
                etf_data = etf_data.drop(columns=[self.date_col])
                etf_data.fillna(0, inplace=True)
                single_row = etf_data.copy()
                single_row.reset_index(drop=True, inplace=True)
                pred_input.append(single_row)
            dep[self.test_data_col] = pred_input
            if country_name in dep.iloc[0][self.name_col]:
                dep['id'] = dep[self.name_col].str.split(f'{country_name}_').str[1].map(id_mapping)
            else:
                dep['id'] = dep[self.name_col].str[0].map(id_mapping)
            dep = dep.rename(columns={self.name_col: 'isin'})
            a = dep.pop( self.deployment_id_col)
            dep.insert(1,  self.deployment_id_col, a)
            dep = dep.rename(columns={'space_id': self.deployment_space_col})
            dep[self.test_data_col] = dep[self.test_data_col].apply(lambda x: x.values)
            deployment_toSubmit = dep.copy()
            return deployment_toSubmit
        except Exception as e:
            logger.exception(f'Failure in creating deployment_toSubmit for {country}, {e}')

    def fetch_prediction(self, datas, dep_list, schedular):
        try:
            data_final = pd.concat(datas)

            def aggregate_arrays(arrays):
                concatenated = []
                for array in arrays:
                    concatenated.extend(array)
                return [concatenated]

            combined_dep = pd.concat(dep_list)
            deployment_toSubmit = combined_dep.groupby(self.deployment_id_col).agg({self.test_data_col: aggregate_arrays, self.deployment_space_col: 'first', 'isin': 'first'}).reset_index()
            deployment_toSubmit = deployment_toSubmit[['isin', self.deployment_id_col, self.deployment_space_col, self.test_data_col]]
            deployment_toSubmit[self.test_data_col] = deployment_toSubmit[self.test_data_col].apply(lambda x: np.array([item for sublist in x for item in sublist]))
            start = time.time()
            url = self.wml_credentials['url']
            api_key = self.wml_credentials['apikey']
            ibm_conn = IBMConnection(url, api_key)
            stt = datetime.now()
            job_count = len(deployment_toSubmit)
            pred_df = BulkPrediction(ibm_conn).prediction_run(deployment_toSubmit)
            ett = datetime.now() 
            end = time.time()
            logger.info('Done prediction')
            combined_dep_dropped = combined_dep.drop_duplicates(subset=[self.deployment_id_col]).reset_index(drop=True)
            comb_df = pd.merge(pred_df, combined_dep_dropped[[self.deployment_id_col, self.id_col]], on=self.deployment_id_col, how='left')
            failed = comb_df[comb_df[self.status_col] != 'completed'].reset_index(drop=True)
            if len(failed)>0:
                logger.info("Following models have failed during prediction:")
                for model_id in failed[self.id_col].tolist():  
                    logger.info(f'{model_id}')
            completed_df = comb_df[comb_df[self.status_col] == 'completed'].reset_index(drop=True)
            success_count = len(completed_df)
            failed_count = 0
            for i in data_final[~data_final[self.id_col].isin(list(completed_df[self.id_col]))][self.id_col].tolist():
                if i in failed[self.id_col].tolist():
                    logger.info('Failed in prediction', i)
                    failed_count+=1
                else:
                    logger.info('Failed before prediction', i)
            n_jobs = pd.DataFrame(columns=['date','start_time','end_time','total_job_count', 'failed_job_count', 'success_job_count'], data=[[(datetime.today().date()), stt, ett, job_count, failed_count, success_count]])
            if schedular.lower() == self.schedular_m.lower():
                job_file = config['s3_paths']['jobcount_filename_monthly']
            elif schedular.lower() == self.schedular_d.lower():
                job_file = config['s3_paths']['jobcount_filename_daily']
            try:
                n_job_file = self.s3helper.read_as_dataframe(self.autoai_job_bucket,job_file)
            except:
                n_job_file = pd.DataFrame()
            n_job_file_new = pd.concat([n_job_file, n_jobs])
            self.s3helper.write_advanced_as_df(n_job_file_new, self.autoai_job_bucket,job_file)
            n_job_file_new.tail() 
            comb_df[self.pred_col] = comb_df[self.pred_col].apply(
                lambda x: np.nan if x is None or (isinstance(x, float) and np.isnan(x)) or (
                            not isinstance(x, float) and len(x) == 0) else x)
            grp_dfs = []
            for isn in comb_df['id']:
                grp_df = data_final[data_final['id'] == isn].reset_index(drop=True)
                grp_df[self.pred_col] = ''
                for d in range(len(grp_df)):
                    prediction_value = comb_df[comb_df['id'] == isn][self.pred_col].iloc[0]
                    if prediction_value is None:
                        # Handle None case
                        grp_df.at[d, self.pred_col] = np.nan
                    elif np.isscalar(prediction_value) and np.isnan(prediction_value):
                        # Handle single NaN value case
                        grp_df.at[d, self.pred_col] = np.nan
                    elif isinstance(prediction_value, list) and len(prediction_value) == 0:
                        # Handle empty list case
                        grp_df.at[d, self.pred_col] = np.nan
                    else:
                        # Handle non-empty list case
                        grp_df.at[d, self.pred_col] = prediction_value[d]
                grp_dfs.append(grp_df)
            pred_df_final = pd.concat(grp_dfs)
            return pred_df_final
        except Exception as e:
            logger.error(f'Error in fetching prediction, {e}')
                                                             

class MetricsHelper:
    def __init__(self):
       self.conn =  DataCollectionHelper()          
                  
    def accuracy_function(self, df_series, coff = 500):
        '''
        Accuracy conversion metric to convert daily APE into accuracy for ER.
        '''
        return df_series.apply(lambda x: 97/( 1 + 20 * np.exp((-coff/x))) if x <  100 else max(0, 106.7 - 0.213 * x))

    def calculate_accuracy(self, df_data, prediction_col = 'predictions', target_col = 'close_change_monthly', coff = 500):

        if prediction_col not in df_data.columns:
            raise Exception('Prediction column not in Dataframe')

        if target_col not in df_data.columns:
            raise Exception('Target column not in Dataframe')

        # Remove any nan's in prediction or target cols
        df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

        # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
        df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
        df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

        # Calculate RMS of MAPE over a rolling of 14 days
        df_data['accuracy_1_day'] = self.accuracy_function(df_data['daily_ape'], coff = coff)

        # Calculate RMS of MAPE over a rolling of 22 days
        df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
        df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5

        df_data.drop(columns=['denominator'], inplace=True)

        return df_data
                                    
    def get_start_date(self, ipo, original_st):
        if ipo is None or ipo == np.nan or str(ipo) == 'nan':
            return original_st
        start_date = original_st if pd.to_datetime(ipo) < pd.to_datetime(original_st) else pd.to_datetime(ipo).strftime('%m/%d/%Y')
        return start_date

    def get_end_date(self, original_ed):
        return datetime.today().date().strftime('%m/%d/%Y') if datetime.today().date() < pd.to_datetime(original_ed).date() else original_ed

    def failed_check(self, GDSSDKResponse):
        if GDSSDKResponse[0]['ErrMsg'] == 'InvalidIdentifier' or GDSSDKResponse[0]['ErrMsg'] == 'Input Arguments Missing':
            return True
        if np.sum([y == 'Data Unavailable' for y in [x['Rows'][0]['Row'][0] for x in GDSSDKResponse]]) / len(GDSSDKResponse) >= 0.75: 
            return True
        return False

    def get_capiq_close(self, tic, isin, years):
        url = self.conn.spglobal_url
        headers = {'Authorization': self.conn.head_auth,'Content-type': self.conn.content_type}
        start_date = self.get_start_date('2003-01-01', f'10/01/{years[0] - 1}')
        end_date = self.get_end_date(f'12/31/{years[1]}')
        body = {
              "function": config['snp_creds']['daterange_fn'],
              "identifier": tic, 
              "mnemonic": self.conn.mnem,
              "properties": {
                "startDate": start_date, # startdate
                "endDate" : end_date #enddate
              }
        }
        input_list = [body]
        data = {"inputRequests" : input_list}
        data_json = json.dumps(data)
        response = requests.post(url, data=data_json, headers=headers)
        try:
            resp_json=json.loads(response.text)
            resp_json['isin'] = isin
            return resp_json
        except Exception as e:
            logger.error(f'Error in getting data from capiq, {e}')

    def get_close_change(self, tic, isin, period):
        years = [datetime.today().date().year-10,datetime.today().date().year]          
        single_date_response = self.get_capiq_close(tic, isin, years)
        try:
            snp_closes = pd.DataFrame({self.conn.date_col: pd.to_datetime(single_date_response[config['snp_creds']['resp_key']][0]['Headers']), 'closeprice_adj': single_date_response[config['snp_creds']['resp_key']][0]['Rows'][0]['Row']})
            snp_closes = snp_closes[snp_closes['closeprice_adj'].notna()].reset_index(drop = True)
            snp_closes["closeprice_adj"] = snp_closes["closeprice_adj"].astype(float)
            snp_closes['close_change'] = snp_closes["closeprice_adj"].pct_change(periods=period) * 100
            snp_closes.drop(columns = 'closeprice_adj', inplace = True)
            return snp_closes
        except Exception as e:
            logger.error(f"Couldn't fetch prices from S&P API., {e}")
            return pd.DataFrame()

    def read_data(self, isin, country_code, years, read_from, pred_col, schedular, es_index, bucket_name, path_loc, local_folder):
        default_schedular = self.conn.schedular_m.lower()
        schedular_dict = config['schedular_dict']  
        mapping_file=self.conn.s3helper.read_as_dataframe(self.conn.bucket_name,config['s3_paths']['map_file'])  
        es = es_config(env='prod')          
        try:
            if read_from == 'es': # fetching data from es
                id_name = f'{country_code}_{config["es_id_mapping"][isin]}'
                start_date = date(years[0], 1, 1).isoformat()
                end_date = datetime.today().date().isoformat()
                query = {"query":{"bool": {"must":[{"bool":{"should":[{"match":{"id":id_name}}]}}]}}}  
                temp = self.conn.get_es_data(es, start_date, end_date, None, query, es_index)[[self.conn.date_col, pred_col]] # fetching data from es         
            elif read_from == 's3':
                temp = self.conn.s3helper.read_as_dataframe(bucket_name, f'{path_loc}/{isin}.csv')[[self.conn.date_col, pred_col]] # fetching data from s3
            else:
                temp = pd.read_csv(os.path.join(local_folder, isin))[[self.conn.date_col, pred_col]] # fetching data from local
            if schedular == None or schedular == '':
                schedular = default_schedular
            if temp.empty:
                raise Exception(f"Data Empty. ISIN: {isin}, Country: {country_code}")
        except Exception as e:
            logger.error(f"Issue with {isin} {country_code}: {e}")       
            return isin, pd.DataFrame()

        try:
            substring = isin.split('_')[0]
            original_dict = eval(mapping_file[mapping_file['country_code']==country_code]['identifiers'].iloc[0])
            reverse_mapping = {v.split('_')[0]: k for k, v in original_dict.items()}
            identifier = reverse_mapping.get(substring)
            close_change = self.get_close_change(identifier,isin,schedular_dict[schedular.capitalize()], )
            temp = pd.merge(temp, close_change, on = self.conn.date_col, how = 'inner').rename(columns = {'percent_pct': 'close_change'})
            temp.rename(columns = {'predictions':f'{schedular}_predictions', 
                                     'close_change': f'actual_{schedular}_returns'}, 
                          inplace = True)
            temp = temp.sort_values(by = self.conn.date_col).reset_index(drop = True)
            temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].shift(schedular_dict[schedular.capitalize()])
            temp[self.conn.date_col] = pd.to_datetime(temp[self.conn.date_col])
            temp = temp[(temp[self.conn.date_col] >= pd.to_datetime(f'{years[0]}-01-01')) & (temp[self.conn.date_col] <= pd.to_datetime(f'{years[1]}-12-31'))]    
            temp = temp.drop_duplicates(subset = [self.conn.date_col]).sort_values(by = self.conn.date_col).reset_index(drop = True)
            temp.reset_index(drop = True, inplace = True)
            temp = temp.ffill().fillna(0)
            temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].astype(float, errors="ignore")
            temp[f'actual_{schedular}_returns'] = temp[f'actual_{schedular}_returns'].astype(float, errors="ignore")
            logger.info(f"Data collected for isin {isin} {country_code}.") 
        except Exception as e:
            logger.error(f"Cannot fetch data from S&P. ISIN:, {isin} {country_code}, {e}") 
            temp = pd.DataFrame()
        return isin, temp
                  
    def directionality_score_calc(self, prediction_direction, close_direction):
            directionality_df = pd.DataFrame()
            directionality_df['prediction_direction'] = prediction_direction
            directionality_df['close_direction'] = close_direction
            correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)] 
            incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)] 
            relaxation_count = len(incorrect_direction_df[(abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)]) 
            directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
            return directionality_score

    def calculate_metrics(self, df, isin, schedular, prediction_column = 'monthly_predictions', actual_column = 'actual_monthly_returns', metrics_to_calculate = config['all_metrics'], n_features = 0):
        req_columns = ["date", "isin", f"actual_{schedular.lower()}_returns", f"{schedular.lower()}_predictions"]  
        schedular_dict = config['schedular_dict']          
        period = schedular_dict[self.conn.schedular_m.capitalize()]          
        if len(df) < period:
            return f'Dataframe size for {isin} too small to calculate metrics.'

        df = df.rename(columns = {prediction_column: 'predictions', actual_column: 'actual_returns'})
        metrics_columns = req_columns + metrics_to_calculate


        # Total perc diff
        if 'total_perc_diff' in metrics_columns or 'abs_total_diff' in metrics_columns:
            df['total_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.mean() - prediction.mean()) / prediction.mean(), period, df['actual_returns'].values, df['predictions'].values)

        # Abs total diff
        if 'abs_total_diff' in metrics_columns:
            df['abs_total_diff'] = abs(df['total_perc_diff'])

        # Total variance perc diff
        if 'total_variance_perc_diff' in metrics_columns or 'abs_total_variance_perc_diff' in metrics_columns:
            df['total_variance_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.var() - prediction.var()) / prediction.var(), period, df['actual_returns'].values, df['predictions'].values)

        # Abs total variance perc diff
        if 'abs_total_variance_perc_diff' in metrics_columns:
            df['abs_total_variance_perc_diff'] = abs(df['total_variance_perc_diff'])

        # MAE
        if 'mean_absolute_error' in metrics_columns:
            df['mean_absolute_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: abs(df_series).mean())

        # MSE
        if 'mean_squared_error' in metrics_columns or 'root_mean_squared_error' in metrics_columns:
            df['mean_squared_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: ((df_series ** 2).sum() / period))

        # RMSE
        if 'root_mean_squared_error' in metrics_columns:
            df['root_mean_squared_error'] = df['mean_squared_error'] ** 0.5

        # R2 score
        if 'r2_score' in metrics_columns or 'adjusted_r2_score' in metrics_columns:
            df['r2_score'] = rolling_apply_ext(r2_score, period, df['actual_returns'].values, df['predictions'].values)

        # Adjusted R2 score
        if 'adjusted_r2_score' in metrics_columns:
            df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features - 1))

        # Mean directionality
        if 'mean_directionality' in metrics_columns:
            df['mean_directionality'] = (df['actual_returns'] * df['predictions']).rolling(period).apply(lambda df_series: 100 * ((df_series) > 0).sum() / period)

        # Correlation score
        if 'correlation_score' in metrics_columns:
            df['correlation_score'] = rolling_apply_ext(lambda target, prediction: np.corrcoef(target, prediction)[0][1], period, df['actual_returns'].values, df['predictions'].values) 

        # Accuracy
        if "accuracy_14_day" in metrics_columns or "accuracy_22_day" in metrics_columns or "accuracy_1_day"  in metrics_columns:
            df = self.calculate_accuracy(df, 'predictions', 'actual_returns')

        # Confidence score
        if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
            min_confidence = 0.01

            max_values =  df['actual_returns'].rolling(period * 24, min_periods = period).max()
            min_values =  df['actual_returns'].rolling(period * 24, min_periods = period).min()
            filt1 = [df.loc[i, 'predictions'] >= max_values.loc[i] for i in range(len(df))]
            filt2 = [df.loc[i, 'predictions'] <= min_values.loc[i] for i in range(len(df))]
            filt3 = [df.loc[i, 'actual_returns'] >= max_values.loc[i] for i in range(len(df))]
            filt4 = [df.loc[i, 'actual_returns'] <= min_values.loc[i] for i in range(len(df))]

            df['confidence_score'] = [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
            max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_returns"])/(max_values.loc[i] - df.loc[i, "predictions"]) if df.loc[i, "actual_returns"] > df.loc[i, "predictions"] else (df.loc[i, "actual_returns"] - min_values.loc[i]) / (df.loc[i, "predictions"] - min_values.loc[i])) for i in range(len(df))]

        # Average confidence score
        if 'avg_confidence_score' in metrics_columns:
            df['avg_confidence_score'] = df['confidence_score'].rolling(period // 2).mean()

        if 'directionality_score' in metrics_columns:
            directionality_df = pd.DataFrame()
            directionality_df["prediction_direction"] = (df["predictions"] - df['predictions'].shift(1)) / df['predictions'].shift(1)
            directionality_df["close_direction"] = (df["actual_returns"] - df['actual_returns'].shift(1)) / df['actual_returns'].shift(1)
            df['directionality_score'] = rolling_apply_ext(self.directionality_score_calc, period, directionality_df['prediction_direction'], directionality_df['close_direction']) 

        df['isin'] = isin
        df.rename(columns={'actual_returns':actual_column, 'predictions':prediction_column},inplace=True)
        return df[metrics_columns][period:].reset_index(drop = True)  
                  
    def metrics_process_save(self, schedular, tag, metrics_folder, s3_version_filename, env):
        ids_list = []          
        mapping_file=self.conn.s3helper.read_as_dataframe(self.conn.bucket_name,config['s3_paths']['map_file'])          
        es_index = config['index']['phase_model_index'] 
        is_prod = env == 'prod'          
        s3_version_bucket_name = config['s3_paths']['s3_versioning_bucket'] if is_prod \
        else config['s3_paths']['test_s3_versioning_bucket']          
        if env == 'prod':         
            metrics_index = config['index']['phase_metrics_index']
            es = es_config(env='pre')      
        else:
            metrics_index = config['index']['preprod_phase_metrics_index'] 
            es = es_config(env='prod')       
        date_ref = self.read_data('vlue', config['aieq_country_code'], [datetime.today().date().year-10,datetime.today().date().year], read_from='es', pred_col='predictions', schedular='daily'.lower(), es_index=es_index, bucket_name=None, path_loc=None, local_folder=None)[1][['date']]
        date_ref['date']=pd.to_datetime(date_ref['date']).dt.date          
        if (schedular.lower() == self.conn.schedular_m.lower()) or (tag == config['tags']['aigo_tag']):
            country_list = mapping_file['country_code'].values
        if  (schedular.lower() == self.conn.schedular_d.lower()) or (tag == config['tags']['aieq_tag']):
            country_list = [config['aieq_country_code']]
        for code in country_list:
            print(code)
            for isin in list(config['es_id_mapping'].keys()):
                try:
                    try:
                        df = self.read_data(isin, code, [datetime.today().date().year-10,datetime.today().date().year], read_from='es', pred_col='predictions', schedular=schedular.lower(), es_index=es_index, bucket_name=None, path_loc=None, local_folder=None)
                    except Exception as e:
                        logger.error(f'Issue, {e}, {code}, {isin}')
                        continue
                    if 'corr' in isin:    
                        n_fin_features = 3 #change accordingly 
                    else:
                        n_fin_features = 2
                    
                    metrics_df = self.calculate_metrics(df[1], isin, schedular, prediction_column=f'{schedular.lower()}_predictions', actual_column=f'actual_{schedular.lower()}_returns', n_features=n_fin_features)
                    metrics_df[self.conn.date_col]=pd.to_datetime(metrics_df[self.conn.date_col]).dt.date
                    metrics_df = pd.merge(metrics_df, date_ref, on=self.conn.date_col, how='outer')
                    metrics_df = metrics_df.ffill().fillna(0)
                    metrics_df = metrics_df[metrics_df[self.conn.date_col]==pd.to_datetime(datetime.today().date()-BDay(1)).date()]
                    id_name = f'{code}_{config["es_id_mapping"][isin]}'
                    metrics_df['id']=id_name
                    country_name = mapping_file[mapping_file['country_code']==code]['country_name'].iloc[0]
                    metrics_df[self.conn.date_col]=pd.to_datetime(metrics_df[self.conn.date_col]).dt.date
                    metrics_df['schedular'] = schedular.capitalize()
                    metrics_df['updated_at'] = datetime.now()
                    try:
                        old_metric_df = self.conn.s3helper.read_as_dataframe(config['s3_paths']['old_bucket'], os.path.join(f'{metrics_folder}/{country_name}', f'{isin}.csv'))
                    except:
                        old_metric_df = pd.DataFrame()
                    new_metric_df = pd.concat([old_metric_df, metrics_df]).reset_index(drop=True)
                    new_metric_df[self.conn.date_col]=pd.to_datetime(new_metric_df[self.conn.date_col]).dt.date
                    new_metric_df.drop_duplicates([self.conn.date_col], inplace=True, keep='last')
                    new_metric_df.sort_values(self.conn.date_col, ascending=True, inplace=True)
                    self.conn.s3helper.write_advanced_as_df(new_metric_df, config['s3_paths']['old_bucket'], os.path.join(f'{metrics_folder}/{country_name}', f'{isin}.csv'))
                    if code == config['aieq_country_code']:
                        date_ref = new_metric_df[[self.conn.date_col]]
                        metrics_folder_new = metrics_folder.replace(config['tags']['aigo_tag'],config['tags']['aieq_tag'])
                        self.conn.s3helper.write_advanced_as_df(new_metric_df, config['s3_paths']['old_bucket'], os.path.join(f'{metrics_folder_new}/{country_name}', f'{isin}.csv'))
                    documents = []
                    for index, row in metrics_df.iterrows():
                        row = row.dropna()
                        filename = s3_version_filename.replace('isin',row['id'])
                        self.conn.s3helper.write_advanced_as_df(pd.DataFrame([row]), s3_version_bucket_name, filename)
                        doc_id = f"{row['id']}_{row[self.conn.date_col]}_{schedular.lower()[0]}"
                        year = pd.to_datetime(row[self.conn.date_col]).year
                        i_name = f'{metrics_index}_{year}'
                        document = {"index": {"_index": i_name, "_id": doc_id}}
                        data = row.to_dict()
                        documents.append(document)
                        documents.append(data)   
                    response = es.client.bulk(documents)
                    ids_list.append(row['id'])
                except Exception as e:
                    logger.error(f"Error in processing and saving metrics: {e}, {code}, {isin}", exc_info=True)                
        return ids_list          



