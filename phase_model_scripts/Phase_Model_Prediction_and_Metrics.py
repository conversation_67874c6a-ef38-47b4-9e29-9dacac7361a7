#!/usr/bin/env python
# coding: utf-8



import argparse
import subprocess
import os
import sys
from Imports import *




if '__file__' in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()
sys.path.insert(0, os.path.abspath(script_dir))
config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)




if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='Phase Metrics Prod Script', description="Script to trigger daily runs with one param.")
    parser.add_argument('-s', '--schedular')
    
    args = parser.parse_args()
    
    schedular = args.schedular

    venv_python = config['script_trigger_paths']['venv_python']
    prediction_script_path = config['script_trigger_paths']['prediction_script_path']
    metrics_script_path = config['script_trigger_paths']['metrics_script_path']
    print(f'Entering subprocess using schedular {schedular}')
    subprocess.run([venv_python, prediction_script_path, "--schedular", schedular])
    subprocess.run([venv_python, metrics_script_path, "--schedular", schedular])








