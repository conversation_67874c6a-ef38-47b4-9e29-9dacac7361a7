#!/usr/bin/env python
# coding: utf-8

import argparse
import os
import sys
from Imports import *




if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='Phase Prod Script', description="Script to run data_collection with one parameter.")
    parser.add_argument('-s', '--schedular')
    
    args = parser.parse_args()
    
    schedular = args.schedular



try:
    if '__file__' in globals():
        script_dir = os.path.dirname(os.path.abspath(__file__))
    else:
        # Default to current working directory
        script_dir = os.getcwd()
    sys.path.insert(0, os.path.abspath(script_dir))
    config_path = os.path.join(script_dir, 'config.yaml')  # Path to config.yaml
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)


    conn = DataCollectionHelper()
    conn.schedular = schedular
    ph = predictionHelper()

    def create_logger(log_file_path):
        log_dir = os.path.dirname(log_file_path)
        print(log_dir)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)  # Create the directory if it doesn't exist
        logger = logging.getLogger('my_logger')
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            logger.removeHandler(handler)
        file_handler = logging.FileHandler(log_file_path, mode='w')  # Use mode='w' for write mode
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger


    env = config['environments']['run_env']
    print(env)
    num = config['n_bday']
    current_date = date.today()
    saved_date = (current_date - BDay(num)).date()
    log_file_path = config['logger_path'].replace('folder',script_dir).replace('date',str(saved_date)).replace('schedular',conn.schedular.capitalize())
    global logger
    logger = create_logger(log_file_path)

    today_date = datetime.today().date() - BDay(num)
    today = today_date.strftime("%m/%d/%Y")
    logger.info(f"Metrics run for {today}")

    es_prod = es_config(env='prod')
    es_pre_prod = es_config(env='pre')

    if env == 'prod':
        receivers_list = config['email_credentials']['receiver_emails_for_trigger']
    else:
        #for testing purpose
        receivers_list = conn.sender_email
    try:  
        subject = f'Phase model {schedular.capitalize()} Metrics run started'
        message_text = f"Phase model {schedular.capitalize()} Metrics run for {str(pd.to_datetime(today).date())} has started"
        conn.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)
    except:
         logger.error("Error in sending starting mail")

    conn.log_file_path = log_file_path
    if schedular.lower() == conn.schedular_m.lower():
        conn.logs_filename = config['s3_paths']['daily_run_logs_filename_monthly']
    elif schedular.lower() == conn.schedular_d.lower():   
        conn.logs_filename = config['s3_paths']['daily_run_logs_filename_daily']


    mh = MetricsHelper()


    file_date = pd.to_datetime(datetime.today().date()-BDay(1)).strftime("%Y-%m-%d")


    if schedular.lower() == conn.schedular_m.lower():
        tags = [config['tags']['aieq_tag'], config['tags']['aigo_tag']]
    elif schedular.lower() == conn.schedular_d.lower():    
        tags = [config['tags']['aieq_tag']]

    is_prod = env == 'prod'
    is_monthly = schedular.lower() == conn.schedular_m.lower()
    is_daily = schedular.lower() == conn.schedular_d.lower()
    
    for tag in tags:
        
        # Set version filename and bucket
        s3_version_filename = config['s3_paths']['s3_versioning_metrics_filename_monthly'] if is_monthly         else config['s3_paths']['s3_versioning_metrics_filename_daily']
        s3_version_filename = s3_version_filename.replace('date', file_date)


        # Set tag-based path mappings
        if is_monthly:
            metrics_map = {
                config['tags']['aieq_tag']: config['s3_paths']['aieq_metrics_s3_path_monthly'] if is_prod else config['s3_paths']['aieq_metrics_s3_path_monthly_temp'],
                config['tags']['aigo_tag']: config['s3_paths']['aigo_metrics_s3_path_monthly'] if is_prod else config['s3_paths']['aigo_metrics_s3_path_monthly_temp']
            }
        elif is_daily:
            metrics_map = {
                config['tags']['aieq_tag']: config['s3_paths']['aieq_metrics_s3_path_daily'] if is_prod else config['s3_paths']['aieq_metrics_s3_path_daily_temp']
            }

        metrics_folder = metrics_map.get(tag)
        id_list = mh.metrics_process_save(schedular, tag, metrics_folder, s3_version_filename, env)


        mapping_file=conn.s3helper.read_as_dataframe(conn.bucket_name,config['s3_paths']['map_file'])
        if is_monthly:
            total = mapping_file['identifiers'] \
            .apply(lambda x: [v for v in eval(x).values() if v not in ['benchmark_closeprice', 'spy_close_price']]) \
            .apply(lambda values: len(values) * 2) \
            .sum()
        if is_daily:
            identifiers_dict = eval(mapping_file['identifiers'].iloc[0])
            filtered = [v for v in identifiers_dict.values() if v not in ['benchmark_closeprice', 'spy_close_price']]
            total = len(filtered) * 2


    try:
        if env == 'prod':
            receivers_list = conn.receiver_emails
        else:    
            #for testing purpose           
            receivers_list = conn.sender_email  
        subject = f'Phase {schedular.capitalize()} model metrics run has been completed'
        message_text = f"""
    <html>
    <body>
    <p>Phase {schedular.capitalize()} model metrics run for {str(pd.to_datetime(today).date())} has been successfully completed.</p>

    <p><strong>Summary:</strong><br>
      &nbsp;&nbsp;&nbsp;- Total Number of Models: {total}<br>
      &nbsp;&nbsp;&nbsp;- Number of Successful Runs: {len(list(set(id_list)))}<br>
      &nbsp;&nbsp;&nbsp;- Number of Failed Runs: {int(total-len(list(set(id_list))))}<br>    

    <p>Records for {len(list(set(id_list)))} ISINs have been added to Elasticsearch.</p>
    </body>
    </html>
    """
        conn.SendMessage(config['sender_name'], receivers_list, subject, None, message_text, None)
    except Exception as e:
        print(e)
        logger.error(f"Error in sending mail regarding run completion: {e}")


    logger.info(f"Completed the run for {schedular}")
    conn.s3helper.upload_file(log_file_path, conn.bucket_name, conn.logs_filename.replace('date',str(datetime.now())))

except Exception as e:
    logger.error(f"Error in the overall script, {traceback.format_exc()}")
    try:                       
        conn.failure_mail(f"Error in the overall script, {e}", schedular, conn.log_file_path, conn.logs_filename.replace('date',str(datetime.now())), env)
    except Exception as e:
        logger.error(f"Error in the overall script for data collection and error in sending failure mail {e}")                       
    raise SystemExit('Stop right there')




