es_index:
        lstm_index: eq_lstm_model
        lstm_metrics_index: eq_lstm_model_metrics
        preprod_lstm_index: pre_lstm_model
        preprod_lstm_metrics_index: pre_lstm_model_metrics

ibm:
        url: https://us-south.ml.cloud.ibm.com
        api_key: fMr33QZg4migeleRziRbqEmrCGTbqTGffjudhbfHrSxk

s3_buckets:
        lstm_bucket_old: eapp-design
        lstm_bucket_new: historical-prediction-data-15yrs
        job_tracking_bucket: autoai-jobs

s3_paths:
        deployment_path_template: lstm/{tag}/deployment/{year}/onnx_deployment_mapping_{tag}_{days}day.csv
        upload_path_template: lstm/{tag}/predictions/{days}_Day/20{year}
        testing_upload_path_template: lstm/prod_script_testing/{tag}/{days}_Day/20{year}
        metrics_upload_path_template: lstm/{tag}/metrics/{days}_Day/20{year}
        testing_metrics_upload_path_template: lstm/prod_script_testing/metrics/{tag}/{days}_Day/20{year}

masteractive:
        masters_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=

log_files:
        aieq_folder: Log_Files_LSTM_AIEQ
        aigo_folder: Log_Files_LSTM_AIGO
        niftyind_folder: Log_Files_LSTM_NIFTYIND
        goetf_folder: Log_Files_LSTM_GOETF
        dbetf_folder: Log_Files_LSTM_DBETF
        bnpetf_folder: Log_Files_LSTM_BNPETF
        ben_etf_folder: Log_Files_LSTM_BEN_ETF
        aieq_metrics_folder: Metrics_Logs_LSTM_AIEQ
        aigo_metrics_folder: Metrics_Logs_LSTM_AIGO
        niftyind_metrics_folder: Metrics_Logs_LSTM_NIFTYIND
        goetf_metrics_folder: Metrics_Logs_LSTM_GOETF
        dbetf_metrics_folder: Metrics_Logs_LSTM_DBETF
        bnpetf_metrics_folder: Metrics_Logs_LSTM_BNPETF
        ben_etf_metrics_folder: Metrics_Logs_LSTM_BEN_ETF

mail:
        sender_email: <EMAIL>
        receiver_emails_local: ['<EMAIL>']
        receiver_emails_preprod: ['<EMAIL>', '<EMAIL>']
        receiver_emails_prod: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        receiver_emails_error: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        password: iqqi vquc vnwr ajru
        scopes: ['https://www.googleapis.com/auth/gmail.send']
        creds_bucket: etf-predictions
        creds_path: preportfolio/gmail_credentials/credentials.json


parameters:
        testing_date: ['2025-04-30']
        year: 24
        model_quarter: Q4
        sector_identifier: sector-average
        pred_column_name: predictions

inhouse_api:
        get_from_api_isin_list: ['BNPIFJP', 'BNPIFEU', 'BNPIFUS', 'BNPIFU10', 'BNPIFCN', 'BNPIFJ10', 'BNPIFE10', 'BNPIG0GC', 'BNPIDSBU', 'BNPIFEM', 'DBDRUS02', 'DBDRUS10', 'DBDRUS20', 'DBEEEMGF', 'DBEETGFU', 'DBEETGFT', 'DBEEUGFT', 'DBEEUNGF', 'DBEEURGF', 'DBRCOYGC', 'HSMETYB8', 'MQFIUSUS', 'MQFIUSTY', 'MQFIUSTU', 'HSMETYV3', 'HSMETYSN']
        stock_url: http://52.2.217.192:8080/stockhistory/getstocksbyisin?
        country_code: USA

snp:
        url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json
        authorization: Basic ************************************************
        content_type: application/json
        function: GDSHE
        function_traded: GDSP
        mnemonic: IQ_CLOSEPRICE
        mnemonic_traded: IQ_PRICEDATE
        period_type: IQ_FY
        frequency: Daily

s3_version_control:
        bucket: eq-model-output
        preprod_bucket: eq-pre-model-output
        filename_Monthly: lstm_model/monthly/date/predictions/isin.csv
        filename_Daily: lstm_model/daily/date/predictions/isin.csv
        metrics_filename_Monthly: lstm_model/monthly/date/metrics/isin.csv
        metrics_filename_Daily: lstm_model/daily/date/metrics/isin.csv

metrics:
        price_metrics: [mean_absolute_error, mean_squared_error, root_mean_squared_error, r2_score, adjusted_r2_score, total_perc_diff, abs_total_diff, total_variance_perc_diff, abs_total_variance_perc_diff, correlation_score]
        er_metrics: [directionality_score, mean_directionality, confidence_score, avg_confidence_score, accuracy_14_day, accuracy_22_day, accuracy_1_day]
        