from imports import *
from common_helper import HelperFuncs

commonconn = HelperFuncs()

script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)

parser = argparse.ArgumentParser(prog='LSTM Metrics Script', description='Fetches tag, schedular and environment from cron job')
parser.add_argument('-t', '--tag')
parser.add_argument('-s', '--schedular')
parser.add_argument('-e', '--environment')
args = parser.parse_args()

schedular = args.schedular
tag = args.tag
env = args.environment

scopes = config['mail']['scopes']

old_bucket = config['s3_buckets']['lstm_bucket_old']
new_bucket = config['s3_buckets']['lstm_bucket_new']
versioning_bucket = config['s3_version_control']['bucket']
versioning_filename_monthly = config['s3_version_control']['metrics_filename_Monthly']
versioning_filename_daily = config['s3_version_control']['metrics_filename_Daily']

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")

class MetricsHelperFuncs:
    # Fetches daily run prediction file
    @staticmethod
    def get_predictions_data(tag, days, saved_date):
        s3_upload_path, test_upload_path = commonconn.get_upload_paths(tag, days, config['parameters']['year'])
        pred_path = f"{s3_upload_path}/predictionresult_{saved_date.replace('-', '_')}.csv"
        pred_df = commonconn.s3_client.read_advanced_as_df(new_bucket, pred_path)
        traded_isins = pred_df['isin'].values.tolist()

        return pred_df, traded_isins
    
    # Fetches historical prediction data for all isins 
    @staticmethod
    def fetch_metrics_data(traded_isins, schedular):
        year = datetime.today().year
        start_date = f"{year}-01-01"
        end_date = (datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')
        pred_index = config['es_index']['lstm_index']

        metrics_data_df = commonconn.get_date_range_es_data(traded_isins, start_date, end_date, pred_index, schedular)
        print(f"metrics data df is: {metrics_data_df}")

        available_isins = metrics_data_df['isin'].unique().tolist()
        missing_isins = list(set(traded_isins) - set(available_isins))

        return metrics_data_df, available_isins, missing_isins

    # Compute avg_prediction
    @staticmethod
    def compute_avg_prediction(row_idx, df_isin):
        values = []
        for i in range(1, 8):
            pred_list = df_isin.loc[row_idx - i, f'pred_shift_{i}'] if row_idx - i >= 0 else None
            if isinstance(pred_list, list) and len(pred_list) > i - 1:
                values.append(pred_list[i - 1])
        return np.mean(values) if values else np.nan
    
    # Prepares the input columns for monthly schedular
    def prepare_monthly_inputs(self, metrics_data_df):
        metrics_data_df['date'] = pd.to_datetime(metrics_data_df['date']) 
        final_df = pd.DataFrame()

        for isin in metrics_data_df['isin'].unique():
            df_isin = metrics_data_df[metrics_data_df['isin'] == isin].copy()
            df_isin.sort_values(by='date', ascending=True, inplace=True)  
            df_isin = df_isin.tail(51).reset_index(drop=True) 
            
            avg_preds = []

            for i in range(len(df_isin)):
                values = []
                for j in range(7):
                    idx = i - j  # today (i), yesterday (i-1), ..., i-6
                    if idx >= 0:
                        preds = df_isin.loc[idx, 'predictions']
                        if isinstance(preds, list) and len(preds) > j:
                            values.append(preds[j])  # take j-th prediction
                avg_preds.append(np.mean(values) if values else np.nan)

            df_isin['avg_predictions'] = avg_preds

            df_isin['ER_pred'] = (df_isin['avg_predictions'].apply(lambda x: x) - df_isin['actuals'].apply(lambda x: x))/df_isin['actuals'].apply(lambda x: x)
            df_isin['ER_actual'] = df_isin['actuals'].pct_change(1)
            df_isin['ER_actual'] = df_isin['ER_actual'].shift(-1)
            df_isin['ER_pred'] = df_isin['ER_pred']*100
            df_isin['ER_actual'] = df_isin['ER_actual']*100

            final_df = pd.concat([final_df, df_isin], ignore_index=True)

        print(f"metrics input df for monthly is: {final_df}")
        return final_df
    
    # Prepares the input columns for daily schedular 
    def prepare_daily_inputs(self, metrics_data_df):
        metrics_data_df['date'] = pd.to_datetime(metrics_data_df['date']) 
        final_df = pd.DataFrame()

        for isin in metrics_data_df['isin'].unique():
            df_isin = metrics_data_df[metrics_data_df['isin'] == isin].copy()
            df_isin.sort_values(by='date', ascending=True, inplace=True)  
            df_isin = df_isin.tail(51).reset_index(drop=True)

            df_isin['avg_predictions'] = df_isin['predictions'].apply(lambda x: x[0] if isinstance(x, list) and len(x) == 1 else np.nan)
            df_isin['ER_pred'] = (df_isin['avg_predictions'].apply(lambda x: x) - df_isin['actuals'].apply(lambda x: x))/df_isin['actuals'].apply(lambda x: x)
            df_isin['ER_actual'] = df_isin['actuals'].pct_change(1)
            df_isin['ER_actual'] = df_isin['ER_actual'].shift(-1)
            df_isin['ER_pred'] = df_isin['ER_pred']*100
            df_isin['ER_actual'] = df_isin['ER_actual']*100

            final_df = pd.concat([final_df, df_isin], ignore_index=True)
            
        print(f"metrics input df for daily is: {final_df}")
        return final_df
    
    # Processes isin for calculating metrics using multithreading
    @staticmethod
    def process_isin(isin, metrics_df):
        df_isin = metrics_df[metrics_df['isin'] == isin].copy()
        df_isin.sort_values(by='date', ascending=True, inplace=True)
        df_isin.reset_index(drop=True, inplace=True)

        df_temp = df_isin[['date', 'actuals', 'avg_predictions', 'ER_pred', 'ER_actual']].copy()

        if len(df_temp) < 22:
            print(f"insufficient data for calculating metrics for isin: {isin}")
            return pd.DataFrame()

        try:
            df_metrics_price = MetricsHelper(commonconn.es_client).calculate_metrics_helper(
                df_temp, isin, period=22, prediction_column='avg_predictions', actual_column='actuals',
                metrics_to_calculate=config['metrics']['price_metrics'], n_features=0
            )
            df_metrics_price = df_metrics_price.rename(columns={'predictions': 'avg_predictions', 'actual_returns': 'actuals'})
            df_metrics_price['date'] = df_metrics_price['date'] + BDay(1)

            df_metrics_er = MetricsHelper(commonconn.es_client).calculate_metrics_helper(
                df_temp, isin, period=22, prediction_column='ER_pred', actual_column='ER_actual',
                metrics_to_calculate=config['metrics']['er_metrics'], n_features=0
            )
            df_metrics_er = df_metrics_er.rename(columns={'predictions': 'ER_pred', 'actual_returns': 'ER_actual'})
            df_metrics_er['date'] = df_metrics_er['date'] + BDay(1)

            merged_price_df = pd.merge(df_isin, df_metrics_price, on='date', how='inner', suffixes=('', '_dup'))
            merged_price_df.drop(columns=[col for col in ['actuals_dup', 'avg_predictions_dup', 'ER_pred_dup', 'ER_actual_dup', 'isin_dup'] if col in merged_price_df.columns], inplace=True)

            merged_metrics_df = pd.merge(merged_price_df, df_metrics_er, on='date', how='inner', suffixes=('', '_dup'))
            merged_metrics_df.drop(columns=[col for col in ['actuals_dup', 'avg_predictions_dup', 'ER_pred_dup', 'ER_actual_dup', 'isin_dup'] if col in merged_metrics_df.columns], inplace=True)

            return merged_metrics_df
        except Exception as e:
            print(f"Error processing ISIN {isin}: {e}")
            return None
        
    # Processes isin for calculating metrics using multiprocessing
    @staticmethod
    def process_isin_metrics(df_isin, isin, saved_date):
        try:
            df_isin.sort_values(by='date', ascending=True, inplace=True)
            df_isin.reset_index(drop=True, inplace=True)

            df_temp = df_isin[['date', 'actuals', 'avg_predictions', 'ER_pred', 'ER_actual']].copy()

            if len(df_temp) < 22:
                print(f"insufficient data for calculating metrics for isin: {isin}")
                return pd.DataFrame()

            df_metrics_price = MetricsHelper(commonconn.es_client).calculate_metrics_helper(
                df_temp, isin, period=22, prediction_column='avg_predictions',
                actual_column='actuals', metrics_to_calculate=config['metrics']['price_metrics'], n_features=0
            )
            df_metrics_price = df_metrics_price.rename(columns={'predictions': 'avg_predictions', 'actual_returns': 'actuals'})
            df_metrics_price['date'] = df_metrics_price['date'] + BDay(1)

            df_metrics_er = MetricsHelper(commonconn.es_client).calculate_metrics_helper(
                df_temp, isin, period=22, prediction_column='ER_pred',
                actual_column='ER_actual', metrics_to_calculate=config['metrics']['er_metrics'], n_features=0
            )
            df_metrics_er = df_metrics_er.rename(columns={'predictions': 'ER_pred', 'actual_returns': 'ER_actual'})
            df_metrics_er['date'] = df_metrics_er['date'] + BDay(1)

            merged_price_df = pd.merge(df_isin, df_metrics_price, on='date', how='inner', suffixes=('', '_dup'))
            merged_price_df.drop(columns=[col for col in ['actuals_dup', 'avg_predictions_dup', 'ER_pred_dup', 'ER_actual_dup', 'isin_dup'] if col in merged_price_df.columns], inplace=True)

            merged_metrics_df = pd.merge(merged_price_df, df_metrics_er, on='date', how='inner', suffixes=('', '_dup'))
            merged_metrics_df.drop(columns=[col for col in ['actuals_dup', 'avg_predictions_dup', 'ER_pred_dup', 'ER_actual_dup', 'isin_dup'] if col in merged_metrics_df.columns], inplace=True)

            merged_metrics_df['date'] = merged_metrics_df['date'].dt.strftime('%Y-%m-%d')
            merged_metrics_df = merged_metrics_df[merged_metrics_df['date'] == saved_date]
            merged_metrics_df.reset_index(drop=True, inplace=True)

            return merged_metrics_df

        except Exception as e:
            print(f"Error in processing ISIN {isin}: {e}")
            return pd.DataFrame()  # Return empty DataFrame on error
        
    # Calculates metrics using multithreading
    def calculate_metrics(self, metrics_df, saved_date):
        final_df_list = []
        unique_isins = metrics_df['isin'].unique()

        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(self.process_isin, isin, metrics_df) for isin in unique_isins]
            for future in as_completed(futures):
                result = future.result()
                if result is not None:
                    final_df_list.append(result)

        final_df = pd.concat(final_df_list, ignore_index=True)
        final_df['date'] = final_df['date'].dt.strftime('%Y-%m-%d')
        final_df = final_df[final_df['date'] == saved_date]
        final_df.reset_index(drop=True, inplace=True)

        print(f"final df with all isin metrics is: {final_df}")
        print(final_df.columns.tolist())

        return final_df
    
    # Calculates metrics using multiprocessing
    def calculate_metrics_with_process_pool(self, metrics_df, saved_date):
        final_df = pd.DataFrame()
        isins = metrics_df['isin'].unique()

        tasks = []
        with ProcessPoolExecutor(max_workers=min(8, multiprocessing.cpu_count())) as executor:
            for isin in isins:
                df_isin = metrics_df[metrics_df['isin'] == isin].copy()
                tasks.append(executor.submit(self.process_isin_metrics, df_isin, isin, saved_date))

            for future in as_completed(tasks):
                try:
                    result = future.result()
                    if result is not None and not result.empty:
                        final_df = pd.concat([final_df, result], ignore_index=True)
                except Exception as e:
                    print(f"Error processing ISIN metrics: {e}")

        print(f"final df with all isin metrics is: {final_df}")
        print(final_df.columns.tolist())
        return final_df
    
    # Gets upload paths for metrics file
    @staticmethod
    def get_metrics_upload_paths(tag, days, year):
        context = {
            'tag': tag,
            'days': days,
            'year': str(year + 1).zfill(2)
        }

        default_keys = ('metrics_upload_path_template', 'testing_metrics_upload_path_template')
        upload_key, test_key = default_keys

        upload_template = config['s3_paths'].get(upload_key)
        test_template = config['s3_paths'].get(test_key)

        s3_upload_path = upload_template.format(**context) if upload_template else None
        test_upload_path = test_template.format(**context) if test_template else None

        return s3_upload_path, test_upload_path
    
    # Uploads the final metrics data
    def upload_final_data(self, final_df, tag, days, year):
        s3_upload_path, test_upload_path = self.get_metrics_upload_paths(tag, days, year)
        if env == 'prod':
            index = config['es_index']['lstm_metrics_index']
            commonconn.s3_client.write_advanced_as_df(final_df, new_bucket, f"{s3_upload_path}/metrics_{saved_date}.csv")
            if days==7:
                rows = [(idx, row, versioning_bucket, versioning_filename_monthly.replace('date',str(saved_date))) for idx, row in final_df.iterrows()]
                with ThreadPoolExecutor() as executor:
                    executor.map(lambda args: commonconn.save_row(*args), rows)
            elif days==1:
                rows = [(idx, row, versioning_bucket, versioning_filename_daily.replace('date',str(saved_date))) for idx, row in final_df.iterrows()]
                with ThreadPoolExecutor() as executor:
                    executor.map(lambda args: commonconn.save_row(*args), rows)
            else:
                print("Invalid schedular")
            final_df = final_df.where(pd.notna(final_df), None)
            commonconn.es_client.save_records_v2(final_df, index)
        elif env == 'preprod':
            index = config['es_index']['preprod_lstm_metrics_index']
            preprod_versioning_bucket = config['s3_version_control']['preprod_bucket']
            commonconn.s3_client.write_advanced_as_df(final_df, new_bucket, f"{test_upload_path}/metrics_{saved_date}.csv")
            if days==7:
                rows = [(idx, row, preprod_versioning_bucket, versioning_filename_monthly.replace('date',str(saved_date))) for idx, row in final_df.iterrows()]
                with ThreadPoolExecutor() as executor:
                    executor.map(lambda args: commonconn.save_row(*args), rows)
            elif days==1:
                rows = [(idx, row, preprod_versioning_bucket, versioning_filename_daily.replace('date',str(saved_date))) for idx, row in final_df.iterrows()]
                with ThreadPoolExecutor() as executor:
                    executor.map(lambda args: commonconn.save_row(*args), rows)
            else:
                print("Invalid schedular")
            final_df = final_df.where(pd.notna(final_df), None)
            commonconn.training_es_client.save_records_v2(final_df, index)
        elif env == 'local':
            index = config['es_index']['preprod_lstm_metrics_index']
            print('Final dataframe Uploaded')
            final_df = final_df.where(pd.notna(final_df), None)
            commonconn.training_es_client.save_records_v2(final_df, index)

        return 'Data Uploaded'





