"""
LSTM Model Scripts - Daily Run

Refactored LSTM model daily run with improved error handling,
logging, and standardized utilities usage.
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import pandas as pd
from datetime import datetime
from pandas.tseries.offsets import BDay

# Import shared utilities package
from shared_utils import (
    load_config, validate_common_config, create_model_logger, create_error_handler,
    ErrorSeverity, ModelError, ConfigurationError, ErrorContext
)

# Import existing modules
from imports import *
from common_helper import HelperFuncs


class LSTMModelRunner:
    """LSTM Model execution orchestrator with improved error handling."""

    def __init__(self, tag: str, scheduler: str, environment: str):
        """
        Initialize LSTM Model Runner.

        Args:
            tag: Model tag
            scheduler: Scheduler type
            environment: Environment (dev, staging, prod)
        """
        self.tag = tag
        self.scheduler = scheduler
        self.environment = environment

        # Setup days mapping
        self.days = {'Monthly': 7, 'Daily': 1}.get(scheduler)
        if self.days is None:
            raise ConfigurationError(f"Invalid scheduler: {scheduler}")

        # Setup script directory and configuration
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(self.script_dir, 'config.yaml')
        self.config = load_config(config_path)
        self._validate_config()

        # Setup dates
        curr_date = datetime.today().strftime("%Y-%m-%d")
        self.saved_date = (pd.to_datetime(curr_date) - BDay(1)).strftime("%Y-%m-%d")

        # Setup logging
        log_dir = os.path.join(self.script_dir, self.config.get(f'log_files.{tag}_folder', 'logs'))
        self.logger = create_model_logger(
            model_name='lstm_model',
            tag=tag,
            scheduler=scheduler,
            run_date=self.saved_date,
            log_dir=log_dir
        )

        # Setup error handler
        self.error_handler = create_error_handler(self.logger.get_logger())

        # Initialize helper connection (backward compatibility)
        self.commonconn = HelperFuncs()

        # Configuration shortcuts
        self.year = self.config.get('parameters.year')
        self.old_bucket = self.config.get('s3_buckets.lstm_bucket_old')
        self.new_bucket = self.config.get('s3_buckets.lstm_bucket_new')

        self.logger.log_model_start({
            'tag': tag,
            'scheduler': scheduler,
            'environment': environment,
            'days': self.days,
            'saved_date': self.saved_date
        })

    def _validate_config(self) -> None:
        """Validate configuration requirements."""
        required_keys = [
            'parameters.year',
            's3_buckets.lstm_bucket_old',
            's3_buckets.lstm_bucket_new'
        ]

        if not self.config.validate_required_keys(required_keys):
            raise ConfigurationError("Missing required configuration keys")

    def daily_predictions(self) -> Tuple[pd.DataFrame, Dict[str, int]]:
        """
        Execute daily predictions with improved error handling and logging.

        Returns:
            Tuple of (final_df, metrics_dict)
        """
        try:
            self.logger.info(f"Starting daily predictions for tag: {self.tag}")

            # Get input data
            with ErrorContext(self.error_handler, "get_inputdata"):
                deployments_df, missing_isin_list, no_dep_count, tag_isin_df = self.commonconn.get_inputdata(
                    self.days, self.year, self.tag
                )

            self.logger.debug(f"Retrieved deployments DataFrame with shape: {deployments_df.shape}")

            # Create model identifier
            with ErrorContext(self.error_handler, "create_model_identifier"):
                deployments_df = self.commonconn.create_model_identifier(deployments_df)

            self.logger.debug(f"Added model identifiers to deployments DataFrame")

            # Prepare deployment submission
            deployment_toSubmit = deployments_df[['deployment_space', 'deployment_id', 'test_data']].copy()
            deployment_toSubmit['test_data'] = deployment_toSubmit['test_data'].apply(lambda x: x[0])
            pred_submit_count = len(deployment_toSubmit)

            self.logger.info(f"Prepared {pred_submit_count} deployments for prediction")

            # Get predictions
            with ErrorContext(self.error_handler, "prediction_run"):
                df_pred = self.commonconn.prediction_run(deployment_toSubmit)

            self.logger.debug(f"Received predictions with shape: {df_pred.shape}")

            # Calculate metrics
            failed_pred_count = df_pred['predictions'].apply(self.commonconn.is_invalid_prediction).sum()
            master_len = len(tag_isin_df) if tag_isin_df is not None else 0

            # Additional processing (sector predictions, etc.)
            sector_pred_count = 0  # Placeholder - implement based on actual logic
            no_sector_pred_count = 0  # Placeholder - implement based on actual logic

            metrics = {
                'no_dep_count': no_dep_count,
                'pred_submit_count': pred_submit_count,
                'failed_pred_count': failed_pred_count,
                'master_len': master_len,
                'sector_pred_count': sector_pred_count,
                'no_sector_pred_count': no_sector_pred_count
            }

            # Log prediction summary
            self.logger.log_prediction_summary(
                predictions_generated=pred_submit_count - failed_pred_count,
                predictions_failed=failed_pred_count
            )

            self.logger.info(f"Daily predictions completed successfully")
            return df_pred, metrics

        except Exception as e:
            self.error_handler.handle_error(
                ModelError(f"Daily predictions failed: {e}", ErrorSeverity.HIGH),
                context={
                    "tag": self.tag,
                    "days": self.days,
                    "year": self.year
                }
            )
            raise
    
    df_new1 = pd.DataFrame()
    df_new1 = commonconn.prediction_run(deployment_toSubmit)
    
    print(f'first prediction run output: {df_new1}')  
    
    df_new3 = df_new1[~pd.isna(df_new1['status'])].copy()
    print(f'completed df new is: {df_new3}')
    
    df_rerun = df_new1[pd.isna(df_new1['status'])].copy()
    print(f'df rerun is: {df_rerun}')
    
    if len(df_rerun) > 0:
        rerun_dep_list = df_rerun['deployment_id'].values.tolist()
        deptosubmit_rerun = deployment_toSubmit[deployment_toSubmit['deployment_id'].isin(rerun_dep_list)]
        print(f'deptoSubmit rerun is: {deptosubmit_rerun}')
        df_new2 = commonconn.prediction_run(deptosubmit_rerun)
        # df_new2 = df_new2[~pd.isna(df_new2['status'])].copy()
        print(f'rerun prediction output is: {df_new2}')
        df_combined = pd.concat([df_new3, df_new2], axis=0)
        df_combined.reset_index(inplace = True, drop = True)
    else:
        df_combined = df_new3
    
    print(f'df after combining: {df_combined}')

    df = pd.DataFrame()
    df = pd.merge(deployments_df, df_combined, on=['deployment_space', 'deployment_id'], how='inner')
    df.rename(columns={'deployment_space':'space_id'}, inplace=True)  
    print(f'df after merging is: {df}')  

    fin_df = pd.DataFrame()
    fin_df, failed_pred_count = commonconn.post_process(df)

    final_df, sector_pred_count, no_sector_pred_count = commonconn.get_sector_avg(fin_df, tag_isin_df, missing_isin_list, days)

    # final_df.fillna('', inplace=True)
    
    print(f'Final df for {tag} {days} is: {final_df}')

    return final_df, no_dep_count, pred_submit_count, failed_pred_count, len(tag_isin_df), sector_pred_count, no_sector_pred_count

# Setting trigger mail
try:
    body1 = f"LSTM model run on date {saved_date} has started"
    subject1 = f"LSTM Model {tag} {schedular} Run Started"
    # commonconn.send_mail(subject1, body1)
    commonconn.send_mail(subject1, body1, errorbool=True)
except Exception as e:
    logs.error(f"Error while sending trigger mail")

try:
    final_df, no_dep_count, pred_submit_count, failed_pred_count, master_len, sector_pred_count, no_sector_pred_count = daily_predictions(tag, days)
except Exception as e:
    logs.error(f"Error while running daily_predictions is: {e}")
    trace = traceback.format_exc()
    body2 = f'''Error while running daily_predictions is: {e}
    Traceback is: {trace}'''
    subject2 = f"Error in LSTM Model {tag} {schedular} run"
    # commonconn.send_mail(subject2, body2)
    commonconn.send_mail(subject2, body2, errorbool=True)
    sys.exit(0)

try:
    uploaded_count, fil_df = commonconn.upload_final_data(final_df, tag, days, year)
    yes_pred_no_traded_len = len(fil_df)
    no_pred_no_traded_len = commonconn.get_traded_count()
except Exception as e:
    logs.error(f"Error while uploading final data is: {e}")
    body3 = f"Error while uploading final data is: {e}"
    subject3 = f"Error in LSTM Model {tag} {schedular} run"
    # commonconn.send_mail(subject3, body3)
    commonconn.send_mail(subject3, body3, errorbool=True)

# Mailing the daily run statistics
body = f"""For date {saved_date}:
        Total number of isins for tag {tag} = {master_len}
        Number of isins with no model available = {no_dep_count}
        Number of isins submitted for predictions = {pred_submit_count}
        Number of isins for which predictions failed = {failed_pred_count}
        Number of isins having sufficient data for prediction but not traded = {yes_pred_no_traded_len}
        Number of isins with no data and were not traded = {no_pred_no_traded_len}
        Number of isins with predictions from sector avg = {sector_pred_count}
        Number of isins with no sector average available = {no_sector_pred_count}
        Number of records uploaded to ES = {uploaded_count}
"""
subject = f'LSTM Model {tag} {schedular} Run Statistics'
commonconn.send_mail(subject, body)


