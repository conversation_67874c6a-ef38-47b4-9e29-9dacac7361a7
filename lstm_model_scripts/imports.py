import requests
from sklearn.preprocessing import MinMaxScaler
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import time
import math
from multiprocessing.pool import ThreadPool
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from concurrent.futures import <PERSON><PERSON>oolExecutor
import multiprocessing
import concurrent.futures
import os
from io import StringIO
import boto3
import json
import yaml
import logging


from ibm_watsonx_ai import APIClient
from ibm_watsonx_ai import Credentials as IBM_Creds
# from ibm_watsonx_ai.experiment import AutoAI
# from ibm_watsonx_ai.deployment import WebService, Batch
# from ibm_watsonx_ai.helpers.connections import DataConnection, S3Location

from eq_common_utils.utils.config.es_config import es_config
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.opensearch_helper import OpenSearch
from eq_common_utils.utils.ibm_helper import IBMConnection
from eq_common_utils.ds_scripts.prediction_helper import <PERSON><PERSON><PERSON><PERSON><PERSON> as PREDICTIONHelper
from eq_common_utils.utils.logger_handler import LoggerHandler
from eq_common_utils.utils.metrics_helper import MetricsHelper
import configparser
import argparse

import smtplib
from email.mime.text import MIMEText
import sys

import base64
import mimetypes
from email.message import EmailMessage
from typing import List, Optional, Union

import google.auth
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google.oauth2.credentials import Credentials

from pandas.tseries.offsets import BDay
# from pandarallel import pandarallel
# pandarallel.initialize(progress_bar=True,nb_workers=4)

import traceback
import warnings
warnings.filterwarnings('ignore')