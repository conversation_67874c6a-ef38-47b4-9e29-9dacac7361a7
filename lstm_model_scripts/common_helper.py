from imports import *

script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)

parser = argparse.ArgumentParser(prog='LSTM Common Script', description='Fetches tag, schedular and environment from cron job')
parser.add_argument('-t', '--tag')
parser.add_argument('-s', '--schedular')
parser.add_argument('-e', '--environment')
args = parser.parse_args()

schedular = args.schedular
tag = args.tag
env = args.environment

scopes = config['mail']['scopes']

old_bucket = config['s3_buckets']['lstm_bucket_old']
new_bucket = config['s3_buckets']['lstm_bucket_new']
versioning_bucket = config['s3_version_control']['bucket']
versioning_filename_monthly = config['s3_version_control']['filename_Monthly']
versioning_filename_daily = config['s3_version_control']['filename_Daily']

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")
job_count = 0

class HelperFuncs:
    def __init__(self):
        self.s3_client = s3_config()
        self.es_client = es_config(env='prod')
        self.training_es_client = es_config(env='pre')
        self.masters_url = config['masteractive']['masters_url']
        # self.ibm_connection = IBMConnection(
        #     config['ibm']['url'],
        #     config['ibm']['api_key'])
        # self._predictionhelper = PREDICTIONHelper(self.ibm_connection)
        self.not_traded_count = 0

    @staticmethod
    def get_ibm_client():
        wml_credentials = IBM_Creds(
            api_key=config['ibm']['api_key'], 
            url=config['ibm']['url']
        )
        wmlclient = APIClient(credentials = wml_credentials)
        return wmlclient
    
    @staticmethod
    def del_jobs(job_id, wmlclient):
        try:
            status = wmlclient.deployments.delete_job(job_id, hard_delete=True)
            return f'{job_id}:{status}'
        except Exception as e:
            print(f'job:{job_id}:{str(e)}')

    @staticmethod
    def create_deployment_jobs(deployment_id, test_data, wmlclient):
        try:
            actuals = []

            if test_data is None:
                print(f"Input data not available for: {deployment_id}")
                raise Exception("Input data not available")

            job_payload_ref = {wmlclient.deployments.ScoringMetaNames.INPUT_DATA: [{'fields': [], 'values': test_data.tolist()}]}

            job = wmlclient.deployments.create_job(deployment_id, meta_props=job_payload_ref) # Creates the scoring/prediction job
            
            global job_count 
            job_count += 1

            job_id = wmlclient.deployments.get_job_uid(job)

            return [deployment_id, job_id]
        
        except Exception as e:
            logs.error(f'for deployment_id: {deployment_id}, error is {e}')
            print(f'for deployment_id: {deployment_id}, error is {e}')
            return [deployment_id, '']
        
    @staticmethod 
    def contains_list(var):
        return isinstance(var, list) and any(isinstance(i, list) for i in var)
    
    def get_state(self, job_id, wmlclient):
        status = ''
        values=[]
        try:
            # Get only the status and predictions output from get_job_details
            job_details = wmlclient.deployments.get_job_details(job_id)#, include='predictions,status')
            status =  job_details['entity']['scoring']['status']['state']

            if status=='completed':
                a = job_details['entity']['scoring']['predictions'][0]['values']
                # print(f' job details is: {a}')
                    
                for value in a:
                    if self.contains_list(value):
                        values = [value[0]]
                    else:
                        values = [value]

            return [job_id, status, values]

        except Exception as e:
            logs.error(f'For job_id: {job_id}, error is {e}')
            return [job_id, 'error', []]
        
    def prediction_run(self, deployment_toSubmit):
        wmlclient = self.get_ibm_client()
        
        # Create a pool for Delete jobs
        delete_pool = ThreadPool(24)

        all_jobs_status = []
        all_jobs_created = []
        all_jobs_deleted = []

        start = time.perf_counter()
    #     batch_run_size = 200
        batch_run_size = 60
        for space_id in deployment_toSubmit['deployment_space'].unique().tolist()[:]:

            wmlclient.set.default_space(space_id)
            print(f"starting for space:{space_id}")

            delete_results = []
            job_status = []

            # Set max time for the entire duration of this run
    #         max_run_duration = 3600
            max_run_duration = 4800
            run_duration = 0

            space_start = time.perf_counter()

            deployments = deployment_toSubmit[deployment_toSubmit['deployment_space']==space_id][:]
    #         deployments = deployment_toSubmit[(deployment_toSubmit['deployment_space']==space_id) & (deployment_toSubmit['deployment_id']=="a4b22f24-c453-4d83-843c-4c951d7ea30f")]

            print(f"length of deployment list:{deployments.shape[0]}")

            jobs_to_submit=batch_run_size
            jobs_submitted=0
            jobs_executing=0
            no_new_jobs_counter=0 # added
            prev_jobs_executing=0 #added
            j=0

            while (( jobs_to_submit > 0 ) or ( jobs_executing > 0 )) and ( run_duration < max_run_duration ) and no_new_jobs_counter < 50: # changed

                jobs_to_submit=min(deployments.shape[0]-jobs_submitted,jobs_to_submit)
                to_fetch_list=()

                #
                # Submit the jobs
                #
                if ( jobs_to_submit > 0 ) :   

                    deployment_jobs = deployments.iloc[jobs_submitted:(jobs_submitted+jobs_to_submit)].copy()
                    deployment_jobs = deployment_jobs[['deployment_id', 'test_data']].copy()

                    job_created = []
                    job_create_start = time.perf_counter()
                    create_results = []
                    print(f'jobs_to_submit:{jobs_to_submit},iteration:{j+1}, submitted:{jobs_submitted},jobs_to_submit_actual:{deployment_jobs.shape[0]},total_jobs:{deployments.shape[0]}')
                    thread_pool_size = jobs_to_submit if jobs_to_submit <= 100 else 100
                    pool = ThreadPool(thread_pool_size)

                    for deployment in deployment_jobs.values.tolist():
                        create_results.append(pool.apply_async(self.create_deployment_jobs, deployment+[wmlclient]))

                    pool.close()
                    pool.join()

                    job_created = [r.get() for r in create_results]
                    all_jobs_created.extend(job_created)
                    job_create_finish = time.perf_counter()
                    print(f'create jobs finished in {round((job_create_finish-job_create_start), 2)} seconds(s)')
                    print(f'number of jobs created: {len(job_created)}')
                    print(f'number of jobs created with error : {len([job for job in job_created if job is None])}')
                    print(f'number of jobs created with no job_id: {len([job for job in job_created if job[1]==""])}')
                    job_created_df = pd.DataFrame(columns=['deployment_id', 'job_id'], data=job_created)
                    to_fetch_list=job_created_df[['job_id']].values.tolist()            

                wait_time = 5 # 0 if jobs_to_submit > 0 else 5 # changed
                no_new_jobs_counter = no_new_jobs_counter+1 if jobs_executing == prev_jobs_executing else 0 # added
                prev_jobs_executing = jobs_executing #added
                print(f'waiting for {wait_time} sec ...')
                time.sleep(wait_time)
                print('done waiting')

                #
                # Get Job Status
                #
                job_fetch_start = time.perf_counter()
                fetch_results = []
                pool = ThreadPool(24)

                if len(to_fetch_list) > 0  :  
                    to_fetch_list.extend([job[0]] for job in job_status if job[1] in ["queued", "running"])
                else:
                    to_fetch_list = ([job[0]] for job in job_status if job[1] in ["queued", "running"])  

                for job in to_fetch_list:
                    fetch_results.append(pool.apply_async(self.get_state, job+[wmlclient]))

                pool.close()
                pool.join()

                job_status = [r.get() for r in fetch_results]

                all_jobs_status.extend([job for job in job_status if job[1] in ["completed", "failed"]])
                job_fetch_finish = time.perf_counter()

                print(f'fetch jobs finished in {round((job_fetch_finish-job_fetch_start), 2)} seconds(s)')
                print(f'number of jobs fetched: {len(job_status)}')
                print(f'number of jobs fetched and completed: {len([job for job in job_status if job[1] in ["completed"]])}')
                print(f'number of jobs fetched and queued: {len([job for job in job_status if job[1] in ["queued"]])}')
                print(f'number of jobs fetched and failed: {len([job for job in job_status if job[1] in ["failed"]])}')
                print(f'number of jobs fetched and running: {len([job for job in job_status if job[1] in ["running"]])}')
                print(f'number of jobs fetched otherwise:{len([job for job in job_status if job[1] not in ["completed", "queued", "failed", "running"]])}')
                job_fetched_df = pd.DataFrame(columns=['job_id', 'status', 'values'], data=job_status)

                #
                # Delete completed and failed jobs
                #

                job_list = [job[0] for job in job_fetched_df[['job_id', 'status']].values.tolist() if (job[1] in ["completed", "failed"])]

                if ( len(job_list) > 0):     
                    for job in job_list:
                        delete_results.append(delete_pool.apply_async(self.del_jobs,(job, wmlclient,)) )

                job_delete_finish = time.perf_counter()

                jobs_submitted+=jobs_to_submit
                jobs_to_submit=len(job_list)
                jobs_executing=len([job for job in job_status if job[1] in ["queued", "running"]])

                j+=1
                print(f'total time taken in this loop, {j}: {round((job_delete_finish-job_create_start), 2)} sec(s)\n')

                run_duration = ( time.perf_counter() - start )
                print(f'current run duration : {run_duration} jobs_executing : {jobs_executing} submitted:{jobs_submitted},total_jobs:{len(deployments)}\n')

                #End While

            if (jobs_executing > 0):     
                for job in [job for job in job_status if job[1] in ["queued", "running"]]:
                    delete_results.append(delete_pool.apply_async(self.del_jobs,(job[0], wmlclient,)) )        

            print(f'total time taken in this space: {round((time.perf_counter()-space_start)/60, 2)} min(s)\n')
            print(f'length of all jobs created:{len(all_jobs_created)}')
            print(f'length of all jobs status:{len(all_jobs_status)}')
            all_jobs_deleted = [res for res in delete_results if res is not None]
            print(f'length of all jobs deleted: {len(all_jobs_deleted)}')

            df_all_jobs_status = pd.DataFrame(all_jobs_status, columns=['job_id', 'status', 'predictions'])
            df_all_jobs_created = pd.DataFrame(all_jobs_created, columns=['deployment_id', 'job_id'])
            df_consolidated = pd.merge(df_all_jobs_created, df_all_jobs_status, how='left', on='job_id')
            df_consolidated = pd.merge(deployment_toSubmit[['deployment_space', 'deployment_id']], df_consolidated, on='deployment_id', how = 'left')

                #End for

        delete_pool.close()
        delete_pool.join()

        print(f'total time taken:{round((time.perf_counter()-start)/60, 2)} min(s)')
        return df_consolidated
        
    @staticmethod
    def create_logger(log_file_path):
        log_dir = os.path.dirname(log_file_path)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)  # Create the directory if it doesn't exist
        logger = logging.getLogger('my_logger')
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            logger.removeHandler(handler)
        file_handler = logging.FileHandler(log_file_path, mode='w')  # Use mode='w' for write mode
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        return logger
    
    # Create the logs variable
    def generate_log_variable(self, tag):
        log_file_path = f"{script_dir}/{config['log_files'][f'{tag}_folder']}/{saved_date}_daily_run.log"

        global logs
        logs = self.create_logger(log_file_path)
    
    #Get data from masteractive for a given tag
    def get_master_df(self, tag):
        master_json = requests.get(f"{self.masters_url}{tag}").json() 
        master = pd.DataFrame(master_json["data"][f"masteractivefirms_{tag}"])
        return master
    
    #Generates predictions from IBM
    # def get_predictions(self, deployment_toSubmit):
    #     df_pred = pd.DataFrame()
    #     df_pred = self._predictionhelper.prediction_run(deployment_toSubmit)
    #     return df_pred
    
    #Get masteractive list for a given tag
    def get_firms_list(self, tag):
        aieq_isin_df = self.get_master_df('aieq')
        indt1_isin_df = self.get_master_df('indt1')
        aigo_isin_df = self.get_master_df('aigo')
        goetf_isin_df = self.get_master_df('goetf')
        dbetf_isin_df = self.get_master_df('dbetf')
        bnpetf_isin_df = self.get_master_df('bnpetf')
        ben_etf_isin_df = self.get_master_df('ben_etf')

        aigo_isin_df = aigo_isin_df[[(x not in aieq_isin_df['isin'].values) & (x not in indt1_isin_df['isin'].values) for x in aigo_isin_df['isin']]].reset_index(drop = True)

        isin_map = {
            'aieq': aieq_isin_df,
            'aigo': aigo_isin_df,
            'niftyind': indt1_isin_df,
            'goetf': goetf_isin_df,
            'dbetf': dbetf_isin_df,
            'bnpetf': bnpetf_isin_df,
            'ben_etf': ben_etf_isin_df
        }

        return isin_map.get(tag)

    # Get prod and testing s3 paths for uploading
    @staticmethod
    def get_upload_paths(tag, days, year):
        upload_key, test_key = ('upload_path_template', 'testing_upload_path_template')

        upload_template = config['s3_paths'].get(upload_key)
        test_template = config['s3_paths'].get(test_key)

        context = {
            'tag': tag,
            'days': days,
            'year': str(year + 1).zfill(2)
        }

        s3_upload_path = upload_template.format(**context) if upload_template else None
        test_upload_path = test_template.format(**context) if test_template else None

        return s3_upload_path, test_upload_path
    
    # Uploads data to ES row by row
    def upload_data_to_elastic_search(self, df, n_seq):
        documents = []
        for index, row in df.iterrows():
            schedular = 'm' if n_seq==7 else ('w' if n_seq==3 else 'd')
            doc_id = f"{row['isin']}_{row['date']}_{schedular}"
            year = row['date'].split('-')[0]

            document = {"index": {"_index": f"{config['es_index']['lstm_index']}_{year}", "_id": doc_id}}
            data = row.to_dict()
            documents.append(document)
            documents.append(data)
            
        if env == 'prod':
            response=self.es_client.bulk(documents)
        elif env == 'preprod':
            response=self.training_es_client.bulk(documents)

        print(f'the response is: {response}')
        logs.info(f"length of records updated in ES is: {len(documents)/2}")

        return len(documents)/2

    #Get ES data for an isin list for a given range of dates
    def get_date_range_es_data(self, isin_list, start_date, end_date, index_prefix, _schedular):
        start_year = (pd.to_datetime(start_date)).year
        end_year = (pd.to_datetime(end_date)).year
        data = []
        hits = []
        for year in range(start_year, end_year + 1):
            q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date,
            "lte": end_date}}},{"term": {"schedular.keyword": _schedular}}]}}}
            try:
                response,total_docs = self.es_client.search_with_pagination(index=f"{index_prefix}_{year}",query=q_total,paginate=False,strict=False)
            except Exception as e:
                pass
            for hit in response:
                es_data=hit['_source']
                data.append(es_data)

        es_df = pd.DataFrame(data)
        es_df['date'] = pd.to_datetime(es_df['date'])
        es_df.sort_values('date', ascending=True, inplace=True)
        es_df.reset_index(inplace=True, drop=True)

        return es_df
    
    #Get ES data for given isin and index from beginning of last year
    def get_es_data(self, isin, curr_year, index_prefix, _schedular):
        data=[]
        for year in range(curr_year-1, curr_year + 1):
            q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
            es = self.es_client
            result = es.run_query(query=json.loads(q_total),index=f"{index_prefix}_{year}")

            for rs in result['hits']['hits']:
                es_data=rs['_source']
                data.append(es_data)
        df=pd.DataFrame(data)

        if df.empty:
            print(f'No data in ES for {isin}')
            return df
        df_dates = df['date'].apply(lambda x: pd.to_datetime(x))
        unique_years = list(df_dates.dt.year.unique())

        if curr_year not in unique_years:
            return pd.DataFrame()
        df=df[df['schedular']== _schedular]
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(drop=True, inplace=True)
        return df
    
    # Ffill close price data in case of snp outage
    def ffill_closeprice(self, isin, start_date, end_date, days):
        start_date1 = '-'.join([start_date.split('/')[2].zfill(4), start_date.split('/')[0].zfill(2), start_date.split('/')[1].zfill(2)])
        end_date1 = '-'.join([end_date.split('/')[2].zfill(4), end_date.split('/')[0].zfill(2), end_date.split('/')[1].zfill(2)])

        curr_year = datetime.strptime(end_date1, "%Y-%m-%d").year
        schedular = 'Monthly' if days==7 else ('Weekly' if days==3 else 'Daily')
        index = config['es_index']['lstm_index']

        try:
            es_df = self.get_date_range_es_data([isin], start_date1, end_date1, index, schedular)
            es_df.rename(columns={'actuals': 'adj_close'}, inplace=True)
            df_snp = es_df[['adj_close', 'date']]
            df_snp['adj_close'] = df_snp['adj_close'].map(float)
        except Exception as e:
            logs.error(f"Error while fetching data from ES for isin {isin}: {e}")
            df_snp = pd.DataFrame(columns=['adj_close', 'date'])

        return df_snp

    # Determines whether a stock was traded or not on a particular day
    def get_last_trading_date(self, isin):
        url = config['snp']['url']
        headers = {'Authorization': config['snp']['authorization'],'Content-type': config['snp']['content_type']}                                  
        data = {
                "inputRequests": [
                {
                    "function": config['snp']['function_traded'],
                    "identifier": isin,
                    "mnemonic": config['snp']['mnemonic_traded']
                }
            ]
        }
        data_json = json.dumps(data)
 
        response = requests.post(url, data=data_json, headers=headers)
        try:
            resp_json=json.loads(response.text)
 
 
            if resp_json == None:
                return np.nan
 
            else:
                try:
                    tmp=pd.json_normalize(resp_json['GDSSDKResponse'])
                    data = tmp['Rows'].iloc[0]
                    date_traded = data[0]['Row'][0]
                    if date_traded == 'Data Unavailable':
                        return 'Last Traded Date Unavailable'
                    else:
                        date_traded = datetime.strptime(date_traded,'%Y-%m-%d')
                        date_obj = datetime.strptime(saved_date,'%Y-%m-%d')
                        if date_traded<date_obj:
                            return f'Not Traded on {date_obj}'
                        else:
                            return np.nan
                except:
                    return np.nan
        except Exception as e:
            logs.error(f"Error in finding the reason for no closeprice: {e}")
            return np.nan

    # Fetches close price data from snp
    def get_stock_price(self, isin, tic, exchange, start_date, end_date, days):
        get_from_api = False
        get_from_api_isin_list = config['inhouse_api']['get_from_api_isin_list']
        
        if isin in get_from_api_isin_list:
            get_from_api = True
            
        if get_from_api:
            
            start_date1 = '-'.join([start_date.split('/')[2].zfill(4), start_date.split('/')[0].zfill(2), start_date.split('/')[1].zfill(2)])
            end_date1 = '-'.join([end_date.split('/')[2].zfill(4), end_date.split('/')[0].zfill(2), end_date.split('/')[1].zfill(2)])

            stock_url = f"{config['inhouse_api']['stock_url']}isin={isin}&startDate={start_date1}&endDate={end_date1}&country_code={config['inhouse_api']['country_code']}"
            data_api = requests.get(stock_url).json()
            
            try:
                df_api = pd.DataFrame(data_api["data"]["stocks"])[['date', 'close']]
                df_api.sort_values(by='date', inplace=True)
                df_api.rename(columns={'close':'adj_close'}, inplace=True)
                df_api['adj_close'] = df_api['adj_close'].map(float)
                df_api['date'] = df_api['date'].apply(lambda x: '/'.join([x.split('-')[1].zfill(2), x.split('-')[2].zfill(2), x.split('-')[0].zfill(4)]))
                
            except Exception as e:
                logs.error(f'Error while getting df_api for isin: {isin}, error is {e}')
                df_api = pd.DataFrame(columns=['date', 'adj_close'])            
            
            return df_api
        
        else:

            data = {    
                "inputRequests": 
                [
                    {
                        "function": config['snp']['function'],
                        "identifier": f'{tic}:{exchange}',
                        "mnemonic": config['snp']['mnemonic'],
                        "properties": 
                        {
                            "periodType": config['snp']['period_type'],
                            "startDate": start_date,
                            "endDate": end_date,
                            "frequency": config['snp']['frequency']
                        }
                    }
                ]
            }

            data_json = json.dumps(data)

            url = config['snp']['url']
            headers = {'Authorization': config['snp']['authorization'],'Content-type': config['snp']['content_type']}
            response = requests.post(url, data=data_json, headers=headers)

            try:
                op=json.loads(response.text)
                df_snp = pd.DataFrame([row['Row'] for row in op['GDSSDKResponse'][0]['Rows']], columns=['adj_close', 'date'])
                df_snp['adj_close'] = df_snp['adj_close'].map(float)
                return df_snp

            except:
                
                data = {    
                    "inputRequests": 
                    [
                        {
                            "function": config['snp']['function'],
                            "identifier": f'{isin}',
                            "mnemonic": config['snp']['mnemonic'],
                            "properties": 
                            {
                                "periodType": config['snp']['period_type'],
                                "startDate":start_date,
                                "endDate":end_date,
                                "frequency": config['snp']['frequency']
                            }
                        }
                    ]
                }

                data_json = json.dumps(data)

                url = config['snp']['url']
                headers = {'Authorization': config['snp']['authorization'],'Content-type': config['snp']['content_type']}
                response = requests.post(url, data=data_json, headers=headers)
                
                try:
                    
                    op=json.loads(response.text)
                    df_snp = pd.DataFrame([row['Row'] for row in op['GDSSDKResponse'][0]['Rows']], columns=['adj_close', 'date'])
                    
                    df_snp['adj_close'] = df_snp['adj_close'].map(float)
                    return df_snp
                except Exception as e:
                    logs.info(f'No snp data able to be collected using either method for isin: {isin}')
                    isin_traded = True
                    traded_check = self.get_last_trading_date(isin)
                    if not pd.isna(traded_check):
                        isin_traded = False
                        logs.info(f'isin {isin} has not been traded on prev business day')

                    if isin_traded == True:
                        df_snp = self.ffill_closeprice(isin, start_date, end_date, days)
                        return df_snp
                    else:
                        self.not_traded_count += 1
                        return pd.DataFrame(columns=['adj_close', 'date'])
                
    # Scales close price values based on max and min price
    @staticmethod
    def scalingfunc(y):  
        min_v, max_v, x = y[8:11]
        scaler=MinMaxScaler(feature_range=(0,1))
        scaler.fit(np.array([min_v, max_v]).reshape(-1, 1))
        values = scaler.inverse_transform(np.array([x]).reshape(-1,1))
        return values[0][0] 
    
    def prepare_payload(self, deployment_space, deployment_id, isin, tic, exchange, min_p, max_p, training_date, days):        
        year = datetime.today().year
        start_date = f'01/01/{str(year-1).zfill(2)}'
        end_date = (datetime.today()+timedelta(days=-1)).strftime('%m/%d/%Y')
        
        try:
            df = self.get_stock_price(isin, tic, exchange, start_date, end_date, days)     
            # print(f"stock price df is: {df}")   
        except Exception as e:
            print(f'Error while running get_stock_price for {isin}, {tic}, {exchange}: {e}')
        
        if not df is None and df.shape[0]>100:
            
            if df.shape[0]>=100:
                scaler=MinMaxScaler(feature_range=(0,1))
                try:
                    scaler.fit(np.array([min_p, max_p]).reshape(-1, 1))      
                except Exception as e:
                    print(f'Error while scaling for {isin}, {tic}, {exchange}, {start_date}, {end_date}: {e}')
    #             dataset=scaler.transform(np.concatenate(([curr_price], np.array(df['adj_close']))).reshape(-1,1))
                dataset=scaler.transform(np.array(df['adj_close']).reshape(-1,1))
                dataX = []
                time_step = 100
                
                for i in range(len(dataset)-time_step+1):
                    a = dataset[i:(i+time_step)]
                    dataX.append(a)

    #             l = len(dataX) if len(dataX)<280 else 280
                l = 1
                test_data = np.array(dataX[-l:])
                test_data = test_data.reshape(test_data.shape[0], test_data.shape[1], 1)

                return [deployment_space, deployment_id, isin, tic, exchange, min_p, max_p, training_date, [test_data, df['date'].values[-l:], df['adj_close'].values[-l:]]]
            
        else:
            print(f"Insufficient price data for generating predictions for isin: {isin}, tic: {tic}, exchange: {exchange}")
            logs.info(f'Insufficient price data for generating predictions for isin: {isin}')

    # Creates the final dataframe to be submitted for predictions
    def get_inputdata(self, days, year, tag):  
        self.generate_log_variable(tag)

        object_key_template = config['s3_paths']['deployment_path_template']
        object_key_deployments = object_key_template.format(tag=tag, year='20' + str(year).zfill(2), days=days)

        df_isin_deployments = self.s3_client.read_as_dataframe(new_bucket, object_key_deployments)
        df_isin_deployments = df_isin_deployments[pd.isnull(df_isin_deployments['expiration_date'])]

        print(f"df_isin_deployments.shape is {df_isin_deployments.shape}")
        print(f"number of distinct spaces: {len(df_isin_deployments['deployment_space'].unique().tolist())}")

        tag_isin_df = self.get_firms_list(tag)
        tag_isin_list = tag_isin_df['isin'].tolist()

        missing_isin = list(set(tag_isin_list) - set(df_isin_deployments['isin'].values))
        no_dep_count = len(missing_isin)

        # Filter deployment file to contain only masteractive isins
        df_isin_deployments = df_isin_deployments[df_isin_deployments['isin'].isin(tag_isin_list)]

        if env == 'local':
            df_isin_deployments = df_isin_deployments[0:10]
            # df_isin_deployments = df_isin_deployments[df_isin_deployments['isin'] == 'DBDRUS02']

        df_isin_deployments = df_isin_deployments[['deployment_space', 'deployment_id', 'isin', 'tic', 'exchange', 'min_p', 'max_p', 'training_date']][:]
        print(f"length of predictions to be made: {df_isin_deployments.shape}")
        start = time.perf_counter()

        days_iterable = [days] * len(df_isin_deployments)
        results = []
        with concurrent.futures.ThreadPoolExecutor(500) as executor:    
            result = executor.map(self.prepare_payload, df_isin_deployments['deployment_space'], df_isin_deployments['deployment_id'], df_isin_deployments['isin'].values.tolist(), df_isin_deployments['tic'].values.tolist(), df_isin_deployments['exchange'].values.tolist(), df_isin_deployments['min_p'].values.tolist(), df_isin_deployments['max_p'].values.tolist(), df_isin_deployments['training_date'], days_iterable)
    #         result = executor.map(prepare_payload, df_isin_deployments['deployment_space'], df_isin_deployments['deployment_id'], df_isin_deployments['isin'].values.tolist(), df_isin_deployments['tic'].values.tolist(), df_isin_deployments['exchange'].values.tolist(), df_isin_deployments['min_p'].values.tolist(), df_isin_deployments['max_p'].values.tolist(), [year for _ in range(df_isin_deployments.shape[0])])

        for res in result:
            results.append(res)

        print(f"total time taken is {round((time.perf_counter()-start)/60, 2)} min")
        print(f"df_isin_deployments.shape is {df_isin_deployments.shape}")

        deployment_toSubmit = pd.DataFrame([res for res in results if not res is None], columns = ['deployment_space', 'deployment_id', 'isin', 'tic', 'exchange', 'min_p', 'max_p', 'training_date', 'test_data'])
        print(deployment_toSubmit.shape)
        return deployment_toSubmit, missing_isin, no_dep_count, tag_isin_df
    
    # Creates the model identifer column in the correct format
    @staticmethod
    def create_model_identifier(deployments_df):
        deployments_df['training_datetime'] = pd.to_datetime(deployments_df['training_date'])
        deployments_df['model_year'] = deployments_df['training_datetime'].dt.year.astype(str) + f"_{config['parameters']['model_quarter']}"
        deployments_df['training_timestamp'] = deployments_df['training_datetime'].dt.strftime('%Y-%m-%d_%H:%M:%S')

        deployments_df['model_identifier'] = (
            deployments_df['isin'] + "_" + 
            deployments_df['model_year'] + "_" + 
            deployments_df['training_timestamp']
        )

        deployments_df.drop(columns=['model_year', 'training_timestamp', 'training_datetime'], inplace=True)
        return deployments_df
            
    @staticmethod
    def inv_scaling(val, min_p, max_p):
        scaler=MinMaxScaler(feature_range=(0,1))
        scaler.fit(np.array([min_p, max_p]).reshape(-1, 1))
        values = scaler.inverse_transform(np.array(val)).tolist()
        return values
    
    @staticmethod
    def new_inv_scaling(val, min_p, max_p):
        if val is None or (isinstance(val, float) and pd.isna(val)):
            return np.nan
        scaler = MinMaxScaler(feature_range=(0, 1))
        scaler.fit(np.array([min_p, max_p]).reshape(-1, 1))
        values = scaler.inverse_transform(np.array(val)).flatten().tolist()
        return values
     
    def post_process(self, df):
        # Filter out rows where 'job_id' is NaN
        # df_filtered = df[~pd.isna(df['job_id'])].copy()
        df_filtered = df.copy()
        # failed_pred_count = df_filtered['job_id'].isna().sum()
        failed_pred_count = df_filtered['status'].isna().sum()

        # Apply inverse scaling to each row's predictions
        df_filtered['predictions'] = df_filtered.apply(lambda row: self.new_inv_scaling(row['predictions'], row['min_p'], row['max_p']), axis=1)
        print(f"df filtered after inverse scaling: {df_filtered}")

        data = []
        for _, row in df_filtered.iterrows():
            predictions = row['predictions']
            test_data = row['test_data']

            dates = test_data[1]
            actuals = test_data[2]

            # Handle case when predictions is NaN
            if isinstance(predictions, float) and pd.isna(predictions):
                predictions = np.nan 
            
            for date, actual in zip(dates, actuals):
                prediction = predictions if isinstance(predictions, float) and pd.isna(predictions) else predictions

            data.append([
                date,                
                row['space_id'],     
                row['deployment_id'],
                row['isin'],         
                row['job_id'],       
                row['status'],       
                row['tic'],        
                row['exchange'],     
                row['min_p'],        
                row['max_p'], 
                row['model_identifier'],       
                actual,              
                prediction           
            ])

            # for date, actual, prediction in zip(dates, actuals, predictions):
            #     data.append([date, row['space_id'], row['deployment_id'], row['isin'], row['job_id'], row['status'], row['tic'], row['exchange'], row['min_p'], row['max_p'], actual, prediction])

        final = pd.DataFrame(data, columns=['date', 'space_id', 'deployment_id', 'isin', 'job_id', 'status', 'tic', 'exchange', 'min_p', 'max_p', 'model_identifier', 'actuals', 'predictions'])
        print(f"final is {final}")

        # Format 'date' column from MM/DD/YYYY to YYYY-MM-DD
        final['date'] = final['date'].apply(lambda x: "-".join([x.split('/')[2], x.split('/')[0].zfill(2), x.split('/')[1].zfill(2)]))
        final = final[['date', 'space_id', 'deployment_id', 'isin', 'model_identifier', 'job_id', 'status', 'tic', 'exchange', 'min_p', 'max_p', 'actuals', 'predictions']]

        # Create closeprice column as duplicate of actuals for naming convention
        final.insert(final.columns.get_loc('actuals') + 1, 'closeprice', final['actuals'])

        return final, failed_pred_count
    
    # Creates the df for fetching close price parallelly
    def fetch_cp_data(self, row, today, one_month_prev, days):
        isin = row['isin']
        tic = row['tic']
        exchange = row['exchange']
        
        df = self.get_stock_price(isin, tic, exchange, one_month_prev, today, days)
        
        if df is not None and not df.empty:
            last_row = df.iloc[[-1]].copy()  # Get last row as DataFrame
            last_row['isin'] = isin
            return last_row  # Will be a 1-row DataFrame
        else:
            return pd.DataFrame(columns=['adj_close', 'date', 'isin'])

    # Gets sector to pred mapping
    @staticmethod
    def get_sector_preds(final_predictions, prediction_column, days):
        # Drop rows where prediction is NaN or not a list
        valid_df = final_predictions[final_predictions[prediction_column].apply(lambda x: isinstance(x, list) and len(x) == days)].copy()

        preds_expanded = pd.DataFrame(valid_df[prediction_column].to_list(), index=valid_df.index)
        preds_expanded['sector_code'] = valid_df['sector_code'].values
        sector_preds = preds_expanded.groupby('sector_code').mean()
        sector_preds = sector_preds.apply(lambda row: row.tolist(), axis=1).reset_index()
        sector_pred_map = dict(zip(sector_preds['sector_code'], sector_preds[0]))

        # Ensure all sector_codes from the final_predictions are included in the map with NaN if missing
        all_sector_codes = final_predictions['sector_code'].unique()
        for sector_code in all_sector_codes:
            if sector_code not in sector_pred_map:
                sector_pred_map[sector_code] = np.nan

        return sector_pred_map
    
    # Gets sector avg for isins with no model available
    def get_sector_avg(self, fin_df, tag_isin_df, missing_isin_list, days):
        today_dt = datetime.today() - BDay(1)
        yesterday_dt = today_dt - BDay(22)
        today = today_dt.strftime('%m/%d/%Y')
        one_month_prev = yesterday_dt.strftime('%m/%d/%Y')

        fin_df = fin_df.merge(tag_isin_df[['isin', 'ind_code']], on=['isin'], how='left')
        fin_df['sector_code'] = fin_df['ind_code'].astype(str).str[:2]

        filtered_tag_df = tag_isin_df[tag_isin_df['isin'].isin(missing_isin_list)]

        cp_data = []
        # for _, row in filtered_tag_df.iterrows():
        #     isin = row['isin']
        #     tic = row['tic']
        #     exchange = row['exchange']
            
        #     df_snp = self.get_capiq_data_as_of_date(isin, tic, exchange, today, one_month_prev, days)

        #     if not df_snp.empty:
        #         cp_data.append(df_snp)

        with concurrent.futures.ThreadPoolExecutor(max_workers=200) as executor:
            rows = [row for _, row in filtered_tag_df.iterrows()]
            results = executor.map(lambda row: self.fetch_cp_data(row, today, one_month_prev, days), rows)

        # Collect only non-empty results
        cp_data = [res for res in results if res is not None and not res.empty]

        cp_df = pd.concat(cp_data, ignore_index=True) if cp_data else pd.DataFrame(columns=['adj_close', 'date', 'isin'])
        merged_df = pd.merge(cp_df, tag_isin_df[['isin', 'tic', 'exchange', 'ind_code']], on='isin', how='left')
        merged_df['sector_code'] = merged_df['ind_code'].astype(str).str[:2]
        print(f"merged df upon creation is: {merged_df}")
        merged_df.rename(columns={'adj_close': 'actuals'}, inplace=True)
        merged_df['closeprice'] = merged_df['actuals']
        missing_cols = set(fin_df.columns) - set(merged_df.columns)
        for col in missing_cols:
            if col == 'model_identifier':
                merged_df[col] = config['parameters']['sector_identifier']
            else:
                merged_df[col] = np.nan
        
        merged_df = merged_df[fin_df.columns]
        print(f"merged df after adding fin df columns: {merged_df}")
        prediction_column = config['parameters']['pred_column_name']

        sector_pred_map = self.get_sector_preds(fin_df, prediction_column, days)
        print(f"sector pred map is: {sector_pred_map}")

        merged_df['predictions'] = merged_df['sector_code'].map(sector_pred_map)
        # merged_df['date'] = merged_df['date'].dt.strftime('%Y-%m-%d')
        merged_df['date'] = merged_df['date'].apply(lambda x: "-".join([x.split('/')[2], x.split('/')[0].zfill(2), x.split('/')[1].zfill(2)]))
        no_sector_pred_count = merged_df['predictions'].isna().sum()
        sector_pred_count = merged_df['predictions'].notna().sum()
        fin_df = pd.concat([fin_df, merged_df], ignore_index=True)
        print(f"fin df after concat is: {fin_df}")

        return fin_df, sector_pred_count, no_sector_pred_count
    
    # Saves the file to s3 versioning path isin wise
    def save_row(self, idx, row, bucket_name, filename):
       row_df = row.to_frame().T
       isin  = row_df['isin'].iloc[0]
       filename = filename.replace('isin',isin)                             
       self.s3_client.write_advanced_as_df(row_df, bucket_name, filename)
    
    # Uploads final df to ES and S3 and Versioning paths
    def upload_final_data(self, final_df, tag, days, year):
        comb_df = pd.DataFrame()
        uploaded_count = 0
        for d in final_df['date'].unique().tolist():
            df_new_copy = pd.DataFrame()
            if d>=saved_date:
                print(f"d is: {d}")
                df_new_copy = final_df[final_df['date'] == d].copy()
                print(f'df new copy is: {df_new_copy}')
    
                s3_upload_path, test_upload_path = self.get_upload_paths(tag, days, year)
                
                df_new_copy['schedular'] = 'Monthly' if days==7 else('Weekly' if days==3 else 'Daily')
                df_new_copy['updated_time'] = datetime.today()
                df_new_copy['updated_at'] = df_new_copy['updated_time']
                df_new_copy = df_new_copy.where(pd.notna(df_new_copy), None)
                uploaded_count += len(df_new_copy)

                if env == 'prod':
                    index = config['es_index']['lstm_index']
                    self.s3_client.write_advanced_as_df(df_new_copy, new_bucket, f"{s3_upload_path}/predictionresult_{d.replace('-', '_')}.csv")
                    if days==7:
                        rows = [(idx, row, versioning_bucket, versioning_filename_monthly.replace('date',str(saved_date))) for idx, row in df_new_copy.iterrows()]
                        with ThreadPoolExecutor() as executor:
                            executor.map(lambda args: self.save_row(*args), rows)
                    elif days==1:
                        rows = [(idx, row, versioning_bucket, versioning_filename_daily.replace('date',str(saved_date))) for idx, row in df_new_copy.iterrows()]
                        with ThreadPoolExecutor() as executor:
                            executor.map(lambda args: self.save_row(*args), rows)
                    else:
                        print("Invalid schedular")
                    self.es_client.save_records_v2(df_new_copy, index)
                elif env == 'preprod':
                    index = config['es_index']['preprod_lstm_index']
                    self.s3_client.write_advanced_as_df(df_new_copy, new_bucket, f"{test_upload_path}/predictionresult_{d.replace('-', '_')}.csv")
                    self.training_es_client.save_records_v2(df_new_copy, index)
                elif env == 'local':
                    index = config['es_index']['preprod_lstm_index']
                    print('Final dataframe Uploaded')
                    self.training_es_client.save_records_v2(df_new_copy, index)

                fil_df = pd.DataFrame(columns=final_df.columns)
                comb_df = pd.concat([comb_df, fil_df], ignore_index=True)

            else:
                fil_df = final_df[final_df['date'] == d]  # filters rows matching date 'd'
                comb_df = pd.concat([comb_df, fil_df], ignore_index=True)
                uploaded_count += 0

        return uploaded_count, comb_df


    # Get credentials for sending mail
    @staticmethod
    def get_credentials():
        if os.path.exists('./token.json'):
            cred = Credentials.from_authorized_user_file('./token.json', scopes)
            return cred
        else:
            return None
            
    # Create an attachment for mail using file path
    @staticmethod
    def attach_file(message, path : str):   
        # determine the data-type of the file
        mime_type, _ = mimetypes.guess_type(path)

        # error check
        if mime_type == None:
            mime_type= 'application/octet-stream'
            
        _type, subtype = mime_type.split('/')

        # read
        with open(path, 'rb') as f:
            data= f.read()

        # attach to message
        message.add_attachment(data,
                            maintype= _type,
                            subtype= subtype,
                            filename= path.split('/')[-1])
        
    
    def send_mail(self, subject: str, body: str, errorbool = False, attachments: Optional[List[str]] = None) -> None:
        sender_email = config['mail']['sender_email']

        if errorbool:
            recipients = config['mail']['receiver_emails_error']
        else:
            if env == 'prod':
                recipients = config['mail']['receiver_emails_prod']
            elif env == 'preprod':
                recipients = config['mail']['receiver_emails_preprod']
            elif env == 'local':
                recipients = config['mail']['receiver_emails_local']
            
        # error check
        if len(recipients) == 0:
            print('ERROR : There are no recipients')
            sys.exit(0)

        # GET the credentials to send the mail
        cred= self.get_credentials()
        
        # error-check
        if cred == None:
            pass

        # main
        try:
            # preprocessing: convert the list of receipients into a string
            if len(recipients) == 1:
                recipients= recipients[0]
            else:
                recipients= ', '.join(recipients)
            
            # create client
            service= build("gmail", "v1", credentials= cred)
            
            # create the mnssage
            message= EmailMessage()
            message['From']= sender_email
            message['To']= recipients
            message['Subject']= subject
            message.set_content(body)

            # attach files, if there are any
            if attachments != None:
                for path in attachments:
                    # error check
                    if not os.path.isfile(path):
                        print(f'ERROR: The file (to be attached) {path} does not exist.')
                        sys.exit(0)
                    self.attach_file(message, path)

            # encoded message
            encoded_message= base64.urlsafe_b64encode( message.as_bytes() ).decode()

            create_message= {'raw' : encoded_message}
            # pylint: disable= E1101
            send_message= (
                service.users()
                .messages()
                .send(userId= 'me',
                    body= create_message)
                .execute()
            )
            print(f"message id: {send_message['id']}")
            # logs.info(f"message id: {send_message['id']}")
        except HttpError as error:
            print(f'ERROR: Failed to send message. Details:\n{error}')
            logs.error(f'ERROR: Failed to send message. Details:\n{error}')         

    # Returns the final count of isins which have not been traded
    def get_traded_count(self):
        return self.not_traded_count
                
