from imports import *
from common_helper import HelperFuncs
from metrics_helper import MetricsHelperFuncs

commonconn = HelperFuncs()
metricsconn = MetricsHelperFuncs()

parser = argparse.ArgumentParser(prog='LSTM Metrics Script', description='Fetches tag, schedular and environment from cron job')
parser.add_argument('-t', '--tag')
parser.add_argument('-s', '--schedular')
parser.add_argument('-e', '--environment')
args = parser.parse_args()

schedular = args.schedular
tag = args.tag
env = args.environment

days = {'Monthly': 7, 'Daily': 1}.get(schedular, None)
print(f'tag is: {tag}, schedular is: {schedular}, days is: {days}')

script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.abspath(script_dir))

with open(f'{script_dir}/config.yaml', 'r') as file:
    config = yaml.safe_load(file)

curr_date = datetime.today().strftime("%Y-%m-%d")
saved_date = (pd.to_datetime(curr_date)-BDay(1)).strftime("%Y-%m-%d")

log_file_path = f"{script_dir}/{config['log_files'][f'{tag}_metrics_folder']}/{saved_date}_metrics_calculation.log"

global logs
logs = commonconn.create_logger(log_file_path)

year = config['parameters']['year']

old_bucket = config['s3_buckets']['lstm_bucket_old']
new_bucket = config['s3_buckets']['lstm_bucket_new']

def daily_metrics():
    tag_df = commonconn.get_firms_list(tag)
    tag_list = tag_df['isin'].values.tolist()
    try:
        pred_df, traded_isins = metricsconn.get_predictions_data(tag, days, saved_date)
    except Exception as e:
        logs.error(f"Error while fetching predictions file is: {e}")
        body4 = f"Prediction file unavailable, no isins traded"
        subject4 = f"Error in LSTM Model {tag} {schedular} Metrics run"
        # commonconn.send_mail(subject4, body4)
        commonconn.send_mail(subject4, body4, errorbool=True)

    if env == 'local':
        traded_isins = traded_isins[0:2]
    metrics_data_df, available_isins, missing_isins = metricsconn.fetch_metrics_data(traded_isins, schedular)

    if schedular == 'Monthly':
        metrics_df = metricsconn.prepare_monthly_inputs(metrics_data_df)
    elif schedular == 'Daily':
        metrics_df = metricsconn.prepare_daily_inputs(metrics_data_df)
    else:
        print("Invalid schedular")
        logs.error("Invalid schedular")

    final_df = metricsconn.calculate_metrics_with_process_pool(metrics_df, saved_date)

    return final_df, len(traded_isins), len(available_isins), len(missing_isins), len(final_df)

# Setting trigger mail
try:
    body1 = f"LSTM model metrics run on date {saved_date} has started"
    subject1 = f"LSTM Model {tag} {schedular} Metrics Run Started"
    # commonconn.send_mail(subject1, body1)
    commonconn.send_mail(subject1, body1, errorbool=True)
except Exception as e:
    logs.error(f"Error while sending trigger mail")

try:
    final_df, traded_count, data_available_count, data_missing_count, final_count = daily_metrics()
    print(f'{traded_count}, {data_available_count}, {data_missing_count}, {final_count}')
except Exception as e:
    logs.error(f"Error while running daily_metrics is: {e}")
    trace = traceback.format_exc()
    body2 = f'''Error while running daily_metrics is: {e}
    Traceback is: {trace}'''
    subject2 = f"Error in LSTM Model {tag} {schedular} Metrics run"
    # commonconn.send_mail(subject2, body2)
    commonconn.send_mail(subject2, body2, errorbool=True)
    sys.exit(0)

try:
    upload_msg = metricsconn.upload_final_data(final_df, tag, days, year)
    print(upload_msg)
except Exception as e:
    logs.error(f"Error while uploading final metrics data is: {e}")
    body3 = f"Error while uploading final metrics data is: {e}"
    subject3 = f"Error in LSTM Model {tag} {schedular} Metrics run"
    # commonconn.send_mail(subject3, body3)
    commonconn.send_mail(subject3, body3, errorbool=True)

# Mailing the daily metrics run statistics
try:
    body = f"""For date {saved_date}:
            Number of traded isins = {traded_count}
            Number of isins with data available in ES = {data_available_count}
            Number of isins with no data in ES = {data_missing_count}
            Number of isins with metrics successfully calculated = {final_count}
            Number of isins with missing metrics = {data_available_count - final_count}
    """
    subject = f'LSTM Model {tag} {schedular} Metrics Run Statistics'
    commonconn.send_mail(subject, body)
except Exception as e:
    logs.error(f"Error while sending stats mail is: {e}")
    print(f"Error while sending stats mail is: {e}")
    
    