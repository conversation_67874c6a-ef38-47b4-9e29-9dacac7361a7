#!/usr/bin/env python3
"""
Test script to verify Git-based shared_utils package functionality.
"""

def test_shared_utils_import():
    """Test shared_utils package import and functionality."""
    print("🧪 Testing shared_utils Git package...")
    
    try:
        # Test basic import
        print("Testing basic import...")
        import shared_utils
        print("✅ shared_utils imported successfully")
        
        # Test specific imports
        print("Testing specific imports...")
        from shared_utils import (
            load_config, create_model_logger, create_email_sender,
            create_s3_manager, create_error_handler, ErrorSeverity
        )
        print("✅ All main functions imported successfully")
        
        # Test error classes
        print("Testing error classes...")
        from shared_utils import (
            ModelError, ConfigurationError, DataError, EmailError, S3Error
        )
        print("✅ Error classes imported successfully")
        
        # Test utility functions
        print("Testing utility functions...")
        from shared_utils import safe_execute, ErrorContext
        print("✅ Utility functions imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        print("💡 Make sure shared_utils is installed from Git:")
        print("   pip install git+https://github.com/EqubotAI/DS_Utils.git")
        return False
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        return False


def test_model_scripts_import():
    """Test that model scripts can import shared_utils."""
    print("\n🧪 Testing model scripts imports...")
    
    test_cases = [
        ("best_model_scripts.main", "BestModelRunner"),
        ("best_model_scripts.helper_functions", "Helpers"),
        ("best_model_scripts.email_notification", "AnalysisNotification"),
        ("management_model_scripts.management_model_daily_run", "ManagementModelRunner"),
        ("fin_model_scripts.Financial_Model_Prediction_Script", "FinancialModelRunner"),
    ]
    
    passed = 0
    failed = 0
    
    for module_name, class_name in test_cases:
        try:
            print(f"Testing {module_name}...")
            module = __import__(module_name, fromlist=[class_name])
            if hasattr(module, class_name):
                print(f"✅ {module_name}.{class_name} importable")
                passed += 1
            else:
                print(f"⚠️  {module_name} imported but {class_name} not found")
                failed += 1
        except ImportError as e:
            print(f"⚠️  {module_name} import failed: {e}")
            failed += 1
        except Exception as e:
            print(f"❌ {module_name} test failed: {e}")
            failed += 1
    
    print(f"\n📊 Model Scripts Test Results: {passed} passed, {failed} failed")
    return failed == 0


def test_configuration_loading():
    """Test configuration loading functionality."""
    print("\n🧪 Testing configuration loading...")
    
    try:
        from shared_utils import load_config
        
        # Test with non-existent file (should handle gracefully)
        config = load_config('non_existent_config.yaml')
        print("✅ Configuration loading handles missing files gracefully")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_logger_creation():
    """Test logger creation functionality."""
    print("\n🧪 Testing logger creation...")
    
    try:
        from shared_utils import create_model_logger
        
        # Test logger creation
        logger = create_model_logger('test_model', 'test_tag', 'daily', '2024-01-15')
        print("✅ Model logger created successfully")
        
        # Test logging
        logger.info("Test log message")
        print("✅ Logger functionality working")
        
        return True
        
    except Exception as e:
        print(f"❌ Logger test failed: {e}")
        return False


def main():
    """Main test function."""
    print("🚀 Testing DS-Inference-Scripts with Git-based shared_utils")
    print("=" * 65)
    
    tests = [
        ("Shared Utils Import", test_shared_utils_import),
        ("Model Scripts Import", test_model_scripts_import),
        ("Configuration Loading", test_configuration_loading),
        ("Logger Creation", test_logger_creation),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*65}")
    print(f"📊 Final Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Git package integration is working correctly.")
        print("\n✅ Your DS-Inference-Scripts are ready to use with Git-based shared_utils!")
    else:
        print("⚠️  Some tests failed. Please check the installation:")
        print("1. Install shared_utils from Git:")
        print("   pip install git+https://github.com/EqubotAI/DS_Utils.git")
        print("2. Verify all dependencies are installed:")
        print("   pip install -r requirements.txt")
    
    return 0 if failed == 0 else 1


if __name__ == '__main__':
    exit_code = main()
    exit(exit_code)
