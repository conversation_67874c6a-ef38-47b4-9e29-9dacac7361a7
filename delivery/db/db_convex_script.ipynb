{"cells": [{"cell_type": "code", "execution_count": 325, "id": "84afd551-0982-41d3-bb5f-00374bf86494", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import requests as req\n", "from pandas import json_normalize\n", "import boto3\n", "import pandas as pd\n", "import numpy as np\n", "import requests\n", "from pandas import json_normalize\n", "import smtplib\n", "from email.mime.multipart import MIMEMultipart\n", "from email.mime.text import MIMEText\n", "from email.mime.image import MIMEImage\n", "from email.mime.application import MIMEApplication\n", "from functools import reduce\n", "from datetime import timedelta, date,datetime\n", "import traceback\n", "import xlrd\n", "import os\n", "from zipfile import ZipFile\n", "AWS_ACCESS_KEY_ID='********************', \n", "AWS_SECRET_ACCESS_KEY='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'\n", "from functools import reduce\n", "import matplotlib.pyplot as plt\n", "import os\n", "from tqdm import tqdm\n", "from datetime import datetime\n", "import cvxpy as cp\n", "import numpy as np\n", "import itertools\n", "from sklearn.experimental import enable_iterative_imputer\n", "#import seaborn as sns\n", "from sklearn.impute import IterativeImputer\n", "from sklearn.covariance import MinCovDet"]}, {"cell_type": "code", "execution_count": 326, "id": "bbd65870-cf08-467e-a219-6fbab4d67060", "metadata": {}, "outputs": [], "source": ["local_path = os.getcwd()+\"/convex/\""]}, {"cell_type": "code", "execution_count": 327, "id": "11b7e932-33de-4fa4-a0b1-f0eb632d49c3", "metadata": {}, "outputs": [], "source": ["def download_s3file(bucketName,folderName,filename):\n", "    s3_client = boto3.client('s3',aws_access_key_id='********************',\n", "         aws_secret_access_key='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/')\n", "    objs = s3_client.list_objects_v2(Bucket=bucketName, Prefix=folderName)['Contents']\n", "    filep = local_path+filename\n", "    print(folderName+filename)\n", "    s3_client.download_file(bucketName,folderName+filename , filep)\n", "def close_price(isin,sdate,edate,ccode):\n", "    try:\n", "        df1 = pd.DataFrame()\n", "        response = requests.get(\"http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin=\" + isin + \"&startDate=\" + sdate + \"&endDate=\" + edate + \"&countrycode=\" + ccode)\n", "        response_data = response.json()\n", "        check = bool(response_data['status'])\n", "        if check:\n", "            df1 = pd.DataFrame.from_dict(\n", "                json_normalize(response_data['data']['stocks'])[['date', 'close']]).iloc[::-1]\n", "            df1.rename(columns={'close': isin}, inplace=True)\n", "            return df1\n", "    except Exception as e:\n", "        print(e)"]}, {"cell_type": "code", "execution_count": 328, "id": "c4a21d39-6e5b-42a0-8711-f80d7e0f431c", "metadata": {}, "outputs": [], "source": ["# # download confidence file\n", "# bucketName = \"etf-predictions\"\n", "# #folderName = \"half_error_correction/daily/2025-02-27/\"\n", "# folderName = \"Monthly/db/client/\"\n", "# file_name = \"db_results_2025-04-30.csv\"\n", "# download_s3file(bucketName,folderName,file_name)\n", "# df = pd.read_csv(local_path+file_name,index_col=False) \n", "# df.rename(columns={'confidence_score':'avg_confidence_score','equity':'isin'},inplace=True)\n", "# df = df[['isin','avg_confidence_score']]\n", "# df.to_csv(local_path+\"DB_conf.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 331, "id": "af292091-4fdd-4522-a4ad-4130488bb8b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Monthly/best_pipeline/daily/2025-05-23/db_best_results_2025-05-23.csv\n"]}], "source": ["# download prediction file\n", "datee = '2025-05-23'\n", "bucketName = \"etf-predictions\"\n", "#folderName = \"half_error_correction/daily/2025-02-27/\"\n", "folderName = \"Monthly/best_pipeline/daily/\"+datee+\"/\"\n", "file_name = \"db_best_results_\"+datee+\".csv\"\n", "download_s3file(bucketName,folderName,file_name)\n", "df = pd.read_csv(local_path+file_name,index_col=False) \n", "df.rename(columns={'final_monthly_predictions':'ER'},inplace=True)\n", "df['er_abs'] = df['ER'].abs()\n", "df['ER_simulation'] = df['er_abs'] \n", "df.to_csv(local_path+\"DB_ER.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 332, "id": "41359cd9-8342-415d-a48c-5be2115d5a36", "metadata": {}, "outputs": [], "source": ["df_er = pd.read_csv(local_path+\"DB_ER.csv\",index_col=False)\n", "df_er.rename(columns={'etf':'isin'},inplace=True)\n", "# df_conf = pd.read_csv(local_path+\"DB_conf.csv\",index_col=False)\n", "# df_conf.rename(columns={'avg_confidence_score':'confidence_score'},inplace=True)\n", "# df_er = pd.merge(df_er,df_conf[['isin','confidence_score']],on='isin',how='left')\n", "df_er.to_csv(local_path+\"pre_optimization_file.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 334, "id": "1dc5f2e0-747a-4156-b6ab-36484e1b461b", "metadata": {}, "outputs": [], "source": ["# Covariance file\n", "start_date = \"2023-05-30\"\n", "end_date = \"2025-05-30\"\n", "date_list = pd.date_range(start=start_date,end=end_date,freq='B').strftime(\"%Y-%m-%d\").tolist()\n", "df_f = pd.DataFrame(date_list,columns=['date'])\n", "df = pd.read_csv(local_path+\"pre_optimization_file.csv\",index_col=False)\n", "ll = df['isin'].tolist()\n", "for i in ll:\n", "    isin = i\n", "    df1 = close_price(isin,start_date,end_date,'USA')\n", "    df_f = pd.merge(df_f,df1,on='date',how='left')\n", "df_f = df_f.ffill()\n", "df_f.to_csv(local_path+'close_price.csv',index=False)\n", "df_f = df_f.set_index('date')\n", "df_c = df_f.pct_change(periods=23)\n", "df_c = df_c.cov()\n", "df_c.to_csv(local_path+\"Covariance.csv\",index=False) "]}, {"cell_type": "code", "execution_count": 335, "id": "12183ff4-d0c3-4376-a6d8-4b08881db5c3", "metadata": {}, "outputs": [], "source": ["# Make cov\n", "def make_cov(close_df, h, df):\n", "    \"\"\"\n", "    Creates a robust covariance matrix using only ISINs that exist in both close prices and input DataFrame\n", "    \n", "    Parameters:\n", "    close_df (pd.DataFrame): DataFrame with close prices and 'date' column\n", "    h (str): Target date\n", "    df (pd.DataFrame): DataFrame containing ISINs to include in covariance matrix\n", "    \n", "    Returns:\n", "    pd.DataFrame: Robust covariance matrix\n", "    \"\"\"\n", "    # Convert target date to datetime\n", "    h_date = pd.to_datetime(h)\n", "    start_date = h_date - pd.DateOffset(years=2)\n", "    \n", "    # Convert and ensure date column is datetime\n", "    close_df = close_df.copy()\n", "    close_df['date'] = pd.to_datetime(close_df['date'], errors='coerce')\n", "    \n", "    # Get list of ISINs from input DataFrame\n", "    input_isins = set(df['isin'].unique())\n", "    close_isins = set(close_df.columns) - {'date'}\n", "    \n", "    # Find common ISINs\n", "    common_isins = list(input_isins.intersection(close_isins))\n", "    print(f\"Total ISINs in input: {len(input_isins)}\")\n", "    print(f\"Total ISINs in close: {len(close_isins)}\")\n", "    print(f\"Common ISINs: {len(common_isins)}\")\n", "    \n", "    if len(common_isins) == 0:\n", "        raise ValueError(\"No common ISINs found between input data and close prices\")\n", "    \n", "    # Filter close_df for date range and common ISINs\n", "    df_close = close_df[\n", "        (close_df['date'] >= start_date) & \n", "        (close_df['date'] <= h_date)\n", "    ][['date'] + common_isins].copy()\n", "    \n", "    # Set date as index\n", "    df_close.set_index('date', inplace=True)\n", "    \n", "    # # Handle missing data\n", "    # df_close.interpolate(method='linear', axis=0, inplace=True)\n", "    # df_close.to_csv(path + 'closerdf.csv')\n", "    # # Iterative imputation for remaining missing values\n", "    # iter_imp = IterativeImputer(skip_complete=True, n_nearest_features=min(15, len(common_isins)))\n", "    \n", "    # # Imputation\n", "    # imputed_data = iter_imp.fit_transform(df_close)\n", "    df_close = pd.DataFrame(df_close, columns=common_isins, index=df_close.index)\n", "    \n", "    # Calculate 22-day returns\n", "    df_returns = df_close.pct_change(periods=23)\n", "    #df_returns = df_returns.dropna()\n", "    \n", "    # if len(df_returns) < 2:\n", "    #     raise ValueError(\"Not enough data points after calculating returns\")\n", "    \n", "    # # Robust covariance estimation\n", "    # mcd = MinCovDet(random_state=42)\n", "    # mcd.fit(df_returns.values)\n", "    \n", "    # # Create covariance matrix DataFrame\n", "    # robust_cov = pd.DataFrame(\n", "    #     mcd.covariance_,\n", "    #     index=common_isins,\n", "    #     columns=common_isins\n", "    # )\n", "    robust_cov = df_returns.cov()\n", "    \n", "    return robust_cov"]}, {"cell_type": "code", "execution_count": 336, "id": "521db191-219c-48ca-826a-dc5ae02f171f", "metadata": {}, "outputs": [], "source": ["def transpose_df(df):\n", "    \"\"\"\n", "    Transpose a DataFrame where ISINs are columns and dates are rows\n", "    to have ISINs as rows and dates as columns.\n", "    \n", "    Parameters:\n", "    df (pd.DataFrame): Input DataFrame where columns are ISINs (except first column with dates)\n", "    \n", "    Returns:\n", "    pd.DataFrame: Transposed DataFrame with ISINs as rows and dates as columns\n", "    \"\"\"\n", "    # Get the date column name (assuming it's the first column)\n", "    date_col = df.columns[0]\n", "    \n", "    # Melt the DataFrame, using the date column as id_vars\n", "    df_melted = df.melt(id_vars=[date_col], \n", "                        var_name=\"isin\", \n", "                        value_name=\"value\")\n", "    \n", "    # Check for duplicates before pivoting\n", "    duplicates = df_melted.duplicated(subset=[date_col, 'isin'])\n", "    if duplicates.any():\n", "        print(f\"Found {duplicates.sum()} duplicate combinations of {date_col} and isin\")\n", "        # Keep the last occurrence of duplicates\n", "        df_melted = df_melted.drop_duplicates(subset=[date_col, 'isin'], keep='last')\n", "    \n", "    # Pivot the melted DataFrame\n", "    df_final = df_melted.pivot(index=\"isin\", \n", "                              columns=date_col, \n", "                              values=\"value\")\n", "    \n", "    # Reset index to make isin a regular column\n", "    df_final = df_final.reset_index()\n", "    \n", "    return df_final"]}, {"cell_type": "code", "execution_count": 337, "id": "8f61c687-b611-4cb0-a9d7-98fa2f1c8ff1", "metadata": {}, "outputs": [], "source": ["# Bunch Constraints\n", "\n", "def add_asset_constraints(df, w, constraints):\n", "    if 'asset_class_weight_min' in df.columns:\n", "        for asset_class in df['asset_class'].unique():\n", "            mask = df['asset_class'] == asset_class\n", "            class_min = df.loc[mask, 'asset_class_weight_min'].iloc[0]\n", "            class_max = df.loc[mask, 'asset_class_weight_max'].iloc[0]\n", "            \n", "            class_weights = w[mask.values]  # Convert mask to numpy array\n", "            constraints.append(cp.sum(class_weights) >= class_min)\n", "            constraints.append(cp.sum(class_weights) <= class_max)\n", "    return constraints\n", "\n", "def add_sector_constraints(df, w, constraints):\n", "    if 'sector_min' in df.columns:\n", "        for sector_name in df['sector_name'].unique():\n", "            mask = df['sector_name'] == sector_name\n", "            sector_min = df.loc[mask, 'sector_min'].iloc[0]\n", "            sector_max = df.loc[mask, 'sector_max'].iloc[0]\n", "            \n", "            sector_weights = w[mask.values]  # Convert mask to numpy array\n", "            constraints.append(cp.sum(sector_weights) >= sector_min)\n", "            constraints.append(cp.sum(sector_weights) <= sector_max)\n", "    return constraints\n", "\n", "def add_geo_constraints(df, w, constraints):\n", "    if 'exposure' in df.columns:\n", "        for geo in df['exposure'].unique():\n", "            mask = df['exposure'] == geo\n", "            geo_min = df.loc[mask, 'geo_min'].iloc[0]\n", "            geo_max = df.loc[mask, 'geo_max'].iloc[0]\n", "            \n", "            geo_weights = w[mask.values]  # Convert mask to numpy array\n", "            constraints.append(cp.sum(geo_weights) >= geo_min)\n", "            constraints.append(cp.sum(geo_weights) <= geo_max)\n", "    return constraints"]}, {"cell_type": "code", "execution_count": 338, "id": "3db9168a-b26b-40d7-aac5-2bd0587752fd", "metadata": {}, "outputs": [], "source": ["def find_optimal_alpha(input, cov):\n", "    \"\"\"\n", "    Find the optimal alpha that maximizes the Sharpe ratio (returns / risk).\n", "\n", "    Parameters:\n", "        input (pd.DataFrame): Contains column 'er' (expected returns).\n", "        cov (np.n<PERSON><PERSON>): Covariance matrix of asset returns.\n", "\n", "    Returns:\n", "        best_alpha (float): Optimal alpha value that maximizes the Sharpe ratio.\n", "    \"\"\"\n", "    ms = float('-inf')  # Track maximum Sharpe ratio\n", "    best_alpha = 1  # Initialize best alpha\n", "    alpha = 0.1  # Start with alpha = 0.1\n", "    E = input['er']  # Expected returns\n", "\n", "    while alpha <= 100:\n", "        # Optimize portfolio weights for the current alpha\n", "        w = lsms(input, cov, alpha,0,1000)\n", "        if hasattr(cov, 'value'):\n", "            cov_np = cov.value\n", "        else:\n", "            cov_np = np.array(cov)\n", "        #print(f'w is {w}')\n", "        returns = np.dot(w,input['er'])\n", "        risk = np.sqrt(w.T@cov_np@w)\n", "        sharpe = returns/risk\n", "        #print(f\"Alpha: {alpha}, <PERSON>: {sharpe}\")\n", "\n", "        # Update best alpha if current Sharpe is better\n", "        if sharpe > ms:\n", "            ms = sharpe\n", "            best_alpha = alpha\n", "\n", "        # Increase alpha by a factor of 1.4 (geometric progression)\n", "        alpha *= 1.6\n", "\n", "    return best_alpha"]}, {"cell_type": "code", "execution_count": 339, "id": "904e7e07-5c29-4d62-bc45-bf6e34d7e137", "metadata": {}, "outputs": [], "source": ["# Risk and Return function\n", "'''\n", "Finding the risk and return if the weights,cov and er are given.\n", "'''\n", "def calculate_risk(w,cov):\n", "    # input are the weights and the covariance risk\n", "    if hasattr(cov, 'value'):\n", "        cov_np = cov.value\n", "    else:\n", "        cov_np = np.array(cov)\n", "    return np.sqrt(w.T@cov_np@w)\n", "    \n", "def calculate_returns(w,er):\n", "    return np.dot(w,er)"]}, {"cell_type": "code", "execution_count": 340, "id": "91f272ad-ee10-40df-bc3d-cf4fb9248285", "metadata": {}, "outputs": [], "source": ["# wrap cov and reorder input\n", "'''\n", "1) Reorder the input file to make sure that it has the same order as cov\n", "2) cov wrap.\n", "'''\n", "def reorder_input(input,cov):\n", "    cov_list = cov.columns.to_list()\n", "    input = input.set_index('isin').loc[cov_list].reset_index()\n", "    return input\n", "def wrap_cov(cov):\n", "    P = cp.atoms.affine.wraps.psd_wrap(cov)\n", "    return P"]}, {"cell_type": "code", "execution_count": 341, "id": "8a6f84fb-fcb1-49a6-80af-1e251d783614", "metadata": {}, "outputs": [], "source": ["# Find Lambda\n", "'''\n", "To do binary search for finding the lambda value which gives the ena of around 80 percent. Change the target_ena for other results\n", "'''\n", "def get_weight(input,cov,method,alpha,lambda_reg,risk_limit,min_return):\n", "    if method == 'sharpe':\n", "        print(f'inside')\n", "        w = lsms(input,cov,alpha,lambda_reg,risk_limit)\n", "    elif method == 'max_returns':\n", "        w = lsmr(input,cov,lambda_reg,risk_limit)\n", "    else:\n", "        w = ls_min_risk(input,cov,lambda_reg,min_return)\n", "    return w\n", "\n", "def find_lambda(input,cov,method,alpha,risk_limit,min_return,target_ena, low, high, tolerance=0.05, max_iterations=100):\n", "    \"\"\"\n", "    Perform a binary search to find the lambda value that results in an effective number of assets (ena)\n", "    within 5% of the target_ena.\n", "\n", "    Parameters:\n", "        input: Input data for the max_returns function.\n", "        cov: Covariance matrix.\n", "        target_ena: Target effective number of assets.\n", "        low: Lower bound for lambda.\n", "        high: Upper bound for lambda.\n", "        tolerance: Tolerance for the difference between ena and target_ena (default is 5%).\n", "        max_iterations: Maximum number of iterations for the binary search.\n", "\n", "    Returns:\n", "        lambda: The lambda value that results in ena within the tolerance of target_ena.\n", "    \"\"\"\n", "    print(f\"method is {method} and type is {type(method)}\")\n", "    #print('target_ena :',target_ena)\n", "    iteration = 0\n", "    while iteration < max_iterations:\n", "        print(f'iteratino number {iteration}')\n", "        mid = low + (high - low) / 2  \n", "        if low >= high - 1:\n", "            break \n", "        #print(f'mid is {mid}')\n", "        w = get_weight(input,cov,method,alpha,mid,risk_limit,min_return)\n", "        #print(w)\n", "        ena = 1 / np.sum(w**2)\n", "        #print('-------ena-----',ena)\n", "        #print('target_ena :',ena/len(w),'lambda :',mid)\n", "        if abs(ena - target_ena) / target_ena <= tolerance:\n", "            print(f'found my guy: ena {ena} and target {target_ena}')\n", "            return mid\n", "\n", "        if ena < target_ena:\n", "            low = mid  \n", "        else:\n", "            high = mid\n", "\n", "        iteration += 1\n", "        print('lambda :',mid)\n", "    # If the loop ends without finding a suitable lambda, return the best estimate\n", "    return mid"]}, {"cell_type": "code", "execution_count": 342, "id": "1ff2004f-f40c-497c-8315-c5c2a7cd338e", "metadata": {}, "outputs": [], "source": ["def lsms(df, cov,alpha, lambda_reg, risk_limit):\n", "     \n", "    \"\"\"\n", "    Portfolio optimization with long-short positions using DCP-compliant formulation.\n", "    Separates weights into positive (long) and negative (short) components.\n", "    \n", "    # Parameters:\n", "        df: DataFrame containing 'er', 'min', 'max' and other constraint data\n", "        cov: Covariance matrix\n", "        lambda_reg: L2 regularization parameter\n", "        risk_limit: Maximum allowed portfolio risk\n", "        \n", "    \"\"\"\n", "\n", "\n", "    is_col1_all_negative = (df['er'] < 0).all()\n", "    if is_col1_all_negative:\n", "        df = df.sort_values(by=['er'],ascending=False)\n", "        df = df.reset_index()\n", "        val = df.iloc[2]['er']\n", "        val = abs(val) + 0.01\n", "        df['er'] = df['er'] + val  \n", "    n = len(df['er'])\n", "    er = df['er']\n", "    min_vector = df['min']\n", "    max_vector = df['max']\n", "    \n", "   # Define variables for long and short positions\n", "    w_long = cp.Variable(n, nonneg=True)  # Long positions (positive)\n", "    \n", "    w_short = cp.Variable(n, nonneg=True)  # Short positions (positive values)\n", "    \n", "    # Net position is long minus short\n", "    w = w_long - w_short\n", "    \n", "    #print(len(df),len(er_w))\n", "    \n", "    # Define objective with the penalty\n", "    returns = w.T @ er\n", "    risk = cp.quad_form(w, cov)\n", "    l2_reg = lambda_reg * (cp.sum_squares(w))\n", "    objective = cp.Maximize(returns - alpha * risk - l2_reg)\n", "   \n", "    \n", "    \n", "     # Define constraints\n", "    constraints = [\n", "        w >= min_vector,                      # Net position lower bounds\n", "        w <= max_vector,                      # Net position upper bounds\n", "        risk <= risk_limit,                   # Risk constraint\n", "\n", "        # Portfolio constraints\n", "        cp.sum(w_long) + cp.sum(w_short) == 1,  # Gross exposure = 1\n", "        #cp.sum(w_short) <= 0.75,                # Less than 75% short\n", "    ]\n", "    \n", "    if len(df[df['er'] > 0]) > 0:\n", "        constraints.append(cp.constraints.NonPos(w_short[er > 0]))  # No shorting when ER > 0  \n", "    if len(df[df['er'] < 0]) > 0:\n", "        constraints.append(cp.constraints.NonPos(w_long[er < 0]))   # No long when ER < 0  \n", "    \n", "    \n", "    # Solve the problem using <PERSON><PERSON> solver\n", "    problem = cp.Problem(objective, constraints)\n", "    problem.solve()\n", "    # print('slove done')\n", "    print(w_long.value)\n", "    print(w_short.value)\n", "    \n", "     # Get the optimized weights\n", "    if w_long.value is not None and w_short.value is not None:\n", "        long_weights = w_long.value\n", "        short_weights = w_short.value\n", "        net_weights = long_weights - short_weights\n", "        \n", "        # Calculate and print summary statistics\n", "        sum_long = np.sum(long_weights)\n", "        sum_short = np.sum(short_weights)\n", "        \n", "        # Check for simultaneous long and short positions\n", "        both_positions = np.logical_and(long_weights > 1e-6, short_weights > 1e-6)\n", "        num_both = np.sum(both_positions)\n", "        \n", "        # If there are still overlapping positions despite the penalty,\n", "        # you might want to post-process them\n", "        if num_both > 0:\n", "            print(\"Warning: Some positions are still both long and short despite the penalty.\")\n", "            print(\"Consider increasing the penalty factor or post-processing the results.\")\n", "\n", "        print(np.sum(np.abs(net_weights)))\n", "        return net_weights\n", "    else:\n", "        print(\"-------------------------------------------------------------Optimization failed.\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 343, "id": "a5d86d16-8551-4dff-afc1-bfc12c7b7e2c", "metadata": {}, "outputs": [], "source": ["def ls_min_risk(df, cov, lambda_reg, min_return):\n", "    \"\"\"\n", "    Portfolio optimization with long-short positions using DCP-compliant formulation.\n", "    Separates weights into positive (long) and negative (short) components.\n", "    \n", "    Parameters:\n", "        df: DataFrame containing 'er', 'min', 'max' and other constraint data\n", "        cov: Covariance matrix\n", "        lambda_reg: L2 regularization parameter\n", "        min_returns : min return\n", "    \"\"\"\n", "   \n", "    is_col1_all_negative = (df['er'] < 0).all()\n", "    if is_col1_all_negative:\n", "        df = df.sort_values(by=['er'],ascending=False)\n", "        df = df.reset_index()\n", "        val = df.iloc[2]['er']\n", "        val = abs(val) + 0.01\n", "        df['er'] = df['er'] + val  \n", "    n = len(df['er'])\n", "    er = df['er']\n", "    min_vector = df['min']\n", "    max_vector = df['max']\n", "\n", "    # Define variables for long and short positions\n", "    w_long = cp.Variable(n, nonneg=True)  # Long positions (positive)\n", "    \n", "    w_short = cp.Variable(n, nonneg=True)  # Short positions (positive values)\n", "    \n", "    # Net position is long minus short\n", "    w = w_long - w_short\n", "\n", "\n", "     # Define objective with the penalty\n", "    returns = w.T @ er\n", "    risk = cp.quad_form(w, cov)\n", "    l2_reg = lambda_reg * (cp.sum_squares(w))\n", "    objective = cp.Minimize(risk + l2_reg)\n", "    \n", "   \n", "    \n", "    # Define constraints\n", "    constraints = [\n", "        w >= min_vector,                      # Net position lower bounds\n", "        w <= max_vector,                      # Net position upper bounds\n", "        risk <= risk_limit,                   # Risk constraint\n", "\n", "        # Portfolio constraints\n", "        cp.sum(w_long) + cp.sum(w_short) == 1,  # Gross exposure = 1\n", "        #cp.sum(w_short) <= 0.75,                # Less than 75% short\n", "    ]\n", "    \n", "    if len(df[df['er'] > 0]) > 0:\n", "        constraints.append(cp.constraints.NonPos(w_short[er > 0]))  # No shorting when ER > 0  \n", "    if len(df[df['er'] < 0]) > 0:\n", "        constraints.append(cp.constraints.NonPos(w_long[er < 0]))   # No long when ER < 0  \n", "    \n", "    \n", "    # Solve the problem using <PERSON><PERSON> solver\n", "    problem = cp.Problem(objective, constraints)\n", "    problem.solve()\n", "    # print('slove done')\n", "    print(w_long.value)\n", "    print(w_short.value)\n", "\n", "    # Get the optimized weights\n", "    if w_long.value is not None and w_short.value is not None:\n", "        long_weights = w_long.value\n", "        short_weights = w_short.value\n", "        net_weights = long_weights - short_weights\n", "        \n", "        # Calculate and print summary statistics\n", "        sum_long = np.sum(long_weights)\n", "        sum_short = np.sum(short_weights)\n", "        \n", "        # Check for simultaneous long and short positions\n", "        both_positions = np.logical_and(long_weights > 1e-6, short_weights > 1e-6)\n", "        num_both = np.sum(both_positions)\n", "        \n", "        # If there are still overlapping positions despite the penalty,\n", "        # you might want to post-process them\n", "        if num_both > 0:\n", "            print(\"Warning: Some positions are still both long and short despite the penalty.\")\n", "            print(\"Consider increasing the penalty factor or post-processing the results.\")\n", "\n", "        print(np.sum(np.abs(net_weights)))\n", "        return net_weights\n", "    else:\n", "        print(\"-------------------------------------------------------------Optimization failed.\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 344, "id": "9af548f9-9f59-48bb-a866-ee4a89dca4f4", "metadata": {}, "outputs": [], "source": ["def lsmr(df, cov, lambda_reg, risk_limit):\n", "    \"\"\"\n", "    Portfolio optimization with long-short positions using DCP-compliant formulation.\n", "    - Gross exposure constrained to 1\n", "    - Less than 75% of portfolio can be shorted\n", "    - Heavily penalizes simultaneous long and short positions\n", "    - Uses Clarabel solver\n", "    \n", "    Parameters:\n", "        df: DataFrame containing 'er', 'min', 'max' and other constraint data\n", "        cov: Covariance matrix\n", "        lambda_reg: L2 regularization parameter\n", "        risk_limit: Maximum allowed portfolio risk\n", "    \"\"\"\n", "    is_col1_all_negative = (df['er'] < 0).all()\n", "    if is_col1_all_negative:\n", "        df = df.sort_values(by=['er'],ascending=False)\n", "        df = df.reset_index()\n", "        val = df.iloc[2]['er']\n", "        val = abs(val) + 0.01\n", "        df['er'] = df['er'] + val    \n", "    \n", "    n = len(df['er'])\n", "    er = df['er']\n", "    min_vector = df['min']\n", "    max_vector = df['max']\n", "    #print(er)\n", "    \n", "    # Define variables for long and short positions\n", "    w_long = cp.Variable(n, nonneg=True)  # Long positions (positive)\n", "    \n", "    w_short = cp.Variable(n, nonneg=True)  # Short positions (positive values)\n", "    \n", "    # Net position is long minus short\n", "    w = w_long - w_short\n", "    \n", "    #print(len(df),len(er_w))\n", "    \n", "    # Define objective with the penalty\n", "    returns = w.T @ er\n", "    risk = cp.quad_form(w, cov)\n", "    l2_reg = lambda_reg * (cp.sum_squares(w))\n", "    objective = cp.Maximize(returns - l2_reg)\n", "    \n", "    # Define constraints\n", "    constraints = [\n", "        w >= min_vector,                      # Net position lower bounds\n", "        w <= max_vector,                      # Net position upper bounds\n", "        risk <= risk_limit,                   # Risk constraint\n", "\n", "        # Portfolio constraints\n", "        cp.sum(w_long) + cp.sum(w_short) == 1,  # Gross exposure = 1\n", "        #cp.sum(w_short) <= 0.75,                # Less than 75% short\n", "    ]\n", "    \n", "    if len(df[df['er'] > 0]) > 0:\n", "        constraints.append(cp.constraints.NonPos(w_short[er > 0]))  # No shorting when ER > 0  \n", "    if len(df[df['er'] < 0]) > 0:\n", "        constraints.append(cp.constraints.NonPos(w_long[er < 0]))   # No long when ER < 0  \n", "    \n", "    \n", "    # Solve the problem using <PERSON><PERSON> solver\n", "    problem = cp.Problem(objective, constraints)\n", "    problem.solve()\n", "    # print('slove done')\n", "    #print(w_long.value)\n", "    #print(w_short.value)\n", "\n", "    # Get the optimized weights\n", "    if w_long.value is not None and w_short.value is not None:\n", "        long_weights = w_long.value\n", "        short_weights = w_short.value\n", "        net_weights = long_weights - short_weights\n", "        \n", "        # Calculate and print summary statistics\n", "        sum_long = np.sum(long_weights)\n", "        sum_short = np.sum(short_weights)\n", "        \n", "        # Check for simultaneous long and short positions\n", "        both_positions = np.logical_and(long_weights > 1e-6, short_weights > 1e-6)\n", "        num_both = np.sum(both_positions)\n", "        \n", "        # If there are still overlapping positions despite the penalty,\n", "        # you might want to post-process them\n", "        if num_both > 0:\n", "            print(\"Warning: Some positions are still both long and short despite the penalty.\")\n", "            print(\"Consider increasing the penalty factor or post-processing the results.\")\n", "\n", "        #print(np.sum(np.abs(net_weights)))\n", "        return net_weights\n", "    else:\n", "        print(\"-------------------------------------------------------------Optimization failed.\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 345, "id": "f79943b9-81c8-425c-9e98-a9dc2755773b", "metadata": {}, "outputs": [], "source": ["'''\n", "\n", "max returns long short.\n", "\n", "\n", "'''\n", "def max_er_long_short(output_folder,is_hard_constraint,bm_deviation,risk_limit,target_enan,er,bmw,min_l,max_l,min_return,close_df):\n", "    #output_path = local_path + '/' + output_folder + '/'\n", "    output_path = output_folder\n", "    target_ena = target_enan * len(er)\n", "    #target_ena = target_enan\n", "    # bm_deviation = 0.5\n", "    print(target_ena)\n", "    mis = []\n", "    tar = {}\n", "    \n", "    #for h in er.columns.tolist()[1:]:\n", "        # if h != '2025-04-14':\n", "        #    continue\n", "    #try:\n", "    # print(h)\n", "    # print('eh')\n", "    h = datetime.now().strftime('%Y-%m-%d')\n", "    df = pd.DataFrame(er[['isin','er']])\n", "    #df[h] = er[h]\n", "    #df.rename(columns={h: 'er'}, inplace=True)\n", "    #df = df.dropna().reset_index(drop=True)\n", "    #current_date = pd.to_datetime(h)\n", "    #df = df[df['er'] !=0]\n", "    #df['bmw_value'] = df['isin'].map(bmw.set_index('isin')[h]) / 100\n", "    cov = make_cov(close_df,h,df)\n", "\n", "    #cov = negate_cov(df,cov)\n", "    \n", "    P = wrap_cov(cov)\n", "    df = reorder_input(df,cov)   \n", "    \n", "    if is_hard_constraint:\n", "        df['min'] = min_l\n", "        df['max'] = max_l\n", "        #print(df)\n", "        tar[h] = target_ena\n", "        lambda_reg = find_lambda(df,P,'max_returns',0,risk_limit,min_return,target_ena,0,10000)\n", "        \n", "    else:\n", "        df['min'] = -1 * (1 + bm_deviation) * df['bmw_value']\n", "        df['max'] = (1 + bm_deviation) * df['bmw_value']\n", "        lambda_reg = 0\n", "        \n", "    #df = df[df.iloc[:, 1] != 0] #\n", "    #print(df)\n", "    \n", "    \n", "    # risk_limit = 10\n", "    # target_ena = 0.6 * len(df)\n", "    \n", "    print(lambda_reg)\n", "    \n", "    w = lsmr(df,P,lambda_reg,risk_limit)\n", "   \n", "    print('target_ena :',(1/np.sum(w**2))/11)\n", "    \n", "    #summary = get_positions_summary(w)\n", "    #print(\"\\nPosition Summary:\", summary)\n", "    \n", "    df['wt'] = w\n", "    df.to_csv(output_path + 'max_er_' + h + '.csv')\n", "   # \n", "    # except Exception as e:\n", "    #     mis.append(h)\n", "    #     print('-------------------------- invalid')\n", "    #     print(e)\n", "    # return mis "]}, {"cell_type": "code", "execution_count": 346, "id": "f88bfaf7-ba24-4c00-8af6-1f952ec6a141", "metadata": {}, "outputs": [], "source": ["'''\n", "\n", "max sharpe long short.\n", "\n", "\n", "'''\n", "def max_sharpe_long_short(output_folder,is_hard_constraint,bm_deviation,risk_limit,target_enan,min_return,er,bmw,min_l,max_l,close_df):\n", "    output_path = output_folder\n", "    target_ena = target_enan * len(er)\n", "    # bm_deviation = 0.5\n", "    #for h in er.columns.tolist()[1:]:\n", "        # if h != '2009-12-11':\n", "        #     continue\n", "    try:\n", "        h = datetime.now().strftime('%Y-%m-%d')\n", "        df = pd.DataFrame(er[['isin','er']])\n", "        # df[h] = er[h]\n", "        # df.rename(columns={h: 'er'}, inplace=True)\n", "        # df = df.dropna().reset_index(drop=True)\n", "        \n", "        # df = df[df['er'] !=0]\n", "        #current_date = pd.to_datetime(h)\n", "        #df['bmw_value'] = df['isin'].map(bmw.set_index('isin')[h]) / 100\n", "        \n", "        cov = make_cov(close_df,h,df)\n", "        P = wrap_cov(cov)\n", "        df = reorder_input(df,cov)  \n", "        if is_hard_constraint:\n", "            df['min'] = min_l\n", "            df['max'] = max_l\n", "            lambda_reg = find_lambda(df,P,'sharpe',0,risk_limit,min_return,target_ena,0,1000)\n", "            \n", "        else:\n", "            df['min'] = -1 * (1 + bm_deviation) * df['bmw_value']\n", "            df['max'] = (1 + bm_deviation) * df['bmw_value']\n", "            lambda_reg = 0\n", "            \n", "        df = df[df.iloc[:, 1] != 0] #\n", "        \n", "        #print(df)\n", "        # risk_limit = 10\n", "        # target_ena = 0.6 * len(df)\n", "        alpha = find_optimal_alpha(df,P)\n", "        w =  lsms(df,P,alpha, lambda_reg, risk_limit)\n", "\n", "        print(w,'w')\n", "        #summary = get_positions_summary(w)\n", "        #print(\"\\nPosition Summary:\", summary)\n", "        \n", "        df['wt'] = w\n", "        df.to_csv(output_path + 'sharpe_' + h + '.csv')\n", "    except Exception as e:\n", "        print(e)"]}, {"cell_type": "code", "execution_count": 347, "id": "7cf61de8-9c79-4c1a-be46-5931a39428ce", "metadata": {}, "outputs": [], "source": ["'''\n", "\n", "min risk long short.\n", "\n", "\n", "'''\n", "def min_risk_long_short(output_folder,is_hard_constraint,bm_deviation,risk_limit,target_enan,min_return,er,bmw,min_l,max_l,close_df):\n", "    output_path = output_folder\n", "    target_ena = target_enan * len(er)\n", "    # bm_deviation = 0.5\n", "    #for h in er.columns.tolist()[1:]:\n", "        # if h != '2009-12-11':\n", "        #     continue\n", "    try:\n", "        h = datetime.now().strftime('%Y-%m-%d')\n", "        df = pd.DataFrame(er[['isin','er']])\n", "        # df[h] = er[h]\n", "        # df.rename(columns={h: 'er'}, inplace=True)\n", "        # df = df.dropna().reset_index(drop=True)\n", "        # df = df[df['er'] !=0]\n", "        # current_date = pd.to_datetime(h)\n", "        #df['bmw_value'] = df['isin'].map(bmw.set_index('isin')[h]) / 100\n", "        \n", "        cov = make_cov(close_df,h,df)\n", "        P = wrap_cov(cov)\n", "        df = reorder_input(df,cov)  \n", "        if is_hard_constraint:\n", "            df['min'] = min_l\n", "            df['max'] = max_l\n", "            lambda_reg = find_lambda(df,P,'min_risk',0,risk_limit,min_return,target_ena,0,10000)\n", "            \n", "        else:\n", "            df['min'] = -1 * (1 + bm_deviation) * df['bmw_value']\n", "            df['max'] = (1 + bm_deviation) * df['bmw_value']\n", "            lambda_reg = 0\n", "            \n", "        #df = df[df.iloc[:, 1] != 0] #\n", "        \n", "        #print(df) \n", "        # risk_limit = 10\n", "        # target_ena = 0.6 * len(df)\n", "        #alpha = find_optimal_alpha(df,P)\n", "        \n", "        w = ls_min_risk(df,P,lambda_reg,min_return)\n", "       \n", "\n", "        print(w,'w')\n", "        #summary = get_positions_summary(w)\n", "        #print(\"\\nPosition Summary:\", summary)\n", "        \n", "        df['wt'] = w\n", "        df.to_csv(output_path + 'min_risk_' + h + '.csv')\n", "    except Exception as e:\n", "        print(e)"]}, {"cell_type": "code", "execution_count": 348, "id": "4a34a5ca-8994-49f8-a006-5c12f7264af1", "metadata": {}, "outputs": [], "source": ["ls = True # True is long short false is long only    \n", "bm_deviation = 0.5\n", "bmw = False\n", "target_enan = 0.5\n", "risk_limit = 10\n", "min_return = -10\n", "is_hard_constraint = True\n", "min_l = -0.9\n", "max_l = 0.9\n", "output_folder = local_path"]}, {"cell_type": "code", "execution_count": 349, "id": "c904d0a5-d2f0-43a6-a66b-214cde10c700", "metadata": {}, "outputs": [], "source": ["er = pd.read_csv(local_path+\"pre_optimization_file.csv\",index_col=False)\n", "er = er.rename(columns={'ER':'er'})\n", "close_df = pd.read_csv(local_path+\"close_price.csv\",index_col=False)"]}, {"cell_type": "code", "execution_count": 350, "id": "db4a5f57-cc54-4f7b-a57a-460eeb2b7e95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.5\n", "Total ISINs in input: 9\n", "Total ISINs in close: 9\n", "Common ISINs: 9\n", "method is max_returns and type is <class 'str'>\n", "iteratino number 0\n", "lambda : 5000.0\n", "iteratino number 1\n", "lambda : 2500.0\n", "iteratino number 2\n", "lambda : 1250.0\n", "iteratino number 3\n", "lambda : 625.0\n", "iteratino number 4\n", "lambda : 312.5\n", "iteratino number 5\n", "lambda : 156.25\n", "iteratino number 6\n", "lambda : 78.125\n", "iteratino number 7\n", "lambda : 39.0625\n", "iteratino number 8\n", "lambda : 19.53125\n", "iteratino number 9\n", "lambda : 9.765625\n", "iteratino number 10\n", "lambda : 4.8828125\n", "iteratino number 11\n", "found my guy: ena 4.344008076145043 and target 4.5\n", "7.32421875\n", "target_ena : 0.3949098251040948\n", "Total ISINs in input: 9\n", "Total ISINs in close: 9\n", "Common ISINs: 9\n", "method is min_risk and type is <class 'str'>\n", "iteratino number 0\n", "[0.         0.11111107 0.11111113 0.         0.11111102 0.11111109\n", " 0.11111117 0.         0.11111101]\n", "[0.11111114 0.         0.         0.11111117 0.         0.\n", " 0.         0.1111112  0.        ]\n", "1.000000000067982\n", "lambda : 5000.0\n", "iteratino number 1\n", "[0.         0.11111104 0.11111114 0.         0.11111094 0.11111107\n", " 0.11111122 0.         0.1111109 ]\n", "[0.11111116 0.         0.         0.11111123 0.         0.\n", " 0.         0.11111129 0.        ]\n", "1.000000000005594\n", "lambda : 2500.0\n", "iteratino number 2\n", "[0.         0.11111096 0.11111117 0.         0.11111076 0.11111104\n", " 0.11111133 0.         0.11111069]\n", "[0.11111121 0.         0.         0.11111136 0.         0.\n", " 0.         0.11111148 0.        ]\n", "1.000000000137788\n", "lambda : 1250.0\n", "iteratino number 3\n", "[0.         0.11111081 0.11111124 0.         0.11111041 0.11111096\n", " 0.11111155 0.         0.11111026]\n", "[0.11111131 0.         0.         0.1111116  0.         0.\n", " 0.         0.11111184 0.        ]\n", "1.000000000037003\n", "lambda : 625.0\n", "iteratino number 4\n", "[8.55275200e-12 1.11110509e-01 1.11111361e-01 8.39396865e-12\n", " 1.11109714e-01 1.11110816e-01 1.11111996e-01 8.08289577e-12\n", " 1.11109418e-01]\n", "[1.11111512e-01 8.79418989e-12 8.58683687e-12 1.11112098e-01\n", " 9.11462443e-12 8.84163188e-12 8.23496953e-12 1.11112576e-01\n", " 7.40626448e-12]\n", "0.9999999998478398\n", "lambda : 312.5\n", "iteratino number 5\n", "[1.20387744e-13 1.11109907e-01 1.11111610e-01 1.22038581e-13\n", " 1.11108316e-01 1.11110521e-01 1.11112882e-01 1.24166231e-13\n", " 1.11107724e-01]\n", "[1.11111913e-01 1.17590460e-13 1.20444497e-13 1.11113086e-01\n", " 1.10109546e-13 1.16307558e-13 1.23903912e-13 1.11114041e-01\n", " 8.16659546e-14]\n", "0.9999999999979238\n", "lambda : 156.25\n", "iteratino number 6\n", "[1.57467267e-13 1.11108703e-01 1.11112110e-01 1.62702515e-13\n", " 1.11105521e-01 1.11109931e-01 1.11114652e-01 1.71212766e-13\n", " 1.11104337e-01]\n", "[1.11112714e-01 1.49418403e-13 1.57574001e-13 1.11115060e-01\n", " 1.28625443e-13 1.45036416e-13 1.69558798e-13 1.11116971e-01\n", " 9.50248923e-14]\n", "0.9999999999973226\n", "lambda : 78.125\n", "iteratino number 7\n", "[1.84868436e-13 1.11106297e-01 1.11113108e-01 1.93540303e-13\n", " 1.11099932e-01 1.11108750e-01 1.11118193e-01 2.08475495e-13\n", " 1.11097565e-01]\n", "[1.11114317e-01 1.71950426e-13 1.85130388e-13 1.11119008e-01\n", " 1.37084214e-13 1.63905925e-13 2.05612222e-13 1.11122829e-01\n", " 1.02207407e-13]\n", "0.9999999999968907\n", "lambda : 39.0625\n", "iteratino number 8\n", "[1.83600049e-13 1.11101486e-01 1.11115105e-01 1.93380396e-13\n", " 1.11088758e-01 1.11106389e-01 1.11125274e-01 2.10137235e-13\n", " 1.11084025e-01]\n", "[1.11117521e-01 1.69204325e-13 1.84008978e-13 1.11126900e-01\n", " 1.29089610e-13 1.59638949e-13 2.07087755e-13 1.11134543e-01\n", " 9.75689769e-14]\n", "0.9999999999969256\n", "lambda : 19.53125\n", "iteratino number 9\n", "[1.56587261e-13 1.11091875e-01 1.11119096e-01 1.65464585e-13\n", " 1.11066421e-01 1.11101667e-01 1.11139432e-01 1.80536709e-13\n", " 1.11056960e-01]\n", "[1.11123924e-01 1.43589528e-13 1.57012896e-13 1.11142670e-01\n", " 1.06477122e-13 1.34578989e-13 1.77878006e-13 1.11157956e-01\n", " 8.11438442e-14]\n", "0.9999999999973872\n", "lambda : 9.765625\n", "iteratino number 10\n", "[1.24399949e-13 1.11072695e-01 1.11127069e-01 1.31826115e-13\n", " 1.11021795e-01 1.11092219e-01 1.11167734e-01 1.44670466e-13\n", " 1.11002896e-01]\n", "[1.11136712e-01 1.13610346e-13 1.24773955e-13 1.11174155e-01\n", " 8.23942157e-14 1.05887638e-13 1.42400651e-13 1.11204725e-01\n", " 6.29977583e-14]\n", "0.9999999999979308\n", "lambda : 4.8828125\n", "iteratino number 11\n", "[1.26039151e-13 1.11034507e-01 1.11142982e-01 1.33734315e-13\n", " 1.10932740e-01 1.11073315e-01 1.11224276e-01 1.47179635e-13\n", " 1.10895028e-01]\n", "[1.11162213e-01 1.14913098e-13 1.26448183e-13 1.11236903e-01\n", " 8.26487703e-14 1.06854315e-13 1.44814874e-13 1.11298036e-01\n", " 6.33857338e-14]\n", "0.9999999999979048\n", "lambda : 2.44140625\n", "iteratino number 12\n", "[1.60289181e-13 1.10958807e-01 1.11174673e-01 1.70258756e-13\n", " 1.10755403e-01 1.11035469e-01 1.11337121e-01 1.87929816e-13\n", " 1.10680331e-01]\n", "[1.11212921e-01 1.45935670e-13 1.60837130e-13 1.11361517e-01\n", " 1.04470587e-13 1.35485791e-13 1.84835094e-13 1.11483757e-01\n", " 8.02728750e-14]\n", "0.9999999999973357\n", "lambda : 1.220703125\n", "iteratino number 13\n", "[1.82809709e-13 1.10810081e-01 1.11237527e-01 1.94292101e-13\n", " 1.10403794e-01 1.10959633e-01 1.11561866e-01 2.14737974e-13\n", " 1.10255031e-01]\n", "[1.11313167e-01 1.66302817e-13 1.83453871e-13 1.11607262e-01\n", " 1.18844524e-13 1.54300650e-13 2.11179942e-13 1.11851638e-01\n", " 9.14267316e-14]\n", "0.9999999999969617\n", "lambda : 0.6103515625\n", "iteratino number 14\n", "[1.74879362e-13 1.10523043e-01 1.11361175e-01 1.85934058e-13\n", " 1.09712539e-01 1.10807411e-01 1.12007660e-01 2.05536217e-13\n", " 1.09420428e-01]\n", "[1.11509082e-01 1.58974561e-13 1.75494940e-13 1.12085171e-01\n", " 1.13526000e-13 1.47514567e-13 2.02103673e-13 1.12573491e-01\n", " 8.73777070e-14]\n", "0.9999999999970934\n", "[-0.11150908  0.11052304  0.11136117 -0.11208517  0.10971254  0.11080741\n", "  0.11200766 -0.11257349  0.10942043] w\n", "Total ISINs in input: 9\n", "Total ISINs in close: 9\n", "Common ISINs: 9\n", "method is sharpe and type is <class 'str'>\n", "iteratino number 0\n", "inside\n", "[1.52697196e-13 1.13094764e-01 1.09979001e-01 1.58258176e-13\n", " 1.09549017e-01 1.09546348e-01 1.09521790e-01 1.64045919e-13\n", " 1.12672413e-01]\n", "[1.10550407e-01 1.46715940e-13 1.52541583e-13 1.14285205e-01\n", " 1.28171384e-13 1.42041164e-13 1.62197372e-13 1.10801056e-01\n", " 9.52257666e-14]\n", "0.9999999999973935\n", "lambda : 500.0\n", "iteratino number 1\n", "inside\n", "[2.13433577e-13 1.15078416e-01 1.08846892e-01 2.25224600e-13\n", " 1.07986922e-01 1.07981585e-01 1.07932469e-01 2.39059714e-13\n", " 1.14233714e-01]\n", "[1.09989702e-01 2.01055194e-13 2.13223237e-13 1.17459298e-01\n", " 1.60873668e-13 1.90057519e-13 2.34947867e-13 1.10491001e-01\n", " 1.21300158e-13]\n", "0.9999999999963971\n", "lambda : 250.0\n", "iteratino number 2\n", "inside\n", "[2.71038819e-13 1.19045722e-01 1.06582672e-01 2.90542078e-13\n", " 1.04862733e-01 1.04852060e-01 1.04753826e-01 3.11049548e-13\n", " 1.17356317e-01]\n", "[1.08868293e-01 2.53500758e-13 2.70456752e-13 1.23807485e-01\n", " 1.89089303e-13 2.33886695e-13 3.03474848e-13 1.09870892e-01\n", " 1.45633538e-13]\n", "0.9999999999954544\n", "lambda : 125.0\n", "iteratino number 3\n", "inside\n", "[3.27575563e-15 1.26980332e-01 1.02054233e-01 3.62426209e-15\n", " 9.86143555e-02 9.85930084e-02 9.83965414e-02 3.78712980e-15\n", " 1.23601523e-01]\n", "[1.06625475e-01 3.13405877e-15 3.22398942e-15 1.36503859e-01\n", " 2.18284730e-15 2.72817779e-15 3.56010009e-15 1.08630672e-01\n", " 1.79078891e-15]\n", "0.999999999999945\n", "lambda : 62.5\n", "iteratino number 4\n", "inside\n", "[3.67462742e-15 1.42849554e-01 9.29973551e-02 4.36906159e-15\n", " 8.61175998e-02 8.60749057e-02 8.56819717e-02 4.25746131e-15\n", " 1.36091935e-01]\n", "[1.02139839e-01 3.86383864e-15 3.31109272e-15 1.61896607e-01\n", " 2.12174069e-15 2.53279187e-15 3.09588337e-15 1.06150234e-01\n", " 2.40687266e-15]\n", "0.9999999999999397\n", "lambda : 31.25\n", "iteratino number 5\n", "inside\n", "[9.64946768e-17 1.74587996e-01 7.48835990e-02 1.21863543e-16\n", " 6.11240885e-02 6.10387002e-02 6.02528322e-02 9.70954887e-17\n", " 1.61072758e-01]\n", "[9.31685667e-02 1.34061080e-16 3.83224488e-17 2.12682103e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 1.01189356e-01\n", " 1.26113530e-16]\n", "0.9999999999999987\n", "lambda : 15.625\n", "iteratino number 6\n", "inside\n", "[2.19729904e-14 2.38064878e-01 3.86560838e-02 2.23942648e-14\n", " 1.11370709e-02 1.09662948e-02 9.39456469e-03 2.22563211e-14\n", " 2.11034401e-01]\n", "[7.52260187e-02 2.24769961e-14 2.00915494e-14 3.14253090e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 9.12675980e-02\n", " 1.85017399e-14]\n", "0.9999999999997518\n", "found my guy: ena 4.634328705971125 and target 4.5\n", "[0.00000000e+00 9.99999999e-02 7.02027030e-12 0.00000000e+00\n", " 5.83714630e-12 6.04453323e-12 5.80936714e-12 0.00000000e+00\n", " 5.40788056e-11]\n", "[9.26757876e-12 0.00000000e+00 0.00000000e+00 9.00000000e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 1.11059216e-11\n", " 0.00000000e+00]\n", "1.0000000000000009\n", "[0.00000000e+00 9.99999999e-02 6.99214151e-12 0.00000000e+00\n", " 5.81366911e-12 6.02037018e-12 5.78612804e-12 0.00000000e+00\n", " 5.38706933e-11]\n", "[9.23037657e-12 0.00000000e+00 0.00000000e+00 9.00000000e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 1.10616566e-11\n", " 0.00000000e+00]\n", "1.000000000000001\n", "[0.00000000e+00 9.99999999e-02 6.94740226e-12 0.00000000e+00\n", " 5.77633556e-12 5.98194001e-12 5.74917180e-12 0.00000000e+00\n", " 5.35390786e-11]\n", "[9.17119402e-12 0.00000000e+00 0.00000000e+00 9.00000000e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 1.09912214e-11\n", " 0.00000000e+00]\n", "1.0000000000000007\n", "[0.00000000e+00 9.99999999e-02 6.87639312e-12 0.00000000e+00\n", " 5.71709583e-12 5.92094576e-12 5.69052745e-12 0.00000000e+00\n", " 5.30112727e-11]\n", "[9.07723626e-12 0.00000000e+00 0.00000000e+00 9.00000000e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 1.08793586e-11\n", " 0.00000000e+00]\n", "1.0000000000000007\n", "[0.00000000e+00 9.99999999e-02 6.76416419e-12 0.00000000e+00\n", " 5.62350565e-12 5.82454778e-12 5.59786901e-12 0.00000000e+00\n", " 5.21734776e-11]\n", "[8.92867906e-12 0.00000000e+00 0.00000000e+00 9.00000000e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 1.07023924e-11\n", " 0.00000000e+00]\n", "1.0000000000000002\n", "[0.00000000e+00 9.99999999e-02 6.58813368e-12 0.00000000e+00\n", " 5.47678166e-12 5.67334681e-12 5.45258363e-12 0.00000000e+00\n", " 5.08512466e-11]\n", "[8.69556928e-12 0.00000000e+00 0.00000000e+00 9.00000000e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 1.04245099e-11\n", " 0.00000000e+00]\n", "1.0000000000000004\n", "[0.00000000e+00 9.99999999e-02 6.31533161e-12 0.00000000e+00\n", " 5.24964042e-12 5.43904718e-12 5.22761048e-12 0.00000000e+00\n", " 4.87819284e-11]\n", "[8.33393494e-12 0.00000000e+00 0.00000000e+00 9.00000000e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 9.99278449e-12\n", " 0.00000000e+00]\n", "1.0000000000000004\n", "[0.00000000e+00 9.99999938e-02 5.89982264e-10 0.00000000e+00\n", " 4.90405411e-10 5.08215045e-10 4.88517092e-10 0.00000000e+00\n", " 4.55912488e-09]\n", "[7.78269332e-10 0.00000000e+00 0.00000000e+00 8.99999998e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 9.33366785e-10\n", " 0.00000000e+00]\n", "1.0000000000000622\n", "[0.00000000e+00 9.99999945e-02 5.28454603e-10 0.00000000e+00\n", " 4.39300751e-10 4.55353638e-10 4.37841349e-10 0.00000000e+00\n", " 4.07964604e-09]\n", "[6.96584357e-10 0.00000000e+00 0.00000000e+00 8.99999998e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 8.35500372e-10\n", " 0.00000000e+00]\n", "1.0000000000000535\n", "[0.00000000e+00 9.99999956e-02 4.41297043e-10 0.00000000e+00\n", " 3.66990058e-10 3.80420649e-10 3.66053453e-10 0.00000000e+00\n", " 3.39150188e-09]\n", "[5.80899865e-10 0.00000000e+00 0.00000000e+00 8.99999998e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 6.96635078e-10\n", " 0.00000000e+00]\n", "1.000000000000041\n", "[0.00000000e+00 9.99999969e-02 3.26529677e-10 0.00000000e+00\n", " 2.71784944e-10 2.81608433e-10 2.71371510e-10 0.00000000e+00\n", " 2.48255268e-09]\n", "[4.28926379e-10 0.00000000e+00 0.00000000e+00 8.99999999e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 5.14044108e-10\n", " 0.00000000e+00]\n", "1.0000000000000235\n", "[8.93125831e-17 9.99999981e-02 1.92968138e-10 8.22306367e-16\n", " 1.61214522e-10 1.66436577e-10 1.61000419e-10 2.82830199e-16\n", " 1.45355020e-09]\n", "[2.52663043e-10 0.00000000e+00 3.09281844e-17 8.99999999e-01\n", " 0.00000000e+00 0.00000000e+00 3.36202277e-16 3.01868226e-10\n", " 0.00000000e+00]\n", "0.9999999999999958\n", "[2.19060695e-15 9.99999991e-02 9.12497698e-11 3.05627138e-15\n", " 7.75462710e-11 7.90991281e-11 7.70899032e-11 2.32836567e-15\n", " 6.10903113e-10]\n", "[1.17924966e-10 2.01404516e-15 2.14804196e-15 9.00000000e-01\n", " 1.81922785e-15 1.99901099e-15 2.34026871e-15 1.38758885e-10\n", " 1.38541406e-15]\n", "0.9999999999999597\n", "[1.95242033e-16 1.00000000e-01 1.23130237e-11 3.87569427e-16\n", " 1.08879597e-11 1.09379128e-11 1.07817939e-11 2.09473611e-16\n", " 5.11514317e-11]\n", "[1.50643720e-11 1.74013177e-16 1.91151892e-16 9.00000000e-01\n", " 1.67821952e-16 1.78271608e-16 2.12156503e-16 1.69303708e-11\n", " 1.31338623e-16]\n", "0.999999999999996\n", "[0.00000000e+00 9.99999996e-02 7.41780447e-11 0.00000000e+00\n", " 6.78346433e-11 6.64915782e-11 6.63001304e-11 0.00000000e+00\n", " 6.57709938e-10]\n", "[8.82720694e-11 0.00000000e+00 0.00000000e+00 8.99999999e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 9.06519997e-11\n", " 0.00000000e+00]\n", "1.000000000000026\n", "[1.87699072e-14 2.36518558e-01 4.02888404e-02 1.91221513e-14\n", " 9.18173696e-03 1.16171250e-02 1.20769025e-02 1.90540124e-14\n", " 2.03717644e-01]\n", "[7.66728173e-02 1.92019069e-14 1.72076614e-14 3.15349746e-01\n", " 0.00000000e+00 0.00000000e+00 0.00000000e+00 9.45766302e-02\n", " 1.58022443e-14]\n", "0.999999999999789\n", "[-0.07667282  0.23651856  0.04028884 -0.31534975  0.00918174  0.01161712\n", "  0.0120769  -0.09457663  0.20371764] w\n"]}], "source": ["max_er_long_short(output_folder,is_hard_constraint,bm_deviation,risk_limit,target_enan,er,bmw,min_l,max_l,min_return,close_df)\n", "min_risk_long_short(output_folder,is_hard_constraint,bm_deviation,risk_limit,target_enan,min_return,er,bmw,min_l,max_l,close_df)\n", "max_sharpe_long_short(output_folder,is_hard_constraint,bm_deviation,risk_limit,target_enan,min_return,er,bmw,min_l,max_l,close_df)"]}, {"cell_type": "code", "execution_count": 351, "id": "3101b432-2e9b-48a4-a4b3-a95865773efd", "metadata": {}, "outputs": [], "source": ["df_max_er = pd.read_csv(local_path+\"max_er_2025-05-27.csv\",index_col=False)[['isin','wt']].rename(columns={'wt':'max_ER'})\n", "df_sharpe = pd.read_csv(local_path+\"sharpe_2025-05-27.csv\",index_col=False)[['isin','wt']].rename(columns={'wt':'max_sharpe'})\n", "df_min_risk = pd.read_csv(local_path+\"min_risk_2025-05-27.csv\",index_col=False)[['isin','wt']].rename(columns={'wt':'min_risk'})\n", "df_er = pd.read_csv(local_path+\"pre_optimization_file.csv\",index_col=False)\n", "df_er = pd.merge(df_er,df_max_er,on='isin',how='left')\n", "df_er = pd.merge(df_er,df_sharpe,on='isin',how='left')\n", "df_er = pd.merge(df_er,df_min_risk,on='isin',how='left')\n", "df_er = df_er.drop(columns=['er_abs','ER_simulation'])\n", "df_er['max_ER'] = df_er['max_ER'].abs()\n", "df_er['max_sharpe'] = df_er['max_sharpe'].abs()\n", "df_er['min_risk'] = df_er['min_risk'].abs()\n", "\n", "df_er.to_csv(local_path+\"optimized_file.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 352, "id": "d0db5e6b-ba47-413e-ac0e-544db2c53ed1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>isin</th>\n", "      <th>ER</th>\n", "      <th>confidence_score</th>\n", "      <th>max_ER</th>\n", "      <th>max_sharpe</th>\n", "      <th>min_risk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBEEEMGF</td>\n", "      <td>-1.572276</td>\n", "      <td>0.646495</td>\n", "      <td>0.089945</td>\n", "      <td>0.094577</td>\n", "      <td>0.112573</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBEETGFU</td>\n", "      <td>-5.056425</td>\n", "      <td>0.650241</td>\n", "      <td>0.327796</td>\n", "      <td>0.315350</td>\n", "      <td>0.112085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBEEURGF</td>\n", "      <td>0.320237</td>\n", "      <td>0.645914</td>\n", "      <td>0.004472</td>\n", "      <td>0.009182</td>\n", "      <td>0.109713</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBEEUNGF</td>\n", "      <td>3.865984</td>\n", "      <td>0.624958</td>\n", "      <td>0.246528</td>\n", "      <td>0.236519</td>\n", "      <td>0.110523</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBEEUGFT</td>\n", "      <td>3.443633</td>\n", "      <td>0.631416</td>\n", "      <td>0.217696</td>\n", "      <td>0.203718</td>\n", "      <td>0.109420</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBRCOYGC</td>\n", "      <td>-1.321627</td>\n", "      <td>0.660225</td>\n", "      <td>0.072834</td>\n", "      <td>0.076673</td>\n", "      <td>0.111509</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBDRUS10</td>\n", "      <td>0.750221</td>\n", "      <td>0.613057</td>\n", "      <td>0.033826</td>\n", "      <td>0.040289</td>\n", "      <td>0.111361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBDRUS02</td>\n", "      <td>0.293010</td>\n", "      <td>0.607711</td>\n", "      <td>0.002614</td>\n", "      <td>0.012077</td>\n", "      <td>0.112008</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-05-23</td>\n", "      <td>DBDRUS20</td>\n", "      <td>0.317568</td>\n", "      <td>0.658457</td>\n", "      <td>0.004290</td>\n", "      <td>0.011617</td>\n", "      <td>0.110807</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date      isin        ER  confidence_score    max_ER  max_sharpe  \\\n", "0  2025-05-23  DBEEEMGF -1.572276          0.646495  0.089945    0.094577   \n", "1  2025-05-23  DBEETGFU -5.056425          0.650241  0.327796    0.315350   \n", "2  2025-05-23  DBEEURGF  0.320237          0.645914  0.004472    0.009182   \n", "3  2025-05-23  DBEEUNGF  3.865984          0.624958  0.246528    0.236519   \n", "4  2025-05-23  DBEEUGFT  3.443633          0.631416  0.217696    0.203718   \n", "5  2025-05-23  DBRCOYGC -1.321627          0.660225  0.072834    0.076673   \n", "6  2025-05-23  DBDRUS10  0.750221          0.613057  0.033826    0.040289   \n", "7  2025-05-23  DBDRUS02  0.293010          0.607711  0.002614    0.012077   \n", "8  2025-05-23  DBDRUS20  0.317568          0.658457  0.004290    0.011617   \n", "\n", "   min_risk  \n", "0  0.112573  \n", "1  0.112085  \n", "2  0.109713  \n", "3  0.110523  \n", "4  0.109420  \n", "5  0.111509  \n", "6  0.111361  \n", "7  0.112008  \n", "8  0.110807  "]}, "execution_count": 352, "metadata": {}, "output_type": "execute_result"}], "source": ["df_er"]}, {"cell_type": "code", "execution_count": 316, "id": "72a7d629-5ff4-4f41-80b0-a2ec2cb323d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9999999999999964\n", "0.9999999999999953\n", "0.9999999999970326\n"]}], "source": ["df = pd.read_csv(local_path+\"optimized_file.csv\",index_col=False)\n", "print(df['max_ER'].sum())\n", "print(df['max_sharpe'].sum())\n", "print(df['min_risk'].sum())"]}, {"cell_type": "code", "execution_count": null, "id": "80e4ca70-275e-4215-bb6c-c2683345f17a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 317, "id": "863389c8-a175-4951-9848-d3970f67db51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n", "1.0\n", "1.0\n"]}], "source": ["df['max_ER'] = df['max_ER'] / df['max_ER'].sum()\n", "df['max_sharpe'] = df['max_sharpe'] / df['max_sharpe'].sum()\n", "df['min_risk'] = df['min_risk'] / df['min_risk'].sum()\n", "print(df['max_ER'].sum())\n", "print(df['max_sharpe'].sum())\n", "print(df['min_risk'].sum())"]}, {"cell_type": "code", "execution_count": 318, "id": "9ba4e889-b012-49da-a1ae-84c6eaec87ae", "metadata": {}, "outputs": [], "source": ["df = df.round({\"max_ER\":4,'max_sharpe':4,'min_risk':4})\n", "df.at[0,'max_ER'] =df.iloc[0]['max_ER'] + (1-df['max_ER'].sum())\n", "df.at[0,'max_sharpe'] =df.iloc[0]['max_sharpe'] + (1-df['max_sharpe'].sum())\n", "df.at[0,'min_risk'] =df.iloc[0]['min_risk'] + (1-df['min_risk'].sum())\n", "df = df.round({\"max_ER\":4,'max_sharpe':4,'min_risk':4})\n", "for i in range(len(df)):\n", "    er_v = df.iloc[i]['ER']\n", "    max_e = df.iloc[i]['max_ER']\n", "    max_s = df.iloc[i]['max_sharpe']\n", "    max_r = df.iloc[i]['min_risk']\n", "    if er_v < 0:\n", "        max_e = max_e * -1\n", "        max_s = max_s * -1\n", "        max_r = max_r * -1\n", "        df.at[i,'max_ER'] = max_e\n", "        df.at[i,'max_sharpe'] = max_s\n", "        df.at[i,'min_risk'] = max_r"]}, {"cell_type": "code", "execution_count": 319, "id": "a4ed4ddf-5f98-46ad-93e2-d215116504e3", "metadata": {}, "outputs": [], "source": ["df.to_csv(local_path+\"optimized_deutche_bank_convex_target_ena_0.5_02-05-2025.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 320, "id": "594cc87b-5553-4242-a5c2-f48152e02312", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>isin</th>\n", "      <th>ER</th>\n", "      <th>confidence_score</th>\n", "      <th>max_ER</th>\n", "      <th>max_sharpe</th>\n", "      <th>min_risk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBEEEMGF</td>\n", "      <td>1.062544</td>\n", "      <td>0.751890</td>\n", "      <td>0.0576</td>\n", "      <td>0.0554</td>\n", "      <td>0.1111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBEETGFU</td>\n", "      <td>-2.415148</td>\n", "      <td>0.768011</td>\n", "      <td>-0.1130</td>\n", "      <td>-0.1137</td>\n", "      <td>-0.1115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBEEURGF</td>\n", "      <td>0.626219</td>\n", "      <td>0.643972</td>\n", "      <td>0.0397</td>\n", "      <td>0.0370</td>\n", "      <td>0.1104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBEEUNGF</td>\n", "      <td>-0.974747</td>\n", "      <td>0.654952</td>\n", "      <td>-0.0540</td>\n", "      <td>-0.0533</td>\n", "      <td>-0.1123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBEEUGFT</td>\n", "      <td>3.319293</td>\n", "      <td>0.659794</td>\n", "      <td>0.1500</td>\n", "      <td>0.1493</td>\n", "      <td>0.1101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBRCOYGC</td>\n", "      <td>9.641449</td>\n", "      <td>0.621238</td>\n", "      <td>0.4090</td>\n", "      <td>0.4194</td>\n", "      <td>0.1111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBDRUS10</td>\n", "      <td>1.041350</td>\n", "      <td>0.915170</td>\n", "      <td>0.0567</td>\n", "      <td>0.0549</td>\n", "      <td>0.1111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBDRUS02</td>\n", "      <td>0.324443</td>\n", "      <td>0.886696</td>\n", "      <td>0.0274</td>\n", "      <td>0.0256</td>\n", "      <td>0.1119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-04-30</td>\n", "      <td>DBDRUS20</td>\n", "      <td>1.917025</td>\n", "      <td>0.900370</td>\n", "      <td>0.0926</td>\n", "      <td>0.0914</td>\n", "      <td>0.1105</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date      isin        ER  confidence_score  max_ER  max_sharpe  \\\n", "0  2025-04-30  DBEEEMGF  1.062544          0.751890  0.0576      0.0554   \n", "1  2025-04-30  DBEETGFU -2.415148          0.768011 -0.1130     -0.1137   \n", "2  2025-04-30  DBEEURGF  0.626219          0.643972  0.0397      0.0370   \n", "3  2025-04-30  DBEEUNGF -0.974747          0.654952 -0.0540     -0.0533   \n", "4  2025-04-30  DBEEUGFT  3.319293          0.659794  0.1500      0.1493   \n", "5  2025-04-30  DBRCOYGC  9.641449          0.621238  0.4090      0.4194   \n", "6  2025-04-30  DBDRUS10  1.041350          0.915170  0.0567      0.0549   \n", "7  2025-04-30  DBDRUS02  0.324443          0.886696  0.0274      0.0256   \n", "8  2025-04-30  DBDRUS20  1.917025          0.900370  0.0926      0.0914   \n", "\n", "   min_risk  \n", "0    0.1111  \n", "1   -0.1115  \n", "2    0.1104  \n", "3   -0.1123  \n", "4    0.1101  \n", "5    0.1111  \n", "6    0.1111  \n", "7    0.1119  \n", "8    0.1105  "]}, "execution_count": 320, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "1be268ab-52bd-45e0-96aa-c30b062808ae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}