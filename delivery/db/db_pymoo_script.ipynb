{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import requests as req\n", "from pandas import json_normalize\n", "import boto3\n", "import pandas as pd\n", "import numpy as np\n", "import requests\n", "from pandas import json_normalize\n", "import smtplib\n", "from email.mime.multipart import MIMEMultipart\n", "from email.mime.text import MIMEText\n", "from email.mime.image import MIMEImage\n", "from email.mime.application import MIMEApplication\n", "from functools import reduce\n", "from datetime import timedelta, date,datetime\n", "import traceback\n", "import xlrd\n", "import os\n", "from zipfile import ZipFile\n", "AWS_ACCESS_KEY_ID='********************', \n", "AWS_SECRET_ACCESS_KEY='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'\n", "from functools import reduce\n", "import matplotlib.pyplot as plt\n", "import os\n", "from tqdm import tqdm\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/home/<USER>/abhishek/delivery/db/25-06/pymoo/'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["month = '25-06'\n", "fin_path = os.getcwd()+ '/' + month + \"/pymoo/\"\n", "fin_path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def download_s3file(bucketName,folderName,filename):\n", "    s3_client = boto3.client('s3',aws_access_key_id='********************',\n", "         aws_secret_access_key='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/')\n", "    # objs = s3_client.list_objects_v2(Bucket=bucketName, Prefix=folderName)['Contents']\n", "    filep = fin_path+filename\n", "    print(folderName+filename)\n", "    s3_client.download_file(bucketName,folderName+filename , filep)\n", "def close_price(isin,sdate,edate,ccode):\n", "    try:\n", "        df1 = pd.DataFrame()\n", "        print(f'fetching the closes')\n", "        print(f'isin: {isin}, sdate: {sdate}, edate: {edate}, ccode: {ccode}')\n", "        response = requests.get(\"http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin=\" + isin + \"&startDate=\" + sdate + \"&endDate=\" + edate + \"&countrycode=\" + ccode)\n", "        response_data = response.json()\n", "        check = bool(response_data['status'])\n", "        print(response)\n", "        if check:\n", "            print(f'inside the check')\n", "            df1 = pd.DataFrame.from_dict(\n", "                json_normalize(response_data['data']['stocks'])[['date', 'close']]).iloc[::-1]\n", "            df1.rename(columns={'close': isin}, inplace=True)\n", "            return df1\n", "    except Exception as e:\n", "        print(e)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["##download_confidence score\n", "# file_list = ['DBDRUS02_metrics.csv','DBDRUS10_metrics.csv','DBDRUS20_metrics.csv','DBEEEMGF_metrics.csv','DBEETGFU_metrics.csv',\n", "#              'DBEEUGFT_metrics.csv','DBEEUNGF_metrics.csv','DBEEURGF_metrics.csv','DBRCOYGC_metrics.csv']\n", "# bucketName = \"etf-predictions\"\n", "# folderName = \"Monthly/db/metrics/\"\n", "# #folderName = \"half_error_correction/daily/2024-02-28/metrics/\"\n", "# df = pd.DataFrame()\n", "# for i in file_list:\n", "#     download_s3file(bucketName,folderName,i)\n", "#     df1 = pd.read_csv(fin_path+i,index_col=False)[['date','avg_confidence_score']]\n", "#     df1['isin'] = i.replace(\"_metrics.csv\",\"\")\n", "#     df1 = df1.iloc[[len(df1)-1]]\n", "#     print(df1)\n", "#     df = pd.concat([df,df1],ignore_index=True)\n", "#     os.remove(fin_path+i)\n", "# df.to_csv(fin_path+\"DB_conf.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# # download confidence file\n", "# bucketName = \"etf-predictions\"\n", "# #folderName = \"half_error_correction/daily/2025-02-27/\"\n", "# folderName = \"Monthly/db/client/\"\n", "# file_name = \"db_results_2025-04-30.csv\"\n", "# download_s3file(bucketName,folderName,file_name)\n", "# df = pd.read_csv(fin_path+file_name,index_col=False) \n", "# df.rename(columns={'confidence_score':'avg_confidence_score','equity':'isin'},inplace=True)\n", "# df = df[['isin','avg_confidence_score']]\n", "# df.to_csv(fin_path+\"DB_conf.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def convert_date_to_string(date):\n", "    if isinstance(date, datetime):\n", "        return date.strftime('%Y-%m-%d')\n", "    elif isinstance(date, date):\n", "        return date.strftime('%Y-%m-%d')\n", "    elif isinstance(date,str):\n", "        return date\n", "    else:\n", "        raise ValueError(\"Input must be a datetime, date, or string in 'YYYY-MM-DD' format.\")\n", "\n", "def convert_string_to_date(date_str):\n", "    if isinstance(date_str, str):\n", "        return datetime.strptime(date_str, '%Y-%m-%d').date()\n", "    else:\n", "        # if any other format we make it as Y-m-d\n", "        return datetime.strptime(date_str, '%Y-%m-%d').date()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Monthly/best_pipeline/daily/2025-06-02/db_best_results_2025-06-02.csv\n"]}], "source": ["# download prediction file\n", "date = '2025-06-02'\n", "bucketName = \"etf-predictions\"\n", "#folderName = \"half_error_correction/daily/2025-02-27/\"\n", "folderName = \"Monthly/best_pipeline/daily/\"+date+\"/\"\n", "file_name = \"db_best_results_\"+date+\".csv\"\n", "download_s3file(bucketName,folderName,file_name)\n", "df = pd.read_csv(fin_path+file_name,index_col=False) \n", "df.rename(columns={'final_monthly_predictions':'ER'},inplace=True)\n", "df['er_abs'] = df['ER'].abs()\n", "df['ER_simulation'] = df['er_abs'] \n", "df.to_csv(fin_path+\"DB_ER.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["df_er = pd.read_csv(fin_path+\"DB_ER.csv\",index_col=False)\n", "df_er.rename(columns={'etf':'isin'},inplace=True)\n", "# df_conf = pd.read_csv(local_path+\"DB_conf.csv\",index_col=False)\n", "# df_conf.rename(columns={'avg_confidence_score':'confidence_score'},inplace=True)\n", "# df_er = pd.merge(df_er,df_conf[['isin','confidence_score']],on='isin',how='left')\n", "df_er.to_csv(fin_path+\"pre_optimization_file.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fetching the closes\n", "isin: DBEEEMGF, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBEEEMGF\n", "489  2023-06-05    214.91\n", "488  2023-06-06    216.05\n", "487  2023-06-07    217.38\n", "486  2023-06-08    217.32\n", "485  2023-06-09    219.27\n", "..          ...       ...\n", "4    2025-05-27    238.70\n", "3    2025-05-28    237.82\n", "2    2025-05-29    238.37\n", "1    2025-05-30    235.58\n", "0    2025-06-02    236.11\n", "\n", "[490 rows x 2 columns]\n", "fetching the closes\n", "isin: DBEETGFU, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBEETGFU\n", "473  2023-06-05    163.74\n", "472  2023-06-06    164.98\n", "471  2023-06-07    163.14\n", "470  2023-06-08    161.69\n", "469  2023-06-09    164.25\n", "..          ...       ...\n", "4    2025-05-27    210.63\n", "3    2025-05-28    211.36\n", "2    2025-05-29    214.13\n", "1    2025-05-30    213.75\n", "0    2025-06-02    211.16\n", "\n", "[474 rows x 2 columns]\n", "fetching the closes\n", "isin: DBEEURGF, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBEEURGF\n", "484  2023-06-05    452.27\n", "483  2023-06-06    464.56\n", "482  2023-06-07    473.06\n", "481  2023-06-08    470.52\n", "480  2023-06-09    466.06\n", "..          ...       ...\n", "4    2025-05-27    480.89\n", "3    2025-05-28    477.08\n", "2    2025-05-29    477.77\n", "1    2025-05-30    477.21\n", "0    2025-06-02    476.23\n", "\n", "[485 rows x 2 columns]\n", "fetching the closes\n", "isin: DBEEUNGF, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBEEUNGF\n", "489  2023-06-05    427.98\n", "488  2023-06-06    427.52\n", "487  2023-06-07    420.08\n", "486  2023-06-08    425.19\n", "485  2023-06-09    426.94\n", "..          ...       ...\n", "4    2025-05-27    568.48\n", "3    2025-05-28    568.04\n", "2    2025-05-29    567.46\n", "1    2025-05-30    566.97\n", "0    2025-06-02    570.64\n", "\n", "[490 rows x 2 columns]\n", "fetching the closes\n", "isin: DBEEUGFT, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBEEUGFT\n", "498  2023-06-05    377.51\n", "497  2023-06-06    378.02\n", "496  2023-06-07    376.70\n", "495  2023-06-08    378.81\n", "494  2023-06-09    379.49\n", "..          ...       ...\n", "4    2025-05-27    479.07\n", "3    2025-05-28    477.67\n", "2    2025-05-29    478.31\n", "1    2025-05-30    478.66\n", "0    2025-06-02    480.13\n", "\n", "[499 rows x 2 columns]\n", "fetching the closes\n", "isin: DBRCOYGC, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBRCOYGC\n", "498  2023-06-05    297.10\n", "497  2023-06-06    298.06\n", "496  2023-06-07    294.76\n", "495  2023-06-08    297.54\n", "494  2023-06-09    297.39\n", "..          ...       ...\n", "4    2025-05-27    454.02\n", "3    2025-05-28    453.05\n", "2    2025-05-29    455.45\n", "1    2025-05-30    451.66\n", "0    2025-06-02    462.76\n", "\n", "[499 rows x 2 columns]\n", "fetching the closes\n", "isin: DBDRUS10, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBDRUS10\n", "487  2023-06-05    202.41\n", "486  2023-06-06    202.26\n", "485  2023-06-07    201.57\n", "484  2023-06-08    201.93\n", "483  2023-06-09    201.67\n", "..          ...       ...\n", "4    2025-05-27    189.66\n", "3    2025-05-28    189.14\n", "2    2025-05-29    189.80\n", "1    2025-05-30    190.01\n", "0    2025-06-02    189.98\n", "\n", "[488 rows x 2 columns]\n", "fetching the closes\n", "isin: DBDRUS02, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBDRUS02\n", "487  2023-06-05    122.08\n", "486  2023-06-06    122.00\n", "485  2023-06-07    121.77\n", "484  2023-06-08    121.94\n", "483  2023-06-09    121.75\n", "..          ...       ...\n", "4    2025-05-27    120.18\n", "3    2025-05-28    120.11\n", "2    2025-05-29    120.17\n", "1    2025-05-30    120.23\n", "0    2025-06-02    120.27\n", "\n", "[488 rows x 2 columns]\n", "fetching the closes\n", "isin: DBDRUS20, sdate: 2023-06-03, edate: 2025-06-02, ccode: USA\n", "<Response [200]>\n", "inside the check\n", "           date  DBDRUS20\n", "487  2023-06-05    228.39\n", "486  2023-06-06    228.18\n", "485  2023-06-07    227.50\n", "484  2023-06-08    227.64\n", "483  2023-06-09    227.63\n", "..          ...       ...\n", "4    2025-05-27    198.94\n", "3    2025-05-28    198.14\n", "2    2025-05-29    199.29\n", "1    2025-05-30    199.55\n", "0    2025-06-02    199.22\n", "\n", "[488 rows x 2 columns]\n"]}], "source": ["# Covariance file\n", "#start date is end date - 2 years\n", "#if the type of date is string, convert it to datetime\n", "if type(date) is str:\n", "    date = datetime.strptime(date, '%Y-%m-%d')\n", "\n", "start_date = date - <PERSON><PERSON><PERSON>(days=730)\n", "end_date = date\n", "date = convert_date_to_string(date)\n", "date_list = pd.date_range(start=start_date,end=end_date,freq='B').strftime(\"%Y-%m-%d\").tolist()\n", "df_f = pd.DataFrame(date_list,columns=['date'])\n", "df = pd.read_csv(fin_path+\"pre_optimization_file.csv\",index_col=False)\n", "ll = df['isin'].tolist()\n", "start_date = convert_date_to_string(start_date)\n", "end_date = convert_date_to_string(end_date)\n", "for i in ll:\n", "    isin = i\n", "    df1 = close_price(isin,start_date,end_date,'USA')\n", "    print(df1)\n", "    df1['date'] = df1['date'].astype(str)\n", "    df_f = pd.merge(df_f,df1,on='date',how='left')\n", "df_f = df_f.ffill()\n", "df_f = df_f.set_index('date')\n", "df_c = df_f.pct_change(periods=23)\n", "df_c = df_c.cov()\n", "df_c.to_csv(fin_path+\"Covariance.csv\",index=False) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# optimization"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from pymoo.algorithms.moo.nsga2 import NSGA2\n", "from pymoo.algorithms.moo.nsga3 import NSGA3\n", "from pymoo.core.problem import Problem\n", "from pymoo.optimize import minimize\n", "from pymoo.visualization.scatter import Scatter\n", "from pymoo.factory import get_sampling, get_crossover, get_mutation\n", "from pymoo.core.problem import ElementwiseProblem\n", "from pymoo.core.problem import Problem\n", "from pymoo.factory import get_problem, get_reference_directions\n", "from pymoo.algorithms.moo.unsga3 import UNSGA3\n", "from pymoo.core.problem import Problem\n", "from pymoo.core.repair import Repair\n", "from pymoo.operators.sampling.rnd import FloatRandomSampling\n", "from pymoo.optimize import minimize\n", "from pymoo.util.nds.non_dominated_sorting import NonDominatedSorting\n", "from pymoo.visualization.pcp import PCP\n", "\n", "import multiprocessing\n", "from concurrent.futures import ThreadPoolExecutor as tpe\n", "from multiprocessing.pool import ThreadPool"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["class SumEqualsOneRepair(Repair):\n", "    def _do(self, problem, pop, **kwargs):\n", "        X = pop.get(\"X\")\n", "        pop.set(\"X\", X / X.sum(axis=1, keepdims=True))\n", "        return pop"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pymoo(data_merge, cov):\n", "    class constraints(ElementwiseProblem):\n", "        def __init__(self, data_merge):\n", "            super().__init__(n_var=n, n_obj=2, n_constr=len(data_merge)*2, xl=0, xu=1)\n", "            self.data_merge = data_merge\n", "\n", "        def _evaluate(self, w, out, *args, **kwargs):\n", "\n", "            f1 = -(np.dot(np.array(w), E))\n", "            f2 = np.dot(np.array(w), (np.dot(cov, np.array(w).T)))\n", "\n", "            #data_grouped = self.data_merge.groupby(['sector'])\n", "            #print(w)\n", "            data_merge['w'] = w\n", "\n", "            def my_cons1(x,u):\n", "                return x-data_merge['company_max'][u]\n", "            def my_cons2(x,u):\n", "                return data_merge['MinCap'][u]-x\n", "#             def my_cons3(x,u):\n", "#                 return x-0.075\n", "#             def risk_cons(x, u):\n", "#                 return f2-0.000691*100\n", "\n", "            # = pool.starmap(my_cons, [w[k] for k in range(len(data_merge))], [k for k in range(len(data_merge))])\n", "            with tpe(max_workers = 50) as exe:\n", "                G = exe.map(my_cons1, [w[k] for k in range(len(data_merge))], [k for k in range(len(data_merge))])\n", "                H = exe.map(my_cons2, [w[k] for k in range(len(data_merge))], [k for k in range(len(data_merge))])\n", "                #I = exe.map(my_cons3, [w[k] for k in range(len(data_merge))], [k for k in range(len(data_merge))])\n", "                #J = exe.map(risk_cons, [w[k] for k in range(len(data_merge))], [k for k in range(len(data_merge))])\n", "                exe.shutdown(wait = True)\n", "            arr = list(G)+list(H)\n", "            out[\"F\"] = [f1, f2]\n", "            out[\"G\"] = np.column_stack([lll for lll in arr])\n", "\n", "    vectorized_problem = constraints(data_merge)\n", "    #ref_dirs = np.array([[2.0]])\n", "\n", "\n", "    algorithm = NSGA2(pop_size = 100, repair=SumEqualsOneRepair())\n", "\n", "    from pymoo.factory import get_termination\n", "\n", "    termination = get_termination(\"n_gen\",2)\n", "\n", "    from pymoo.optimize import minimize\n", "\n", "    res = minimize(vectorized_problem,\n", "                   algorithm,\n", "                   termination,\n", "                   return_least_infeasible=True,\n", "                   seed=1,\n", "                   save_history=False,\n", "                   verbose=True)\n", "\n", "    X = res.X\n", "    F = res.F\n", "\n", "    G = res.G\n", "    #pool.close()\n", "    #pool.close()\n", "    return F, X\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Compiled modules for significant speedup can not be used!\n", "https://pymoo.org/installation.html#installation\n", "\n", "To disable this warning:\n", "from pymoo.config import Config\n", "Config.show_compile_hint = False\n", "\n", "=====================================================================================\n", "n_gen |  n_eval |   cv (min)   |   cv (avg)   |  n_nds  |     eps      |  indicator  \n", "=====================================================================================\n", "    1 |     100 |  0.00000E+00 |  0.00000E+00 |       5 |            - |            -\n", "    2 |     200 |  0.00000E+00 |  0.00000E+00 |       3 |  0.333484900 |        ideal\n", "    3 |     300 |  0.00000E+00 |  0.00000E+00 |       7 |  0.142598252 |        ideal\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:00<00:00,  1.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["    4 |     400 |  0.00000E+00 |  0.00000E+00 |      13 |  0.118484533 |        ideal\n", "    5 |     500 |  0.00000E+00 |  0.00000E+00 |      16 |  0.********* |        ideal\n", "pymoo done\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["FILES = ['pre_optimization_file.csv']\n", "for file in tqdm(FILES):\n", "    data_merge = pd.read_csv(fin_path+file, index_col=False)\n", "    data_merge['MaxCap'] = 0.9\n", "    data_merge['MinCap'] = 0\n", "    data_merge['ER_simulation'] = data_merge['ER_simulation'] / 100\n", "    data_merge.rename(columns = {'MaxCap':'company_max'}, inplace = True)\n", "    #data_merge['company_max'] = data_merge['company_max']\n", "\n", "    cov_matrix = pd.read_csv(fin_path+\"Covariance.csv\", index_col=False)\n", "    n = len(data_merge)\n", "    wt = np.array([])\n", "    wt=np.repeat(1/n, n)\n", "    data_merge['wt'] = wt\n", "    w = data_merge['wt']\n", "    cov = cov_matrix\n", "    E = data_merge['ER_simulation']\n", "    \n", "    F, X = pymoo(data_merge, cov)\n", "    print('pymoo done')\n", "    #files.remove(file)\n", "    if len(F)>1:\n", "        for i in range(len(X)):\n", "            B = X[i]\n", "            #data_merge['wt_1']= B\n", "            data_merge['wt_'+str(i+1)] = B\n", "        A = []\n", "        B = []\n", "        C = []\n", "        for jj in range(len(F)):\n", "            #print(i)\n", "            A.append(-F[jj][0])\n", "            B.append(F[jj][1])\n", "            C.append(-F[jj][0]/F[jj][1])\n", "        ind_1 = A.index(np.max(A))\n", "        ind_3 = B.index(np.min(B))\n", "        ind_2 = C.index(np.max(C))\n", "        wt_1=data_merge['wt_'+str(ind_1+1)]\n", "        wt_2=data_merge['wt_'+str(ind_2+1)]\n", "        wt_3=data_merge['wt_'+str(ind_3+1)]\n", "\n", "        data_merge = data_merge.iloc[:,:7]\n", "        data_merge['max_ER'] = wt_1\n", "        data_merge['max_sharpe'] = wt_2\n", "        data_merge['min_risk'] = wt_3\n", "        \n", "    elif len(F) == 1:\n", "        B = X[0]\n", "        data_merge['max_ER or max_sharpe'] = B\n", "\n", "    data_merge['ER_simulation'] = data_merge['ER_simulation'] * 100\n", "    data_merge.to_csv(fin_path+\"optimized_file.csv\",index=False)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9999999999999997\n", "0.9999999999999998\n", "0.9999999999999998\n"]}], "source": ["df = pd.read_csv(fin_path+\"optimized_file.csv\",index_col=False)\n", "print(df['max_ER'].sum())\n", "print(df['max_sharpe'].sum())\n", "print(df['min_risk'].sum())"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9999999999999998\n", "1.0\n", "1.0\n"]}], "source": ["df['max_ER'] = df['max_ER'] / df['max_ER'].sum()\n", "df['max_sharpe'] = df['max_sharpe'] / df['max_sharpe'].sum()\n", "df['min_risk'] = df['min_risk'] / df['min_risk'].sum()\n", "print(df['max_ER'].sum())\n", "\n", "print(df['max_sharpe'].sum())\n", "print(df['min_risk'].sum())\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["df = df.round({\"max_ER\":4,'max_sharpe':4,'min_risk':4})\n", "df.at[0,'max_ER'] =df.iloc[0]['max_ER'] + (1-df['max_ER'].sum())\n", "df.at[0,'max_sharpe'] =df.iloc[0]['max_sharpe'] + (1-df['max_sharpe'].sum())\n", "df.at[0,'min_risk'] =df.iloc[0]['min_risk'] + (1-df['min_risk'].sum())\n", "df = df.round({\"max_ER\":4,'max_sharpe':4,'min_risk':4})\n", "for i in range(len(df)):\n", "    er_v = df.iloc[i]['ER']\n", "    max_e = df.iloc[i]['max_ER']\n", "    max_s = df.iloc[i]['max_sharpe']\n", "    max_r = df.iloc[i]['min_risk']\n", "    if er_v < 0:\n", "        max_e = max_e * -1\n", "        max_s = max_s * -1\n", "        max_r = max_r * -1\n", "        df.at[i,'max_ER'] = max_e\n", "        df.at[i,'max_sharpe'] = max_s\n", "        df.at[i,'min_risk'] = max_r "]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>isin</th>\n", "      <th>ER</th>\n", "      <th>confidence_score</th>\n", "      <th>er_abs</th>\n", "      <th>ER_simulation</th>\n", "      <th>company_max</th>\n", "      <th>max_ER</th>\n", "      <th>max_sharpe</th>\n", "      <th>min_risk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBEEEMGF</td>\n", "      <td>0.779199</td>\n", "      <td>0.686732</td>\n", "      <td>0.779199</td>\n", "      <td>0.779199</td>\n", "      <td>0.9</td>\n", "      <td>0.0097</td>\n", "      <td>0.0459</td>\n", "      <td>0.0459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBEETGFU</td>\n", "      <td>-4.184373</td>\n", "      <td>0.616001</td>\n", "      <td>4.184373</td>\n", "      <td>4.184373</td>\n", "      <td>0.9</td>\n", "      <td>-0.3522</td>\n", "      <td>-0.0531</td>\n", "      <td>-0.0531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBEEURGF</td>\n", "      <td>-0.261153</td>\n", "      <td>0.609364</td>\n", "      <td>0.261153</td>\n", "      <td>0.261153</td>\n", "      <td>0.9</td>\n", "      <td>-0.0144</td>\n", "      <td>-0.0228</td>\n", "      <td>-0.0228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBEEUNGF</td>\n", "      <td>3.125924</td>\n", "      <td>0.638434</td>\n", "      <td>3.125924</td>\n", "      <td>3.125924</td>\n", "      <td>0.9</td>\n", "      <td>0.0000</td>\n", "      <td>0.0389</td>\n", "      <td>0.0389</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBEEUGFT</td>\n", "      <td>2.490304</td>\n", "      <td>0.642198</td>\n", "      <td>2.490304</td>\n", "      <td>2.490304</td>\n", "      <td>0.9</td>\n", "      <td>0.0279</td>\n", "      <td>0.0429</td>\n", "      <td>0.0429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBRCOYGC</td>\n", "      <td>-4.502002</td>\n", "      <td>0.848464</td>\n", "      <td>4.502002</td>\n", "      <td>4.502002</td>\n", "      <td>0.9</td>\n", "      <td>-0.3993</td>\n", "      <td>-0.2167</td>\n", "      <td>-0.2167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBDRUS10</td>\n", "      <td>0.426256</td>\n", "      <td>0.654821</td>\n", "      <td>0.426256</td>\n", "      <td>0.426256</td>\n", "      <td>0.9</td>\n", "      <td>0.0160</td>\n", "      <td>0.0845</td>\n", "      <td>0.0845</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBDRUS02</td>\n", "      <td>0.188326</td>\n", "      <td>0.641292</td>\n", "      <td>0.188326</td>\n", "      <td>0.188326</td>\n", "      <td>0.9</td>\n", "      <td>0.0483</td>\n", "      <td>0.4271</td>\n", "      <td>0.4271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-06-02</td>\n", "      <td>DBDRUS20</td>\n", "      <td>1.399670</td>\n", "      <td>0.653236</td>\n", "      <td>1.399670</td>\n", "      <td>1.399670</td>\n", "      <td>0.9</td>\n", "      <td>0.1322</td>\n", "      <td>0.0681</td>\n", "      <td>0.0681</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date      isin        ER  confidence_score    er_abs  ER_simulation  \\\n", "0  2025-06-02  DBEEEMGF  0.779199          0.686732  0.779199       0.779199   \n", "1  2025-06-02  DBEETGFU -4.184373          0.616001  4.184373       4.184373   \n", "2  2025-06-02  DBEEURGF -0.261153          0.609364  0.261153       0.261153   \n", "3  2025-06-02  DBEEUNGF  3.125924          0.638434  3.125924       3.125924   \n", "4  2025-06-02  DBEEUGFT  2.490304          0.642198  2.490304       2.490304   \n", "5  2025-06-02  DBRCOYGC -4.502002          0.848464  4.502002       4.502002   \n", "6  2025-06-02  DBDRUS10  0.426256          0.654821  0.426256       0.426256   \n", "7  2025-06-02  DBDRUS02  0.188326          0.641292  0.188326       0.188326   \n", "8  2025-06-02  DBDRUS20  1.399670          0.653236  1.399670       1.399670   \n", "\n", "   company_max  max_ER  max_sharpe  min_risk  \n", "0          0.9  0.0097      0.0459    0.0459  \n", "1          0.9 -0.3522     -0.0531   -0.0531  \n", "2          0.9 -0.0144     -0.0228   -0.0228  \n", "3          0.9  0.0000      0.0389    0.0389  \n", "4          0.9  0.0279      0.0429    0.0429  \n", "5          0.9 -0.3993     -0.2167   -0.2167  \n", "6          0.9  0.0160      0.0845    0.0845  \n", "7          0.9  0.0483      0.4271    0.4271  \n", "8          0.9  0.1322      0.0681    0.0681  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["df.to_csv(fin_path+f\"optimized_deutche_bank_{date}.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["#df = pd.read_excel(local_path+\"DB_weights_Dec2024.xlsx\",index_col=False)\n", "df = pd.read_csv(fin_path+f\"optimized_deutche_bank_{date}.csv\",index_col=False)[['date','isin','min_risk']].rename(columns={'min_risk':'Wt'})\n", "#df = df[['date','isin','min_risk']]\n", "df.rename(columns={'date':'Dates','isin':'Identifier','Wt':'wt'},inplace=True)\n", "df['Dates'] = f'{date}'\n", "order_list = ['DBRC<PERSON>Y<PERSON>','DBEEEMG<PERSON>','DBEETGF<PERSON>','DBEEURG<PERSON>','DBEEUNGF','DBEEUGFT','DBDRUS10','DBDRUS02','DBDRUS20']\n", "df_id = pd.DataFrame(order_list,columns=['Identifier'])\n", "df_id = pd.merge(df_id,df,on='Identifier',how='left')\n", "df_id = df_id[['Dates','Identifier','wt']]\n", "df_id.to_csv(fin_path+f\"EQUBOT_DBIQ_{date}.csv\",index=False)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}