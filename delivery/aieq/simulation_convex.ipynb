{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d11c9074", "metadata": {"scrolled": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np \n", "import requests as req\n", "from pandas import json_normalize\n", "from functools import reduce\n", "#import matplotlib.pyplot as plt|\n", "import io\n", "import os\n", "from datetime import datetime\n", "from dateutil.relativedelta import relativedelta\n", "import math\n", "\n", "import cvxpy as cp\n", "import itertools\n", "from sklearn.experimental import enable_iterative_imputer\n", "#import seaborn as sns\n", "from sklearn.impute import IterativeImputer\n", "from sklearn.covariance import MinCovDet\n", "\n", "#!pip3 install xlrd\n", "\n", "#!pip3 install openpyxl\n", "import concurrent.futures\n", "\n", "from functools import reduce\n", "import matplotlib.pyplot as plt\n", "import multiprocessing\n", "from concurrent.futures import ThreadPoolExecutor as tpe\n", "from multiprocessing.pool import ThreadPool\n", "import os\n", "from datetime import datetime\n", "from dateutil.relativedelta import relativedelta\n", "import math\n", "from io import StringIO\n", "import warnings\n", "warnings.simplefilter(\"ignore\")\n", "import boto3\n", "import pandas as pd\n", "import numpy as np\n", "import requests\n", "from pandas import json_normalize\n", "import smtplib\n", "from email.mime.multipart import MIMEMultipart\n", "from dateutil.relativedelta import relativedelta\n", "from email.mime.text import MIMEText\n", "from email.mime.image import MIMEImage\n", "from email.mime.application import MIMEApplication\n", "from functools import reduce\n", "from datetime import timedelta,date,datetime\n", "import traceback\n", "import xlrd\n", "import os\n", "from zipfile import ZipFile\n", "\n", "AWS_ACCESS_KEY_ID='********************', \n", "AWS_SECRET_ACCESS_KEY='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'\n", "import pandas as pd\n", "import threading\n", "import math\n", "from functools import reduce\n", "import requests\n", "from pandas import json_normalize\n", "import datetime\n", "from openpyxl import Workbook\n", "from openpyxl.utils.dataframe import dataframe_to_rows\n", "from openpyxl.utils import get_column_letter\n", "from datetime import datetime\n", "from dateutil.rrule import *\n", "from dateutil.relativedelta import relativedelta\n", "from datetime import datetime, date, timedelta\n", "from opensearchpy.client import OpenSearch\n", "import logging\n", "import concurrent.futures\n", "from opensearchpy.connection.http_requests import RequestsHttpConnection\n", "from aws_requests_auth.aws_auth import AWSRequestsAuth\n", "from opensearchpy.client import OpenSearch\n", "from requests_aws4auth import AWS4Auth\n", "import logging\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "id": "bab783a0-5c3d-4816-a2ac-525a595742b8", "metadata": {}, "outputs": [], "source": ["path = os.getcwd()+\"/\"\n", "fin_path = path+\"25-05/\"\n", "sector_isin_file = 'aisrt_isin.xlsx'"]}, {"cell_type": "code", "execution_count": 3, "id": "c2b23100-075b-4782-92e3-e2c9313ebbfa", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def get_csv_data_from_s3(bucket_name, object_key): # object key of the file to read\n", "    s3 = boto3.client('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)\n", "    csv_obj = s3.get_object(Bucket=bucket_name, Key=object_key)\n", "    body = csv_obj['Body']\n", "    csv_string = body.read().decode('utf-8')\n", "    df = pd.read_csv(StringIO(csv_string))\n", "    return df\n", "\n", "def get_s3_keys_from_bucket(bucket_name, folder_path):\n", "    s3 = boto3.resource('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)\n", "    s3_bucket_obj = s3.Bucket(bucket_name)\n", "    key_list = []\n", "    time_stamp_list = []\n", "    try:\n", "        for i, object_summary in enumerate(s3_bucket_obj.objects.filter(Prefix = folder_path)):\n", "            try:\n", "                key_list.append(object_summary.key)\n", "                time_stamp_list.append(object_summary.last_modified)\n", "            except Exception as e:\n", "                print(f'exception in for loop {e}')\n", "    except Exception as e:\n", "        print(f'Exception as outer loop{e}')\n", "    keys_df = pd.DataFrame(list(zip(key_list, time_stamp_list)), columns= ['key', 'timestamp'])\n", "    keys_df=keys_df[keys_df['key']!=folder_path]\n", "    keys_df.sort_values(by=[\"timestamp\"], ascending=False, inplace=True)\n", "    return keys_df\n", "\n", "def upload_files_to_s3(data,data_bucket_name,folder_path,file_prefix):\n", "    csv_buffer = StringIO()\n", "    data.to_csv(csv_buffer)\n", "    s3 = boto3.resource('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)\n", "    s3.Object(data_bucket_name, folder_path+file_prefix+'.csv').put(Body=csv_buffer.getvalue())\n", "\n", "def get_excel_data_from_s3(bucket_name, object_key):\n", "    s3 = boto3.client('s3', aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY)\n", "    obj = s3.get_object(Bucket=bucket_name, Key=object_key)\n", "    body = obj['Body'].read()\n", "    data = pd.read_excel(io.BytesIO(body))\n", "    return data\n", "def download_s3file(bucketName,folderName,filename):\n", "    s3_client = boto3.client('s3',aws_access_key_id='********************',\n", "         aws_secret_access_key='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/')\n", "\n", "    objs = s3_client.list_objects_v2(Bucket=bucketName, Prefix=folderName)['Contents']\n", "    filep = fin_path+filename\n", "    print(folderName+filename)\n", "    s3_client.download_file(bucketName,folderName+filename , filep)\n", "def upload_s3file(file_name,bucket,object_name):\n", "    s3_client = boto3.client('s3',aws_access_key_id=AWS_ACCESS_KEY_ID,\n", "         aws_secret_access_key=AWS_SECRET_ACCESS_KEY)\n", "    response = s3_client.upload_file(file_name, bucket, object_name) \n", "def close_price(isin,sdate,edate,ccode):\n", "    try:\n", "        df1 = pd.DataFrame()\n", "        response = requests.get(\"http://************:8080/stockhistory/getstocksbyisin?isin=\" + isin + \"&startDate=\" + sdate + \"&endDate=\" + edate + \"&countrycode=\" + ccode)\n", "        response_data = response.json()\n", "        check = bool(response_data['status'])\n", "        if check:\n", "            df1 = pd.DataFrame.from_dict(\n", "                json_normalize(response_data['data']['stocks'])[['date', 'close']]).iloc[::-1]\n", "            df1.rename(columns={'close': isin}, inplace=True)\n", "            return df1\n", "    except Exception as e:\n", "        print(e)\n", "def get_file_list(bucketName,folderName):    \n", "    session = boto3.Session( \n", "             aws_access_key_id=AWS_ACCESS_KEY_ID, \n", "             aws_secret_access_key=AWS_SECRET_ACCESS_KEY)\n", "    #Then use the session to get the resource\n", "    s3 = session.resource('s3')\n", "    my_bucket = s3.<PERSON><PERSON>(bucketName)\n", "\n", "    files = my_bucket.objects.filter(Prefix=folderName)\n", "    files = [obj.key for obj in sorted(files, key=lambda x: x.last_modified, \n", "        reverse=False)]\n", "    files = [i.replace(folderName,\"\").replace(\".csv\",\"\") for i in files]\n", "    return files        "]}, {"cell_type": "code", "execution_count": 5, "id": "3285f41e-a7b4-49cc-a9de-22a1987323f4", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["US81369Y1001 USA\n", "US81369Y8527 USA\n", "US81369Y5069 USA\n", "US81369Y6059 USA\n", "US81369Y7040 USA\n", "US81369Y8030 USA\n"]}, {"name": "stdout", "output_type": "stream", "text": ["US81369Y3080 USA\n", "US81369Y8600 USA\n", "US81369Y8865 USA\n", "US81369Y2090 USA\n", "US81369Y4070 USA\n"]}], "source": ["df_isin = pd.read_excel(path+sector_isin_file,index_col=False,sheet_name='Sheet1')\n", "#df_isin = pd.read_csv(os.getcwd()+\"/\"+\"pre_portfolio_er_consolidate_2025-03-27.csv\",index_col=False)\n", "sdate = (datetime.now() - relativedelta(years=2)).strftime(\"%Y-%m-%d\")\n", "edate = datetime.now().strftime(\"%Y-%m-%d\")\n", "df_date = pd.DataFrame(pd.date_range(start=sdate,end=edate,freq='B').strftime(\"%Y-%m-%d\").tolist(),columns=['date'])\n", "\n", "for i in range(len(df_isin)):\n", "    isin = df_isin.iloc[i]['isin']\n", "    ccode = df_isin.iloc[i]['country_code']\n", "    print(isin,ccode)\n", "    try:\n", "        df1 = close_price(isin,sdate,edate,ccode)\n", "        df_date = pd.merge(df_date,df1,on='date',how='left')\n", "        df_date = df_date.drop_duplicates(subset=['date'])\n", "    except :\n", "        continue\n", "df_date.to_csv(fin_path+\"close_price.csv\",index=False)        "]}, {"cell_type": "markdown", "id": "2c871a9e-b4a2-4a35-82df-c53ae9d5c0ee", "metadata": {}, "source": ["ER collections"]}, {"cell_type": "code", "execution_count": 6, "id": "1865d3c1-6a6d-4fe4-ae91-82071fc9671f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ETF_Pre_Portfolio_Folder/ETF_pre_portfolio_05_23_2025.xlsx\n"]}], "source": ["bucketName = 'aimax'\n", "folderName = 'ETF_Pre_Portfolio_Folder/'\n", "filename  = 'ETF_pre_portfolio_05_23_2025.xlsx'\n", "download_s3file(bucketName,folderName,filename)\n", "df_er = pd.read_excel(fin_path+filename,index_col=False)\n", "df_isin = pd.merge(df_isin,df_er[['Equity','Avg']].rename(columns={'Equity':'tic','Avg':'ER'}),on='tic',how='left')\n", "df_isin.to_csv(fin_path+\"aisrt_er.csv\",index=False)"]}, {"cell_type": "markdown", "id": "77d82994-3de5-4f65-8341-bae2c2d5f4d0", "metadata": {}, "source": ["Optimization"]}, {"cell_type": "code", "execution_count": 7, "id": "a82b06b6", "metadata": {}, "outputs": [], "source": ["# Make cov\n", "def make_cov(close_df, h, df):\n", "    \"\"\"\n", "    Creates a robust covariance matrix using only ISINs that exist in both close prices and input DataFrame\n", "    \n", "    Parameters:\n", "    close_df (pd.DataFrame): DataFrame with close prices and 'date' column\n", "    h (str): Target date\n", "    df (pd.DataFrame): DataFrame containing ISINs to include in covariance matrix\n", "    \n", "    Returns:\n", "    pd.DataFrame: Robust covariance matrix\n", "    \"\"\"\n", "    # Convert target date to datetime\n", "    h_date = pd.to_datetime(h)\n", "    start_date = h_date - pd.DateOffset(years=2)\n", "    \n", "    # Convert and ensure date column is datetime\n", "    close_df = close_df.copy()\n", "    close_df['date'] = pd.to_datetime(close_df['date'], errors='coerce')\n", "    \n", "    # Get list of ISINs from input DataFrame\n", "    input_isins = set(df['isin'].unique())\n", "    close_isins = set(close_df.columns) - {'date'}\n", "    \n", "    # Find common ISINs\n", "    common_isins = list(input_isins.intersection(close_isins))\n", "    print(f\"Total ISINs in input: {len(input_isins)}\")\n", "    print(f\"Total ISINs in close: {len(close_isins)}\")\n", "    print(f\"Common ISINs: {len(common_isins)}\")\n", "    \n", "    if len(common_isins) == 0:\n", "        raise ValueError(\"No common ISINs found between input data and close prices\")\n", "    \n", "    # Filter close_df for date range and common ISINs\n", "    df_close = close_df[\n", "        (close_df['date'] >= start_date) & \n", "        (close_df['date'] <= h_date)\n", "    ][['date'] + common_isins].copy()\n", "    \n", "    # Set date as index\n", "    df_close.set_index('date', inplace=True)\n", "    \n", "    # # Handle missing data\n", "    # df_close.interpolate(method='linear', axis=0, inplace=True)\n", "    # df_close.to_csv(path + 'closerdf.csv')\n", "    # # Iterative imputation for remaining missing values\n", "    # iter_imp = IterativeImputer(skip_complete=True, n_nearest_features=min(15, len(common_isins)))\n", "    \n", "    # # Imputation\n", "    # imputed_data = iter_imp.fit_transform(df_close)\n", "    df_close = pd.DataFrame(df_close, columns=common_isins, index=df_close.index)\n", "    \n", "    # Calculate 22-day returns\n", "    df_returns = df_close.pct_change(periods=23)\n", "    #df_returns = df_returns.dropna()\n", "    \n", "    # if len(df_returns) < 2:\n", "    #     raise ValueError(\"Not enough data points after calculating returns\")\n", "    \n", "    # # Robust covariance estimation\n", "    # mcd = MinCovDet(random_state=42)\n", "    # mcd.fit(df_returns.values)\n", "    \n", "    # # Create covariance matrix DataFrame\n", "    # robust_cov = pd.DataFrame(\n", "    #     mcd.covariance_,\n", "    #     index=common_isins,\n", "    #     columns=common_isins\n", "    # )\n", "    robust_cov = df_returns.cov()\n", "    \n", "    return robust_cov"]}, {"cell_type": "code", "execution_count": 8, "id": "5b9ff693", "metadata": {}, "outputs": [], "source": ["# Bunch Constraints\n", "\n", "def add_asset_constraints(df, w, constraints):\n", "    if 'asset_class_weight_min' in df.columns:\n", "        for asset_class in df['asset_class'].unique():\n", "            mask = df['asset_class'] == asset_class\n", "            class_min = df.loc[mask, 'asset_class_weight_min'].iloc[0]\n", "            class_max = df.loc[mask, 'asset_class_weight_max'].iloc[0]\n", "            \n", "            class_weights = w[mask.values]  # Convert mask to numpy array\n", "            constraints.append(cp.sum(class_weights) >= class_min)\n", "            constraints.append(cp.sum(class_weights) <= class_max)\n", "    return constraints\n", "\n", "def add_sector_constraints(df, w, constraints):\n", "    if 'sector_min' in df.columns:\n", "        for sector_name in df['sector_name'].unique():\n", "            mask = df['sector_name'] == sector_name\n", "            sector_min = df.loc[mask, 'sector_min'].iloc[0]\n", "            sector_max = df.loc[mask, 'sector_max'].iloc[0]\n", "            \n", "            sector_weights = w[mask.values]  # Convert mask to numpy array\n", "            constraints.append(cp.sum(sector_weights) >= sector_min)\n", "            constraints.append(cp.sum(sector_weights) <= sector_max)\n", "    return constraints\n", "\n", "def add_geo_constraints(df, w, constraints):\n", "    if 'exposure' in df.columns:\n", "        for geo in df['exposure'].unique():\n", "            mask = df['exposure'] == geo\n", "            geo_min = df.loc[mask, 'geo_min'].iloc[0]\n", "            geo_max = df.loc[mask, 'geo_max'].iloc[0]\n", "            \n", "            geo_weights = w[mask.values]  # Convert mask to numpy array\n", "            constraints.append(cp.sum(geo_weights) >= geo_min)\n", "            constraints.append(cp.sum(geo_weights) <= geo_max)\n", "    return constraints\n"]}, {"cell_type": "code", "execution_count": 46, "id": "903a4d05-b6e3-44d2-8e6b-12bd40fff4bc", "metadata": {}, "outputs": [], "source": ["# MAX RETURNS\n", "def max_returns(df,cov,lambda_reg,risk_limit):\n", "   \n", "    er = df['er']\n", "    min_vector = df['min']\n", "    max_vector = df['max']\n", "    print(f'min vector is {min_vector}')\n", "    w = cp.Variable(len(er))\n", "    returns = w.T @ er\n", "    risk = cp.quad_form(w,cov)\n", "    # w.T @ cov @ w\n", "    print(f'risk limit is {risk_limit}')\n", "    print(f'min vector is {type(min_vector)}')\n", "    print(f'max vector is {max_vector}')\n", "    l2_reg = lambda_reg * cp.sum_squares(w)\n", "    x = returns - l2_reg\n", "    constraints = [\n", "        cp.sum(w) == 1,\n", "        w >= min_vector,\n", "        w <= max_vector,\n", "        risk <= risk_limit\n", "    ]\n", "    # constraints = add_asset_constraints(df,w,constraints)\n", "    constraints = add_sector_constraints(df,w,constraints)\n", "    # constraints = add_geo_constraints(df,w,constraints)\n", "    objective = cp.Maximize(x)\n", "    problem = cp.Problem(objective,constraints)\n", "    problem.solve(solver=cp.CLARABEL)\n", "    if w.value is None:\n", "        raise ValueError(\"Optimization failed to find a solution. Check constraints and input data.\")\n", "    return w.value"]}, {"cell_type": "code", "execution_count": 10, "id": "024588ba", "metadata": {}, "outputs": [], "source": ["# SHARPE\n", "def optimize(df,cov,alpha,lambda_reg,risk_limit,min_return):\n", "    er = df['er']\n", "    min_vector = df['min']\n", "    max_vector = df['max']\n", "    print(alpha,lambda_reg)\n", "    w = cp.Variable(len(er))\n", "    returns = w.T @ er\n", "    risk = cp.quad_form(w,cov)\n", "    l2_reg = lambda_reg * cp.sum_squares(w)\n", "    sharpe = returns - alpha*risk - l2_reg\n", "    objective = cp.Maximize(sharpe)\n", "    constraints = [\n", "        cp.sum(w) == 1,\n", "        w >= min_vector,\n", "        w <= max_vector,\n", "        returns >= min_return,\n", "        risk <= risk_limit\n", "    ]\n", "    constraints = add_asset_constraints(df,w,constraints)\n", "    constraints = add_sector_constraints(df,w,constraints)\n", "    constraints = add_geo_constraints(df,w,constraints)\n", "    problem = cp.Problem(objective,constraints)\n", "    # problem.solve(verbose=True)\n", "    problem.solve()\n", "    if w.value is None:\n", "        raise ValueError(\"Optimization failed to find a solution. Check constraints and input data.\")\n", "    return w.value"]}, {"cell_type": "code", "execution_count": 11, "id": "a0068406-efe1-4567-84cd-82ea6759213b", "metadata": {}, "outputs": [], "source": ["# Find Lambda\n", "'''\n", "To do binary search for finding the lambda value which gives the ena of around 80 percent. Change the target_ena for other results\n", "'''\n", "def get_weight(input,cov,method,alpha,lambda_reg,risk_limit,min_return):\n", "    if method == 'sharpe':\n", "        print(f'inside')\n", "        w = optimize(input,cov,alpha,lambda_reg,risk_limit,min_return)\n", "    elif method == 'max_returns':\n", "        w = max_returns(input,cov,lambda_reg,risk_limit)\n", "    else:\n", "        w = minimize_risk(input,cov,lambda_reg,min_return)\n", "    return w\n", "\n", "def find_lambda(input,cov,method,alpha,risk_limit,min_return,target_ena, low, high, tolerance=0.05, max_iterations=100):\n", "    \"\"\"\n", "    Perform a binary search to find the lambda value that results in an effective number of assets (ena)\n", "    within 5% of the target_ena.\n", "\n", "    Parameters:\n", "        input: Input data for the max_returns function.\n", "        cov: Covariance matrix.\n", "        target_ena: Target effective number of assets.\n", "        low: Lower bound for lambda.\n", "        high: Upper bound for lambda.\n", "        tolerance: Tolerance for the difference between ena and target_ena (default is 5%).\n", "        max_iterations: Maximum number of iterations for the binary search.\n", "\n", "    Returns:\n", "        lambda: The lambda value that results in ena within the tolerance of target_ena.\n", "    \"\"\"\n", "    print(f\"method is {method} and type is {type(method)}\")\n", "    iteration = 0\n", "    while iteration < max_iterations:\n", "        print(f'iteratino number {iteration}')\n", "        mid = low + (high - low) // 2\n", "        if low >= high - 1:\n", "            break  \n", "        print(f'mid is {mid}')\n", "        w = get_weight(input,cov,method,alpha,mid,risk_limit,min_return)\n", "        ena = 1 / np.sum(w**2)  \n", "        print(ena)\n", "        if abs(ena - target_ena) / target_ena <= tolerance:\n", "            print(f'found my guy: ena {ena} and target {target_ena}')\n", "            return mid\n", "\n", "        if ena < target_ena:\n", "            low = mid  \n", "        else:\n", "            high = mid\n", "\n", "        iteration += 1\n", "\n", "    # If the loop ends without finding a suitable lambda, return the best estimate\n", "    print(f'returning the best estimate')\n", "    return mid"]}, {"cell_type": "code", "execution_count": 12, "id": "ac94af5c-6a75-420d-bdea-630dc24ea273", "metadata": {}, "outputs": [], "source": ["# Risk and Return function\n", "'''\n", "Finding the risk and return if the weights,cov and er are given.\n", "'''\n", "def calculate_risk(w,cov):\n", "    # input are the weights and the covariance risk\n", "    if hasattr(cov, 'value'):\n", "        cov_np = cov.value\n", "    else:\n", "        cov_np = np.array(cov)\n", "    return np.sqrt(w.T@cov_np@w)\n", "    \n", "def calculate_returns(w,er):\n", "    return np.dot(w,er)"]}, {"cell_type": "code", "execution_count": 13, "id": "029e7aae-506c-46e9-bfe3-e87f303c8766", "metadata": {}, "outputs": [], "source": ["# wrap cov and reorder input\n", "'''\n", "1) Reorder the input file to make sure that it has the same order as cov\n", "2) cov wrap.\n", "'''\n", "def reorder_input(input,cov):\n", "    cov_list = cov.columns.to_list()\n", "    input = input.set_index('isin').loc[cov_list].reset_index()\n", "    return input\n", "def wrap_cov(cov):\n", "    P = cp.atoms.affine.wraps.psd_wrap(cov)\n", "    return P"]}, {"cell_type": "code", "execution_count": 14, "id": "8df336b4-4786-4c99-a012-1f603aa549a9", "metadata": {}, "outputs": [], "source": ["\n", "def mr_long(output_folder,risk_limit,min_return,target_enan,df,close_df,end_date):\n", "    output_path = path + '/' + output_folder + '/'\n", "    target_ena = target_enan * len(df)\n", "    try:\n", "        \n", "        cov = make_cov(close_df,end_date,df)\n", "        P = wrap_cov(cov)\n", "        df = reorder_input(df,cov) \n", "        lambda_reg = find_lambda(df,P,'max_returns',0,risk_limit,min_return,target_ena,0,100)\n", "        w = max_returns(df,P,lambda_reg,risk_limit)\n", "        print(w,'w')\n", "        df['wt'] = w\n", "        df.to_csv(output_path + 'optimized_aisrt_' + end_date + '.csv')\n", "        \n", "    except Exception as e:\n", "        #print the trackback and the error message\n", "        print(f\"An error occurred: {e}\")\n", "        traceback.print_exc()\n", "       \n", "        print(e)\n", "        "]}, {"cell_type": "code", "execution_count": 41, "id": "4c37891d-84fc-42f4-b215-efdef616e5b2", "metadata": {}, "outputs": [], "source": ["def mr_long_aipex(output_folder,risk_limit,min_return,target_enan,df,close_df,end_date):\n", "    output_path = path + '/' + output_folder + '/'\n", "    target_ena = target_enan * len(df)\n", "    try:\n", "        \n", "        cov = make_cov(close_df,end_date,df)\n", "        P = wrap_cov(cov)\n", "        df = reorder_input(df,cov)\n", "        lambda_reg = find_lambda(df,P,'max_returns',0,risk_limit,min_return,target_ena,0,100)\n", "        w = max_returns(df,P,lambda_reg,risk_limit)\n", "        print(w,'w')\n", "        print(f'the enan is {1/np.sum(w**2) / len(w)}')\n", "        df['wt'] = w\n", "        #df.to_csv(output_path + 'optimized_aisrt_' + end_date + '.csv')\n", "        df.to_excel(output_path+ \"aipex_er_new_cap_\"+today_date+\".xlsx\",index=False)\n", "        \n", "    except Exception as e:\n", "       \n", "        print(e)\n", "        "]}, {"cell_type": "markdown", "id": "8126f43b-e03b-4f29-94a0-21c9d5d397a4", "metadata": {}, "source": ["AISRT optimization"]}, {"cell_type": "code", "execution_count": null, "id": "b7fafdd3-ca79-4254-9e5c-244e8d439f24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["folder already there\n", "Total ISINs in input: 11\n", "Total ISINs in close: 11\n", "Common ISINs: 11\n", "method is max_returns and type is <class 'str'>\n", "iteratino number 0\n", "mid is 50\n", "'max'\n"]}], "source": ["\n", "target_enan = 0.6 # target ena/n\n", "output_folder = 'output'\n", "path = fin_path\n", "try:\n", "    os.mkdir(path + output_folder)\n", "except:\n", "    print(f'folder already there')\n", "\n", "risk_limit = 1\n", "min_return = -10\n", "is_hard_constraint = True\n", "objj = 'max_returns'\n", "\n", "file_sec = 'aipex_cat_historic_er1'\n", "\n", "df_er = pd.read_csv(fin_path + 'aisrt_er.csv',index_col=False)\n", "df_s = df_er[['tic','sector_name','isin','ER']].rename(columns={'ER':'er'})\n", "data_merge = df_s\n", "data_merge['er'] = data_merge['er'] * 100\n", "close_df = pd.read_csv(fin_path + \"close_price.csv\",index_col=False)\n", "close_df['date'] = pd.to_datetime(close_df['date'],format='%Y-%m-%d')\n", "end_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "mr_long(output_folder,risk_limit,min_return,target_enan,data_merge,close_df,end_date)\n", "output_path = path + '/' + output_folder + '/'\n", "df = pd.read_csv(output_path + 'optimized_aisrt_' + end_date + '.csv')"]}, {"cell_type": "code", "execution_count": 17, "id": "4f2c6c3a-5b12-4685-8395-f64d91ec0a32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>isin</th>\n", "      <th>tic</th>\n", "      <th>sector_name</th>\n", "      <th>er</th>\n", "      <th>max</th>\n", "      <th>min</th>\n", "      <th>wt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>US81369Y2090</td>\n", "      <td>XLV</td>\n", "      <td>Health Care</td>\n", "      <td>3.559640</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.160648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>US81369Y3080</td>\n", "      <td>XLP</td>\n", "      <td>Consumer Staples</td>\n", "      <td>0.002725</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.042085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>US81369Y8030</td>\n", "      <td>XLK</td>\n", "      <td>Information Technology</td>\n", "      <td>3.865984</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.170860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>US81369Y5069</td>\n", "      <td>XLE</td>\n", "      <td>Energy</td>\n", "      <td>3.263514</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.150777</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>US81369Y1001</td>\n", "      <td>XLB</td>\n", "      <td>Materials</td>\n", "      <td>1.651132</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.097031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>US81369Y8527</td>\n", "      <td>XLC</td>\n", "      <td>Communication Services</td>\n", "      <td>0.089132</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.044965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>6</td>\n", "      <td>US81369Y4070</td>\n", "      <td>XLY</td>\n", "      <td>Consumer Discretionary</td>\n", "      <td>-2.724988</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.000200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>7</td>\n", "      <td>US81369Y7040</td>\n", "      <td>XLI</td>\n", "      <td>Industrials</td>\n", "      <td>-0.521642</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.024606</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>8</td>\n", "      <td>US81369Y8600</td>\n", "      <td>XLRE</td>\n", "      <td>Real Estate</td>\n", "      <td>5.518701</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.225950</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>9</td>\n", "      <td>US81369Y8865</td>\n", "      <td>XLU</td>\n", "      <td>Utilities</td>\n", "      <td>-0.359860</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.029998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>10</td>\n", "      <td>US81369Y6059</td>\n", "      <td>XLF</td>\n", "      <td>Financials</td>\n", "      <td>0.326564</td>\n", "      <td>0.9</td>\n", "      <td>0.0002</td>\n", "      <td>0.052879</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Unnamed: 0          isin   tic             sector_name        er  max  \\\n", "0            0  US81369Y2090   XLV             Health Care  3.559640  0.9   \n", "1            1  US81369Y3080   XLP        Consumer Staples  0.002725  0.9   \n", "2            2  US81369Y8030   XLK  Information Technology  3.865984  0.9   \n", "3            3  US81369Y5069   XLE                  Energy  3.263514  0.9   \n", "4            4  US81369Y1001   XLB               Materials  1.651132  0.9   \n", "5            5  US81369Y8527   XLC  Communication Services  0.089132  0.9   \n", "6            6  US81369Y4070   XLY  Consumer Discretionary -2.724988  0.9   \n", "7            7  US81369Y7040   XLI             Industrials -0.521642  0.9   \n", "8            8  US81369Y8600  XLRE             Real Estate  5.518701  0.9   \n", "9            9  US81369Y8865   XLU               Utilities -0.359860  0.9   \n", "10          10  US81369Y6059   XLF              Financials  0.326564  0.9   \n", "\n", "       min        wt  \n", "0   0.0002  0.160648  \n", "1   0.0002  0.042085  \n", "2   0.0002  0.170860  \n", "3   0.0002  0.150777  \n", "4   0.0002  0.097031  \n", "5   0.0002  0.044965  \n", "6   0.0002  0.000200  \n", "7   0.0002  0.024606  \n", "8   0.0002  0.225950  \n", "9   0.0002  0.029998  \n", "10  0.0002  0.052879  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "id": "33253291-2ede-4861-874a-058afcfffcf4", "metadata": {"scrolled": true}, "source": ["collecting er for aipex"]}, {"cell_type": "code", "execution_count": 16, "id": "844dbdcc-5c54-4da5-bb2a-cb30beb19c02", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["platform/pre_consolidated_er/Monthly/pre_portfolio_er_consolidate_2025-05-29.csv\n", "ReceivedConstraint/Sendout_AIPEXTR_Caps.xlsx\n"]}], "source": ["bucket = 'micro-ops-output'\n", "folder = 'platform/pre_consolidated_er/Monthly/'\n", "filename_pre = \"pre_portfolio_er_consolidate_2025-05-29.csv\"\n", "download_s3file(bucket,folder,filename_pre)\n", "bucket = 'index-build'\n", "folder = 'ReceivedConstraint/'\n", "filename_sendout = 'Sendout_AIPEXTR_Caps.xlsx'\n", "download_s3file(bucket,folder,filename_sendout)"]}, {"cell_type": "code", "execution_count": 18, "id": "2fd6effa-20ea-4a7d-8558-9bbfafff00cb", "metadata": {}, "outputs": [], "source": ["filename_sector = \"aipex_sector.csv\"\n", "df_sendout = pd.read_excel(fin_path+filename_sendout,index_col=False)\n", "df_pre = pd.read_csv(fin_path+filename_pre,index_col=False)\n", "df_sector = pd.read_csv(fin_path+filename_sector,index_col=False)\n", "df_sendout = df_sendout.rename(columns={'ISIN':'isin'})\n", "df_sendout = pd.merge(df_sendout,df_pre[['isin','country_code','ER1']],on='isin',how='left')\n", "df_sendout = pd.merge(df_sendout,df_sector,on='isin',how='left')\n", "df_sendout.to_csv(fin_path+\"aipex.csv\",index=False)"]}, {"cell_type": "markdown", "id": "3fd2ea0b-ec7a-4e47-b345-e5e0a124cd61", "metadata": {}, "source": ["aipex close price collection"]}, {"cell_type": "code", "execution_count": 19, "id": "f265a9e1-7428-4362-a516-9a2943bf91d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error fetching data for US5303071071: 'NoneType' object has no attribute 'empty'\n", "Error fetching data for US5260573028: 'NoneType' object has no attribute 'empty'\n", "Error fetching data for US5797801074: 'NoneType' object has no attribute 'empty'\n", "Error fetching data for US92556H1077: 'NoneType' object has no attribute 'empty'\n"]}], "source": ["# function to get close price\n", "file_name = \"aipex.csv\"\n", "out_put_file_name = \"aipex_close.csv\"\n", "s_date = (datetime.now() - relativedelta(years=2)).strftime(\"%Y-%m-%d\")\n", "e_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "batch_size = 10  # Define batch size (tune this based on performance)\n", "\n", "# Load the dataset\n", "df = pd.read_csv(fin_path + file_name, index_col=False)\n", "df['country_code'] = df['country_code'].fillna('USA')\n", "\n", "# Create the date DataFrame\n", "df_date = pd.DataFrame(pd.date_range(start=s_date, end=e_date, freq='B').strftime(\"%Y-%m-%d\").tolist(), columns=['date'])\n", "\n", "# Function to fetch and merge close prices for a batch of ISINs\n", "def fetch_close_price_batch(isin_batch, ccode_batch):\n", "    results = []\n", "    for isin, ccode in zip(isin_batch, ccode_batch):\n", "        try:\n", "            df1 = close_price(isin, s_date, e_date, ccode)\n", "            if not df1.empty:\n", "                results.append(df1)\n", "        except Exception as e:\n", "            print(f\"Error fetching data for {isin}: {e}\")\n", "    return results\n", "\n", "# Split the ISINs into batches\n", "batches = [df.iloc[i:i + batch_size] for i in range(0, len(df), batch_size)]\n", "\n", "# Use ThreadPoolExecutor for multithreading within each batch\n", "with concurrent.futures.ThreadPoolExecutor() as executor:\n", "    for batch in batches:\n", "        isin_batch = batch['isin'].tolist()\n", "        ccode_batch = batch['country_code'].tolist()\n", "\n", "        # Submit a batch to fetch in parallel\n", "        future = executor.submit(fetch_close_price_batch, isin_batch, ccode_batch)\n", "        \n", "        try:\n", "            # Get the result for the current batch\n", "            batch_results = future.result()\n", "            \n", "            # Merge each DataFrame in the batch_results with df_date\n", "            for df1 in batch_results:\n", "                df_date = pd.merge(df_date, df1, on='date', how='left').drop_duplicates('date')\n", "        except Exception as e:\n", "            print(f\"Error processing batch: {e}\")\n", "df_date.to_csv(fin_path+out_put_file_name,index=False)   "]}, {"cell_type": "markdown", "id": "77cb8264-7459-4793-a7d1-ff8dad603411", "metadata": {}, "source": ["AIPEX optimization using catboost ER"]}, {"cell_type": "code", "execution_count": 20, "id": "56e6f898", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sector_name</th>\n", "      <th>wt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Information Technology</td>\n", "      <td>0.3175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Financials</td>\n", "      <td>0.1411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Industrials</td>\n", "      <td>0.0873</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Real Estate</td>\n", "      <td>0.0214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Health Care</td>\n", "      <td>0.0960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Materials</td>\n", "      <td>0.0193</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Communication Services</td>\n", "      <td>0.0964</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Consumer Staples</td>\n", "      <td>0.0586</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Consumer Discretionary</td>\n", "      <td>0.1075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Energy</td>\n", "      <td>0.0302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Utilities</td>\n", "      <td>0.0246</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               sector_name      wt\n", "0   Information Technology  0.3175\n", "1               Financials  0.1411\n", "2              Industrials  0.0873\n", "3              Real Estate  0.0214\n", "4              Health Care  0.0960\n", "5                Materials  0.0193\n", "6   Communication Services  0.0964\n", "7         Consumer Staples  0.0586\n", "8   Consumer Discretionary  0.1075\n", "9                   Energy  0.0302\n", "10               Utilities  0.0246"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# read the sector_df from fin_path\n", "sector_df = pd.read_csv(fin_path + 'spy_sector_wise_weights.csv', index_col=False)\n", "sector_df"]}, {"cell_type": "code", "execution_count": 47, "id": "dbb708d2-8de6-4dea-9139-05c6317de729", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["folder already there\n", "0.3175 -------------- nan\n", "0.1411 -------------- nan\n", "0.0873 -------------- nan\n", "0.0214 -------------- nan\n", "0.096 -------------- nan\n", "0.0193 -------------- nan\n", "0.0964 -------------- nan\n", "0.0586 -------------- nan\n", "0.1075 -------------- nan\n", "0.0302 -------------- nan\n", "0.0246 -------------- nan\n", "160\n", "Total ISINs in input: 159\n", "Total ISINs in close: 1020\n", "Common ISINs: 159\n", "method is max_returns and type is <class 'str'>\n", "iteratino number 0\n", "mid is 50\n", "min vector is 0      0.0002\n", "1      0.0002\n", "2      0.0002\n", "3      0.0002\n", "4      0.0002\n", "        ...  \n", "154    0.0002\n", "155    0.0002\n", "156    0.0002\n", "157    0.0002\n", "158    0.0002\n", "Name: min, Length: 159, dtype: float64\n", "risk limit is 10\n", "min vector is <class 'pandas.core.series.Series'>\n", "max vector is 0      0.005531\n", "1      0.005636\n", "2      0.007985\n", "3      0.075000\n", "4      0.008099\n", "         ...   \n", "154    0.021635\n", "155    0.010161\n", "156    0.007986\n", "157    0.010795\n", "158    0.022328\n", "Name: max, Length: 159, dtype: float64\n"]}, {"name": "stdout", "output_type": "stream", "text": ["147.77362813680418\n", "iteratino number 1\n", "mid is 25\n", "min vector is 0      0.0002\n", "1      0.0002\n", "2      0.0002\n", "3      0.0002\n", "4      0.0002\n", "        ...  \n", "154    0.0002\n", "155    0.0002\n", "156    0.0002\n", "157    0.0002\n", "158    0.0002\n", "Name: min, Length: 159, dtype: float64\n", "risk limit is 10\n", "min vector is <class 'pandas.core.series.Series'>\n", "max vector is 0      0.005531\n", "1      0.005636\n", "2      0.007985\n", "3      0.075000\n", "4      0.008099\n", "         ...   \n", "154    0.021635\n", "155    0.010161\n", "156    0.007986\n", "157    0.010795\n", "158    0.022328\n", "Name: max, Length: 159, dtype: float64\n", "146.88779600069446\n", "iteratino number 2\n", "mid is 12\n", "min vector is 0      0.0002\n", "1      0.0002\n", "2      0.0002\n", "3      0.0002\n", "4      0.0002\n", "        ...  \n", "154    0.0002\n", "155    0.0002\n", "156    0.0002\n", "157    0.0002\n", "158    0.0002\n", "Name: min, Length: 159, dtype: float64\n", "risk limit is 10\n", "min vector is <class 'pandas.core.series.Series'>\n", "max vector is 0      0.005531\n", "1      0.005636\n", "2      0.007985\n", "3      0.075000\n", "4      0.008099\n", "         ...   \n", "154    0.021635\n", "155    0.010161\n", "156    0.007986\n", "157    0.010795\n", "158    0.022328\n", "Name: max, Length: 159, dtype: float64\n", "143.29313964456856\n", "iteratino number 3\n", "mid is 6\n", "min vector is 0      0.0002\n", "1      0.0002\n", "2      0.0002\n", "3      0.0002\n", "4      0.0002\n", "        ...  \n", "154    0.0002\n", "155    0.0002\n", "156    0.0002\n", "157    0.0002\n", "158    0.0002\n", "Name: min, Length: 159, dtype: float64\n", "risk limit is 10\n", "min vector is <class 'pandas.core.series.Series'>\n", "max vector is 0      0.005531\n", "1      0.005636\n", "2      0.007985\n", "3      0.075000\n", "4      0.008099\n", "         ...   \n", "154    0.021635\n", "155    0.010161\n", "156    0.007986\n", "157    0.010795\n", "158    0.022328\n", "Name: max, Length: 159, dtype: float64\n", "131.4791375874866\n", "iteratino number 4\n", "mid is 3\n", "min vector is 0      0.0002\n", "1      0.0002\n", "2      0.0002\n", "3      0.0002\n", "4      0.0002\n", "        ...  \n", "154    0.0002\n", "155    0.0002\n", "156    0.0002\n", "157    0.0002\n", "158    0.0002\n", "Name: min, Length: 159, dtype: float64\n", "risk limit is 10\n", "min vector is <class 'pandas.core.series.Series'>\n", "max vector is 0      0.005531\n", "1      0.005636\n", "2      0.007985\n", "3      0.075000\n", "4      0.008099\n", "         ...   \n", "154    0.021635\n", "155    0.010161\n", "156    0.007986\n", "157    0.010795\n", "158    0.022328\n", "Name: max, Length: 159, dtype: float64\n", "107.67946422584603\n", "iteratino number 5\n", "mid is 1\n", "min vector is 0      0.0002\n", "1      0.0002\n", "2      0.0002\n", "3      0.0002\n", "4      0.0002\n", "        ...  \n", "154    0.0002\n", "155    0.0002\n", "156    0.0002\n", "157    0.0002\n", "158    0.0002\n", "Name: min, Length: 159, dtype: float64\n", "risk limit is 10\n", "min vector is <class 'pandas.core.series.Series'>\n", "max vector is 0      0.005531\n", "1      0.005636\n", "2      0.007985\n", "3      0.075000\n", "4      0.008099\n", "         ...   \n", "154    0.021635\n", "155    0.010161\n", "156    0.007986\n", "157    0.010795\n", "158    0.022328\n", "Name: max, Length: 159, dtype: float64\n", "76.47179777468732\n", "iteratino number 6\n", "returning the best estimate\n", "min vector is 0      0.0002\n", "1      0.0002\n", "2      0.0002\n", "3      0.0002\n", "4      0.0002\n", "        ...  \n", "154    0.0002\n", "155    0.0002\n", "156    0.0002\n", "157    0.0002\n", "158    0.0002\n", "Name: min, Length: 159, dtype: float64\n", "risk limit is 10\n", "min vector is <class 'pandas.core.series.Series'>\n", "max vector is 0      0.005531\n", "1      0.005636\n", "2      0.007985\n", "3      0.075000\n", "4      0.008099\n", "         ...   \n", "154    0.021635\n", "155    0.010161\n", "156    0.007986\n", "157    0.010795\n", "158    0.022328\n", "Name: max, Length: 159, dtype: float64\n", "[0.0002     0.00563624 0.00798473 0.0002     0.00809934 0.00330377\n", " 0.0038491  0.00649089 0.0002     0.00957692 0.00431793 0.01761674\n", " 0.0087617  0.0002     0.02139596 0.0002     0.01316748 0.00625485\n", " 0.00505309 0.02347518 0.00416648 0.00339686 0.00582101 0.03014112\n", " 0.02130442 0.00020001 0.0002     0.01139405 0.00624233 0.0002\n", " 0.0002     0.00331923 0.00020002 0.0002     0.03040802 0.0002\n", " 0.0002     0.02818937 0.0002     0.01204529 0.0002     0.01885303\n", " 0.00584292 0.00550761 0.02355372 0.0002     0.006814   0.0002\n", " 0.01120743 0.00352315 0.00621974 0.0002     0.0002     0.0002\n", " 0.0002     0.00798504 0.0002     0.0002     0.0002     0.00743942\n", " 0.00870146 0.00614331 0.0002     0.0002     0.01106687 0.00599798\n", " 0.0002     0.02508231 0.0002     0.00417021 0.0002     0.0002\n", " 0.0002     0.0009058  0.0002     0.0002     0.00240242 0.02028557\n", " 0.0002     0.0002     0.0002     0.00798112 0.0002     0.00356241\n", " 0.01231477 0.00434252 0.0002     0.0002     0.00020001 0.01149324\n", " 0.00410116 0.0002     0.0002     0.075      0.00681985 0.00499915\n", " 0.0042971  0.0002     0.0002     0.0002     0.03057355 0.0002\n", " 0.0002     0.0002     0.03328639 0.0002     0.0002     0.0002\n", " 0.0002     0.00530666 0.0002     0.0002     0.00443138 0.01632104\n", " 0.01109208 0.0002     0.0002     0.0002     0.0002     0.0002\n", " 0.0002     0.0152587  0.00556959 0.0002     0.01341366 0.01571687\n", " 0.0002     0.01618079 0.01300932 0.00841863 0.00564512 0.0002\n", " 0.0002     0.06110753 0.00879189 0.0002     0.02047972 0.0002\n", " 0.0002     0.0002     0.0002     0.00344774 0.00426938 0.0002\n", " 0.01668546 0.00932808 0.0002     0.0002     0.0002     0.00535184\n", " 0.01004265 0.0002     0.01108438 0.00361501 0.0002     0.01016105\n", " 0.00798603 0.0002     0.0002    ] w\n", "the enan is 0.2737651113307024\n"]}], "source": ["target_enan = 0.38 # target ena/n\n", "output_folder = 'output'\n", "path = fin_path\n", "try:\n", "    os.mkdir(path + output_folder)\n", "except:\n", "    print(f'folder already there')\n", "\n", "risk_limit = 10\n", "min_return = -10\n", "is_hard_constraint = True\n", "objj = 'max_returns'\n", "\n", "file_sec = 'aipex_cat_historic_er1'\n", "\n", "today_date =  datetime.now().strftime(\"%Y-%m-%d\")\n", "df_sec = sector_df\n", "df_sec = df_sec[['sector_name','wt']].rename(columns={'wt':'sector_weight'})\n", "df_sec_weight = df_sec.copy(deep=True)\n", "df_sec.rename(columns={'sector_name':'isin'},inplace=True)\n", "df_er = pd.read_csv(fin_path+\"/aipex.csv\",index_col=False,encoding='windows-1252')\n", "  \n", "df_fin = pd.DataFrame()\n", "df_s = df_sec\n", "df_g = df_er[['isin','sector_name','ER1','Caps']].groupby('sector_name')\n", "df_sec_weight_m = df_sec_weight\n", "df_sec_weight_m.rename(columns={'sector_weight':'sector_weight_max'},inplace=True)\n", "for k in range(len(df_s)):  \n", "    sec = df_s.iloc[k]['isin']\n", "    print(df_s.iloc[k]['sector_weight'],\"--------------\",float(\"nan\"))\n", "    if not math.isnan(float(df_s.iloc[k]['sector_weight'])) and float(df_s.iloc[k]['sector_weight']) > 0:\n", "        per = math.ceil((float(df_s.iloc[k]['sector_weight'])) * 100) + 5 #\n", "        df_ss = df_g.get_group(sec)\n", "        df_ss = df_ss.dropna()\n", "        df_ss = df_ss.drop_duplicates(subset=['isin'],keep='first')\n", "        df_ss = df_ss.sort_values(by=['ER1'],ascending=False).reset_index().drop(columns=['index'])\n", "        df_ss = df_ss.iloc[:per]\n", "        df_fin = pd.concat([df_fin,df_ss],ignore_index=True)\n", "print(len(df_fin))        \n", "df_fin = pd.merge(df_fin,df_sec_weight,on='sector_name',how='left')\n", "\n", "df_fin.rename(columns={'ER1':'er','Caps':'max'},inplace=True)\n", "df_fin['sector_min'] = df_fin['sector_weight_max'] - (df_fin['sector_weight_max'] * 0.75) \n", "df_fin['sector_max'] = (df_fin['sector_weight_max'] * 0.75) + df_fin['sector_weight_max']\n", "df_fin['min'] = 2e-4\n", "\n", "df_fin['er'] = df_fin['er'].astype(float)\n", "df_fin['wt'] = 1/len(df_fin)\n", "# if the max in df_fin is 0 then remove it\n", "df_fin = df_fin[df_fin['max'] != 0]    \n", "data_merge = df_fin\n", "close_df = pd.read_csv(fin_path + \"aipex_close.csv\",index_col=False)\n", "close_df['date'] = pd.to_datetime(close_df['date'],format='%Y-%m-%d')\n", "end_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "output_path = fin_path + '/' + output_folder + '/'\n", "mr_long_aipex(output_folder,risk_limit,min_return,target_enan,data_merge,close_df,end_date)\n"]}, {"cell_type": "markdown", "id": "3f1131eb-b1c8-47db-8257-94286795e9a2", "metadata": {}, "source": ["pre pair delivery file"]}, {"cell_type": "code", "execution_count": 48, "id": "c2e7de07-519b-4ae6-87e3-330b8507dbc0", "metadata": {}, "outputs": [], "source": ["today_date =  datetime.now().strftime(\"%Y-%m-%d\")\n", "df = pd.read_excel(fin_path+\"output/aipex_er_new_cap_\"+today_date+\".xlsx\",index_col=False)\n", "df.rename(columns={'wt':'Wt_Allocation'},inplace=True)\n", "df_a = pd.read_excel(fin_path+\"Sendout_AIPEXTR_Caps.xlsx\",index_col=False)"]}, {"cell_type": "code", "execution_count": 51, "id": "27376676-92b3-413e-840c-12aecdefc282", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Wt_Allocation'].sum()"]}, {"cell_type": "code", "execution_count": 50, "id": "b0561d72-e618-444b-af7b-0ea730e0bf6d", "metadata": {}, "outputs": [], "source": ["tw = df['Wt_Allocation'].sum()\n", "for i in range(len(df)):\n", "    df.at[i,'Wt_Allocation'] =df.iloc[i]['Wt_Allocation']/tw"]}, {"cell_type": "code", "execution_count": 52, "id": "e31ab163-f227-4463-b698-51eb07b8d93b", "metadata": {}, "outputs": [], "source": ["df.rename(columns={'isin':'ISIN'},inplace=True)\n", "df_a = pd.merge(df_a,df[['ISIN','Wt_Allocation']],on='ISIN',how='left')\n", "df_a = df_a.fillna(0.00000)\n", "df_a.rename(columns={'Wt_Allocation':'Wts'},inplace=True)\n", "  "]}, {"cell_type": "code", "execution_count": 53, "id": "508e184a-26ff-48bd-b4e1-c071b19f19ec", "metadata": {}, "outputs": [], "source": ["excel_file = fin_path + \"AIEQ_rebalance_file_mvo_\"+today_date+\".xlsx\"\n", "\n", "# Create an Excel writer using xlsxwriter as the engine\n", "with pd.ExcelWriter(excel_file, engine=\"xlsxwriter\") as writer:\n", "    # Write DataFrame to Excel\n", "    df_a.to_excel(writer, index=False, sheet_name=\"Sheet1\")\n", "    \n", "    # Access the workbook and worksheet\n", "    workbook = writer.book\n", "    worksheet = writer.sheets[\"Sheet1\"]\n", "    \n", "    # Define a format for the header\n", "    header_format = workbook.add_format({\n", "        \"valign\": \"vcenter\",\n", "        \"bg_color\": \"#1F497D\",\n", "        \"font_color\": \"#ffffff\",\n", "        \"font_size\" : '10'\n", "        #\"border\": 1\n", "    })\n", "    \n", "    # Apply the format to the header row\n", "    for col_num, value in enumerate(df_a.columns.values):\n", "        worksheet.write(0, col_num, value, header_format)"]}, {"cell_type": "markdown", "id": "eabd31d4-d242-458a-917e-d647e769b628", "metadata": {}, "source": ["############################################################################################"]}, {"cell_type": "markdown", "id": "7285550f-8c37-4f5c-bc26-248b863c5d40", "metadata": {}, "source": ["Consolidate er collections"]}, {"cell_type": "code", "execution_count": 199, "id": "f72ec081-ab7d-4290-8344-7db0767b9286", "metadata": {}, "outputs": [], "source": ["auth_dict = {'prod': {'aws_access_key': '********************',\n", "        'aws_secret_access_key': 'xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV',\n", "        'aws_host': 'search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com',\n", "        'aws_region': 'us-east-1',\n", "        'aws_service': 'es',\n", "        'port': 443\n", "    },\n", "    'preprod': {\n", "        'aws_access_key': '********************',\n", "        'aws_secret_access_key': 'oVD9ZhQRCGz74RzQpIqqiyIvyIXIpRIElO+8ftvb',\n", "        'aws_host': 'search-training-data-7llze3ehbf3ry4hu5rwlu662ye.us-east-1.es.amazonaws.com',\n", "        'aws_region': 'us-east-1',\n", "        'aws_service': 'es',\n", "        'port': 443\n", "    }\n", "}\n", "env = 'prod'\n", "\n", "\n", "# function to create index\n", "def create_index(index_name):\n", "    print(\"create index\",index_name)\n", "    index_body = {\n", "        'settings': {\n", "            'index': {\n", "                'number_of_shards': 4\n", "            }\n", "        }\n", "    }\n", "    try:\n", "        es.indices.create(index_name, body=index_body)\n", "        indices = str(es.indices.get_alias().keys())\n", "    except:\n", "        print(traceback.format_exc())\n", "\n", "#function to connect elastic search\n", "def connect_openSearch():\n", "    auth = AWSRequestsAuth(aws_access_key=auth_dict[env]['aws_access_key'],\n", "                       aws_secret_access_key=auth_dict[env]['aws_secret_access_key'],\n", "                       aws_host=auth_dict[env]['aws_host'],\n", "                       aws_region=auth_dict[env]['aws_region'],\n", "                       aws_service=auth_dict[env]['aws_service'])\n", "    host = auth_dict[env]['aws_host']\n", "    port = auth_dict[env]['port']\n", "    hosts = [f'https://{host}:{port}']\n", "    client = OpenSearch(\n", "        hosts=hosts,\n", "        port=port,\n", "        http_auth=auth,\n", "        connection_class=RequestsHttpConnection\n", "     )\n", "    return client\n", "    \n", "#geting data from s3 folder\n", "def get_data_from_s3(s3, bucket_name, filename):\n", "    try:\n", "        s3_response_object = s3.get_object(Bucket=bucket_name, Key=filename)\n", "        object_content = s3_response_object['Body'].read()\n", "        ext = os.path.splitext(filename)[1]\n", "        if ext == \".xlsx\":\n", "            excel_object = pd.ExcelFile(object_content)\n", "            df = excel_object.parse()\n", "        else:\n", "            df = pd.read_csv(io.BytesIO(object_content))\n", "        # filename = get_latest_obj(s3, bucket_name, filename)\n", " \n", "#         df = pd.read_excel(object_content, engine='xlrd')\n", "    except:\n", "        print(traceback.format_exc())\n", "        return None\n", "    return df\n", "schedular=\"Monthly\"\n", "\n", "#aws access key and token\n", "auth = AWSRequestsAuth(aws_access_key='********************',\n", "                       aws_secret_access_key='xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV',\n", "                       aws_host='search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com',\n", "                       aws_region='us-east-1',\n", "                       aws_service='es')\n", "es=connect_openSearch()\n", "global indices\n", "indices=str(es.indices.get_alias().keys())\n", "\n", "#function to get data from elastic search\n", "def get_es_data(isin, dates, index_prefix):\n", "    data=[]\n", "    for year in range(dates[0], dates[1] + 1):\n", "        q_total = '{\"query\":{\"bool\": {\"must\":[{\"bool\":{\"should\":[{\"match\":{\"isin\":\"'+isin+'\"}}]}}]}}}'\n", "        try:\n", "            result = es.search(index=f\"{index_prefix}_{year}\", body=q_total, size=10000,request_timeout=6000)\n", "            for rs in result['hits']['hits']:\n", "                es_data=rs['_source']\n", "                data.append(es_data)\n", "        except:\n", "            print(f'ES data for {index_prefix} not present for {year}.')\n", "    df=pd.DataFrame(data)\n", "    if len(df) == 0:\n", "        return df\n", "    #df['date']=pd.to_datetime(df['date'],format='%Y-%m-%d')\n", "    df.sort_values('date', ascending=True, inplace=True)\n", "    df.reset_index(inplace=True, drop=True)\n", "    return df"]}, {"cell_type": "code", "execution_count": 200, "id": "17685ac5-a118-4337-a31c-5fb851e0bdc7", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error processing US3981823038: 'schedular'\n", "Error processing US1156371007: 'schedular'\n", "Error processing KYG9572D1034: 'schedular'\n", "Error processing US22002T1088: 'schedular'\n", "Error processing US21873S1087: 'schedular'\n", "Error processing US18539C2044: 'schedular'\n", "Error processing US35137L2043: 'schedular'\n", "Error processing US02079K1079: 'schedular'\n", "Error processing US4228062083: 'schedular'\n", "Error processing US5303071071: 'schedular'\n", "Error processing US5260573028: 'schedular'\n", "Error processing US5797801074: 'schedular'\n", "Error processing US6378701063: 'schedular'\n", "Error processing US65249B2088: 'schedular'\n", "Error processing US92556H1077: 'schedular'\n", "Error processing US78781J1097: 'schedular'\n", "Error processing US8322482071: 'schedular'\n", "Error processing US80004C2008: 'schedular'\n", "Error processing US85914M1071: 'schedular'\n", "Error processing US81764X1037: 'schedular'\n", "Error processing US92333F1012: 'schedular'\n"]}], "source": ["path =os.getcwd() \n", "model_index = 'eq_temp_model'\n", "col_name = 'predicted_er'\n", "file_name = 'aipex_consolidated_er'\n", "df_l = pd.read_csv(fin_path+\"/aipex.csv\",index_col=False)\n", "ll = df_l['isin'].tolist()\n", "s_date = '2024-11-01'\n", "e_date = '2024-12-25'\n", "s_date_obj = datetime.strptime(s_date, '%Y-%m-%d')\n", "s_year = s_date_obj.year\n", "e_date_obj = datetime.strptime(e_date, '%Y-%m-%d')\n", "e_year = e_date_obj.year\n", "frequency = 'Monthly'\n", "df_date = pd.DataFrame(pd.date_range(start=s_date, end=e_date, freq='B').strftime(\"%Y-%m-%d\"), columns=['date'])\n", "\n", "batch_size = 10  # Define the batch size\n", "\n", "\n", "# Function to get predictions from Elasticsearch\n", "def process_data(isi):\n", "    try:\n", "        df = get_es_data(isi, [s_year, e_year], model_index)\n", "        df = df[df['schedular'] == frequency][['date', col_name]].rename(columns={col_name: isi})\n", "        return df\n", "    except Exception as e:\n", "        print(f\"Error processing {isi}: {e}\")\n", "        return pd.DataFrame()  # Return an empty DataFrame in case of an error\n", "\n", "# Function to process a batch of ISINs\n", "def process_batch(isi_batch):\n", "    results = []\n", "    with concurrent.futures.ThreadPoolExecutor() as executor:\n", "        futures = {executor.submit(process_data, isi): isi for isi in isi_batch}\n", "        for future in concurrent.futures.as_completed(futures):\n", "            try:\n", "                df = future.result()\n", "                if not df.empty:\n", "                    results.append(df)\n", "            except Exception as e:\n", "                print(f\"Error processing batch: {e}\")\n", "    return results\n", "\n", "# Split ISINs into batches\n", "batches = [ll[i:i + batch_size] for i in range(0, len(ll), batch_size)]\n", "\n", "# Process each batch and merge results\n", "for batch in batches:\n", "    batch_results = process_batch(batch)\n", "    \n", "    # Merge each DataFrame from the batch into df_date\n", "    for df in batch_results:\n", "        df_date = pd.merge(df_date, df, on='date', how='left').drop_duplicates('date')\n", "\n", "# Save the final result\n", "df_date.to_csv(os.getcwd() + \"/\" + file_name + \".csv\", index=False) "]}, {"cell_type": "code", "execution_count": 201, "id": "8ea8f697-30a1-424f-9f6d-9c89e039f179", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error processing column US1091941005:\n", "Error processing column US0758871091:\n", "Error processing column US1156372096:\n", "Error processing column US0865161014:\n", "Error processing column CH1300646267:\n", "Error processing column US0905722072:\n", "Error processing column US09062X1037:\n", "Error processing column US09857L1089:\n", "Error processing column US0640581007:\n", "Error processing column US0900431000:\n", "Error processing column US05550J1016:\n", "Error processing column US05722G1004:\n", "Error processing column US89055F1030:\n", "Error processing column US12008R1077:\n", "Error processing column US0921131092:\n", "Error processing column US10316T1043:\n", "Error processing column US09290D1019:\n", "Error processing column US07831C1036:\n", "Error processing column US09627Y1091:\n", "Error processing column US11133T1034:\n", "Error processing column US1101221083:\n", "Error processing column US05561Q2012:\n", "Error processing column US09061G1013:\n", "Error processing column US1167941087:\n", "Error processing column US0565251081:\n", "Error processing column US0846707026:\n", "Error processing column US1220171060:\n", "Error processing column US11120U1051:\n", "Error processing column US26701L1008:\n", "Error processing column US0997241064:\n", "Error processing column US05589G1022:\n", "Error processing column US1011371077:\n", "Error processing column US08265T2087:\n", "Error processing column US1152361010:\n", "Error processing column US05605H1005:\n", "Error processing column US1033041013:\n", "Error processing column US2253101016:\n", "Error processing column US2058871029:\n", "Error processing column US09260D1072:\n", "Error processing column US1729674242:\n", "Error processing column US1011211018:\n", "Error processing column US1271903049:\n", "Error processing column US14149Y1082:\n", "Error processing column US12740C1036:\n", "Error processing column US1491231015:\n", "Error processing column US1489291021:\n", "Error processing column US2005251036:\n", "Error processing column US12504L1098:\n", "Error processing column US1280302027:\n", "Error processing column US14448C1045:\n", "Error processing column US5653941030:\n", "Error processing column US1475281036:\n", "Error processing column US12503M1080:\n", "Error processing column CH0044328745:\n", "Error processing column US1273871087:\n", "Error processing column US12514G1085:\n", "Error processing column US15118V2079:\n", "Error processing column US21037T1097:\n", "Error processing column US2283681060:\n", "Error processing column PA1436583006:\n", "Error processing column US22822V1017:\n", "Error processing column US1270551013:\n", "Error processing column US1508701034:\n", "Error processing column US1924221039:\n", "Error processing column US2298991090:\n", "Error processing column US1713401024:\n", "Error processing column US20717M1036:\n", "Error processing column US1252691001:\n", "Error processing column US1746101054:\n", "Error processing column US14316J1088:\n", "Error processing column US1714841087:\n", "Error processing column US16359R1032:\n", "Error processing column US1699051066:\n", "Error processing column US16119P1084:\n", "Error processing column US1717793095:\n", "Error processing column US1255231003:\n", "Error processing column US1941621039:\n", "Error processing column US1890541097:\n", "Error processing column US1844961078:\n", "Error processing column US12541W2098:\n", "Error processing column US6742152076:\n", "Error processing column US16679L1098:\n", "Error processing column US1720621010:\n", "Error processing column US2017231034:\n", "Error processing column NL0010545661:\n", "Error processing column US20030N1019:\n", "Error processing column US2310211063:\n", "Error processing column US1696561059:\n", "Error processing column US12572Q1058:\n", "Error processing column US2003401070:\n", "Error processing column US15135B1017:\n", "Error processing column US1261171003:\n", "Error processing column US1258961002:\n", "Error processing column US15189T1079:\n", "Error processing column US1910981026:\n", "Error processing column US19260Q1076:\n", "Error processing column US14040H1059:\n", "Error processing column US03064D1081:\n", "Error processing column US2166485019:\n", "Error processing column US19247G1076:\n", "Error processing column US1972361026:\n", "Error processing column US21874C1027:\n", "Error processing column US12653C1080:\n", "Error processing column US20825C1045:\n", "Error processing column US62482R1077:\n", "Error processing column US2172041061:\n", "Error processing column US03073E1055:\n", "Error processing column US1331311027:\n", "Error processing column US2199481068:\n", "Error processing column US2244081046:\n", "Error processing column US22160K1051:\n", "Error processing column US1344291091:\n", "Error processing column US2183521028:\n", "Error processing column US79466L3024:\n", "Error processing column IE0001827041:\n", "Error processing column US2057683029:\n", "Error processing column US1442851036:\n", "Error processing column US1598641074:\n", "Error processing column US2270461096:\n", "Error processing column US22788C1053:\n", "Error processing column US21871X1090:\n", "Error processing column US1727551004:\n", "Error processing column US2210061097:\n", "Error processing column US22160N1090:\n", "Error processing column US1423391002:\n", "Error processing column US14174T1079:\n", "Error processing column US1264081035:\n", "Error processing column US1264021064:\n", "Error processing column US1729081059:\n", "Error processing column US17275R1023:\n", "Error processing column US1924461023:\n", "Error processing column US1270971039:\n", "Error processing column US22052L1044:\n", "Error processing column US2296631094:\n", "Error processing column US1468691027:\n", "Error processing column US1266501006:\n", "Error processing column US2041661024:\n", "Error processing column US18539C1053:\n", "Error processing column US2315611010:\n", "Error processing column US1667641005:\n", "Error processing column US1851231068:\n", "Error processing column US12769G1004:\n", "Error processing column US1474481041:\n", "Error processing column US23282W6057:\n", "Error processing column US2473617023:\n", "Error processing column US25746U1097:\n", "Error processing column US2372661015:\n", "Error processing column US25809K1051:\n", "Error processing column US15677J1088:\n", "Error processing column US26210C1045:\n", "Error processing column US2576511099:\n", "Error processing column US2540671011:\n", "Error processing column US2358511028:\n", "Error processing column US74834L1008:\n", "Error processing column US2441991054:\n", "Error processing column US26614N1028:\n", "Error processing column US23331A1097:\n", "Error processing column US23804L1035:\n", "Error processing column US2435371073:\n", "Error processing column US2566771059:\n", "Error processing column US24703L2025:\n", "Error processing column US4039491000:\n", "Error processing column US25400Q1058:\n", "Error processing column US2533931026:\n", "Error processing column US2546871060:\n", "Error processing column US42250P1030:\n", "Error processing column US2567461080:\n", "Error processing column US2538681030:\n", "Error processing column US26142V1052:\n", "Error processing column US26622P1075:\n", "Error processing column US25659T1079:\n", "Error processing column US2605571031:\n", "Error processing column US2600031080:\n", "Error processing column US25754A2015:\n", "Error processing column GB0022569080:\n", "Error processing column US52661A1088:\n", "Error processing column US2561631068:\n", "Error processing column US2371941053:\n", "Error processing column US2333311072:\n", "Error processing column US2681501092:\n", "Error processing column US23918K1088:\n", "Error processing column US2521311074:\n", "Error processing column US2855121099:\n", "Error processing column US26441C2044:\n", "Error processing column US25179M1036:\n", "Error processing column US26603R1068:\n", "Error processing column US2674751019:\n", "Error processing column US1096411004:\n", "Error processing column US2786421030:\n", "Error processing column US2788651006:\n", "Error processing column US2944291051:\n", "Error processing column US2091151041:\n", "Error processing column US2772761019:\n", "Error processing column US2810201077:\n", "Error processing column US5184391044:\n", "Error processing column US28414H1032:\n", "Error processing column BMG3223R1088:\n", "Error processing column US2987361092:\n", "Error processing column US29472R1086:\n", "Error processing column US29261A1007:\n", "Error processing column US29355A1079:\n", "Error processing column US2910111044:\n", "Error processing column US29084Q1004:\n", "Error processing column US2774321002:\n", "Error processing column US29362U1043:\n", "Error processing column US26875P1012:\n", "Error processing column US0367521038:\n", "Error processing column US29358P1012:\n", "Error processing column US29670E1073:\n", "Error processing column US29414B1044:\n", "Error processing column US29452E1010:\n", "Error processing column US29444U7000:\n", "Error processing column US30040W1080:\n", "Error processing column US29605J1060:\n", "Error processing column US26884L1098:\n", "Error processing column BMG3075P1014:\n", "Error processing column US29476L1070:\n", "Error processing column US29530P1021:\n", "Error processing column US28618M1062:\n", "Error processing column BMG3198U1027:\n", "Error processing column IE00B8KQN827:\n", "Error processing column US29364G1031:\n", "Error processing column US2971781057:\n", "Error processing column NL0013056914:\n", "Error processing column US29786A1060:\n", "Error processing column US30034W1062:\n", "Error processing column US27579R1041:\n", "Error processing column US28176E1082:\n", "Error processing column US29977A1051:\n", "Error processing column US30063P1057:\n", "Error processing column US3020811044:\n", "Error processing column US30161N1019:\n", "Error processing column US3453708600:\n", "Error processing column US30161Q1040:\n", "Error processing column US26969P1084:\n", "Error processing column US30212P3038:\n", "Error processing column US30225T1025:\n", "Error processing column US1651677353:\n", "Error processing column US3021301094:\n", "Error processing column US30214U1025:\n", "Error processing column US31847R1023:\n", "Error processing column US34964C1062:\n", "Error processing column US31428X1063:\n", "Error processing column US3029411093:\n", "Error processing column US25278X1090:\n", "Error processing column US33768G1076:\n", "Error processing column US3119001044:\n", "Error processing column US35671D8570:\n", "Error processing column US31946M1036:\n", "Error processing column US3030751057:\n", "Error processing column US31488V1070:\n", "Error processing column US3379321074:\n", "Error processing column US3156161024:\n", "Error processing column US3167731005:\n", "Error processing column US31620M1062:\n", "Error processing column US33829M1018:\n", "Error processing column US3205171057:\n", "Error processing column US3032501047:\n", "Error processing column US3377381088:\n", "Error processing column US32020R1095:\n", "Error processing column IE00BWT6H894:\n", "Error processing column US3434121022:\n", "Error processing column US3024913036:\n", "Error processing column US3025201019:\n", "Error processing column US31620R3030:\n", "Error processing column US34354P1057:\n", "Error processing column US1999081045:\n", "Error processing column US3397501012:\n", "Error processing column US82452J1097:\n", "Error processing column US3364331070:\n", "Error processing column US34959E1091:\n", "Error processing column US32054K1034:\n", "Error processing column KYG3730V1059:\n", "Error processing column US35137L1052:\n", "Error processing column US3137451015:\n", "Error processing column US34959J1088:\n", "Error processing column US35909D1090:\n", "Error processing column GB00BDSFG982:\n", "Error processing column US3138551086:\n", "Error processing column US3614481030:\n", "Error processing column KYG393871085:\n", "Error processing column US6687711084:\n", "Error processing column US3696043013:\n", "Error processing column US36828A1016:\n", "Error processing column US3647601083:\n", "Error processing column US3802371076:\n", "Error processing column US37637Q1058:\n", "Error processing column US36266G1076:\n", "Error processing column US3695501086:\n", "Error processing column US2193501051:\n", "Error processing column LU0974299876:\n", "Error processing column US37045V1008:\n", "Error processing column US37959E1029:\n", "Error processing column US3773221029:\n", "Error processing column US3755581036:\n", "Error processing column US40131M1099:\n", "Error processing column US36467J1088:\n", "Error processing column US3841091040:\n", "Error processing column US3703341046:\n", "Error processing column US3795772082:\n", "Error processing column US36467W1099:\n", "Error processing column US02079K3059:\n", "Error processing column US3719011096:\n", "Error processing column US37940X1028:\n", "Error processing column US3886891015:\n", "Error processing column US3989051095:\n", "Error processing column US3687361044:\n", "Error processing column US3724601055:\n", "Error processing column US37637K1088:\n", "Error processing column US3848021040:\n", "Error processing column US38141G1040:\n", "Error processing column US36262G1013:\n", "Error processing column US16115Q3083:\n", "Error processing column US4485791028:\n", "Error processing column GB00BD9G2S12:\n", "Error processing column US4062161017:\n", "Error processing column CH0114405324:\n", "Error processing column US40171V1008:\n", "Error processing column US40637H1095:\n", "Error processing column US4370761029:\n", "Error processing column US4464131063:\n", "Error processing column US4281031058:\n", "Error processing column US4165151048:\n", "Error processing column US4228061093:\n", "Error processing column US4180561072:\n", "Error processing column US40412C1018:\n", "Error processing column US4461501045:\n", "Error processing column US4415931009:\n", "Error processing column US4330001060:\n", "Error processing column US4074971064:\n", "Error processing column US40434L1052:\n", "Error processing column US7707001027:\n", "Error processing column US4368932004:\n", "Error processing column US4385161066:\n", "Error processing column US42824C1099:\n", "Error processing column US4364401012:\n", "Error processing column US43300A2033:\n", "Error processing column US42226K1051:\n", "Error processing column US0936711052:\n", "Error processing column US4435731009:\n", "Error processing column US44107P1049:\n", "Error processing column US4278661081:\n", "Error processing column US4404521001:\n", "Error processing column US4435106079:\n", "Error processing column US4448591028:\n", "Error processing column US42226A1079:\n", "Error processing column US8064071025:\n", "Error processing column US45168D1046:\n", "Error processing column US45867G1013:\n", "Error processing column US4592001014:\n", "Error processing column US45167R1041:\n", "Error processing column US4595061015:\n", "Error processing column US45866F1049:\n", "Error processing column US4432011082:\n", "Error processing column US4511071064:\n", "Error processing column US45841N1072:\n", "Error processing column US45780R1014:\n", "Error processing column US46187W1071:\n", "Error processing column US45674M1018:\n", "Error processing column US4576693075:\n", "Error processing column US4523271090:\n", "Error processing column US4581401001:\n", "Error processing column US4571871023:\n", "Error processing column US46222L1089:\n", "Error processing column US4612021034:\n", "Error processing column US45337C1027:\n", "Error processing column US4577301090:\n", "Error processing column US4622221004:\n", "Error processing column US46266C1053:\n", "Error processing column US4606901001:\n", "Error processing column US3666511072:\n", "Error processing column US4601461035:\n", "Error processing column US45687V1061:\n", "Error processing column US45378A1060:\n", "Error processing column US46120E6023:\n", "Error processing column US79589L1061:\n", "Error processing column US46284V1017:\n", "Error processing column IE00B4Q5ZN47:\n", "Error processing column US4657411066:\n", "Error processing column US4778391049:\n", "Error processing column IE00BY7QL619:\n", "Error processing column BMG491BT1088:\n", "Error processing column US4663131039:\n", "Error processing column US45073V1089:\n", "Error processing column US4523081093:\n", "Error processing column US47233W1099:\n", "Error processing column US4456581077:\n", "Error processing column US4781601046:\n", "Error processing column US46625H1005:\n", "Error processing column US46817M1071:\n", "Error processing column US4262811015:\n", "Error processing column US49271V1008:\n", "Error processing column US48020Q1076:\n", "Error processing column US50155Q1004:\n", "Error processing column US4972661064:\n", "Error processing column US48242W1062:\n", "Error processing column US4878361082:\n", "Error processing column US49338L1035:\n", "Error processing column US4932671088:\n", "Error processing column US49456B1017:\n", "Error processing column US4988941047:\n", "Error processing column US4824801009:\n", "Error processing column US4943681035:\n", "Error processing column US48251W1045:\n", "Error processing column US5007541064:\n", "Error processing column US49446R1095:\n", "Error processing column US1431301027:\n", "Error processing column US1912161007:\n", "Error processing column US49845K1016:\n", "Error processing column US49177J1025:\n", "Error processing column US02215L2097:\n", "Error processing column US49803T3005:\n", "Error processing column US5011471027:\n", "Error processing column US23345M1071:\n", "Error processing column US50077B2079:\n", "Error processing column US49714P1084:\n", "Error processing column US4990491049:\n", "Error processing column US5404241086:\n", "Error processing column US5149521008:\n", "Error processing column US5128161099:\n", "Error processing column US5494981039:\n", "Error processing column US5303073051:\n", "Error processing column US5253271028:\n", "Error processing column US5138471033:\n", "Error processing column US5367971034:\n", "Error processing column US5218652049:\n", "Error processing column US53566V1061:\n", "Error processing column US5261071071:\n", "Error processing column US52736R1023:\n", "Error processing column US5024311095:\n", "Error processing column IE000S9YS762:\n", "Error processing column US5260571048:\n", "Error processing column US5018892084:\n", "Error processing column US5339001068:\n", "Error processing column US5049221055:\n", "Error processing column US5165441032:\n", "Error processing column US38526M1062:\n", "Error processing column US80874P1093:\n", "Error processing column US53947R1059:\n", "Error processing column US5398301094:\n", "Error processing column US0188021085:\n", "Error processing column US5486611073:\n", "Error processing column US5341871094:\n", "Error processing column US5324571083:\n", "Error processing column US16411R2085:\n", "Error processing column US5128073062:\n", "Error processing column US53190C1027:\n", "Error processing column US5178341070:\n", "Error processing column US8447411088:\n", "Error processing column US5463471053:\n", "Error processing column US50212V1008:\n", "Error processing column US5132721045:\n", "Error processing column US5184151042:\n", "Error processing column US86333M1080:\n", "Error processing column US5150981018:\n", "Error processing column US5719032022:\n", "Error processing column US5745991068:\n", "Error processing column US5747951003:\n", "Error processing column US5657881067:\n", "Error processing column US57636Q1040:\n", "Error processing column US55087P1049:\n", "Error processing column US59522J1034:\n", "Error processing column US5380341090:\n", "Error processing column NL0009434992:\n", "Error processing column US5627501092:\n", "Error processing column US5801351017:\n", "Error processing column US6092071058:\n", "Error processing column IE00BTN1Y115:\n", "Error processing column US5588681057:\n", "Error processing column US58506Q1094:\n", "Error processing column US5770811025:\n", "Error processing column US5950171042:\n", "Error processing column US60937P1066:\n", "Error processing column US58155Q1031:\n", "Error processing column US6153691059:\n", "Error processing column US57060D1081:\n", "Error processing column US30303M1027:\n", "Error processing column US55306N1046:\n", "Error processing column US5962781010:\n", "Error processing column US59156R1086:\n", "Error processing column US5705351048:\n", "Error processing column US6081901042:\n", "Error processing column US5529531015:\n", "Error processing column US5797802064:\n", "Error processing column US5717481023:\n", "Error processing column US6177001095:\n", "Error processing column US6247561029:\n", "Error processing column US88579Y1010:\n", "Error processing column US5732841060:\n", "Error processing column US60855R1005:\n", "Error processing column US61174X1090:\n", "Error processing column US02209S1033:\n", "Error processing column US6153942023:\n", "Error processing column US5898891040:\n", "Error processing column US61945C1036:\n", "Error processing column US56585A1025:\n", "Error processing column US6174464486:\n", "Error processing column US58933Y1055:\n", "Error processing column US60770K1079:\n", "Error processing column US5949181045:\n", "Error processing column US5738741041:\n", "Error processing column US6098391054:\n", "Error processing column US5534981064:\n", "Error processing column US55354G1004:\n", "Error processing column US55261F1049:\n", "Error processing column US5926881054:\n", "Error processing column US5949724083:\n", "Error processing column US5528481030:\n", "Error processing column US59001A1025:\n", "Error processing column US91879Q1094:\n", "Error processing column US57667L1070:\n", "Error processing column US5764852050:\n", "Error processing column US6200763075:\n", "Error processing column US55405Y1001:\n", "Error processing column US6311031081:\n", "Error processing column US6267551025:\n", "Error processing column BMG667211046:\n", "Error processing column US18915M1071:\n", "Error processing column US6516391066:\n", "Error processing column US64125C1099:\n", "Error processing column US5763231090:\n", "Error processing column US5951121038:\n", "Error processing column US65339F1012:\n", "Error processing column US6556631025:\n", "Error processing column US65473P1057:\n", "Error processing column US6460251068:\n", "Error processing column US64110L1061:\n", "Error processing column US6361801011:\n", "Error processing column US6515871076:\n", "Error processing column US6668071029:\n", "Error processing column US6541061031:\n", "Error processing column US62955J1034:\n", "Error processing column US0357108390:\n", "Error processing column US6374171063:\n", "Error processing column US6558441084:\n", "Error processing column US6293775085:\n", "Error processing column US45765U1034:\n", "Error processing column US81762P1021:\n", "Error processing column US67059N1081:\n", "Error processing column US6703461052:\n", "Error processing column US64110D1046:\n", "Error processing column US6658591044:\n", "Error processing column US6323071042:\n", "Error processing column US67066G1040:\n", "Error processing column US6707031075:\n", "Error processing column US62944T1051:\n", "Error processing column US6501111073:\n", "Error processing column US65290E1010:\n", "Error processing column US7561091049:\n", "Error processing column US65249B1098:\n", "Error processing column IE00BDVJJQ56:\n", "Error processing column US65336K1034:\n", "Error processing column US6795801009:\n", "Error processing column US68235P1084:\n", "Error processing column US6708371033:\n", "Error processing column US6792951054:\n", "Error processing column US91347P1057:\n", "Error processing column US6811161099:\n", "Error processing column US6907421019:\n", "Error processing column US6819191064:\n", "Error processing column US6819361006:\n", "Error processing column US68268W1036:\n", "Error processing column US6800331075:\n", "Error processing column US68404L2016:\n", "Error processing column US68389X1054:\n", "Error processing column US6833441057:\n", "Error processing column US6882392011:\n", "Error processing column US68902V1070:\n", "Error processing column US6802231042:\n", "Error processing column US67103H1077:\n", "Error processing column US6821891057:\n", "Error processing column US6866881021:\n", "Error processing column US09581B1035:\n", "Error processing column US92556H2067:\n", "Error processing column US70432V1026:\n", "Error processing column US69047Q1022:\n", "Error processing column US6745991058:\n", "Error processing column US06417N1037:\n", "Error processing column US6974351057:\n", "Error processing column US70959W1036:\n", "Error processing column US90364P1057:\n", "Error processing column US7043261079:\n", "Error processing column US70438V1061:\n", "Error processing column US92243G1085:\n", "Error processing column US69331C1080:\n", "Error processing column US7055731035:\n", "Error processing column US71844V2016:\n", "Error processing column US7445731067:\n", "Error processing column US74275K1088:\n", "Error processing column US7436061052:\n", "Error processing column US6937181088:\n", "Error processing column US7134481081:\n", "Error processing column US70975L1070:\n", "Error processing column US7010941042:\n", "Error processing column US7458671010:\n", "Error processing column US7427181091:\n", "Error processing column US74251V1026:\n", "Error processing column US7170811035:\n", "Error processing column US72352L1061:\n", "Error processing column US71377A1034:\n", "Error processing column US7433151039:\n", "Error processing column US6951561090:\n", "Error processing column US7181721090:\n", "Error processing column US6934751057:\n", "Error processing column US72346Q1040:\n", "Error processing column US69343T1079:\n", "Error processing column US7240781002:\n", "Error processing column US74340W1036:\n", "Error processing column US69608A1088:\n", "Error processing column IE00BLS09M33:\n", "Error processing column US72703H1014:\n", "Error processing column US7234841010:\n", "Error processing column US72147K1088:\n", "Error processing column US73278L1052:\n", "Error processing column US71424F1057:\n", "Error processing column US7374461041:\n", "Error processing column US6935061076:\n", "Error processing column US69351T1060:\n", "Error processing column US7365088472:\n", "Error processing column US45784P1012:\n", "Error processing column US74164M1080:\n", "Error processing column US7416231022:\n", "Error processing column US74624M1027:\n", "Error processing column US74762E1029:\n", "Error processing column US7185461040:\n", "Error processing column US74460D1090:\n", "Error processing column US70202L1026:\n", "Error processing column US7443201022:\n", "Error processing column US7475251036:\n", "Error processing column US69370C1009:\n", "Error processing column US70450Y1038:\n", "Error processing column NL0015002CX3:\n", "Error processing column US74736K1016:\n", "Error processing column US7835491082:\n", "Error processing column LR0008862868:\n", "Error processing column US75524B1044:\n", "Error processing column US7710491033:\n", "Error processing column US7811541090:\n", "Error processing column US82846H4056:\n", "Error processing column US74758T3032:\n", "Error processing column US74736L1098:\n", "Error processing column US7588491032:\n", "Error processing column US7593516047:\n", "Error processing column US7599161095:\n", "Error processing column US7802871084:\n", "Error processing column US7591EP1005:\n", "Error processing column US76171L1061:\n", "Error processing column US76169C1009:\n", "Error processing column US7502361014:\n", "Error processing column US75734B1008:\n", "Error processing column US75886F1075:\n", "Error processing column US7547301090:\n", "Error processing column US77311W1018:\n", "Error processing column US7496071074:\n", "Error processing column US76954A1034:\n", "Error processing column US7509171069:\n", "Error processing column US7512121010:\n", "Error processing column US7703231032:\n", "Error processing column US64828T2015:\n", "Error processing column US78377T1079:\n", "Error processing column US7731221062:\n", "Error processing column BMG7496G1033:\n", "Error processing column US7739031091:\n", "Error processing column US7611521078:\n", "Error processing column US7496851038:\n", "Error processing column US7766961061:\n", "Error processing column GB00BMVP7Y09:\n", "Error processing column US75281A1097:\n", "Error processing column US77543R1023:\n", "Error processing column US7782961038:\n", "Error processing column US7757111049:\n", "Error processing column US7587501039:\n", "Error processing column US7595091023:\n", "Error processing column US76155X1000:\n", "Error processing column US81730H1095:\n", "Error processing column US7607591002:\n", "Error processing column US8086251076:\n", "Error processing column US78709Y1055:\n", "Error processing column US75513E1010:\n", "Error processing column US7140461093:\n", "Error processing column US78351F1075:\n", "Error processing column US85423L1035:\n", "Error processing column US8085131055:\n", "Error processing column US2787681061:\n", "Error processing column US7841171033:\n", "Error processing column US84265V1052:\n", "Error processing column US8175651046:\n", "Error processing column US78410G1040:\n", "Error processing column US8552441094:\n", "Error processing column US8606301021:\n", "Error processing column US8243481061:\n", "Error processing column US8163001071:\n", "Error processing column US8299331004:\n", "Error processing column US8305661055:\n", "Error processing column US8326964058:\n", "Error processing column US8308301055:\n", "Error processing column US88023U1016:\n", "Error processing column US82982L1035:\n", "Error processing column US85208M1027:\n", "Error processing column US8270481091:\n", "Error processing column AN8068571086:\n", "Error processing column US8330341012:\n", "Error processing column US83304A1060:\n", "Error processing column KYG8068L1086:\n", "Error processing column US86627T1088:\n", "Error processing column US86800U3023:\n", "Error processing column US78442P1066:\n", "Error processing column US8334451098:\n", "Error processing column US87162W1009:\n", "Error processing column US83444M1018:\n", "Error processing column US8354951027:\n", "Error processing column US87161C5013:\n", "Error processing column US78463M1071:\n", "Error processing column US8288061091:\n", "Error processing column US78409V1044:\n", "Error processing column US8425871071:\n", "Error processing column US8716071076:\n", "Error processing column US83406F1021:\n", "Error processing column US8168511090:\n", "Error processing column IE00BFY8C754:\n", "Error processing column US8036071004:\n", "Error processing column US78473E1038:\n", "Error processing column US85254J1025:\n", "Error processing column US8592411016:\n", "Error processing column US8581191009:\n", "Error processing column US78467J1007:\n", "Error processing column US8290731053:\n", "Error processing column US8574771031:\n", "Error processing column IE00BKVD2N49:\n", "Error processing column US8666741041:\n", "Error processing column US8448951025:\n", "Error processing column IE00028FXN24:\n", "Error processing column US85571B1052:\n", "Error processing column US87165B1035:\n", "Error processing column US83088M1027:\n", "Error processing column US21036P1084:\n", "Error processing column US8545021011:\n", "Error processing column US87151X1019:\n", "Error processing column US0494681010:\n", "Error processing column US09073M1045:\n", "Error processing column US00206R1023:\n", "Error processing column US60871R2094:\n", "Error processing column US8936411003:\n", "Error processing column IE000IVNQZ81:\n", "Error processing column US8636671013:\n", "Error processing column US8718291078:\n", "Error processing column US8793601050:\n", "Error processing column US88023B1035:\n", "Error processing column US88322Q1085:\n", "Error processing column US8725401090:\n", "Error processing column US87256C1018:\n", "Error processing column US87612E1064:\n", "Error processing column US88033G4073:\n", "Error processing column US89832Q1094:\n", "Error processing column US8807701029:\n", "Error processing column US4108671052:\n", "Error processing column US8793691069:\n", "Error processing column US8873891043:\n", "Error processing column US87422Q1094:\n", "Error processing column US8725901040:\n", "Error processing column US8835561023:\n", "Error processing column US87724P1066:\n", "Error processing column US8887871080:\n", "Error processing column US88262P1021:\n", "Error processing column US8894781033:\n", "Error processing column US8726571016:\n", "Error processing column US8760301072:\n", "Error processing column US87612G1013:\n", "Error processing column US89417E1091:\n", "Error processing column US89400J1079:\n", "Error processing column US88146M1018:\n", "Error processing column US88160R1014:\n", "Error processing column US8923561067:\n", "Error processing column US8962391004:\n", "Error processing column US74144T1088:\n", "Error processing column US89531P1057:\n", "Error processing column US9024941034:\n", "Error processing column IE00BK9ZQ967:\n", "Error processing column US90138F1021:\n", "Error processing column US88339J1051:\n", "Error processing column US8926721064:\n", "Error processing column US8910921084:\n", "Error processing column US69349H1077:\n", "Error processing column US8825081040:\n", "Error processing column US88162G1031:\n", "Error processing column US8740541094:\n", "Error processing column US91332U1016:\n", "Error processing column US9022521051:\n", "Error processing column US9100471096:\n", "Error processing column US9026811052:\n", "Error processing column US8832031012:\n", "Error processing column US8826811098:\n", "Error processing column US9026531049:\n", "Error processing column US90353T1007:\n", "Error processing column US90278Q1085:\n", "Error processing column US9099071071:\n", "Error processing column US90353W1036:\n", "Error processing column US0235861004:\n", "Error processing column US9037311076:\n", "Error processing column US90384S3031:\n", "Error processing column US0235865062:\n", "Error processing column US9139031002:\n", "Error processing column US9027881088:\n", "Error processing column US91324P1021:\n", "Error processing column US91529Y1064:\n", "Error processing column US9078181081:\n", "Error processing column US9113121068:\n", "Error processing column US91307C1027:\n", "Error processing column US9120081099:\n", "Error processing column US91823B1098:\n", "Error processing column US9116841084:\n", "Error processing column US9113631090:\n", "Error processing column US9224751084:\n", "Error processing column US9029733048:\n", "Error processing column US92826C8394:\n", "Error processing column US92538J1060:\n", "Error processing column US9182041080:\n", "Error processing column BMG93A5A1010:\n", "Error processing column US92338C1036:\n", "Error processing column US9256521090:\n", "Error processing column US9282541013:\n", "Error processing column US9202531011:\n", "Error processing column US91913Y1001:\n", "Error processing column US9291601097:\n", "Error processing column US9290421091:\n", "Error processing column US9288811014:\n", "Error processing column US9222801022:\n", "Error processing column US92532F1003:\n", "Error processing column US9279591062:\n", "Error processing column US9290891004:\n", "Error processing column US92840M1027:\n", "Error processing column US92276F1003:\n", "Error processing column US92343E1029:\n", "Error processing column US92345Y1064:\n", "Error processing column US92537N1081:\n", "Error processing column US9297401088:\n", "Error processing column US9467841055:\n", "Error processing column US9344231041:\n", "Error processing column US92047W1018:\n", "Error processing column US9418481035:\n", "Error processing column US9478901096:\n", "Error processing column US95082P1057:\n", "Error processing column US9576381092:\n", "Error processing column US92556V1061:\n", "Error processing column US9581021055:\n", "Error processing column US98311A1051:\n", "Error processing column US96208T1043:\n", "Error processing column US98138H1014:\n", "Error processing column US92939U1060:\n", "Error processing column US9741551033:\n", "Error processing column US95040Q1040:\n", "Error processing column US9497461015:\n", "Error processing column US9633201069:\n", "Error processing column US9604131022:\n", "Error processing column US94106L1098:\n", "Error processing column US9694571004:\n", "Error processing column US9311421039:\n", "Error processing column US9713781048:\n", "Error processing column US0844231029:\n", "Error processing column US9426222009:\n", "Error processing column US92936U1097:\n", "Error processing column US00790R1041:\n", "Error processing column US9699041011:\n", "Error processing column US9345502036:\n", "Error processing column US9553061055:\n", "Error processing column US29670G1022:\n", "Error processing column US9427491025:\n", "Error processing column US9831341071:\n", "Error processing column US97650W1080:\n", "Error processing column US9129091081:\n", "Error processing column US98389B1008:\n", "Error processing column IE00BDB6Q211:\n", "Error processing column US9621661043:\n", "Error processing column US9807451037:\n", "Error processing column US98419M1009:\n", "Error processing column US8522341036:\n", "Error processing column US98954M1018:\n", "Error processing column US9884981013:\n", "Error processing column US30231G1022:\n", "Error processing column US98956P1021:\n", "Error processing column US98954M2008:\n", "Error processing column US9892071054:\n", "Error processing column US9837931008:\n", "Error processing column US9897011071:\n", "Error processing column US98980G1022:\n", "Error processing column US98978V1035:\n", "Error processing column US98980L1017:\n", "Error processing column US98983L1089:\n", "Error processing column US92343V1044:\n"]}], "source": ["file_name = 'aipex_consolidated_er.csv'\n", "out_put_file_name = 'aipex_consolidated_er_smooth.csv'\n", "path = os.getcwd() \n", "df = pd.read_csv(path + \"/\"+file_name, index_col=False)\n", "# Define the function to apply the rolling operation on a single column\n", "def process_column(col):\n", "    if col != 'date':\n", "         df[col] = df[col].rolling(5).apply(lambda x: (x.iloc[4]*60 + x.iloc[3]*10 + x.iloc[2]*10 + x.iloc[1]*10 + x.iloc[0]*10) / 100\n", "                                           if len(x) == 5 else None)\n", "    return col\n", "# Use ThreadPoolExecutor for multithreading\n", "with concurrent.futures.ThreadPoolExecutor() as executor:\n", "    # Submit tasks to process each column in parallel\n", "    futures = {executor.submit(process_column, col): col for col in df.columns.tolist()}\n", "\n", "    # Collect results and handle potential exceptions\n", "    for future in concurrent.futures.as_completed(futures):\n", "        col = futures[future]\n", "        try:\n", "            future.result()  # If there's an exception, it'll be raised here\n", "        except Exception as e:\n", "            print(f\"Error processing column {col}:\")\n", "            #print(f\"Error processing column {col}: {e}\")\n", "df.to_csv(os.getcwd()+\"/\"+out_put_file_name,index=False)"]}, {"cell_type": "code", "execution_count": 202, "id": "0e331bab-64d1-4bb0-9a87-052f91e6b7e2", "metadata": {}, "outputs": [], "source": ["df = df.T\n", "df.columns = df.iloc[0].tolist()\n", "df = df.iloc[1:]\n", "df.index.name = 'isin'\n", "df.reset_index(inplace=True)\n", "df = df[['isin',df.columns.tolist()[-3]]]\n", "df.rename(columns={df.columns.tolist()[1]:'ER'},inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6eb7ccef-66d8-4781-bfa6-eb77f44486ba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 203, "id": "3287a07a-3989-40e1-ab1c-83972d37f6d5", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/home/<USER>/abhishek/delivery/aipex/aipex.csv'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[203]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m df_aipex = \u001b[43mpd\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mos\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgetcwd\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m+\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/aipex.csv\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mindex_col\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mF<PERSON>e\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mwindows-1252\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      2\u001b[39m df_min_max = pd.read_csv(os.getcwd()+\u001b[33m\"\u001b[39m\u001b[33m/pre_portfolio_er_consolidate_2024-11-22.csv\u001b[39m\u001b[33m\"\u001b[39m,index_col=\u001b[38;5;28;01mF<PERSON>e\u001b[39;00m,encoding=\u001b[33m'\u001b[39m\u001b[33mwindows-1252\u001b[39m\u001b[33m'\u001b[39m)[[\u001b[33m'\u001b[39m\u001b[33misin\u001b[39m\u001b[33m'\u001b[39m,\u001b[33m'\u001b[39m\u001b[33mmin\u001b[39m\u001b[33m'\u001b[39m,\u001b[33m'\u001b[39m\u001b[33mmax\u001b[39m\u001b[33m'\u001b[39m]]\n\u001b[32m      3\u001b[39m df_aipex = df_aipex.drop(columns=[\u001b[33m'\u001b[39m\u001b[33mER1\u001b[39m\u001b[33m'\u001b[39m])\n", "\u001b[36mFile \u001b[39m\u001b[32m~/abhishek/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py:1026\u001b[39m, in \u001b[36mread_csv\u001b[39m\u001b[34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[39m\n\u001b[32m   1013\u001b[39m kwds_defaults = _refine_defaults_read(\n\u001b[32m   1014\u001b[39m     dialect,\n\u001b[32m   1015\u001b[39m     delimiter,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1022\u001b[39m     dtype_backend=dtype_backend,\n\u001b[32m   1023\u001b[39m )\n\u001b[32m   1024\u001b[39m kwds.update(kwds_defaults)\n\u001b[32m-> \u001b[39m\u001b[32m1026\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/abhishek/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py:620\u001b[39m, in \u001b[36m_read\u001b[39m\u001b[34m(filepath_or_buffer, kwds)\u001b[39m\n\u001b[32m    617\u001b[39m _validate_names(kwds.get(\u001b[33m\"\u001b[39m\u001b[33mnames\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[32m    619\u001b[39m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m620\u001b[39m parser = \u001b[43mTextFileReader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    622\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[32m    623\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "\u001b[36mFile \u001b[39m\u001b[32m~/abhishek/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py:1620\u001b[39m, in \u001b[36mTextFileReader.__init__\u001b[39m\u001b[34m(self, f, engine, **kwds)\u001b[39m\n\u001b[32m   1617\u001b[39m     \u001b[38;5;28mself\u001b[39m.options[\u001b[33m\"\u001b[39m\u001b[33mhas_index_names\u001b[39m\u001b[33m\"\u001b[39m] = kwds[\u001b[33m\"\u001b[39m\u001b[33mhas_index_names\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m   1619\u001b[39m \u001b[38;5;28mself\u001b[39m.handles: IOHandles | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1620\u001b[39m \u001b[38;5;28mself\u001b[39m._engine = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/abhishek/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py:1880\u001b[39m, in \u001b[36mTextFileReader._make_engine\u001b[39m\u001b[34m(self, f, engine)\u001b[39m\n\u001b[32m   1878\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[32m   1879\u001b[39m         mode += \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m-> \u001b[39m\u001b[32m1880\u001b[39m \u001b[38;5;28mself\u001b[39m.handles = \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1881\u001b[39m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1882\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1883\u001b[39m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mencoding\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1884\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcompression\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1885\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmemory_map\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1886\u001b[39m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m=\u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1887\u001b[39m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mencoding_errors\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstrict\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1888\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstorage_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1889\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1890\u001b[39m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m.handles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1891\u001b[39m f = \u001b[38;5;28mself\u001b[39m.handles.handle\n", "\u001b[36mFile \u001b[39m\u001b[32m~/abhishek/.venv/lib/python3.12/site-packages/pandas/io/common.py:873\u001b[39m, in \u001b[36mget_handle\u001b[39m\u001b[34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[39m\n\u001b[32m    868\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[32m    869\u001b[39m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[32m    870\u001b[39m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[32m    871\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m ioargs.encoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs.mode:\n\u001b[32m    872\u001b[39m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m873\u001b[39m         handle = \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    880\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    881\u001b[39m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[32m    882\u001b[39m         handle = \u001b[38;5;28mopen\u001b[39m(handle, ioargs.mode)\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: '/home/<USER>/abhishek/delivery/aipex/aipex.csv'"]}], "source": ["df_aipex = pd.read_csv(os.getcwd()+\"/aipex.csv\",index_col=False,encoding='windows-1252')\n", "df_min_max = pd.read_csv(os.getcwd()+\"/pre_portfolio_er_consolidate_2024-11-22.csv\",index_col=False,encoding='windows-1252')[['isin','min','max']]\n", "df_aipex = df_aipex.drop(columns=['ER1'])\n", "df['ER'] = df['ER'] / 100\n", "df_aipex = pd.merge(df_aipex,df,on='isin',how='left')\n", "df_aipex = pd.merge(df_aipex,df_min_max,on='isin',how='left')\n", "df_aipex['ER1'] = df_aipex['ER'].clip(lower=df_aipex['min'], upper=df_aipex['max'])\n", "df_aipex.to_csv(os.getcwd()+\"/aipex_pre_con_er.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "17fbf07c-5b86-4c2a-a320-1c64fac7e006", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0020197521046215 -------------- nan\n", "0.1286179952979163 -------------- nan\n", "0.1064338641142343 -------------- nan\n", "0.0827014508065388 -------------- nan\n", "0.094551876861452 -------------- nan\n", "0.192124237812249 -------------- nan\n", "0.0046601360384157 -------------- nan\n", "0.1179305492364374 -------------- nan\n", "0.1867495225744532 -------------- nan\n", "0.0754225534400512 -------------- nan\n", "0.0087880617136304 -------------- nan\n", "116\n", "2022-11-28 ------ 2024-11-28\n", "=====================================================================================\n", "n_gen |  n_eval |   cv (min)   |   cv (avg)   |  n_nds  |     eps      |  indicator  \n", "=====================================================================================\n", "    1 |     100 |  0.204265287 |  0.258516694 |       1 |            - |            -\n", "    2 |     200 |  0.186548844 |  0.233917563 |       1 |  0.001470633 |            f\n", "    3 |     300 |  0.175526008 |  0.219234153 |       1 |  0.000231383 |            f\n", "    4 |     400 |  0.175526008 |  0.204989588 |       1 |  0.00000E+00 |            f\n", "    5 |     500 |  0.158641211 |  0.193429636 |       1 |  0.001127351 |            f\n", "    6 |     600 |  0.156494102 |  0.181261055 |       1 |  0.005861590 |        ideal\n", "    7 |     700 |  0.145542154 |  0.170590004 |       1 |  0.006855926 |        ideal\n", "    8 |     800 |  0.139645907 |  0.161097954 |       1 |  0.000292871 |            f\n", "    9 |     900 |  0.134955694 |  0.152044296 |       1 |  0.001036912 |            f\n", "   10 |    1000 |  0.111058294 |  0.142108751 |       1 |  0.000687741 |            f\n", "   11 |    1100 |  0.100801347 |  0.132825742 |       1 |  0.002473312 |            f\n", "   12 |    1200 |  0.089380941 |  0.122021623 |       1 |  0.002059868 |            f\n", "   13 |    1300 |  0.082827600 |  0.111180419 |       1 |  0.002483381 |            f\n", "   14 |    1400 |  0.076757615 |  0.099367756 |       1 |  0.002580819 |        ideal\n", "   15 |    1500 |  0.072198819 |  0.090001499 |       1 |  0.001316696 |            f\n", "   16 |    1600 |  0.060092619 |  0.082553137 |       1 |  0.006270534 |        ideal\n", "   17 |    1700 |  0.053080886 |  0.076450763 |       1 |  0.002380368 |            f\n", "   18 |    1800 |  0.039561312 |  0.069451582 |       1 |  0.002126340 |            f\n", "   19 |    1900 |  0.039561312 |  0.062978026 |       1 |  0.00000E+00 |            f\n", "   20 |    2000 |  0.039561312 |  0.056212502 |       1 |  0.00000E+00 |            f\n", "   21 |    2100 |  0.039561312 |  0.051240396 |       1 |  0.00000E+00 |            f\n", "   22 |    2200 |  0.034260428 |  0.047160906 |       1 |  0.000430286 |            f\n", "   23 |    2300 |  0.028889342 |  0.043578479 |       1 |  0.002284184 |            f\n", "   24 |    2400 |  0.020163965 |  0.039866896 |       1 |  0.005825039 |        ideal\n", "   25 |    2500 |  0.020163965 |  0.035387340 |       1 |  0.00000E+00 |            f\n", "   26 |    2600 |  0.018392042 |  0.031522059 |       1 |  0.001024767 |            f\n", "   27 |    2700 |  0.018111711 |  0.027662726 |       1 |  0.001385203 |            f\n", "   28 |    2800 |  0.013011767 |  0.023686060 |       1 |  0.000114874 |            f\n", "   29 |    2900 |  0.012640497 |  0.020258237 |       1 |  0.001585758 |            f\n", "   30 |    3000 |  0.011668403 |  0.017498984 |       1 |  0.000621329 |            f\n", "   31 |    3100 |  0.009318774 |  0.015444317 |       1 |  0.000813488 |            f\n", "   32 |    3200 |  0.006728694 |  0.013244580 |       1 |  0.002990297 |        ideal\n", "   33 |    3300 |  0.005829022 |  0.011095611 |       1 |  0.003612345 |        ideal\n", "   34 |    3400 |  0.004440794 |  0.008874517 |       1 |  0.004053467 |        ideal\n", "   35 |    3500 |  0.003372588 |  0.007149432 |       1 |  0.001756458 |            f\n", "   36 |    3600 |  0.001403365 |  0.005577807 |       1 |  0.002535492 |        ideal\n", "   37 |    3700 |  0.000770863 |  0.004333280 |       1 |  0.001623285 |            f\n", "   38 |    3800 |  0.000770863 |  0.003239316 |       1 |  0.00000E+00 |            f\n", "   39 |    3900 |  0.000151327 |  0.002406711 |       1 |  0.002151265 |            f\n", "   40 |    4000 |  0.000151327 |  0.001737474 |       1 |  0.00000E+00 |            f\n", "   41 |    4100 |  0.000072322 |  0.001225814 |       1 |  0.002172273 |            f\n", "   42 |    4200 |  0.00000E+00 |  0.000808995 |       2 |  0.133814305 |        ideal\n", "   43 |    4300 |  0.00000E+00 |  0.000439783 |       2 |  0.141421690 |        ideal\n", "   44 |    4400 |  0.00000E+00 |  0.000249333 |       4 |  0.332469488 |        ideal\n", "   45 |    4500 |  0.00000E+00 |  0.000085762 |       7 |  0.263705281 |        ideal\n", "   46 |    4600 |  0.00000E+00 |  7.64896E-06 |      10 |  0.062725211 |            f\n", "   47 |    4700 |  0.00000E+00 |  0.00000E+00 |       8 |  0.342191171 |        ideal\n", "   48 |    4800 |  0.00000E+00 |  0.00000E+00 |      12 |  0.058049462 |            f\n", "   49 |    4900 |  0.00000E+00 |  0.00000E+00 |      11 |  0.019203901 |        ideal\n", "   50 |    5000 |  0.00000E+00 |  0.00000E+00 |      15 |  0.048476508 |        ideal\n", "pymoo done\n"]}], "source": ["today_date =  datetime.now().strftime(\"%Y-%m-%d\")\n", "df_sec = pd.read_csv(os.getcwd()+\"/out_put/optimized_aisrt_2024-11-27.csv\",index_col=False)\n", "df_sec = df_sec[['sector_name','max_ER']].rename(columns={'max_ER':'sector_weight'})\n", "df_sec_weight = df_sec.copy(deep=True)\n", "df_sec.rename(columns={'sector_name':'isin'},inplace=True)\n", "df_er = pd.read_csv(os.getcwd()+\"/aipex_pre_con_er.csv\",index_col=False,encoding='windows-1252')\n", "  \n", "df_fin = pd.DataFrame()\n", "df_s = df_sec\n", "df_g = df_er[['isin','sector_name','ER1','Caps']].groupby('sector_name')\n", "df_sec_weight_m = df_sec_weight\n", "df_sec_weight_m.rename(columns={'sector_weight':'sector_weight_max'},inplace=True)\n", "for k in range(len(df_s)):  \n", "    sec = df_s.iloc[k]['isin']\n", "    print(df_s.iloc[k]['sector_weight'],\"--------------\",float(\"nan\"))\n", "    if not math.isnan(float(df_s.iloc[k]['sector_weight'])) and float(df_s.iloc[k]['sector_weight']) > 0:\n", "        per = math.ceil((float(df_s.iloc[k]['sector_weight'])) * 100) + 1\n", "        df_ss = df_g.get_group(sec)\n", "        df_ss = df_ss.dropna()\n", "        df_ss = df_ss.drop_duplicates(subset=['isin'],keep='first')\n", "        df_ss = df_ss.sort_values(by=['ER1'],ascending=False).reset_index().drop(columns=['index'])\n", "        df_ss = df_ss.iloc[:per]\n", "        df_fin = pd.concat([df_fin,df_ss],ignore_index=True)\n", "print(len(df_fin))        \n", "df_fin = pd.merge(df_fin,df_sec_weight,on='sector_name',how='left')\n", "\n", "df_fin.rename(columns={'ER1':'er','Caps':'max'},inplace=True)\n", "df_fin['sector_weight_min'] = df_fin['sector_weight_max'] - (df_fin['sector_weight_max'] * 0.75) \n", "df_fin['sector_weight_max'] = (df_fin['sector_weight_max'] * 0.75) + df_fin['sector_weight_max']\n", "df_fin['min'] = 0\n", "df_fin['er'] = df_fin['er'].astype(float)\n", "df_fin['wt'] = 1/len(df_fin)    \n", "data_merge = df_fin\n", "df_cc = pd.read_csv(\"aipex_close.csv\",index_col=False)\n", "df_cc['date'] = pd.to_datetime(df_cc['date'],format='%Y-%m-%d')\n", "dd = ['date']\n", "dd = dd+ data_merge['isin'].tolist()\n", "\n", "df_cc = df_cc[dd]\n", "#print(df_cc.columns)\n", "#break\n", "start_date = (datetime.strptime(today_date, \"%Y-%m-%d\") - relativedelta(years=2)).strftime(\"%Y-%m-%d\")\n", "end_date = today_date\n", "df_cc = df_cc.query('date >= @start_date and date <= @end_date')\n", "print(start_date,\"------\",end_date)\n", "df_cc.set_index('date', inplace=True)\n", "df_p = df_cc.pct_change(periods=22)\n", "df_pp = df_p.cov()\n", "df_pp.to_csv(\"Covariance.csv\", index=False)\n", "\n", "cov_matrix = pd.read_csv(\"Covariance.csv\", index_col=False)\n", "\n", "n = len(data_merge)\n", "wt = np.array([])\n", "wt=np.repeat(1/n, n)\n", "data_merge['wt'] = wt\n", "w = data_merge['wt']\n", "cov = cov_matrix\n", "E = data_merge['er']\n", "\n", "#print(data_merge)\n", "s_col_list = data_merge.columns.tolist()\n", "#('sector_name','sector_weight_max','sector_weight_min'),\n", "col_list = [('isin','max','min'),('sector_name','sector_weight_max','sector_weight_min')]\n", "\n", "#pymoo(data_merge, cov,col_list)\n", "pop_size_v = 100\n", "gen_val = 50\n", "\n", "F, X = pymoo(data_merge, cov, col_list,pop_size_v,gen_val)\n", "print('pymoo done')\n", "\n", "if len(F)>1:\n", "    for i in range(len(X)):\n", "        B = X[i]\n", "        #data_merge['wt_1']= B\n", "        data_merge['wt_'+str(i+1)] = B\n", "    A = []\n", "    B = []\n", "    C = []\n", "    for jj in range(len(F)):\n", "        #print(i)\n", "        A.append(-F[jj][0])\n", "        B.append(F[jj][1])\n", "        C.append(-F[jj][0]/F[jj][1])\n", "    ind_1 = A.index(np.max(A))\n", "    ind_3 = B.index(np.min(B))\n", "    ind_2 = C.index(np.max(C))\n", "    wt_1=data_merge['wt_'+str(ind_1+1)]\n", "    wt_2=data_merge['wt_'+str(ind_2+1)]\n", "    wt_3=data_merge['wt_'+str(ind_3+1)]\n", "\n", "    data_merge = data_merge.iloc[:,:]\n", "    data_merge['max_ER'] = wt_1\n", "    data_merge['max_sharpe'] = wt_2\n", "    data_merge['min_risk'] = wt_3\n", "\n", "\n", "elif len(F) == 1:\n", "    B = X[0]\n", "    data_merge['max_ER or max_sharpe'] = B   \n", "if 'max_ER or max_sharpe' in data_merge.columns.tolist():\n", "    s_col_list.append('max_ER or max_sharpe')\n", "    data_merge = data_merge[s_col_list] \n", "else:\n", "    s_col_list.extend(['max_ER','max_sharpe','min_risk'])\n", "    data_merge = data_merge[s_col_list]     \n", "        \n", "data_merge.to_excel(os.getcwd()+\"/out_put/aieq_consolidated_optimization_\"+today_date+\".xlsx\",index=False)\n", "    "]}, {"cell_type": "markdown", "id": "d10ea3bf-c07f-4e75-8eca-d035d4e76db2", "metadata": {}, "source": ["aipex file format"]}, {"cell_type": "code", "execution_count": null, "id": "894e104e-1422-4e0c-aab4-a4ff0043e864", "metadata": {}, "outputs": [], "source": ["today_date =  datetime.now().strftime(\"%Y-%m-%d\")\n", "df = pd.read_excel(os.getcwd()+\"/out_put/aipex_consolidated_optimization_\"+today_date+\".xlsx\",index_col=False)\n", "df.rename(columns={'max_ER':'Wt_Allocation'},inplace=True)\n", "df_a = pd.read_excel(os.getcwd()+\"/Sendout_AIPEXTR_Caps.xlsx\",index_col=False)"]}, {"cell_type": "code", "execution_count": null, "id": "b83c87ac-df4b-45e9-abf8-5e6a23126188", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 328, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Wt_Allocation'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "a1f16dc0-d0cb-49aa-b430-30d9057ffa0c", "metadata": {}, "outputs": [], "source": ["tw = df['Wt_Allocation'].sum()\n", "for i in range(len(df)):\n", "    df.at[i,'Wt_Allocation'] =df.iloc[i]['Wt_Allocation']/tw"]}, {"cell_type": "code", "execution_count": null, "id": "fa5777b4-88e6-4b72-9cdd-6cc44655df28", "metadata": {}, "outputs": [], "source": ["df.rename(columns={'isin':'ISIN'},inplace=True)\n", "df_a = pd.merge(df_a,df[['ISIN','Wt_Allocation']],on='ISIN',how='left')\n", "df_a = df_a.fillna(0.00000)\n", "df_a.rename(columns={'Wt_Allocation':'Wts'},inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d8efef4f-3778-4dc2-9a25-72918ff78959", "metadata": {}, "outputs": [], "source": ["excel_file = \"EQBT_AI_Eqty_Index_Constrained_\"+today_date+\".xlsx\"\n", "\n", "# Create an Excel writer using xlsxwriter as the engine\n", "with pd.ExcelWriter(excel_file, engine=\"xlsxwriter\") as writer:\n", "    # Write DataFrame to Excel\n", "    df_a.to_excel(writer, index=False, sheet_name=\"Sheet1\")\n", "    \n", "    # Access the workbook and worksheet\n", "    workbook = writer.book\n", "    worksheet = writer.sheets[\"Sheet1\"]\n", "    \n", "    # Define a format for the header\n", "    header_format = workbook.add_format({\n", "        \"valign\": \"vcenter\",\n", "        \"bg_color\": \"#1F497D\",\n", "        \"font_color\": \"#ffffff\",\n", "        \"font_size\" : '10'\n", "        #\"border\": 1\n", "    })\n", "    \n", "    # Apply the format to the header row\n", "    for col_num, value in enumerate(df_a.columns.values):\n", "        worksheet.write(0, col_num, value, header_format)"]}, {"cell_type": "markdown", "id": "9f48122e-f000-4dcb-957c-b07f2d74045b", "metadata": {}, "source": ["aieq file format"]}, {"cell_type": "code", "execution_count": null, "id": "245d1c72-aab8-4fed-85d0-21e0d932a5e5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "tag = \"aieq\"\n", "df_list = []\n", "try:\n", "    response = req.get(\"http://************:8080/masteractivefirms/getmasterbytag?tag=\"+tag)\n", "    response_data = response.json()\n", "    check = bool(response_data['status'])\n", "    if check:\n", "        df1 = pd.DataFrame()\n", "        df1 = pd.DataFrame.from_dict(\n", "            json_normalize(response_data['data']['masteractivefirms_'+tag]))\n", "        #df1.rename(columns={'close': isin}, inplace=True)\n", "            #print(len(df1),\" : isin :\",isin)\n", "        #df1 = df1[['isin','tic','conm','cusip','country_code','ind_code','bloombergticker','custodyticker','gvkey']] \n", "        df1.to_csv(os.getcwd()+\"/master_\"+tag+\".csv\",index=False)\n", "    else:\n", "        print('false ',isin)\n", "except Exception as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": null, "id": "399ebfa3-f502-4b5b-809c-330fd5090846", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(os.getcwd()+\"/out_put/aieq_consolidated_optimization_\"+today_date+\".xlsx\",index_col=False)\n", "df_ma = pd.read_csv(os.getcwd()+\"/master_aieq.csv\",index_col=False)\n", "df.rename(columns={'max_ER':'PortfolioPercentage'},inplace=True)\n", "df = pd.merge(df,df_ma[['isin','tic','conm','cusip','gvkey']].rename(columns={'gvkey':'Gvkey','cusip':'Cusip','conm':'Cname','tic':'Tic'}),on='isin',how='left')\n", "df['FundShare'] = ''\n", "df['OrderType'] = 'MKT'\n", "df['Instruction'] = 'Buy on MOC'\n", "df = df[['Gvkey','Cusip','Tic','Cname','FundShare','PortfolioPercentage','OrderType','Instruction','isin']]\n"]}, {"cell_type": "code", "execution_count": null, "id": "5385dda1-9921-4887-a4ca-71e94d09aadc", "metadata": {}, "outputs": [{"data": {"text/plain": ["100.0"]}, "execution_count": 321, "metadata": {}, "output_type": "execute_result"}], "source": ["df['PortfolioPercentage'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "99084880-8742-4d8c-aeca-a8044f139529", "metadata": {}, "outputs": [], "source": ["df['PortfolioPercentage'] = df['PortfolioPercentage']*100\n", "df = df.round({\"PortfolioPercentage\":5})\n", "df.at[0,'PortfolioPercentage'] =df.iloc[0]['PortfolioPercentage'] + (100-df['PortfolioPercentage'].sum())\n", "df = df.round({\"PortfolioPercentage\":5})"]}, {"cell_type": "code", "execution_count": null, "id": "3c94428a-2c98-4c6e-927e-a1648c91fe28", "metadata": {}, "outputs": [{"data": {"text/plain": ["100.0"]}, "execution_count": 314, "metadata": {}, "output_type": "execute_result"}], "source": ["df['PortfolioPercentage'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "41243546-0f1b-405c-a0cb-e75b80c8c224", "metadata": {}, "outputs": [], "source": ["#df['PortfolioPercentage'] = df['PortfolioPercentage'] / df['PortfolioPercentage'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "44828553-2370-4b15-86c5-f01558b90bc0", "metadata": {}, "outputs": [], "source": ["df.to_csv(os.getcwd()+\"/aieq_rebalance_portfolio_\"+today_date+\".csv\",index=False)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}