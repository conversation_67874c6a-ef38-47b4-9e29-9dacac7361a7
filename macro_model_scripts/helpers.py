import sys
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from eq_common_utils.utils.metrics_helper import MetricsHelper
from eq_common_utils.ds_scripts.prediction_helper import PredictionHelper
from eq_common_utils.utils.ibm_helper import IBMConnection
from opensearchpy.connection.http_requests import RequestsHttpConnection
from datetime import timedelta, date, datetime
from opensearchpy.exceptions import OpenSearchException
from aws_requests_auth.aws_auth import AWSRequestsAuth
from opensearchpy.client import OpenSearch
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
import requests 
import pandas as pd
import numpy as np
import configparser
import boto3
import yaml
from google.oauth2.credentials import Credentials
from email import encoders
from email.message import EmailMessage
import base64
import tempfile
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from functools import reduce
import json
from fredapi import Fred
import time
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor, as_completed
import ast
from multiprocessing.pool import ThreadPool
from concurrent.futures import ThreadPoolExecutor
import ibm_watsonx_ai
from numpy_ext import rolling_apply as rolling_apply_ext
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from ibm_watsonx_ai import APIClient
from ibm_watsonx_ai.deployment import Batch
import traceback


s3conn = s3_config()
ss = s3conn
es = es_config(env='prod')
preprod_es = es_config(env='pre')
metrics_obj = MetricsHelper(OpenSearch)


with open('config.yaml', 'r') as file:
    config= yaml.safe_load(file)

wml_credentials = ibm_watsonx_ai.Credentials(
                   url = config['IBM_creds']['wml_credentials']['url'],
                   api_key = config['IBM_creds']['wml_credentials']['apikey']
                  )

schedular_dict = config['Metrics_data']['schedular_dict']
req_columns = config['Metrics_data']['req_columns']
all_metrics = config['Metrics_data']['all_metrics']
metrics_period = config['Metrics_data']['metrics_period']
country_mappings = config['Misc']['country_mappings']

mapi = config['Misc']['country_mappings']
macro_datapoints= ss.read_as_dataframe(config['S3_paths']['macro_datapoints_bucket'],config['S3_paths']['macro_datapoints_path'])[['Country_Code','Type','Ticker','Country']]
macro_datapoints['Type'] = macro_datapoints['Type'].apply(lambda x:x.lower())
macro_datapoints['name'] = macro_datapoints['Country_Code']+'_'+macro_datapoints['Type']
macro_datapoints.set_index('name',inplace=True)
macro_datapoints.drop(columns=['Country_Code','Type'],inplace=True)

macro_deployments= ss.read_as_dataframe('micro-ops-output','macro_model_split/model_training_details/deployment_details.csv')
macro_deployments = macro_deployments[macro_deployments['year']==2024]
macro_deployments['Country_Code'] = macro_deployments['name'].apply(lambda x:x.split('_')[0])
macro_deployments['type'] = macro_deployments['name'].apply(lambda x:x.split('_')[1].capitalize())
macro_deployments.set_index('name',inplace=True)

macro_deployments = macro_deployments.join(macro_datapoints)
macro_deployments.rename(columns={'type':'Type'},inplace=True)
macro_deployments.reset_index(inplace=True)
macro_keywords = ss.read_advanced_as_df(config['S3_paths']['macro_keywords_bucket'],config['S3_paths']['macro_keywords_path'])
europe_sp = {'EUR':['EUU','EA19']}
temp_kapo_lll = list(set(macro_deployments['Country_Code'].values))


SCOPES = ['https://www.googleapis.com/auth/gmail.send']
APPLICATION_NAME = 'Gmail API Python Send Email'

try:
    fred = Fred(api_key='57b0fb46e088b7bfcdf4b85ce5606edb')
except Exception as e:
    print(e)


def get_credentials(s3bucket, s3path, s3conn):
    s3 = boto3.client('s3', aws_access_key_id=s3conn._key_id, aws_secret_access_key=s3conn._secret)
    with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
        s3.download_fileobj(s3bucket, s3path, tmp_file)
        creds_path = tmp_file.name  # Local path to the downloaded file
    creds = None
    creds = Credentials.from_authorized_user_file(creds_path, SCOPES)
    return creds

def SendMessage(s3bucket, s3path, s3conn, sender, to, subject, body_html , filenames : list=None,attachments: list=None):
    credentials = get_credentials(s3bucket, s3path, s3conn)
    if credentials== None:
        return print("credentials not found, Generate credentials")
    
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        
        html = body_html
        
        body=MIMEText(html, 'html')
        message.set_content(body)
        
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype= (attachment.split('.')[1]), filename=filenames[i])


        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None
        
def build_or_query(keyword_list, start_date,country):
    end_date=start_date
    q_string_or='['
    for l in range(len(keyword_list)):
        if l != (len(keyword_list)-1):
            q_or_tmp='{"match_phrase": {"body": "'+keyword_list[l]+'"}}'
            q_string_or=q_string_or+q_or_tmp+','
        else:
            q_or_tmp='{"match_phrase": {"body": "'+keyword_list[l]+'"}}'
            q_string_or=q_string_or+q_or_tmp+']'
    if(str(country)!='nan'):
        if country == 'EUR':
            q_or='{"_source":["id", "sentiment.body.score", "sentiment.body.polarity"],"query": {"bool": {"must": [{"bool": {"should": '+q_string_or+'}}, {"range": {"published_at": {"gte": "' + start_date + 'T00:00:00Z","lte": "' + end_date + 'T23:59:59Z"}}},{"terms": {"source.locations.country.keyword": '+EU_countries_str+'}}]}}}'
        else:
            q_or='{"_source":["id", "sentiment.body.score", "sentiment.body.polarity"],"query": {"bool": {"must": [{"bool": {"should": '+q_string_or+'}}, {"range": {"published_at": {"gte": "' + start_date + 'T00:00:00Z","lte": "' + end_date + 'T23:59:59Z"}}},{"bool": {"must":{"match_phrase": {"source.locations.country": "'+country+'"}}}}]}}}'
    else:
        q_or='{"_source":["id", "sentiment.body.score", "sentiment.body.polarity"],"query": {"bool": {"must": [{"bool": {"should": '+q_string_or+'}}, {"range": {"published_at": {"gte": "' + start_date + 'T00:00:00Z","lte": "' + end_date + 'T23:59:59Z"}}}]}}}'
   
    return q_or

def senti_multi_thread(i):
        global or_df
        global cc
        global k_l
        d=pd.DataFrame()
        tmp_and_kw3 = k_l
        q_and_tmp1=build_or_query(tmp_and_kw3,str(i.date()),cc)
        id = 'quantexa_data_*'
        result123 = client_es.search(index=id, body=q_and_tmp1, size=100)
        df_tmp = pd.json_normalize(result123['hits']['hits'])
        if(df_tmp.shape[0]!=0) and '_source.sentiment.body.score' in df_tmp.columns :
            df_tmp['sentiment_score'] = df_tmp.apply(calculate_final_score, axis=1)
            df_tmp['date']=i.date()
            df_tmp['sentiment_score']= df_tmp['sentiment_score'].astype('float')
            pos_df=df_tmp[df_tmp['sentiment_score']>=0]
            neg_df=df_tmp[df_tmp['sentiment_score']<0]
            pos_sen = neg_sen = 0.0
            if len(pos_df) > 0:
                pos_sen=pos_df['sentiment_score'].sum()/len(pos_df)
            if len(neg_df) > 0:
                neg_sen=neg_df['sentiment_score'].sum()/len(neg_df)
        else:
            pos_sen=0.0
            neg_sen=0.0
        d['date']=[str(i.date())]
        d['pos_sen']=[pos_sen]
        d['neg_sen']=[neg_sen]
        or_df=pd.concat([or_df,d],axis=0)
        #print(or_df)
        
def find_the_senti_df(k_l1,date_list,c_c):
    global k_l
    global cc
    global or_df
    cc = c_c
    k_l = k_l1
    or_df=pd.DataFrame()
    with ThreadPoolExecutor(max_workers=10) as exe:
        exe.map(senti_multi_thread,date_list)
#     print(or_df.shape)
    return or_df

def get_calender_date_list(string_start_date,string_end_date):
    end_date = string_end_date
    start_date = string_start_date
    dr = end_date - start_date
    N = dr.days
    date_list = [end_date - timedelta(days=float(x)) for x in reversed(range(N+1))]

    return date_list

def create_date_range(st_date,end_date):
    
    from datetime import date, timedelta
    
    start_dt = st_date
    end_dt = end_date
    
    # difference between current and previous date
    delta = BDay(1)
    
    # store the dates between two dates in a list
    dates = []
    
    while start_dt <= end_dt:
        # add current date to list by converting  it to iso format
        dates.append(start_dt)
        # increment start date by timedelta
        start_dt += delta
    return dates



def senti_data(country_code,start_date,k_l):
    cur_date = start_date
    prev = cur_date-timedelta(30)
    kk = get_calender_date_list(prev,cur_date)
    df = find_the_senti_df(k_l,kk,country_code)
    lf = df.mean()
    posi = lf['pos_sen']
    negi = lf['neg_sen']
    return (posi,negi)
    
def input_data_for_features(country_code,dates):
    final_df = pd.DataFrame()
    for st_date in dates:
        end_date=st_date
        start_date=end_date-timedelta(30)
        for j in macro_deployments.index:
            try:
                if macro_deployments['Country_Code'][j] in country_code:
                    current_country_code = []
                    if macro_deployments['Country_Code'][j] == 'EUR':
                        current_country_code = europe_sp[macro_deployments['Country_Code'][j]]
                    else:
                        current_country_code.append(macro_deployments['Country_Code'][j])
                    macro_df=pd.DataFrame(columns=ast.literal_eval(macro_deployments['trained_on_cols'][j]))
                    data=pd.DataFrame({'dates':pd.date_range(start=(end_date-timedelta(700)).strftime("%m/%d/%Y"), end=end_date.strftime("%m/%d/%Y"))})
                    macro_df['date']=data['dates']
                    for x in current_country_code:
                        # print(type(x))
                        
                        result=requests.get(config['Inhouse_API']['inhouse_api_url_for_macroeconomic_data'].format(country=x,startdate=(end_date-timedelta(700)).strftime("%Y-%m-%d"),enddate = end_date.strftime("%Y-%m-%d")))
                        # display(result.json())
                        data=result.json()['data']
                        for i in data:
                            z = macro_df['date']==datetime.strptime(i['date'],"%Y-%m-%d")
                            if(i['data_point']not in macro_df.columns):
                                # print(i['data_point'])
                                continue
                            if i['data_point'] in macro_df.columns and len(macro_df[i['data_point']].index[z])!=0:
                                l = macro_df[i['data_point']].index[z][0]
                                macro_df.at[l,i['data_point']]=i['value']
                    # display(macro_df)
                    macro_df=macro_df.fillna(method='ffill')
                    macro_df=macro=macro_df.fillna(0)
                    data=pd.DataFrame({'dates':pd.date_range(start=start_date.strftime("%m/%d/%Y"), end=end_date.strftime("%m/%d/%Y"))})
                    #print(macro_deployments['Ticker'][j])
                    spg_url = config['SNP_data']['url']
                    spg_headers = config['SNP_data']['headers']
                    data_json = {
                                      "inputRequests": [
    
                                              {
                                          "function": "GDST",
                                          "identifier": macro_deployments['Ticker'][j], ### IQsectorcode
                                          "mnemonic": 'IQ_CLOSEPRICE',
                                        "properties":{
                                                    "currencyID" : "USD",
                                            "startDate" :(end_date-timedelta(90)).strftime("%m/%d/%Y"), ### startdate
                                            "endDate" : end_date.strftime("%m/%d/%Y"), ###end date
                                            "frequency": "daily"
                                                }
                                            },
    
    
                                            ]
                                    }
    
                    data_json = json.dumps(data_json)
                    response = requests.post(spg_url, data=data_json, headers=spg_headers)
                    response_json=json.loads(response.text)
                    df_temp=pd.DataFrame()
                    if 'Headers' in response_json['GDSSDKResponse'][0]:
                        df_temp['dates']=response_json['GDSSDKResponse'][0]['Headers']
                        df_temp['close_val']=response_json['GDSSDKResponse'][0]['Rows'][0]['Row']
                    for i in df_temp.index:
                        if df_temp['dates'][i] == '' or df_temp['dates'][i] == ' ':
                            continue
                        else:
                            df_temp['dates'][i]=datetime.strptime(df_temp['dates'][i],'%m/%d/%Y').date()
                    df_temp['close_val']=df_temp['close_val'].apply(lambda x:float(x) if x != 'Data Unavailable' else x)
                    macro_df = macro_df.tail(1)
                    df_temp = df_temp.tail(1)
                    df_temp = df_temp.fillna(method='ffill')
                    macro_df['close_val'] = df_temp['close_val'].values
                    macro_df['date'] = df_temp['dates'].values
                    senti_c = mapi[macro_deployments['Country_Code'][j]]
                    str_type = macro_deployments['Type'][j]
                    str_type = str_type.upper()
                    k_l = macro_keywords[str_type].dropna().values.tolist()
                    try:
                        jj = senti_data(senti_c,st_date,k_l)
                        macro_df['avg_pos'] = jj[0]
                        macro_df['avg_neg'] = jj[1]
                    except:
                        macro_df['avg_pos'] = 0
                        macro_df['avg_neg'] = 0
                    macro_df['isin'] = [macro_deployments['Country_Code'][j] + '_' + macro_deployments['Type'][j]]
                    macro_df['country_code'] = macro_deployments['Country_Code'][j]
                    macro_df['type'] = macro_deployments['Type'][j]
                    macro_df['schedular'] = ['monthly']
                    if final_df.empty :
                        final_df = macro_df
                    else:
                        final_df = pd.concat([final_df, macro_df], ignore_index=True)
            except:
                print(traceback.format_exc())
                print('error')
    return final_df

def test_data_generation(country_code,type,dates):
    final_feature=[]
    for st_date in dates:
        end_date=st_date
        start_date=end_date-timedelta(30)
        for day in range(1):
            end_date=st_date
            start_date=end_date-timedelta(30)
            for j in macro_deployments.index:
                #try:
                if macro_deployments['Type'][j] != type:
                    #print('dfdgrg')
                    continue
                if macro_deployments['Country_Code'][j] in country_code:
                    current_country_code = []
                    if macro_deployments['Country_Code'][j] == 'EUR':
                        current_country_code = europe_sp[macro_deployments['Country_Code'][j]]
                    else:
                        current_country_code.append(macro_deployments['Country_Code'][j])
#                         print(j)
#                         print(macro_deployments['Type'][j])
                    macro_df=pd.DataFrame(columns=ast.literal_eval(macro_deployments['trained_on_cols'][j]))
                    #display(macro_df.columns)
                    #print(len(macro_df.columns))
                    data=pd.DataFrame({'dates':pd.date_range(start=(end_date-timedelta(700)).strftime("%m/%d/%Y"), end=end_date.strftime("%m/%d/%Y"))})
                    macro_df['date']=data['dates']
                    for x in current_country_code:
                        result=requests.get(config['Inhouse_API']['inhouse_api_url_for_macroeconomic_data'].format(country=x,startdate=(end_date-timedelta(700)).strftime("%Y-%m-%d"),enddate = end_date.strftime("%Y-%m-%d")))
                        data=result.json()['data']
                        for i in data:
                            z = macro_df['date']==datetime.strptime(i['date'],"%Y-%m-%d")
                            if(i['data_point'] not in macro_df.columns):
                                continue
                            if i['data_point'] in macro_df.columns and len(macro_df[i['data_point']].index[z])!=0:
                                l = macro_df[i['data_point']].index[z][0]
                                macro_df.at[l,i['data_point']]=i['value']
                    macro_df=macro_df.fillna(method='ffill')
                    macro_df=macro=macro_df.fillna(0)
                    data=pd.DataFrame({'dates':pd.date_range(start=start_date.strftime("%m/%d/%Y"), end=end_date.strftime("%m/%d/%Y"))})
                    spg_url = config['SNP_data']['url']
                    spg_headers = config['SNP_data']['headers']
                    data_json = {
                                      "inputRequests": [

                                              {
                                          "function": "GDST",
                                          "identifier": macro_deployments['Ticker'][j], ### IQsectorcode
                                          "mnemonic": 'IQ_CLOSEPRICE',
                                        "properties":{
                                                    "currencyID" : "USD",
                                            "startDate" :(end_date-timedelta(90)).strftime("%m/%d/%Y"), ### startdate
                                            "endDate" : end_date.strftime("%m/%d/%Y"), ###end date
                                            "frequency": "daily"
                                                }
                                            },


                                            ]
                                    }
                    data_json = json.dumps(data_json)
                    response = requests.post(spg_url, data=data_json, headers=spg_headers)
                    response_json=json.loads(response.text)
                    df_temp=pd.DataFrame()
                    if 'Headers' in response_json['GDSSDKResponse'][0]:
                        df_temp['dates']=response_json['GDSSDKResponse'][0]['Headers']
                        df_temp['close_val']=response_json['GDSSDKResponse'][0]['Rows'][0]['Row']
                    for i in df_temp.index:
                        if df_temp['dates'][i] == '':
                            continue
                        df_temp['dates'][i]=datetime.strptime(df_temp['dates'][i],'%m/%d/%Y').date()
                    df_temp['close_val']=df_temp['close_val'].apply(lambda x:float(x))
                    macro_df = macro_df.tail(1)
                    df_temp = df_temp.tail(1)
                    df_temp = df_temp.fillna(method='ffill')
                    macro_df['close_val'] = df_temp['close_val'].values
                    macro_df['date'] = df_temp['dates'].values
                    senti_c = mapi[macro_deployments['Country_Code'][j]]
                    str_type = macro_deployments['Type'][j]
                    str_type = str_type.upper()
                    k_l = macro_keywords[str_type].dropna().values.tolist()
                    try:
                        jj = senti_data(senti_c,st_date,k_l)
                        macro_df['avg_pos'] = jj[0]
                        macro_df['avg_neg'] = jj[1]
                    except:
                        macro_df['avg_pos'] = 0
                        macro_df['avg_neg'] = 0
                    macro_df['date'] = macro_df['date'].apply(lambda x:pd.to_datetime(x).strftime('%Y-%m-%d'))
                    macro_df = macro_df[ast.literal_eval(macro_deployments['trained_on_cols'][j])]
                    k = [{'fields': macro_df.columns.tolist(),'values': macro_df.values.tolist()}]
                    #kl = macro_df.drop(['date'],axis=1)
                    #print(len(macro_df.columns))
                    kl = macro_df.values.tolist()
                    final_feature.append(kl[0])
#                 except:
#                     print(traceback.format_exc())
#                     print('error')
    final_array = np.array(final_feature)
    return final_array

def input_data_for_prediction(country_code,dates):
    
    apikey = config['IBM_creds']['wml_credentials']['apikey']
    url = config['IBM_creds']['wml_credentials']['url']
    ibm_conn = IBMConnection(url,apikey)
    client = ibm_conn.getIBMConnection()
    client.set.default_space(config['IBM_creds']['default_space_id'])
    deployment_to_submit = pd.DataFrame()
    for day in range(1):
        for j in macro_deployments.index:
            try:
                if macro_deployments['Country_Code'][j] in country_code:
                    c_c = macro_deployments['Country_Code'][j]
                    type = macro_deployments['Type'][j]
                    test = test_data_generation(c_c,type,dates)
                    #columns = macro_deployments['trained_on_cols'][j]
                    space = macro_deployments['space_id'][j]
                    id = macro_deployments['deployment_id'][j]
                    isin = macro_deployments['Country_Code'][j]+'_'+macro_deployments['Type'][j]
                    if(deployment_to_submit.empty):
                        deployment_to_submit['isin'] = [isin]
                        deployment_to_submit['deployment_space'] = [space]
                        deployment_to_submit['deployment_id'] = [id]
                        deployment_to_submit['test_data'] = [test]
                    else :
                        deployment_to_submit.loc[len(deployment_to_submit.index)] = [isin,space,id,test]
            except:
                print(traceback.format_exc())
                print('error')
    return deployment_to_submit

def prediction_funtion(country_code,dates,deployment):
    apikey = config['IBM_creds']['wml_credentials']['apikey']
    url = config['IBM_creds']['wml_credentials']['url']
    ibm_conn = IBMConnection(url,apikey)
    client = ibm_conn.getIBMConnection()
    prediction = PredictionHelper(ibm_conn)
    #try:
    pred_df = prediction.prediction_run(deployment)
    final_df1 = pd.DataFrame()
    for ind,row in pred_df.iterrows():
        st = row['status']
        indi = 0
        for date in dates:
            if(final_df1.empty):
                final_df1['isin'] = [row['isin']]
                final_df1['deployment_space']=[row['deployment_space']]
                final_df1['deployment_id'] = [row['deployment_id']]
                final_df1['status'] = [row['status']]
                # final_df1['date'] = [date]
                if(st=='completed'):
                    final_df1['predictions'] = [row['predictions'][indi]]
                else:
                    final_df1['predictions'] = [None]
            else:
                prediction = None
                if(st=='completed'):
                    prediction = row['predictions'][indi]
                final_df1.loc[len(final_df1.index)] = [row['isin'],row['deployment_space'],row['deployment_id'],row['status'],prediction]
            indi = indi + 1
    return final_df1         
#     except:
#         print(traceback.format_exc())
#         print('error')

def trigger_predict(country_code,st_date):
    #try :
    macro_df = input_data_for_features(country_code,st_date)
    #display(macro_df)
    deployment_to_submit = input_data_for_prediction(country_code,st_date)
    #display(deployment_to_submit)
    pred_df = prediction_funtion(country_code,st_date,deployment_to_submit)
    macro_df['date'] = pd.to_datetime(macro_df['date']).dt.date

#     macro_df['date'] = macro_df['date'].astype('datetime64').dt.date
    # pred_df['date'] = pred_df['date'].astype('datetime64').dt.date
#     print(macro_df.dtypes)
#     print(pred_df.dtypes)
    #display(pred_df)
    #display(macro_df)
    final_df = pd.merge(macro_df,pred_df,on=['isin'],how='left')
    return final_df
    # except:
    #     print(traceback.format_exc())
    #     print('error')
    
def upload_data_to_elastic_search(es_obj, df,es_index):
    print('sssssssssssssssssssssssssssssssssssss')
    if df.isna().any().any():
        print('mddhdhdhdfhdghdfghdfhdhdghdgfhdhdfhdfgghd')
        
    documents = []
    for index, row in df.iterrows():
        # if row['predictions'].isna().any():
        #     continue
        try:
            doc_id = f"{row['country_code']}_{row['type']}_{row['date']}"
        except:
            doc_id = f"{row['isin']}_{row['date']}"
        year = row['date'].year
        document = {"index": {"_index": f'{es_index}_{year}', "_id": doc_id}}
        print(document)
        data = row.to_dict()
        documents.append(document)
        documents.append(data)
    #print("len",len(documents))
    response=es_obj.client.bulk(documents)
    #print(response)
    
def s3_versioning(df):
    for i in df.index:
        country_code = df.iloc[[i]][['country_code']].values[0][0]
        asset_type = df.iloc[[i]][['type']].values[0][0]
        date = df.iloc[[i]][['date']].values[0][0].strftime("%Y-%m-%d")
        ss.write_advanced_as_df(df.iloc[[i]], config['S3_paths']['macro_daily_predictions_s3versioning_bucket'],config['S3_paths']['macro_daily_predictions_s3versioning_path'].format(country_code=country_code,asset_type=asset_type,date = date))
        
def s3_versioning_metrics(df):
    df.reset_index(inplace=True,drop=True)
    for i in df.index:
        name = df.iloc[[i]][['isin']].values[0][0]
        date = pd.to_datetime(df.iloc[[i]][['date']].values[0][0]).strftime("%Y-%m-%d")
        ss.write_advanced_as_df(df.iloc[[i]], config['S3_paths']['macro_daily_metrics_s3versioning_bucket'],config['S3_paths']['macro_daily_metrics_s3versioning_path'].format(date=date,name=name))
        
        
def directionality_score_calc(prediction_direction, close_direction):
    directionality_df = pd.DataFrame()
    directionality_df['prediction_direction'] = prediction_direction
    directionality_df['close_direction'] = close_direction
    correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)] 
    incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)] 
    relaxation_count = len(incorrect_direction_df[(abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)]) 
    if len(directionality_df) == relaxation_count:
        directionality_score = np.nan
    else:
        directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
    return directionality_score


def accuracy_function(df_series, coff = 500):
    '''
    Accuracy conversion metric to convert daily APE into accuracy for ER.
    '''
    return df_series.apply(lambda x: 97/( 1 + 20 * np.exp((-coff/x))) if x <  100 else max(0, 106.7 - 0.213 * x))

def calculate_accuracy(df_data, prediction_col = 'predictions', target_col = 'close_change_monthly', coff = 500):
    '''
    This function calculates the accuracy based on actual er and predicted er for an isin for daily, 14 & 22 trading days rolling average.
    Accuracy calculations:
        - Calculate the modified APE for individual data points (modification: divide by max( abs(actual), abs(pred) )) )
        - Convert it to accuracy using 100/1+20*(exp^-500/x) if x < 100 and we linearly degrade to 0 for ape = 500
        

    Input params (required):
        - date
        - prediction_col : predicted ER value in percentage (prediction for today's date that are generated 1 month back ie. if date is 28 Dec 2023, it'll have the predictions generated on 28 Nov 2023)
        - target_col : target column value in percentage, it's also shifted by 22 days similar to prediction_col
        - coff : coefficient to be used in accuracy function

    Output columns:
        - accuracy_1_day: accuracy calculated for each day
        - accuracy_14_day : accuracy calculated for 14 day rolling window
        - accuracy_22_day : accuracy calculated for 22 day rolling window

    Range of columns:
        - prediction_col : [0, 100]
        - target_col : [0, 100]
        - accuracy: [0, 100]
    '''
    if prediction_col not in df_data.columns:
        raise Exception('Prediction column not in Dataframe')

    if target_col not in df_data.columns:
        raise Exception('Target column not in Dataframe')
    
    # Remove any nan's in prediction or target cols
    df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

    # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
    df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
    df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

    # Calculate RMS of MAPE over a rolling of 14 days
    df_data['accuracy_1_day'] = accuracy_function(df_data['daily_ape'], coff = coff)

    # Calculate RMS of MAPE over a rolling of 22 days
    df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
    df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5
    
    df_data.drop(columns=['denominator'], inplace=True)
    
    return df_data

def get_es_data(isin, dates, index_prefix, schedular = None):
    if schedular is not None:
        schedular = schedular.capitalize()
    data=[]
    for year in range(dates[0], dates[1] + 1):
        q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
        try:
            result = es.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
            for rs in result['hits']['hits']:
                es_data=rs['_source']
                data.append(es_data)
        except:
            print(f'ES data for {index_prefix} not present for {year}.')
    df=pd.DataFrame(data)
    if (schedular != None) and ('schedular' in df.columns):
        df = df[df['schedular'] == schedular].reset_index(drop = True)
    if len(df) == 0:
        return df
    df['date']=pd.to_datetime(df['date'])
    df.sort_values('date', ascending=True, inplace=True)
    df.reset_index(inplace=True, drop=True)
    return df


def read_data(isin, years, read_from, pred_col, schedular, es_index, bucket_name, path_loc, local_folder):
    default_schedular = 'monthly'
    #try:
    if read_from == 'es': # fetching data from es
        temp = get_es_data(isin, years, es_index)[['date', pred_col,'close_val']] # fetching data from es
        temp['pct']=temp['close_val'].pct_change(22)*100
#             temp['output']=temp['pct'].shift(-22, axis = 0)
        temp.drop(columns=['close_val'],inplace=True)
        print(temp)
    elif read_from == 's3':
        temp = s3h.s32df(bucket_name, f'{path_loc}/{isin}.csv')[['date', pred_col]] # fetching data from s3
    else:
        temp = pd.read_csv(os.path.join(local_folder, isin))[['date', pred_col]] # fetching data from local
    if schedular == None or schedular == '':
        schedular = default_schedular
#     except:
#         return isin, pd.DataFrame()
    
#     company = all_df[[x == isin for x in all_df['isin']]][['tic', 'exchange', 'isin', 'ipodate', 'ciq_trading_id']].reset_index(drop = True)
#     if len(company) == 0:
#         print(f'Isin {isin} not in Masters Active Firms')
#         return isin, pd.DataFrame()
#     try:
#         close_change = get_close_change(company, schedular_dict[schedular])
#         temp = pd.merge(temp, close_change, on = 'date').rename(columns = {'percent_pct': 'close_change'})
    temp.rename(columns = {pred_col:f'{schedular}_predictions', 
                     'pct': f'actual_{schedular}_returns'}, 
          inplace = True)
    temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].shift(schedular_dict[schedular])
    temp['date'] = pd.to_datetime(temp['date'])
    temp = temp[(temp['date'] >= pd.to_datetime(f'{years[0]}-01-01')) & (temp['date'] <= pd.to_datetime(f'{years[1]}-12-31'))]    
    temp = temp.drop_duplicates(subset = 'date').sort_values(by = 'date').reset_index(drop = True)
    temp.reset_index(drop = True, inplace = True)
    temp = temp.ffill().fillna(0)
    temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].astype(float, errors="ignore")
    temp[f'actual_{schedular}_returns'] = temp[f'actual_{schedular}_returns'].astype(float, errors="ignore")
    print(f'Data collected for isin {isin}.')
#     except:
#         temp = pd.DataFrame()
    return isin, temp
 
def read_data_parallely(list_of_isins, years, read_from = 'es', pred_col = 'predictions', n_workers = 100, **kwargs): # function to read historical data
    '''
    'kwargs' keys-
        'es_index': index of Elastic Search to fetch data from (if read_from is 'es')
        'bucket_name': name of S3 bucket where data is stored (if read_from is 's3')
        'path_loc': S3 folder location to fetch data from (if read_from is 's3')
        'local_folder': path for folder to fetch data from (if data needs to be collected from local)
        'schedular': 'daily', 'weekly' or 'monthly'
    '''
    if 'schedular' in kwargs.keys():
        schedular = kwargs['schedular'].lower()
    else:
        schedular = None
    if read_from == 'es': # fetching data from es
        es_index = kwargs['es_index']
        bucket_name = None
        path_loc = None
        local_folder = None
    elif read_from == 's3':
        bucket_name = kwargs['bucket_name']
        path_loc = kwargs['path_loc']
        es_index = None
        local_folder = None
    else:
        local_folder = kwargs['local_folder']
        es_index = None
        bucket_name = None
        path_loc = None
    n_calls = len(list_of_isins)
    with ThreadPoolExecutor(max_workers=n_workers) as exe:
        dfs = exe.map(read_data, 
                     list_of_isins, 
                     [years] * n_calls, 
                     [read_from] * n_calls, 
                     [pred_col] * n_calls, 
                     [schedular] * n_calls, 
                     [es_index] * n_calls, 
                     [bucket_name] * n_calls, 
                     [path_loc] * n_calls, 
                     [local_folder] * n_calls)
    data_dict = {}
    for isin, data in dfs:
        data_dict[isin] = data
    print(len(data_dict))
    return data_dict

def calculate_metrics (etf, temp_df, lastbday, schedular,  prediction_column, actual_column):
    '''
        Function to calculate metrics using Inhouse Library function
    '''
    print(temp_df)
    if schedular == 'Monthly':
        shift_window = 22
    elif schedular == 'Quarterly':
        shift_window = 66
 
    isin = temp_df['isin'].values[0]
    
    temp_df = temp_df[['date',actual_column, prediction_column]].copy()
    temp_df[actual_column] = temp_df[actual_column].shift(shift_window)
    temp_df[prediction_column] = temp_df[prediction_column].shift(shift_window)
    
    temp_df = temp_df.dropna()
 
    temp_df = temp_df[-700:] ### assuming the daily run needs a single row, so no need to iterate over all historical
    temp_df.reset_index(inplace = True, drop = True)
    
    new_metrics = metrics_obj.calculate_metrics_helper(temp_df , isin =isin, prediction_column =prediction_column,
                                                       actual_column = actual_column)
    new_metrics = new_metrics[new_metrics['date']<= lastbday]
    metrics_oneday = new_metrics[-1:]
    metrics_oneday.reset_index(inplace = True, drop = True)
    metrics_oneday.rename(columns={'actual_returns':'actual_monthly_returns','predictions':'monthly_predictions'}, inplace = True)
    # metrics_oneday = metrics_oneday.drop(columns = ['actual_returns','predictions'], axis = 1)
 
    metrics_oneday['tic'] = etf
    return metrics_oneday


def get_macro_metrics(cp_date):
    years = [(int(cp_date.split('-')[0])-3),(int(cp_date.split('-')[0]))]
    es_index = config['Metrics_data']['es_index']
    prediction_col_name = 'predictions'
    list_of_geos = config['Misc']['list_of_geos']
    schedular = 'monthly'
    data_source = 'es'
    parallel_workers = 120
    n_fin_features = 128 # number of input features in financial models
    data_dict = read_data_parallely(list_of_geos,
                                years,
                                read_from = 'es',
                                n_workers = parallel_workers,
                                es_index = es_index,
                                schedular = schedular)
#     today = date

    daily_metrics = pd.DataFrame()
    mtx_2_calc1 = config['Metrics_data']['all_metrics']
    mtx_2_calc2 = ["confidence_score", "avg_confidence_score"]

    for isin in data_dict.keys(): 
        df = data_dict[isin]
        df = df[df['date'] <= pd.to_datetime(cp_date)]
        df['isin'] = isin
        if len(df) == 0:
            print(f"Data not available for {isin}.")
            continue

    #     df[f'{schedular}_predictions'] = df[f'{schedular}_predictions'] * 100 # run this in case predictions are rations in range 0-1.
        
        metrics_df1 = calculate_metrics (isin, df, cp_date, config['Metrics_data']['schedular'],  'monthly_predictions', 'actual_monthly_returns')
        if type(metrics_df1) ==  str:
            print(metrics_df1)
            continue
#         metrics_df2 = calculate_metrics(df, isin, n_features=n_fin_features, metrics_to_calculate = mtx_2_calc2).drop(columns = ['isin','actual_monthly_returns', 'monthly_predictions'])
#         if type(metrics_df2) ==  str:
#             print(metrics_df2)
#             continue
#         metrics_df = pd.merge(metrics_df1, metrics_df2, on = 'date', how = 'left')

        daily_metrics = pd.concat([daily_metrics,metrics_df1], ignore_index=True)

# #         break
#     daily_metrics['date'] = pd.to_datetime(yesterday)
#     daily_metrics = daily_metrics[req_columns + all_metrics]
    return daily_metrics

def upload_no_of_jobs(df):
    try:
        df_temp = ss.read_as_dataframe(config['S3_paths']['macro_job_count_bucket'],config['S3_paths']['macro_job_count_path'])
        df_temp = pd.concat([df_temp, df], ignore_index=True)
        #df_temp.to_csv('job_count.csv',index=False)
        ss.write_advanced_as_df(df_temp,config['S3_paths']['macro_job_count_bucket'],config['S3_paths']['macro_job_count_path'])
    except:
        #df.to_csv('job_count.csv',index=False)
        ss.write_advanced_as_df(df,config['S3_paths']['macro_job_count_bucket'],config['S3_paths']['macro_job_count_path'])   
        
        
def trigger_and_save(st_date):
    data = trigger_predict(temp_kapo_lll,st_date)
    data.fillna(0,inplace=True)
    upload_data_to_elastic_search(es,data,config['Metrics_data']['es_index'])
    s3_versioning(data)
    metrics_df = get_macro_metrics(str(st_date[0]).split(' ')[0])
    metrics_df.reset_index(drop=True,inplace=True)
    metrics_df.replace({np.nan:None},inplace=True)
#     metrics_df['date'] = metrics_df['date'].apply(lambda x:(pd.to_datetime(x)).strftime('%Y-%m-%d'))
    upload_data_to_elastic_search(es,metrics_df,config['Metrics_data']['metrics_es_index'])
    s3_versioning_metrics(metrics_df)
    for j in macro_deployments.index:
        print('starting')
        isin_for_preds = macro_deployments['Country_Code'][j]+'_'+macro_deployments['Type'][j]
        isin_for_metrics = macro_deployments['Country_Code'][j]+'_'+macro_deployments['Type'][j].lower()
        print(isin_for_preds)
        req_pred_data = data.loc[data['isin']==isin_for_preds]
        req_metrics_data = metrics_df.loc[metrics_df['isin']==isin_for_metrics]
        
        Country = macro_deployments['Country'][j]
        Type = macro_deployments['Type'][j]
        path = Country + "_" + Type.lower()
        if not (req_metrics_data.empty):
            ss.write_advanced_as_df(req_metrics_data, config['S3_paths']['macro_daily_metrics_bucket'],config['S3_paths']['macro_daily_metrics_path'].format(Country=Country,Type=Type,date=req_pred_data['date'].values[0].strftime("%Y-%m-%d")))
        
        if not (req_pred_data.empty):
            ss.write_advanced_as_df(req_pred_data, config['S3_paths']['macro_daily_predictions_bucket'],config['S3_paths']['macro_daily_predictions_path'].format(Country=Country,Type=Type,date=str(req_pred_data['date'].values[0])))         
    return data,metrics_df

def prepare_body_html(body):
    if body:
        receiver = config['Gmail_creds']['email_receiver_failed']
        html = f"""\
                    <html>
                      <head></head>
                      <body>
                       {body}
                      </body>
                    </html>
                    """
        
    else:
        receiver = config['Gmail_creds']['email_receiver_success']
        html  = f'<html><title></title><body>Hi,<br><p><b>Macro Model predictions and metrics files have been sucessfuly generated.</b> <br></p><br>Thanks</body></html>'
    receiver = [email.strip() for email in receiver.split(',')]
    return html,receiver

def send_email(subject , body = None):
    gmail_creds_bucket = config['Gmail_creds']['gmail_creds_bucket']
    gmail_creds_path =config['Gmail_creds']['gmail_creds_path']
    sender = config['Gmail_creds']['gmail_sender']
    # receiver_passed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_passed')
    # receiver_failed = config.get('MONTHLY_BNP','monthly_bnp_email_receiver_failed')
    # receiver = [email.strip() for email in receiver.split(',')]
    body_html,receiver = prepare_body_html(body)
    SendMessage(gmail_creds_bucket , gmail_creds_path, ss, sender, receiver , subject, body_html)
        


    


