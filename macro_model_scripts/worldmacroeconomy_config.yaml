capiq_headers:
    "Authorization": "Basic ************************************************"
    "Content-Type": "application/json"
 
headers: {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
    'accept-encoding': 'gzip, deflate, br',
    'accept-language': 'en-GB,en;q=0.9,en-US;q=0.8,ml;q=0.7',
    'cache-control': 'max-age=0',
    'dnt': '1',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'none',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.122 Safari/537.36'
} 

imf_headers: {"User-Agent": "Mozilla/5.0"}

countries: ['ARG', 'AUS', 'AUT', 'BHS', 'BEL', 'BMU', 'BRA', 'VGB', 'CAN', 'CYM', 'CHL', 'CHN', 'COL', 'CYP', 'CZE', 'DNK', 'EGY', 'FIN', 'FRA',
            'GEO', 'DEU', 'GIB', 'GRC', 'GGY', 'HKG', 'HUN', 'IND', 'IDN', 'IRL', 'IMN', 'ISR', 'ITA', 'JPN', 'JEY', 'LIE', 'LUX', 'MYS', 'MLT',
            'MEX', 'MCO', 'NLD', 'NZL', 'NOR', 'PAK', 'PAN', 'PNG', 'PER', 'PHL', 'POL', 'PRT', 'QAT', 'RUS', 'SAU', 'SGP', 'ZAF', 'KOR', 'ESP',
            'SWE', 'CHE', 'TWN', 'THA', 'TUR', 'ARE', 'GBR', 'USA', 'EA19', 'EU27_2020', 'EUU', 'KWT']
            
data_dict:
    imf:  {
            "cpi": "PCPI_IX",
            "ppi": "PPPI_IX",
            "foreign_exchange_reserves": "RAXGFX_USD"
        }
    wb: {
            'current_account_balance': 'BN.CAB.XOKA.GD.ZS',
            'foreign_direct_investment': 'BX.KLT.DINV.WD.GD.ZS',
            'exports': 'BX.GSR.TOTL.CD',
            'imports': 'BM.GSR.TOTL.CD',
            'portfolio_investment_bonds': 'DT.NFL.BOND.CD',
            'total_reserves_minus_gold': 'FI.RES.XGLD.CD',
            'total_reserves': 'FI.RES.TOTL.CD',
            'gdp_real': 'NY.GDP.MKTP.CD',
            'gdp_nominal': 'NY.GDP.MKTP.KD',
            'population_growth': 'SP.POP.GROW',
            'unemployment': 'SL.UEM.TOTL.ZS',
            'employment': 'SL.EMP.TOTL.SP.ZS',
            'ppi': 'FP.WPI.TOTL',
            'cpi': 'FP.CPI.TOTL',
            'portfolio_investment_net': 'BN.KLT.PTXL.CD',
            'inflation': 'NY.GDP.DEFL.KD.ZG',
            'personal_consumption_expenditures': 'NE.CON.PRVT.PP.KD',
            'private_consumption': 'NE.CON.TOTL.CD'
        }
    oecd: {
            'oecd_leading_indicator':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_CLI,/.M.LI...AA...H?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'business_confidence_index':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_CLI,/.M.BCICP...AA...H?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'consumer_confidence_index':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_CLI,/.M.CCICP...AA...H?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'interest_rates':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_FINMARK,4.0/.M.IRLT.PA.....?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'govt_bond_yied_long_term_interest_rate':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_FINMARK,4.0/.M.IR3TIB.PA.....?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'govt_bond_yied_short_term_interest_rate':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_FINMARK,4.0/.M.IRSTCI.PA.....?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'm1':'https://sdmx.oecd.org/archive/rest/data/OECD,DF_DP_LIVE,/.M1...M?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'm2_m3':'https://sdmx.oecd.org/archive/rest/data/OECD,DF_DP_LIVE,/.M3...M?startPeriod=prev_year-01&dimensionAtObservation=AllDimensions',
            'industrial_production_index':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_INDSERV,4.3/.Q.PRVM.IX.BTE.N...?startPeriod=prev_year-Q1&dimensionAtObservation=AllDimensions',
            'retail_sales':'https://sdmx.oecd.org/public/rest/data/OECD.SDD.STES,DSD_STES@DF_INDSERV,4.3/.Q.TOVM.IX.G47.N...?startPeriod=prev_year-Q1&dimensionAtObservation=AllDimensions'
        }
    fred: [
                {'USA':{    'Payems': 'PAYEMS', 'Repo_rate': 'RRPONTSYAWARD', 'treasury_Spreads': 'T10Y2Y',
                            'Treasury_change': 'RESPPALGUOMNXAWXCH1NWW', 'inflation expectation': 'T5YIFR',
                             'inflation breakeven': 'T5YIE', 'ICE_BofA_BBB_US_Corporate': 'BAMLC0A4CBBBEY',
                            'Economic_Policy_Uncertainty': 'USEPUINDXD', 'Wilshire_US_Real_Estate': 'WILLREITIND',
                            'CBOE_Gold_ETF_Volatilit': 'GVZCLS'}},
                    {'CHN': {'World_Uncertainty': 'WUIHKG', 'Total_for_China': 'CHNSPASTT01GYM',
                                                     'Balance_of_Payments': 'TWNBCAGDPBP6PT'}},
                    {'JPN': {'Consumer_Price': 'JPNPCPIPCPPPT', 'Total_Share_Prices': 'SPASTT01JPM661N'}},
                   {'EA19': {
                            'World_Uncertainty': 'WUIEUROPE', 'ECB_Deposit_Facility_Rate': 'ECBDFR',
                            'Economic_Policy_Uncertainty': 'EUEPUINDXM'}},
                    {'EU27_2020': {'Economic_Policy_Uncertainty': 'EUEPUINDXM'}}
                    ]
        
url_dict:
    imf: "http://dataservices.imf.org/REST/SDMX_JSON.svc/CompactData/IFS"
    wb: "http://api.worldbank.org/v2/country/{}/indicator/{}?format=json&date={}:{}"
    
ns: {
        'generic': 'http://www.sdmx.org/resources/sdmxml/schemas/v2_1/data/generic',
        'message': 'http://www.sdmx.org/resources/sdmxml/schemas/v2_1/message',
        'common': 'http://www.sdmx.org/resources/sdmxml/schemas/v2_1/common'
    }
    
mnemonic_frequency: {
            'PAYEMS': 'M',
            'RRPONTSYAWARD': 'D',
            'T10Y2Y': 'D',
            'RESPPALGUOMNXAWXCH1NWW': 'W',
            'T5YIFR': 'D',
            'T5YIE': 'D',
            'BAMLC0A4CBBBEY': 'D',
            'USEPUINDXD': 'D',
            'WILLREITIND': 'M',
            'GVZCLS': 'D',
            'WUIHKG': 'Q',
            'CHNSPASTT01GYM': 'M',
            'TWNBCAGDPBP6PT': 'A',
            'JPNPCPIPCPPPT': 'A',
            'SPASTT01JPM661N': 'A',
            'WUIEUROPE': 'Q',
            'ECBDFR': 'D',
            'EUEPUINDXM': 'M'
        }
        
fred_creds:
    api: '57b0fb46e088b7bfcdf4b85ce5606edb'
    env_var: 'PYTHONHTTPSVERIFY'
    
common_gmail:
    scope: ['https://www.googleapis.com/auth/gmail.send']
    gmail_cred_bucket: etf-predictions
    gmail_cred_file: preportfolio/gmail_credentials/credentials.json
    
sender_name: "Sandra P <<EMAIL>>"
receiver_email: <EMAIL>

s3_paths:
    bucket_name: micro-ops-output
    file_name: macro-historical-data/macro_economic_data/country/world_macro_economic_data.csv

    