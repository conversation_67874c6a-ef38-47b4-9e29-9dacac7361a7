#!/usr/bin/env python
# coding: utf-8



import os
import smtplib
import ssl
import urllib.request
import xml.etree.ElementTree as ET
import boto3
import requests
from datetime import datetime, timedelta
import pandas as pd
from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config
from injectable import injectable, autowired, Autowired
import time
from fredapi import Fred
from collections import defaultdict
import sys
import yaml
import re
from functools import reduce
import json
from google.oauth2.credentials import Credentials as GoogleCredentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.message import EmailMessage
from email.mime.text import MIMEText
import base64

if '__file__' in globals():
        script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Default to current working directory
    script_dir = os.getcwd()
sys.path.insert(0, os.path.abspath(script_dir))
config_path = os.path.join(script_dir, 'worldmacroeconomy_config.yaml')  # Path to config.yaml
with open(config_path, 'r') as file:
    config = yaml.safe_load(file)

ssl._create_default_https_context = ssl._create_unverified_context
s3_client=s3_config()

def convert_macro_to_daily(df):
    df.index = pd.to_datetime(df.index)
    start_date = df.index.min() - pd.DateOffset(day=1)
    end_date = datetime.now().date()
    dates = pd.date_range(start_date, end_date, freq='D')
    dates.name = 'date'
    full_df = pd.DataFrame(index=dates)
    df = full_df.join(df)
    return df

def get_credentials():
    SCOPES = config['common_gmail']['scope']
    creds = None

    response = s3_client._s3Client.get_object(Bucket=config['common_gmail']['gmail_cred_bucket'], Key=config['common_gmail']['gmail_cred_file'])
    credentials_data = json.loads(response['Body'].read().decode("utf-8"))
    creds = GoogleCredentials.from_authorized_user_info(info=credentials_data, scopes=SCOPES)
    return creds 

def SendMessage(sender, to, subject, filename, message_text, attachments: list = None):
    credentials = get_credentials()
    if credentials == None:
        return print("credentials not found, Generate credentials")
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        html = message_text
        body = MIMEText(html, 'html')
        message.set_content(body)
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype=(
                        attachment.split('.')[1]), filename=filename[i])
        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        encoded_message = base64.urlsafe_b64encode(message.as_bytes())             .decode()

        create_message = {
            'raw': encoded_message
        }
        send_message = (service.users().messages().send(
            userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None             




capiq_headers = config['capiq_headers']
headers = config['headers']
    
class WorldMacroEconomyEntity:
    def __init__(self, id=None, data_point=None,
                 value=None,
                 date=None,
                 modified_on=None,
                 country_code=None):
        self.data_point = data_point
        self.value = value
        self.date = date
        self.modified_on = modified_on
        self.country_code = country_code

        
@injectable(singleton=True)
class WorldMacroEconomyService:
    def __init__(self):
        self.country_list = config['countries']
#         self.country_list = ['USA','AUS']
        self.failed_dict = defaultdict(list)
        self.error_log = ""

    def imf_data(self):
        imf_data_links = config['data_dict']['imf']
        self.error_log = ""
        df_list = []
        try:
            curr_year = datetime.now().year
            prev_year = curr_year - 5
            headers = config['imf_headers']

            for cnt, country in enumerate(self.country_list):
                print(f"\n==== {country} ====")
                indicator_dfs = []
                for i in imf_data_links:
                    attempts = 0
                    max_attempts = 5
                    wait_time = 5  # initial wait time in seconds

                    while attempts < max_attempts:
                        try:
                            date_range = f"?startPeriod={prev_year}&endPeriod={curr_year}"
                            base_url = config["url_dict"]["imf"]
                            data_code = imf_data_links[i]
                            country_code = country[:2]
                            country_df = None

                            url = f"{base_url}/M.{country_code}.{data_code}{date_range}"
                            print(url)
                            response = requests.get(url, headers=headers, timeout=120, verify=False)
                            print(f"Status {response.status_code} - {i}")

                            if response.status_code == 200 and "application/json" in response.headers.get("Content-Type", ""):
                                data = response.json()['CompactData']['DataSet']['Series']["Obs"]
                                rows=[]
                                for row in data:
                                    rows.append({
                                        "country_code": country,
                                        "date": row["@TIME_PERIOD"] + "-01",
                                        i: float(row["@OBS_VALUE"]) if row["@OBS_VALUE"] not in ["", "NA"] else None,
#                                         "frequency": "M"
                                    })
                                    print(row["@TIME_PERIOD"] + "-01" + ":-------------:" + row["@OBS_VALUE"] + ":--------------:" + i + ":------------:" + country)
                                df = pd.DataFrame(rows)
                                df['date'] = pd.to_datetime(df['date'])
                                indicator_dfs.append(df)
                                
                                break  # success, break retry loop

                            elif "rate limit exceeded" in response.text.lower() or "bandwidth limit exceeded" in response.text.lower():
                                print(f"Rate limited. Waiting {wait_time} seconds before retrying...")
                                time.sleep(wait_time)
                                wait_time *= 2  # Exponential backoff
                                attempts += 1
                            else:
                                print(f"Unexpected response for {country}-{i}: {response.text[:100]}")
                                self.failed_dict[country].append(i)
                                break

                        except Exception as ex:
                            self.failed_dict[country].append(i)
                            self.error_log += f"\nError occurred while scraping IMF Data of {i} for {country}, Error: {ex}"
                            break  # avoid retrying on other exceptions
                    
                    if attempts == max_attempts:
                        self.error_log += f"\n Final failure for {country}-{i} after {max_attempts} retries"
                        
                    time.sleep(1)
                if indicator_dfs:
                    merged = reduce(lambda left, right: pd.merge(left, right, on=["country_code", "date"], how="outer"), indicator_dfs)
                    df_list.append(merged)

            if df_list:
                final_df = pd.concat(df_list, axis=0).sort_values(by=["country_code", "date"]).reset_index(drop=True)
                return final_df
            else:
                return pd.DataFrame()
        except Exception as e:
            self.error_log += "\nError occurred while scraping IMF Data\n" + str(e)

    
    def world_bank_data(self):
        world_bank_urls = config['data_dict']['wb']
        curr_year = datetime.now().year
        prev_year = curr_year - 5
        all_dfs = []
        print('world bank data')
        for cnt, i in enumerate(self.country_list):
            print(cnt, i)
            try:
                # World bank
                for j in world_bank_urls:
                    time.sleep(1)
                    try:
                        url = config["url_dict"]["wb"].format(i, world_bank_urls[j], prev_year, curr_year)
                        print(url)
                        reponse = requests.get(url, verify=False, headers=headers, timeout=120)
                        if reponse.status_code == 200:
                            try:
                                data = reponse.json()[1] 
                            except:
                                print('response error in worldbank', reponse.json())
                                self.failed_dict[i].append(j)
                                continue
                            try: 
                                rows = []
                                for item in data:
                                    rows.append({
                                        "country_code": i,
                                        "date": item["date"] + "-01-01",
                                        j: float(item["value"]) if item["value"] is not None else None,
#                                         "frequency": "Y"
                                    })
                                    
                                    print(i,":--------------:",j,":-------:",item['value'],":---------------:",item["date"] + "-01-01")
                                df = pd.DataFrame(rows)
                                df = df[["country_code", "date", j]]
                                all_dfs.append(df)
                            except:
                                self.failed_dict[i].append(j)
                                print('parsing error in worldbank', data)
                                continue        
                    except Exception as e:
                        self.failed_dict[i].append(j)
                        self.error_log += "\n world_bank data error of {} for {}, error - {} \n".format(j, i, e)
                    
            except Exception as er:
                self.error_log += "\n error occured in {}, Error - {} \n".format(i, er)
#             break
        if all_dfs:
            df_concat = pd.concat(all_dfs, axis=0, ignore_index=True)
            df_final = df_concat.groupby(['country_code', 'date'], as_index=False).first()
            return df_final
        else:
            return pd.DataFrame()
                                     
    def oecd_data(self):
        curr_year = datetime.now().year
        prev_year = curr_year - 5
        urls_dict = config['data_dict']['oecd']  
        country_str = '+'.join(self.country_list)  
        all_dfs = []
        for data_point, base_url  in urls_dict.items():
            match = re.search(r'/\.([AQMD])(?=[^A-Z]|$)', base_url)
            freq = match.group(1)
            print(f"Fetching: {data_point}")                              
            try:
                url = base_url.replace('/.', f'/{country_str}.').replace('prev_year',str(prev_year))
                print(url)                      
                response = requests.get(url)
                response.raise_for_status()
                root = ET.fromstring(response.text)

                ns = config['ns']

                rows = []
                for obs in root.findall('.//generic:Obs', ns):
                    obs_data = {}
                    for val in obs.findall('.//generic:Value', ns):
                        obs_data[val.get('id')] = val.get('value')
                    obs_value = obs.find('.//generic:ObsValue', ns).get('value')

                    date_v = obs_data.get('TIME_PERIOD')
                    if 'Q1' in date_v:
                        date_v = date_v.replace("Q1", "01-01")
                    elif 'Q2' in date_v:
                        date_v = date_v.replace("Q2", "04-01")
                    elif 'Q3' in date_v:
                        date_v = date_v.replace("Q3", "07-01")
                    elif 'Q4' in date_v:
                        date_v = date_v.replace("Q4", "10-01")
                    elif len(date_v) == 7:  # YYYY-MM
                        date_v = f"{date_v}-01"

                    country = obs_data.get('REF_AREA') or obs_data.get('LOCATION')
                    rows.append({
                                    "country_code": country,
                                    "date": date_v,
                                    data_point: float(obs_value) if obs_value is not None else None,
#                                     "frequency": freq
                                })
                df = pd.DataFrame(rows)
                if len(df[df[['country_code','date']].duplicated()])>0:
                     print(data_point)
                     print(df[df[['country_code','date']].duplicated()])                 
                all_dfs.append(df)

            except Exception as err:
                print(f"[ERROR] {data_point}: {err}")
                self.error_log += f"\n OECD error for {data_point}, error - {err} \n"
                self.failed_dict[country].append(data_point)
#             break
        if all_dfs:
            df_final = reduce(lambda left, right: pd.merge(left, right, on=['country_code', 'date'], how='outer'), all_dfs)
            return df_final
        else:
            return pd.DataFrame()

    def fred_data(self):
        mnemonic_frequency = config['mnemonic_frequency']
        fred_list = config['data_dict']['fred']
                                                           
        if (not os.environ.get(config['fred_creds']['env_var'], '') and getattr(ssl, '_create_unverified_context', None)):
            ssl._create_default_https_context = ssl._create_unverified_context
            try:
                fred = Fred(api_key=config['fred_creds']['api'])
            except Exception as e:
                print(e)
        seen_columns = set(['country_code', 'date'])        
        all_rows = []
        try:
            for val in fred_list:
                print(val)
                for cunt, mnemo in val.items():
                    rows = []
                    for k, v in mnemo.items():
                        try:
                            cpi = pd.DataFrame(data=fred.get_series(v))
                            cpi.reset_index(inplace=True)
                            cpi.columns = ['date', v]
                            cpi.dropna(how='all', inplace=True)
                            cpi.date = pd.to_datetime(cpi.date, format='%d-%m-%Y')
                            cpi.sort_values(by="date", inplace=True)
                            cpi.set_index('date', inplace=True)
                            cpi = convert_macro_to_daily(cpi)
                            today = datetime.now()
                            five_years_ago = today - timedelta(days=5 * 365)
                            cpi = cpi[cpi.index >= pd.to_datetime(five_years_ago)]
                            cpi = cpi.reset_index()
                            print("country :-----:"+cunt+":----------:"+k)
                            if len(cpi) > 3:
                                for h in range(len(cpi)):
                                    try:
                                        all_rows.append({
                                            "country_code": cunt,
                                            "date": cpi.loc[h]['date'],
                                            k: float(cpi.loc[h][v]) if cpi.loc[h][v] is not None else None,
#                                              "frequency": mnemonic_frequency.get(v, "Unknown")
                                        })
                                    except Exception as e:
                                        print(e)
                        except Exception as e:
                            self.failed_dict[cunt].append(k)
                            print(e)
                            continue
                    df_all  = pd.DataFrame(all_rows)
                    
            if not df_all.empty:
                df_final = df_all.pivot_table(index=['country_code', 'date'], 
                                              values=[col for col in df_all.columns if col not in ['country_code', 'date', 'frequency']],
                                              aggfunc='first').reset_index()

                
                return df_final

            else:
                return pd.DataFrame()

        except Exception as e:
            print(e)




def main_method():
    try:
        service = WorldMacroEconomyService()
        start = time.time()
        try:
            fred_df = service.fred_data()
        except Exception as e:
            fred_df = pd.DataFrame(columns=['country_code', 'date'])
            service.error_log += "\n error occured in fred_data function, Error - {} \n".format(e)
        try:
            wb_df = service.world_bank_data()
        except Exception as e:
            wb_df = pd.DataFrame(columns=['country_code', 'date'])
            service.error_log += "\n error occured in world_bank_data function, Error - {} \n".format(e)
        try:
            imf_df = service.imf_data()
        except Exception as e:
            imf_df = pd.DataFrame(columns=['country_code', 'date'])
            service.error_log += "\n error occured in imf_data function, Error - {} \n".format(e)
        try:
            oecd_df = service.oecd_data()
        except Exception as e:
            oecd_df = pd.DataFrame(columns=['country_code', 'date'])
            service.error_log += "\n error occured in oecd_data function, Error - {} \n".format(e)

        all_dfs = [fred_df,wb_df,imf_df,oecd_df]
        df_total = pd.DataFrame(columns=['country_code', 'date'])
        for df in all_dfs:
            df['date']=pd.to_datetime(df['date'])
            df_total = pd.merge(df_total , df, on=['country_code', 'date'], how='outer', suffixes=('_df1', '_df2'))

        # For each overlapping column, prioritize df1 if not null, otherwise df2
        for col in ['cpi', 'ppi']:
            df_total[col] = df_total[f'{col}_df1'].combine_first(df_total[f'{col}_df2'])
            df_total.drop([f'{col}_df1', f'{col}_df2'], axis=1, inplace=True)

        df_total['modified_on'] = datetime.now().strftime("%Y-%m-%d")
        for country, cnt_df in df_total.groupby('country_code'):
            print(f'Saving data for {country}')  
            cnt_df['date']=pd.to_datetime(cnt_df['date'])
            cnt_df.set_index('date', inplace=True)
            date_df=convert_macro_to_daily(cnt_df)
            date_df=date_df.reset_index()
            cnt_df=pd.merge(date_df[['date']],cnt_df,how='left',on='date')
            cnt_df=cnt_df.sort_values('date')
            file_name = config['s3_paths']['file_name'].replace('country',country)
            old_data=s3_client.read_as_dataframe(config['s3_paths']['bucket_name'],file_name)
            old_data['date']=pd.to_datetime(old_data['date'])
            updated_df = pd.concat([old_data,cnt_df])
            updated_df['country_code']=country
            updated_df = updated_df.drop_duplicates(['country_code','date'],keep='last')
            s3_client.write_advanced_as_df(updated_df,config['s3_paths']['bucket_name'],file_name)

        # Automated email
        sender = config['sender_name']
        recipients = config["receiver_email"]
        subject = 'World Macro Economy Trigger Status'
        message_text = f"""
        <html>
        <body>
        <p><strong>World Macro Economy Trigger has completed.</strong></p>

        <p><strong>Summary:</strong><br>
        &nbsp;&nbsp;&nbsp;- Time taken to finish trigger (in seconds): {round(time.time() - start, 2)}<br>
        &nbsp;&nbsp;&nbsp;- Any Failures: {"Yes" if service.failed_dict else "No"}<br>
        </p>

        <p><strong>Error Log:</strong><br>
        <pre>{service.error_log}</pre>
        </p>

        <p><strong>Failed Dictionary:</strong><br>
        <pre>{service.failed_dict}</pre>
        </p>
        </body>
        </html>
        """

        SendMessage(sender, recipients, subject, None, message_text, None)
        return "Succesfully executed main method"
    except Exception as e:
        print(e)
        sender = config['sender_name']
        recipients = config["receiver_email"]
        subject = 'World Macro Economy Trigger Status - Failed'
        message_text = f"""
        <html>
        <body>
        <p><strong>World Macro Economy Trigger has failed.</strong></p>

        <p><strong>Summary:</strong><br>
        &nbsp;&nbsp;&nbsp;- Time taken to finish trigger (in seconds): {round(time.time() - start, 2)}<br>
        &nbsp;&nbsp;&nbsp;- Any Failures: {"Yes" if service.failed_dict else "No"}<br>
        </p>

        <p><strong>Error Log:</strong><br>
        <pre>{service.error_log}</pre>
        </p>

        <p><strong>Failed Dictionary:</strong><br>
        <pre>{service.failed_dict}</pre>
        </p>
        </body>
        </html>
        """

        SendMessage(sender, recipients, subject, None, message_text, None)
        return all_dfs





resp=main_method()






