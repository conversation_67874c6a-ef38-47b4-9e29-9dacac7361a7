from helpers import *

def macro_dailyrun_predictions(run_date):
    end_dt = pd.to_datetime(run_date)-BDay()
    start_dt = pd.to_datetime(run_date)-BDay(1)
    datelist = create_date_range(start_dt,end_dt)
    df_job_count = pd.DataFrame()   
    job_count_dt = pd.Timestamp('today').normalize().date()
    df_job_count['date'] = [job_count_dt]
    st_time = time.time()
    df_job_count['start_time'] = [st_time]
    count= 0
    for date in datelist:
        count+=65
        preds,metrics = trigger_and_save([date])
    end_time = time.time()
    df_job_count['end_time'] = [end_time]
    df_job_count['job_count'] = [count]
    upload_no_of_jobs(df_job_count)
    

if __name__ == "__main__":
    if len(sys.argv)== 1:
        run_date= date.today().strftime("%Y-%m-%d")
    else:
        run_date=pd.to_datetime(sys.argv[1]).strftime("%Y-%m-%d")
    
    # run_date = (datetime.today()-BDay(0)).strftime("%Y-%m-%d")
    # run_date = '2025-04-07'
    try:
        macro_dailyrun_predictions(run_date)
        print(f"Monthly Macro Model Predictions and metrics file generated for {run_date}")
        send_email(f'Monthly Macro Model : Daily run pipeline COMPLETED successfully for {run_date}')
    except Exception as e:
        print(f'Monthly Macro Model : Run FAILED for run date {run_date} due to the Error : {e}')
        print(traceback.format_exc())
        send_email( f'Monthly Macro Model : Daily run pipeline FAILED for run date {run_date}', traceback.format_exc())
        print(e)
        raise

