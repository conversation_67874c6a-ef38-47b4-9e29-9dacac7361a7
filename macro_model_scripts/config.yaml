Gmail_creds:
    gmail_creds_bucket : etf-predictions
    gmail_creds_path: preportfolio/gmail_credentials/credentials.json
    gmail_sender : <EMAIL>
    email_receiver_success : <EMAIL>, <EMAIL>, <EMAIL>,<EMAIL>
    email_receiver_failed : <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>

IBM_creds:
    wml_credentials : { "apikey": M1MZPBWTdIyon3T9RyFJlEo8VNCJ6blusJTYRvuB9k2W,"url": 'https://us-south.ml.cloud.ibm.com'}
    default_space_id : d25ddc7f-1e07-4831-910a-4a6770b0af1e
    
Master_active_firms:
    api_url : http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all

Inhouse_API:
    inhouse_api_url_by_isin : http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA
    inhouse_api_url_by_tic : http://52.2.217.192:8080/stockhistory/getstocksbytic?tic={identifier}&startDate={startdate}&endDate={enddate}&countrycode=USA
    inhouse_api_url_for_macroeconomic_data : http://54.85.240.91:8080/worldmacroeconomy/get_data?country={country}&startDate={startdate}&endDate={enddate}
    
SNP_data:
    url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json
    headers : {'Authorization': 'Basic ************************************************','Content-type': 'application/json'}

    
Metrics_data:
    schedular_dict : {'monthly': 22,'weekly': 5,'daily': 1}
    req_columns : ["date", "isin", "actual_monthly_returns", "monthly_predictions"]
    all_metrics : ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "confidence_score", "avg_confidence_score", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day"]
    metrics_period : 22
    es_index : eq_macro_model
    schedular : Monthly
    metrics_es_index : eq_macro_model_metrics
    
Misc:
    country_mappings : {'USA': 'US',
                          'CHN': 'CN',
                            'JPN': 'JP',
                         'EUR': 'IN',
                         'IND': 'IN',
                         'BRA': 'BR',
                         'GBR': 'GB',
                         'CAN': 'CA',
                         'DEU': 'DE',
                         'SGP': 'SG',
                         'HKG': 'HK',
                         'FRA': 'FR',
                         'CHE': 'CH',
                         'ARE': 'AE',
                         'KOR': 'KR',
                         'MEX': 'MX',
                         'AUS': 'AU',
                         'IRL': 'IE',
                         'ISR': 'IL',
                         'LUX': 'LU',
                         'ZAF': 'ZA',
                         'BEL': 'BE',
                         'CHL': 'CL',
                         'COL': 'CO',
                         'DNK': 'DK',
                         'EGY': 'EG',
                         'FIN': 'FI',
                         'GRC': 'GR',
                         'IDN': 'ID',
                         'ITA': 'IT',
                         'MYS': 'MY',
                         'MLT': 'MT',
                         'NLD': 'NL',
                         'NZL': 'NZ',
                         'NOR': 'NO',
                         'PER': 'PE',
                         'PHL': 'PH',
                         'POL': 'PL',
                         'PRT': 'PT',
                         'QAT': 'QA',
                         'ESP': 'ES',
                         'SWE': 'SE',
                         'THA': 'TH',
                         'TUR': 'TR',
                         'AUT': 'AT',
                         'BMU': 'BM',
                         'CZE': 'CZ',
                         'HUN': 'HU',
                         'KWT': 'KW',
                         'MAC': 'MO',
                         'PAN': 'PA',
                         'SAU': 'SA',
                         'ARG': 'AR',
                         'RUS': 'RU',
                         'MCO': 'MC',
                         'GGY': 'GG',
                         'CYM': 'KY'}
    list_of_geos : ['KWT_equity',
                     'IND_equity',
                     'ISR_equity',
                     'POL_equity',
                     'PHL_equity',
                     'KOR_equity',
                     'TUR_equity',
                     'USA_bond',
                     'DEU_bond',
                     'HKG_equity',
                     'BRA_equity',
                     'ESP_equity',
                     'NOR_equity',
                     'CZE_equity',
                     'SGP_equity',
                     'EUR_bond',
                     'FIN_equity',
                     'CHE_equity',
                     'IDN_equity',
                     'HUN_equity',
                     'NLD_equity',
                     'EUR_equity',
                     'CHN_equity',
                     'NZL_equity',
                     'ITA_equity',
                     'PRT_equity',
                     'MCO_equity',
                     'PER_equity',
                     'CAN_bond',
                     'MLT_equity',
                     'CHN_bond',
                     'JPN_bond',
                     'DNK_equity',
                     'SWE_equity',
                     'CHL_equity',
                     'JPN_equity',
                     'QAT_equity',
                     'ARE_equity',
                     'COL_equity',
                     'PAN_equity',
                     'DEU_equity',
                     'BEL_equity',
                     'RUS_equity',
                     'CYM_equity',
                     'GBR_equity',
                     'GBR_bond',
                     'GRC_equity',
                     'ARG_equity',
                     'IRL_equity',
                     'FRA_equity',
                     'EGY_equity',
                     'HKG_bond',
                     'MYS_equity',
                     'MEX_equity',
                     'SAU_equity',
                     'THA_equity',
                     'AUT_equity',
                     'AUS_equity',
                     'BRA_bond',
                     'BMU_equity',
                     'LUX_equity',
                     'CAN_equity',
                     'SGP_bond',
                     'USA_equity',
                     'ZAF_equity',
                     'IND_bond']
    snp_headers : {"Authorization" : 'Basic ************************************************',"Content-type": 'application/json'}

    
S3_paths :
    #upload_paths
    macro_daily_metrics_bucket : micro-ops-output
    macro_daily_metrics_path : macro-historical-data/final-metrics/daily/{Country}/{Type}/daily/{date}.csv
    macro_daily_predictions_bucket : micro-ops-output
    macro_daily_predictions_path : macro-historical-data/final-predictions/daily/{Country}/{Type}/daily/{date}.csv
    macro_daily_predictions_s3versioning_bucket : eq-model-output
    macro_daily_predictions_s3versioning_path : macro_model/monthly/{date}/predictions/{country_code}_{asset_type}.csv
    macro_daily_metrics_s3versioning_bucket : eq-model-output
    macro_daily_metrics_s3versioning_path : macro_model/monthly/{date}/metrics/{name}.csv
    macro_job_count_bucket : micro-ops-output
    macro_job_count_path : macro_model_split/deployment/job_count.csv
    
    
    
    #read_from_paths
    macro_datapoints_bucket : micro-ops-output
    macro_datapoints_path : macro_model_split/deployment/macro_ deployment.csv
    macro_deployments_bucket : micro-ops-output
    macro_deployments_path : macro_model_split/model_training_details/deployment_details.csv'
    macro_keywords_bucket : micro-ops-output
    macro_keywords_path : macro_model_split/deployment/macro_keywords.csv