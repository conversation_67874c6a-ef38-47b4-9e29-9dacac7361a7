lstm_features: ['open_change_Monthly','high_change_Monthly','low_change_Monthly','close_change_Monthly']
gserp_features: ['isin', 'date', 'gserp_pos_senti_comp', 'gserp_neg_senti_comp']
ffill_cols_excluded: ['eps_current_qr', 'eps_next_qr', 'eps_current_yr', 'eps_next_yr']
unnecessary_cols: ['index', 'index_x', 'index_y']
mis_st_columns: ['isin', 'NaN Columns', 'Reason']

maf_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=

open_search:
  eq_data_url: search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com
  eq_os_key: ********************
  eq_os_secret: xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV
  scaleserp_raw_data_index: scaleserp_raw_data
  scaleserp_processed_upload_index: info_scaleserp_nlu_data
  unstructured_data_index: unstructured_data
  info_model_predictions_index: eq_information_model
  info_model_metrics_index: eq_information_model_metrics


mailing:
  email: <EMAIL>
  app_password: cdjkisiszarvnsyq
  sender: Shivam Jain <<EMAIL>>
  gmail_cred_bucket: etf-predictions
  gmail_cred_file: preportfolio/gmail_credentials/credentials.json
  scopes: https://www.googleapis.com/auth/gmail.send
  recipient_list: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
  recipient_list_failed: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
  recipient_list_trigger: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
  recipient_list_pre_prod: ['<EMAIL>']

versioning:
  bucket_name: eq-model-output
  model_key: eq_information_model

s3:
  bucket_name: micro-ops-output

ibm:
  nlu_url: https://api.us-south.natural-language-understanding.watson.cloud.ibm.com/instances/1b87995c-4b2a-4d19-ae42-7b6df203639f
  nlu_api_key: vfhpPKMWLM9FrwNuF1zIhs0LKPbGbNcuvc1IPqG0fmtW
  nlu_version: 2022-08-10
  
  #api <NAME_EMAIL>
  cloud_api_key: 2eTXFIMpFGpB_-cZf_KIoO3UWGNQHC_wvwZwwwPXi1zu

input_data:
  bucket_name: micro-ops-output
  structured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/structured/
  unstructured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/unstructured/
  gserp_input_data_folder_path: new_info_model/v2/input_data/daily_run/raw/gserp/
  missing_structured_input_data_folder_path: new_info_model/v1/input_data/daily_run/missing_data/structured/
  missing_unstructured_input_data_folder_path: new_info_model/v1/input_data/daily_run/missing_data/unstructured/
  raw_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/combined/
  processed_input_data_folder_path: new_info_model/v1/input_data/daily_run/processed/
  # raw_input_data_folder_path: new_info_model/v1/input_data/buf/daily_run/raw/combined/
  # processed_input_data_folder_path_buf: new_info_model/v1/input_data/buf/daily_run/processed/

  log_input_data_folder_path: new_info_model/v1/input_data/daily_run/logs/
  input_features_file_path: new_info_model/v1/input_features.json
  ffill_period: 90

indt1:
  bucket_name: micro-ops-output
  scaleserp_processed_folder_path: gserp/indt1_daily_processed/
  scaleserp_extracted_folder_path: test/rishij/indt1_gserp/
  # processed_input_data_folder_path: new_info_model/indt1/daily_run/input_data/
  processed_input_data_folder_path: new_info_model/v1/input_data/daily_run/processed/
  

  structured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/structured/
  unstructured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/unstructured/
  lstm_predictions_folder_path: new_info_model/indt1/daily_run/monthly_model_lstm_predictions/
  autoai_predictions_folder_path: new_info_model/indt1/daily_run/monthly_model_autoai_predictions/
  combined_predictions_folder_path: new_info_model/indt1/daily_run/monthly_model_final_predictions/
  metrics_folder_path: new_info_model/indt1/daily_run/monthly_model_metrics/
  autoai_input_features_list_path: new_info_model/indt1/daily_run/indt1_autoai_input_features.pkl
  lstm_deployment_details_file_path: test/rishij/info_indt1_2/monthly_models/isinwise/lstm_deployment_details_2/2024.csv
  autoai_deployment_details_file_path: test/rishij/info_indt1_2/monthly_models/isinwise/autoai_deployment_details_2/2024.csv
  ibm_url: https://us-south.ml.cloud.ibm.com

aipex:
  bucket_name: micro-ops-output
  scaleserp_processed_folder_path: gserp/daily_processed/
  scaleserp_extracted_folder_path: new_info_model/v2/input_data/daily_run/raw/gserp/
  # processed_input_data_folder_path: new_info_model/new_info_model_evidence_mention/tier1/daily_run/input_data/
  processed_input_data_folder_path: new_info_model/v1/input_data/daily_run/processed/
  

  structured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/structured/
  unstructured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/unstructured/
  lstm_predictions_folder_path: new_info_model/monthly_model_lstm_predictions/
  autoai_predictions_folder_path: new_info_model/new_info_model_evidence_mention/tier1/daily_run/monthly_model_autoai_predictions/
  combined_predictions_folder_path: new_info_model/new_info_model_evidence_mention/tier1/daily_run/monthly_model_final_predictions/
  metrics_folder_path: new_info_model/new_info_model_evidence_mention/tier1/daily_run/monthly_model_metrics/
  autoai_input_features_list_path: new_info_model/new_info_model_evidence_mention/tier1/autoai_input_features.pkl
  lstm_deployment_details_file_path: new_info_model/new_info_model_evidence_mention/tier1/lstm_deployment_details/2024.csv
  autoai_deployment_details_file_path: new_info_model/new_info_model_evidence_mention/tier1/autoai_deployment_details/2024.csv
  ibm_url: https://us-south.ml.cloud.ibm.com

aieq:
  bucket_name: micro-ops-output
  # processed_input_data_folder_path: new_info_model/aieq_sectorwise/v1/daily_run/input_data/
  processed_input_data_folder_path: new_info_model/v1/input_data/daily_run/processed/

  structured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/structured/
  unstructured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/unstructured/
  lstm_predictions_folder_path: new_info_model/aieq_sectorwise/v1/daily_run/monthly_model_lstm_predictions/
  autoai_predictions_folder_path: new_info_model/aieq_sectorwise/v1/daily_run/monthly_model_autoai_predictions/
  combined_predictions_folder_path: new_info_model/aieq_sectorwise/v1/daily_run/monthly_model_final_predictions/
  metrics_folder_path: new_info_model/new_info_model_evidence_mention/tier1/daily_run/monthly_model_metrics/
  autoai_input_features_list_path: new_info_model/new_info_model_evidence_mention/tier1/autoai_input_features.pkl
  lstm_deployment_details_file_path: new_info_model/aieq_sectorwise/v1/lstm_deployment_details/2024_production.csv
  autoai_deployment_details_file_path: new_info_model/aieq_sectorwise/v1/autoai_deployment_details/2024.csv
  ibm_url: https://us-south.ml.cloud.ibm.com

aigo:
  bucket_name: micro-ops-output
  # processed_input_data_folder_path: new_info_model/aigo_sectorwise/daily_run/input_data/
  processed_input_data_folder_path: new_info_model/v1/input_data/daily_run/processed/

  structured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/structured/
  unstructured_input_data_folder_path: new_info_model/v1/input_data/daily_run/raw/unstructured/
  lstm_predictions_folder_path: new_info_model/aigo_sectorwise/daily_run/monthly_model_lstm_predictions/
  autoai_predictions_folder_path: new_info_model/aigo_sectorwise/daily_run/monthly_model_autoai_predictions/
  combined_predictions_folder_path: new_info_model/aigo_sectorwise/daily_run/monthly_model_final_predictions/
  metrics_folder_path: new_info_model/aigo_sectorwise/daily_run/monthly_model_metrics/
  autoai_input_features_list_path: new_info_model/new_info_model_evidence_mention/tier1/autoai_input_features.pkl
  lstm_deployment_details_file_path: new_info_model/aigo_sectorwise/lstm_deployment_details/2024_production.csv
  autoai_deployment_details_file_path: new_info_model/aigo_sectorwise/autoai_deployment_details/2024.csv
  ibm_url: https://us-south.ml.cloud.ibm.com