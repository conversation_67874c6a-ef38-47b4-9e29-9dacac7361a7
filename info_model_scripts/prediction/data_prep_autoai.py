from utils import *
from prepare_raw_data import *
from email_notification import *

script_dir, config_path, env_path, config = load_config_and_env()

ibm_url=config['ibm']['nlu_url']
# nlu_apikey=os.getenv('NLU_APIKEY')
# cloud_api_key = os.getenv('CLOUD_API_KEY')
nlu_apikey = config['ibm']['nlu_api_key']
nlu_version = config['ibm']['nlu_version']
cloud_api_key = config['ibm']['cloud_api_key']

maf_url = config['maf_url']
bucket_name = config['s3']['bucket_name']
es_data_index = config['open_search']['scaleserp_processed_upload_index']

authenticator = IAMAuthenticator(nlu_apikey)
natural_language_understanding = NaturalLanguageUnderstandingV1(
    version=nlu_version,
    authenticator=authenticator
)

natural_language_understanding.set_service_url(ibm_url)
es_client = es_config()

bucket_name = config['s3']['bucket_name']
es_raw_fetch_index = config['open_search']['scaleserp_raw_data_index']
es_processed_upload_index = config['open_search']['scaleserp_processed_upload_index']
es_date_isin_fetch_index = config['open_search']['unstructured_data_index']

# str_data_cols = ['isin', 'date', 'age_year', 'eps_current_qr', 'eps_current_yr', 'eps_next_yr', 'eps_next_qr', 'high_price', 'low_price', 'open_price', 'close_price', 'volume', 'age_days']
# unstr_data_cols = ['isin','date', 'source_id_sales','overall_pos_sentiment_sales', 'overall_neg_sentiment_sales','pos_sentiment_sales', 'neg_sentiment_sales', 'average_mentions_sales','average_evidence_sales', 'source_id_innovation','overall_pos_sentiment_innovation', 'overall_neg_sentiment_innovation','pos_sentiment_innovation', 'neg_sentiment_innovation','average_mentions_innovation', 'average_evidence_innovation','source_id_general', 'overall_pos_sentiment_general','overall_neg_sentiment_general', 'pos_sentiment_general','neg_sentiment_general', 'average_mentions_general','average_evidence_general', 'source_id_growth','overall_pos_sentiment_growth', 'overall_neg_sentiment_growth','pos_sentiment_growth', 'neg_sentiment_growth','average_mentions_growth', 'average_evidence_growth','source_id_merger', 'overall_pos_sentiment_merger','overall_neg_sentiment_merger', 'pos_sentiment_merger','neg_sentiment_merger', 'average_mentions_merger','average_evidence_merger', 'source_id_litigation','overall_pos_sentiment_litigation', 'overall_neg_sentiment_litigation','pos_sentiment_litigation', 'neg_sentiment_litigation','average_mentions_litigation', 'average_evidence_litigation','pos_senti_ind', 'neg_senti_ind']
# gserp_data_cols = ['isin','date','gserp_pos_senti_comp','gserp_neg_senti_comp']

def push_ss_processed(tempdf):
    try:
        ss_df = tempdf
        ss_df.reset_index(drop=True, inplace=True)
        # Convert the data to yearwise data
        ss_df['date'] = pd.to_datetime(ss_df['date'])
        ss_df['year'] = ss_df['date'].apply(lambda x: x.year)
        ss_df['date'] = ss_df['date'].apply(lambda x: x.strftime("%Y-%m-%d"))
        for year in ss_df['year'].unique().tolist():
            upload_df = ss_df[ss_df['year'] == year]
            upload_df.drop(columns=['year'], inplace=True)
            output = es_config().save_records_v2(records = upload_df, index= f"{es_processed_upload_index}", primary_column='isin', unique_columns=['isin', 'date'], date_column = 'date', op_type = 'update')
            print('Output of es config is', output)
    except Exception as e_sspush:
        traceback.print_exc()
        print(e_sspush)
        pass
    return ss_df

# def extract_entity_sentiment(article_link, chatgpt_names):
def extract_entity_sentiment(row, chatgpt_names):
    """
    This function takes the input as url from a new article and outputs the
        - concepts
        - keywords
        - entities
    """
    response_dict = {'position' : row['position'],
                     'title': row['title'],
                     'url': row['url'],
                     'date': row['publishedDate'],
                     'isin': row['isin'],
                     'aid': row['aid']
                    }
    
    article_link = response_dict['url']
    
    time_count = 0
    while time_count < 5:
        try:
            response = natural_language_understanding.analyze(
                    url=article_link,
                    features=Features(
                        concepts=ConceptsOptions(limit=10),
                        entities=EntitiesOptions(emotion=True, sentiment=True, limit=10),
                        keywords=KeywordsOptions(limit=10),
                        sentiment=SentimentOptions(targets=chatgpt_names)
                    ),
                    language='en',
                    return_analyzed_text = True,
                ).get_result()
            # Get the  
            response_dict['sentiment_document'] = response['sentiment']['document']['score']
            target_sentiment = 0
            for elem in response['sentiment']['targets']:
                target_sentiment += elem['score']
            response_dict['sentiment_targets'] = target_sentiment / len(response['sentiment']['targets'])
            response_dict['keywords'] = str(response['keywords'])
            response_dict['entities'] = str(response['entities'])
            response_dict['concepts'] = str(response['concepts'])
            response_dict['analyzed_text'] = str(response['analyzed_text'])
            
            return response_dict
        except Exception as e:
            time.sleep(2)
            time_count += 1

    # Try fetching the data via newspaper API\
    try:
        article = Article(article_link)
        article.download()
        article_text = fulltext(article.html)
    except Exception as e:
        return response_dict

    while time_count < 5:
            try:
                response = natural_language_understanding.analyze(
                        text=article_text,
                        features=Features(
                            concepts=ConceptsOptions(limit=10),
                            entities=EntitiesOptions(emotion=True, sentiment=True, limit=10),
                            keywords=KeywordsOptions(limit=10),
                            sentiment=SentimentOptions(targets=chatgpt_names)
                        ),
                        language='en',
                        return_analyzed_text = True,
                    ).get_result()
                
                response_dict['sentiment_document'] = response['sentiment']['document']['score']
                target_sentiment = 0
                for elem in response['sentiment']['targets']:
                    target_sentiment += elem['score']
                response_dict['sentiment_targets'] = target_sentiment / len(response['sentiment']['targets'])
                response_dict['keywords'] = str(response['keywords'])
                response_dict['entities'] = str(response['entities'])
                response_dict['concepts'] = str(response['concepts'])
                response_dict['analyzed_text'] = str(response['analyzed_text'])

                return response_dict
            except Exception as e:
                time.sleep(5)
                time_count += 1
    return response_dict


def gserp_nlu_process(tag='indt1'):
    s3_conn = s3_config()
    tier1 = get_isin_df(tag)
    chatgpt_names_df = pd.read_csv(os.path.join(script_dir, f'{tag}_chatGPT_names.csv'))
    chatgpt_names_df['chatgpt_names'] = chatgpt_names_df['chatgpt_names'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else None)

    # Add the tier1 list to chatgpt names dataframe
    chatgpt_names_df = tier1[['isin', 'processedconm']].merge(chatgpt_names_df, on='isin', how='left')
    for idx, row in chatgpt_names_df.iterrows():
        if row['chatgpt_names'] == None:
            chatgpt_names_df.loc[idx, 'chatgpt_names'] = [row['processedconm']]
            print('for isin', row['isin'])

    chatgpt_names_df = chatgpt_names_df[['isin', 'chatgpt_names']]
    
    processed_data_folder = config[f'{tag}']['scaleserp_processed_folder_path']
    file_list = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, processed_data_folder, end=(datetime.now()+timedelta(days=1)) ))
    file_list['date'] = file_list['Key'].apply(lambda x: x.split('/')[-1].split('.')[0])
    completed_date = file_list.sort_values(by='date').iloc[len(file_list) - 1]['date']

    start_date = (pd.to_datetime(completed_date) + timedelta(days=1)).strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=-1)).date().strftime("%Y-%m-%d")

    start_date = min(start_date, end_date)
    print(start_date, end_date)



    for date in tqdm(pd.date_range(start=start_date, end=end_date)):
        date = date.strftime("%Y-%m-%d")
        data = es_connection(type='pre').get_es_data(date, date, tier1['isin'].tolist(), es_date_isin_fetch_index)

        # Select only the Scaleserp articles
        articles_df = pd.DataFrame()
        for _, row in data.iterrows():
            articles = row['articles']
            articles_to_be_processed = []

            # Stores the position or rank of the article
            position = 0
            for article in articles:
                if article['source'] == 'scaleserp':
                    article['position'] = position
                    article['isin'] = row['isin']
                    articles_to_be_processed.append({'isin': row['isin'], 'article': article})
                    position += 1
            articles_df = pd.concat([articles_df, pd.DataFrame(articles_to_be_processed)], axis=0)

        if articles_df.empty:
            continue
        articles_df['date'] = articles_df.apply(lambda x: x['article']['publishedDate'], axis=1)
        articles_df = articles_df[articles_df['date'] >= (pd.to_datetime(date) - timedelta(days=4)).strftime("%Y-%m-%d")]
        articles_df = articles_df.reset_index(drop=True)
        articles_df = articles_df.merge(chatgpt_names_df, on='isin', how='inner')

        # Process the whole corpse through NLU
        num_threads = 64

        # Create a ThreadPoolExecutor
        try:
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                # Apply the function to each row of the DataFrame in parallel
                results =  list(tqdm(
                    executor.map(extract_entity_sentiment, articles_df['article'].tolist(), articles_df['chatgpt_names'].tolist()),
                    total=len(articles_df),
                    desc=f"NLU extraction for date {date}",
                ))
        except Exception as e:
            raise e

        # Process the results to store them in Elastic Search
        results_list = []
        for result in results:
            results_list.append({
                'isin' : result['isin'],
                'articles': result,
                'date': result['date']
            })
        results_df = pd.DataFrame(results_list)
        final_list = results_df.groupby('isin')['articles'].apply(list).reset_index()

        # Prepare the dataframe for final upload
        upload_list = []
        for idx, row in final_list.iterrows():
            isin_df = pd.DataFrame(row['articles'])
            isin_df.drop(columns=['date', 'isin'], inplace=True)
            isin_df = isin_df.sort_values(by='position').reset_index(drop=True)
            isin_df['position'] = isin_df.index
            isin_df.replace([np.nan], None, inplace=True)
            isin_dict = isin_df.to_dict(orient='index')
            upload_list.append([row['isin'], date, list(isin_dict.values())])
        upload_df = pd.DataFrame(upload_list, columns=['isin', 'date', 'articles'])

        # Upload the data to s3 and opensearch
        s3_conn.write_advanced_as_df(upload_df, bucket_name, f'{processed_data_folder}{date}.csv')
        push_ss_processed(upload_df)

def get_sentiment(articles_list, sentiment_type='positive'):
    if articles_list == None:
        return 0
    elif len(articles_list) == 0:
        return 0
    
    # Check if the sentiment_type is valid
    assert sentiment_type in ['positive', 'negative']
    
    # Calcualte the sentiment
    sentiment = 0
    total_articles = 0
    for article in articles_list:
        if 'sentiment_targets' in article.keys():
            if not isinstance(article['sentiment_targets'], float):
                continue
            
            if sentiment_type == 'positive':
                if article['sentiment_targets'] > 0:
                    sentiment += article['sentiment_targets']
                    total_articles += 1
            elif sentiment_type == 'negative':
                if article['sentiment_targets'] < 0:
                    sentiment += article['sentiment_targets']
                    total_articles += 1
    
    if total_articles > 0:
        return sentiment / total_articles
    return 0

def gserp_nlu_sentiment_extract(tag='indt1'):
    s3_conn = s3_config()
    tier1 = get_isin_df(tag)
    processed_data_folder = config[f'{tag}']['scaleserp_extracted_folder_path']


    file_list = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, processed_data_folder, end=(datetime.now()+timedelta(days=1))))
    file_list['date'] = file_list['Key'].apply(lambda x: x.split('/')[-1].split('.')[0])
    completed_date = file_list.sort_values(by='date').iloc[len(file_list) - 1]['date']

    start_date = (pd.to_datetime(completed_date) + timedelta(days=1)).strftime('%Y-%m-%d')
    end_date = (datetime.now() - timedelta(days=1)).date().strftime("%Y-%m-%d")
    start_date = min(start_date, end_date)
    print(start_date,end_date)



    for date in tqdm(pd.date_range(start=start_date, end=end_date)):
        try:
            date = date.strftime("%Y-%m-%d")
            data = es_connection(type='pre').get_es_data(date, date, tier1['isin'].tolist(), es_data_index)

            data['gserp_pos_senti_comp'] = data['articles'].apply(lambda x: get_sentiment(x, sentiment_type='positive'))
            data['gserp_neg_senti_comp'] = data['articles'].apply(lambda x: get_sentiment(x, sentiment_type='negative'))

            data = data[['isin', 'date', 'gserp_pos_senti_comp', 'gserp_neg_senti_comp']].reset_index(drop=True)
            data = data.sort_values(by='date')
            s3_conn.write_advanced_as_df(data, bucket_name, f'{processed_data_folder}{date}.csv')

        except Exception as e:
            print(e, date)
            print(traceback.print_exc())
            continue

# def combine_data(scaleserp: bool,processed_data_folder: str,unstr_data_df: pd.DataFrame, str_data_df: pd.DataFrame, start_date: str, end_date: str, gserp_data_df: pd.DataFrame=None):
#     s3_conn = s3_config()

#     ## Structured Data
#     str_data_df = str_data_df.sort_values(by='date').reset_index(drop=True)
#     try:
#         str_data_df = str_data_df[str_data_cols]
#     except:
#         for col in str_data_cols:
#             if col not in str_data_cols:
#                 str_data_df[col] = 0
#         str_data_df = str_data_df[str_data_cols]

#     ## Unstructured Data
#     unstr_data_df = unstr_data_df.sort_values(by='date').reset_index(drop=True)
#     for col in unstr_data_cols:
#         if col in unstr_data_df.columns:
#             continue
#         else:
#             unstr_data_df[col] = 0
#     unstr_data_df = unstr_data_df[unstr_data_cols]

#     combined_df = unstr_data_df.merge(str_data_df, on=['isin', 'date'], how='right')
    
#     ## ScaleSERP Data
#     if scaleserp:
#         gserp_data_df = gserp_data_df.sort_values(by='date').reset_index(drop=True)
#         for col in gserp_data_cols:
#             if col in gserp_data_df.columns:
#                 continue
#             else:
#                 gserp_data_df[col] = 0
#         gserp_data_df = gserp_data_df[gserp_data_cols]
#         combined_df = gserp_data_df.merge(combined_df,on=['isin', 'date'],how='right')

#     final_df = combined_df.drop_duplicates(subset = ["date", "isin"], keep = "first").reset_index(drop=True)

    
#     for date in pd.date_range(start_date, end_date):
#         final_df_date = final_df[final_df['date'] == date.strftime('%Y-%m-%d')]
#         if final_df_date.shape[0] != 0:
#             s3_conn.write_advanced_as_df(final_df_date, bucket_name, f'{processed_data_folder}{date.strftime("%Y-%m-%d")}.csv')
#             print('Data Processed for date:', date.strftime('%Y-%m-%d'))            
#     return

def fetch_data_from_s3(bucket_name: str, isin: str, isin_key: str, start_date: str, end_date: str):
    s3_conn = s3_config()
    file_df = s3_conn.read_as_dataframe(bucket_name, isin_key)
    if file_df is None:
        return
    file_df = file_df[file_df['date'] >= start_date]
    file_df = file_df[file_df['date'] <= end_date]
    return file_df

# def data_prep(tag,scaleserp: bool=False):
#     s3_conn = s3_config()

#     processed_data_folder = config[tag]['processed_input_data_folder_path']
#     str_data_folder = config[tag]['structured_input_data_folder_path']
#     usntr_data_folder = config[tag]['unstructured_input_data_folder_path']
#     gserp_data_folder = config[tag]['scaleserp_extracted_folder_path']

#     unstr_files_list = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, usntr_data_folder, end=(datetime.now()+timedelta(days=1))))
#     unstr_files_list['date'] = unstr_files_list['Key'].apply(lambda x: x.split('/')[-1].split('.')[0])

#     # For the start date we look at the last date for which the data was present in AWS
#     processed_files_list = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, processed_data_folder, end=(datetime.now()+timedelta(days=1))))
#     processed_files_list['date'] = processed_files_list['Key'].apply(lambda x: x.split('/')[-1].split('.')[0])

#     completed_date = processed_files_list.sort_values(by='date').iloc[len(processed_files_list) - 1]['date']
#     start_date = (pd.to_datetime(completed_date) + timedelta(days=1)).strftime('%Y-%m-%d')
#     # To get the today's date for the script
#     end_date = (date.today() + timedelta(days=-1)).strftime('%Y-%m-%d') #Gives the date at UTC 00:00 for today
    
#     print(start_date,end_date)
#     results = []
#     wr_results = []
#     gserp_results = []

#     maf_json = requests.get(f'{maf_url}{tag}').json()
#     maf_df = pd.DataFrame(maf_json["data"][f"masteractivefirms_{tag}"])
#     isin_list = list(maf_df['isin'])

#     for date_to_process in pd.date_range(start=start_date, end=end_date):
#         date_to_process = date_to_process.strftime("%Y-%m-%d")
#         if date_to_process in unstr_files_list['date'].tolist():
#             try:
#                 file_name = unstr_files_list[unstr_files_list['date'] == date_to_process]['Key'].values[0]
#                 data_df = s3_conn.read_as_dataframe(bucket_name, file_name)
#                 data_df = data_df[data_df['isin'].isin(isin_list)]
#             except Exception as e:
#                 print(e)

#         else:
#             prev_date_file_list = unstr_files_list['Key'].values[0].split('/')
#             prev_date = (pd.to_datetime(date_to_process) - timedelta(days=1)).strftime('%Y-%m-%d')
#             prev_date_file = '/'.join(prev_date_file_list[:-1]) + '/'+prev_date + '.csv'
#             data_df = s3_conn.read_as_dataframe(bucket_name, prev_date_file)
#             data_df['date'] = date_to_process

#         results.append(data_df)
#         try:
#             sp_data_df = s3_conn.read_as_dataframe(bucket_name, f'{str_data_folder}{date_to_process}.csv')
#             sp_data_df = sp_data_df[sp_data_df['isin'].isin(isin_list)]
#             wr_results.append(sp_data_df)
#         except Exception as e:
#             print(e)
#         if scaleserp:
#             try:
#                 gserp_data_df = s3_conn.read_as_dataframe(bucket_name, f'{gserp_data_folder}{date_to_process}.csv')
#                 gserp_data_df = gserp_data_df[gserp_data_df['isin'].isin(isin_list)]
#                 gserp_results.append(gserp_data_df)
#             except Exception as e:
#                 print(e)

#     senti_data_df = pd.concat(results)
#     df_isins = pd.concat(wr_results)
#     if scaleserp:
#         if len(gserp_results) == 0:
#             gserp_data_df = pd.DataFrame(columns=gserp_data_cols)
#         else:
#             gserp_data_df = pd.concat(gserp_results)

    

#     print(start_date, end_date)
#     if scaleserp:
#         combine_data(scaleserp,processed_data_folder,senti_data_df, df_isins, start_date, end_date,gserp_data_df)
#     else:
#         combine_data(scaleserp,processed_data_folder,senti_data_df, df_isins, start_date, end_date)
#     print('Data Processed for start date:', start_date, 'end date:', end_date)

def save_to_s3(final_pred_df, isin_input_feature_df, start_date, predictions_folder):
    """
    This function takes the final predictions dataframes that contains the following columns:
        - isin
        - deployment_id
        - deployment_space
        - dates: list of all the dates for which the predictions are done
        - predictions: list of the all predictions for the corresponding dates
    
    - isin_input_feature_df : contains the final features passed in the input datewise
    - start_date : corresponds to the start date for which we want to store the predictions
    - predictions_folder : s3 path to the folder where we want to store the predictions datewise in a csv format
    """
    s3_conn = s3_config()
    # Convert final_pred_df to a final_df that contains the: isin, date, predictions
    final_df = pd.DataFrame(columns=['isin', 'date', 'predictions'])

    # For each isin append the date along with predictions to the final_df
    for idx in range(len(final_pred_df)):
        buf_df = pd.DataFrame({
            'date': final_pred_df.iloc[idx]['dates'],
            'isin': final_pred_df.iloc[idx]['isin'],
            'predictions': final_pred_df.iloc[idx]['predictions']
        })
        final_df = pd.concat([final_df, buf_df], axis=0)


    
    if isin_input_feature_df['date'].dtype != 'object':
        isin_input_feature_df['date'] = isin_input_feature_df['date'].apply(lambda x: str(x.strftime('%Y-%m-%d')))

    if final_df['date'].dtype != 'object':
        final_df['date'] = final_df['date'].apply(lambda x: str(x.strftime('%Y-%m-%d')))
    
    final_df = isin_input_feature_df.merge(final_df, on=['isin', 'date'], how = 'left')

    # For each date generate a dataframe that contains the predictions of all isins, for that date
    date_list = final_df['date'].unique()
    
    for cur_date in tqdm(date_list, desc="Gen Dataframe for dates"):
        if pd.to_datetime(cur_date) < pd.to_datetime(start_date):
            print(cur_date, 'is less than start date', start_date)
        else:
            date_df = final_df[final_df['date'] == cur_date]

            # Store the data to S3
            if predictions_folder[-1] == '/':
                output_file_name = f'{predictions_folder}data_{cur_date}.csv'
            else:
                output_file_name = f'{predictions_folder}/data_{cur_date}.csv'

            date_df["ER_Copied"] = False
            s3_conn.write_advanced_as_df(date_df, bucket_name, output_file_name)
              
    print('Data Saved to S3 for total of', len(date_list), 'and dates', date_list)

def fetch_autoai_data_from_s3(input_data_folder,start_date, end_date):
    s3_conn = s3_config()
    file_df = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, input_data_folder, end=(datetime.now()+timedelta(days=1))))
    
    df_isins_list = []
    for file_name in file_df['Key'].tolist():
        file_date = file_name.split('/')[-1].split('.')[0]
        if file_date >= start_date and file_date <= end_date:
            df_isins_list.append(s3_conn.read_as_dataframe(bucket_name, file_name))
    
    df_isins = pd.concat(df_isins_list)
    return df_isins

def preprocess_data(isin, df_data,autoai_input_features):
    if df_data.shape[0] == 0:
        print('No data present for isin:', isin)
        return {}, pd.DataFrame()
    
    # Forward fill the data in relevant columns
    df_data = df_data.fillna(method="ffill")
    
    # Select the relevant columns, sort by date and drop duplicates
    df_data = df_data[['isin','date']+autoai_input_features].copy()
    df_data = df_data.sort_values(by = ["date"])
    
    df_data["date"] = pd.to_datetime(df_data["date"])

    # Sort the dates and forward fill for the required dates
    df_data = df_data.sort_values(by = ["date"]).ffill().reset_index(drop=True)
    df_data = df_data.drop_duplicates(subset = "date", keep = "first").reset_index(drop=True)
    df_data = df_data.fillna(0).reset_index(drop=True)

    # Get the dates array
    dates_isin = df_data['date'].tolist()

    # Get the test data array
    test_data = df_data[autoai_input_features]
    test_data = test_data.to_numpy()
    
    final_isin_dict = {'isin': isin,
                       'test_data': test_data,
                       'dates': dates_isin,
                       'identifier': [ f'{isin}_{buf_date.strftime("%Y-%m-%d")}' for buf_date in dates_isin],
                      }
    # The final processed dataframe df_data is also passed is used while saving the input features along with output features in the file
    return final_isin_dict, df_data

def create_final_df(deployment_df, isin_data_df):
    """
    deployment_df must contain the following columns: 
        - deployment_space
        - deployment_id
        - isin
    isin_df must contain the following columns:
        - isin
        - test_data
        - dates
    """
    buf_deployment_df = deployment_df[['deployment_space', 'deployment_id', 'isin', 'model_identifier']]
    final_df = isin_data_df.merge(buf_deployment_df, on='isin', how='left')
    
    return final_df

def save_job_count(job_count, ibm_dc_url):
    s3_conn = s3_config()
    job_count_df = s3_conn.read_as_dataframe('autoai-jobs', 'information_model/job_count.csv')
    job_count_df.loc[len(job_count_df)] = {
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'num_of_jobs': job_count,
        'ibm_dc_url': ibm_dc_url,
    }    
    s3_conn.write_advanced_as_df(job_count_df, 'autoai-jobs', 'information_model/job_count.csv')
    return True

###############################
def gen_autoai_predictions(tag, schedular, bdate):
    s3_conn = s3_config()
    predictions_folder = config[tag]['autoai_predictions_folder_path']
    input_data_folder = config[tag]['processed_input_data_folder_path']

    autoai_input_features_list_path = config[tag]['autoai_input_features_list_path']
    file_obj, file_path = s3_conn.read_as_stream(bucket_name, autoai_input_features_list_path)
    autoai_input_features = pickle.loads(file_obj)

    deployment_details = s3_conn.read_as_dataframe(bucket_name,config[tag]['autoai_deployment_details_file_path'])
    deployment_details = deployment_details[deployment_details['expired_at'].isna()]
    deployment_details = deployment_details.rename(columns={'space_id': 'deployment_space'})

    all_isin_list = get_isin_df_excl(tag)['isin'].tolist()

    # Get the list of deployed isins
    deployed_isin_list = list(set(deployment_details['isin'].tolist()))
    print("No of deployed isin is "+str(len(deployed_isin_list)))

    # For the start date we look at the last date for which the data was present in AWS
    file_list = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, predictions_folder, end=(datetime.now()+timedelta(days=1))))
    file_list['date'] = file_list['Key'].apply(lambda x: x.split('/')[-1].split('.')[0].split('_')[-1])
    completed_date = file_list.sort_values(by='date').iloc[len(file_list) - 1]['date']
    start_date = (pd.to_datetime(completed_date) + timedelta(days=-20)).strftime('%Y-%m-%d')
    # To get the today's date for the script
    end_date = (date.today() + timedelta(days=-1)).strftime('%Y-%m-%d') #Gives the date at UTC 00:00 for today


    start_date = bdate
    end_date = bdate
    
    # start_date = '2025-04-11'
    # end_date = '2025-04-11'

    print('Start date:', start_date, "End date:", end_date)

    # Get the input data for all the isins
    df_isins = fetch_autoai_data_from_s3(input_data_folder, start_date, end_date)
    df_isins = df_isins.sort_values(by = ["date"]).reset_index(drop=True)

    # Align the variables with the existing code
    print('Total isins', len(all_isin_list))

    # Make the isin data into individual dataframes so that we can use multithreading and speed up data preprocessing
    isin_data_list = []
    for isin in tqdm(all_isin_list):
        isin_df = df_isins[df_isins["isin"] == isin].reset_index(drop=True)
        isin_data_list.append(isin_df)

        # Make a log in case no data is present for that isin
        if isin_df.shape[0] == 0:
            print('No data present for ISIN', isin)

    # Get the input data for all the isins
    num_threads = 8
    # Create a ThreadPoolExecutor
    try:
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # Apply the function to each row of the DataFrame in parallel
            results =  list(tqdm(
                executor.map(preprocess_data, all_isin_list, isin_data_list,repeat(autoai_input_features)),
                total=len(all_isin_list),
                desc="Tasks",
            ))
    except Exception as e:
        raise e

    deployment_df = deployment_details

    # isin_data_df is to be used for prediction helper input, so we have a row for each isin
    isin_data_df = pd.DataFrame([results[i][0] for i in range(len(results))])
    final_df = create_final_df(deployment_df, isin_data_df)

    # Drop the columns for which the daployment_id doesn't exist
    final_df = final_df[final_df['deployment_id'].notna()]
    final_input_df = final_df[["deployment_space", "deployment_id", "test_data", "isin","identifier", "model_identifier"]]
    final_input_df = final_input_df.dropna().reset_index(drop=True)

    url = config[tag]['ibm_url']
    save_job_count(len(results), url)
    autoai_deployment_to_Submit = final_input_df
    autoai_deployment_to_Submit['ibm_dc_url'] = url

    # Add the input feature data to the final dataframe, pass the dataframe containing processed features
    autoai_input_df = pd.concat([results[i][1] for i in range(len(results))], axis=0)
    autoai_input_df['date'] = autoai_input_df['date'].apply(lambda x:x.strftime("%Y-%m-%d"))

    if len(autoai_deployment_to_Submit) > 0:
        group_by = autoai_deployment_to_Submit.groupby('deployment_id', group_keys=True)
        deployment_to_Submit = group_by[['test_data']].apply(lambda x: x['test_data'].tolist()).reset_index()
        deployment_to_Submit.columns = ['deployment_id', 'test_data']
        deployment_to_Submit['identifier'] = group_by[['identifier']].apply(lambda x: x['identifier'].tolist()).reset_index(drop=True)

        deployment_to_Submit['identifier'] = deployment_to_Submit['identifier'].apply(lambda list_of_lists: [item for sublist in list_of_lists for item in sublist])
        deployment_to_Submit['test_data'] = deployment_to_Submit['test_data'].apply(lambda list_of_arrs: np.concatenate(list_of_arrs))
        deployment_to_Submit['deployment_space'] = group_by[['deployment_space']].agg(['first']).reset_index(drop=True)
        deployment_to_Submit['model_identifier'] = group_by[['model_identifier']].agg(['first']).reset_index(drop=True)
        deployment_to_Submit['ibm_dc_url'] = group_by[['ibm_dc_url']].agg(['first']).reset_index(drop=True)
        deployment_to_Submit['isin'] = group_by[['isin']].agg(['first']).reset_index(drop=True)
        
        # Now start the predictions
        pred_df_list = []
        for dc_url in deployment_to_Submit['ibm_dc_url'].unique().tolist():
            try:
                ibm_conn = IBMConnection(dc_url, cloud_api_key)
                # Connection to Prediction Helper
                _prediction_helper = PredictionHelper(ibm_conn)
                final_deployment_to_Submit = deployment_to_Submit[deployment_to_Submit['ibm_dc_url'] == dc_url]
                final_deployment_to_Submit = final_deployment_to_Submit[['deployment_space', 'deployment_id', 'isin', 'test_data']]
                pred_df_list.append(_prediction_helper.prediction_run(final_deployment_to_Submit))
            except Exception as e:
                print('unable to get predictions due to problem in deployment params, please check for deployment_id, space, and dc_url', final_deployment_to_Submit['isin'].tolist())
        
        pred_df = pd.concat(pred_df_list)
        pred_df = pred_df.merge(deployment_to_Submit[['deployment_id', 'identifier', 'model_identifier']], on='deployment_id', how='inner')

        buf_df_list = []
        for _, row in pred_df.iterrows():
            buf_df = pd.DataFrame({'identifier': row['identifier']})
            buf_df['isin'] = buf_df['identifier'].apply(lambda x: x.split('_')[0])
            buf_df['date'] = buf_df['identifier'].apply(lambda x: x.split('_')[1])
            buf_df['status'] = row['status']
            buf_df['model_identifier'] = row['model_identifier']

            # We use try catch for the predictions checking
            try:
                buf_df['autoai_predictions'] = row['predictions']
            except:
                buf_df['autoai_predictions'] = None

            buf_df_list.append(buf_df)

        final_pred_df = pd.concat(buf_df_list)
        failed_df = final_pred_df[final_pred_df['status'] != 'completed']

        # Convert the prediction dataframe into proper format to get the final output
        final_pred_df = final_pred_df[final_pred_df['status'] == 'completed'].reset_index(drop=True)
        
        input_date_df = autoai_input_df
        # final_pred_df = final_pred_df.merge(input_date_df, on=['isin', 'date'], how='inner')
        final_pred_df = input_date_df.merge(final_pred_df[['isin', 'date', 'autoai_predictions', 'model_identifier']], on=['isin', 'date'], how='left')
    else:
        input_date_df = autoai_input_df
        input_date_df['autoai_predictions'] = None
        input_date_df['model_identifier'] = None
        final_pred_df = input_date_df

    for cur_date in tqdm(final_pred_df['date'].unique().tolist()):
        date_df = final_pred_df[final_pred_df['date'] == cur_date]

        # Store the data to S3
        if predictions_folder[-1] == '/':
            output_file_name = f'{predictions_folder}data_{cur_date}.csv'
        else:
            output_file_name = f'{predictions_folder}/data_{cur_date}.csv'
        date_df["ER_Copied"] = False
        s3_conn.write_advanced_as_df(date_df, bucket_name, output_file_name)
        # date_df.to_csv(f'autoai_pred/{cur_date}.csv', index=False)
        
    print('Data Updated')

if __name__=="__main__":
    parser = argparse.ArgumentParser(
                    prog='ProgramName',
                    description='What the program does',
                    epilog='Text at the bottom of help')
    parser.add_argument('-t', '--tag')
    parser.add_argument('-s', '--schedular')
    parser.add_argument('-bdate', '--bdate')
    args = parser.parse_args()
    schedular = args.schedular
    tag = args.tag
    if args.bdate:
        bdate = args.bdate
    else:
        bdate = (datetime.now()-BDay(1)).date().strftime("%Y-%m-%d")

    gen_and_send_message_trigger(config, tag, schedular, bdate)

    # if tag in ['indt1']:
    #     gserp_nlu_process(tag)
    #     gserp_nlu_sentiment_extract(tag)
    
    # # data_prep(tag,scaleserp)

    prep_raw_data(config, bdate)
    prep_ffill_data(config, bdate, tag)
    
    gen_autoai_predictions(tag, schedular, bdate)