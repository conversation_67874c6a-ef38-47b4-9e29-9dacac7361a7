from utils import *
from email_notification import gen_message_prediction_run_status, gen_message_metrics_run_status, gen_message_signature

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")
env_path = os.path.join(script_dir, ".env")

load_dotenv(env_path)
with open(config_path,'r') as f:
    config = yaml.safe_load(f)
    
# OS_API_KEY = os.getenv('OS_API_KEY')
# OS_SECRET = os.getenv('OS_SECRET')
# APP_PASSWORD = os.getenv('APP_PASSWORD')

OS_API_KEY = config['open_search']['eq_os_key']
OS_SECRET = config['open_search']['eq_os_secret']
APP_PASSWORD = config['mailing']['app_password']

bucket_name = config['s3']['bucket_name']
eq_data_url = config['open_search']['eq_data_url']

mtx_2_calc1 = ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day"]
mtx_2_calc2 = ["confidence_score", "avg_confidence_score"]
req_columns = ["date", "isin", "actual_monthly_returns", "monthly_predictions"]
all_metrics = ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "confidence_score", "avg_confidence_score", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day","confidence_score_14_day"]
period = 22
current_date = date.today() - timedelta(days=0)

manager = multiprocessing.Manager()
failed_metrics_list = manager.list()

def is_date(string, fuzzy=False):
    """
    Return whether the string can be interpreted as a date.
    :param string: str, string to check for date
    :param fuzzy: bool, ignore unknown tokens in string if True
    """
    try: 
        parse(string, fuzzy=fuzzy)
        return True

    except ValueError:
        return False



def validate_file_name(s3_file_key):
    """
    Check if the s3 file key is in a valid format. Please make changes to this function if there is a change in a valid file_name
    default for: data_date.csv ie. data_2023-06-07.csv
    Input: s3_file_key
    Ouptut: Boolean
    """
    try:
        file_name = s3_file_key.split('/')[-1]
        file_name_split = file_name.split('.')
        if file_name_split[1].lower() != 'csv':
            print(file_name , ' is not a csv file')
            return False
        if file_name_split[0].split('_')[0] != 'data':
            return False
        if is_date(file_name_split[0].split('_')[1]):
            return True
        print(file_name, 'is not in proper format')
        return False
    except:
        return False
    
def preprocess_files_dataframe(file_df):
    """
    Input: AutoAI file dataframe or LSTM file dataframe retrieved from S3
    Output: Validated file names dataframe along with date extracted
    """
    file_df = file_df.reset_index(drop=True)
    file_df['validate_file'] = file_df['Key'].apply(validate_file_name)
    file_df = file_df[file_df['validate_file']].reset_index(drop=True)

    # Extract the dates as they are to be used as a primary key
    file_df['date'] = file_df['Key'].apply(lambda x: x.split('/')[-1].split('.')[0].split('_')[1])
    file_df['date'] = pd.to_datetime(file_df['date'])
    file_df['date'] = file_df['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    # Take only the relevant columns
    file_df = file_df[['Key', 'LastModified','date']]
    return file_df

def _score_calculation_helper(z_score, min_threshold, max_threshold):
    score = None
    if z_score > max_threshold:
        score = 10
    elif z_score < min_threshold:
        score = 1
    else:
        score = (z_score - min_threshold) / (max_threshold - min_threshold) * 9 + 1
    return score

# Function to calculate the i-score
def calculate_score(predictions): # series of predictions (in percentage)
    processed_predictions = predictions.apply(lambda x: max(min(x, 100), -100)) # preprocess_columns
    Zscore = (processed_predictions - processed_predictions.mean())/(processed_predictions.std())
    min_Z_score_threshold = Zscore.median() - 3 * Zscore.std()
    max_Z_score_threshold = Zscore.median() + 3 * Zscore.std()
    score = Zscore.apply(lambda x: _score_calculation_helper(x, min_Z_score_threshold, max_Z_score_threshold))

    return Zscore, score


def merge_predictions(tag, schedular, formatted_date_info):
    s3_conn = s3_config()
    prediction_files_directory_aai = config[tag]['autoai_predictions_folder_path']
    prediction_files_directory_lstm = config[tag]['lstm_predictions_folder_path']
    prediction_files_directory_final = config[tag]['combined_predictions_folder_path']
    
    # Load the file dataframes and preprocess them to get valid file keys and extract dates
    prediction_autoai_file_df = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, prediction_files_directory_aai, end=(datetime.now()+timedelta(days=1)) ))
    prediction_autoai_file_df = preprocess_files_dataframe(prediction_autoai_file_df)
    
    prediction_lstm_file_df = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, prediction_files_directory_lstm, end=(datetime.now()+timedelta(days=1)) ))
    prediction_lstm_file_df = preprocess_files_dataframe(prediction_lstm_file_df)
    
    # Get the timestamp when the last final predictions happened
    prediction_final_file_df = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, prediction_files_directory_final, end=(datetime.now()+timedelta(days=1)) ))
    
    # We process only the files that have changed since last final predictions
    last_final_pred_time = prediction_final_file_df.sort_values(by='LastModified').iloc[-1]['LastModified']
    combined_df = prediction_lstm_file_df.merge(prediction_autoai_file_df, on = "date", how='left').reset_index(drop=True)
    combined_df = combined_df.rename(columns = {"Key_x" : "lstm_files",
                                                "Key_y" : "autoai_files",
                                               "LastModified_x": "lstm_timestamp",
                                               "LastModified_y": "autoai_timestamp",})
    combined_df["timestamp"] = combined_df[["lstm_timestamp", "autoai_timestamp"]].max(axis=1)
    # combined_df = combined_df[combined_df["timestamp"] > last_final_pred_time].reset_index(drop=True)
    combined_df = combined_df[combined_df["date"] == formatted_date_info]
    print(combined_df)
    try:
        for date, lstm, autoai in zip(tqdm(combined_df["date"]), combined_df["lstm_files"], combined_df["autoai_files"]):
            df_lstm = s3_conn.read_as_dataframe(bucket_name, lstm)
            df_lstm = df_lstm.rename(columns = {"predictions" : "lstm_predictions", "model_identifier": "model_identifier_lstm"})

            if isinstance(autoai, float):
                # If Autoai files aren't present, we use the LSTM predictions only
                merged_df = df_lstm
                merged_df['predictions'] = merged_df['lstm_predictions']

            else:
                # Else, we use both lstm and autoai predictions
                df_aai = s3_conn.read_as_dataframe(bucket_name, autoai)
                df_aai = df_aai.rename(columns = {"predictions" : "autoai_predictions", "model_identifier": "model_identifier_autoai"})
                merged_df = df_lstm.merge(df_aai, on = ["isin"], how = "outer")

                merged_df = merged_df.drop_duplicates(subset = "isin", keep = "first").reset_index(drop=True)
                merged_df = merged_df.drop(columns=['date_y'])
                merged_df = merged_df.rename(columns={'date_x': 'date'})

                # Drop the columns in case there are no lstm predictions present
                # merged_df = merged_df.dropna(subset=['lstm_predictions']).reset_index(drop=True)

                # Fill 0 in autoai prediction in case it is NAN
                merged_df['predictions'] = merged_df["lstm_predictions"] + merged_df["autoai_predictions"].fillna(0)

            # Calculate the zscore and i-score for the predictions
            z_score, i_score = calculate_score(merged_df['predictions'])
            merged_df['z_score'] = z_score
            merged_df['i_score'] = i_score


            # Remove the columns that were forward filled
            ffill_df = s3_conn.read_as_dataframe(config['s3']['bucket_name'], f'{config[tag]["processed_input_data_folder_path"]}{date}.csv')
            merged_df = merged_df.merge(ffill_df[['isin', 'fwd_filled_cols']], on='isin')

            for idx, row in merged_df.iterrows():
                if pd.isna(row['fwd_filled_cols']):
                    continue

                cols_to_nan = eval(row['fwd_filled_cols'])
                if len(cols_to_nan) == 0:
                    continue
                
                for col in cols_to_nan:
                    if col in config['ffill_cols_excluded']:
                        continue
                    if col in merged_df.columns:
                        merged_df.at[idx, col] = np.nan
            
            merged_df = merged_df.drop(columns=['fwd_filled_cols'])

            # Generate the final file name and save the file
            file_name_output = prediction_files_directory_final + ( f'data_{date}.csv' if prediction_files_directory_final[-1] == '/' else f'/data_{date}.csv' )

            # Upload to ES
            merged_df.replace({np.inf:np.nan},inplace=True)
            merged_df.replace({np.nan: None}, inplace=True)
            
            # Fix for removing dates that are None
            merged_df = merged_df[merged_df['date'].notna()]

            merged_df['schedular']=schedular
            merged_df['updated_at']= datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            merged_df['date'] = merged_df['date'].astype(str)
            merged_df['updated_at'] = merged_df['updated_at'].astype(str)
            # merged_df = merged_df[merged_df['isin'] == 'US9182041080'].reset_index(drop=True)

            # # Do the 2 year min-max capping
            # tag_df = get_isin_df(tag)
            # merged_df = merged_df.merge(tag_df[['isin', 'dif_min_monthly', 'dif_max_monthly']], on='isin', how='left')
            # merged_df['predictions'] = merged_df.apply(lambda x: clip_2y_min_max(x['predictions'], x['dif_min_monthly'], x['dif_max_monthly']), axis=1)
            

            es_client = OpenSearch(
                host=eq_data_url,
                port=443,
                key_id=OS_API_KEY,
                secret=OS_SECRET,
                region = 'us-east-1'
                )
            
            if merged_df.shape[0] > 0:
                s3_conn.write_advanced_as_df(merged_df, bucket_name, file_name_output)
                es_client.save_records_v2(merged_df,config['open_search']['info_model_predictions_index'])
                merged_df.parallel_apply(lambda x: save_row(x, 'predictions'), axis=1)
                # merged_df.to_csv(f'merged_pred/{date}.csv', index=False)
                subject, message = gen_message_prediction_run_status(tag, schedular, merged_df)
                return subject, message
    except Exception as e:
        print(e)
        print(traceback.print_exc())
    
    print('**** Merging Script Completed ****')
    return None, None

def accuracy_function(df_series, coff = 500):
    '''
    Accuracy conversion metric to convert daily APE into accuracy for ER.
    '''
    return df_series.apply(lambda x: 97/( 1 + 20 * np.exp((-coff/x))) if x <  100 else max(0, 106.7 - 0.213 * x))

def calculate_accuracy(df_data, prediction_col = 'predictions', target_col = 'close_change_monthly', coff = 500):

    if prediction_col not in df_data.columns:
        raise Exception('Prediction column not in Dataframe')

    if target_col not in df_data.columns:
        raise Exception('Target column not in Dataframe')
    
    # Remove any nan's in prediction or target cols
    df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

    # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
    df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
    df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

    # Calculate RMS of MAPE over a rolling of 14 days
    df_data['accuracy_1_day'] = accuracy_function(df_data['daily_ape'], coff = coff)

    # Calculate RMS of MAPE over a rolling of 22 days
    df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
    df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5
    
    df_data.drop(columns=['denominator'], inplace=True)
    
    return df_data


def directionality_score_calc(prediction_direction, close_direction):
    directionality_df = pd.DataFrame()
    directionality_df['prediction_direction'] = prediction_direction
    directionality_df['close_direction'] = close_direction
    correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)] 
    incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)] 
    relaxation_count = len(incorrect_direction_df[(abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)]) 
    try:
        directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
    except ZeroDivisionError:
        directionality_score = 0  # Or any other appropriate value
    return directionality_score


def get_confidence_score(df, metrics_to_calculate = all_metrics):
    metrics_columns = req_columns + metrics_to_calculate
    # Confidence score
    if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
        min_confidence = 0.01
        max_values =  df['actual_monthly_returns'].rolling(period * 24, min_periods = period).max()
        min_values =  df['actual_monthly_returns'].rolling(period * 24, min_periods = period).min()
        filt1 = [df.loc[i, 'monthly_predictions'] >= max_values.loc[i] for i in range(len(df))]
        filt2 = [df.loc[i, 'monthly_predictions'] <= min_values.loc[i] for i in range(len(df))]
        filt3 = [df.loc[i, 'actual_monthly_returns'] >= max_values.loc[i] for i in range(len(df))]
        filt4 = [df.loc[i, 'actual_monthly_returns'] <= min_values.loc[i] for i in range(len(df))]

        return [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
        max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_monthly_returns"])/(max_values.loc[i] - df.loc[i, "monthly_predictions"]) if df.loc[i, "actual_monthly_returns"] > df.loc[i, "monthly_predictions"] else (df.loc[i, "actual_monthly_returns"] - min_values.loc[i]) / (df.loc[i, "monthly_predictions"] - min_values.loc[i])) for i in range(len(df))]

def calculate_metrics(df, isin, period, prediction_column = 'monthly_predictions', actual_column =  'actual_monthly_returns', metrics_to_calculate = all_metrics, n_features = 0):
    if len(df) < period:
        return f'Dataframe size for {isin} too small to calculate metrics.'
    
    df = df.rename(columns = {prediction_column: 'monthly_predictions', actual_column: 'actual_monthly_returns'})
    metrics_columns = req_columns + metrics_to_calculate

    # Confidence score
    if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
        min_confidence = 0.01
        
        max_values =  df['actual_monthly_returns'].rolling(period * 24, min_periods = period).max()
        min_values =  df['actual_monthly_returns'].rolling(period * 24, min_periods = period).min()
        filt1 = [df.loc[i, 'monthly_predictions'] >= max_values.loc[i] for i in range(len(df))]
        filt2 = [df.loc[i, 'monthly_predictions'] <= min_values.loc[i] for i in range(len(df))]
        filt3 = [df.loc[i, 'actual_monthly_returns'] >= max_values.loc[i] for i in range(len(df))]
        filt4 = [df.loc[i, 'actual_monthly_returns'] <= min_values.loc[i] for i in range(len(df))]

        df['confidence_score'] = [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
        max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_monthly_returns"])/(max_values.loc[i] - df.loc[i, "monthly_predictions"]) if df.loc[i, "actual_monthly_returns"] > df.loc[i, "monthly_predictions"] else (df.loc[i, "actual_monthly_returns"] - min_values.loc[i]) / (df.loc[i, "monthly_predictions"] - min_values.loc[i])) for i in range(len(df))]

    # Total perc diff
    if 'total_perc_diff' in metrics_columns or 'abs_total_diff' in metrics_columns:
        df['total_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.mean() - prediction.mean()) / prediction.mean(), period, df['actual_monthly_returns'].values, df['monthly_predictions'].values)
        
    # Abs total diff
    if 'abs_total_diff' in metrics_columns:
        df['abs_total_diff'] = abs(df['total_perc_diff'])
    
    # Total variance perc diff
    if 'total_variance_perc_diff' in metrics_columns or 'abs_total_variance_perc_diff' in metrics_columns:
        df['total_variance_perc_diff'] = rolling_apply_ext(lambda target, prediction: (target.var() - prediction.var()) / prediction.var(), period, df['actual_monthly_returns'].values, df['monthly_predictions'].values)
        
    # Abs total variance perc diff
    if 'abs_total_variance_perc_diff' in metrics_columns:
        df['abs_total_variance_perc_diff'] = abs(df['total_variance_perc_diff'])
    
    # MAE
    if 'mean_absolute_error' in metrics_columns:
        df['mean_absolute_error'] = (df['actual_monthly_returns'] - df['monthly_predictions']).rolling(period).apply(lambda df_series: abs(df_series).mean())
    
    # MSE
    if 'mean_squared_error' in metrics_columns or 'root_mean_squared_error' in metrics_columns:
        df['mean_squared_error'] = (df['actual_monthly_returns'] - df['monthly_predictions']).rolling(period).apply(lambda df_series: ((df_series ** 2).sum() / period))
    
    # RMSE
    if 'root_mean_squared_error' in metrics_columns:
        df['root_mean_squared_error'] = df['mean_squared_error'] ** 0.5
        
    # R2 score
    if 'r2_score' in metrics_columns or 'adjusted_r2_score' in metrics_columns:
        df['r2_score'] = rolling_apply_ext(r2_score, period, df['actual_monthly_returns'].values, df['monthly_predictions'].values)
                
    # Adjusted R2 score
    if 'adjusted_r2_score' in metrics_columns:
        df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features - 1))
    
    # Mean directionality
    if 'mean_directionality' in metrics_columns:
        df['mean_directionality'] = (df['actual_monthly_returns'] * df['monthly_predictions']).rolling(period).apply(lambda df_series: 100 * ((df_series) > 0).sum() / period)
    
    # Correlation score
    if 'correlation_score' in metrics_columns:
        df['correlation_score'] = rolling_apply_ext(lambda target, prediction: np.corrcoef(target, prediction)[0][1], period, df['actual_monthly_returns'].values, df['monthly_predictions'].values) 
        
    # Accuracy
    if "accuracy_14_day" in metrics_columns or "accuracy_22_day" in metrics_columns or "accuracy_1_day"  in metrics_columns:
        df = calculate_accuracy(df, 'monthly_predictions', 'actual_monthly_returns')
        
    # Average confidence score
    if 'avg_confidence_score' in metrics_columns:
        df['avg_confidence_score'] = df['confidence_score'].rolling(period // 2).mean()

    if 'directionality_score' in metrics_columns:
        directionality_df = pd.DataFrame()
        directionality_df["prediction_direction"] = (df["monthly_predictions"] - df['monthly_predictions'].shift(1)) / df['monthly_predictions'].shift(1)
        directionality_df["close_direction"] = (df["actual_monthly_returns"] - df['actual_monthly_returns'].shift(1)) / df['actual_monthly_returns'].shift(1)
        df['directionality_score'] = rolling_apply_ext(directionality_score_calc, period, directionality_df['prediction_direction'], directionality_df['close_direction']) 
    df['isin'] = isin
    return df[metrics_columns][period:].reset_index(drop = True)

def read_data(isin,schedular, read_from = 'es', append_data = None, pred_col = 'predictions'): # function to read historical data
    years = [datetime.now().date().year-4,datetime.now().date().year]
    period_schedular = 1 if schedular=='Daily' else 5 if schedular=='Weekly' else 22
    columns_not_needed = ['date','isin','actual_monthly_return','actual_monthly_return_predictions','ind_code','schedular','updated_at','tic']
    if read_from == 'es': # fetching data from es
        temp = get_es_data_metrics(isin,years,config['open_search']['info_model_predictions_index'])
        temp = temp[temp['schedular']==schedular]
        n_features = []
        for i in temp.columns:
            if i not in columns_not_needed:
                n_features.append(i)
        temp = temp[['date', pred_col,'close_price']] # fetching data from es
    temp['date'] = pd.to_datetime(temp['date'])
    
    temp = temp[(temp['date'] >= pd.to_datetime(f'{years[0]}-01-01')) & (temp['date'] <= pd.to_datetime(f'{years[1]}-12-31'))]
    business_days = pd.bdate_range(temp['date'].min(), temp['date'].max())
    temp = temp[temp['date'].isin(business_days)]
    if append_data is not None:
        temp = temp.append(append_data[(append_data['isin'] == isin) & (append_data['date'] > temp['date'].max())].drop(columns = ['isin']))
          
    temp.rename(columns = {pred_col:'monthly_predictions'}, inplace = True)
    
    temp['actual_monthly_returns'] = (temp['close_price'].pct_change(periods=period_schedular))*100
    
    temp['monthly_predictions'] = temp['monthly_predictions'].shift(period_schedular)* 100
    temp = temp.drop_duplicates(subset = 'date').sort_values(by = 'date').reset_index(drop = True)
    temp = temp.ffill().fillna(0)
    temp = temp[(temp['monthly_predictions'] != 0) & \
            (temp['actual_monthly_returns'] != 0) & \
            temp['monthly_predictions'].notna() & \
            temp['actual_monthly_returns'].notna()]  
    temp.reset_index(drop = True, inplace = True)
    temp['monthly_predictions'] = temp['monthly_predictions'].astype(float, errors="ignore")
    temp['actual_monthly_returns'] = temp['actual_monthly_returns'].astype(float, errors="ignore")
    return temp,len(list(set(n_features)))

def calculate_and_save_metrics(isin,formatted_date_info,schedular):
    try:
        temp,n_features = read_data(isin,schedular,read_from='es')
    except Exception as e:
        
        print(f'exception is str({e})')
        print(f"Data not available for {isin}.")
        failed_metrics_list.append((isin,e))
        return
    temp.replace([np.inf], np.nan, inplace=True)
    temp = temp.sort_values(by='date').reset_index(drop=True)
    temp = temp.ffill()
    try:
        date_index = len(temp) - (temp[temp['date'] == formatted_date_info].index.values[0])
    except:
        date_index = 0
    try:
        metrics_df1 = calculate_metrics(temp[-(30+date_index):], isin, period = 22, n_features=n_features, metrics_to_calculate = mtx_2_calc1)
        if type(metrics_df1) ==  str:
            print(metrics_df1)
            return
        metrics_df2 = calculate_metrics(temp, isin, period=22 ,n_features=n_features, metrics_to_calculate = mtx_2_calc2).drop(columns = ['isin', 'actual_monthly_returns', 'monthly_predictions'])
        if type(metrics_df2) ==  str:
            print(metrics_df2)
            return
        
        metrics_df = pd.merge(metrics_df1, metrics_df2, on = 'date', how = 'left')
        metrics_df['date'] = metrics_df['date'].apply(lambda x: x.strftime("%Y-%m-%d"))
        # metrics_df = metrics_df[metrics_df['date'] >= formatted_date_info]
        return metrics_df
    except Exception as e:
        traceback.print_exc()
        failed_metrics_list.append((isin,e))

def generate_metrics(tag,schedular, formatted_date_info):
    es_client = OpenSearch(
            host=eq_data_url,
            port=443,
            key_id=OS_API_KEY,
            secret=OS_SECRET,
            region = 'us-east-1'
            )
    isin_list = get_isin_df(tag)['isin'].to_list()
    isin_df=pd.DataFrame(isin_list,columns=['isin'])
    results_metrics = isin_df.parallel_apply(lambda x: calculate_and_save_metrics(x['isin'],formatted_date_info,schedular),axis=1)
    metrics_df = pd.concat(results_metrics.tolist())
    metrics_df.reset_index(drop=True,inplace=True)
    # failed_metrics_list = list(failed_metrics_list)

    metrics_df.replace({np.inf:np.nan},inplace=True)
    metrics_df['schedular']= schedular
    metrics_df['updated_at']= datetime.now()

    metrics_df['date'] = metrics_df['date'].astype(str)
    metrics_df['updated_at'] =metrics_df['updated_at'].astype(str)
    metrics_df = metrics_df[metrics_df['date'] == formatted_date_info]

    s3_conn = s3_config()
    metrics_file_name = f'{config[tag]["metrics_folder_path"]}data_{formatted_date_info}.csv'
    
    if metrics_df.shape[0] > 0:
        s3_conn.write_advanced_as_df(metrics_df, bucket_name, metrics_file_name)
        es_client.save_records_v2(metrics_df,config['open_search']['info_model_metrics_index'])
        metrics_df.parallel_apply(lambda x: save_row(x, 'metrics'), axis=1)
        message_metrics = gen_message_metrics_run_status(tag, schedular, metrics_df)
        return message_metrics
    

if __name__=="__main__":
    parser = argparse.ArgumentParser(
                    prog='ProgramName',
                    description='What the program does',
                    epilog='Text at the bottom of help')
    parser.add_argument('-t', '--tag')
    parser.add_argument('-s', '--schedular')
    parser.add_argument('-bdate', '--bdate')
    args = parser.parse_args()
    schedular = args.schedular
    tag = args.tag
    if args.bdate:
        formatted_date_info = args.bdate
    else:
        formatted_date_info = (current_date-BDay(1)).date().strftime("%Y-%m-%d")
    
    try:
        subject, message = merge_predictions(tag, schedular, formatted_date_info)
        message_metrics = generate_metrics(tag, schedular, formatted_date_info)

        if isinstance(message, str) and isinstance(message_metrics, str):
            message += message_metrics
        message_signature = gen_message_signature()
        message += message_signature

        send_email_ds_auto(
            sender = config["mailing"]["sender"],
            to = config["mailing"]["recipient_list"],
            subject= subject,
            filename = None,
            message_text=message,
        )

    except Exception as e:
        message_text = f"Information Model run failure for Tag {tag} {schedular} due to following exception" + str(e)
        message_signature = gen_message_signature()
        message_text += message_signature
        send_email_ds_auto(
            sender = config["mailing"]["sender"],
            to = config["mailing"]["recipient_list_pre_prod"],
            subject = f'Information Model {tag.upper()} {schedular.capitalize()} Run Failed',
            filename = None,
            message_text = message_text,
        )
        print(e)