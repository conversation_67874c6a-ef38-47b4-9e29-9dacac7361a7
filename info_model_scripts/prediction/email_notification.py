from utils import *

script_dir, config_path, env_path, config = load_config_and_env()

def gen_message_signature():
    return f'''
    <html>
        <body>
        For any queries or assistance, please feel free to reach out. <br>
        <br>
        Best,<br>
        <PERSON><PERSON><br>
        </body>
        </html>
    '''

def gen_and_send_message_trigger(config, tag, schedular, run_date):
    subject = f'Information Model {tag.upper()} {schedular.capitalize()} Run Trigger'
    message_text = f'''
    <html>
        <body>
            <p>Information {tag.upper()} {schedular.capitalize()} model run for Business Date {run_date} has been triggered.</p> <br>
        </body>
    </html>
    '''
    message_signature = gen_message_signature()
    message_text += message_signature

    send_email_ds_auto(
            sender = config["mailing"]["sender"],
            to = config["mailing"]["recipient_list_trigger"],
            subject= subject,
            filename = None,
            message_text=message_text,
        )
    

def gen_message_prediction_run_status(tag, schedular, pred_df):
    s3_client = s3_connection()

    tag_df = get_isin_df_excl(tag)
    pred_df = pred_df[pred_df['isin'].isin(tag_df['isin'])]
    run_date = pred_df['date'].unique()[0]

    log_df = s3_client.s32df(
        config["input_data"]["bucket_name"],
        f'{config["input_data"]["log_input_data_folder_path"]}{run_date}.csv'
    )

    log_df = log_df[~log_df['security_traded']]
    log_df = log_df[log_df['isin'].isin(tag_df['isin'])]

    total_isin = tag_df['isin'].nunique()
    successful_isin = pred_df[pred_df['predictions'].notna()].shape[0]
    untraded_isin = log_df.shape[0]
    failed_isin = total_isin - successful_isin - untraded_isin
    predicion_zero_isin = pred_df[pred_df['predictions'] == 0].shape[0]

    pred_df = pred_df[pred_df['predictions'].notna()]
    pred_df['model_identifier'] = pred_df.apply(lambda x: x['model_identifier_autoai'] if pd.isna(x['model_identifier_lstm']) else x['model_identifier_lstm'], axis=1)
    
    try:
        subject = f'Information Model {tag.upper()} {schedular.capitalize()} Run Completed'
        message_text = f"""
        <html>
        <body>
            <p>Information {tag.upper()} {schedular.capitalize()} model run for Business Date {run_date} has been successfully completed.</p>
            <p><strong>Summary for Predictions:</strong><br>
            &nbsp;&nbsp;&nbsp; Geography: {tag.upper()}, Schedular: {schedular.capitalize()}<br>
            

            &nbsp;&nbsp;&nbsp;- Total Number of ISIN: {total_isin}<br>

            &nbsp;&nbsp;&nbsp;- Number of Successful Runs: {successful_isin}<br>

            &nbsp;&nbsp;&nbsp;- Number of Failed Runs: {failed_isin}<br>
            &nbsp;&nbsp;&nbsp;- Number of ISIN not traded: {untraded_isin}<br>
            &nbsp;&nbsp;&nbsp;- Number of ISIN with 0 as Prediction: {predicion_zero_isin}<br>

            &nbsp;&nbsp;&nbsp;- Number of ISIN with predictions from individual model: { pred_df[pred_df['model_identifier'].apply(lambda x: True if len(x.split('_'))>2 and len(x.split('_')[0]) > 6 else False)].shape[0] }<br>
            &nbsp;&nbsp;&nbsp;- Number of ISIN with predictions from sector-based model: { pred_df[pred_df['model_identifier'].apply(lambda x: True if len(x.split('_'))>2 and len(x.split('_')[0]) <= 6 else False)].shape[0] }<br>
            &nbsp;&nbsp;&nbsp;- Number of ISIN with predictions from sector-average: { pred_df[pred_df['model_identifier'].apply(lambda x: True if x.lower()=='sector-average' else False)].shape[0] }<br>
            </p>


        </body>
        </html>
        """
        return subject, message_text
    except Exception as e:
        print(f"Error in sending mail regarding run completion: {e}")


def gen_message_metrics_run_status(tag, schedular, metrics_df):
    s3_client = s3_connection()

    tag_df = get_isin_df_excl(tag)
    metrics_df = metrics_df[metrics_df['isin'].isin(tag_df['isin'])]

    total_isin = tag_df['isin'].nunique()
    successful_isin = metrics_df.shape[0]
    failed_isin = total_isin - successful_isin

    try:
        message_text = f"""
        <html>
        <body>
            <p><strong>Summary for Metrics:</strong><br>
            &nbsp;&nbsp;&nbsp; Geography: {tag.upper()}, Schedular: {schedular.capitalize()}<br>

            &nbsp;&nbsp;&nbsp;- Total Number of ISIN: {total_isin}<br>

            &nbsp;&nbsp;&nbsp;- Number of Successful Runs: {successful_isin}<br>

            &nbsp;&nbsp;&nbsp;- Number of Failed Runs: {failed_isin}<br>
        </body>
        </html>
        """
        return message_text
    except Exception as e:
        print(f"Error in sending mail regarding run completion: {e}")

def gen_ffill_report(date, schedular):
    ffill_df = es_connection().get_es_data_datewise(date, date, schedular, config["versioning"]["model_key"])

    def generate_backward_nan_report(self, df, feature_cols, date_col='date', isin_col='isin'):
        nan_report = defaultdict(list)
        df = df.sort_values(by=[isin_col, date_col])
        last_date = df[date_col].max()
        grouped = df.groupby(isin_col)

        for isin, group in grouped:
            group = group.sort_values(date_col, ascending=False).reset_index(drop=True)
            last_row = group.iloc[0]

            for feat in feature_cols:
                if pd.isna(last_row[feat]):
                    nan_streak = 0
                    start_date = None
                    for _, row in group.iterrows():
                        if pd.isna(row[feat]):
                            nan_streak += 1
                            start_date = row[date_col]
                        else:
                            break
                    if nan_streak > 0:
                        nan_report[isin].append({
                            'feature': feat,
                            'start_date': start_date,
                            'end_date': last_date,
                            'no_of_days_nan': nan_streak
                        })
        return nan_report                                      
                                     
    def flatten_nan_report(self, nan_report):
        records = []
        for isin, issues in nan_report.items():
            for issue in issues:
                records.append({
                    'isin': isin,
                    'feature': issue['feature'],
                    'start_date': issue['start_date'],
                    'end_date': issue['end_date'],
                    'no_of_days_nan': issue['no_of_days_nan']
                })
        return pd.DataFrame(records)
  
    def get_credentials(self):
        SCOPES = config['common_gmail']['scope']
        creds = None
 
        response = self.s3_client._s3Client.get_object(Bucket=config['common_gmail']['gmail_cred_bucket'], Key=config['common_gmail']['gmail_cred_file'])
        credentials_data = json.loads(response['Body'].read().decode("utf-8"))
        creds = GoogleCredentials.from_authorized_user_info(info=credentials_data, scopes=SCOPES)
        return creds                                 
                                     
    def SendMessage(self, sender, to, subject, filename, message_text, attachments: list = None):
        credentials = self.get_credentials()
        if credentials == None:
            return print("credentials not found, Generate credentials")
        try:
            service = build('gmail', 'v1', credentials=credentials)
            message = EmailMessage()
            html = message_text
            body = MIMEText(html, 'html')
            message.set_content(body)
            if attachments:
                for i,attachment in enumerate(attachments):
                    with open(attachment, 'rb') as content_file:
                        content = content_file.read()
                        message.add_attachment(content, maintype='application', subtype=(
                            attachment.split('.')[1]), filename=filename[i])
            message['To'] = to
            message['From'] = sender
            message['Subject'] = subject

            encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
                .decode()

            create_message = {
                'raw': encoded_message
            }
            send_message = (service.users().messages().send(
                userId="me", body=create_message).execute())
            print(F'Message Id: {send_message["id"]}')
        except HttpError as error:
            print(F'An error occurred: {error}')
            send_message = None  


# feature_owner_mapping: {'<EMAIL>':['adx','apo','cci','dx','mfi','mom','ppo','rsi','ultosc','willr','macd_0','macd_1','macd_2','macdfix_0','macdfix_1','macdfix_2','stoch_0','stoch_1','stochf_0','stochf_1','stochrsi_0','stochrsi_1'],

#                         '<EMAIL>':['closeprice', 'volume', 'marketcap', 'tev', 'sharesoutstanding', 'price_vol_hist_5yr', 'price_vol_hist_2yr', 'price_vol_hist_yr', 'price_vol_hist_6mth', 'price_vol_hist_3mth', 'pe_excl', 'peg_fwd_ciq', 'price_cfps_fwd_ciq', 'pe_excl_fwd', 'price_sales', 'pbv', 'dividend_yield', 'inflation_rate', 'cpi', 'opinion_survey', 'unemployment', 'ppi', 'yield_maturity', 'growth_1d', 'sphq_1d', 'vlue_1d', 'mtum_1d', 'growth_7d', 'sphq_7d', 'vlue_7d', 'mtum_7d', 'growth_30d', 'sphq_30d', 'vlue_30d', 'mtum_30d','close_change_yesterday', 'close_change_weekly', 'close_change_monthly', 'volume_change_yesterday', 'volume_change_weekly', 'volume_change_monthly', 'average_market_cap', 'ar_turns', 'asset_turns', 'cash_st_invest', 'cogs', 'current_ratio', 'earning_co_margin', 'ebit_int', 'ebit_margin', 'ebitda', 'ebitda_capex_int', 'ebitda_margin', 'effect_tax_rate', 'eps_1yr_ann_growth', 'eps_5yr_ann_cagr', 'est_act_return_assets_ciq', 'fixed_asset_turns', 'gross_margin', 'inventory_turns', 'lt_debt', 'lt_debt_equity', 'minority_interest', 'net_debt_ebitda', 'net_operating_income', 'ni_margin', 'payout_ratio', 'pref_equity', 'quick_ratio', 'rd_exp', 're', 'return_assets', 'return_capital', 'return_equity', 'sga', 'sga_margin', 'tbv_5yr_ann_cagr', 'total_common_equity', 'total_debt', 'total_debt_capital', 'total_debt_equity', 'total_rev', 'total_rev_1yr_ann_growth', 'total_rev_3yr_ann_cagr', 'total_rev_5yr_ann_cagr', 'total_rev_employee', 'total_rev_share', 'ufcf_3yr_ann_cagr', 'ufcf_5yr_ann_cagr', 'unlevered_fcf', 'z_score', 'beta_1yr', 'beta_2yr', 'beta_5yr', 'isin', 'tic', 'dps_5yr_ann_cagr', 'curr_foreign_taxes', 'capex_pct_rev', 'benchmark_closeprice_adj', 'benchmark_volume', 'equity_value_per_share', 'iwf_corr_er', 'iwf_er', 'mtum_corr_er', 'mtum_er', 'sphq_corr_er', 'sphq_er', 'vlue_corr_er', 'vlue_er', 'predictions'],

#                         '<EMAIL>':['mp1d','mp1w','mp1m','mp1q','mp6m','mp1y','terminalroe', 'valprice']}

# model_mapping: {'<EMAIL>':'TI', '<EMAIL>':'finance', '<EMAIL>':'dcf-momentum'}    

