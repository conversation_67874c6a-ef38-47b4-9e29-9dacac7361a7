from utils import *

script_dir, config_path, env_path, config = load_config_and_env()

def remove_unnecessary_col(config, df):
    '''
    Removes the index and its derivative columns from the dataframe
    '''
    for unnecessary_col in config['unnecessary_cols']:
        if unnecessary_col in df.columns:
            df.drop(columns=[unnecessary_col], inplace=True)
    return df

def prep_raw_data(config, date):
    assert isinstance(date, str)
    s3_conn = s3_config()
    s3_client = s3_connection()

    try:
        raw_st_file_df = s3_client.s32df(config["input_data"]["bucket_name"], f'{config["input_data"]["structured_input_data_folder_path"]}{date}.csv')
        raw_unst_file_df = s3_client.s32df(config["input_data"]["bucket_name"], f'{config["input_data"]["unstructured_input_data_folder_path"]}{date}.csv')
        raw_gserp_file_df = s3_client.s32df(config["input_data"]["bucket_name"], f'{config["input_data"]["gserp_input_data_folder_path"]}{date}.csv')
        if raw_gserp_file_df.shape[0] == 0:
            raw_gserp_file_df = pd.DataFrame([], columns=config['gserp_features'])
        raw_mis_st_file_df = s3_client.s32df(config["input_data"]["bucket_name"], f'{config["input_data"]["missing_structured_input_data_folder_path"]}{date}.csv')
        if raw_mis_st_file_df.shape[0] == 0:
            raw_mis_st_file_df = pd.DataFrame([], columns=config['mis_st_columns'])

        raw_st_file_df = remove_unnecessary_col(config, raw_st_file_df)
        raw_unst_file_df = remove_unnecessary_col(config, raw_unst_file_df)
        raw_gserp_file_df = remove_unnecessary_col(config, raw_gserp_file_df)
        raw_mis_st_file_df = remove_unnecessary_col(config, raw_mis_st_file_df)

        merge_df = raw_st_file_df.merge(raw_unst_file_df, on=['isin', 'date'], how='outer')
        
        merge_df = merge_df.merge(raw_gserp_file_df, on=['isin', 'date'], how='outer')

        merge_df = merge_df.merge(raw_mis_st_file_df, on='isin', how='outer').drop(columns=["NaN Columns"])
        merge_df["Reason"] = merge_df["Reason"].fillna("Traded")

        merge_df['date'] = merge_df['date'].fillna(method='ffill')
        s3_conn.write_advanced_as_df(merge_df, config["input_data"]["bucket_name"], f'{config["input_data"]["raw_input_data_folder_path"]}{date}.csv')

    except Exception as e:
        print(e)


def prep_ffill_data(config, date, tag):
    assert isinstance(tag, str)
    s3_conn = s3_config()
    s3_client = s3_connection()

    # Get all the isins for which we want to merge and get the forward filled predictions
    tag_df = get_isin_df(tag)
    tag_df = tag_df.drop_duplicates(subset='isin')
    
    # Get all the relevant columns for the tag
    input_features_json = s3_client.s3_to_dict(config["input_data"]["bucket_name"], config["input_data"]["input_features_file_path"])
    input_cols = input_features_json[tag]["input_features"]

    # Get the raw data file and All the respective columns for the tag
    raw_data_df = s3_client.s32df(config["input_data"]["bucket_name"], f'{config["input_data"]["raw_input_data_folder_path"]}{date}.csv')

    if raw_data_df.shape[0] == 0:
        return

    raw_data_df = raw_data_df[raw_data_df['isin'].isin(tag_df['isin'])]
    raw_data_df = raw_data_df.drop(columns=['ind_code']).merge(tag_df[['isin', 'ind_code']], on='isin', how='left')

    for col in input_cols:
        if col not in raw_data_df.columns:
            raw_data_df[col] = None
    raw_data_df = raw_data_df[input_cols + ["Reason"]]

    # Check for securities traded and close price null
    raw_data_df['security_traded'] = raw_data_df.apply(lambda x: False if x["Reason"].split(" ")[0].lower() == "not" else True , axis=1)
    raw_data_df['close_price_null'] = raw_data_df.apply(lambda x: True if math.isnan(x["close_price"]) else False, axis=1)
    raw_data_df['close_price_ffill'] = raw_data_df.apply(lambda x: True if (x['security_traded'] and x["close_price_null"]) else False, axis=1)
    raw_data_df['cols_to_ffill'] = raw_data_df.apply(lambda x: x.index[x.isna()].tolist(), axis=1)
    
    # Create log dataframe
    log_file_df = raw_data_df.copy()
    log_file_df['updated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    raw_data_df = raw_data_df[raw_data_df['security_traded']]

    # forward fill the predictions from last available dates
    processed_files_df = pd.DataFrame(s3_conn.get_objects_in_range(config["input_data"]["bucket_name"], config["input_data"]["processed_input_data_folder_path"], end=(datetime.now() + timedelta(days=1)) ) )
    processed_files_df["Bucket"] = config["input_data"]["bucket_name"]
    processed_files_df["date"] = processed_files_df["Key"].apply(lambda x: x.split("/")[-1].split(".")[0])
    processed_files_df = processed_files_df[processed_files_df["date"] >= (pd.to_datetime(date) + timedelta(days=-config["input_data"]["ffill_period"])).strftime("%Y-%m-%d")]
    processed_files_df = processed_files_df[processed_files_df["date"] <= pd.to_datetime(date).strftime("%Y-%m-%d")]
    # processed_files_df = processed_files_df[:10]
    
    results = s3_client.bulk_s32df(processed_files_df["Bucket"].tolist(),
                                        processed_files_df["Key"].tolist())

    ffill_df = pd.concat(results)
    ffill_df_es = es_connection().get_es_data(
        start_date = (pd.to_datetime(date) + timedelta(days=-config["input_data"]["ffill_period"])).strftime("%Y-%m-%d"),
        end_date = pd.to_datetime(date).strftime("%Y-%m-%d"),
        isin_list = raw_data_df[~raw_data_df['isin'].isin(ffill_df['isin'].unique())]['isin'].tolist(),
        index_prefix = config["open_search"]["info_model_predictions_index"]
    )

    ffill_df = pd.concat([ffill_df, ffill_df_es]).reset_index(drop=True)
    ffill_df = ffill_df.drop_duplicates(subset=['isin', 'date'], keep='first')

    for col in input_cols:
        if col not in ffill_df.columns:
            ffill_df[col] = None

    for idx, row in raw_data_df.iterrows():
        isin_df = ffill_df[ffill_df['isin'] == row['isin']]
        isin_df = isin_df.sort_values(by='date').reset_index(drop=True)
        isin_df = isin_df.ffill()
        
        # Fill the forward filled columns
        if isin_df.shape[0] != 0:
            raw_data_df.loc[idx, row['cols_to_ffill']] = isin_df.loc[len(isin_df)-1, row['cols_to_ffill']]

    # Get the columns that have been forward filled in case the size of df is greater than 1
    raw_data_df = raw_data_df.replace(np.nan, None)
    if raw_data_df.shape[0] > 0:
        raw_data_df['fwd_filled_cols'] = raw_data_df.apply(lambda x: [col for col in x['cols_to_ffill'] if not pd.isna(x[col])], axis=1)
        log_file_df = log_file_df.merge(raw_data_df[['isin', 'fwd_filled_cols']], on='isin', how='left')
    else:
        raw_data_df["fwd_filled_cols"] = None
        log_file_df["fwd_filled_cols"] = None

    # Forward filling is completed, Now save the final dataframe and logs path
    processed_file_path = f'{config["input_data"]["processed_input_data_folder_path"]}{date}.csv'
    log_file_path = f'{config["input_data"]["log_input_data_folder_path"]}{date}.csv'
    
    if s3_client.check_path_in_s3(config["input_data"]["bucket_name"], processed_file_path):
        # Read the previous file and append the data in the new file
        prev_file_df = s3_client.s32df(config["input_data"]["bucket_name"], processed_file_path)
        final_data_df = pd.concat([raw_data_df[input_cols + ["cols_to_ffill", "fwd_filled_cols"]], prev_file_df]).reset_index(drop=True)
        final_data_df = final_data_df.drop_duplicates(subset='isin', keep='first')

        prev_log_file_df = s3_client.s32df(config["input_data"]["bucket_name"], log_file_path)
        final_log_data_df = pd.concat([log_file_df[['isin', 'security_traded', 'close_price_null', 'close_price_ffill','cols_to_ffill','fwd_filled_cols','updated_at']], prev_log_file_df]).reset_index(drop=True)
        final_log_data_df = final_log_data_df.drop_duplicates(subset='isin', keep='first')
    else:
        final_data_df = raw_data_df[input_cols + ["cols_to_ffill","fwd_filled_cols"]].reset_index(drop=True)
        final_log_data_df = log_file_df[['isin', 'security_traded', 'close_price_null', 'close_price_ffill','cols_to_ffill','fwd_filled_cols', 'updated_at']]

    s3_client.df2s3(final_data_df, config["input_data"]["bucket_name"], processed_file_path)
    s3_client.df2s3(final_log_data_df, config["input_data"]["bucket_name"], log_file_path)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
                    prog='Prepare Processed Data',
                    description='It merges and forward fills the files for various tags',
                    epilog='Pass tag and business date, by default bdate is take for yesterday')
    
    parser.add_argument('-t', '--tag')
    parser.add_argument('-bdate', '--bdate')
    
    args = parser.parse_args()
    tag = args.tag

    if args.bdate:
        bdate = args.bdate
    else:
        bdate = (datetime.now()-BDay(1)).date().strftime("%Y-%m-%d")

    prep_raw_data(config, bdate)
    prep_ffill_data(config, bdate, tag)
    