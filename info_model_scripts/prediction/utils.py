from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.metrics_helper import MetricsHelper
import boto3
import pandas as pd
from datetime import timedelta, date, datetime
import requests
import numpy as np
from ibm_watson import NaturalLanguageUnderstandingV1
from ibm_cloud_sdk_core.authenticators import IAMAuthenticator
from io import StringIO, BytesIO
from eq_common_utils.utils.config.es_config import es_config
from tqdm import tqdm
import smtplib
from eq_common_utils.utils.opensearch_helper import OpenSearch
import ast
import os
import json
import math
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from ibm_watson_machine_learning import APIClient
from keras.models import load_model #type: ignore
import tempfile
import keras.backend as K #type: ignore
import tarfile
from dotenv import load_dotenv
from eq_common_utils.ds_scripts.prediction_helper import PredictionHelper
from eq_common_utils.utils.ibm_helper import IBMConnection
import pickle
from dateutil.parser import parse
from ibm_watson.natural_language_understanding_v1 import Features,SentimentOptions, ConceptsOptions, EntitiesOptions, KeywordsOptions
import time
from newspaper import Article
from newspaper import fulltext
import traceback
import yaml
import argparse
from distutils.util import strtobool
from itertools import repeat
import multiprocessing
from numpy_ext import rolling_apply as rolling_apply_ext
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from pandas.tseries.offsets import BDay
from pandarallel import pandarallel
pandarallel.initialize(progress_bar=True,nb_workers=12)

# Imports for email
import base64
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.message import EmailMessage


script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")
env_path = os.path.join(script_dir, ".env")
with open(config_path,'r') as f:
    config = yaml.safe_load(f)

load_dotenv(env_path)
# OS_API_KEY = os.getenv('OS_API_KEY')
# OS_SECRET = os.getenv('OS_SECRET')
OS_API_KEY = config['open_search']['eq_os_key']
OS_SECRET = config['open_search']['eq_os_secret']
eq_data_url = config['open_search']['eq_data_url']
s3_conn = s3_config()

def load_config_and_env():
    '''
    Loads the Config and environment for any helper files
    '''
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, "config.yaml")
    env_path = os.path.join(script_dir, ".env")
    with open(config_path,'r') as f:
        config = yaml.safe_load(f)

    load_dotenv(env_path)
    return script_dir, config_path, env_path, config

def download_file_from_s3(local_path: str, bucket: str, path: str):
    s3_conn = s3_config()
    # Use the read_as_stream function to get the object content
    object_content, object_path = s3_conn.read_as_stream(bucket, path)

    if object_content:
        # Write the content to the local file system
        with open(local_path, 'wb') as file:
            file.write(object_content)
        print(f"Downloaded object from {object_path} to {local_path}")
    else:
        print(f"Failed to download object from {object_path}")

def save_row(row, data_type):
    '''
    This function is used for version control
    row -> row to be saved
    data_type -> predictions or metrics (str)
    '''

    if not isinstance(row['date'], str):
        date = pd.to_datetime(row['date']).strftime("%Y-%m-%d")
    else:
        date = row['date']
    
    assert data_type in ['predictions', 'metrics']

    model_key = config['versioning']['model_key']
    bucket_name = config['versioning']['bucket_name']
    file_name = f'{model_key}/{row["schedular"].lower()}/{date}/{data_type}/{row["isin"]}.csv'
    
    row_df = row.to_frame().T
    s3_connection().df2s3(row_df, bucket_name, file_name)

def unzip_tar_gz(archive_path, extract_path):
    '''
    archive_path : path to tar file
    extract_path : path where the file should be extracted
    '''
    try:
        with tarfile.open(archive_path, 'r:gz') as tar:
            tar.extractall(extract_path)
        # print("Files extracted successfully.")
    except Exception as e:
        print(f"Error extracting files: {e}")
    
def get_isin_df(tag):
    maf_url = config['maf_url']
    all_df = pd.DataFrame.from_dict(requests.get(f'{maf_url}{tag}').json()['data'][f'masteractivefirms_{tag}'])
    all_df['tag'] = tag
    return all_df


def get_isin_df_excl(tag):
    if tag == 'aigo':
        aigo = get_isin_df('aigo')
        aieq = get_isin_df('aieq')
        indt1 = get_isin_df('indt1')

        aigo = aigo[~aigo['isin'].isin(aieq['isin'])].reset_index(drop=True)
        aigo = aigo[~aigo['isin'].isin(indt1['isin'])].reset_index(drop=True)
        return aigo
    
    if tag == 'tier2' or tag == 'aieq':
        aipex = get_isin_df('aipex')
        aieq = get_isin_df('aieq')
        tier2 = aieq[~aieq['isin'].isin(aipex['isin'])].reset_index(drop=True)
        return tier2
    
    else:
        tag_df = get_isin_df(tag)
        return tag_df


def get_es_data_metrics(isin, dates, index_prefix):
    es_client = OpenSearch(
            host=eq_data_url,
            port=443,
            key_id=OS_API_KEY,
            secret=OS_SECRET,
            region = 'us-east-1'
            )
    try:
        data = []
        for year in range(dates[0], dates[1] + 1):
            q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
            try:
                # Run the query on Elasticsearch for the given year
                result = es_client.run_query(query=json.loads(q_total), index=f"{index_prefix}_{year}")
                for rs in result.get('hits', {}).get('hits', []):
                    es_data = rs.get('_source', {})
                    if es_data:  # Add data only if _source exists
                        data.append(es_data)
            except Exception as e:
                traceback.print_exc()
                print(f"Failed to query Elasticsearch for year {year}: {e}")

        # Convert the collected data to a DataFrame
        if data:
            df = pd.DataFrame(data)
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'], errors='coerce')  # Handle parsing errors
                df.sort_values('date', ascending=True, inplace=True)
                df.reset_index(drop=True, inplace=True)
            else:
                print("No 'date' column found in the Elasticsearch data.")
            return df
        else:
            print("No data retrieved from Elasticsearch.")
            return pd.DataFrame()  # Return an empty DataFrame if no data
    except Exception as e:
        print(f"An error occurred in get_es_data: {e}")
        return pd.DataFrame()  # Return an empty DataFrame on general failure
    

def send_email(subject, body, recipient_list):
    # Sender email address
    from_email = config['mailing']['email']
    app_password = config['mailing']['app_password']

    # Set up the MIME object
    msg = MIMEMultipart()
    msg['From'] = from_email
    msg['To'] = ', '.join(recipient_list)  # Join multiple email addresses with a comma
    msg['Subject'] = subject

    # Attach the email body
    msg.attach(MIMEText(body, 'plain'))
    
    try:
        # Establish a connection to the SMTP server
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            server.starttls()
            server.login(from_email, app_password)
    
            # Send the email
            server.sendmail(from_email, recipient_list, msg.as_string())

    except Exception as e:
        print('Failure sending email, due to exception:', e)


def get_credentials():
    SCOPES = [config["mailing"]["scopes"]]
    response = s3_conn._s3Client.get_object(Bucket=config['mailing']['gmail_cred_bucket'], Key=config['mailing']['gmail_cred_file'])        
    credentials_data = json.loads(response['Body'].read().decode("utf-8"))

    creds = Credentials.from_authorized_user_info(info=credentials_data, scopes=SCOPES)
    return creds

def send_email_ds_auto(sender, to, subject, filename, message_text, attachments: list = None):
    credentials = get_credentials()
    if credentials == None:
        return print("credentials not found, Generate credentials")
    try:
        service = build('gmail', 'v1', credentials=credentials)
        message = EmailMessage()
        html = message_text
        body = MIMEText(html, 'html')
        message.set_content(body)
        if attachments:
            for i,attachment in enumerate(attachments):
                with open(attachment, 'rb') as content_file:
                    content = content_file.read()
                    message.add_attachment(content, maintype='application', subtype=(
                        attachment.split('.')[1]), filename=filename[i])
        message['To'] = to
        message['From'] = sender
        message['Subject'] = subject

        # encoded message
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
            .decode()

        create_message = {
            'raw': encoded_message
        }
        # pylint: disable=E1101
        send_message = (service.users().messages().send(
            userId="me", body=create_message).execute())
        print(F'Message Id: {send_message["id"]}')
    except HttpError as error:
        print(F'An error occurred: {error}')
        send_message = None


class s3_connection():
    def __init__(self):
        # AWS Credientials
        self.s3 = s3_config()._s3Client

    def df2s3(self, df, bucket_name, file_name):
        s3_client = self.s3
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index = False)
        s3_client.put_object(Bucket=bucket_name,
                            Key=file_name,
                            Body=csv_buffer.getvalue()
                            )
        return True

    def s32df(self, bucket_name, file_name):
        try:
            s3_client = s3_config()._s3Client
            csv_obj = s3_client.get_object(Bucket=bucket_name, Key=file_name)
            csv_string = csv_obj['Body'].read().decode('utf-8')
            df = pd.read_csv(StringIO(csv_string))
            return df
        except Exception as e:
            print('Unable to locate file ', file_name, 'error:', e)
            return pd.DataFrame()

    def get_s3_keys_from_bucket(self, bucket_name, folder_path):
        '''
        To get all the files along with TIMESTAMP from the S3 folder
        '''
        s3 = self.s3
        s3_bucket_obj = s3.Bucket(bucket_name)
        key_list = []
        time_stamp_list = []
        try:
            for i, object_summary in enumerate(s3_bucket_obj.objects.filter(Prefix = folder_path)):
                try:
                    key_list.append(object_summary.key)
                    time_stamp_list.append(object_summary.last_modified)
                except Exception as e:
                    print(f'exception in for loop {e}')
        except Exception as e:
            print(f'Exception as outer loop{e}')
        keys_df = pd.DataFrame(list(zip(key_list, time_stamp_list)), columns= ['key', 'timestamp'])
        keys_df=keys_df[keys_df['key']!=folder_path]
        keys_df.sort_values(by=["timestamp"], ascending=False, inplace=True)
        return keys_df
    
    def check_path_in_s3(self, bucket_name, file_name):
        s3_client = self.s3

        # Check if the file exists in the S3 bucket
        try:
            s3_client.head_object(Bucket=bucket_name, Key=file_name)
            return True
        except s3_client.exceptions.NoSuchKey:
            return False
        except s3_client.exceptions.ClientError as e:
            print(f"An error occurred while checking for the file: {e}")
            return None

    def dict_to_s3(self, data_dict, bucket_name, file_key):
        try:
            # Initialize a session using boto3
            s3 = self.s3
            
            json_data = json.dumps(data_dict)
            s3.put_object(Bucket=bucket_name, Key=file_key, Body=json_data)
            return True
        except Exception as e:
            print('Unable to Store file in S3', e)
            return False

    def s3_to_dict(self, bucket_name, file_key):
        try:
            # Initialize a session using boto3
            s3 = self.s3

            response = s3.get_object(Bucket=bucket_name, Key=file_key)
            json_data = response['Body'].read().decode('utf-8')
            data_dict = json.loads(json_data)
            return data_dict
        except Exception as e:
            print('Unable to Retrieve file. Error', e)
            return None
        
    def delete_s3_object(self, bucket_name, file_key):
        try:
            # Initialize a session using boto3
            s3 = self.s3

            response = s3.delete_object(Bucket=bucket_name, Key=file_key)
            return response.get("ResponseMetadata", {}).get("HTTPStatusCode") == 204
        except Exception as e:
            print(f"Error deleting file {file_key} from bucket {bucket_name}: {e}")
            return False
    
    def get_bucket_and_key(self, s3_path):
        assert s3_path[:5] == 's3://', "Invalid s3 path"    
        bucket = s3_path.split('/')[2]
        key = '/'.join(s3_path.split('/')[3:])
        return bucket, key

    def bulk_s32df(self, bucket_list, file_list):
        assert isinstance(bucket_list , list)
        assert isinstance(file_list , list)
        assert len(bucket_list) == len(file_list)
        
        # Load all the processed files
        try:
            with ThreadPoolExecutor(max_workers=50) as executor:
                # Apply the function to each row of the DataFrame in parallel
                results =  list(tqdm(
                    executor.map(self.s32df,
                                bucket_list,
                                file_list),
                    total=len(bucket_list),
                    desc="S3 bulk file read",
                ))
            return results
        except Exception as e:
            print(traceback.print_exc())
            raise e
    
class es_connection():

    def __init__(self, type='prod'):
        if type == 'prod':
            self.es_client = es_config(env='prod')
        else:
            self.es_client = es_config(env='pre')
        return

    def get_es_data(self, start_date, end_date, isin_list, index_prefix):
        start_year = pd.to_datetime(start_date).year
        end_year = pd.to_datetime(end_date).year
        year_list = [year for year in range(start_year, end_year + 1)]

        query = {
            "query": {
                "bool": { 
                    "must": [
                        {
                            "terms": { "isin.keyword": isin_list  # Replace with your list of IDs
                            }
                        },
                        {
                            "range": {
                                "date": {
                                    "gte": start_date,    # Start of the last 10 days
                                    "lte": end_date     # End of today
                                    }
                                }
                        }
                    ]
                }
            },
            "sort": [{
                "date": {
                    "order": "desc"  
                }
            }]
        }

        try:
            all_hits_list = []
            for year in range(start_year, end_year + 1):
                try:
                    response,total_docs = self.es_client.search_with_pagination(index=f"{index_prefix}_{year}",query=query,paginate=False,strict=False)
                except Exception as e:
                    continue
                for hit in response:
                    es_data=hit['_source']
                    all_hits_list.append(es_data)
            all_hits_df = pd.DataFrame(all_hits_list)
            all_hits_df['date']=pd.to_datetime(all_hits_df['date'])
            all_hits_df.sort_values('date', ascending=True, inplace=True)
            all_hits_df.reset_index(inplace=True, drop=True)
            return all_hits_df
        
        except Exception as e:
            print(e)
            return pd.DataFrame()

    def get_es_data_datewise(self, start_date, end_date, schedular, index_prefix):
        start_year = pd.to_datetime(start_date).year
        end_year = pd.to_datetime(end_date).year
        year_list = [year for year in range(start_year, end_year + 1)]

        query = {
            "query": {
                "bool": { 
                    "must": [
                        {
                            "terms": { "schedular": schedular  # Replace with your list of IDs
                            }
                        },
                        {
                            "range": {
                                "date": {
                                    "gte": start_date,    # Start of the last 10 days
                                    "lte": end_date     # End of today
                                    }
                                }
                        }
                    ]
                }
            },
            "sort": [{
                "date": {
                    "order": "desc"  
                }
            }]
        }

        try:
            all_hits_list = []
            for year in range(start_year, end_year + 1):
                try:
                    response,total_docs = self.es_client.search_with_pagination(index=f"{index_prefix}_{year}",query=query,paginate=False,strict=False)
                except Exception as e:
                    pass
                for hit in response:
                    es_data=hit['_source']
                    all_hits_list.append(es_data)
            all_hits_df = pd.DataFrame(all_hits_list)
            all_hits_df['date']=pd.to_datetime(all_hits_df['date'])
            all_hits_df.sort_values('date', ascending=True, inplace=True)
            all_hits_df.reset_index(inplace=True, drop=True)
            return all_hits_df
        
        except Exception as e:
            print(e)
            return pd.DataFrame()


def clip_2y_min_max(prediction, min_val, max_val):
    if (not pd.isna(min_val)) and (not pd.isna(prediction)):
        prediction = max(float(min_val)/100, prediction)
    if (not pd.isna(max_val)) and (not pd.isna(prediction)):
        prediction = min(float(max_val)/100, prediction)
    return prediction