from utils import *

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")
env_path = os.path.join(script_dir, ".env")

load_dotenv(env_path)
with open(config_path,'r') as f:
    config = yaml.safe_load(f)
    

s3_conn = s3_config()
bucket_name = config['s3']['bucket_name']
lstm_features = config['lstm_features']

def convert_to_timeseries(data, index, lookback):
    # finds the data sample at the given index, converts to tensor and returns as a tuple
    x1 = []
    x_cols = lstm_features
    # take first 20 rows given the index 20, then next 20 rows etc
    while index != data.shape[0]-lookback:
        x = data.iloc[index: index+lookback, :][x_cols].values
        x1.append(x)
        index = index + 1
    return np.array(x1)

def fetch_data_from_s3(input_data_folder,start_date, end_date):
    file_df = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, input_data_folder))
    
    df_isins_list = []
    for file_name in file_df['Key'].tolist():
        try:
            file_date = file_name.split('/')[-1].split('.')[0]
            if file_date >= start_date and file_date <= end_date:
                df_isins_list.append(s3_conn.read_as_dataframe(bucket_name, file_name))
        except Exception as e:
            print(f"Exception {str(e)} in file", file_name)
            continue
    df_isins = pd.concat(df_isins_list, axis=0)
    return df_isins        

def preprocess_data(isin, df_data, final_start_date,safe_start_date):
    if df_data.shape[0] == 0:
        print('No data present for isin:', isin)
        return {}
    # Select the relevant columns, sort by date and drop duplicates
    df_data = df_data[["isin", "date", "open_price", "high_price", "low_price", "close_price"]].copy()
    df_data = df_data.sort_values(by = ["date"])
    df_data["date"] = pd.to_datetime(df_data["date"])

    # Sort the dates and forward fill for the required dates
    df_data = df_data.sort_values(by = ["date"]).ffill().reset_index(drop=True)
    df_data = df_data.drop_duplicates(subset = "date", keep = "first").reset_index(drop=True)
    df_data["year_extracted"] = df_data["date"].apply(lambda x : str(x).split("-")[0])
    
    # Convert date to datetime64 and rename the column names
    df_data = df_data.rename(columns = {"open_price" : "Open", "high_price" : "High",
                             "low_price" : "Low", "close_price" : "Close",
                             "volume" : "Volume", "date" : "Date"})
    
    # Calculate the precentage change and drop any Infinity/NAN rows
    df_data[lstm_features] = df_data[["Open", "High", "Low", "Close"]].apply(lambda x : x.pct_change(periods=22, fill_method='pad'))
    
    df_data.replace([np.inf, -np.inf], np.nan, inplace=True)
    
    df_data = df_data.dropna().reset_index(drop=True)
    
    # Get the relevant rows from the data
    queried_data = df_data[["Date"]+lstm_features].copy()
    
    # Keep only the relevant date
    final_queried_data = queried_data[queried_data["Date"] >= final_start_date]
    
    # Convert to timeseries to get the input data and the dates range
    lookback = 20
    if final_queried_data.shape[0] < lookback:
        print('Not enough data for isin', isin, 'data:', final_queried_data.shape)
        return {}
    queried_x = convert_to_timeseries(final_queried_data, 0, lookback)
    dates_isin = final_queried_data[final_queried_data["Date"] >= safe_start_date]["Date"].tolist()[-queried_x.shape[0]:]
    
    final_isin_dict = {'isin': isin,
                       'test_data': queried_x,
                       'dates': dates_isin,
                      }
    return final_isin_dict,df_data



def create_final_df(deployment_df, isin_data_df):
    """
    deployment_df must contain the following columns: 
        - deployment_space
        - deployment_id
        - isin
    isin_df must contain the following columns:
        - isin
        - test_data
        - dates
    """
    buf_deployment_df = deployment_df[['deployment_space', 'deployment_id', 'isin', 's3_file_path', 'model_identifier']]
    final_df = isin_data_df.merge(buf_deployment_df, on='isin', how='left')
    return final_df



def save_to_s3(final_pred_df, start_date, predictions_folder,input_df):
    """
    This function takes the final predictions dataframes that contains the following columns:
        - isin
        - deployment_id
        - deployment_space
        - dates: list of all the dates for which the predictions are done
        - predictions: list of the all predictions for the corresponding dates

    - start_date : corresponds to the start date for which we want to store the predictions
    - predictions_folder : s3 path to the folder where we want to store the predictions datewise in a csv format
    """

    # Convert final_pred_df to a final_df that contains the: isin, date, predictions
    final_df = pd.DataFrame(columns=['isin', 'date', 'predictions'])

    # For each isin append the date along with predictions to the final_df
    # final_pred_df.to_csv('lstm_pred/final_pred_aigo.csv', index=False)
    for idx in range(len(final_pred_df)):
        print(final_pred_df.iloc[idx], idx)
        buf_df = pd.DataFrame({
            'date': final_pred_df.iloc[idx]['dates'],
        })
        buf_df['isin'] = final_pred_df.iloc[idx]['isin']
        buf_df['model_identifier'] = final_pred_df.iloc[idx]['model_identifier']
        try:
            buf_df['predictions'] = final_pred_df.iloc[idx]['predictions']
        except:
            buf_df['predictions'] = None

        final_df = pd.concat([final_df, buf_df], axis=0)

    input_df = input_df.rename(columns={"Date": "date"})
    input_df = input_df[['date', 'isin']+lstm_features]
    final_df = final_df.merge(input_df, on=['date', 'isin'], how='left')
    # For each date generate a dataframe that contains the predictions of all isins, for that date
    date_list = final_df['date'].unique()
    for cur_date in tqdm(date_list, desc="Gen Dataframe for dates"):
        if cur_date < pd.to_datetime(start_date):
            print(cur_date, 'is less than start date', start_date)
        else:
            date_df = final_df[final_df['date'] == cur_date]

            # Convert the current date into proper format for saving the file
            cur_date = cur_date.strftime("%Y-%m-%d")
            
            # Store the data to S3
            if predictions_folder[-1] == '/':
                output_file_name = f'{predictions_folder}data_{cur_date}.csv'
            else:
                output_file_name = f'{predictions_folder}/data_{cur_date}.csv'
            
            s3_conn.write_advanced_as_df(date_df, bucket_name, output_file_name)
            # date_df.to_csv(f'lstm_pred/aigo_lstm_{cur_date}.csv', index=False)
        
    print('Data Saved to S3 for total of', len(date_list), 'and dates', date_list)

def get_local_prediction(isin, bucket_name, s3_file_path, test_data):
    with tempfile.TemporaryDirectory() as tempdir:
        local_model_path = f'{tempdir}/model_lstm_{isin}'
        local_tar_model_path = f'{tempdir}/model_lstm_{isin}.tar.gz'
        download_file_from_s3(local_tar_model_path, bucket_name, s3_file_path)
        unzip_tar_gz(local_tar_model_path, local_model_path)
        model_name = os.listdir(f'{tempdir}/model_lstm_{isin}')[0]
        model = load_model(f'{tempdir}/model_lstm_{isin}/{model_name}')
        
        # Do the model.compile to run the model serially and avoid tensorflow optimizations (optimizer is added for consistancy)
        model.compile(optimizer='adam',run_eagerly=True)
        
        # Check if the training data batches are present
        if test_data.shape[0] == 0:
            print('No data for ISIN', isin)
            return np.array([])
        
        # Predict the model on the test data
        output = model.predict(test_data, verbose=0)
        
        # Clear the session after every use
        K.clear_session()
        return output
        
def gen_lstm_predictions(tag, schedular, bdate):
    predictions_folder = config[tag]['lstm_predictions_folder_path']
    input_data_folder = config[tag]['processed_input_data_folder_path']
    lstm_deployment_details_path = config[tag]['lstm_deployment_details_file_path']
    print('LSTM script started for ', datetime.now())
    # Load the ISIN list and deployment details
    deployment_df = s3_conn.read_as_dataframe(bucket_name, lstm_deployment_details_path)
    deployment_df = deployment_df[deployment_df['expired_at'].isna()]
    deployment_df['s3_storage_path'] = deployment_df['s3_storage_path'].apply(lambda x: '/'.join(x.split('/')[3:]) if isinstance(x, str) else None)
    deployment_df.dropna(subset='s3_storage_path', inplace=True)
    deployment_df.rename(columns={'s3_storage_path': 's3_file_path'}, inplace=True)
    deployment_df = deployment_df[['deployment_id', 'deployment_space', 'isin', 's3_file_path', 'model_identifier']]

    print("Total Deployments:", len(deployment_df))
    print("Deployment dataframe head", deployment_df.head())

    # For the start date we look at the last date for which the data was present in AWS
    file_list = pd.DataFrame(s3_conn.get_objects_in_range(bucket_name, predictions_folder))
    file_list['date'] = file_list['Key'].apply(lambda x: x.split('/')[-1].split('.')[0].split('_')[-1])
    completed_date = file_list.sort_values(by='date').iloc[len(file_list) - 1]['date']
    # start_date = (pd.to_datetime(completed_date) + timedelta(days=1)).strftime('%Y-%m-%d')
    start_date = bdate

    # To get the today's date for the script
    # end_date = (date.today() + timedelta(days=-1)).strftime("%Y-%m-%d") #Gives the date at UTC 00:00 for today
    end_date = bdate
    
    # For safety purposes we fetch the data 90 days
    safe_start_date = (pd.to_datetime(start_date) - timedelta(days=60)).strftime("%Y-%m-%d")

    # We fetch the date 19 days in prior to the safe limit of monthly start date (for lstm model)
    final_start_date = (pd.to_datetime(safe_start_date) - timedelta(days=19)).strftime("%Y-%m-%d")

    df_isins = fetch_data_from_s3(input_data_folder,final_start_date, end_date)
    df_isins = df_isins.sort_values(by = ["date"]).reset_index(drop=True)

    # Align the variables with the existing code
    df_lstm_model_data = deployment_df
    all_isin_list = get_isin_df_excl(tag)['isin'].tolist()
    print('Total isins', len(all_isin_list))

    # Make the isin data into individual dataframes so that we can use multithreading and speed up data preprocessing
    isin_data_list = []
    for isin in tqdm(all_isin_list):
        isin_df = df_isins[df_isins["isin"] == isin].reset_index(drop=True)
        isin_data_list.append(isin_df)

        # Make a log in case no data is present for that isin
        if isin_df.shape[0] == 0:
            print('No data present for ISIN', isin)

    num_threads = 8
    final_start_date_itr = [final_start_date for i in range(len(all_isin_list))]
    # Create a ThreadPoolExecutor
    try:
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # Apply the function to each row of the DataFrame in parallel
            results =  list(tqdm(
                executor.map(preprocess_data, all_isin_list, isin_data_list, final_start_date_itr,repeat(safe_start_date)),
                total=len(all_isin_list),
                desc="Tasks",
            ))
    except Exception as e:
        raise e

    # Create the isin dataframe
    isin_input_df = pd.DataFrame()
    isin_data_df = []
    for res in results:
        if len(res) == 0:
            continue
        isin_input_df = pd.concat([isin_input_df,res[1]])
        isin_data_df.append(res[0])
    isin_data_df = pd.DataFrame(isin_data_df)
    isin_data_df = isin_data_df[isin_data_df['isin'].notna()].reset_index(drop=True)
    final_df = create_final_df(deployment_df, isin_data_df)
    # final_df.to_csv('final_df.csv', index=False)
    pred_df_list = []
    for idx in tqdm(range(len(final_df))):
        isin = final_df.iloc[idx]['isin']
        test_data = final_df.iloc[idx]['test_data']
        s3_file_path = final_df.iloc[idx]['s3_file_path']
        if pd.isna(s3_file_path):
            pred_df_list.append({'isin': isin,
                       'test_data': test_data,
                       'predictions': None})
        else:
            output_pred = get_local_prediction(isin, bucket_name, s3_file_path, test_data)
            output_pred = output_pred.squeeze()
            pred_df_list.append({'isin': isin,
                        'test_data': test_data,
                        'predictions': output_pred})

    pred_df = pd.DataFrame(pred_df_list)
    final_pred_df = pred_df.merge(final_df[['isin', 'dates', 'model_identifier']], on=['isin'], how="left")

    # Save the final predictions to the S3 folder
    save_to_s3(final_pred_df, start_date, predictions_folder,isin_input_df)
    print('**** LSTM Script Completed ****')

if __name__=="__main__":
    parser = argparse.ArgumentParser(
                    prog='lstm_pred',
                    description='Generate LSTM Predictions')
    parser.add_argument('-t', '--tag')
    parser.add_argument('-s', '--schedular')
    parser.add_argument('-bdate', '--bdate')

    args = parser.parse_args()
    tag = args.tag
    schedular = args.schedular
    if args.bdate:
        bdate = args.bdate
    else:
        bdate = (datetime.now()-BDay(1)).date().strftime("%Y-%m-%d")

    gen_lstm_predictions(tag, schedular, bdate)