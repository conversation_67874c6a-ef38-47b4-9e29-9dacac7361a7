# info_model_scripts

This repository contains scripts and utilities for data collection and inference related to information models.

## Overview

The repository is organized into different components:
- **data_collection**: Scripts for collecting, preprocessing, and storing data for daily predictions
- **prediction**: Scripts for generating daily predictions

## Data Collection

The `data_collection` folder contains all necessary scripts and configurations to collect, preprocess, and store data used for generating daily predictions.

### Components

| File | Description |
|------|-------------|
| `config.yaml` | Contains paths and API keys required for executing the scripts |
| `generate_chatgpt_names.py` | Generates `ChatGPT` names for newly added ISINs |
| `industry_data.py` | Collects `industry` sentiment and saves it as a CSV file |
| `isin_data.py` | Collects sentiment for individual `ISINs` and saves it as a CSV file |
| `lexisnexis.py` | Triggers industry and ISIN sentiment collection, merges the data and uploads to `S3` |
| `structured.py` | Collects structured data from `S&P` and uploads it to `S3` |
| `utils.py` | Helper functions used across the data collection pipeline |

### Usage

#### Command Line Usage

**LexisNexis Data Collection:**
```bash
python3 lexisnexis.py <tag>
```

**Structured Data Collection:**
```bash
python3 structured.py <tag>
```

Where `<tag>` is any tag present in the `Master Active firms` API.

**Example:**
```bash
python3 lexisnexis.py aieq
python3 structured.py aieq
```

#### Special Tags

The repository supports special combined tags for batch processing:

- `combined`: Collects data for `tier1,indsec,aifint,aieq,aigo,indt1`
- `combined2`: Collects data for `indsec,aifint,aigo,indt1`

#### Programmatic Usage

To trigger data collection from another script:

**LexisNexis Data:**
```python
from lexisnexis import collect_lexisnexis_data

# Collect data for a specific tag and date
collect_lexisnexis_data(tag="aieq", date="2023-01-01")
```

**Structured Data:**
```python
from structured import collect_structured_data

# Collect data for a specific tag and date
collect_structured_data(tag="aieq", date="2023-01-01")
```