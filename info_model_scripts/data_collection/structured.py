from utils import *

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")

with open(config_path,'r') as f:
    config = yaml.safe_load(f)

url_sp = config['snp']['url']
headers_sp = {'Authorization': config['snp']['authorization'],'Content-type': config['snp']['content_type']}

bucket_name = config['s3']['bucket_name']

def get_eps_multiple_period_type(identfier,url,headers):
    
    data = {
    "inputRequests": [
    {
      "function": "GDSP",
      "identifier": identfier,
      "mnemonic": config['snp']['eps_estimate_mnemonic'],
      "properties": {
        "periodType": 'IQ_CQ+1',
      }
    },{
      "function": "GDSP",
      "identifier": identfier,
      "mnemonic": config['snp']['eps_estimate_mnemonic'],
      "properties": {
        "periodType": 'IQ_CY+1',
      }
    },{
      "function": "GDSP",
      "identifier": identfier,
      "mnemonic": config['snp']['eps_estimate_mnemonic'],
      "properties": {
        "periodType": 'IQ_CQ+2',
      }
    },{
      "function": "GDSP",
      "identifier": identfier,
      "mnemonic": config['snp']['eps_estimate_mnemonic'],
      "properties": {
        "periodType": 'IQ_CY+2',
      }
    }
    ]
    }
    data_json = json.dumps(data)

    response = requests.post(url, data=data_json, headers=headers)
    try:
        resp_json=json.loads(response.text)
        return resp_json
    except Exception as e:
        print(e)
        return None

def get_capiq_data_as_of_date_multiple_mnemonics_for_single_company(body,string_date_ymd,url=url_sp, headers=headers_sp):
    date = datetime.strftime(datetime.strptime(string_date_ymd, '%Y-%m-%d'),'%m/%d/%Y')
    identifier, mnemonic_list_d = body
    input_list = []
    for mnemonic in mnemonic_list_d:
        body_req = {
              "function": "GDSHE",
              "identifier": f'{identifier}',
              "mnemonic": mnemonic['pnemo'],
              "properties": {
                "self.currencyID":"USD",
                'asofDate': date, # date should be mm/dd/YYYY format
              }
        }
        input_list.append(body_req)
    data = {"inputRequests" : input_list}
    data_json = json.dumps(data)
    response = requests.post(url, data=data_json, headers=headers)
    try:
        resp_json=json.loads(response.text)
        return resp_json
    except Exception as e:
        print(e)
        return None
    
def get_eps_data(isin, identifier_list):
    successful_keys = set()
    eps_res = {}
    for identifier in identifier_list:
        if not isinstance(identifier, str):
            continue
        if len(identifier) == 0:
            continue
        try:
            resp=get_eps_multiple_period_type(identifier,url_sp,headers_sp)
            if resp == None:
                print(f'No eps data using {identifier}')
                continue
            else:
                tmp=pd.json_normalize(resp['GDSSDKResponse'])
                for p in PERIOD_TYPE_LIST:
                    if p['name'] in successful_keys:
                        continue
                    try:
                        if tmp.loc[tmp['Properties.periodtype'] == p['period_type']].iloc[0,13][0]['Row'][0]=='Data Unavailable':
                            eps_res['isin']=isin
                            eps_res[p['name']]=None
                        else:
                            eps_res['isin']=isin
                            eps_res[p['name']]=float(tmp.loc[tmp['Properties.periodtype'] == p['period_type']].iloc[0,13][0]['Row'][0])
                            successful_keys.add(p['name'])
                    except Exception as e:
                        print(f"Error in getting eps data for {isin}:{e}")
                        eps_res['isin']=isin
                        eps_res[p['name']]=None

                if len(successful_keys) == 4:
                    break
        except:
            print(f'Error while using {identifier} for eps data')

    if len(successful_keys) == 0:
        print(f'No eps data for isin {isin}')
    return eps_res

def get_price_data(isin, identifier_list, date = (datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')):
    successful_keys = set()
    price_res = {}
    for identifier in identifier_list:
        if not isinstance(identifier, str):
            continue
        if len(identifier) == 0:
            continue
        try:
            body = [identifier, MNEMONICS_LIST]
            resp=get_capiq_data_as_of_date_multiple_mnemonics_for_single_company(body, date, url=url_sp, headers=headers_sp)
            if resp == None:
                print(f'No princing data using {identifier}')
                continue
            else:
                tmp=pd.json_normalize(resp['GDSSDKResponse'])

                for p in MNEMONICS_LIST:
                    if p['name'] in successful_keys:
                        continue
                    try:
                        if tmp.loc[tmp['Mnemonic'] == p['pnemo']].iloc[0,7][0]['Row'][0]=='Data Unavailable':
                            price_res['isin']=isin
                            price_res[p['name']]=None
                        else:
                            price_res['isin']=isin
                            price_res[p['name']]=float(tmp.loc[tmp['Mnemonic'] == p['pnemo']].iloc[0,7][0]['Row'][0])
                            successful_keys.add(p['name'])
                    except Exception as e:
                        print(f"Error in getting price data for {isin}:{e}")
                        price_res['isin']=isin
                        price_res[p['name']]=None
                        continue
                    
                if len(successful_keys) == 5:
                    break
        except:
            print(f'Error while using {identifier} for pricing data')
    if len(successful_keys) == 0:
        print(f'No pricing data for isin {isin}')
    return price_res

def process_isin(isin,identifier_list, date):
    res = {}
    
    # Getting the pricing data
    try:
        price_res = get_price_data(isin, identifier_list, date)
        res['open_price']=price_res['open_price']
        res['high_price']=price_res['high_price']
        res['low_price']=price_res['low_price']
        res['close_price']=price_res['close_price']
        res['volume']=price_res['volume']
    except:
        res['isin']=isin
        res['open_price']=None
        res['high_price']=None
        res['low_price']=None
        res['close_price']=None
        res['volume']=None
    
    # Getting eps data
    try:
        eps_res = get_eps_data(isin, identifier_list)
        res['isin']=isin
        res['eps_current_qr']=eps_res['eps_current_qr']
        res['eps_current_yr']=eps_res['eps_current_yr']
        res['eps_next_qr']=eps_res['eps_next_qr']
        res['eps_next_yr']=eps_res['eps_next_yr']
    except:
        res['isin']=isin
        res['eps_current_qr']=None
        res['eps_current_yr']=None
        res['eps_next_qr']=None
        res['eps_next_yr']=None

    return res

def collect_structured_data(tag,date):
    s3_conn = s3_config()
    maf = get_masterActiveFirms(tag)
    maf_rel=maf[['isin','tic','exchange','ciq_trading_id','ipodate']]
    maf_rel['s_and_p_identifier']=maf_rel["tic"]+":"+maf_rel["exchange"]

    isin_df = s3_conn.read_as_dataframe(bucket_name, config['s3']['isin_list_filepath'])
    print(f'daily run for {date}')
    new_isin_df = maf_rel[~maf_rel['isin'].isin(isin_df['isin'])]
    new_isin_list = list(new_isin_df['isin'].dropna())

    if len(new_isin_list) !=0:
        print(f'New isins added: {len(new_isin_list)}')
        isin_df = pd.concat([isin_df,new_isin_df],ignore_index=True)
        s3_conn.write_advanced_as_df(isin_df, bucket_name, config['s3']['isin_list_filepath'])
    isin_df = isin_df.drop_duplicates()

    print(len(isin_df))
    fin_list=[]
    with concurrent.futures.ProcessPoolExecutor(max_workers=100) as executor:
        futures = []
        for ind, row in isin_df.iterrows():
            try:
                isin = row['isin']
                tic_ex = row['s_and_p_identifier']
                ciq_id = row['ciq_trading_id']
                identifier_list = [tic_ex, isin, ciq_id]
                future = executor.submit(process_isin, isin, identifier_list, date)
                futures.append(future)
            except Exception as e:
                print(f'Error while processing {isin}: {e}')
        concurrent.futures.wait(futures)
        for future in futures:
            try:
                isin_dict = future.result()
                if isinstance(isin_dict, dict):
                    fin_list.append(isin_dict)
            except Exception as e:
                print(f"Error processing {e}")
                continue
    df = pd.DataFrame(fin_list)
    df['date'] = pd.to_datetime(date, format='%Y-%m-%d')
    maf_all = get_masterActiveFirms('all')
    for isin in df['isin'].unique():
        try:
            ipo_date = maf_all[maf_all['isin'] == isin]['ipodate'].iloc[0]

            if ipo_date is None:
                df.loc[df['isin'] == isin, 'age_days'] = 3650
                df.loc[df['isin'] == isin, 'age_year'] = 10
                df['age_days'] = df['age_days'].astype(int)
                df['age_year'] = df['age_year'].astype(int)
            elif len(ipo_date) == 0:
                df.loc[df['isin'] == isin, 'age_days'] = 3650
                df.loc[df['isin'] == isin, 'age_year'] = 10
                df['age_days'] = df['age_days'].astype(int)
                df['age_year'] = df['age_year'].astype(int)
            else:
                ipo_date = pd.to_datetime(ipo_date, format='%m/%d/%Y')
                df.loc[df['isin'] == isin, 'age_days'] = df.loc[df['isin'] == isin, 'date'].apply(lambda x: (x - ipo_date).days)
                df.loc[df['isin'] == isin, 'age_year'] = df.loc[df['isin'] == isin, 'date'].apply(lambda x: relativedelta(x, ipo_date).years)
                df['age_days'] = df['age_days'].astype(int)
                df['age_year'] = df['age_year'].astype(int)
        except Exception as e:
            print(f'error while calculating age for isin: {isin}')

    file_name = f"{config['s3']['daily_data_folder_path']['structured']}/{date}.csv"
    if len(df) != 0:
        try:
            s3_conn.write_advanced_as_df(df,bucket_name,file_name)
        except Exception as e:
            print(f'error while uploading to s3: {e}')

if __name__=='__main__':
    date_yesterday = (datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')
    start=datetime.now()

    parser = argparse.ArgumentParser(description="Daily Structured Data Collection Script")
    parser.add_argument('tag', type=str, help='tag for collecting data')
    args = parser.parse_args()
    tag = args.tag

    collect_structured_data(tag,date_yesterday)
    end = datetime.now()
    print(f'total time: {end-start}')