import requests
import pandas as pd
from datetime import datetime, timedelta
import time
from dateutil.relativedelta import relativedelta
import json
import concurrent.futures
import os
import yaml
import re
import pickle
import argparse
from openai import OpenAI
from eq_common_utils.utils.config.s3_config import s3_config
import shutil
import warnings
import ast
from requests_aws4auth import AWS4Auth
from opensearchpy.client import OpenSearch
from opensearchpy.connection.http_requests import RequestsHttpConnection
import io
import nltk
nltk.download('words')
from nltk.corpus import words

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")

with open(config_path,'r') as f:
    config = yaml.safe_load(f)
KW_DICT_ISIN = {'sales': ['income statement',
                        'business sales',
                        'sales metrics',
                        'revenue streams',
                        'sales projections',
                        'sales outlook',
                        'market performance',
                        'customer acquisition',
                        'top-line growth',
                        'profit margins',
                        'sales analysis',
                        'sales trends',
                        'sales forecast',
                        'annual sales',
                        'quarterly sales',
                        'sales strategy',
                        'business expansion',
                        'market share',
                        'sales figures',
                        'earnings report',
                        'profitability',
                        'financial results',
                        'sales performance',
                        'revenue growth',
                        'company sales'],
            'growth': ['Revenue Growth',
                    'Profitability Increase',
                    'Market Expansion',
                    'Earnings Growth',
                    'Sales Surge',
                    'Market Share Gain',
                    'Product Launch Success',
                    'International Expansion',
                    'Strategic Partnerships',
                    'Innovation Achievements',
                    'Operational Efficiency Improvement',
                    'Customer Base Growth',
                    'Investment Success',
                    'Financial Performance Boost',
                    'Leadership Excellence',
                    'Stock Price Appreciation',
                    'Cost Optimization',
                    'New Market Entry',
                    'Positive Executive Changes',
                    'Customer Retention Increase',
                    'Diversification Success',
                    'Research and Development Milestones',
                    'Bullish Market Sentiment',
                    'Guidance Upgrade',
                    'Streamlined Operations'],
                'merger': ['company acquisition news',
                    'merger and acquisition strategy',
                    'business consolidation',
                    'acquisition deal analysis',
                    'mergers and acquisitions trends',
                    'corporate takeover updates',
                    'strategic mergers in finance',
                    'acquisition announcement',
                    'recent M&A transactions',
                    'merger impact on stock prices',
                    'buyout negotiations',
                    'M&A market analysis',
                    'merger synergy evaluation',
                    'corporate integration process',
                    'acquisition target evaluation',
                    'cross-border M&A deals',
                    'merger and acquisition success stories',
                    'hostile takeover news',
                    'merger due diligence checklist',
                    'mergers and acquisitions legal process',
                    'financial implications of mergers',
                    'mergers and acquisitions case studies',
                    'M&A regulatory compliance',
                    'post-merger performance evaluation',
                    'mergers and acquisitions financial modeling'],
                'litigation': ['current lawsuits',
                    'legal battles',
                    'litigation news',
                    'legal disputes',
                    'company litigation',
                    'legal challenges',
                    'lawsuits filed',
                    'court cases',
                    'legal issues',
                    'litigation cases',
                    'recent lawsuits',
                    'legal troubles',
                    'business litigation',
                    'court cases involving',
                    'legal battles',
                    'lawsuits',
                    'corporate litigation updates',
                    'company facing legal action',
                    'legal disputes',
                    'lawsuits in progress',
                    'current legal battles',
                    'company lawsuit tracker',
                    'recent court cases',
                    'company litigation summary',
                    'legal challenges'],
                'innovation': ['innovation breakthroughs',
                    'corporate strategies',
                    'business solutions',
                    'tech advancements',
                    'industry-disrupting',
                    'innovation success',
                    'R&D achievements',
                    'product launches',
                    'technological advancements',
                    'emerging trends',
                    'strategic innovation',
                    'leading-edge initiatives',
                    'innovation case studies',
                    'disruptive technologies',
                    'groundbreaking developments',
                    'driving innovation',
                    'pioneering innovations',
                    'innovative practices',
                    'revolutionary breakthroughs',
                    'cutting-edge technologies',
                    'innovation spotlight',
                    'at the forefront of innovation',
                    'innovation excellence',
                    'latest breakthroughs'],
                'general': ['the']}
CHATGPT_MODEL = "gpt-3.5-turbo"
PERIOD_TYPE_LIST=[{'name':'eps_current_qr','period_type':'IQ_CQ+1'},{'name':'eps_current_yr','period_type':'IQ_CY+1'},{'name':'eps_next_qr','period_type':'IQ_CQ+2'},{'name':'eps_next_yr','period_type':'IQ_CY+2'}]
COMBINED_TAG_LIST = ['tier1','indsec','aifint','aieq','aigo','indt1']
MNEMONICS_LIST = [{'name':'open_price','pnemo':'IQ_OPENPRICE'},{'name':'high_price','pnemo':'IQ_HIGHPRICE'},{'name':'low_price','pnemo':'IQ_LOWPRICE'},{'name':'close_price','pnemo':'IQ_CLOSEPRICE'},{'name':'volume','pnemo':'IQ_VOLUME'}]

def is_bussiness_day(date_str):
    date_to_check = pd.Timestamp(date_str)
    bday = pd.offsets.BDay()
    is_business_day = bday.is_on_offset(date_to_check)
    return is_business_day

def str_list(text):
    try:
        b = ast.literal_eval(text)
        return b
    except:
        return "NA"
    
def get_masterActiveFirms(tag):
    if tag == 'combined2':
        df_list = []
        for t in ['indsec','aifint','aigo','indt1']:
            maf_json = requests.get(f"{config['master_active_firms_url']}{t}").json()
            maf_df = pd.DataFrame(maf_json["data"][f"masteractivefirms_{t}"])
            df_list.append(maf_df)
        maf_df = pd.concat(df_list,ignore_index=True)
        maf_df.reset_index(inplace=True,drop=True)
        maf_df = maf_df.drop_duplicates(subset=['isin'])

    if tag == 'combined':
        df_list = []
        for t in COMBINED_TAG_LIST:
            maf_json = requests.get(f"{config['master_active_firms_url']}{t}").json()
            maf_df = pd.DataFrame(maf_json["data"][f"masteractivefirms_{t}"])
            df_list.append(maf_df)
        maf_df = pd.concat(df_list,ignore_index=True)
        maf_df.reset_index(inplace=True,drop=True)
        maf_df = maf_df.drop_duplicates(subset=['isin'])
    else:
        maf_json = requests.get(f"{config['master_active_firms_url']}{tag}").json()
        maf_df = pd.DataFrame(maf_json['data'][f'masteractivefirms_{tag}'])
    return maf_df

def get_chatgpt_prompt(comp_name):
    prompt= f"Provide 5 alternate names for the company known as {comp_name} as generally used or likely to be used in news articles. Give your output as a Python list, without any text apart from the list itself and each keyword inside double quotes so it can be directly plugged into a function"
    return prompt

def query_string_or(kw_list):
    q_string_or='['
    for l in range(len(kw_list)):
        if l != (len(kw_list)-1):
            q_or_tmp='{"match_phrase": {"content": "'+kw_list[l]+'"}}'
            q_string_or=q_string_or+q_or_tmp+','
        else:
            q_or_tmp='{"match_phrase": {"content": "'+kw_list[l]+'"}}'
            q_string_or=q_string_or+q_or_tmp+']'
    return q_string_or

def connect_openSearch():
    aws_access_key = config['open_search']['aws_access_key']
    aws_secret_key = config['open_search']['aws_secret_key']
    region = config['open_search']['region']
    service = config['open_search']['service']
    hosts = [config['open_search']['hosts']]
    auth = AWS4Auth(aws_access_key, aws_secret_key, region, service)
    port = 443
    client = OpenSearch(
        hosts=hosts,
        port=port,
        http_auth=auth,
        connection_class=RequestsHttpConnection,
        timeout=50, 
        max_retries=5, 
        retry_on_timeout=True
     )
    return client

def get_ranges(start_date, end_date):
    # Convert the date strings to datetime objects
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)

    # Create an empty list to store monthly date ranges
    monthly_date_ranges = []

    # Loop through each month and generate the date range
    current_month = start_date
    while current_month <= end_date:
        # Calculate the last day of the current month
        last_day_of_month = current_month + pd.offsets.MonthEnd(0)
        
        if last_day_of_month <= end_date:
            # Add the date range for the current month to the list
            monthly_date_ranges.append((current_month, last_day_of_month))
        else:
            # Handle the last month if it's shorter than a full month
            monthly_date_ranges.append((current_month, end_date))
        
        # Move to the next month
        current_month = last_day_of_month + pd.offsets.Day(1)

    monthly_date_dict = {'Start_Date' : [], 'End_Date': []}
    # Print the monthly date ranges
    for start, end in monthly_date_ranges:
        monthly_date_dict['Start_Date'].append(start.strftime('%Y-%m-%d'))
        monthly_date_dict['End_Date'].append(end.strftime('%Y-%m-%d'))
    return monthly_date_dict

def get_lexisnexis_industry_query(q_string_or,start_date_yyyy_mm_dd,end_date_yyyy_mm_dd):
    query='{"_source":["id","sentiment.score","content","estimatedPublishedDate","harvestDate", "publishedDate"],"query": {"bool": {"must": [{"bool": {"should":'+q_string_or+'}},{"bool": {"should": [{"range": {"harvestDate": {"gte":"'+start_date_yyyy_mm_dd+'", "lte":"'+end_date_yyyy_mm_dd+'"}}}, {"range": {"publishedDate": {"gte":"'+start_date_yyyy_mm_dd+'", "lte":"'+end_date_yyyy_mm_dd+'"}}}, {"range": {"estimatedPublishedDate": {"gte": "'+start_date_yyyy_mm_dd+'", "lte": "'+end_date_yyyy_mm_dd+'"}}}],"minimum_should_match": 1}}]}}}'
    return query

def get_lexisnexis_isin_query(company_name, company_name1, company_name2, isin, start_date_yyyy_mm_dd, end_date_yyyy_mm_dd, general=False, q_string_or=''):
    if general:
        query = '''
                {
                "_source": ["id", "sentiment.entities", "sentiment.score", "estimatedPublishedDate", "publishedDate"],
                "query": {
                    "bool": {
                    "must": [
                        {
                        "bool": {
                            "should": [
                            {"match_phrase": {"title": "''' + company_name + '''"}},
                            {"match_phrase": {"content": "''' + company_name + '''"}},
                            {"match_phrase": {"title": "''' + company_name1 + '''"}},
                            {"match_phrase": {"content": "''' + company_name1 + '''"}},
                            {"match_phrase": {"title": "''' + company_name2 + '''"}},
                            {"match_phrase": {"content": "''' + company_name2 + '''"}},
                            {"match_phrase": {"companies.isin": "''' + isin + '''"}}
                            ]
                        }
                        },
                        {
                        "bool": {
                            "should": [
                            {"range": {"publishedDate": {"gte": "''' + start_date_yyyy_mm_dd + '''", "lte": "''' + end_date_yyyy_mm_dd + '''"}}},
                            {"range": {"estimatedPublishedDate": {"gte": "''' + start_date_yyyy_mm_dd + '''", "lte": "''' + end_date_yyyy_mm_dd + '''"}}}
                            ],
                            "minimum_should_match": 1
                        }
                        }
                    ]
                    }
                }
                }
                '''
    else:
        query = '''
                {
                "_source": ["id", "sentiment.entities", "sentiment.score", "estimatedPublishedDate", "publishedDate"],
                "query": {
                    "bool": {
                    "must": [
                        {
                        "bool": {
                            "should": [
                            {"match_phrase": {"title": "''' + company_name + '''"}},
                            {"match_phrase": {"content": "''' + company_name + '''"}},
                            {"match_phrase": {"title": "''' + company_name1 + '''"}},
                            {"match_phrase": {"content": "''' + company_name1 + '''"}},
                            {"match_phrase": {"title": "''' + company_name2 + '''"}},
                            {"match_phrase": {"content": "''' + company_name2 + '''"}},
                            {"match_phrase": {"companies.isin": "''' + isin + '''"}}
                            ]
                        }
                        },
                        {
                        "bool": {
                            "should": ''' + q_string_or + '''
                        }
                        },
                        {
                        "bool": {
                            "should": [
                            {"range": {"publishedDate": {"gte": "''' + start_date_yyyy_mm_dd + '''", "lte": "''' + end_date_yyyy_mm_dd + '''"}}},
                            {"range": {"estimatedPublishedDate": {"gte": "''' + start_date_yyyy_mm_dd + '''", "lte": "''' + end_date_yyyy_mm_dd + '''"}}}
                            ],
                            "minimum_should_match": 1
                        }
                        }
                    ]
                    }
                }
                }
                '''