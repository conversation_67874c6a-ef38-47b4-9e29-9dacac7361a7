"""
Information Model Scripts - LexisNexis Data Collection

Refactored LexisNexis data collection with improved error handling,
logging, and standardized utilities usage.
"""

import sys
import os
import pickle
from pathlib import Path
from typing import List, Dict, Any, Optional
import pandas as pd

# Import shared utilities package
from shared_utils import (
    load_config, validate_common_config, create_model_logger, create_error_handler,
    ErrorSeverity, DataError, S3Error, ErrorContext, safe_execute
)

# Import existing modules
from industry_data import main_industry_run
from isin_data import daily_run
from utils import *


class LexisNexisDataCollector:
    """LexisNexis data collection orchestrator with improved error handling."""

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize LexisNexis Data Collector.

        Args:
            config_path: Path to configuration file
        """
        # Setup script directory and configuration
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        config_file = config_path or os.path.join(self.script_dir, "config.yaml")
        self.config = load_config(config_file)
        self._validate_config()

        # Setup utilities
        self.s3_manager = create_s3_manager()

        # Configuration shortcuts
        self.bucket_name = self.config.get('s3.bucket_name')
        self.pickle_files_path = self.config.get('s3.pickle_files_path')

        # Setup logging (will be updated per run)
        self.logger = None
        self.error_handler = None

    def _validate_config(self) -> None:
        """Validate configuration requirements."""
        required_keys = [
            's3.bucket_name',
            's3.pickle_files_path'
        ]

        if not self.config.validate_required_keys(required_keys):
            raise ConfigurationError("Missing required configuration keys")

    def _setup_run_context(self, tag: str, date: str) -> None:
        """Setup logging and error handling for a specific run."""
        self.logger = create_model_logger(
            model_name='info_model_lexisnexis',
            tag=tag,
            scheduler='daily',
            run_date=date,
            log_dir=os.path.join(self.script_dir, 'logs')
        )

        self.error_handler = create_error_handler(self.logger.get_logger())

    def merge_data(self, tag: str, date: str) -> bool:
        """
        Merge industry and ISIN sentiment data.

        Args:
            tag: Data tag
            date: Processing date

        Returns:
            True if successful, False otherwise
        """
        try:
            self._setup_run_context(tag, date)
            self.logger.info(f"Starting data merge for tag: {tag}, date: {date}")

            # Load ISIN list from S3
            with ErrorContext(self.error_handler, "load_isin_list"):
                isins = self._load_isin_list(tag)

            if not isins:
                raise DataError(f"No ISINs found for tag: {tag}", ErrorSeverity.HIGH)

            # Filter for combined2 tag
            if tag == 'combined2':
                isins = self._filter_combined2_isins(isins)

            # Setup folder paths
            ind_code_folder = os.path.join(self.script_dir, "industry_data")
            isin_folder = os.path.join(self.script_dir, tag, "isin")
            merged_folder = os.path.join(self.script_dir, tag, "merged")

            # Create merged folder if it doesn't exist
            os.makedirs(merged_folder, exist_ok=True)

            # Process each ISIN
            successful_merges = 0
            failed_merges = 0

            for i, isin_data in enumerate(isins):
                try:
                    isin = isin_data['isin']
                    ind_code = isin_data['ind_code']

                    if self._merge_single_isin(isin, ind_code, date, ind_code_folder, isin_folder, merged_folder):
                        successful_merges += 1
                    else:
                        failed_merges += 1

                except Exception as e:
                    failed_merges += 1
                    self.error_handler.handle_error(
                        DataError(f"Failed to merge ISIN {isin_data.get('isin', 'unknown')}: {e}", ErrorSeverity.MEDIUM),
                        context={"isin_data": isin_data, "index": i}
                    )

            # Log summary
            self.logger.log_data_quality(
                total_records=len(isins),
                processed_records=successful_merges,
                failed_records=failed_merges
            )

            success = failed_merges == 0
            self.logger.log_model_end(success, {
                'successful_merges': successful_merges,
                'failed_merges': failed_merges,
                'total_isins': len(isins)
            })

            return success

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    DataError(f"Data merge failed: {e}", ErrorSeverity.HIGH),
                    context={"tag": tag, "date": date}
                )
            return False

    def _load_isin_list(self, tag: str) -> List[Dict[str, Any]]:
        """Load ISIN list from S3."""
        try:
            pickle_path = f'{self.pickle_files_path}/{tag}_list.pkl'

            # Use s3_config for backward compatibility
            s3_conn = s3_config()
            file_obj, _ = s3_conn.read_as_stream(self.bucket_name, pickle_path)
            isins = pickle.loads(file_obj)

            self.logger.info(f"Loaded {len(isins)} ISINs for tag: {tag}")
            return isins

        except Exception as e:
            raise S3Error(f"Failed to load ISIN list: {e}", ErrorSeverity.HIGH)

    def _filter_combined2_isins(self, isins: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter ISINs for combined2 tag."""
        try:
            isin_df = pd.DataFrame(isins)
            aieq_isin = list(get_masterActiveFirms('aieq')['isin'])
            isin_df = isin_df[~isin_df['isin'].isin(aieq_isin)]
            filtered_isins = isin_df.to_dict('records')

            self.logger.info(f"Filtered combined2 ISINs: {len(isins)} -> {len(filtered_isins)}")
            return filtered_isins

        except Exception as e:
            raise DataError(f"Failed to filter combined2 ISINs: {e}", ErrorSeverity.MEDIUM)

    def _merge_single_isin(self,
                          isin: str,
                          ind_code: str,
                          date: str,
                          ind_code_folder: str,
                          isin_folder: str,
                          merged_folder: str) -> bool:
        """Merge data for a single ISIN."""
        try:
            # Read industry code file
            ind_code_file_path = os.path.join(ind_code_folder, f"{ind_code}.csv")
            if not os.path.exists(ind_code_file_path):
                self.logger.warning(f"Industry code file not found: {ind_code_file_path}")
                return False

            ind_code_df = pd.read_csv(ind_code_file_path)
            ind_code_df['date'] = date

            # Read ISIN file
            isin_file_path = os.path.join(isin_folder, f"{isin}.csv")
            if not os.path.exists(isin_file_path):
                self.logger.warning(f"ISIN file not found: {isin_file_path}")
                return False

            isin_df = pd.read_csv(isin_file_path)
            isin_df['date'] = date

            # Merge data
            merged_df = pd.merge(ind_code_df, isin_df, on='date', how='outer').fillna(0)

            # Save merged data
            merged_file_path = os.path.join(merged_folder, f'{ind_code}_{isin}.csv')
            merged_df.to_csv(merged_file_path, index=False)

            self.logger.debug(f"Successfully merged data for ISIN: {isin}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to merge ISIN {isin}: {e}")
            return False
            count_isins += 1
        except Exception as e:
            print(f"Error processing ind_code {ind_code} and isin {isin}: {str(e)}")
            continue

    print(count_isins)

    # The directory where your CSV files are stored
    directory_path = f'{script_dir}/{tag}/merged'

    # List to hold data from all files
    all_data = []

    # Loop through each file in the directory
    for filename in os.listdir(directory_path):
        if filename.endswith('.csv'):
            file_path = os.path.join(directory_path, filename)
            # Read the CSV file
            df = pd.read_csv(file_path)

            # Assuming each file has only one row and you want to include it if it's not empty
            if not df.empty:
                all_data.append(df)

    # Concatenate all dataframes in the list
    final_df = pd.concat(all_data, ignore_index=True)
    # Fill NA/NaN values with 0
    final_df.fillna(0, inplace=True)
    # Assuming the date is the same in all files and you want to name the file after the first row's date
    date_string = final_df.iloc[0]['date']  # Gets the date from the first row
    output_filename = f'{date_string}.csv'
    output_path = f'{script_dir}/{tag}/merged_s3'
    # Save the combined dataframe to a new CSV file
    output_path = os.path.join(output_path, output_filename)
    s3_file_name = f"{config['s3']['daily_data_folder_path']['unstructured']}/{date_string}.csv"

    
    if tag=='combined2':
        try:
            aieq_df = s3_conn.read_as_dataframe(bucket_name,s3_file_name)
            final_df = pd.concat([aieq_df,final_df],ignore_index=True)
        except Exception as e:
            print('aieq file not available')

    final_df.drop_duplicates(subset=['isin'],inplace=True)
    final_df.reset_index(drop=True,inplace=True)
    final_df.to_csv(output_path, index=False)
    s3_conn.write_advanced_as_df(final_df,bucket_name,s3_file_name)
    print(f'Combined file saved as {output_filename}')

def create_temp_dirs(tag):
    os.makedirs(f"{script_dir}/industry_data")
    os.makedirs(f"{script_dir}/{tag}/isin")
    os.makedirs(f"{script_dir}/{tag}/merged")
    os.makedirs(f"{script_dir}/{tag}/merged_s3")

def delete_temp_dirs(tag):
    shutil.rmtree(f"{script_dir}/industry_data")
    shutil.rmtree(f"{script_dir}/{tag}")

def collect_lexisnexis_data(tag,date):

    start_time=datetime.now()
    print(f'Daily run started for {date}')
    create_temp_dirs(tag)
    # first run industry file, ensure that industry_daily_run script is in the same folder
    main_industry_run(date)
    print(f'Collecting data for tag {tag}')
    daily_run(tag,date)
    merge_data(tag,date)
    delete_temp_dirs(tag)
    end_time = datetime.now()
    print(f'Total daily run time: {(end_time-start_time)}')

if __name__=='__main__':
    date_yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    parser = argparse.ArgumentParser(description="Daily Sentiment Data Collection Script")
    parser.add_argument('tag', type=str, help='tag for collecting data')
    args = parser.parse_args()
    tag = args.tag

    collect_lexisnexis_data(tag,date_yesterday)
