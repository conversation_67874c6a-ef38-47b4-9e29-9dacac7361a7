from utils import *
# Ignore all warnings
warnings.filterwarnings("ignore")

url_sp = config['snp']['url']
headers_sp = {'Authorization': config['snp']['authorization'],'Content-type': config['snp']['content_type']}

def get_name_from_isin(isin):
    data = {'inputRequests':[{'function':"GDSP",'identifier':isin,'mnemonic':config['snp']['company_name_mnemonic'],'properties':{}}]}
    data_json = json.dumps(data)

    response = requests.post(url_sp, data=data_json, headers=headers_sp)
    response_json=json.loads(response.text)
    if len(response_json['GDSSDKResponse'][0]['ErrMsg']) != 0:
        print("Got Error in reponse: "+response_json['GDSSDKResponse'][0]['ErrMsg'])
        return None

    if(response_json['GDSSDKResponse'][0]['Rows'][0]['Row'][0]=='Data Unavailable'):
        print("No isin or name available for  "+isin)
        return None
    else:
        data1 = response_json['GDSSDKResponse'][0]['Rows'][0]['Row'][0]
        return data1

# Function to generate variations using GPT-3
def generate_variations(comp_name):
    
    client = OpenAI(api_key=config['openai_api_key'])
    prompt= get_chatgpt_prompt(comp_name)
    messages = [{"role": "user", "content": prompt}]
    
    response = client.chat.completions.create(
        model=CHATGPT_MODEL,
        messages=messages,
        temperature=0
    )
    return response.choices[0].message.content

def modify_company_names(companies_list):
    for company in companies_list:
        if len(company['chatgpt_names']) <= 1:
            company_name = company['company_listed_name']

            # Remove the period in "Inc." and create another version without "Inc" or "Inc."
            name_without_period = company_name.replace("Inc.", "Inc")
            if name_without_period not in company['chatgpt_names']:
                company['chatgpt_names'].append(name_without_period)
            
            # Create another version without "Inc" or "Inc."
            if " Inc" in name_without_period:
                name_without_inc = name_without_period.replace(" Inc", "")
            elif " Inc." in company_name:
                name_without_inc = company_name.replace(" Inc.", "")
            else:
                name_without_inc = company_name

            if name_without_inc not in company['chatgpt_names']:
                company['chatgpt_names'].append(name_without_inc)
        
        # After modifications, ensure at least two names in 'chatgpt_names'
        if len(company['chatgpt_names']) < 2 and company['chatgpt_names']:
            company['chatgpt_names'].append(company['chatgpt_names'][0])
    return companies_list

def generate_chatGPT_names(isins):
    print(f'Generating ChatGPT names for {len(isins)} isins')
    chatgpt = []
    maf = get_masterActiveFirms('all')
    for i in range(len(isins)):
            d = {}
            # code to get ticker name listing from US
            isin = isins[i]
            # List of ISIN company name variations
            company_name_from_isin = get_name_from_isin(isin)
            # Generate ISIN company name variations
            res = generate_variations(company_name_from_isin)
            dictionary_words = set(words.words())
            chatgpt_keywords = ast.literal_eval(res)
            generated_variations = [word for word in chatgpt_keywords if word not in dictionary_words]
            generated_variations.append(company_name_from_isin)
            print(generated_variations)
            # generated_variations.append(company_name1)
            d['isin'] =  isin
            d['company_listed_name'] = company_name_from_isin
            d['chatgpt_names'] = generated_variations
            # Remove names containing special characters from the 'chatgpt_names' list
            d['chatgpt_names'] = [name for name in d['chatgpt_names'] if isinstance(name, str) and not re.search(r'[^A-Za-z0-9\s]', name)]
            d['ind_code'] = maf[maf['isin'] == isin]['ind_code'].unique()[0]
            print(d)
            chatgpt.append(d)
            print(i)
    
    chatgpt = modify_company_names(chatgpt)
    return chatgpt

if __name__ == '__main__':
    indt1 = get_masterActiveFirms('indt1')
    indeq = get_masterActiveFirms('indeq')
    indt1_isins = indt1['isin'].to_list()
    indeq = indeq[~indeq['isin'].isin(indt1_isins)]
    indeq = indeq.sample(n=20)
    isins = indeq['isin'].to_list()

    print(len(indeq))
    chatgpt = generate_chatGPT_names(isins)
    with open('path/to/pickle/file.pkl','wb') as f:
        pickle.dump(chatgpt,f)