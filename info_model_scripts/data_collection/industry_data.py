from utils import *

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")

with open(config_path,'r') as f:
    config = yaml.safe_load(f)

# Ignore all warnings
warnings.filterwarnings("ignore")
bucket_name = config['s3']['bucket_name']
pickle_files_path = config['s3']['pickle_files_path']

def get_date_only(df):
    df['date'] = 0
    for i in range(len(df)):
        if '_source.estimatedPublishedDate' in df.columns and pd.notna(df['_source.estimatedPublishedDate'][i]):
            original_date_str = df['_source.estimatedPublishedDate'][i]

            # Parse the original date string
            original_date = datetime.strptime(str(original_date_str), "%Y-%m-%dT%H:%M:%SZ")

            # Format the date as "YYYY-MM-DD"
            df.loc[i, 'date'] = original_date.strftime("%Y-%m-%d")
        elif '_source.harvestDate' in df.columns:
            original_date_str = df['_source.harvestDate'][i]

            # Parse the original date string
            original_date = datetime.strptime(str(original_date_str), "%Y-%m-%dT%H:%M:%SZ")

            # Format the date as "YYYY-MM-DD"
            df.loc[i, 'date'] = original_date.strftime("%Y-%m-%d")
        else:
            pass
    return df

# final function to call to generate final required dataframe
def get_final_file(ind_code,start_date_yyyy_mm_dd:str,end_date_yyyy_mm_dd:str,kw_list:list,date) -> pd.DataFrame:
    year = date[0:4]
    client_os=connect_openSearch()
    # Initialize the merged_df with None
    merged_df = None

    q_string_or = query_string_or(kw_list)

    query = get_lexisnexis_industry_query(q_string_or,start_date_yyyy_mm_dd,end_date_yyyy_mm_dd)
    index= f"ln_data_{year}"
    result123 = client_os.search(index=index, body=query, size=2000)
   
    df_tmp = pd.json_normalize(result123['hits']['hits'])

    if '_source.estimatedPublishedDate' in df_tmp.columns:
        df_tmp = df_tmp[df_tmp['_source.estimatedPublishedDate'] != 'nan']
        df_tmp = get_date_only(df_tmp)
    else:
        df_tmp = get_date_only(df_tmp)


    # Group by 'Date'
    grouped = df_tmp.groupby('date')
    
    def custom_aggregation(group):
        if '_source.sentiment.score' in group.columns:

            # Convert the 'sentiment' column to numeric (float)
            group['_source.sentiment.score'] = pd.to_numeric(group['_source.sentiment.score'], errors='coerce')

            # Separate positive and negative sentiment values into two DataFrames
            positive_sentiment = group[group['_source.sentiment.score'] > 0]['_source.sentiment.score'].reset_index(drop=True)
            negetive_sentiment = group[group['_source.sentiment.score'] < 0]['_source.sentiment.score'].reset_index(drop=True)
            overall_pos_mean_value = positive_sentiment.mean()
            overall_neg_mean_value = negetive_sentiment.mean()
            
            return pd.Series({
                'date': group['date'].iloc[0],
                'ind_code': ind_code,
                f'source_id_ind': list(set(group['_source.id'].to_list())),
                f'pos_senti_ind': overall_pos_mean_value,
                f'neg_senti_ind': overall_neg_mean_value
            })
        else:
            return pd.Series({
                'date': group['date'].iloc[0],
                'ind_code': ind_code,
                f'source_id_ind': 0,
                f'pos_senti_ind': 0,
                f'neg_senti_ind': 0
            })
        
    # Apply the custom aggregation function to each group
    merged_df = grouped.apply(custom_aggregation).reset_index(drop=True)
    return merged_df

def get_industry_data(i,ind_list,date,path):
        ind_code = ind_list[i]['ind_code']

        if str(ind_code):
            kw_list = ind_list[i]['ind_name_list']
            kw_list = [item.lower() for item in kw_list]
            monthly_date_dict = get_ranges(date, date)

            # Initialse a final_df
            final_df = pd.DataFrame()

            # Now we need to run for each month seperately and concat the dataframe
            for start, end in zip(monthly_date_dict['Start_Date'], monthly_date_dict['End_Date']):

                # Create a Dataframe which containes all dates of particular month
                all_dates_df = pd.DataFrame(pd.date_range(start, end), columns=['date'])
                all_dates_df['ind_code'] = ind_code
                merged_df = get_final_file(ind_code, start, end, kw_list,date)
                if 'date' in merged_df.columns and 'ind_code' in merged_df.columns:
                    merged_df['date'] = pd.to_datetime(merged_df['date'])
                    merged_df = all_dates_df.merge(merged_df, on=['date', 'ind_code'], how='left')
                else:
                    merged_df = all_dates_df

                # Now need to concat monthly data
                final_df = pd.concat([final_df, merged_df], axis=0).fillna(0).reset_index(drop=True)
                final_df.to_csv(f'{path}/{ind_code}.csv', index=False)            
        else:
            pass

def main_industry_run(date):
    path = f'{script_dir}/industry_data'
    s3_conn = s3_config()

    # Load the list back from the file
    file_obj, _ = s3_conn.read_as_stream(bucket_name, f'{pickle_files_path}/industry_kw.pkl')
    ind_list = pickle.loads(file_obj)
    
    start_time = time.time()
    with concurrent.futures.ProcessPoolExecutor(max_workers=50) as executor:
        futures = []
        for i in range(len(ind_list)):
            future = executor.submit(get_industry_data, i,ind_list,date,path)
            futures.append(future)
        concurrent.futures.wait(futures)
    end_time = time.time()
    runtime = end_time - start_time
    print(f"Industry data collection runtime: {runtime:.6f} seconds")

if __name__ == '__main__':
    date_yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    main_industry_run(date_yesterday)