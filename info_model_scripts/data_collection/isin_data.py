from generate_chatgpt_names import generate_chatGPT_names
from utils import *

script_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(script_dir, "config.yaml")

with open(config_path,'r') as f:
    config = yaml.safe_load(f)

bucket_name = config['s3']['bucket_name']
pickle_files_path = config['s3']['pickle_files_path']
    
def get_final_file(isin:str,company_name: str,company_name1:str,company_name2:str,start_date_yyyy_mm_dd:str,end_date_yyyy_mm_dd:str,key,value,generated_variations:list,date) -> pd.DataFrame:
    client_os = connect_openSearch()
    kw_list = value.copy()
    year = start_date_yyyy_mm_dd[0:4]
    q_string_or = query_string_or(kw_list)
    if key != 'general':
        query = get_lexisnexis_isin_query(company_name,company_name1,company_name2,isin,start_date_yyyy_mm_dd,end_date_yyyy_mm_dd,general=False,q_string_or=q_string_or)
    else:
        query = get_lexisnexis_isin_query(company_name,company_name1,company_name2,isin,start_date_yyyy_mm_dd,end_date_yyyy_mm_dd,general=True,q_string_or=q_string_or)

    index1= f"ln_data_{year}"
    result123 = client_os.search(index=index1, body=query, size=200)
    if not result123['hits']['hits']:
        final_df = pd.DataFrame({
            'date': end_date_yyyy_mm_dd,
            'isin': isin,
            f'source_id_{key}': [0],
            f'overall_pos_sentiment_{key}': 0.0,
            f'overall_neg_sentiment_{key}': 0.0,
            f'pos_sentiment_{key}': 0.0,
            f'neg_sentiment_{key}': 0.0,
            f'average_mentions_{key}': 0.0,
            f'average_evidence_{key}': 0.0
        })
        return final_df
    
    df_tmp = pd.json_normalize(result123['hits']['hits'])
    if '_source.sentiment.score' not in df_tmp.columns:
        final_df = pd.DataFrame({
            'date': end_date_yyyy_mm_dd,
            'isin': isin,
            f'source_id_{key}': [0],
            f'overall_pos_sentiment_{key}': 0.0,
            f'overall_neg_sentiment_{key}': 0.0,
            f'pos_sentiment_{key}': 0.0,
            f'neg_sentiment_{key}': 0.0,
            f'average_mentions_{key}': 0.0,
            f'average_evidence_{key}': 0.0
        })
        return final_df
    df_tmp['date'] = date

    if "_source.sentiment.entities" in df_tmp.columns:
        df_tmp["_source.sentiment.entities"] = df_tmp["_source.sentiment.entities"].astype("string")
        # the below code to convert string into list using str_list custom function
        df_tmp['senti_details'] = df_tmp['_source.sentiment.entities'].apply(lambda x: str_list(x))
    else:
        pass
        
    # Group by 'Date'
    grouped = df_tmp.groupby('date')

    def custom_aggregation(group):
        
        # Convert the 'sentiment' column to numeric (float)
        group['_source.sentiment.score'] = pd.to_numeric(group['_source.sentiment.score'], errors='coerce')
        
        # Separate positive and negative sentiment values into two DataFrames
        positive_sentiment = group[group['_source.sentiment.score'] > 0]['_source.sentiment.score'].reset_index(drop=True)
        negetive_sentiment = group[group['_source.sentiment.score'] < 0]['_source.sentiment.score'].reset_index(drop=True)
        
        overall_pos_mean_value = positive_sentiment.mean()
        overall_neg_mean_value = negetive_sentiment.mean()
        if "_source.sentiment.entities" in group.columns:
            senti_list_tmp = []
            for g in range(len(group)):
                senti_list_tmp.extend(group['senti_details'].iloc[g])
            
            # Code to extract only company types
            comp_list = [item for item in senti_list_tmp if 'type' in item and item['type'] == 'Company']
            
            # List to store matching dictionaries
            only_ISIN_data_list = [data for data in comp_list if data['value'].lower() in generated_variations]

            # Initialize variables for average_mentions and average_evidence
            pos_sentiment = 0.0
            neg_sentiment = 0.0
            average_mentions = 0.0
            average_evidence = 0.0
            
            for item in only_ISIN_data_list:
                if 'mentions' in item:
                    average_mentions += float(item['mentions'])
                if 'evidence' in item:
                    average_evidence += float(item['evidence'])
                if 'score' in item:
                    if float(item['score']) >= 0.0:
                        pos_sentiment += float(item['score'])
                    else:
                        neg_sentiment += float(item['score'])
            
            n = len(only_ISIN_data_list)
            if n != 0:
                return pd.Series({
                    'date': group['date'].iloc[0],
                    'isin': isin,
                    f'source_id_{key}': list(set(group['_source.id'].to_list())),
                    f'overall_pos_sentiment_{key}': overall_pos_mean_value,
                    f'overall_neg_sentiment_{key}': overall_neg_mean_value,
                    f'pos_sentiment_{key}': pos_sentiment/n,
                    f'neg_sentiment_{key}': neg_sentiment/n,
                    f'average_mentions_{key}': average_mentions/n,
                    f'average_evidence_{key}': average_evidence/n
                })
            else:
                return pd.Series({
                    'date': group['date'].iloc[0],
                    'isin': isin,
                    f'source_id_{key}': list(set(group['_source.id'].to_list())),
                    f'overall_pos_sentiment_{key}': overall_pos_mean_value,
                    f'overall_neg_sentiment_{key}': overall_neg_mean_value,
                    f'pos_sentiment_{key}': 0.0,
                    f'neg_sentiment_{key}': 0.0,
                    f'average_mentions_{key}': 0.0,
                    f'average_evidence_{key}': 0.0
                })
        else:
            return pd.Series({
                    'date': group['date'].iloc[0],
                    'isin': isin,
                    f'source_id_{key}': list(set(group['_source.id'].to_list())),
                    f'overall_pos_sentiment_{key}': overall_pos_mean_value,
                    f'overall_neg_sentiment_{key}': overall_neg_mean_value,
                    f'pos_sentiment_{key}': 0.0,
                    f'neg_sentiment_{key}': 0.0,
                    f'average_mentions_{key}': 0.0,
                    f'average_evidence_{key}': 0.0
                })
    # Apply the custom aggregation function to each group
    final_df = grouped.apply(custom_aggregation).reset_index(drop=True)

    return final_df

def process_isin(tag,isin,kw_dict_isin,company_name, company_name1, company_name2, start_date_yyyy_mm_dd, end_date_yyyy_mm_dd, generated_variations,date):

    final= pd.DataFrame()
    for key, value in kw_dict_isin.items():
        df= get_final_file(isin, company_name, company_name1, company_name2, start_date_yyyy_mm_dd, end_date_yyyy_mm_dd, key, value, generated_variations,date)
        if 'isin' in df.columns:
            final = pd.concat([final, df.set_index(['date', 'isin'])], axis=1)

        else:
            print(isin)

    final.to_csv(f'{script_dir}/{tag}/isin/{isin}.csv')
    return final

def daily_run(tag,date):
    s3_conn = s3_config()
    file_obj, _ = s3_conn.read_as_stream(bucket_name, f'{pickle_files_path}/{tag}_list.pkl')
    isins = pickle.loads(file_obj)
    isin_pkl = list(pd.DataFrame(isins)['isin'].dropna())

    maf = get_masterActiveFirms(tag)
    new_isins = list(maf[~maf['isin'].isin(isin_pkl)]['isin'].dropna())
    if len(new_isins) != 0:
        print(f'New isins added: {len(new_isins)}')
        generated_names_for_new_isins = generate_chatGPT_names(new_isins)
        print(f'Generated data for {len(generated_names_for_new_isins)} isins')
        isins.extend(generated_names_for_new_isins)
        buffer = io.BytesIO()
        pickle.dump(isins, buffer)
        buffer.seek(0)

        # Upload to S3
        s3_conn.upload_binary_file(binary_file=buffer,bucket=bucket_name,path=f'{pickle_files_path}/{tag}_list.pkl')
    
    start_date_yyyy_mm_dd=date
    end_date_yyyy_mm_dd=date
    print(start_date_yyyy_mm_dd)
    if tag == 'combined2':
        isin_df = pd.DataFrame(isins)
        aieq_isin = list(get_masterActiveFirms('aieq')['isin'])
        isin_df = isin_df[~isin_df['isin'].isin(aieq_isin)]
        isins = isin_df.to_dict('records')
    count = 0
    start_time = time.time()
    print(len(isins))
    isin_list = list(get_masterActiveFirms(tag)['isin'])
    with concurrent.futures.ProcessPoolExecutor(max_workers=50) as executor:
        futures = []
        for i in range(len(isins)):
            try:
                isin = isins[i]['isin']
                print(isin)

                company_name1 = isins[i]['chatgpt_names'][0]
                company_name2 = isins[i]['chatgpt_names'][1]

                if isinstance(company_name1, str):
                    company_name = isins[i]['company_listed_name']
                    generated_variations = isins[i]['chatgpt_names']
                    generated_variations = [item.lower() for item in generated_variations]

                    future = executor.submit(process_isin,tag, isin,KW_DICT_ISIN,company_name, company_name1, company_name2, start_date_yyyy_mm_dd, end_date_yyyy_mm_dd, generated_variations,date)
                    futures.append(future)

                    count += 1
                    print(count)

            except Exception as e:
                print(f"{count} Error processing {isin}: {e}")
                continue
        concurrent.futures.wait(futures)

    # Calculate and print the runtime
    end_time = time.time()
    runtime = end_time - start_time
    print(f"Function runtime: {runtime:.6f} seconds")

    
if __name__ == '__main__':
    tag = 'combined2'
    processed_isin = []
    date_yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    daily_run(tag,date_yesterday)