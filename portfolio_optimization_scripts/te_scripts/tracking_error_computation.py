from imports import *

from core import *
import boto3
import requests
import copy
import threading
import numpy as np
import pandas as pd
from io import BytesIO
from functools import reduce
from botocore.client import BaseClient
from datetime import date, datetime, timedelta
from pandas import json_normalize

te_data_bucket = "micro-ops-output"
te_data_prefix = "test/tracking-error-dump/data/closeprices/"
te_strategy_prefix = "test/tracking-error-dump/TE_Application/out_strategy/"
base_index_prefix = "test/tracking-error-dump/SentPortfolio/"

snp_bucket = "index-build"
snp_solactive_prefix = "snp_solactive/"

s3_aws: BaseClient = boto3.client('s3', aws_access_key_id="********************",
                                  aws_secret_access_key="bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/")
s3 = boto3.resource("s3", region_name='us-east-1', aws_access_key_id="********************",
                    aws_secret_access_key="bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/")


def get_universe(benchmark: pd.DataFrame, base_portfolio: pd.DataFrame):
    
    teu_set = list(set(benchmark['ISIN'].tolist() + base_portfolio['ISIN'].tolist()))
    return teu_set

def get_stock_data(api_url):
    resp = requests.get(api_url).json()
    if "stocks" in resp["data"]:
        dataframe = pd.DataFrame.from_dict(json_normalize(resp["data"]["stocks"]))
        dataframe = dataframe[['date', 'adj_close', 'isin']]
        return dataframe
    
    dataframe = pd.DataFrame(columns=["date", "adj_close", "isin"])
    return dataframe

def get_data_es(firm, lock, dataframes):
    object_key = te_data_prefix + f"{firm['isin']}.csv"
    
    with lock:
        try:
            dataframe = s3_reader.read_as_dataframe(te_data_bucket, object_key)
        except:
            
            startDate = "2009-01-01"
            endDate = date.today().strftime("%Y-%m-%d")
            api_url = "http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin=" + firm[
                        'isin'] + "&startDate=" + str(startDate) + "&endDate=" + str(endDate) + "&countrycode=" + firm[
                                'country_code']
            dataframe = get_stock_data(api_url)
            s3_writer.write_as_dataframe(dataframe, te_data_bucket, object_key)
            
        if len(dataframe):
            dataframe["actual-daily-return"] = dataframe["adj_close"].pct_change(periods=1)
            dataframe = dataframe[["date", "actual-daily-return"]]
        else:
            dataframe = dataframe[["date"]]
            dataframe["actual-daily-return"] = None
        
        dataframe.rename(columns={"actual-daily-return": firm["isin"]}, inplace=True)
        dataframes[firm['isin']] = dataframe
        
def collect_data(masterfirms):
    
    thread_track = []
    readers = []
    
    lock = threading.Lock()
    dataframes = {}
    for i, firm in enumerate(masterfirms):
        thread_track.append(threading.Thread(target=lambda: get_data_es(firm, lock, dataframes))) 
        thread_track[i].start()
    
    for i in range(len(masterfirms)):
        thread_track[i].join()
        
    dfs = []
    for firm, dataframe in dataframes.items():
        dfs.append(dataframe)
    
    dataframe = reduce(
        lambda left, right: pd.merge(left, right, on=["date"], how="outer", suffixes=('_left', '_right')),
        dfs
    )
    dataframe["date"] = pd.to_datetime(dataframe["date"]).dt.strftime('%Y-%m-%d')
    dataframe = dataframe.sort_values(by="date").reset_index(drop=True).fillna(0.0)
    return dataframe

def create_cov(masterfirms, sDate, eDate):
    dataframe = collect_data(masterfirms)
    dataframe = dataframe[dataframe["date"] <= eDate].reset_index(drop=True)
    dataframe = dataframe[dataframe["date"] >= sDate].reset_index(drop=True)
    dataframe = dataframe.iloc[-126:].reset_index(drop=True)
    cov_df = dataframe.drop(columns=["date"]).cov()
    cov_m = cov_df.values
    return 252 * cov_m

def create_te_function(masterfirms, sDate, eDate):
    cov_m = create_cov(masterfirms, sDate, eDate)
    
    def te(vec):
        temp = cov_m.dot(vec)
        return np.sqrt(temp.dot(vec))
    return te

def optimize(benchmark, base_portfolio, sDate, eDate, te_threshold, tf=0.02):
    teu_universe = get_universe(benchmark, base_portfolio)
    teu_df = pd.DataFrame({"isin": teu_universe})
    
    ccode_map = {}
    benchmark.apply(lambda row: ccode_map.update({row['ISIN'] : row["country_code"]}), axis=1)
    base_portfolio.apply(lambda row: ccode_map.update({row['ISIN'] : row["country_code"]}), axis=1)
    
    teu_df["country_code"] = teu_df["isin"].map(ccode_map)
    masterfirms = [row.to_dict() for _, row in teu_df.iterrows()]
    
    te_function = create_te_function(masterfirms, sDate, eDate)
    teu_df.rename(columns={"isin": "ISIN"}, inplace=True)
    
    weights_b = np.array(pd.merge(teu_df, benchmark[["ISIN", "Weight"]], on=["ISIN"], how="left")["Weight"].fillna(0.0).tolist())
    weights_i = np.array(pd.merge(teu_df, base_portfolio[["ISIN", "Weight"]], on=["ISIN"], how="left")["Weight"].fillna(0.0).tolist())
    print(weights_b)
    weights_o = weights_i
    
    
    diff = weights_o - weights_b
    te_error = te_function(diff)
    
    while te_error > te_threshold:
        weights_o = (1 - tf) * weights_o + tf * weights_b
        diff = weights_o - weights_b
        te_error = te_function(diff)
        
    teu_df["te_strategy"] = weights_o
    teu_df["base_index"] = weights_i
    teu_df["benchmark"] = weights_b
    teu_df.to_csv("strategy_files/teu-strategy-file.csv")
    
    te_strategy_key = te_strategy_prefix + f"{eDate}/te_strategy.csv"
    s3_writer.write_as_dataframe(teu_df, te_data_bucket, te_strategy_key)
    print("final-tracking-error", te_error)
    return weights_o

def main():
    
    bucket = te_data_bucket
    objs = s3_aws.list_objects_v2(Bucket=bucket, Prefix=base_index_prefix)["Contents"]
    last_added = max(objs, key=lambda x: x["LastModified"])
    
    s3_response_object = s3_aws.get_object(Bucket=bucket, Key=last_added["Key"])
    object_content = BytesIO(s3_response_object["Body"].read())
    base_portfolio = pd.read_csv(object_content)
    base_portfolio = base_portfolio.rename(columns={"weights": "Weight", "isin": "ISIN"}).drop(columns=["Unnamed: 0"])
    
    bucket = snp_bucket
    objs = s3_aws.list_objects_v2(Bucket=bucket, Prefix=snp_solactive_prefix)["Contents"]
    last_added = max(objs, key=lambda x: x["LastModified"])
    
    s3_response_object = s3_aws.get_object(Bucket=bucket, Key=last_added["Key"])
    object_content = BytesIO(s3_response_object["Body"].read())
    try:
        benchmark = pd.read_excel(object_content, "Sheet0", skiprows=2, index_col=0, header=0,
                            usecols=[0, 4], engine="xlrd")
    except:
        benchmark = pd.read_excel(object_content, "Sheet0", skiprows=2, index_col=0, header=0,
                            usecols=[0, 4], engine="openpyxl")
        
    benchmark.dropna(inplace=True)
    benchmark['Index Weighting'] = benchmark['Index Weighting'].map(lambda x: float(x.replace("%", "")))
    benchmark['Index Weighting'] /= 100
    benchmark.index.name = None
    benchmark = benchmark.reset_index().rename(columns={"index": "ISIN", "Index Weighting": "Weight"})

    aieq_df = get_master("aieq")
    ccode_map = dict(zip(aieq_df["isin"].tolist(), aieq_df["country_code"].tolist()))
    benchmark["country_code"] = benchmark["ISIN"].map(ccode_map)
    base_portfolio['country_code'] = base_portfolio["ISIN"].map(ccode_map)
    benchmark["country_code"].fillna("USA", inplace=True)
    base_portfolio["country_code"].fillna("USA", inplace=True)
    benchmark = benchmark[~benchmark["ISIN"].isna()].reset_index(drop=True)    
    
    today = date.today()
    prev = today - timedelta(days=1)
    dt = pd.to_datetime(prev)
    te_date_str=dt.strftime("%Y-%m-%d")
    te_date = dt
    
    sDate =(te_date - timedelta(days=366)).strftime("%Y-%m-%d")
    endDate = (te_date - timedelta(days=0)).strftime("%Y-%m-%d")
    
    te_threshold = 0.025
    tilt_factor = 0.02
    optimize(benchmark=benchmark, base_portfolio=base_portfolio, sDate=sDate, eDate=endDate, te_threshold=te_threshold, tf=tilt_factor)