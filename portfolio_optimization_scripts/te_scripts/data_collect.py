from imports import *

from core import *
import boto3
import requests
import threading
import pandas as pd
from functools import reduce
from botocore.client import BaseClient
from datetime import date
from pandas import json_normalize

te_data_bucket = "micro-ops-output"
te_data_prefix = "test/tracking-error-dump/data/closeprices/"
te_strategy_prefix = "test/tracking-error-dump/TE_Application/out_strategy/"
base_index_prefix = "test/tracking-error-dump/SentPortfolio/"

snp_bucket = "index-build"
snp_solactive_prefix = "snp_solactive/"

s3_aws: BaseClient = boto3.client('s3', aws_access_key_id="********************",
                                  aws_secret_access_key="bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/")
s3 = boto3.resource("s3", region_name='us-east-1', aws_access_key_id="********************",
                    aws_secret_access_key="bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/")


def get_universe(benchmark: pd.DataFrame, base_portfolio: pd.DataFrame):
    
    teu_set = list(set(benchmark['ISIN'].tolist() + base_portfolio['ISIN'].tolist()))
    return teu_set

def get_stock_data(api_url):
    resp = requests.get(api_url).json()
    if "stocks" in resp["data"]:
        dataframe = pd.DataFrame.from_dict(json_normalize(resp["data"]["stocks"]))
        dataframe = dataframe[['date', 'adj_close', 'isin']]
        return dataframe
    
    dataframe = pd.DataFrame(columns=["date", "adj_close", "isin"])
    return dataframe

def get_data_es(firm, lock, dataframes):
    object_key = te_data_prefix + f"{firm['isin']}.csv"
    
    with lock:
        startDate = "2009-01-01"
        endDate = date.today().strftime("%Y-%m-%d")
        api_url = "http://52.2.217.192:8080/stockhistory/getstocksbyisin?isin=" + firm[
                    'isin'] + "&startDate=" + str(startDate) + "&endDate=" + str(endDate) + "&countrycode=" + firm[
                            'country_code']
        dataframe = get_stock_data(api_url)
        s3_writer.write_as_dataframe(dataframe, te_data_bucket, object_key)
            
        if len(dataframe):
            dataframe["actual-daily-return"] = dataframe["adj_close"].pct_change(periods=1)
            dataframe = dataframe[["date", "actual-daily-return"]]
        else:
            dataframe = dataframe[["date"]]
            dataframe["actual-daily-return"] = None
        
        dataframe.rename(columns={"actual-daily-return": firm["isin"]}, inplace=True)
        dataframes[firm['isin']] = dataframe
        
def collect_data(masterfirms):
    
    thread_track = []
    readers = []
    
    lock = threading.Lock()
    dataframes = {}
    for i, firm in enumerate(masterfirms):
        thread_track.append(threading.Thread(target=lambda: get_data_es(firm, lock, dataframes))) 
        thread_track[i].start()
    
    for i in range(len(masterfirms)):
        thread_track[i].join()
        
    dfs = []
    for firm, dataframe in dataframes.items():
        dfs.append(dataframe)
    
    dataframe = reduce(
        lambda left, right: pd.merge(left, right, on=["date"], how="outer", suffixes=('_left', '_right')),
        dfs
    )
    dataframe["date"] = pd.to_datetime(dataframe["date"]).dt.strftime('%Y-%m-%d')
    dataframe = dataframe.sort_values(by="date").reset_index(drop=True).fillna(0.0)
    return dataframe