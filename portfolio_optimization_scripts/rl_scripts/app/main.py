import os
import requests
import numpy as np
import pandas as pd
from services import *
from typing import Union, List
from fastapi import FastAPI
from pydantic import BaseModel
from tools import *

script_dir = os.path.dirname(os.path.abspath(__file__))
resource_dir = os.path.join(script_dir, "resources")
template = None

def prepare():
    uni_key = os.path.join(resource_dir, "universe.npy")
    template_key = os.path.join(resource_dir, "template.csv")
    globals()["template"] = pd.DataFrame({"isin": np.load(uni_key)})
    globals()["template"] = pd.read_csv(template_key)
    return

def postprocess(data_frame, ps=250, ws= 0.0002):
    
    max_market_cap = data_frame["marketcap"].max()
    data_frame["marketcap_comp"] = max_market_cap - data_frame["marketcap"]
    data_frame["marketcap_comp"] = data_frame["marketcap_comp"].apply(lambda marketcap: max(1, marketcap))
    data_frame = data_frame.sort_values(by="weights", ascending=False).reset_index(drop=True)
    
    n = min(ps, len(data_frame[data_frame["weights"] > 0]))
    print(f"number of isins with non-zero weights --> {n}")
    while True:
        data_frame.loc[n:, "weights"] = 0
        extra_weight = 1 - data_frame["weights"].sum()
        total_market_cap = data_frame.loc[:(n-1), "marketcap_comp"].sum()
        data_frame.loc[:(n-1), "weights"] += (data_frame.loc[:(n-1), "marketcap_comp"] * extra_weight / total_market_cap)
        data_frame.loc[:(n-1), "weights"] = data_frame.loc[:(n-1), "weights"].apply(lambda _: max(_, 0.0002))
        if (np.isclose(data_frame["weights"].sum(), 1)):
            break
    print('phase-I-completed!')
    print(data_frame.loc[:n])
            
    while True:
        data_frame = data_frame.sort_values(by="weights", ascending=False).reset_index(drop=True)
        data_frame.loc[n:, "weights"] = 0
        extra_weight = 1 - data_frame["weights"].sum()
        total_market_cap = data_frame.loc[:(n-1), "marketcap"].sum()
        data_frame.loc[:(n-1), "weights"] += (data_frame.loc[:(n-1), "marketcap"] * extra_weight / total_market_cap)
        constraint_verification = sum(data_frame["weights"] <= data_frame["caps"])
        data_frame['weights'] = data_frame['weights'].where(data_frame['weights'] >= ws, 0)
        data_frame["weights"] = data_frame.apply(lambda row: min(row["weights"], row["caps"]), axis=1)
        if data_frame[data_frame["weights"] > 0]["caps"].sum() < 1:
            n += 1
        if (np.isclose(data_frame["weights"].sum(), 1)):
            break
    
    print('phase-II-completed!')
    
    return data_frame

def weight_check(request_data):
    total_weight = request_data["weights"].sum()
    
    if total_weight == 1:
        return request_data
    
    if total_weight > 1:
        request_data = request_data.sort_values(by="weights").reset_index(drop=True)
        weight_dump = total_weight - 1
        
        mins, weights, index = request_data["min"].tolist(), request_data["weights"].tolist(), 0
        while weight_dump and (index < len(request_data)):
            weight = min(weight_dump, weights[index] - mins[index], weights[index] - 0.0002)
            weights[index] -= weight
            weight_dump -= weight
            index += 1
        request_data["weights"] = weights
    
    if total_weight < 1:
        request_data = request_data.sort_values(by="weights", ascending=False).reset_index(drop=True)
        weight_dump = 1 - total_weight
        
        caps, weights, index = request_data["caps"].tolist(), request_data["weights"].tolist(), 0
        while weight_dump and (index < len(request_data)):
            weight = min(weight_dump, caps[index] - weights[index])
            weights[index] += weight
            weight_dump -= weight
            index += 1
        request_data["weights"] = weights
    
    return request_data
    
    

def get_master():
    firms_url = ' http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aieq'
    aieq_json = requests.get(f'{firms_url}').json()
    aieq_df = pd.DataFrame(aieq_json['data']['masteractivefirms_aieq'])
    return aieq_df[["isin", ]]

app = FastAPI()
prepare()

@app.post("/rebalance")
async def rebalance(request: List[dict], tag: str = "", er_field: str = "er", caps_field: str = "max", min_caps_field: str = None, market_cap_field: str = None):
    print(len(request))
    print(request)
    request_data = pd.DataFrame(request)
    request_data.to_csv("request-data-3.csv")
    request_data[caps_field] = request_data[caps_field] - request_data[min_caps_field]
    temp = template.copy()
    date = request[0]["date"]
    
    cat_er = get_es_data_by_date(date, 'eq_cat_er_model')
    cat_er = cat_er[cat_er["schedular"] == "Monthly"].reset_index(drop=True)
    temp = pd.merge(temp[["isin"]], cat_er[["isin", "marketcap"]], on=['isin'], how='left').reset_index(drop=True)
    temp["marketcap"].fillna(0.0, inplace=True)
    if min_caps_field:
        request_data.rename(columns={min_caps_field: "min"}, inplace=True)
    else:
        request_data["min"] = 0.0
    
    if market_cap_field:
        temp.drop(columns=["marketcap"], inplace=True)
        
    req_data = pd.merge(temp, request_data, on=["isin"], how="left")
    req_data.to_csv("req_data_2.csv")
    
    if er_field:
        request_data.rename(columns={er_field: "er"}, inplace=True)
        req_data.rename(columns={er_field: "er"}, inplace=True)
    else:
        return "er field cannot be None, as it is a required field for the process."
    
    if caps_field:
        request_data.rename(columns={caps_field: "caps"}, inplace=True)
        req_data.rename(columns={caps_field: "caps"}, inplace=True)
    else:
        return "caps field cannot be None, as it is a required field for the process."
    
    if market_cap_field:
        request_data.rename(columns={market_cap_field: "marketcap"}, inplace=True)
        req_data.rename(columns={market_cap_field: "marketcap"}, inplace=True)
    
    
    req_data["caps"].fillna(0.0, inplace=True)
    req_data["er"].fillna(-100.0, inplace=True)
    req_data["marketcap"].fillna(0.0, inplace=True)
    
    req_data = req_data.sort_values(by="er", ascending=False).reset_index(drop=True)
    req_data["mask"] = np.concatenate([np.ones(300,), np.zeros(len(req_data) - 300)])
    req_data = req_data.sort_values(by="isin").reset_index(drop=True)
    
    if tag == "aieq":
        req_data["caps"] = req_data["caps"] * req_data["mask"]
        ps = 160
        ws = 0.0005
    else:
        ps = 250
        ws = 0.0002
        
    expected_return = np.array(req_data["er"].tolist())
    cap = np.array(req_data["caps"].tolist())
    
    req_data.to_csv("request.csv")
    
    weight = run_actor(expected_return, cap, tag=tag)
    weight_dump = 1 - request_data["min"].sum()
    weight = weight_dump * weight
    
    req_data["weights"] = weight
    resp_data = postprocess(req_data, ps=ps, ws=ws)
    weights = dict(zip(resp_data["isin"].tolist(), resp_data["weights"].tolist()))
    
    request_data["weights"] = request_data["min"]
    request_data["weights"] = request_data.apply(lambda row: row["weights"] + weights.get(row["isin"], 0.0), axis=1)
    
    request_data = weight_check(request_data)
    response = [resp.to_dict() for _, resp in request_data.iterrows()]
    
    return response