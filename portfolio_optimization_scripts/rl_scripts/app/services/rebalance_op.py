import os
import gym
import jax
import reverb
import dm_env
import logging
import pprint
import timeit
import copy
import json
import boto3
import haiku as hk
import numpy as np
import jax.nn as jnn
from tqdm import tqdm
from gym import spaces
import jax.numpy as jnp
from typing import Union, List, Callable, Tuple, Iterator, Any, Dict

from acme import specs
from acme import types
from acme.jax import utils
from acme.jax import experiments
from acme.agents.jax import bc
from acme.datasets import tfds
from acme.jax import types as jax_types
from acme.adders import reverb as adders
from acme.wrappers import GymWrapper
from reverb import structured_writer as sw
from acme.adders.reverb import structured
from acme.datasets import reverb as datasets
from acme.adders.reverb import base as reverb_base
from acme.examples.baselines.imitation import helpers
from acme.agents.jax import actor_core as actor_core_lib


aws_access_key='********************'
aws_secret_key='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'

bucket_key = "micro-ops-output"
# agent_key = "test/aipex-testing/rebalancing/poc/agents/cat-er-model/behavior-cloning/episode-lengths [u]/2025-04-15/policy.json"
agent_key = "test/varaprasad-rl/backtest/aipex-backtesting/institutional-platform/behaviour-cloning/policy.json"
aieq_agent_key = "test/varaprasad-rl/backtest/aipex-backtesting/institutional-platform/behaviour-cloning/aieq-policy.json"

script_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(os.path.dirname(script_dir), "resources/data")

data_man = None

class DataLoader:
    
    data_dir = data_dir
    def __init__(self, data_dir: Union[str, None] = None):
        if data_dir:
            self.data_dir = data_dir
    
    @property
    def expected_return(self):
        expected_return_path = os.path.join(self.data_dir, "catboost-returns.npy")
        return np.load(expected_return_path)
    
    @property
    def actual_return(self):
        actual_return_path = os.path.join(self.data_dir, "true-returns.npy")
        return np.load(actual_return_path)
    
    @property
    def cat_mask(self):
        cat_mask_path = os.path.join(self.data_dir, "catboost-masks.npy")
        return np.load(cat_mask_path)
    
    @property
    def train_dates(self):
        train_date_path = os.path.join(self.data_dir, "train-dates.npy")
        return np.load(train_date_path)

class PortfolioOptimizationEnv(gym.Env):
    def __init__(self, terminal_state: int, portfolio_size: int, max_cap: float = 1.0, episode_length: int = 44):
        self._terminal_state = terminal_state
        self._portfolio_size = portfolio_size
        self._max_cap = max_cap
        
        self.observation_space = spaces.Box(-np.inf, np.inf, shape=(2*portfolio_size,), dtype=np.float64)
        self.action_space = spaces.Box(0, max_cap, shape=(portfolio_size,), dtype=np.float64)
        self._episode_length = episode_length
        self._initialize_state()
    
    @property
    def _state(self):
        self._expected_return = data_man.expected_return[self._state_id]
        self._true_return = data_man.actual_return[self._state_id]
        self._caps = np.random.uniform(low=0.0, high=self._max_cap, size=self._portfolio_size)
        state = np.concatenate([self._expected_return, self._caps])
        return state
        
    def _initialize_state(self):
        self._state_id = self._terminal_state - self._episode_length
        
    def reset(self):
        self._initialize_state()
        return self._state
        
    def step(self, action: np.ndarray):
        reward = PortfolioOptimizer.compute_return(self._state, self._expected_return, self._caps, action)
        done = self._state_id >= self._terminal_state
        
        if done:
            self._state_id = self._terminal_state - self._episode_length
        else:
            self._state_id += 1    
        return self._state, reward, done, {}
        
class PortfolioOptimizer:
    @staticmethod
    def compute_return(state, sreturns, caps, action):
        weight = action * caps
        portfolioreturn = np.dot(weight, sreturns)
        totalweight = np.sum(weight)
        if totalweight > 1.0:
            portfolioreturn = portfolioreturn / totalweight
        theoretical_max_return = PortfolioOptimizer._compute_max_return(sreturns, caps)
        if theoretical_max_return == 0.0:
            theoretical_max_return = portfolioreturn
        return portfolioreturn/theoretical_max_return
    
    @staticmethod
    def _compute_max_return(sreturns, caps):
        portfolio_size = sreturns.shape[0]
        weight = np.zeros(shape=(portfolio_size,))
        total_weight, ranks = 0, np.argsort(-sreturns)
        
        for rank in ranks:
            if (sreturns[rank] <= 0) or total_weight == 1.0:
                break
            weight[rank] = min(1.0 - total_weight, caps[rank])
            total_weight += weight[rank]
        
        return np.dot(weight, sreturns) + 1e-3 if total_weight >= 0.1 else 0.0
    
    @staticmethod
    def policy(sreturns, caps):
        portfolio_size = sreturns.shape[0]
        weight = np.zeros(shape=(portfolio_size,))
        total_weight, ranks = 0, np.argsort(-sreturns)
        
        for rank in ranks:
            if (sreturns[rank] <= 0) or total_weight == 1.0:
                break
            weight[rank] = min(1.0 - total_weight, caps[rank])
            total_weight += weight[rank]
        
        return weight

def read_json(bucket_name: str, path: str) -> Dict[str, Any]:
    client = boto3.client('s3', aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key)
    try:
        response = client.get_object(Bucket=bucket_name, Key=path)
        content = response["Body"].read().decode("utf-8")
        return json.loads(content)
    except:
        pass

def convert_to_jax_arrays(d):
    if isinstance(d, list):
        return jnp.array(d)
    elif isinstance(d, dict):
        return {k: convert_to_jax_arrays(v) for k, v in d.items()}
    return d

def _make_adder_config(
    step_spec: reverb_base.Step,
    sequence_length: int,
    period: int,
    table: str
) -> List[sw.Config]:
    return structured.create_sequence_config(
        step_spec=step_spec,
        sequence_length=sequence_length,
        period=period,
        table=table
    )

def read_arr_from_s3(bucket_name, file_name):
    client = boto3.client('s3', aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key)
    try:
        response=client.get_object(Bucket=bucket_name, Key=file_name)
        byte_stream=response['Body'].read()
        numpy_array=np.frombuffer(byte_stream)
        return numpy_array
    except:
        pass

def write_arr_to_s3(arr, bucket_name, file_name):
    client=boto3.client('s3', aws_access_key_id = aws_access_key, aws_secret_access_key = aws_secret_key)
    bytes_stream = arr.tobytes()
    client.put_object(Bucket=bucket_name, Key=file_name, Body=bytes_stream)
    return True

def get_environment(portfolio_date):
    """
    Creates and returns a Gym environment for the given portfolio date.
    Args:
        portfolio_date: The date for which the environment is created.

    Returns:
        A GymWrapper instance wrapping the PortfolioOptimizationEnv.
    """
    monthly_period = 22
    portfolio_size = data_man.expected_return.shape[1]

    logging.info(f"Creating environment for portfolio-date: {portfolio_date}")
    terminal_state = sum(date < portfolio_date for date in data_man.train_dates) - monthly_period
    logging.info(f"Terminal state for portfolio-date: {terminal_state}")

    environment = GymWrapper(
        PortfolioOptimizationEnv(
            terminal_state=terminal_state,
            portfolio_size=portfolio_size,
        )
    )
    return environment

def ideal_actor(
    environment: PortfolioOptimizationEnv,
    table_name: str,
    num_episodes: int = 20,
    num_demonstrations: int = 200,
):
    global server
    environment_spec = specs.make_environment_spec(environment)
    step_spec = adders.create_step_spec(environment_spec=environment_spec)

    sequence_configs = _make_adder_config(
        step_spec=step_spec,
        sequence_length=2,
        period=1,
        table=table_name,
    )

    signature = sw.infer_signature(
        configs=sequence_configs,
        step_spec=step_spec,
    )

    replay_table = reverb.Table.queue(
        table_name, max_size=100000000, signature=signature
    )
    server = reverb.Server([replay_table])
    client = reverb.Client(f"localhost:{server.port}")
    priority_fns = {table_name: lambda x: 1.0}

    adder = adders.SequenceAdder(
        client=client,
        sequence_length=2,
        period=1,
        priority_fns=priority_fns,
        end_of_episode_behavior=adders.EndBehavior.TRUNCATE,
    )

    for _ in tqdm(range(num_episodes)):
        first = environment.reset()
        adder.add_first(first)
        timestep = first
        while not timestep.last():
            observation = timestep.observation
            action = PortfolioOptimizer.policy(environment._true_return, environment._caps)
            timestep = environment.step(action)
            adder.add(action, next_timestep=timestep, extras=())

    server_address = f"localhost:{server.port}"

    def convert_sample(sample):
        info = sample.info
        data = sample.data

        return types.Transition(
            observation=data.observation[0],
            action=data.action[0],
            reward=data.reward[0],
            discount=data.discount[0],
            next_observation=data.observation[1],
            extras=(),
        )

    dataset = datasets.make_reverb_dataset(
        table=table_name,
        server_address=server_address,
        num_parallel_calls=None,
    )
    dataset = dataset.map(convert_sample)
    dataset = dataset.take(num_demonstrations)
    return dataset

def run_experiment(portfolio_date: str):
    environment = get_environment(portfolio_date)
    dataset = ideal_actor(environment=environment, table_name=portfolio_date)

    def _make_demonstration_dataset_factory(
        num_demonstrations: int, dataset, batch_size: int
    ) -> Callable[[jax_types.PRNGKey], Iterator[types.Transition]]:
        def demonstration_dataset_factory(
            random_key: jax_types.PRNGKey,
        ) -> Iterator[types.Transition]:
            """Returns an iterator of demonstration samples."""
            return tfds.JaxInMemoryRandomSampleIterator(
                dataset, key=random_key, batch_size=batch_size
            )

        return demonstration_dataset_factory

    def _make_environment_factory() -> jax_types.EnvironmentFactory:
        """Returns an environment factory for the given environment."""

        def environment_factory(seed: int) -> dm_env.Environment:
            del seed
            environment = get_environment(portfolio_date)
            return environment

        return environment_factory

    def _make_network_factory(
        shift: Tuple[np.float64],
        scale: Tuple[np.float64],
        num_layers: int,
        num_units: int,
        dropout_rate: float,
    ) -> Callable[[specs.EnvironmentSpec], bc.BCNetworks]:
        def network_factory(spec: specs.EnvironmentSpec) -> bc.BCNetworks:
            action_spec = spec.actions
            num_dimensions = np.prod(action_spec.shape, dtype=int)

            def actor_fn(obs, is_training=False, key=None):
                hidden_layers = [num_units] * num_layers
                mlp = hk.Sequential(
                    [
                        hk.nets.MLP(hidden_layers + [num_dimensions]),
                        jnn.relu,
                    ]
                )

                if is_training:
                    output = mlp(obs, dropout_rate=dropout_rate, rng=key)
                    return output
                else:
                    output = mlp(obs)
                    return output

            policy = hk.without_apply_rng(hk.transform(actor_fn))

            # Create dummy observations to create network parameters.
            dummy_obs = utils.zeros_like(spec.observations)
            dummy_obs = utils.add_batch_dim(dummy_obs)

            policy_network = bc.BCPolicyNetwork(
                lambda key: policy.init(key, dummy_obs), policy.apply
            )

            return bc.BCNetworks(policy_network=policy_network)

        return network_factory

    data_stats = {}
    # Hyper-parameters.
    batch_size = 64
    num_demonstrations = 20000

    def build_experiment_config() -> experiments.OfflineExperimentConfig[
        bc.BCNetworks, actor_core_lib.FeedForwardPolicy, types.Transition
    ]:
        environment = get_environment(portfolio_date)
        environment_spec = specs.make_environment_spec(environment)
        demonstration_dataset_factory = _make_demonstration_dataset_factory(
            num_demonstrations=num_demonstrations, dataset=dataset, batch_size=batch_size
        )

        shift, scale = helpers.get_observation_stats(dataset)

        data_stats["shift"] = shift.tolist()
        data_stats["scale"] = scale.tolist()

        network_factory = _make_network_factory(
            shift=shift,
            scale=scale,
            num_layers=3,
            num_units=1024,
            dropout_rate=0.1,
        )

        bc_config = bc.BCConfig(learning_rate=1e-4)
        bc_builder = bc.BCBuilder(bc_config, loss_fn=bc.mse())
        environment_factory = _make_environment_factory()

        return experiments.OfflineExperimentConfig(
            builder=bc_builder,
            network_factory=network_factory,
            demonstration_dataset_factory=demonstration_dataset_factory,
            environment_factory=environment_factory,
            max_num_learner_steps=20000,
            seed=0,
            environment_spec=environment_spec,
        )

    config = build_experiment_config()
    return config

def build_actor(portfolio_date: str):
    
    config = run_experiment(portfolio_date)
    experiment = copy.deepcopy(config)
    key = jax.random.PRNGKey(experiment.seed)
    
    environment = experiment.environment_factory(experiment.seed)
    environment_spec = experiment.environment_spec or specs.make_environment_spec(environment)
    
    networks = experiment.network_factory(environment_spec)
    eval_key, key = jax.random.split(key)
    eval_actor = experiment.builder.make_actor(
        random_key=eval_key,
        policy=experiment.builder.make_policy(networks, environment_spec, True),
        environment_spec=environment_spec,
        variable_source='')
    return eval_actor


def get_params(tag=""):
    policy_key = agent_key
    if tag == "aieq":
        policy_key = aieq_agent_key
    
    eval_params = read_json(bucket_key, policy_key)
    eval_params = convert_to_jax_arrays(eval_params)
    return eval_params

def get_actor():
    portfolio_date = "2025-01-31"
    return build_actor(portfolio_date)

data_man = DataLoader()
eval_actor = get_actor()
eval_params = get_params()
aieq_eval_params = get_params("aieq")

key = jax.random.PRNGKey(seed=0)
eval_key, key = jax.random.split(key)

def run_actor(expected_return, caps, tag=""):
    
    params = eval_params
    if tag == "aieq":
        params = aieq_eval_params
    
    wcaps = caps
    expected_return = np.nan_to_num(expected_return, nan=-100.0)
    
    weights = np.zeros(shape=wcaps.shape)
    action_count = 0
    
    pprint.pprint(eval_params)
    while np.sum(np.abs(weights)) < 1.0 and action_count <= 30:
        caps = wcaps - weights
        state = jnp.concatenate([expected_return, caps], axis=0)
        action, _ = eval_actor._policy(params, state, eval_key)
        action = utils.to_numpy(action)
        action = action * ((action != 0).astype('float64'))
        abs_caps_action = np.abs(caps * action)
        factor = np.min(np.divide(caps, abs_caps_action, out=np.full_like(caps, np.inf), where=(abs_caps_action)!=0))
        if factor == np.inf:
            factor = 1
        action = action * factor
        weights += (caps * action)
        action_count += 1

    total_weight = np.sum(np.abs(weights))
    caps = wcaps - weights
    if total_weight < 1.0:
        weights += ((1 - total_weight) /np.sum(caps)) * caps
    
    weights = weights / np.sum(weights)
    return weights