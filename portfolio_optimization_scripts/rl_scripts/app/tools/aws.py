import json
import boto3
import numpy as np
import jax.numpy as jnp
from typing import Optional

class CustomJsonEncoder(json.JSONEncoder):
    def default(self, object):
        if isinstance(object, (jnp.ndarray)):
            return object.tolist()
        elif hasattr(object, '__dict__'):
            return object.__dict__
        return super().default(object)
  

class S3:
    def __init__(self):
        self.access_key = "********************"
        self.secret_key = "bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/"
        self.bucket_key = "micro-ops-output"
        self.path_prefix = "f'test/aipex-testing/rebalancing/poc/agents/behavior-cloning/episode-lengths [u]"
    
    @property
    def client(self):
        client = boto3.client("s3", aws_access_key_id=self.access_key, aws_secret_access_key=self.aws_secret_key)
        return client
    
    def read_arr(self, bucket_name: str, path_key: str) -> Optional[np.ndarray]:
        try:
            response = self.client.get_object(Bucket=bucket_name, Key=path_key)
            byte_stream = response["Body"].read()
            numpy_array = np.frombuffer(byte_stream)
            return numpy_array
        except:
            return
    
    def write_arr(self, numpy_array: np.ndarray, bucket_name: str, path_key: str) -> bool:
        bytes_stream = numpy_array.tobytes()
        self.client.put_object(Bucket=bucket_name, Key=path_key, Body=bytes_stream)
        return True
    
    def store_json(self, object: dict, portfolio_date: str, component: str = "policy") -> None:
        json_data = json.dumps()
        path_key = self.data_prefix + f"{portfolio_date}/{component}.json"
        self.client.put_object(Bucket=self.bucket_key, Key=path_key, Body=json_data)
        return True