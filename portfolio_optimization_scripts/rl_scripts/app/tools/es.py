import json
import traceback
import pandas as pd

import pandas as pd
from eq_common_utils.utils.opensearch_helper import OpenSearch
import json
def connect_openSearch():
    hosts = ['https://search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com:443']
    port = 443
 
    client = OpenSearch(
        host='search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com',
        port=443,
        key_id='********************',
        secret='xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV',
        region = 'us-east-1'
     )
    return client

def get_values_from_es(isin,date_needed,index,schedular):
    year = pd.to_datetime(date_needed).date().year
    try:
        df=get_es_data(isin, [year,year], f'{index}')
        df=df[df['schedular']==f'{schedular}']
        df=df[pd.to_datetime(df['date'])==pd.to_datetime(date_needed)]
        return df
    except:
        print("Data not there for ISIN",isin)
 
def get_es_data(isin, dates, index_prefix):
    try:
        data=[]
        for year in range(dates[0], dates[1] + 1):
            q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
            try:
                result = es.run_query(query=json.loads(q_total),index=f"{index_prefix}_{year}")
            except Exception as e:
                print(e)
                pass
            for rs in result['hits']['hits']:
                es_data=rs['_source']
                data.append(es_data)
        df=pd.DataFrame(data)
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        return df
    except Exception as e:
        print(e.message)
        pass

es=connect_openSearch()


def get_es_data_by_date(date, index_prefix):
        try:
            data = []
            date_str = pd.to_datetime(date).strftime('%Y-%m-%d')
            
            q_total = '{"query":{"bool": {"must":[{"match":{"date":"'+date_str+'"}}]}}}'
            year = pd.to_datetime(date).year
            index_list = [f"{index_prefix}_{year}"]
            
            for index in index_list:
                try:
                    result = es.run_query(query=json.loads(q_total), index=index)
                    for rs in result['hits']['hits']:
                        es_data = rs['_source']
                        data.append(es_data)
                except Exception as e:
                    print(f'encountered exception {traceback.format_exc()}')
                    
            df = pd.DataFrame(data)
            if not df.empty:
                df['date'] = pd.to_datetime(df['date'])
                df.sort_values('date', ascending=True, inplace=True)
                df.reset_index(inplace=True, drop=True)
                return df
        except Exception as e:
            print(traceback.format_exc())
            return pd.DataFrame()
        
def get_values_from_es(isin, date_needed, index, schedular):
    year = pd.to_datetime(date_needed).date().year
    try:
        df = get_es_data(isin, [year, year], f'{index}')
        df = df[df['schedular'] == f'{schedular}']
        df = df[pd.to_datetime(df['date']) == pd.to_datetime(date_needed)]
        return df
    except:
        pass
    
    
print(get_es_data_by_date("2025-04-25", "eq_cat_er_model"))