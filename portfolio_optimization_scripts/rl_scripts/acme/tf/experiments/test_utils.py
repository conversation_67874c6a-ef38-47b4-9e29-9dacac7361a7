"""utilities for testing of acme.jax.experiments functions."""

from acme.tf import experiments
from acme.tf import savers
from acme.utils import counting

def restore_counter(
    checkpointing_config: experiments.CheckpointingConfig) -> counting.Counter:
  """Restores a counter from the latest checkpoint saved with this config."""
  counter = counting.Counter()
  savers.Checkpointer(
      objects_to_save={'counter': counter},
      directory=checkpointing_config.directory,
      add_uid=checkpointing_config.add_uid,
      max_to_keep=checkpointing_config.max_to_keep)
  return counter