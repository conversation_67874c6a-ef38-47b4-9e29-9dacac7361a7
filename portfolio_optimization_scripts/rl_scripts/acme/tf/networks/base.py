
"""Convenient base classes for custom networks."""

import abc
from typing import <PERSON><PERSON>, TypeVar

from acme import types
import sonnet as snt
import tensorflow_probability as tfp

State = TypeVar('State')


class Module(snt.Module, abc.ABC):
  """A base class for module with abstract __call__ method."""

  @abc.abstractmethod
  def __call__(self, *args, **kwargs) -> types.NestedTensor:
    """Forward pass of the module."""


class DistributionalModule(snt.Module, abc.ABC):
  """A base class for modules that output distributions."""

  @abc.abstractmethod
  def __call__(self, *args, **kwargs) -> tfp.distributions.Distribution: # type: ignore
    """Forward pass of the module."""


class RNNCore(snt.RNNCore, abc.ABC):
  """An RNN core with a custom `unroll` function."""

  @abc.abstractmethod
  def unroll(self,
             inputs: types.NestedTensor,
             state: State,
             sequence_length: int,
             ) -> <PERSON><PERSON>[types.NestedTensor, State]:
    """A custom function for doing static unrolls over sequences.

    This has the same API as `snt.static_unroll`, but allows the user to specify
    their own implementation to take advantage of the structure of the network
    for better performance, e.g. by batching the feed-forward pass over the
    whole sequence.

    Args:
      inputs: A nest of `tf.Tensor` in time-major format.
      state: The RNN core state.
      sequence_length: How long the static_unroll should go for.

    Returns:
      Nested sequence output of RNN, and final state.
    """