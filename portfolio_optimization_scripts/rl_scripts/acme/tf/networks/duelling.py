"""a duelling network architecture, as described in [0].

[0] https://arxiv.org/abs/1511.06581
"""

from typing import Sequence

import sonnet as snt
import tensorflow as tf

class DuellingMLP(snt.Module):
    """a duelling MLP q-network."""

    def __init__(
        self,
        num_actions: int,
        hidden_sizes: Sequence[int],
    ):
        super().__init__(name='duelling_q_network')

        self._value_mlp = snt.nets.MLP([*hidden_sizes, 1])
        self._advantage_mlp = snt.nets.MLP([*hidden_sizes, num_actions])

    def __call__(self, inputs: tf.Tensor) -> tf.Tensor:
        """forward pass of the duelling network.
        
        args:
            inputs: a 2-d tensor of the shape [batch_size, embedding_size].
        
        returns:
            q_values: 2-d tensor of action values of shape [batch_size, num_actions].
        """

        # compute the value & advantage for duelling.
        value = self._value_mlp(inputs) # [B, 1]
        advantages = self._advantage_mlp(inputs) # [B, A]

        # advantages have zero mean.
        advantages -= tf.reduce_mean(advantages, axis=-1, keepdims=True) # [B, A]

        q_values = value + advantages # [B, A]

        return q_values