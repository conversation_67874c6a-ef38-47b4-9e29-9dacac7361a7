from acme.tf.networks.base import DistributionalModule
from acme.tf.networks.base import Module
from acme.tf.networks.base import RNNCore
from acme.tf.networks.continuous import LayerNormAndResidualMLP
from acme.tf.networks.continuous import LayerNormMLP
from acme.tf.networks.continuous import NearZeroInitializedLinear
from acme.tf.networks.discrete import DiscreteFilteredQNetwork
from acme.tf.networks.distributional import ApproximateMode
from acme.tf.networks.distributional import DiscreteValuedHead
from acme.tf.networks.distributional import MultivariateGaussianMixture
from acme.tf.networks.distributional import MultivariateNormalDiagHead
from acme.tf.networks.distributional import UnivariateGaussianMixture
from acme.tf.networks.distributions import DiscreteValuedDistribution
from acme.tf.networks.duelling import DuellingMLP
from acme.tf.networks.masked_epsilon_greedy import NetworkWithMaskedEpsilonGreedy
from acme.tf.networks.noise import ClippedGaussian
from acme.tf.networks.recurrence import <PERSON>riticDeepRNN
from acme.tf.networks.recurrence import DeepRNN
from acme.tf.networks.recurrence import LSTM
from acme.tf.networks.recurrence import RecurrentExpQWeightedPolicy
from acme.tf.networks.rescaling import ClipToSpec
from acme.tf.networks.rescaling import RescaleToSpec
from acme.tf.networks.rescaling import TanhToSpec
from acme.tf.networks.stochastic import ExpQWeightedPolicy
from acme.tf.networks.stochastic import StochasticMeanHead
from acme.tf.networks.stochastic import StochasticModeHead
from acme.tf.networks.stochastic import StochasticSamplingHead

# For backwards compatibility.
GaussianMixtureHead = UnivariateGaussianMixture

try:
  # pylint: disable=g-bad-import-order,g-import-not-at-top
  from acme.tf.networks.legal_actions import MaskedSequential
  from acme.tf.networks.legal_actions import EpsilonGreedy
except ImportError:
  pass
