"""variable handling utilities for tensorflow 2."""

from concurrent import futures
from typing import Mapping, Optional, Sequence

from acme import core
import tensorflow as tf
import tree

class VariableClient:
    """a variable client for updating variables from remote source."""

    def __init__(self,
                            client: core.VariableSource,
                            variables: Mapping[str, Sequence[tf.Variable]],
                            update_period: int =1):
        
        self._keys = list(variables.keys())
        self._variables = tree.flatten(list(variables.values()))
        self._call_counter = 0
        self._update_period = update_period
        self._client = client
        self._request = lambda: client.get_variables(self._keys)

        # create a single background thread to fetch variables without necessarily
        # blocking the actor.
        self._executor = futures.ThreadPoolExecutor(max_workers=1)
        self._async_request = lambda: self._executor.submit(self._request)

        # initialize this client's future to None to indicate to the `update()`
        # method that there is not pending/running request
        self._future: Optional[futures.Future] = None

    def update(self, wait: bool = True):
        """periodically updates the variables with the latest copy from the source.
        
        this stateful update method keeps track of the number of calls to it and,
        every `update_period` call, sends a request to its server to retrieve the 
        latest variables.
        
        if wait is True, a blocking request is executed. any active request will be 
        cancelled.

        if wait is False, this method makes an asynchronous request for variables
        and returns. unless the request is immediately fullfilled., the variables are
        only copied _within a subsequent call to `_update()`, whenever the request 
        is fulfilled by the `VariableSource`. if there is an existing fullfilled
        request when this method is called, the resulting variables are immediately
        copied.

        args:
            wait: True, executes the blocking update.
        """
        # track the number of calls (we only update periodically).
        if self._call_counter < self._update_period:
            self._call_counter += 1
        
        period_reached: bool = self._call_counter >= self._update_period

        if period_reached and wait:
            # cancel any active request.
            self._future: Optional[futures.Future] = None
            self.update_and_wait()
            self._call_counter = 0
            return
        
        if period_reached and self._future is None:
            # the update period has been requested and no request has been sent yet, so
            # making an asynchronous request now.
            self._future = self._async_request()
            self._call_counter = 0
        
        if self._future is not None and self._future.done():
            # the active request is done so copy the result and remove the future.
            self._copy(self._future.result())
            self._future: Optional[futures.Future] = None
        else:
            # there is either a pending/running request or we're between update
            # periods, so just carry on.
            return
    
    def update_and_wait(self):
        """immediately update and block until we get the result."""
        self._copy(self._request())
    
    def _copy(self, new_variables: Sequence[Sequence[tf.Variable]]):
        """copies the new variables to the new one."""

        new_variables = tree.flatten(new_variables)
        if len(self._variables) != len(new_variables):
            raise ValueError("length mismatch between old variables and new one.")
        
        for new, old in zip(new_variables, self._variables):
            old.assign(new)