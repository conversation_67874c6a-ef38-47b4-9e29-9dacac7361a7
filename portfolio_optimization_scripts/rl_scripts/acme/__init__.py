"""Acme is a framework for reinforcement learning."""

# Internal import.

# Expose specs and types modules.
from acme import specs
from acme import types

# Expose core interfaces.
from acme.core import Actor
from acme.core import Learner
from acme.core import Saveable
from acme.core import VariableSource
from acme.core import Worker

# Expose the environment loop.
from acme.environment_loop import EnvironmentLoop

from acme.specs import make_environment_spec
