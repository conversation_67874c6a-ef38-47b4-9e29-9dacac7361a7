"""NoO<PERSON> starts wrapper to allow stochastic initial state for determinstic python environments."""


from typing import Optional

from acme import types
from acme.wrappers import base
import dm_env
import numpy as np

class NoopStartsWrapper(base.EnvironmentWrapper):
    """implements random noop starts to episodes.

    this introduces randomness into an otherwise determinstic environment.

    note that the base environment must support a no-op action and the value
    of this action must be known and provided to this wrapper.
    """

    def __init__(self,
                            environment: dm_env.Environment,
                            noop_action: types.NestedArray = 0,
                            noop_max: int = 30,
                            seed: Optional[int] = None):
        """initializes a `NoopStartsWrapper` wrapper.
        
        Args:
            environment: an environment confirming to the dm_env.Environment
                interface.
            noop_action: the noop action used to step the environment for random 
                initialization.
            noop_max: the maximal number of noop actions at the start of the episode.
            seed: the random seed used to sample the number of noops.
        """
        if noop_max < 0:
            raise ValueError(
                'maximal number of no-ops after reset cannot be negative.'
                f'received noop_max={noop_max}')
        
        super().__init__(environment)
        self.np_random = np.random.RandomState(seed)
        self._noop_max = noop_max
        self._noop_action = noop_action

    def reset(self) -> dm_env.TimeStep:
        """resets the environment and provides the first timestep."""
        noops = self.np_random.randint(self._noop_max + 1)
        timestep = self.environment.reset()
        for _ in range(noops):
            timestep = self.environment.step(self._noop_action)
            if timestep.last():
                timestep = self.environment.reset()
        return timestep._replace(step_type=dm_env.StepType.FIRST)
