"""common environment wrapper classes."""


from acme.wrappers.action_repeat import <PERSON><PERSON><PERSON>eatWrapper
from acme.wrappers.base import EnvironmentWrapper
from acme.wrappers.base import wrap_all
from acme.wrappers.frame_stacking import <PERSON>ame<PERSON><PERSON>cker
from acme.wrappers.frame_stacking import Frame<PERSON>tackingWrapper
from acme.wrappers.noop_starts import NoopStartsWrapper
from acme.wrappers.concatenate_observations import ConcatObservationWrapper
from acme.wrappers.observation_action_reward import ObservationActionRewardWrapper
from acme.wrappers.observation_action_reward import OAR
from acme.wrappers.gym_wrapper import GymWrapper
from acme.wrappers.canonical_spec import CanonicalSpecWrapper