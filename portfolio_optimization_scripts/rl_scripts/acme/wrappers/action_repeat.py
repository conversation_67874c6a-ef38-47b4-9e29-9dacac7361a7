"""wrapper that implements action repeats."""

from acme import types
from acme.wrappers import base
import dm_env

class ActionRepeatWrapper(base.EnvironmentWrapper):
    """action repeat wrapper."""

    def __init__(self, environment: dm_env.Environment, num_repeats: int=1):
        super().__init__(environment)
        self._num_repeats = num_repeats
    
    def step(self, action: types.NestedArray) -> dm_env.TimeStep:
        # initialize a accumulates reward and discount.
        reward = 0
        discount = 1

        # step the environment by repeating action.
        for _ in range(self._num_repeats):
            timestep = self._environment.step(action)

            # accumulate the reward and discount
            reward += timestep.reward * discount
            discount *= timestep.discount

            # don't go over episode boundaries.
            if timestep.last():
                break

        # Replace the final timestep's reward and discount.
        return timestep._replace(reward=reward, discount=discount)