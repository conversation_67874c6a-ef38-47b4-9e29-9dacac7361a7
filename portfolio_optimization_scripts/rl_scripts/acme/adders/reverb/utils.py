"""utilities for reverb-based adders."""

from typing import Dict, Union

from acme import types
from acme.adders.reverb import base
import jax
import jax.numpy as jnp
import numpy as np
import tree

def zeros_like(x: Union[np.ndarray, int, float, np.number]):
    """returns a zero-filled object of the same (d)type and shape as the input.
    
    The difference between this and `np.zeros_like` is that this works well
    with `np.number`, `int`, `float`, and `jax.numpy.DeviceArray` objects without 
    converting them to `np.ndarrays`
    
    Args:
        x: the object to replace with 0s.
    
    Returns:
        A zero-filled object of the same (d)type and shape as the input.
    """
    if isinstance(x, (int, float, np.number)):
        return type(x)(0)
    elif isinstance(x, jax.Array):
        return jnp.zeros_like(x)
    elif isinstance(x, np.ndarray):
        return np.zeros_like(x)
    else:
        raise ValueError(
            f'Input ({type(x)}) must be either a numpy array, an int, or a float, or a jax array.'
        )

def final_step_like(step: base.Step,
                    next_observation: types.NestedArray) -> base.Step:
    """return a list of steps with the final step zero-filled."""
    # make zero-filled components so we can fill out the last step.
    zero_action, zero_reward, zero_discount, zero_extras = tree.map_structure(
        zeros_like, (step.action, step.reward, step.discount, step.extras))
    
    # return a final step that only has next-observation.
    return base.Step(
        observation=next_observation,
        action=zero_action,
        reward=zero_reward,
        discount=zero_discount,
        start_of_episode=False,
        extras=zero_extras
    )

def calculate_priorities(
        priority_fns: base.PriorityFnMapping,
        trajectory_or_transition: Union[base.Trajectory, types.Transition]
    ) -> Dict[str, float]:
    """helper used to compute the priority of a trajectory or transition.
    
    this helper converts the leaves of the trajectory or transition from
    `reverb.TrajectoryColumn` objects into numpy arrays. The converted Trajectory
    or Transition objects are passed into each of the functions in `priority_fns`.
    
    Args:
        priority_fns: a mapping from table names to priority functions (i.e. a 
            callable of type PriorityFn). The given function will be used to generate
            the priority (a float) for the given table.
        
        trajectory_or_transition: the trajectory or tranistion used to compute
            priorities.
    """
    if any([priority_fn is not None for priority_fn in priority_fns.values()]):

        trajectory_or_transition = tree.map_structure(lambda col: col.numpy(),
                                                      trajectory_or_transition)
    
    return {
        table: (priority_fn(trajectory_or_transition) if priority_fn is not None else 1.0)
        for table, priority_fn in priority_fns.items()
    }