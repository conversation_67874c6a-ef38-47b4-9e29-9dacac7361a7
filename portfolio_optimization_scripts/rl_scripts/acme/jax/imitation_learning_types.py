# Copyright 2018 DeepMind Technologies Limited. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""JAX type definitions for imitation and apprenticeship learning algorithms."""

from typing import TypeVar

# Common TypeVars that correspond to various aspects of the direct RL algorithm.
DirectPolicyNetwork = TypeVar('DirectPolicyNetwork')
DirectRLNetworks = TypeVar('DirectRLNetworks')
DirectRLTrainingState = TypeVar('DirectRLTrainingState')
