# Copyright 2018 DeepMind Technologies Limited. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Runner used for executing local offline RL agents."""

import acme
from acme import specs
from acme.jax.experiments import config
from acme.tf import savers
from acme.utils import counting
import jax
from io import BytesIO
import boto3
from acme.jax import utils
import json
import jax.numpy as jnp
import numpy as np

aws_access_key='********************'
aws_secret_key='bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'

bucket_key = 'micro-ops-output'
data_prefix =  f'test/varaprasad-rl/backtest/aipex-backtesting/daily-run-test/behaviour-cloning'

vc_bucket_key = 'eq-training-dump'
vc_learner_key = f'rl_portfolio_optimization/product/date/imitation-learning/policy.json'
class CustomJsonEncoder(json.JSONEncoder):
  def default(self, obj):
    if isinstance(obj, (jnp.ndarray)):
      return obj.tolist()
    elif hasattr(obj, '__dict__'):
      return obj.__dict__
    return super().default(obj)
  
def store_actor(learner, agent_type, portfolio_date):
  s3_client=boto3.client('s3', aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key)

  json_data = json.dumps(learner[0], cls=CustomJsonEncoder)
  learner_key = f'{data_prefix}/policy.json'
  if len(agent_type):
    learner_key = f'{data_prefix}/{agent_type}-policy.json'    
  s3_client.put_object(Bucket=bucket_key, Key=learner_key, Body=json_data)
  
  learner_key = vc_learner_key.replace("product", agent_type).replace("date", portfolio_date)
  s3_client.put_object(Bucket=vc_bucket_key, Key=learner_key, Body=json_data)
  pass

def store_json(object, agent_type, component='policy'):
  s3_client=boto3.client('s3', aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key)

  json_data = json.dumps(object, cls=CustomJsonEncoder)
  learner_key = f'{data_prefix}/{component}.json'
  if len(agent_type):
    learner_key = f'{data_prefix}/{agent_type}-{component}.json'
  s3_client.put_object(Bucket=bucket_key, Key=learner_key, Body=json_data)
  pass

def read_arr_from_s3(bucket_name, file_name):
    s3_client=boto3.client('s3', aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key)
    try:
        response=s3_client.get_object(Bucket=bucket_name, Key=file_name)
        byte_stream=response['Body'].read()
        numpy_array=np.frombuffer(byte_stream)
        return numpy_array
    except Exception as e:
        print(f"Error reading {file_name} from S3: {e}")
        return None

def write_arr_to_s3(arr, bucket_name, file_name):
    s3_client=boto3.client('s3', aws_access_key_id = aws_access_key, aws_secret_access_key = aws_secret_key)
    bytes_stream = arr.tobytes()
    s3_client.put_object(Bucket=bucket_name, Key=file_name, Body=bytes_stream)
    return True

def run_offline_experiment(experiment: config.OfflineExperimentConfig,
                           portfolio_date: str,
                           eval_every: int = 100,
                           num_eval_episodes: int = 1,
                           poc_n: int = 2,
                           agent_type: str = ''):
  """Runs a simple, single-threaded training loop using the default evaluators.

  It targets simplicity of the code and so only the basic features of the
  OfflineExperimentConfig are supported.

  Arguments:
    experiment: Definition and configuration of the agent to run.
    eval_every: After how many learner steps to perform evaluation.
    num_eval_episodes: How many evaluation episodes to execute at each
      evaluation step.
  """

  key = jax.random.PRNGKey(experiment.seed)

  # Create the environment and get its spec.
  environment = experiment.environment_factory(experiment.seed)
  environment_spec = experiment.environment_spec or specs.make_environment_spec(
      environment)

  # Create the networks and policy.
  networks = experiment.network_factory(environment_spec)

  # Parent counter allows to share step counts between train and eval loops and
  # the learner, so that it is possible to plot for example evaluator's return
  # value as a function of the number of training episodes.
  parent_counter = counting.Counter(time_delta=0.)

  # Create the demonstrations dataset.
  dataset_key, key = jax.random.split(key)
  dataset = experiment.demonstration_dataset_factory(dataset_key)

  # Create the learner.
  learner_key, key = jax.random.split(key)
  learner = experiment.builder.make_learner(
      random_key=learner_key,
      networks=networks,
      dataset=dataset,
      logger_fn=experiment.logger_factory,
      environment_spec=environment_spec,
      counter=counting.Counter(parent_counter, prefix='learner', time_delta=0.),
      portfolio_date=portfolio_date)

  # Define the evaluation loop.
  eval_loop = None
  if num_eval_episodes > 0:
    # Create the evaluation actor and loop.
    eval_counter = counting.Counter(
        parent_counter, prefix='evaluator', time_delta=0.)
    eval_logger = experiment.logger_factory(f'{portfolio_date}/evaluator',
                                            eval_counter.get_steps_key(), 0)
    eval_key, key = jax.random.split(key)
    eval_actor = experiment.builder.make_actor(
        random_key=eval_key,
        policy=experiment.builder.make_policy(networks, environment_spec, True),
        environment_spec=environment_spec,
        variable_source=learner)
    eval_loop = acme.EnvironmentLoop(
        environment,
        eval_actor,
        counter=eval_counter,
        logger=eval_logger,
        observers=experiment.observers)

  checkpointer = None
  if experiment.checkpointing is not None:
    checkpointing = experiment.checkpointing
    checkpointer = savers.Checkpointer(
        objects_to_save={'learner': learner, 'counter': parent_counter},
        time_delta_minutes=checkpointing.time_delta_minutes,
        directory=checkpointing.directory,
        add_uid=checkpointing.add_uid,
        max_to_keep=checkpointing.max_to_keep,
        keep_checkpoint_every_n_hours=checkpointing.keep_checkpoint_every_n_hours,
        checkpoint_ttl_seconds=checkpointing.checkpoint_ttl_seconds,
    )
  max_num_learner_steps = (
      experiment.max_num_learner_steps -
      parent_counter.get_counts().get('learner_steps', 0))

  # Run the training loop.
  if eval_loop:
    eval_loop.run(num_eval_episodes)
  steps = 0
  generation = 0
  while steps < experiment.max_num_learner_steps:
    print(f'generation-{generation} started......')
    learner_steps = eval_every
    for _ in range(learner_steps):
      learner.step()
      if checkpointer is not None:
        checkpointer.save()
    if eval_loop:
      eval_loop.run(num_eval_episodes)
    steps += learner_steps
    print(f'generation-{generation} completed..... {steps}/{experiment.max_num_learner_steps} done....')
    generation += 1

  store_actor(learner.get_variables(['policy']), agent_type=agent_type, portfolio_date=portfolio_date)
