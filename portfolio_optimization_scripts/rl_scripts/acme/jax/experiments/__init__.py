# Copyright 2018 DeepMind Technologies Limited. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""JAX experiment utils."""

from acme.jax.experiments.config import CheckpointingConfig
from acme.jax.experiments.config import default_evaluator_factory
from acme.jax.experiments.config import DeprecatedPolicyFactory
from acme.jax.experiments.config import EvaluatorFactory
from acme.jax.experiments.config import ExperimentConfig
from acme.jax.experiments.config import make_policy
from acme.jax.experiments.config import MakeActorFn
from acme.jax.experiments.config import NetworkFactory
from acme.jax.experiments.config import OfflineExperimentConfig
from acme.jax.experiments.config import PolicyFactory
from acme.jax.experiments.config import SnapshotModelFactory
# from acme.jax.experiments.make_distributed_experiment import make_distributed_experiment
# from acme.jax.experiments.make_distributed_offline_experiment import make_distributed_offline_experiment
from acme.jax.experiments.run_experiment import run_experiment
from acme.jax.experiments.run_offline_experiment import run_offline_experiment
