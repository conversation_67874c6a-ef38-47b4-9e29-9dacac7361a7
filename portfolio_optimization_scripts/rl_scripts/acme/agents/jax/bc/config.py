# Copyright 2018 DeepMind Technologies Limited. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Config classes for BC."""
import dataclasses


@dataclasses.dataclass
class BCConfig:
  """Configuration options for BC.

  Attributes:
    learning_rate: Learning rate.
    num_sgd_steps_per_step: How many gradient updates to perform per step.
  """
  learning_rate: float = 1e-4
  num_sgd_steps_per_step: int = 1
