# Adversarial Imitation Learning (AIL)

This folder contains a modular implementation of an Adversarial
Imitation Learning agent.
The initial algorithm is Generative Adversarial Imitation Learning
(GAIL - [<PERSON> et al., 2016]), but many more tricks and variations are
available.
The corresponding paper ([<PERSON><PERSON><PERSON> et al., 2021]) explains and discusses
the utility of all those tricks.

AIL requires an off-policy RL algorithm to work, passed in as an
`ActorLearnerBuilder`.

If you use this code, please cite [<PERSON><PERSON><PERSON> et al., 2021].

[<PERSON> et al., 2016]: https://arxiv.org/abs/1606.03476
[<PERSON><PERSON><PERSON> et al., 2021]: https://arxiv.org/abs/2106.00672
