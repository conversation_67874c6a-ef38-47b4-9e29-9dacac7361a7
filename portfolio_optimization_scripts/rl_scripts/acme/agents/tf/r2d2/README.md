# R2D2 - Recurrent Experience Replay in Distributed Reinforcement Learning

This folder contains an implementation of the R2D2 agent introduced in
([<PERSON><PERSON><PERSON><PERSON> et al., 2019]). This work builds upon the DQN algorithm
([<PERSON><PERSON><PERSON> et al., 2013], [<PERSON><PERSON><PERSON> et al., 2015]) and Ape-X framework ([<PERSON><PERSON> et al.,
2018]), extending distributed Q-Learning to use recurrent neural networks. This
version is a synchronous version of the agent, and is therefore not distributed.

[<PERSON><PERSON><PERSON><PERSON> et al., 2019]: https://openreview.net/forum?id=r1lyTjAqYX
[<PERSON><PERSON><PERSON> et al., 2013]: https://arxiv.org/abs/1312.5602
[<PERSON><PERSON><PERSON> et al., 2015]: https://www.nature.com/articles/nature14236
[<PERSON><PERSON> et al. 2018]: https://arxiv.org/pdf/1803.00933