# Behavioral Cloning (BC)

This folder contains an implementation for supervised learning of a policy from
a dataset of observations and target actions. This is an approach known as
Behavioral Cloning, introduced by [<PERSON><PERSON><PERSON><PERSON>, 1989]. There is an example which
generates data for bsuite environment `Deep Sea` using an optimal policy.

[<PERSON><PERSON><PERSON>u, 1989]: https://papers.nips.cc/paper/95-alvinn-an-autonomous-land-vehicle-in-a-neural-network.pdf