"""tests for paths."""

from unittest import mock

from acme.testing import test_utils
import acme.utils.paths as paths # pylint: disable=consider-using-from-import

from absl.testing import absltest

class PathTest(test_utils.TestCase):

    def test_process_path(self):
        root_directory = self.get_tempdir()
        with mock.patch.object(paths, 'get_unique_id') as mock_unique_id:
            mock_unique_id.return_value = ('test', )
            path = paths.process_path(root_directory, 'foo', 'bar')
            self.assertEqual(path, f'{root_directory}/test/foo/bar')
        
    def test_unique_id_with_flag(self):
        with mock.patch.object(paths, 'ACME_ID') as mock_acme_id:
            mock_acme_id.value = 'test_flag'
            self.assertEqual(paths.get_unique_id(), ('test_flag', ))

if __name__ == '__main__':
    absltest.main()