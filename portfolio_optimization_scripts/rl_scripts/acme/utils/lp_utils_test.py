"""tests for acme launchpad utilities."""

from acme.utils import lp_utils
from absl.testing import absltest


class LpUtilsTest(absltest.TestCase):

    def partial_kwargs(self):

        def foo(a, b, c=2):
            return a, b, c
        
        def bar(a, b):
            return a, b
        
        # override the default values. the last two should be no-ops.
        foo1 = lp_utils.partial_kwargs(foo, c=1)
        foo2 = lp_utils.partial_kwargs(foo)
        bar1 = lp_utils.partial_kwargs(bar)

        # check that we raise errors on overriding kwargs with no default values.
        with self.assertRaises(ValueError):
            lp_utils.partial_kwargs(foo, a=2)
        
        # check that we raise errors if we try to override a kwarg that doesn't exits.
        with self.assertRaises(ValueError):
            lp_utils.partial_kwargs(foo, d=2)
        
        # make sure we get back the correct values.
        self.assertEqual(foo1(1, 2), (1, 2, 1))
        self.assertEqual(foo2(1, 2), (1, 2, 2))
        self.assertEqual(bar1(1, 2), (1, 2))

if __name__ == '__main__':
    absltest.main()
