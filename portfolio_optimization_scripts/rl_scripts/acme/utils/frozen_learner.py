"""Frozen learner."""

from typing import Callable, List, Optional, Sequence

import acme

class FrozenLearner(acme.Learner):
    """wraps a learner ignoring the step calls, i.e. freezing it."""

    def __init__(self,
                 learner: acme.Learner,
                 step_fn: Optional[Callable[[], None]] = None):
        """Initializes the frozen learner.
        
        Args:
            learner: Learner to be wrapped.
            step_fn: Function to call instead of the step() method of the learner.
                This can be used e.g. to drop sample from an iterator that would 
                be normally consumed by the learner.
        """
        self._learner = learner
        self._step_fn = step_fn
    
    def step(self):
        """see base class."""
        if self._step_fn:
            self._step_fn()
    
    def run(self, num_steps: Optional[int]=None):
        """see base class"""
        self._learner.run(num_steps)
    
    def save(self):
        """see base class"""
        self._learner.save()
    
    def restore(self):
        """see base class."""
        self._learner.restore()
    
    def get_variables(self, names: Sequence[str]) -> List[acme.types.NestedArray]:
        """see base class"""
        return self._learner.get_variables(names)