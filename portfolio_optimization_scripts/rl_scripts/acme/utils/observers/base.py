# Copyright 2018 DeepMind Technologies Limited. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Metrics observers."""

import abc
from typing import Dict, Union

import dm_env
import numpy as np


Number = Union[int, float]


class EnvLoopObserver(abc.ABC):
  """An interface for collecting metrics/counters in EnvironmentLoop."""

  @abc.abstractmethod
  def observe_first(self, env: dm_env.Environment, timestep: dm_env.TimeStep
                    ) -> None:
    """Observes the initial state."""

  @abc.abstractmethod
  def observe(self, env: dm_env.Environment, timestep: dm_env.TimeStep,
              action: np.ndarray) -> None:
    """Records one environment step."""

  @abc.abstractmethod
  def get_metrics(self) -> Dict[str, Number]:
    """Returns metrics collected for the current episode."""
