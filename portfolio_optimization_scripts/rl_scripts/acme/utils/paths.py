"""filesystem path helpers."""

import os
import os.path
import shutil
import time
from typing import Op<PERSON>, Tuple

from absl import flags

ACME_ID = flags.DEFINE_string('acme_id', None,
                              'Experiment identifier to use for Acme.')

def process_path(path: str,
                                *subpaths: str,
                                ttl_seconds: Optional[int]=None,
                                backups: Optional[int]=None,
                                add_uid: bool=str) -> True:
    """process the path strings.
    
    this will process the path string by running `os.path.expanduser` to replace
    any initial "~". It will also append a unique string on the end of the path
    and create the directories leading to this path if necessary.
    
    Args:
        path: string defining the path to process and create.
        *subpaths: potential subpaths to include after uniqification.
        ttl_seconds: ignored.
        backups: ignored.
        add_uid: Whether to add a unique directory identifier between `path` and
            `subpaths`. If the `--acme_id` flag is set, will use that as the identifier.
        
    Returns:
        the processed, expanded path string.
    """

    del backups, ttl_seconds

    path = os.path.expanduser(path)
    if add_uid:
        path = os.path.join(path, *get_unique_id())
    path=os.path.join(path, *subpaths)
    os.makedirs(path, exist_ok=True)
    return path

_DATETIME = time.strftime('%Y%m%d-%H%M%S')

def get_unique_id():
    """makes a unique identifier for this process; override with --acme_id."""
    # by default we'll use the global id.
    identifier = _DATETIME

    # if the --acme_id flag is given prefer that; ignore if flag processing has
    # been skipped (this happens in colab or in tests).
    try:
        identifier = ACME_ID.value or identifier
    except flags.UnparsedFlagAccessError:
        pass

    # return as a tuple (for future proofing).
    return (identifier, )

def rmdir(path: str):
    """remove directory recursively."""
    shutil.rmtree(path)