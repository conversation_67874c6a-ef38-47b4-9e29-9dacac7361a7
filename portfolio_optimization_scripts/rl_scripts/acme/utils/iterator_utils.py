"""Iterator utilities."""
import itertools
import operator
from typing import Any, Iterator, List, Sequence


def unzip_iterators(zipped_iterators: Iterator[Sequence[Any]],
                    num_sub_iterators: int) -> List[Iterator[Any]]:
  """Returns unzipped iterators.

  Note that simply returning:
    [(x[i] for x in iter_tuple[i]) for i in range(num_sub_iterators)]
  seems to cause all iterators to point to the final value of i, thus causing
  all sub_learners to consume data from this final iterator.
  
  Args:
    zipped_iterators: zipped iterators (e.g., from zip_iterators()).
    num_sub_iterators: the number of sub-iterators in the zipped iterator.
  """
  iter_tuple = itertools.tee(zipped_iterators, num_sub_iterators)
  return [
      map(operator.itemgetter(i), iter_tuple[i])
      for i in range(num_sub_iterators)
  ]
