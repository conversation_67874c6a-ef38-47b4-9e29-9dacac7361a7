"""utility definitions for acme experiments."""

from typing import Optional

from acme.utils import loggers

def make_experiment_logger(label: str,
                           steps_key: Optional[str] = None,
                           task_instance: int =0) -> loggers.Logger:
    del task_instance
    if steps_key is None:
        steps_key = f'{label}_steps'
    return loggers.make_default_logger(label, steps_key=steps_key)

def create_experiment_logger_factory() -> loggers.LoggerFactory:
    return make_experiment_logger