"""Objects which specify the input/output spaces of an environment.

This module exposes the same spec classes as `dm_env` as well as providing an
additional `EnvironmentSpec` class which collects all of the specs for a given
environment. An `EnvironmentSpec` instance can be created directly or by using
the `make_environment_spec` helper given a `dm_env.Environment` instance.
"""

from typing import Any, NamedTuple

import dm_env
from dm_env import specs

Array = specs.Array
BoundedArray = specs.BoundedArray
DiscreteArray = specs.DiscreteArray

class EnvironmentSpec(NamedTuple):
    """Full specification of the domains used by a given environment."""
    # TODO(b/144758674): Use NestedSpec type here.
    observations: Any
    actions: Any
    rewards: Any
    discounts: Any

def make_environment_spec(environment: dm_env.Environment) -> EnvironmentSpec:
    """Returns a `EnironmentSpec` object describing the values used by an environment"""
    return EnvironmentSpec(
        observations=environment.observation_spec(),
        actions=environment.action_spec(),
        rewards=environment.reward_spec(),
        discounts=environment.discount_spec())
