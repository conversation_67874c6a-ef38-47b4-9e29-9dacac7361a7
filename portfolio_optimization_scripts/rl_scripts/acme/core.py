"""core acme interfaces.

This file speeifies and documents the notions of `Actor ` and `Learner`
"""

import abc
import itertools
from typing import Generic, Iterator, List, Optional, Sequence, TypeVar

from acme import types
from acme.utils import metrics
import dm_env

T=TypeVar('T')

@metrics.record_class_usage
class Actor(abc.ABC):
    """interface for an agent that can act.
    
    this interface defines an API for an actor to interact with an environmentloop
    (see acme.environment_loop), e.g. a simpel RL loop where each step is of the
    form: 
    
        # make a first observation.
        timestep = dm_env.reset()
        actor.observe_first()
        
        # take a step and observe
        action = actor.select_action(timestep.observation)
        next_timestep = env.step(action)
        actor.observe(action, next_timestep)
        
        # update the actor policy/parameters.
        actor.update()
    """

    @abc.abstractmethod
    def select_action(self, observation: types.NestedArray) -> types.NestedArray:
        """samples from the policy and returns an action."""

    @abc.abstractmethod
    def observe_first(self, timestep: dm_env.TimeStep):
        """make a first observation from the environment.
        
        note that this need not be an initial state, it merely begining the
        recording of a trajectory.
        
        Args:
            timestep: dm_env.Timestep
        """
    
    @abc.abstractmethod
    def observe(
        self,
        action: types.NestedArray,
        timestep: dm_env.TimeStep
    ):
        """makes an observation of timestep data from the environment.
        
        Args:
            action: action taken in the environment.
            next_timestep: timestep produced by the environment given the action.
        """
    
    @abc.abstractmethod
    def update(self, wait: bool = False):
        """perform an update of the actor parameters from past observations.
        
        Args:
            wait: if True, the update will be blocking.
        """
    
class VariableSource(abc.ABC):
    """Abstract source of variables.
    
    Objects which implements this interface provide a source of variables, returned
    as a collection of (nested) numpy arrays. generally this will be used to 
    provide variables to some learner policy/etc.
    """

    @abc.abstractmethod
    def get_variables(self, names: Sequence[str]) -> List[types.NestedArray]:
        """returns the named variables as a collection of (nested) numpy arrays.
        
        Args:
            names: args where each name is a string identifying a predefined subset of 
                the variables.
        
        Returns:
            A list of (nested) numpy arrays `variables` such that `variables[i]`
            corresponds to the collections named by `names[i]`
        """

@metrics.record_class_usage
class Worker(abc.ABC):
    """An interface for (potentially) distributed workers."""

    @abc.abstractmethod
    def run(self):
        """runs the worker."""

class Saveable(abc.ABC, Generic[T]):
    """an interface for saveable objects."""

    @abc.abstractmethod
    def save(self) -> T:
        """returns the state from the object to be saved."""
    
    @abc.abstractmethod
    def restore(self, state: T):
        """given the state, restores the object."""

class Learner(VariableSource, Worker, Saveable):
    """Abstract learner object.
    
    this corresponds to an object which implements a learning loop. A single step 
    of learning should be implemented via the `step` method and this step
    is generally interacted with via the `run` method that which runs update
    continuously.
    
    All objects implementing this interface should be able to take in an
    external dataset (see acme.datasets) and run updates using data from this
    dataset. This can be acomplished by explictly running `learner.step()`
    inside a for/while loop or by using the `learner.run()` conveniece function.
    Data will be read from this dataset asynchronously and this is primarily 
    useful when the dataset is filled by an external process.
    """

    @abc.abstractmethod
    def step(self):
        """perform an update step of the learner's parameters."""
    
    def run(self, num_steps: Optional[int] = None) -> None:
        """runs the update loop, typically an infinite loop which calls step."""

        iterator = range(num_steps) if num_steps is not None else itertools.count()

        for _ in iterator:
            self.step()
    
    def save(self):
        raise NotImplementedError('Method "save" is not implemented!')
    
    def restore(self):
        raise NotImplementedError('Method "restore" is not implemented!')

class PrefetchingIterator(Iterator[T], abc.ABC):
    """Abstract iterator which supports ready method."""

    @abc.abstractmethod
    def ready(self) -> bool:
        """is there any data waiting for processing."""
    
    @abc.abstractmethod
    def retrieved_elements(self) -> int:
        """how many elements were retrieved from the iterator."""