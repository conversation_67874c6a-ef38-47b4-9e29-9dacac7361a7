import os
import sys
import requests
from core import *
import numpy as np
import pandas as pd
script_dir = os.path.dirname(os.path.abspath(__file__))
resources_dir = os.path.join(script_dir, 'resource')

s3_bucket = 'micro-ops-output'
caps_key = 'test/varaprasad-rl/backtest/aipex-backtesting/daily-run-test/behaviour-cloning/caps.csv'
post_aipex_url = "http://*************:6334/rebalance?er_field=er&caps_field=caps&min_caps_field=min&market_cap_field=marketcap&tag=aipex"
post_aieq_url = "http://*************:6334/rebalance?er_field=er&caps_field=caps&min_caps_field=min&market_cap_field=marketcap&tag=aieq"

headers = {
    "Content-Type": "application/json"
}
true_return_path = os.path.join(resources_dir, 'actual-returns.npy')
train_dates_path = os.path.join(resources_dir, 'train-dates.npy')
expected_return_path = os.path.join(resources_dir, 'expected-returns.npy')
portfolio_template_path = os.path.join(resources_dir, 'portfolio-template.npy')

true_returns = np.load(true_return_path)
expected_returns = np.load(expected_return_path)
train_dates = np.load(train_dates_path)
portfolio_template = np.load(portfolio_template_path)

expected_return = expected_returns[-1]

max_caps = s3_reader.read_as_dataframe(s3_bucket, caps_key)
portfolio_t = pd.DataFrame({'isin': portfolio_template, 'er': expected_return})
portfolio = pd.merge(portfolio_t, max_caps.drop(columns=['er']), on=['isin'], how='left')
portfolio['er'].fillna(-100.0, inplace=True)
portfolio['caps'].fillna(0.0, inplace=True)
portfolio['marketcap'].fillna(0.0, inplace=True)
portfolio['date'] = portfolio['date'].ffill().bfill()
portfolio['min'] = 0.0
portfolio['date'] = train_dates[-1]
portfolio.to_csv('portfolio.csv')

def prepare_request(portfolio):
    portfolio_t = portfolio[['isin', 'marketcap', 'er', 'date', 'caps', 'min']]
    request = [row.to_dict() for _, row in portfolio_t.iterrows()]
    return request
    
payload = prepare_request(portfolio)
aipex_response = requests.post(post_aipex_url, json=payload, headers=headers)
aieq_response = requests.post(post_aieq_url, json=payload, headers=headers)

aipex_portfolio = pd.DataFrame(aipex_response.json())
aieq_portfolio = pd.DataFrame(aieq_response.json())

aipex_portfolio['date'] = pd.to_datetime(aipex_portfolio['date']).dt.strftime('%Y-%m-%d')
date = aipex_portfolio['date'].tolist()[0]

def postprocess(data_frame, ps=250, ws= 0.0002):
    
    max_market_cap = data_frame["marketcap"].max()
    data_frame["marketcap_comp"] = max_market_cap - data_frame["marketcap"]
    data_frame.loc[data_frame["caps"] == 0.0, "er"] = -100.0
    data_frame["marketcap_comp"] = data_frame["marketcap_comp"].apply(lambda marketcap: max(1, marketcap))
    data_frame = data_frame.sort_values(by=["weights", "er"], ascending=[False, False]).reset_index(drop=True)
    
    n = ps
    print(f"number of isins with non-zero weights --> {n}")
    while True:
        data_frame.loc[n:, "weights"] = 0
        extra_weight = 1 - data_frame["weights"].sum()
        total_market_cap = data_frame.loc[:(n-1), "marketcap_comp"].sum()
        data_frame.loc[:(n-1), "weights"] += (data_frame.loc[:(n-1), "marketcap_comp"] * extra_weight / total_market_cap)
        data_frame.loc[:(n-1), "weights"] = data_frame.loc[:(n-1), "weights"].apply(lambda _: max(_, ws))
        if (np.isclose(data_frame["weights"].sum(), 1)):
            break
    print('phase-I-completed!')
    print(data_frame.loc[:n])
            
    while True:
        data_frame = data_frame.sort_values(by=["weights", "er"], ascending=[False, False]).reset_index(drop=True)
        data_frame.loc[n:, "weights"] = 0
        extra_weight = 1 - data_frame["weights"].sum()
        total_market_cap = data_frame.loc[:(n-1), "marketcap"].sum()
        data_frame.loc[:(n-1), "weights"] += (data_frame.loc[:(n-1), "marketcap"] * extra_weight / total_market_cap)
        data_frame.loc[:(n-1), "weights"] = data_frame.loc[:(n-1), "weights"].apply(lambda _: max(_, ws))
        constraint_verification = sum(data_frame["weights"] <= data_frame["caps"])
        data_frame['weights'] = data_frame['weights'].where(data_frame['weights'] >= ws, 0)
        data_frame["weights"] = data_frame.apply(lambda row: min(row["weights"], row["caps"]), axis=1)
        if data_frame[data_frame["weights"] > 0]["caps"].sum() < 1:
            n += 1
        if (np.isclose(data_frame["weights"].sum(), 1)):
            break
    
    print('phase-II-completed!')
    
    return data_frame

aipex_portfolio = postprocess(aipex_portfolio)
aieq_portfolio = postprocess(aieq_portfolio, ps=160, ws=0.0005)
extra_weight = aipex_portfolio[aipex_portfolio['weights'] > 0]['weights'].sum() - 1
aipex_portfolio.loc[0, 'weights'] -= extra_weight
extra_weight = aieq_portfolio[aieq_portfolio['weights'] > 0]['weights'].sum() - 1
aieq_portfolio.loc[0, 'weights'] -= extra_weight

print("aipex-weight:", aipex_portfolio["weights"].sum())
print("aieq-weight:", aieq_portfolio["weights"].sum())
bucket = "eq-model-output"
AIPEX_portfolio_key = f'rl_portfolio_optimization/aipex/{date}/imitation-learning/aipex_portfolio.csv'
AIEQ_portfolio_key = f'rl_portfolio_optimization/aieq/{date}/imitation-learning/aieq_portfolio.csv'

s3_writer.write_as_dataframe(aipex_portfolio, bucket, AIPEX_portfolio_key)
s3_writer.write_as_dataframe(aieq_portfolio, bucket, AIEQ_portfolio_key)
print(email_manager.send_portfolio(aipex_portfolio, aieq_portfolio))