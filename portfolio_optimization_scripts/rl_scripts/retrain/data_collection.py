from .core.utils import *
from datetime import datetime

script_dir = os.path.dirname(os.path.abspath(__file__))
resource_dir = os.path.join(os.path.dirname(script_dir), 'resource')

start_year = int(config['es_data_properties']['start_year'])
es_index = config['es_data_properties']['es_index']
es_index = "pre_ttm_model"
def get_data(isin: str, date: str):
    year = int(date.split('-')[0])
    data = prod_es_manager.get_es_data(isin, [start_year, year], es_index)
    print(data)
    return data
def store_data(geo: str = 'tier1', date: str = '2025-01-01'):
    master_df = get_master(geo)
    return get_data(master_df['isin'].tolist()[0], date)

if __name__ == '__main__':
    store_data()
    