import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
rl_scripts_dir = os.path.dirname(script_dir)
sys.path.append(rl_scripts_dir)

import traceback
import logging
import warnings
import numpy as np
import timeit
from datetime import datetime
import jax.nn as jnn
import reverb
from reverb import structured_writer as sw
from acme.datasets import reverb as datasets
from acme.adders.reverb import base as reverb_base
from acme.adders.reverb import structured
from acme.jax.experiments import *
from typing import List, Optional, Callable, Iterator, Tuple
import gym
from gym import spaces
from acme.wrappers import GymWrapper
from acme import specs
from acme import types
import json
import boto3
import jax.numpy as jnp
from acme.adders import reverb as adders
from acme import specs
from acme import types
from acme.agents.jax import actor_core as actor_core_lib
from acme.agents.jax import bc
from acme.datasets import tfds
from acme.examples.baselines.imitation import helpers
from acme.jax import experiments
from acme.jax import types as jax_types
from acme.jax import utils
import dm_env
import haiku as hk
import numpy as np
from tqdm import tqdm

# Configure basic settings
logging.basicConfig(level=logging.INFO)
warnings.filterwarnings("ignore")

class S3Handler:
    def __init__(self):
        self.aws_access_key = '********************'
        self.aws_secret_key = 'bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/'
        self.bucket_key = 'micro-ops-output'
        self.ubs_prefix = 'test/ubs-testing/backtesting/agents/behavior-cloning/version-0.1/'
        self.data_prefix = f'test/aipex-testing/rebalancing/poc/agents/behavior-cloning/episode-lengths [u]'

    def read_arr_from_s3(self, bucket_name: str, file_name: str) -> Optional[np.ndarray]:
        s3_client = boto3.client('s3', 
                               aws_access_key_id=self.aws_access_key, 
                               aws_secret_access_key=self.aws_secret_key)
        try:
            response = s3_client.get_object(Bucket=bucket_name, Key=file_name)
            byte_stream = response['Body'].read()
            numpy_array = np.frombuffer(byte_stream)
            return numpy_array
        except Exception:
            return None

    def write_arr_to_s3(self, arr: np.ndarray, bucket_name: str, file_name: str) -> bool:
        s3_client = boto3.client('s3', 
                               aws_access_key_id=self.aws_access_key, 
                               aws_secret_access_key=self.aws_secret_key)
        bytes_stream = arr.tobytes()
        s3_client.put_object(Bucket=bucket_name, Key=file_name, Body=bytes_stream)
        return True

    def _get_client(self):
        return boto3.client('s3', 
                          aws_access_key_id=self.aws_access_key, 
                          aws_secret_access_key=self.aws_secret_key)

    def store_json(self, object_data: dict, portfolio_date: str, component: str = 'policy') -> None:
        s3_client = self._get_client()
        json_data = json.dumps(object_data, cls=CustomJsonEncoder)
        key = self.data_prefix + f'/{portfolio_date}/{component}.json'
        s3_client.put_object(Bucket=self.bucket_key, Key=key, Body=json_data)
        
class CustomJsonEncoder(json.JSONEncoder):
  def default(self, obj):
    if isinstance(obj, (jnp.ndarray)):
      return obj.tolist()
    elif hasattr(obj, '__dict__'):
      return obj.__dict__
    return super().default(obj)

class DataLoader:
    @staticmethod
    def load_experiment_data():
        return {
            'expected_returns': np.load('cat-returns.npy'),
            'actual_returns': np.load('true-returns.npy'),
            'focus_mask': np.load('cat_masks.npy'),
            'bdates': np.load('train-dates.npy'),
            'pdates': np.load('ubs-test-dates.npy')
        }
        
class PortfolioOptimizationEnv(gym.Env):
    def __init__(self, terminal_state: int, portfolio_size: int = 974, max_cap: float = 1, episode_length: int = 44):
        
        print("portfolio-size: ", portfolio_size)
        self._terminal_state = terminal_state
        self._portfolio_size = portfolio_size
        self._max_cap = max_cap
        
        self.observation_space = spaces.Box(-np.inf, np.inf, shape=(2*portfolio_size,), dtype=np.float64)
        self.action_space = spaces.Box(0, max_cap, shape=(portfolio_size,), dtype=np.float64)
        self._episode_length = episode_length
        self._initialize_state()
    
    def _initialize_state(self):
        self._state_id = self._terminal_state - self._episode_length
        self._er = expected_returns[self._state_id]
        self._ar = actual_returns[self._state_id]
        self._caps = np.random.uniform(low=0.0, high=self._max_cap, size=self._portfolio_size)
        self._state = np.concatenate([self._er, self._caps], axis=0)
    
    def reset(self):
        self._initialize_state()
        return self._state
    
    def step(self, action):
        reward = PortfolioOptimizer.compute_return(self._state, self._ar, self._caps, action)
        done = self._state_id >= self._terminal_state
        
        if done:
            self._state_id = self._terminal_state - self._episode_length
        else:
            self._state_id += 1
            
        self._er = expected_returns[self._state_id]
        self._ar = actual_returns[self._state_id]
        self._caps = np.random.uniform(low=0.0, high=self._max_cap, size=self._portfolio_size)
        self._state = np.concatenate([self._er, self._caps], axis=0)

        return self._state, reward, done, {}

class PortfolioOptimizer:
    @staticmethod
    def compute_return(obs, ar, caps, action):
        weights = action * caps
        portfolioreturn = np.dot(ar, weights)
        totalweight = np.sum(weights)
        if totalweight > 1:
            portfolioreturn = portfolioreturn / totalweight
        theoritical_max_return = PortfolioOptimizer._compute_max_return(ar, caps)
        if theoritical_max_return == 0.0:
            theoritical_max_return = portfolioreturn
        return portfolioreturn / theoritical_max_return

    @staticmethod
    def _compute_max_return(er, caps):
        portfolio_size = er.shape[0]
        weights = np.zeros(shape=(portfolio_size,))
        total_weight = 0
        ranks = np.argsort(-er)
        
        for rank in ranks:
            if (er[rank] <= 0) or total_weight == 1:
                break
            weights[rank] = min(1 - total_weight, caps[rank])
            total_weight += weights[rank]
            
        return np.dot(weights, er) + 1e-3 if total_weight >= 0.1 else 0.0

    @staticmethod
    def policy(er, caps):
        
        portfolio_size = er.shape[0]
        weights = np.zeros(shape=(portfolio_size,))
        total_weight = 0
        ranks = np.argsort(-er)

        for rank in ranks:
            if total_weight == 1:
                break
            
            weight = min(1 - total_weight, caps[rank])
            total_weight += weight
            weights[rank] = weight / caps[rank]
        
        return weights

def _make_adder_config(
    step_spec: reverb_base.Step,
    sequence_length: int,
    period: int,
    table: str) -> List[sw.Config]: # type: ignore

    return structured.create_sequence_config(
        step_spec=step_spec, sequence_length=sequence_length, period=period, table=table)

def get_environment(portfolio_date):
	# Environment (Simulation) for rl agent to work on.
	monthly_period = 22
	portfolio_size = expected_returns.shape[1]
	cap = 0.25
	
	logging.info(f'creating environment for portfolio-date: {portfolio_date}')
	terminal_state = sum(date < portfolio_date for date in bdates) - monthly_period
	logging.info(f'terminal-state for portfolio-date: {terminal_state}')

	environment = GymWrapper(PortfolioOptimizationEnv(
														terminal_state=terminal_state,
														portfolio_size=portfolio_size,
														max_cap=cap))
	return environment

def ideal_actor(environment, table_name, num_episodes=5000, num_demonstrations=60000):
    global server
    environment_spec = specs.make_environment_spec(environment)
    step_spec = adders.create_step_spec(environment_spec=environment_spec)
    
    sequence_configs = _make_adder_config(
							step_spec,
							sequence_length=2,
							period=1,
							table=table_name)
    
    signature = sw.infer_signature(
						configs=sequence_configs,
						step_spec=step_spec)
    
    replay_table = reverb.Table.queue(table_name, max_size=100000000, signature=signature)
    server = reverb.Server([replay_table])
    client = reverb.Client(f'localhost:{server.port}')
    priority_fns = {table_name: lambda x: 1.0}

    adder = adders.SequenceAdder(
		        client=client,
		        sequence_length=2,
		        period=1,
                priority_fns=priority_fns,
		        end_of_episode_behavior=adders.EndBehavior.TRUNCATE)

    for episode in tqdm(range(num_episodes)):
        first = environment.reset()
        adder.add_first(first)
        timestep = first
        while not (timestep.last()):
            obs = timestep.observation
            action = PortfolioOptimizer.policy(environment._ar, environment._caps)
            timestep = environment.step(action)
            adder.add(action, next_timestep=timestep, extras=())
    
    server_address = f'localhost:{server.port}'

    def convert_sample(sample):
        info = sample.info
        data = sample.data
        
        return types.Transition(
            observation=data.observation[0],
            action=data.action[0],
            reward=data.reward[0],
            discount=data.discount[0],
            next_observation=data.observation[1],
            extras=(),
        )
    
    dataset = datasets.make_reverb_dataset(
                table = table_name,
                server_address=server_address,
                num_parallel_calls=None
            )
    dataset = dataset.map(convert_sample)
    dataset = dataset.take(num_demonstrations)
    print(type(dataset))
    return dataset

def run_experiment(portfolio_date: str):
    # Implementation of experiment runner

    # get the simulator for rl-agent to work with
    environment = get_environment(portfolio_date)
    dataset = ideal_actor(environment=environment, table_name=portfolio_date)

    def _make_demonstration_dataset_factory(
        num_demonstrations: int,
        dataset, batch_size: int,
    ) -> Callable[[jax_types.PRNGKey], Iterator[types.Transition]]:
        def demonstration_dataset_factory(
            random_key: jax_types.PRNGKey) -> Iterator[types.Transition]:
            """returns an iterator of demonstration samples."""
            return tfds.JaxInMemoryRandomSampleIterator(
                dataset, key=random_key, batch_size=batch_size)
        return demonstration_dataset_factory

    def _make_environment_factory() -> jax_types.EnvironmentFactory:
        """returns a environment factory for the given environment"""
        
        def environment_factory(seed: int) -> dm_env.Environment:
            del seed
            environment = get_environment(portfolio_date)
            return environment
        return environment_factory

    def _make_network_factory(
        shift: Tuple[np.float64], scale: Tuple[np.float64], num_layers: int,
        num_units: int,
        dropout_rate: float) -> Callable[[specs.EnvironmentSpec], bc.BCNetworks]:
        def network_factory(spec: specs.EnvironmentSpec) -> bc.BCNetworks:
            action_spec = spec.actions
            num_dimensions = np.prod(action_spec.shape, dtype=int)
            def actor_fn(obs, is_training=False, key=None):
                hidden_layers = [num_units] * num_layers
                mlp = hk.Sequential([
                    hk.nets.MLP(hidden_layers + [num_dimensions]),
                    jnn.tanh,
                ])

                if is_training:
                    output = mlp(obs, dropout_rate=dropout_rate, rng=key)
                    return output
                else:
                    output = mlp(obs)
                    return output

            policy = hk.without_apply_rng(hk.transform(actor_fn))

            # Create dummy observations to create network parameters.
            dummy_obs = utils.zeros_like(spec.observations)
            dummy_obs = utils.add_batch_dim(dummy_obs)

            policy_network = bc.BCPolicyNetwork(lambda key: policy.init(key, dummy_obs),
                                                policy.apply)

            return bc.BCNetworks(policy_network=policy_network)
        return network_factory

    data_stats = {}
    # hyper-parameters.
    batch_size = 64
    num_demonstrations = 20000

    def build_experiment_config() -> experiments.OfflineExperimentConfig[
        bc.BCNetworks, actor_core_lib.FeedForwardPolicy, types.Transition]:
        
        environment = get_environment(portfolio_date)
        environment_spec = specs.make_environment_spec(environment)
        demonstration_dataset_factory = _make_demonstration_dataset_factory(
            num_demonstrations=num_demonstrations, 
            dataset=dataset, batch_size=batch_size)
        
        shift, scale = helpers.get_observation_stats(dataset)
        
        data_stats['shift'] = shift.tolist()
        data_stats['scale'] = scale.tolist()
        
        network_factory = _make_network_factory(  # pytype: disable=wrong-arg-types  # numpy-scalars
        shift=shift,
        scale=scale,
        num_layers=3,
        num_units=1024,
        dropout_rate=0.1)
        
        bc_config = bc.BCConfig(learning_rate=1e-4)
        bc_builder = bc.BCBuilder(bc_config, loss_fn=bc.mse())
        environment_factory = _make_environment_factory()
        
        return experiments.OfflineExperimentConfig(
        builder=bc_builder,
        network_factory=network_factory,
        demonstration_dataset_factory=demonstration_dataset_factory,
        environment_factory=environment_factory,
        max_num_learner_steps=20000,
        seed=0,
        environment_spec=environment_spec)
    
    start_time=timeit.default_timer()
    config=build_experiment_config()
    s3_helper=S3Handler()
    s3_helper.store_json(data_stats, portfolio_date, 'data-stats')
    try:
        logging.info('starting the offline experiment...')
        experiments.run_offline_experiment(
                experiment=config,
                portfolio_date=portfolio_date,
                agent_type='aipex',
                eval_every=4000,
                num_eval_episodes=2)
    except:
        logging.info('failed to run the experiment', traceback.format_exc())
    end_time = timeit.default_timer()
    logging.info(f'The experiment for {portfolio_date} ran in {end_time - start_time} seconds.')

resource_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resource')
expected_return_path = os.path.join(resource_dir, 'expected-returns.npy')
actual_return_path = os.path.join(resource_dir, 'actual-returns.npy')
train_dates_path = os.path.join(resource_dir, 'train-dates.npy')

expected_returns = np.load(expected_return_path)
actual_returns = np.load(actual_return_path)
bdates = np.load(train_dates_path)