email:
  password: ldni kmvg txji ejtb
  credentials: 'credentials.json'

urls:
  masters_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/
  snp_url: http://52.2.217.192:8080//stockhistory/getstocksbyisin
  spglobal_url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json

data_sources:
  closeprice: eq_financial_model
  expected_return: eq_er_model
  model_predictions: eq_ttm_model

metrics_settings:
  req_columns: ["isin", "date", "closeprice", "volume", "schedular", "tag", "updated_at"]
  all_metrics: ["mean_absolute_error", "mean_squared_error", "root_mean_squared_error", "r2_score", "adjusted_r2_score", "total_perc_diff", "abs_total_diff", "total_variance_perc_diff", "abs_total_variance_perc_diff",  "correlation_score", "directionality_score", "mean_directionality", "confidence_score", "avg_confidence_score", "accuracy_14_day", "accuracy_22_day", "accuracy_1_day"]
  rolling_period: 22

snp_creds:
  snp_function: GDSHE
  close_mnemonic: IQ_CLOSEPRICE
  price_check_mnemoic: IQ_PRICEDATE
  period_type: IQ_FY
  frequency: Daily
  head_auth: Basic ************************************************
  content_type: application/json

prod_version_control: &prod_version_control_anchor
  s3_bucket: eq-model-output
  s3_model_bucket_key: eq-training-dump
  s3_destination_data_bucket: eq-model-output
  s3_source_data_bucket: eq-model-output
  s3_predictions_prefix: ttm_model/schedular/date/predictions/
  s3_metrics_prefix: ttm_model/schedular/date/metrics/
  s3_model_prefix: ttm_model/schedular/data_dump/model_year/
  s3_features_prefix: ttm_model/schedular/date/features/
  s3_source_data_prefix: ttm_model/data_dump/
  s3_destination_data_prefix: ttm_model/data_dump/
  s3_meta_data_prefix: ttm_model/schedular/date/

preprod_version_control:
  s3_bucket: eq-pre-model-output
  s3_model_bucket_key: eq-training-dump
  s3_destination_data_bucket: eq-pre-model-output
  s3_source_data_bucket: eq-model-output
  s3_predictions_prefix: ttm_model/schedular/date/predictions/
  s3_metrics_prefix: ttm_model/schedular/date/metrics/
  s3_model_prefix: ttm_model/schedular/data_dump/model_year/
  s3_features_prefix: ttm_model/schedular/date/features/
  s3_source_data_prefix: ttm_model/data_dump/
  s3_destination_data_prefix:  ttm_model/data_dump/
  s3_meta_data_prefix: ttm_model/schedular/date/

prod_es_stores:
  predictions: eq_ttm_model
  metrics: eq_ttm_model_metrics

preprod_es_stores:
  predictions: pre_ttm_model
  metrics: pre_ttm_model_metrics

schedulers:
  monthly: Monthly
  daily: Daily
  quarterly: Quarterly

scheduler_periods:
  monthly: 22
  daily: 2
  quarterly: 66

old_scheduler_periods:
  monthly: 22
  daily: 1
  quarterly: 66

model_settings:
  context_length: 1024
  buffer_length: 2000
  model_year: 2024_Q4
  ttm_model_revision: 1024_96_v1
  head_dropout_rate: 0.1

process_settings:
  sector_map: sector_map.json
  ts_dict: ts_dict.json

es_data_properties:
  start_year: 2009
  es_index: eq_cat_er_model