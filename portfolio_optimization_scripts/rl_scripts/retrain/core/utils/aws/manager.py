import abc
from abc import ABC

from .reader import *
from .writer import *
from ...config import *
from ...read_yaml import *

class Generic_S3_Manager(abc.ABC):
    def __init__(self, **kwargs):
        self.s3_writer = s3_writer
        self.s3_reader = s3_reader
        self.s3_helper = s3_config()

        for key, value in kwargs.items():
            setattr(self, key, value)


    @abc.abstractmethod
    def write_predictions(self, model_predictions_df, date, isin):
        pass

    @abc.abstractmethod
    def write_metrics(self, model_metrics_df, date, isin):
        pass

    @abc.abstractmethod
    def read_data(self, isin):
        pass

    @abc.abstractmethod
    def write_data(self, data_frame_df, isin):
        pass

    @abc.abstractmethod
    def write_features(self, data_frame_df, date, isin):
        pass

    @abc.abstractmethod
    def read_model(self, model, model_key):
        pass

    @abc.abstractmethod
    def upload_file(self, local_key, date, prefix=None):
        pass

class prod_S3_Manager(Generic_S3_Manager, ABC):

    def __init__(self, **kwargs):

        self.schedular = None
        super().__init__(**kwargs)
        self.s3_config = prod_config
        self.check()

    def check(self):
        if hasattr(self, "schedular"):
            return
        self.schedular = schedulers["monthly"]
    
    def check_file(self, s3_bucket_key, s3_path_key):
        return self.s3_reader.check_file(s3_bucket_key, s3_path_key)

    def write_predictions(self, model_predictions_df, date, isin):
        s3_bucket_key = self.s3_config.s3_bucket
        s3_predictions_prefix = self.s3_config.s3_predictions_prefix.replace("schedular", self.schedular).replace("date", date)
        s3_predictions_key = s3_predictions_prefix + f'{isin}.csv'
        done = self.s3_writer.write_as_dataframe(model_predictions_df, s3_bucket_key, s3_predictions_key)
        return done

    def write_metrics(self, model_metrics_df, date, isin):
        s3_bucket_key = self.s3_config.s3_bucket
        s3_metrics_prefix = self.s3_config.s3_metrics_prefix.replace("schedular", self.schedular).replace("date", date)
        s3_metrics_key = s3_metrics_prefix + f"{isin}.csv"
        done = self.s3_writer.write_as_dataframe(model_metrics_df, s3_bucket_key, s3_metrics_key)
        return done

    def read_data(self, isin):
        s3_bucket_key = self.s3_config.s3_source_data_bucket
        s3_source_data_prefix = self.s3_config.s3_source_data_prefix
        s3_source_data_key = s3_source_data_prefix + f"{isin}.csv"
        data_frame_df = self.s3_reader.read_as_dataframe(s3_bucket_key, s3_source_data_key)
        return data_frame_df

    def write_data(self, data_frame_df, isin):
        s3_bucket_key = self.s3_config.s3_destination_data_bucket
        s3_destination_data_prefix = self.s3_config.s3_destination_data_prefix
        s3_destination_data_key = s3_destination_data_prefix + f"{isin}.csv"
        done = self.s3_writer.write_as_dataframe(data_frame_df, s3_bucket_key, s3_destination_data_key)
        return done

    def write_features(self, data_frame_df, date, isin):
        s3_bucket_key = self.s3_config.s3_bucket_key
        s3_features_prefix = self.s3_config.s3_features_prefix.replace("schedular", self.schedular).replace("date", date)
        s3_features_key = s3_features_prefix + f"{isin}.csv"
        done = self.s3_writer.write_as_dataframe(data_frame_df, s3_bucket_key, s3_features_key)
        return done

    def read_model(self, model, model_key):
        s3_model_bucket_key = self.s3_config.s3_model_bucket_key
        s3_model_key = model_key
        print(f"reading the model: {s3_model_bucket_key}")
        model = self.s3_reader.read_as_model(model, s3_model_bucket_key, s3_model_key)
        return model

    def upload_file(self, local_key, date, prefix=None):
        s3_bucket_key = self.s3_config.s3_bucket
        s3_meta_data_prefix = self.s3_config.s3_meta_data_prefix.replace("schedular", self.schedular).replace("date", date)
        name = local_key.split('/')[-1]
        s3_meta_data_key = s3_meta_data_prefix + f"{prefix}_{name}"
        done = self.s3_writer.upload_file(local_key, s3_bucket_key, s3_meta_data_key)
        return done

class preprod_S3_Manager(Generic_S3_Manager, ABC):

    def __init__(self, **kwargs):

        self.schedular = None
        super().__init__(**kwargs)
        self.s3_config = preprod_config
        self.check()

    def check(self):
        if hasattr(self, "schedular"):
            return
        self.schedular = schedulers["monthly"]
        
    def check_file(self, s3_bucket_key, s3_path_key):
        return self.s3_reader.check_file(s3_bucket_key, s3_path_key)
    
    def write_predictions(self, model_predictions_df, date, isin):
        s3_bucket_key = self.s3_config.s3_bucket
        s3_predictions_prefix = self.s3_config.s3_predictions_prefix.replace("schedular", self.schedular).replace("date", date)
        s3_predictions_key = s3_predictions_prefix + f'{isin}.csv'
        done = self.s3_writer.write_as_dataframe(model_predictions_df, s3_bucket_key, s3_predictions_key)
        return done

    def write_metrics(self, model_metrics_df, date, isin):
        s3_bucket_key = self.s3_config.s3_bucket
        s3_metrics_prefix = self.s3_config.s3_metrics_prefix.replace("schedular", self.schedular).replace("date", date)
        s3_metrics_key = s3_metrics_prefix + f"{isin}.csv"
        done = self.s3_writer.write_as_dataframe(model_metrics_df, s3_bucket_key, s3_metrics_key)
        return done

    def read_data(self, isin):
        s3_bucket_key = self.s3_config.s3_source_data_bucket
        s3_source_data_prefix = self.s3_config.s3_source_data_prefix
        s3_source_data_key = s3_source_data_prefix + f"{isin}.csv"
        data_frame_df = self.s3_reader.read_as_dataframe(s3_bucket_key, s3_source_data_key)
        return data_frame_df

    def write_data(self, data_frame_df, isin):
        s3_bucket_key = self.s3_config.s3_destination_data_bucket
        s3_destination_data_prefix = self.s3_config.s3_destination_data_prefix
        s3_destination_data_key = s3_destination_data_prefix + f"{isin}.csv"
        done = self.s3_writer.write_as_dataframe(data_frame_df, s3_bucket_key, s3_destination_data_key)
        return done

    def write_features(self, data_frame_df, date, isin):
        s3_bucket_key = self.s3_config.s3_bucket_key
        s3_features_prefix = self.s3_config.s3_features_prefix.replace("schedular", self.schedular).replace("date", date)
        s3_features_key = s3_features_prefix + f"{isin}.csv"
        done = self.s3_writer.write_as_dataframe(data_frame_df, s3_bucket_key, s3_features_key)
        return done

    def read_model(self, model, model_key):
        s3_model_bucket_key = self.s3_config.s3_model_bucket_key
        s3_model_key = model_key
        print(f"reading the model: {s3_model_bucket_key}")
        model = self.s3_reader.read_as_model(model, s3_model_bucket_key, s3_model_key)
        return model

    def upload_file(self, local_key, date, prefix=None):
        s3_bucket_key = self.s3_config.s3_bucket
        s3_meta_data_prefix = self.s3_config.s3_meta_data_prefix.replace("schedular", self.schedular).replace("date", date)
        name = local_key.split('/')[-1]
        s3_meta_data_key = s3_meta_data_prefix + f"{prefix}_{name}"
        done = self.s3_writer.upload_file(local_key, s3_bucket_key, s3_meta_data_key)
        return done


prod_s3_managers = {}
preprod_s3_managers = {}

for schedular_key, schedular in schedulers.items():
    prod_s3_managers[schedular_key] = prod_S3_Manager(schedular=schedular.lower())
    preprod_s3_managers[schedular_key] = preprod_S3_Manager(schedular=schedular.lower())

s3_managers = {"prod": prod_s3_managers, "preprod": preprod_s3_managers}
