import os
from string import Template

script_dir = os.path.dirname(os.path.abspath(__file__))
template_dir = os.path.join(script_dir, "templates")

template = os.path.join(template_dir, "trigger.txt")
    
def extract_subject_and_body(file_path):
    subject = None
    body = []
    with open(file_path, 'r') as file:
        for line in file:
            # Extract the subject
            if line.startswith("Subject:"):
                subject = line[len("Subject:"):].strip()
            # Collect the body (skip empty lines and the subject line)
            elif line.strip() and not line.startswith("Subject:"):
                body.append(line.strip())
    return subject, Template("\n".join(body))

subject, template = extract_subject_and_body(template)
print(template.substitute(geo="aieq", schedular="Monthly"))