from .base import *
import pandas as pd
from string import Template
from bs4 import BeautifulSoup

script_dir = os.path.dirname(os.path.abspath(__file__))
template_dir = os.path.join(script_dir, "templates")

class EmailManager:
    
    def __init__(self, creds: str = creds_path, ebook: str = yaml_path):
        """
        Initialize the EmailManager with credentials and configuration paths.
        
        :param creds: Path to the credentials file.
        :param ebook: Path to the YAML file containing email configurations.
        """
        self.creds = creds
        self.ebook = ebook
        self.email_client = EmailClient(creds, ebook)
    
    def send_portfolio(self, aipex_portfolio: pd.DataFrame, aieq_portfolio: pd.DataFrame):
        
        date = aipex_portfolio['date'].tolist()[0]
        aipex_portfolio.to_csv('aipex_portfolio.csv')
        aieq_portfolio.to_csv("aieq_portfolio.csv")
        
        portfolio_template = os.path.join(template_dir, "daily_portfolio.txt")
        subject, template = self._extract_subject_and_body_from_html(portfolio_template)
        template = Template(template)
        message_txt = template.substitute(date=date)
        to = ["nagasai", "nikhil", "chida"]
        to = [self.email_client.ebook[recipient]["email"] for recipient in to]
        self.email_client.send_message(to, subject, message_txt, attachments=["aipex_portfolio.csv", "aieq_portfolio.csv"])
        return portfolio_template
    
    def trigger(self, geo: str, schedular: str):
        trigger_template = os.path.join(template_dir, "trigger.txt")
        subject, template = self._extract_subject_and_body_from_html(trigger_template)
        template = Template(template)
        message_txt = template.substitute(geo=geo, schedular=schedular)
        to = ["nagasai", "nikhil"]
        to = [self.email_client.ebook[recipient]["email"] for recipient in to]
        self.email_client.send_message(to, subject, message_txt)
    
    def notify_failure(self, geo: str, schedular: str, error: str):
        trigger_template = os.path.join(template_dir, "failed_run.txt")
        subject, template = self._extract_subject_and_body_from_html(trigger_template)
        template = Template(template)
        message_txt = template.substitute(geo=geo, schedular=schedular, error=error)
        to = ["nagasai", "nikhil"]
        to = [self.email_client.ebook[recipient]["email"] for recipient in to]
        self.email_client.send_message(to, subject, message_txt)
    
    def notify_status(self, geo: str, schedular: str, status: dict):
        trigger_template = os.path.join(template_dir, "run_status.txt")
        subject, template = self._extract_subject_and_body_from_html(trigger_template)
        template = Template(template)
        message_txt = template.substitute(geo=geo, schedular=schedular, **status)
        to = ["nagasai", "nikhil", "sanchit", "shashi", "sukriti", "garima", "ekatmika"]
        to = [self.email_client.ebook[recipient]["email"] for recipient in to]
        self.email_client.send_message(to, subject, message_txt)
    
    def warn_ffilled_features(self, geo: str, schedular: str, attachments: List[str], priority: int):
        if priority:
            trigger_template = os.path.join(template_dir, "feature_ffill_check (H).txt")
        else:
            trigger_template = os.path.join(template_dir, "feature_ffill_check (L).txt")

        subject, template = self._extract_subject_and_body(trigger_template)
        template = Template(template)
        message_txt = template.substitute(geo=geo, schedular=schedular)
        to = ["nagasai"]
        to = [self.email_client.ebook[recipient]["email"] for recipient in to]
        self.email_client.send_message(to, subject, message_txt, attachments)
    
    def warn_predictions(self, geo: str, schedular: str, attachments: List[str]):
        trigger_template = os.path.join(template_dir, "predictions_check.txt")
        subject, template = self._extract_subject_and_body(trigger_template)
        template = Template(template)
        message_txt = template.substitute(geo=geo, schedular=schedular)
        to = ["nagasai"]
        to = [self.email_client.ebook[recipient]["email"] for recipient in to]
        self.email_client.send_message(to, subject, message_txt, attachments)
        
    def _extract_subject_and_body(self, template):
        subject = None
        body = []
        with open(template, 'r') as file:
            for line in file:
                # Extract the subject
                if line.startswith("Subject:"):
                    subject = line[len("Subject:"):].strip()
                # Collect the body (skip empty lines and the subject line)
                elif line.strip() and not line.startswith("Subject:"):
                    body.append(line)
        return subject, "\n".join(body)
    
    def _extract_subject_and_body_from_html(self, template):
        """
        Extracts the subject from the first <h2> tag and the body text excluding <h2> from the HTML content.
        Returns a tuple: (subject, body)
        """
        with open(template, 'r') as html_content:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Extract and remove <h2> tag (subject)
            h2_tag = soup.find('h2')
            subject = h2_tag.get_text(strip=True) if h2_tag else None
            if h2_tag:
                h2_tag.decompose()

            # Extract body text (excluding <h2>)
            body = str(soup.find('div'))

            return subject, body


email_manager = EmailManager()
