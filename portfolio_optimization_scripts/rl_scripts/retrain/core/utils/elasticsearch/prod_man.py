import json
import pandas as pd
import traceback
from .base import *
from eq_common_utils.utils.config.es_config import es_config


class ProdEsManager(Generic_es_Manager):
    def __init__(self, env="prod"):
        self._env = env
        self._client = es_config(env="prod")

    def get_es_data(self, isin, years, prefix):
        start_year, end_year = years
        data = []

        for year in range(start_year, end_year + 1):
            query = self._build_query(isin)
            try:
                result, _ = self._client.search_with_pagination(
                    query=json.loads(query),
                    index=f"{prefix}_{year}",
                    paginate=False,
                    strict=False,
                )
                data.extend(self._extract_hits(result))
            except Exception as e:
                print(f"Failed to fetch data for ISIN: {isin}, Year: {year}, Prefix: {prefix}. Error: {e}")
                traceback.print_exc()

        if not data:
            print(f"No data found for ISIN: {isin}, Index: {prefix} between {start_year} and {end_year}.")
            return None

        return self._prepare_dataframe(data)

    def get_values_from_es(self, isin, date, index, schedular):
        year = pd.to_datetime(date).year
        data_frame_df = self.get_es_data(isin, [year, year], index)

        if data_frame_df is not None:
            filtered_df = data_frame_df[
                (data_frame_df["schedular"] == schedular)
                & (pd.to_datetime(data_frame_df["date"]) == pd.to_datetime(date))
            ]
            return filtered_df.reset_index(drop=True)

        return None

    def get_es_data_by_date(self, date, index, date_format="%Y-%m-%d"):
        data = []
        date_str = pd.to_datetime(date).strftime(date_format)
        query = self._build_date_query(date_str)
        year = pd.to_datetime(date).year

        try:
            result, _ = self._client.search_with_pagination(
                query=json.loads(query),
                index=f"{index}_{year}",
                paginate=False,
                strict=False,
            )
            data.extend(self._extract_hits(result))
        except Exception as e:
            print(f"Error querying index: {index}, Query: {query}. Error: {e}")
            return None

        if not data:
            print(f"No data found for Date: {date} on Index: {index}.")
            return None

        return self._prepare_dataframe(data)
    
    def save_df_to_es(self, data, index):
        self._client.save_records_v2(data, index)

    @staticmethod
    def _build_query(isin):
        return json.dumps(
            {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "bool": {
                                    "should": [{"match": {"isin": isin}}],
                                }
                            }
                        ]
                    }
                }
            }
        )

    @staticmethod
    def _build_date_query(date_str):
        return json.dumps(
            {
                "query": {
                    "bool": {
                        "must": [{"match": {"date": date_str}}],
                    }
                }
            }
        )

    @staticmethod
    def _extract_hits(result):
        return [hit["_source"] for hit in result]

    @staticmethod
    def _prepare_dataframe(data):
        df = pd.DataFrame(data)
        df["date"] = pd.to_datetime(df["date"])
        return df.sort_values(by="date", ascending=True).reset_index(drop=True)


prod_es_manager = ProdEsManager()
