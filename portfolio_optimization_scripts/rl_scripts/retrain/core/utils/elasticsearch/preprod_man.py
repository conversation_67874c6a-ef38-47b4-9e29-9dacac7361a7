import json
import pandas as pd
import traceback
from .base import Generic_es_Manager
from eq_common_utils.utils.config.es_config import es_config


#TODO: provide options to download only specific fields data.
class PreprodEsManager(Generic_es_Manager):
    def __init__(self, env="pre"):
        self._env = env
        self._read_client =  es_config("prod")
        self._client = es_config(env)

    def get_es_data(self, isin, years, prefix):
        start_year, end_year = years
        data = []

        for year in range(start_year, end_year + 1):
            query = self._build_query(isin)
            try:
                result = self._fetch_data(query, f"{prefix}_{year}")
                data.extend(self._extract_sources(result))
            except Exception as e:
                self._log_error(isin, year, prefix, e)
                traceback.print_exc()

        return self._process_data(data, isin, prefix, start_year, end_year)

    def get_values_from_es(self, isin, date, index, schedular):
        year = pd.to_datetime(date).year
        data_frame = self.get_es_data(isin, [year, year], index)

        if data_frame is not None:
            return self._filter_data(data_frame, schedular, date)
        return None

    def get_es_data_by_date(self, date, index, date_format="%Y-%m-%d"):
        data = []
        query = self._build_date_query(date, date_format)
        year = pd.to_datetime(date).year

        try:
            result = self._fetch_data(query, f"{index}_{year}")
            data.extend(self._extract_sources(result))
        except Exception as e:
            self._log_date_error(index, query, e)

        return self._process_date_data(data, date, index)
    
    def save_df_to_es(self, data, index):
        self._client.save_records_v2(data, index)

    def _build_query(self, isin):
        return {
            "query": {
                "bool": {
                    "must": [
                        {"bool": {"should": [{"match": {"isin": isin}}]}}
                    ]
                }
            }
        }

    def _build_date_query(self, date, date_format):
        date_str = pd.to_datetime(date).strftime(date_format)
        return {
            "query": {
                "bool": {
                    "must": [{"match": {"date": date_str}}]
                }
            }
        }

    def _fetch_data(self, query, index):
        result, _ = self._read_client.search_with_pagination(
            query=query, index=index, paginate=False, strict=False
        )
        return result

    def _extract_sources(self, result):
        return [rs["_source"] for rs in result]

    def _log_error(self, isin, year, prefix, error):
        print(f"Failed to fetch data for ISIN: {isin}, Year: {year}, Prefix: {prefix}. Error: {error}")

    def _log_date_error(self, index, query, error):
        print(f"Error querying index: {index} for query: {query}. Error: {error}")

    def _process_data(self, data, isin, prefix, start_year, end_year):
        if not data:
            print(f"No data found for ISIN: {isin}, Index: {prefix} between {start_year} and {end_year}.")
            return None

        return self._convert_to_dataframe(data)

    def _process_date_data(self, data, date, index):
        if not data:
            print(f"No data found for Date: {date} on Index: {index}.")
            return None

        return self._convert_to_dataframe(data)

    def _convert_to_dataframe(self, data):
        data_frame = pd.DataFrame(data)
        data_frame["date"] = pd.to_datetime(data_frame["date"])
        return data_frame.sort_values(by="date", ascending=True).reset_index(drop=True)

    def _filter_data(self, data_frame, schedular, date):
        filtered_data = data_frame[
            (data_frame["schedular"] == schedular) &
            (pd.to_datetime(data_frame["date"]) == pd.to_datetime(date))
        ]
        return filtered_data.reset_index(drop=True)


preprod_es_manager = PreprodEsManager()
