from .base_config import *
from ..read_yaml import *

class PreprodConfig(GeneralConfig):
    pass

data_sources = DataSourceConfig(**config["data_sources"])
model_settings = ModelConfig(**config["model_settings"])
metrics_config = MetricsConfig(**config["metrics_settings"])
es_stores = DestESConfig(**config["prod_es_stores"])
rl_config = RLConfig(**config['es_data_properties'])
preprod_config = PreprodConfig(**config["prod_version_control"], 
                                                data_sources=data_sources, 
                                                model_settings=model_settings,
                                                metrics_config=metrics_config,
                                                es_store_config=es_stores,
                                                rl_config=rl_config)

print(preprod_config)