from .utils import *
from datetime import datetime
from tqdm import tqdm

script_dir = os.path.dirname(os.path.abspath(__file__))
resource_dir = os.path.join(os.path.dirname(script_dir), 'resource')
os.makedirs(resource_dir, exist_ok=True)

start_year = int(config['es_data_properties']['start_year'])
es_index = config['es_data_properties']['es_index']
def get_data(isin: str, date: str, date_template: pd.DataFrame):
    year = int(date.split('-')[0])
    data = prod_es_manager.get_es_data(isin, [start_year, year], es_index)
    
    if date_template is not None:
        if data is None:
            data = date_template.copy()
        else:
            data = data[data['schedular'] == 'Monthly'].reset_index(drop=True)
            data = pd.merge(date_template, data, on=['date'], how='left')
            data['actual-monthly-return'] = data['closeprice'].pct_change(periods=22) * 100
            data['actual-monthly-return'] = data['actual-monthly-return'].shift(-22)
    
    if data is None:
        return data
    
    data['isin'] = isin
    return data
def store_data(geo: str = 'aipex', date: str = '2025-01-01', sparsity=0.5):
    master_df = get_master(geo)
    
    n_zeros = int(len(master_df) * sparsity)
    n_ones = len(master_df) - n_zeros    
    rank = np.concatenate([np.ones(n_ones), np.zeros(n_zeros)])
    
    index, date_template = 0, None
    
    while index < len(master_df) and (date_template is None):
        date_template = get_data(master_df['isin'].iloc[index], date, date_template)
        index += 1
        
    date_template = date_template[['date']]
    data = master_df['isin'].parallel_apply(lambda isin: get_data(isin, date, date_template))
    data = pd.concat(data.tolist())
    data = data[['date', 'isin', 'closeprice', 'actual-monthly-return', 'actual_monthly_return_predictions']].rename(columns={"actual_monthly_return_predictions": "expected-return"})
    data['actual-monthly-return'].fillna(-100.0, inplace=True)
    data['expected-return'].fillna(-100.0, inplace=True)
    data['date'] = pd.to_datetime(data['date']).dt.strftime('%Y-%m-%d')
    
    print("resource directory:", resource_dir)
    expected_returns = []
    monthly_returns = []
    train_dates = []
    expected_masks = []
    
    for name, group in tqdm(data.groupby('date')):
        group = group.reset_index(drop=True)
        group = group.sort_values(by='expected-return', ascending=False).reset_index(drop=True)
        group['expected-mask'] = rank
        group = group.sort_values(by='isin').reset_index(drop=True)
        
        expected_returns.append(group['expected-return'].tolist())
        expected_masks.append(group['expected-mask'].tolist())
        monthly_returns.append(group['actual-monthly-return'].tolist())
        train_dates.append(name)
    
    portfolio_template = np.array(group['isin'].tolist())
    
    expected_return_path = os.path.join(resource_dir, 'expected-returns.npy')
    monthly_return_path = os.path.join(resource_dir, 'actual-returns.npy')
    expected_masks_path = os.path.join(resource_dir, 'expected-masks.npy')
    train_dates_path = os.path.join(resource_dir, 'train-dates.npy')
    portfolio_template_path = os.path.join(resource_dir, 'portfolio-template.npy')
    rank_template_path = os.path.join(resource_dir, 'rank-template.npy')
    
    np.save(expected_return_path, expected_returns)
    np.save(expected_masks_path, expected_masks)
    np.save(train_dates_path, train_dates)
    np.save(monthly_return_path, monthly_returns)
    np.save(portfolio_template_path, portfolio_template)
    np.save(rank_template_path, rank)
    
    return True
        
if __name__ == '__main__':
    store_data()
    