# DS-Inference-Scripts Requirements
# Main project dependencies

# Shared utilities package (installed from Git)
# Note: Temporarily commented out until Git repository is configured with setup.py
# shared-utils @ git+https://github.com/EqubotAI/DS_Utils.git
#
# To install manually:
# git clone https://github.com/EqubotAI/DS_Utils.git
# cd DS_Utils
# pip install -e .

# Core data science libraries
pandas>=2.1.4
numpy>=1.24.0
scikit-learn>=1.3.0

# AWS and cloud services
boto3>=1.34.137
aws-requests-auth>=0.4.3

# Configuration and data handling
PyYAML>=6.0.2
configparser>=7.0.0

# Email and communication
google-api-python-client>=2.166.0
google-auth>=2.40.3
google-auth-httplib2>=0.2.0
googleapis-common-protos>=1.70.0

# Progress bars and UI
tqdm>=4.66.0

# Excel file support
openpyxl>=3.1.0

# Market calendars
pandas-market-calendars>=4.3.0

# Caching
expiringdict>=1.2.2

# Template engine
Jinja2>=3.1.0

# HTTP requests
requests>=2.31.0

# Parallel processing (concurrent.futures is built into Python 3.2+)
# concurrent-futures>=3.1.1  # Not needed for Python 3.2+

# Date handling
python-dateutil>=2.8.2

# Existing common utilities
eq-common-utils@git+https://<EMAIL>/EqubotAI/eq-common-utils.git@v0.14

# Additional dependencies for specific models
# Uncomment as needed for your specific use case

# Machine Learning
# tensorflow>=2.13.0
# torch>=2.0.0
# transformers>=4.30.0

# Time series analysis
# statsmodels>=0.14.0
# prophet>=1.1.0

# Database connectivity
# psycopg2-binary>=2.9.0
# pymongo>=4.0.0

# Visualization
# matplotlib>=3.7.0
# seaborn>=0.12.0
# plotly>=5.15.0

# Development and testing (optional)
# pytest>=7.4.0
# pytest-cov>=4.1.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.5.0
