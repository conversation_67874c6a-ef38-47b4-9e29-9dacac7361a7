metrics:
  default_schedular: monthly
  read_from: es
  metrics_period: 22 # 22 business days ragardless of schedular type
  metrics_basic_columns: [date, isin, actual_returns, predictions]
  metrics_list: [mean_absolute_error, mean_squared_error, root_mean_squared_error, r2_score, adjusted_r2_score, total_perc_diff, abs_total_diff, total_variance_perc_diff, abs_total_variance_perc_diff, correlation_score, directionality_score, mean_directionality, confidence_score, avg_confidence_score, accuracy_14_day, accuracy_22_day, accuracy_1_day]
  metrics_required: [root_mean_squared_error, mean_directionality, confidence_score, avg_confidence_score, accuracy_14_day, accuracy_22_day, accuracy_1_day]


schedular2days:
  quarterly: 66
  monthly: 22
  weekly: 5
  daily: 1


properties:
  aieq_monthly:
    save_files: True
    forward_fill_in_es: True
    bucket_name: micro-ops-output
    best_model_filename: er_comparison/daily_data/{{ current_date }}_best_model.csv
    daily_metrics_filename: er_comparison/daily_metrics/{{ current_date }}_best_model_metrics.csv
    es_index_prefix: eq_best_model
    metrics_es_index_prefix: eq_best_model_metrics
    gradient_size: 5
    pred_col: predicted_er
    base_model: cat_er
    preportfolio_url: http://100.26.178.130:8000/portfolio/consolidate_er
    close_fetch_model: 
      first_index: cat_er
      second_index: fin
      columns: [date, isin, closeprice]
    es_index_mapping: 
      fin: 
        predictions: eq_financial_model
        metrics: eq_financial_model_metrics
        column: predictions
      inf: 
        predictions: eq_information_model
        metrics: eq_information_model_metrics
        column: predictions
      er: 
        predictions: eq_er_model
        metrics: eq_er_model_metrics
        column: actual_monthly_return_predictions
      cat_er: 
        predictions: eq_cat_er_model
        metrics: eq_cat_er_model_metrics
        column: actual_monthly_return_predictions
    last_file_drop_columns: [date, model, raw_er, predicted_er, closeprice, base_predicted_er, base_rmse, base_mean_dir, base_conf, base_mean_conf, base_accuracy_14_day, base_accuracy_22_day, base_accuracy_1_day]
    past_data_cols: [date, isin, raw_er]
    prediction_columns: 
      column_list: [isin, root_mean_squared_error, confidence_score, avg_confidence_score, mean_directionality, accuracy_14_day, accuracy_22_day, accuracy_1_day]
      rename: 
        avg_confidence_score: average_confidence
    base_metrics_columns:
      root_mean_squared_error: base_rmse
      mean_directionality: base_mean_dir
      confidence_score: base_conf
      avg_confidence_score: base_mean_conf
      accuracy_14_day: base_accuracy_14_day
      accuracy_22_day: base_accuracy_22_day
      accuracy_1_day: base_accuracy_1_day
    best_file_columns:
      isin: isin
      model: model
      raw_er: raw_er
      predicted_er: predicted_er
      closeprice: closeprice
      monthly_predictions_er: base_predicted_er
    metrics_columns:
      drop: [actual_returns, predictions]
      rename:
        root_mean_squared_error: rmse
        mean_directionality: mean_dir
        confidence_score: conf
        avg_confidence_score: mean_conf
      column_list: [date, isin, model, raw_er, predicted_er, base_predicted_er, closeprice, rmse, base_rmse, mean_dir, base_mean_dir, base_conf, base_mean_conf, base_accuracy_1_day, base_accuracy_14_day, base_accuracy_22_day, accuracy_1_day, conf, mean_conf, accuracy_14_day, accuracy_22_day, schedular]


  aieq_daily:
    save_files: True
    forward_fill_closeprice: True
    bucket_name: micro-ops-output
    best_model_filename: er_comparison_daily/daily_data/{{ current_date }}_best_model.csv
    daily_metrics_filename: er_comparison_daily/daily_metrics/{{ current_date }}_best_model_metrics.csv
    es_index_prefix: eq_best_model
    metrics_es_index_prefix: eq_best_model_metrics
    gradient_size: 5
    pred_col: predicted_er
    pred_columns: [date, closeprice, isin, model, raw_er, predicted_er, daily_predictions_er, daily_predictions_fin, daily_predictions_inf, root_mean_squared_error, confidence_score, average_confidence, mean_directionality, accuracy_14_day, accuracy_22_day, accuracy_1_day, schedular]
    metrics_columns: [date, isin, model, raw_er, predicted_er, base_predicted_er, closeprice, rmse, base_rmse, mean_dir, base_mean_dir, base_conf, base_mean_conf, base_accuracy_1_day, base_accuracy_14_day, base_accuracy_22_day, accuracy_1_day, conf, mean_conf, accuracy_14_day, accuracy_22_day, schedular]
    forward_fill: True
    matrix_to_calculate: [root_mean_squared_error, mean_directionality, confidence_score, avg_confidence_score, accuracy_14_day, accuracy_22_day, accuracy_1_day]
    preportfolio_url: http://100.26.178.130:8000/portfolio/consolidate_er_daily
    window: 5
    metrics_locs:
      inf: [s3://micro-ops-output/new_info_model/daily_models/tier1_isinwise/v1/daily_run/daily_model_metrics/data_{{ current_date }}.csv,
        {
          isin: isin,
          root_mean_squared_error: root_mean_squared_error,
          confidence_score: confidence_score,
          avg_confidence_score: average_confidence,
          mean_directionality': mean_directionality,
          accuracy_14_day: accuracy_14_day,
          accuracy_22_day: accuracy_22_day,
          accuracy_1_day: accuracy_1_day
        }
      ]
      fin: [s3://financial-model-data-collection/aieq/daily_metrics/daily_prediction_metrics_{{ current_date }}.csv,
        {
          isin: isin, 
          root_mean_squared_error: root_mean_squared_error, 
          confidence_score: confidence_score, 
          avg_confidence_score: average_confidence, 
          mean_directionality: mean_directionality, 
          accuracy_14_day: accuracy_14_day, 
          accuracy_22_day: accuracy_22_day, 
          accuracy_1_day: accuracy_1_day
        }
      ]
      er: [s3://portfolio-experiments-test/aieq_training/Daily/daily_run_metrics/{{ current_date }}.csv,
        {
          isin: isin, 
          root_mean_squared_error: root_mean_squared_error, 
          confidence_score: confidence_score, 
          avg_confidence_score: average_confidence, 
          mean_directionality: mean_directionality, 
          accuracy_14_day: accuracy_14_day, 
          accuracy_22_day: accuracy_22_day, 
          accuracy_1_day: accuracy_1_day
        }
      ]


  aieq_quarterly:
    save_files: True
    forward_fill_in_es: True
    bucket_name: micro-ops-output
    best_model_filename: er_comparison_quarterly/daily_data/{{ current_date }}_best_model.csv
    daily_metrics_filename: er_comparison_quarterly/daily_metrics/{{ current_date }}_best_model_metrics.csv
    es_index_prefix: eq_best_model
    metrics_es_index_prefix: eq_best_model_metrics
    gradient_size: 5
    pred_col: predicted_er
    base_model: xgb_er
    preportfolio_url: http://100.26.178.130:8000/portfolio/consolidate_er_quaterly
    past_data_cols: [date, isin, raw_er]
    es_index_mapping:
      xgb_er:  
        predictions: eq_xgb_er_model
        metrics: eq_xgb_er_model_metrics
        column: actual_quarterly_return_predictions
    close_fetch_model: 
      first_index: xgb_er
      second_index: xgb_er
      columns: [date, isin, closeprice]
    prediction_columns: 
      column_list: [isin, root_mean_squared_error, confidence_score, avg_confidence_score, mean_directionality, accuracy_14_day, accuracy_22_day, accuracy_1_day]
      rename: 
        avg_confidence_score: average_confidence
    base_metrics_columns:
      root_mean_squared_error: base_rmse
      mean_directionality: base_mean_dir
      confidence_score: base_conf
      avg_confidence_score: base_mean_conf
      accuracy_14_day: base_accuracy_14_day
      accuracy_22_day: base_accuracy_22_day
      accuracy_1_day: base_accuracy_1_day
    metrics_columns:
        rename:
          root_mean_squared_error: rmse
          mean_directionality: mean_dir
          confidence_score: conf
          avg_confidence_score: mean_conf
        get_columns: [isin, root_mean_squared_error, mean_directionality, confidence_score, avg_confidence_score, accuracy_14_day, accuracy_22_day, accuracy_1_day]
        column_list: [date, isin, model, raw_er, predicted_er, base_predicted_er, closeprice, volume, marketcap, rmse, base_rmse, mean_dir, base_mean_dir, base_conf, base_mean_conf, base_accuracy_1_day, base_accuracy_14_day, base_accuracy_22_day, accuracy_1_day, conf, mean_conf, accuracy_14_day, accuracy_22_day, schedular]
    best_file_columns:
      rename:
        quarterly_predictions_er: base_predicted_er
      column_list: [isin, model, raw_er, predicted_er, closeprice, volume, marketcap, quarterly_predictions_er]
    last_file_drop_columns: [date, model, raw_er, predicted_er, closeprice, base_predicted_er, base_rmse, base_mean_dir, base_conf, base_mean_conf, base_accuracy_14_day, base_accuracy_22_day, base_accuracy_1_day]
    additional_columns: [volume, marketcap]
    explainability_s3:
      bucket_name: eq-model-output
      folder_name: explainability_inputs/quarterly/{{ current_date }}/xgb_er_data.csv

  aigo_monthly:
    save_files: True
    forward_fill_in_es: True
    bucket_name: micro-ops-output
    best_model_filename: er_comparison_aigo/daily_data/{{ current_date }}_best_model.csv
    daily_metrics_filename: er_comparison_aigo/daily_metrics/{{ current_date }}_best_model_metrics.csv
    es_index_prefix: eq_best_model
    metrics_es_index_prefix: eq_best_model_metrics
    gradient_size: 5
    pred_col: predicted_er
    base_model: er
    preportfolio_url: ''
    close_fetch_model: 
      first_index: er
      second_index: fin
      columns: [date, isin, closeprice]
    es_index_mapping: 
      fin: 
        predictions: eq_financial_model
        metrics: eq_financial_model_metrics
        column: predictions
      inf: 
        predictions: eq_information_model
        metrics: eq_information_model_metrics
        column: predictions
      er: 
        predictions: eq_er_model
        metrics: eq_er_model_metrics
        column: actual_monthly_return_predictions
      cat_er: 
        predictions: eq_cat_er_model
        metrics: eq_cat_er_model_metrics
        column: actual_monthly_return_predictions
    last_file_drop_columns: [date, model, raw_er, predicted_er, closeprice, base_predicted_er, base_rmse, base_mean_dir, base_conf, base_mean_conf, base_accuracy_14_day, base_accuracy_22_day, base_accuracy_1_day]
    past_data_cols: [date, isin, raw_er]
    prediction_columns: 
      column_list: [isin, root_mean_squared_error, confidence_score, avg_confidence_score, mean_directionality, accuracy_14_day, accuracy_22_day, accuracy_1_day]
      rename: 
        avg_confidence_score: average_confidence
    base_metrics_columns:
      root_mean_squared_error: base_rmse
      mean_directionality: base_mean_dir
      confidence_score: base_conf
      avg_confidence_score: base_mean_conf
      accuracy_14_day: base_accuracy_14_day
      accuracy_22_day: base_accuracy_22_day
      accuracy_1_day: base_accuracy_1_day
    best_file_columns:
      isin: isin
      model: model
      raw_er: raw_er
      predicted_er: predicted_er
      closeprice: closeprice
      monthly_predictions_er: base_predicted_er
    metrics_columns:
      drop: [actual_returns, predictions]
      rename:
        root_mean_squared_error: rmse
        mean_directionality: mean_dir
        confidence_score: conf
        avg_confidence_score: mean_conf
      column_list: [date, isin, model, raw_er, predicted_er, base_predicted_er, closeprice, rmse, base_rmse, mean_dir, base_mean_dir, base_conf, base_mean_conf, base_accuracy_1_day, base_accuracy_14_day, base_accuracy_22_day, accuracy_1_day, conf, mean_conf, accuracy_14_day, accuracy_22_day, schedular]


common:
  model_name: best_er_model
  n_parallel_workers: 120
  lookback_window: 22 # days to consider while forward filling the data and checking for nan and forward filled values
  historical_min_max_window: 520
  max_forward_filled_limit: 2
  urgent_forward_filled_limit: 5
  urgent_forward_filled_close_limit: 2
  temporary_folder: static
  diff: 0.0001 # Accurate upto 4 decimal places
  tab_spaces: 8
  mail_file:
    columns: [isin, tic, exchange, conm, country_code]
    rename: 
      tic: ticker
      conm: company_name
    generated_numeric_columns: [missing_records, forward_filled_closeprices, forward_filled_er, nan_or_zero_er, max_constraint_violation, min_constraint_violation, forward_filled_metrics]
    metrics_to_track: [accuracy_1_day, accuracy_14_day, accuracy_22_day, conf, mean_conf]

  cap_iq: 
    url: https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/v3/clientservice.json
    headers: 
      Authorization: "Basic ************************************************"
      Content-type': application/json

  master_active_firms: 
    aieq_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aieq
    aigo_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aigo
    indt1_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=indt1
    all_url: http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=all
    tier1_loc:
      bucket: micro-ops-output
      folder: tier_data/tier_list.xlsx

  gmail_creds:
    bucket: etf-predictions
    folder: preportfolio/gmail_credentials/credentials.json
    scopes: [https://www.googleapis.com/auth/gmail.send]
    to_mail_ids:
      trigger_mail: [<EMAIL>, <EMAIL>]
      success_mail: [<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>]
      error_mail: [<EMAIL>, <EMAIL>]
      # trigger_mail: [<EMAIL>]
      # success_mail: [<EMAIL>]
      # error_mail: [<EMAIL>]


