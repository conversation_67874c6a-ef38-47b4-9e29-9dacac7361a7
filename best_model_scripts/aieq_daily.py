# Imports
import numpy as np
import datetime
import pandas as pd
from tqdm import tqdm
from copy import copy
import requests
from tqdm import tqdm
from pandas.tseries.offsets import BDay
from helper_functions import Helpers, getBucketKey


def main(today, schedular, tag):
    hf = Helpers(today, schedular, tag)
    year = today.year

    bucket_name = hf.properties['bucket_name']
    best_model_filename = hf.properties['best_model_filename']
    daily_metrics_filename = hf.properties['daily_metrics_filename']
    es_index_prefix = hf.properties['es_index_prefix']
    metrics_es_index_prefix = hf.properties['metrics_es_index_prefix']

    save_files = hf.properties['save_files']
    fetch_period = int(hf.config['common']['lookback_window'])
    start_date = (today - BDay(1 + fetch_period)).strftime('%Y-%m-%d')
    end_date = (today - BDay(1)).strftime('%Y-%m-%d')


    # Daily Prediction Run

    gradient_size = hf.properties['gradient_size']
    multiplier = 1 / (np.e * (np.e ** gradient_size - 1) / (np.e - 1))
    smoothening_gradient = [multiplier * (np.e ** (i + 1)) for i in range(gradient_size)]

    window = hf.properties['window'] # in days
    years = [today.year - 1 if today.month == 1 else today.year, today.year] # years to fetch data for
    list_of_isins = hf.isin_list


    final_data = pd.DataFrame()
    for isin in tqdm(list_of_isins):
        try:
            actuals = hf.get_es_data(isin, [years[-1] - 2, years[-1]], 'eq_financial_model', schedular='Daily')[['date', 'close_change_yesterday']]
            actuals['actual_daily_returns'] = actuals['close_change_yesterday'].shift(-1)
            actual_max, actual_min = actuals['actual_daily_returns'].max(), actuals['actual_daily_returns'].min()
        except:
            actual_max, actual_min = None, None
        try:
            df_er = hf.get_es_data(isin, years, index_prefix='eq_er_model', schedular='Daily')[['date', 'closeprice', 'actual_daily_return_predictions']].rename(columns = {'actual_daily_return_predictions': 'daily_predictions_er'})
            df_er.drop_duplicates(subset = 'date', inplace = True)
            if df_er.empty:
                raise Exception('Empty dataframe.')
        except Exception as e:
            print('Issue', e)
            print(f"ER data not available for {isin}.") 
            continue
        try:
            df_fin = hf.get_es_data(isin, years, index_prefix='eq_financial_model', schedular='Daily')[['date', 'predictions']].rename(columns = {'predictions': 'daily_predictions_fin'})
            df_fin.drop_duplicates(subset = 'date', inplace = True)
            if df_fin.empty:
                raise Exception('Empty dataframe.')
        except Exception as e:
            print('Issue', e)
            print(f"Financial data not available for {isin}.") 
            df_fin = copy(df_er).drop(columns = ['closeprice']).rename(columns = {'daily_predictions_er': 'daily_predictions_fin'})
            df_fin['daily_predictions_fin'] = np.nan
        try:
            df_inf = hf.get_es_data(isin, years, index_prefix='eq_information_model', schedular='Daily')[['date', 'predictions']].rename(columns = {'predictions': 'daily_predictions_inf'})
            df_inf.drop_duplicates(subset = 'date', inplace = True)
            if df_inf.empty:
                raise Exception('Empty dataframe.')
        except Exception as e:
            print('Issue', e)
            print(f"Information data not available for {isin}.") 
            df_inf = copy(df_er).drop(columns = ['closeprice']).rename(columns = {'daily_predictions_er': 'daily_predictions_inf'})
            df_inf['daily_predictions_inf'] = np.nan
        df = pd.merge(
                pd.merge(
                    df_er, df_fin, on = 'date', how = 'left'
                ),
                df_inf, on = 'date', how = 'left'
            ).ffill()
        df['daily_predictions_inf'] = df['daily_predictions_inf'] * 100 # Converting information model predictions into percentages
        df['close_change_daily'] = df['closeprice'].pct_change(periods=hf.config['schedular2days'][schedular.lower()]) * 100
        df['actual_daily_returns'] = df['close_change_daily'].shift(-1)
        data_today = df[-1:].drop(columns = ['close_change_daily', 'actual_daily_returns'])
        df = df[df['date'] <= pd.to_datetime(today)].reset_index(drop = True)
        if len(smoothening_gradient) != 0:
            last_preds = hf.get_es_data(isin, years, index_prefix='eq_best_model', schedular='Daily')
            try:
                last_preds = last_preds[last_preds['date'] <= pd.to_datetime(today)].reset_index(drop = True)['raw_er'][-len(smoothening_gradient)+1:].values
            except:
                continue
        if len(df) > window:
            df = df.drop(columns = ['close_change_daily'])[-(window+1):-1].reset_index(drop = True)
            for model in ['er', 'fin', 'inf']:
                df[f"absolute_error_{model}"] = abs(df[f"daily_predictions_{model}"].astype(float) - df['actual_daily_returns'].astype(float))
            df['best_model'] = np.nan
            for idx in df.index:
                scores = {model:df.loc[idx, f"absolute_error_{model}"] for model in ['er', 'fin', 'inf']}
                if str(min(scores.values())) == 'nan':
                    df.loc[idx, 'best_model'] = np.nan
                    continue
                best_model = min(scores, key = scores.get)
                df.loc[idx, 'best_model'] = best_model        
            df = df[df['best_model'].notna()]
            counts = np.unique(df['best_model'].values, return_counts = True)
            counts = {counts[0][i]: counts[1][i] for i in range(len(counts[0]))}
            counts = sorted(counts, key = lambda x: counts[x], reverse = True)
            
            if actual_max is not None and actual_min is not None:
                for best_in_window in counts:
                    data_today['model'] = best_in_window
                    data_today['raw_er'] = data_today[f'daily_predictions_{best_in_window}']
                    if str(data_today['raw_er'].values[0]) !='nan' and data_today['raw_er'].values[0] > actual_min and data_today['raw_er'].values[0] < actual_max:
                        break
            else:
                best_in_window = counts[0]
                data_today['model'] = best_in_window
                data_today['raw_er'] = data_today[f'daily_predictions_{best_in_window}']
        else:
            best_in_window = 'er'
            data_today['model'] = best_in_window
            data_today['raw_er'] = data_today[f'daily_predictions_{best_in_window}']
        data_today['raw_er'] = min(actual_max, max(actual_min, data_today['raw_er'].values[0]))
        data_today['isin'] = isin
        if len(smoothening_gradient) != 0 and len(last_preds) == len(smoothening_gradient) - 1:
            data_today['predicted_er'] = np.sum(np.append(last_preds, data_today['raw_er'].values) * smoothening_gradient)
        else:
            data_today['predicted_er'] = data_today['raw_er']
        final_data = final_data.append(data_today)
    final_data = final_data.reset_index(drop = True)
    final_data['date'] = pd.to_datetime(today)
    print('Number of best models:', len(final_data))

    metrics_locs = hf.properties['metrics_locs']

    final = pd.DataFrame()
    for model in ['er', 'fin','inf']:
        print('model', model)
        data = final_data[final_data['model'] == model]
        metrics_loc = metrics_locs[model][0]
        metrics_cols = metrics_locs[model][1]
        if metrics_loc == '':
            final = final.append(data)
            continue
        try:
            metrics = hf.s3conf.read_advanced_as_df(*getBucketKey(metrics_loc))[metrics_cols.keys()].rename(columns = metrics_cols)
        except:
            raise Exception("File doesn't exist: " + str(getBucketKey(metrics_loc)))
        final = final.append(pd.merge(data, metrics, on = 'isin', how = 'left'))

    forward_fill = hf.properties['forward_fill']

    if forward_fill:
        defaulty = final.loc[(final['confidence_score'].isna() | final['root_mean_squared_error'].isna() | final['confidence_score'].isna() | final['average_confidence'].isna() | final['mean_directionality'].isna() | final['accuracy_14_day'].isna() | final['accuracy_22_day'].isna() | final['accuracy_1_day'].isna()), ['model', 'isin']]
        print('length of defaulty isins', len(defaulty))
        defaulty_dict = {}
        for model, data in defaulty.groupby('model'):
            isins = data['isin'].to_list()
            defaulty_dict[model] = isins
    print("Number of companies with missing data need to be forward filled:", len(defaulty))

    if forward_fill:
        last_data = pd.DataFrame()
        for isin in final.loc[(final['confidence_score'].isna() | final['root_mean_squared_error'].isna() | final['confidence_score'].isna() | final['average_confidence'].isna() | final['mean_directionality'].isna() | final['accuracy_14_day'].isna() | final['accuracy_22_day'].isna() | final['accuracy_1_day'].isna()), 'isin']:
            last_data_isin = hf.get_es_data(isin, [year, year], 'eq_best_model').replace(0, np.nan)
            last_data = last_data.append(last_data_isin)
        temp = copy(final)
        temp = temp.replace(0, np.nan) 
        temp = temp.append(last_data)
        temp['date'] = pd.to_datetime(temp['date'])
        final = pd.DataFrame()
        for _, data in tqdm(temp.groupby('isin')):
            data = data.sort_values(by = ['date']).ffill()
            final = final.append(data[data['date'] == pd.to_datetime(today)].drop_duplicates(subset = ['isin']))
        final['schedular'] = 'Daily'
        final = final.reset_index(drop = True)


    print('Number of best models- total:', len(final), 'er:', (final['model'] == 'er').sum(), 'fin:', (final['model'] == 'fin').sum(), 'inf:', (final['model'] == 'inf').sum())

    all_columns = hf.properties['pred_columns']
    final = final[final['closeprice'].notna() & final['predicted_er'].notna()][all_columns].reset_index(drop = True)

    # Save the data
    if save_files:
        save_params = {
            's3': {
                'bucket_name': bucket_name,
                'folder_name': best_model_filename
            },
            'es': {
                'year': year,
                'index_prefix': es_index_prefix
            },
            'versioned_s3': {
                'model_year': f'{year}_Q4',
                'type': 'predictions'
            }
        }
        hf.save_data(df, save_params)


    ## Daily Metrics Run

    try:
        data_dict = hf.read_data_parallely(
                                    read_from = hf.config['metrics']['read_from'],
                                    pred_col = hf.properties['pred_col'],
                                    es_index = hf.properties['es_index_prefix'])
    except:
        data_dict = {}


    daily_metrics = pd.DataFrame()
    mtx_2_calc = hf.properties['matrix_to_calculate']
    model = 'er'
    metrics_loc = metrics_locs[model][0]
    metrics_cols = metrics_locs[model][1]

    base_metrics_file_today = s3h.s32df(*getBucketKey(metrics_loc))[metrics_cols.keys()].rename(columns = metrics_cols)
    base_metrics_file_today = base_metrics_file_today[['isin', 'root_mean_squared_error', 'mean_directionality', 'confidence_score', 'average_confidence', 'accuracy_14_day', 'accuracy_22_day', 'accuracy_1_day']].rename(columns = {'root_mean_squared_error': 'base_rmse', 'mean_directionality': 'base_mean_dir', 'confidence_score': 'base_conf', 'average_confidence': 'base_mean_conf', 'accuracy_14_day': 'base_accuracy_14_day', 'accuracy_22_day': 'base_accuracy_22_day', 'accuracy_1_day': 'base_accuracy_1_day'})
    best_file_today = s3h.s32df(bucket_name, best_model_filename)[['isin', 'model', 'raw_er', 'predicted_er', 'closeprice', 'daily_predictions_er']].rename(columns = {'daily_predictions_er': 'base_predicted_er'})

    for isin in tqdm(data_dict.keys()): 
        df = data_dict[isin]
        if df.empty:
            print(f'Data not available for {isin}.')
            continue
        df = df[df['date'] <= pd.to_datetime(today)]
        if len(df) == 0:
            print(f"Data not available for {isin}.")
            continue
            
        metrics_df = hf.calculate_metrics(df, isin, prediction_column='daily_predictions', actual_column='actual_daily_returns', n_features=0, metrics_to_calculate = mtx_2_calc)
        if type(metrics_df) ==  str:
            print(metrics_df)
            continue
        metrics_df = metrics_df.drop(columns = ['actual_returns', 'predictions']).rename(columns = {
            'root_mean_squared_error': 'rmse',
            'mean_directionality': 'mean_dir',
            'confidence_score': 'conf',
            'avg_confidence_score': 'mean_conf',
        })
        try:
            daily_metrics = daily_metrics.append(pd.DataFrame(metrics_df.drop(columns = ['date']).iloc[-1]).T)
        except:
            continue

    if daily_metrics.empty: # forward fill from last file
        daily_metrics_loc = '/'.join(hf.properties['daily_metrics_filename'].split('/')[:-1])
        last_file = sorted(s3h.get_s3filelist(bucket_name, '/'.join(daily_metrics_loc.split('/')[:-1])), key = lambda x: pd.to_datetime(x.split('/')[-1].split('_')[0]))[-1]
        last_file_df = s3h.s32df(bucket_name, last_file)
        last_file_df.drop(columns = ['date', 'model', 'raw_er', 'predicted_er', 'closeprice', 'base_predicted_er', 'base_rmse', 'base_mean_dir', 'base_conf', 'base_mean_conf', 'base_accuracy_14_day', 'base_accuracy_22_day', 'base_accuracy_1_day'], inplace = True)
        daily_metrics = last_file_df

    daily_metrics['date'] = pd.to_datetime(today)
    daily_metrics = pd.merge(daily_metrics, best_file_today, how = 'left', on = 'isin')
    daily_metrics = pd.merge(daily_metrics, base_metrics_file_today, how = 'left', on = 'isin')

    daily_metrics = daily_metrics[hf.properties['metrics_columns']]
    print('Metrics shape:', daily_metrics.shape)

    print('Median RMSE', daily_metrics['rmse'].median())
    print('Median Base RMSE', daily_metrics['base_rmse'].median())
    print('Median Directionality', daily_metrics['mean_dir'].median())
    print('Median Base Directionality', daily_metrics['base_mean_dir'].median())
    percent_better_rmse = 100 * (daily_metrics['rmse'] <= daily_metrics['base_rmse']).sum() / len(daily_metrics)
    print('Percent better RMSE:', percent_better_rmse)
    percent_better_dir = 100 * (daily_metrics['mean_dir'] >= daily_metrics['base_mean_dir']).sum() / len(daily_metrics)
    print('Percent better Directionality:', percent_better_dir)

    # Save the data
    if save_files:
        save_params_metrics = {
            's3': {
                'bucket_name': bucket_name,
                'folder_name': daily_metrics_filename
            },
            'es': {
                'year': year,
                'index_prefix': metrics_es_index_prefix
            },
            'versioned_s3': {
                'model_year': f'{year}_Q4',
                'type': 'metrics'
            }
        }
        hf.save_data(daily_metrics, save_params_metrics)   


        # Pre-portfolio trigger
        preportfolio_url = hf.properties.get('preportfolio_url')
        if preportfolio_url is not None and preportfolio_url != '':
            response = requests.get(preportfolio_url)
            print('Preportfolio trigger response:', response)