"""
Best Model Scripts - Helper Functions

Refactored helper functions with improved error handling, logging,
and standardized utilities usage.
"""

import sys
import os
import numpy as np
import pandas as pd
import datetime
import requests
import json
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from tqdm import tqdm
import yaml
import openpyxl
from jinja2 import Template
from sklearn.metrics import r2_score
from concurrent.futures import ThreadPoolExecutor
from numpy_ext import rolling_apply
import logging

# Import shared utilities package (installed from Git)
from shared_utils import (
    create_s3_manager, load_config, create_error_handler,
    ErrorSeverity, S3Error, DataError, safe_execute
)

from eq_common_utils.utils.config.s3_config import s3_config
from eq_common_utils.utils.config.es_config import es_config

logger = logging.getLogger(__name__)


def download_s3_object(s3_client, bucketname: str, s3_filepath: str, local_folder: str) -> Optional[str]:
    """
    Function to download s3 file to local directory.
    """
    try:
        # Create folder if it doesn't exist
        Path(local_folder).mkdir(parents=True, exist_ok=True)

        # Extract filename from S3 path
        filename = s3_filepath.strip().split('/')[-1]
        local_path = os.path.join(local_folder, filename)

        s3_client._s3Client.download_file(bucketname, s3_filepath, local_path)
        logger.info(f"Downloaded {bucketname}/{s3_filepath} to {local_path}")
        return local_path

    except Exception as e:
        logger.error(f"Failed to download {bucketname}/{s3_filepath}: {e}")
        return None


def upload_s3_object(s3_client, bucketname: str, s3_folder: str, local_filepath: str) -> bool:
    """
    Function to upload local file to s3 folder.
    """
    try:
        if not os.path.exists(local_filepath):
            logger.error(f"Local file not found: {local_filepath}")
            return False

        filename = os.path.basename(local_filepath)
        s3_path = os.path.join(s3_folder, filename).replace('\\', '/')

        s3_client._s3Client.upload_file(local_filepath, bucketname, s3_path)
        logger.info(f"Uploaded {local_filepath} to {bucketname}/{s3_path}")
        return True

    except Exception as e:
        logger.error(f"Failed to upload {local_filepath} to {bucketname}/{s3_folder}: {e}")
        return False


class S3VersionedManager:
    """
    Improved S3 versioned data manager with better error handling and logging.
    """

    def __init__(self,
                 date: Union[str, datetime.date],
                 model_name: str,
                 scheduler: str,
                 model_year: str,
                 upload_type: str = 'predictions',
                 n_workers: int = 120,
                 bucket: str = 'eq-model-output'):
        """
        Initialize S3 versioned manager.

        Args:
            date: Run date
            model_name: Name of the model
            scheduler: Scheduler type
            model_year: Model year
            upload_type: Type of upload (predictions, metrics, etc.)
            n_workers: Number of parallel workers
            bucket: S3 bucket name
        """
        self.date = str(date) if isinstance(date, datetime.date) else date
        self.model_name = model_name
        self.scheduler = scheduler
        self.n_workers = n_workers
        self.model_year = model_year
        self.bucket = bucket
        self.upload_type = upload_type
        self.path = f'{self.model_name}/{self.scheduler}/{self.date}/{self.upload_type}/'

        # Initialize utilities
        self.s3conf = s3_config()
        self.error_handler = create_error_handler(logger)

        logger.info(f"S3VersionedManager initialized - Date: {self.date}, Model: {self.model_name}, "
                   f"Scheduler: {self.scheduler}, Upload type: {self.upload_type}")

    def update_column_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Update DataFrame with metadata columns.

        Args:
            df: Input DataFrame

        Returns:
            Updated DataFrame with metadata
        """
        try:
            df = df.copy()

            # Add scheduler column if missing
            if 'scheduler' not in df.columns:
                df['scheduler'] = self.scheduler.capitalize()

            # Filter by date
            df = df[pd.to_datetime(df['date']) == pd.to_datetime(self.date)].reset_index(drop=True)

            # Add metadata
            timestamp = datetime.datetime.today().strftime('%Y-%m-%d_%H:%M:%S')
            df['updated_at'] = timestamp
            df['model_identifier'] = df['isin'].apply(
                lambda isin: f'{isin}_{self.model_year}_{timestamp}'
            )

            logger.debug(f"Updated column data for {len(df)} records")
            return df

        except Exception as e:
            self.error_handler.handle_error(
                DataError(f"Failed to update column data: {e}", ErrorSeverity.HIGH),
                context={"operation": "update_column_data", "df_shape": df.shape if df is not None else None}
            )
            raise

    def dataframe_upload(self, df: pd.DataFrame) -> bool:
        """
        Upload DataFrame to S3 with versioning.

        Args:
            df: DataFrame to upload

        Returns:
            True if successful, False otherwise
        """
        try:
            if df is None or df.empty:
                raise DataError("Cannot upload empty DataFrame", ErrorSeverity.HIGH)

            # Update column data
            df = self.update_column_data(df)

            # Prepare data for parallel upload
            list_of_data = []
            list_of_filepaths = []

            for isin, data in df.groupby('isin'):
                list_of_data.append(data)
                list_of_filepaths.append(f'{self.path}{isin}.csv')

            logger.info(f"Uploading {len(list_of_data)} files to S3 with {self.n_workers} workers")

            # Parallel upload
            n_calls = len(list_of_filepaths)
            with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
                results = list(executor.map(
                    self.s3conf.write_advanced_as_df,
                    list_of_data,
                    [self.bucket] * n_calls,
                    list_of_filepaths
                ))

            logger.info(f"Successfully uploaded {len(list_of_data)} files to S3")
            return True

        except Exception as e:
            self.error_handler.handle_error(
                S3Error(f"Failed to upload DataFrame: {e}", ErrorSeverity.HIGH),
                context={"operation": "dataframe_upload", "path": self.path}
            )
            return False

    def dataframe_download(self, isin_list: List[str]) -> Optional[pd.DataFrame]:
        """
        Download DataFrames from S3 for given ISINs.

        Args:
            isin_list: List of ISIN codes

        Returns:
            Combined DataFrame or None if failed
        """
        try:
            if not isin_list:
                raise DataError("ISIN list cannot be empty", ErrorSeverity.MEDIUM)

            # Prepare file paths
            list_of_filepaths = [os.path.join(self.path, isin + '.csv') for isin in isin_list]

            logger.info(f"Downloading {len(list_of_filepaths)} files from S3 with {self.n_workers} workers")

            # Parallel download
            n_calls = len(list_of_filepaths)
            with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
                list_of_data = list(executor.map(
                    self.s3conf.read_advanced_as_df,
                    [self.bucket] * n_calls,
                    list_of_filepaths
                ))

            # Filter out empty DataFrames
            list_of_data = [data for data in list_of_data if data is not None and not data.empty]

            if not list_of_data:
                logger.warning("No valid data found for the given ISINs")
                return pd.DataFrame()

            # Combine data
            df = pd.concat(list_of_data, ignore_index=True)

            # Filter by date
            df = df[pd.to_datetime(df['date']) == pd.to_datetime(self.date)].reset_index(drop=True)

            # Clean up index column if present
            if 'index' in df.columns:
                df.drop(columns=['index'], inplace=True)

            logger.info(f"Successfully downloaded and combined {len(df)} records")
            return df

        except Exception as e:
            self.error_handler.handle_error(
                S3Error(f"Failed to download DataFrames: {e}", ErrorSeverity.HIGH),
                context={"operation": "dataframe_download", "isin_count": len(isin_list)}
            )
            return None


# Backward compatibility alias
s3_versioned = S3VersionedManager


class Helpers():
    def __init__(self, run_date, schedular, tag, env='prod'):
        # Read config file
        self.run_date = run_date
        self.schedular = schedular
        self.tag = tag
        self.s3conf = s3_config()
        self.esconf = es_config(env=env)
        with open('config.yaml', 'rt') as f:
            conf = f.read().rstrip()
        template = Template(conf)
        conf = template.render(current_date = str(run_date))
        self.config = yaml.safe_load(conf)

        # Dataframes of companies for different tags
        t1_list = self.s3conf.read_advanced_as_df(self.config['common']['master_active_firms']['tier1_loc']['bucket'], self.config['common']['master_active_firms']['tier1_loc']['folder'])['ISIN'].to_list()
        self.aieq_df = pd.DataFrame.from_dict(requests.get(self.config['common']['master_active_firms']['aieq_url']).json()['data']['masteractivefirms_aieq'])
        self.tier1_df = self.aieq_df[[isin in t1_list for isin in self.aieq_df['isin']]].reset_index(drop=True)
        self.indt1_df = pd.DataFrame.from_dict(requests.get(self.config['common']['master_active_firms']['indt1_url']).json()['data']['masteractivefirms_indt1'])
        aigo_df = pd.DataFrame.from_dict(requests.get(self.config['common']['master_active_firms']['aigo_url']).json()['data']['masteractivefirms_aigo'])
        self.aigo_df = aigo_df[[(isin not in self.aieq_df['isin'].values) and (isin not in self.indt1_df['isin'].values) for isin in aigo_df['isin']]].reset_index(drop=True)
        self.all_df = pd.DataFrame(requests.get(self.config['common']['master_active_firms']['all_url']).json()["data"]["masteractivefirms_all"])

        self.isin_df = eval(f'self.{self.tag}_df')
        self.isin_list = self.isin_df['isin'].to_list()

        # AWS S3 and ES locations
        self.properties = self.config['properties'][f'{tag}_{schedular.lower()}']


    # Elastic Search data fetch functions
    def get_es_data(self, isin: str, years, index_prefix):
        schedular = self.schedular
        if schedular is not None:
            schedular = schedular.capitalize()
        data=[]
        for year in range(years[0], years[1] + 1):
            q_total = '{"query":{"bool": {"must":[{"bool":{"should":[{"match":{"isin":"'+isin+'"}}]}}]}}}'
            try:
                result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
                for rs in result['hits']['hits']:
                    es_data=rs['_source']
                    data.append(es_data)
            except:
                print(f'ES data for {index_prefix} not present for {year}.')
        df=pd.DataFrame(data)
        if (schedular != None) and ('schedular' in df.columns):
            df = df[df['schedular'] == schedular].reset_index(drop = True)
        if len(df) == 0:
            return df
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        return df


    def get_es_data_datewise(self, index_prefix: str, date: str | None = None, isin_list=None):
        schedular = self.schedular
        if isin_list is None:
            isin_list = self.isin_list
        if date is None:
            date = self.run_date.strftime('%Y-%m-%d')

        year = int(date.split('-')[0])
        if schedular is not None:
            schedular = schedular.capitalize()
        q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}}, {"range": {"date": {"gte": date, "lte": date}}}]}}}
        try:
            result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=json.dumps(q_total), size=10000,request_timeout=6000)
            hits = result['hits']['hits']
            df = pd.json_normalize(hits)
            df.rename(columns={col: col.replace('_source.', '') for col in df.columns}, inplace = True)
            df = df.loc[:, (~df.columns.str.startswith('_')) & (df.columns != "sort")]
        except Exception as e:
            print('Error:', e)
            raise Exception(f'Issue fetching data for {index_prefix}_{year}.')
        if (schedular != None) and ('schedular' in df.columns):
            df = df[df['schedular'] == schedular].reset_index(drop = True)
        if len(df) == 0:
            return df
        df['date']=pd.to_datetime(df['date'])
        df.sort_values('date', ascending=True, inplace=True)
        df.reset_index(inplace=True, drop=True)
        return df


    def get_es_data_in_range(self, isin_list, start_date, end_date, index_prefix, as_dict=True):
        schedular = self.schedular
        if schedular is not None:
            schedular = schedular.capitalize()
        years = [int(start_date.split('-')[0]), int(end_date.split('-')[0])]
        data = []
        if years[0] != years[1]:
            for year in range(years[0], years[1] + 1):
                if year == years[0]:
                    temp_start_date = start_date
                    temp_end_date = f'{year}-12-31'
                elif year == years[1]:
                    temp_start_date = f'{year}-01-01'
                    temp_end_date = end_date
                else:
                    temp_start_date = f'{year}-01-01'
                    temp_end_date = f'{year}-12-31'
                if schedular is not None:
                    q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": temp_start_date, "lte": temp_end_date}}}, {"match": {"schedular.keyword": schedular}}]}},"sort": [{"date": {"order": "desc"}}]}
                else:
                    q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": temp_start_date, "lte": temp_end_date}}}]}},"sort": [{"date": {"order": "desc"}}]}
                try:
                    result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
                    for rs in result['hits']['hits']:
                        es_data=rs['_source']
                        data.append(es_data)
                except Exception as e:
                    print(f'ES data for {index_prefix} not present for isins: {isin_list} for {year}.')
                    print('Error:', e)
        else:
            year = years[0]
            if schedular is not None:
                q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date, "lte": end_date}}}, {"match": {"schedular.keyword": schedular}}]}},"sort": [{"date": {"order": "desc"}}]}
            else:
                q_total = {"query": {"bool": {"must": [{"terms": {"isin.keyword": isin_list}},{"range": {"date": {"gte": start_date, "lte": end_date}}}]}},"sort": [{"date": {"order": "desc"}}]}
            try:
                result = self.esconf.client.search(index=f"{index_prefix}_{year}", body=q_total, size=10000,request_timeout=6000)
                for rs in result['hits']['hits']:
                    es_data=rs['_source']
                    data.append(es_data)
            except Exception as e:
                print(f'Error fetching ES data for {index_prefix} not present for isins: {isin_list} for {year}.')
                print('Error:', e)
        df=pd.DataFrame(data)
        
        if df.empty:
            print(f'No data found in the range [{start_date}, {end_date}] in ES for given list of {len(isin_list)} isins: \n{isin_list}')
            return None
        
        if as_dict:
            df_dict = {}
            for isin, data in df.groupby('isin'):
                data['date'] = pd.to_datetime(data['date'])
                data = data.sort_values(['date'], ascending=True).reset_index(drop=True)
                df_dict[isin] = data
            return df_dict
        else:
            df['date']=pd.to_datetime(df['date'])
            df.sort_values(['isin', 'date'], ascending=True, inplace=True)
            df.reset_index(inplace=True, drop=True)
            return df

    def get_diff_min_max(self, isin_list = None):
        # 2 year historical min-max
        print(f'Fetching historical {self.schedular.lower()} min-max values for {self.tag} isins.')
        if isin_list is None:
            isin_list = self.isin_list
        min_max_list = []
        for isin in tqdm(isin_list):
            try:
                closes = self.get_es_data(isin, [self.run_date.year - 2, self.run_date.year], self.properties['es_index_mapping'][self.properties['base_model']]['predictions'])[['date', 'closeprice']].drop_duplicates(subset='date').sort_values(by='date')
                closes['closeprice'] = closes['closeprice'].astype(float).replace(0, np.nan)
                closes[f'close_change_{self.schedular.lower()}'] = closes['closeprice'].ffill().pct_change(periods=self.config['schedular2days'][self.schedular.lower()]) * 100
                closes[f'dif_max_{self.schedular.lower()}'] = closes[f'close_change_{self.schedular.lower()}'].rolling(window = self.config['common']['historical_min_max_window'], min_periods = 1).max()
                closes[f'dif_min_{self.schedular.lower()}'] = closes[f'close_change_{self.schedular.lower()}'].rolling(window = self.config['common']['historical_min_max_window'], min_periods = 1).min()
            except:
                print(f'Error: Data not availabe for {isin}.')
                continue
            closes['isin'] = isin
            min_max_list.append(closes.tail(1)[['isin', f'dif_max_{self.schedular.lower()}', f'dif_min_{self.schedular.lower()}']])
        actual_min_max = pd.concat(min_max_list, axis=0).reset_index(drop=True)
        return actual_min_max


    # Metrics functions
    def accuracy_function(self, df_series, coff = 500):
        '''
        Accuracy conversion metric to convert daily APE into accuracy for ER.
        '''
        return df_series.apply(lambda x: (97/( 1 + 20 * np.exp((-coff/x))) if x <  100 else max(0, 106.7 - 0.213 * x)) if x != 0 else 97)


    def calculate_accuracy(self, df_data, prediction_col = 'predictions', target_col = 'close_change_monthly', coff = 500):
        '''
        This function calculates the accuracy based on actual er and predicted er for an isin for daily, 14 & 22 trading days rolling average.
        Accuracy calculations:
            - Calculate the modified APE for individual data points (modification: divide by max( abs(actual), abs(pred) )) )
            - Convert it to accuracy using 100/1+20*(exp^-500/x) if x < 100 and we linearly degrade to 0 for ape = 500
        Input params (required):
            - date
            - prediction_col : predicted ER value in percentage (prediction for today's date that are generated 1 month back ie. if date is 28 Dec 2023, it'll have the predictions generated on 28 Nov 2023)
            - target_col : target column value in percentage, it's also shifted by 22 days similar to prediction_col
            - coff : coefficient to be used in accuracy function
        Output columns:
            - accuracy_1_day: accuracy calculated for each day
            - accuracy_14_day : accuracy calculated for 14 day rolling window
            - accuracy_22_day : accuracy calculated for 22 day rolling window
        Range of columns:
            - prediction_col : [0, 100]
            - target_col : [0, 100]
            - accuracy: [0, 100]
        '''
        if prediction_col not in df_data.columns:
            raise Exception('Prediction column not in Dataframe')

        if target_col not in df_data.columns:
            raise Exception('Target column not in Dataframe')
        
        # Remove any nan's in prediction or target cols
        df_data = df_data[df_data[prediction_col].notna() & df_data[target_col].notna()]

        # Modify the MAPE to divide by maximum of magnitude of (actual, pred) values to prevent overshooting
        df_data['denominator'] = df_data[[target_col, prediction_col]].apply(lambda x: max( abs(x[target_col]), abs(x[prediction_col]) ) if x[target_col] * x[prediction_col] > 0 else abs(x[target_col]), axis=1 )
        df_data['daily_ape'] = (abs( (df_data[target_col] - df_data[prediction_col]) / df_data['denominator'] ) * 100).apply(lambda x: min(x, 500))

        # Calculate RMS of MAPE over a rolling of 14 days
        df_data['accuracy_1_day'] = self.accuracy_function(df_data['daily_ape'], coff = coff)

        # Calculate RMS of MAPE over a rolling of 22 days
        df_data['accuracy_14_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=14, min_periods = 1).mean()) ** 0.5
        df_data['accuracy_22_day'] = ((df_data['accuracy_1_day'] ** 2).rolling(window=22, min_periods = 1).mean()) ** 0.5
        df_data.drop(columns=['denominator'], inplace=True)
        return df_data
    

    def get_closes_for_isins(self, isins, date = None, id_type = None): # Function to fetch closeprices from SnP (for list of isins in a single date)
            if date is None:
                date = self.run_date
            else:
                date = pd.to_datetime(date)
            url = self.config['common']['cap_iq']['url']
            headers = self.config['common']['cap_iq']['headers']
            mnemonic = 'IQ_CLOSEPRICE'
            input_list = []
            for isin in isins:
                isin_df = self.all_df.loc[self.all_df['isin'] == isin, ['tic', 'exchange', 'isin', 'ciq_trading_id']]
                tic, exchange, isin, tid = isin_df.values[0]
                if id_type == 'isin':
                    identifier = isin
                elif id_type == 'tic:exg':
                    identifier = f'{tic}:{exchange}'
                elif id_type == 'tid':
                    identifier = tid
                else:
                    raise Exception('Invalid ID type.')
                body = {
                        "function": "GDST",
                        "identifier": isin if id_type == 'isin' else identifier,
                        "mnemonic": mnemonic,
                        "properties": {
                            "self.currencyID":"USD",
                            "asOfDate": date.strftime('%m/%d/%Y')
                        }
                    }
                input_list.append(body)
            data = {"inputRequests" : input_list}
            data_json = json.dumps(data)
            response = requests.post(url, data=data_json, headers=headers)
            resp_json = json.loads(response.text)
            df = pd.DataFrame.from_dict([{'isin': data['Identifier'], 'closeprice': data['Rows'][0]['Row'][0]} for data in resp_json['GDSSDKResponse']])
            df['closeprice'] = df['closeprice'].replace({'Data Unavailable': np.nan})
            df['closeprice'] = df['closeprice'].astype(float)
            df['date'] = date
            return df


    def get_capiq_close(self, years, body, id_type = None): # Function to fetch adjusted closeprices from SnP (for a single isin in the year range)
        url = self.config['common']['cap_iq']['url']
        headers = self.config['common']['cap_iq']['headers']
        mnemonic = 'IQ_CLOSEPRICE_ADJ'
        tic, exchange, isin, ipodate, tid = body
        start_date = get_start_date(ipodate, f'10/01/{years[0] - 1}')
        end_date = get_end_date(f'12/31/{years[1]}')
        if id_type == 'isin':
            identifier = isin
        elif id_type == 'tic:exg':
            identifier = f'{tic}:{exchange}'
        elif id_type == 'tid':
            identifier = tid
        else:
            raise Exception('Invalid ID type.')
        body = {
            "function": "GDST",
            "identifier": identifier,
            "mnemonic": mnemonic,
            "properties": {
                "self.currencyID":"USD",
                "startDate": start_date, # startdate
                "endDate" : end_date # enddate
            }
        }
        input_list = [body]
        data = {"inputRequests" : input_list}
        data_json = json.dumps(data)
        response = requests.post(url, data=data_json, headers=headers)
        try:
            resp_json=json.loads(response.text)
            resp_json['isin'] = isin
            return resp_json
        except Exception as e:
            print(e)


    def get_closes_waterfall(self, first_index, second_index): # Function to get given date's closeprices using waterfall method
        remaining_isins = self.isin_list
        try:
            closes = self.get_es_data_datewise(self.properties['es_index_mapping'][first_index]['predictions'])[self.properties['close_fetch_model']['columns']]
            remaining_isins = list(set(self.isin_list) - set(closes.loc[closes['closeprice'].notna(), 'isin']))
        except Exception as e:
            closes = pd.DataFrame(columns=self.properties['close_fetch_model']['columns'])
            print(f'Issue with fetching close prices from first index {self.properties['es_index_mapping'][first_index]['predictions']}: {e}')
        if len(remaining_isins) != 0:
            try:
                closes = pd.concat([closes, self.get_es_data_datewise(self.properties['es_index_mapping'][second_index]['predictions'], isin_list=remaining_isins)[self.properties['close_fetch_model']['columns']]], axis=0)
            except Exception as e:
                print(f'Issue with fetching/merging close prices from second index {self.properties['es_index_mapping'][second_index]['predictions']}: {e}')
            remaining_isins = list(set(self.isin_list) - set(closes.loc[closes['closeprice'].notna(), 'isin']))
        if len(remaining_isins) != 0:
            try:
                closes = pd.concat([closes, self.get_closes_for_isins(remaining_isins, id_type='isin')[self.properties['close_fetch_model']['columns']]], axis=0)
            except Exception as e:
                print(f'Issue with fetching/merging close prices from snp: {e}')
            remaining_isins = list(set(self.isin_list) - set(closes.loc[closes['closeprice'].notna(), 'isin']))
        closes['date'] = pd.to_datetime(closes['date']) # Correcting the date format for all records
        return closes, remaining_isins


    def get_close_change(self, company, period, years):
        single_date_response = self.get_capiq_close(years, company.values[0].tolist(), id_type='tid')
        if failed_check(single_date_response['GDSSDKResponse']):
            single_date_response = self.get_capiq_close(years, company.values[0].tolist(), id_type='tic:exg')
            if failed_check(single_date_response['GDSSDKResponse']):
                single_date_response = self.get_capiq_close(years, company.values[0].tolist(), id_type='isin')
        try:
            snp_closes = pd.DataFrame({'date': pd.to_datetime(single_date_response['GDSSDKResponse'][0]['Headers']), 'closeprice_adj': single_date_response['GDSSDKResponse'][0]['Rows'][0]['Row']})
            snp_closes["closeprice_adj"] = snp_closes["closeprice_adj"].astype(float)
            snp_closes['close_change'] = snp_closes["closeprice_adj"].ffill().pct_change(periods=period) * 100
            snp_closes.drop(columns = 'closeprice_adj', inplace = True)
            return snp_closes
        except:
            print("Couldn't fetch prices from S&P API.")
            return pd.DataFrame()


    def read_data(self, isin, years, read_from, pred_col, schedular, es_index, bucket_name, path_loc, local_folder, close_change_col):
        default_schedular = self.config['metrics']['default_schedular']
        schedular_dict = self.config['schedular2days']
        try:
            if read_from == 'es': # fetching data from es
                if close_change_col is not None:
                    temp = self.get_es_data(isin, years, es_index)[['date', pred_col, close_change_col]] 
                else:
                    temp = self.get_es_data(isin, years, es_index)[['date', pred_col]] 
            elif read_from == 's3': # fetching data from s3
                if close_change_col is not None:
                    temp = self.s3conf.read_advanced_as_df(bucket_name, f'{path_loc}/{isin}.csv')[['date', pred_col, close_change_col]] 
                else:
                    temp = self.s3conf.read_advanced_as_df(bucket_name, f'{path_loc}/{isin}.csv')[['date', pred_col]] 
            else: # fetching data from local
                if close_change_col is not None:
                    temp = pd.read_csv(os.path.join(local_folder, isin))[['date', pred_col, close_change_col]] 
                else:
                    temp = pd.read_csv(os.path.join(local_folder, isin))[['date', pred_col]] 
            if schedular == None or schedular == '':
                schedular = default_schedular
        except Exception as e:
            print(f'Issue fetching data for isin {isin} for years {years}.')
            print('Error:', e)
            return isin, pd.DataFrame()
        company = self.all_df[[x == isin for x in self.all_df['isin']]][['tic', 'exchange', 'isin', 'ipodate', 'ciq_trading_id']].reset_index(drop = True)
        if len(company) == 0:
            print(f'Isin {isin} not in Masters Active Firms')
            return isin, pd.DataFrame()
        try:
            temp['date'] = pd.to_datetime(temp['date'])
            temp = temp.sort_values(by = 'date').reset_index(drop = True)
            if close_change_col is not None:
                temp.rename(columns = {pred_col:f'{schedular}_predictions', 
                                    close_change_col: f'actual_{schedular}_returns'}, 
                        inplace = True)
            else:
                close_change = self.get_close_change(company, schedular_dict[schedular.lower()], years)
                temp = pd.merge(temp, close_change, on = 'date', how = 'inner').rename(columns = {'percent_pct': 'close_change'})
                temp.rename(columns = {pred_col:f'{schedular}_predictions', 
                                    'close_change': f'actual_{schedular}_returns'}, 
                        inplace = True)
            temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].shift(schedular_dict[schedular.lower()])
            temp = temp[(temp['date'] >= pd.to_datetime(f'{years[0]}-01-01')) & (temp['date'] <= pd.to_datetime(f'{years[1]}-12-31'))]    
            temp = temp.drop_duplicates(subset = 'date').sort_values(by = 'date').reset_index(drop = True)
            temp.reset_index(drop = True, inplace = True)
            temp[f'{schedular}_predictions'] = temp[f'{schedular}_predictions'].astype(float, errors="ignore")
            temp[f'actual_{schedular}_returns'] = temp[f'actual_{schedular}_returns'].astype(float, errors="ignore")
            temp = temp[schedular_dict[schedular.lower()]:].ffill().reset_index(drop=True)
            print(f'Data collected for isin {isin}.')
        except Exception as e:
            print(e)
            temp = pd.DataFrame()
        return isin, temp


    def read_data_parallely(self, list_of_isins=None, read_from='es', pred_col='predictions', n_workers=100, **kwargs): # function to read historical data
        '''
        'kwargs' keys-
            'es_index': index of Elastic Search to fetch data from (if read_from is 'es')
            'bucket_name': name of S3 bucket where data is stored (if read_from is 's3')
            'path_loc': S3 folder location to fetch data from (if read_from is 's3')
            'local_folder': path for folder to fetch data from (if data needs to be collected from local)
            'schedular': 'daily', 'weekly', 'monthly' or 'quarterly'
            'close_change_col': 'column name containing daily/weekly/monthly/quarterly closeprice change in percentage'
        '''
        if 'schedular' in kwargs.keys():
            schedular = kwargs['schedular'].lower()
        else:
            schedular = None
        if list_of_isins is None:
            list_of_isins = self.isin_list
        years = [self.run_date.year - 3 if self.run_date.month < 3 else self.run_date.year - 2, self.run_date.year] # years to fetch data for
        if read_from == 'es': # fetching data from es
            es_index = kwargs['es_index']
            bucket_name = None
            path_loc = None
            local_folder = None
        elif read_from == 's3':
            bucket_name = kwargs['bucket_name']
            path_loc = kwargs['path_loc']
            es_index = None
            local_folder = None
        else:
            local_folder = kwargs['local_folder']
            es_index = None
            bucket_name = None
            path_loc = None
        n_calls = len(list_of_isins)
        if 'close_change_col' in kwargs.keys():
            close_change_col = kwargs['close_change_col']
        else:
            close_change_col = None
        with ThreadPoolExecutor(max_workers=n_workers) as exe:
            dfs = exe.map(self.read_data, 
                        list_of_isins, 
                        [years] * n_calls, 
                        [read_from] * n_calls, 
                        [pred_col] * n_calls, 
                        [schedular] * n_calls, 
                        [es_index] * n_calls, 
                        [bucket_name] * n_calls, 
                        [path_loc] * n_calls, 
                        [local_folder] * n_calls,
                        [close_change_col] * n_calls)
        data_dict = {}
        for isin, data in dfs:
            data_dict[isin] = data
        return data_dict


    def directionality_score_calc(self, prediction_direction, close_direction):
        directionality_df = pd.DataFrame()
        directionality_df['prediction_direction'] = prediction_direction
        directionality_df['close_direction'] = close_direction
        correct_direction_df = directionality_df[directionality_df.ge(0).all(axis=1) | directionality_df.lt(0).all(axis=1)] 
        incorrect_direction_df = directionality_df[~directionality_df.index.isin(correct_direction_df.index)] 
        relaxation_count = len(incorrect_direction_df[(abs(abs(incorrect_direction_df["close_direction"]) - abs(incorrect_direction_df["prediction_direction"])) < 0.5)]) 
        if len(directionality_df) == relaxation_count:
            directionality_score = np.nan
        else:
            directionality_score = (len(correct_direction_df) / (len(directionality_df) - relaxation_count)) * 100
        return directionality_score


    def calculate_metrics(self, df, isin, prediction_column = 'monthly_predictions', actual_column =  'actual_monthly_returns', metrics_to_calculate = None, n_features = 0):
        period = self.config['metrics']['metrics_period']
        if metrics_to_calculate is None:
            metrics_to_calculate = self.config['metrics']['metrics_list']

        if len(df) < period:
            return f'Dataframe size for {isin} too small to calculate metrics.'
        
        df = df.rename(columns = {prediction_column: 'predictions', actual_column: 'actual_returns'})
        metrics_columns = self.config['metrics']['metrics_basic_columns'] + metrics_to_calculate
        
        # Total perc diff
        if 'total_perc_diff' in metrics_columns or 'abs_total_diff' in metrics_columns:
            df['total_perc_diff'] = rolling_apply(lambda target, prediction: (target.mean() - prediction.mean()) / prediction.mean(), period, df['actual_returns'].values, df['predictions'].values)
            
        # Abs total diff
        if 'abs_total_diff' in metrics_columns:
            df['abs_total_diff'] = abs(df['total_perc_diff'])
        
        # Total variance perc diff
        if 'total_variance_perc_diff' in metrics_columns or 'abs_total_variance_perc_diff' in metrics_columns:
            df['total_variance_perc_diff'] = rolling_apply(lambda target, prediction: (target.var() - prediction.var()) / prediction.var(), period, df['actual_returns'].values, df['predictions'].values)
            
        # Abs total variance perc diff
        if 'abs_total_variance_perc_diff' in metrics_columns:
            df['abs_total_variance_perc_diff'] = abs(df['total_variance_perc_diff'])
        
        # MAE
        if 'mean_absolute_error' in metrics_columns:
            df['mean_absolute_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: abs(df_series).mean())
        
        # MSE
        if 'mean_squared_error' in metrics_columns or 'root_mean_squared_error' in metrics_columns:
            df['mean_squared_error'] = (df['actual_returns'] - df['predictions']).rolling(period).apply(lambda df_series: ((df_series ** 2).sum() / period))
        
        # RMSE
        if 'root_mean_squared_error' in metrics_columns:
            df['root_mean_squared_error'] = df['mean_squared_error'] ** 0.5
            
        # R2 score
        if 'r2_score' in metrics_columns or 'adjusted_r2_score' in metrics_columns:
            df['r2_score'] = rolling_apply(r2_score, period, df['actual_returns'].values, df['predictions'].values)
                    
        # Adjusted R2 score
        if 'adjusted_r2_score' in metrics_columns:
            df['adjusted_r2_score'] = 1 - ((1 - df['r2_score']) * (period - 1) / (period - n_features - 1))
        
        # Mean directionality
        if 'mean_directionality' in metrics_columns:
            df['mean_directionality'] = (df['actual_returns'] * df['predictions']).rolling(period).apply(lambda df_series: 100 * ((df_series) > 0).sum() / period)
        
        # Correlation score
        if 'correlation_score' in metrics_columns:
            df['correlation_score'] = rolling_apply(lambda target, prediction: np.corrcoef(target, prediction)[0][1], period, df['actual_returns'].values, df['predictions'].values) 
            
        # Confidence score
        if 'confidence_score' in metrics_columns or 'avg_confidence_score' in metrics_columns:
            min_confidence = 0.01
            
            max_values =  df['actual_returns'].rolling(period * 24, min_periods = period).max()
            min_values =  df['actual_returns'].rolling(period * 24, min_periods = period).min()
            filt1 = [df.loc[i, 'predictions'] >= max_values.loc[i] for i in range(len(df))]
            filt2 = [df.loc[i, 'predictions'] <= min_values.loc[i] for i in range(len(df))]
            filt3 = [df.loc[i, 'actual_returns'] >= max_values.loc[i] for i in range(len(df))]
            filt4 = [df.loc[i, 'actual_returns'] <= min_values.loc[i] for i in range(len(df))]

            df['confidence_score'] = [min_confidence if (filt1[i] or filt2[i] or filt3[i] or filt4[i]) else
            max(min_confidence, (max_values.loc[i] - df.loc[i, "actual_returns"])/(max_values.loc[i] - df.loc[i, "predictions"]) if df.loc[i, "actual_returns"] > df.loc[i, "predictions"] else (df.loc[i, "actual_returns"] - min_values.loc[i]) / (df.loc[i, "predictions"] - min_values.loc[i])) for i in range(len(df))]

        # Average confidence score
        if 'avg_confidence_score' in metrics_columns:
            df['avg_confidence_score'] = df['confidence_score'].rolling(period // 2).mean()

        # Directionality score
        if 'directionality_score' in metrics_columns:
            directionality_df = pd.DataFrame()
            directionality_df["prediction_direction"] = (df["predictions"] - df['predictions'].shift(1)) / df['predictions'].shift(1)
            directionality_df["close_direction"] = (df["actual_returns"] - df['actual_returns'].shift(1)) / df['actual_returns'].shift(1)
            df['directionality_score'] = rolling_apply(self.directionality_score_calc, period, directionality_df['prediction_direction'], directionality_df['close_direction']) 
                    
        # Accuracy
        if "accuracy_14_day" in metrics_columns or "accuracy_22_day" in metrics_columns or "accuracy_1_day"  in metrics_columns:
            df = self.calculate_accuracy(df, 'predictions', 'actual_returns')

        df['isin'] = isin
        return df[metrics_columns][period:].reset_index(drop = True)


    def es_upload(self, df, year, es_index_prefix, drop_duplicates='isin'):
        # Filter out the data accourding to the year
        df['date'] = pd.to_datetime(df['date'])
        df = df[df['date'].apply(lambda x: x.year) == year].reset_index(drop=True)
        es_index = f"{es_index_prefix}_"+str(year)
        print('Index_name:', es_index)
        # to check and create index if not already present
        indices=str(self.esconf.client.indices.get_alias().keys())
        if es_index not in indices:
            self.esconf.create_index(es_index)
        if 'schedular' not in df.columns:
            df['schedular'] = self.schedular.capitalize()
        df['date'] = df['date'].astype(str)
        df = df.drop_duplicates(subset=[drop_duplicates]).reset_index(drop=True)
        if df.empty:
            raise Exception('File is empty!')
        df.replace({np.nan: None}, inplace = True)
        documents = []
        for index, row in df.iterrows():
            doc_id = f"{row['isin']}_{row['date']}_{self.schedular.lower()[0]}"
            document = {"index": {"_index": es_index, "_id": doc_id}}
            data = row.to_dict()
            documents.append(document)
            documents.append(data)
        response=self.esconf.client.bulk(documents)
        if response['errors']:
            print('Error during ES upload for', self.run_date.strftime('%Y-%m-%d'))
        else:
            print('ES Upload successful.')


    def save_data(self, df, save_params):
        if 's3' in save_params.keys():
            # save to s3 location
            try:
                self.s3conf.write_advanced_as_df(df, save_params['s3']['bucket_name'], save_params['s3']['folder_name'])
                print('S3 file upload successful.')
            except:
                print('Error during S3 file upload for', self.run_date.strftime('%Y-%m-%d'))
        if 'es' in save_params.keys():
            # save to es index
            self.es_upload(df, save_params['es']['year'], save_params['es']['index_prefix'])
        if 'versioned_s3' in save_params.keys():
            # save to verioned s3 locaion
            s3v = s3_versioned(self.run_date.strftime('%Y-%m-%d'), self.config['common']['model_name'], self.schedular.lower(), save_params['versioned_s3']['model_year'], upload_type = save_params['versioned_s3']['type'])
            s3v.dataframe_upload(df)
            checkisin_list = df['isin'].to_list()
            versioned_df = s3v.dataframe_download(checkisin_list)
            if len(df) == len(versioned_df):
                print('Error during Versioned S3 upload for', self.run_date.strftime('%Y-%m-%d'))
            else:
                print('Versioned S3 upload successful.')


    def security_traded_last(self, isin_list=None):
        '''
        Check if the security was traded on last working day or not.
        '''
        url_sp = self.config['common']['cap_iq']['url']
        headers_sp = self.config['common']['cap_iq']['headers']

        if isin_list is None:
            isin_list = self.isin_list

        def _valid_date(date_string):
            if date_string == 'Data Unavailable': # Consider a company as traded when not able to fetch the data
                return pd.to_datetime(self.run_date.strftime('%Y-%m-%d'))
            try:
                return pd.to_datetime(date_string)
            except ValueError:
                return pd.to_datetime('2000-01-01')
        try:
            batch_size = 500
            n_batches = int(np.ceil(len(isin_list)/batch_size))
            all_responses = []
            for i in range(n_batches):
                isin_batch = isin_list[i * batch_size: min((i + 1) * batch_size, len(isin_list))]
                data = {
                        "inputRequests": [
                        {
                            "function": "GDSP",
                            "identifier": isin,
                            "mnemonic": "IQ_PRICEDATE"
                        }
                    for isin in isin_batch]
                }
                data_json = json.dumps(data)
                response = requests.post(url_sp, data=data_json, headers=headers_sp)
                response = json.loads(response.text)['GDSSDKResponse']
                all_responses += response
            all_responses = {response['Identifier']: _valid_date(response['Rows'][0]['Row'][0]) for response in all_responses}
            traded_isins = []
            for isin, date_traded in all_responses.items():
                if pd.to_datetime(date_traded).date() >= pd.to_datetime(self.run_date).date():
                    traded_isins.append(isin)
            return traded_isins
        except Exception as e:
            print('Error geting traded companies:', e)
            return np.nan

            

# Some additional functions
def get_start_date(ipo, original_st):
    if ipo is None or ipo == np.nan or str(ipo) == 'nan':
        return original_st
    try: 
        ipo = pd.to_datetime(ipo)
        original_st = pd.to_datetime(original_st)
    except:
        return original_st
    start_date = original_st.strftime('%m/%d/%Y') if ipo < original_st else ipo.strftime('%m/%d/%Y')
    return start_date

def get_end_date(original_ed):
    return datetime.date.today().strftime('%m/%d/%Y') if datetime.date.today() < pd.to_datetime(original_ed).date() else original_ed

def failed_check(GDSSDKResponse):
    if GDSSDKResponse[0]['ErrMsg'] == 'InvalidIdentifier' or GDSSDKResponse[0]['ErrMsg'] == 'Input Arguments Missing':
        return True
    if np.sum([y == 'Data Unavailable' for y in [x['Rows'][0]['Row'][0] for x in GDSSDKResponse]]) / len(GDSSDKResponse) >= 0.75: 
        return True
    return False

def getBucketKey(s3path):
    s3path = s3path[len('s3://'):]
    bucket = s3path.split('/')[0]
    key = '/'.join(s3path.split('/')[1:])
    return bucket, key

def read_excel(filename, sheetname):
    df = pd.DataFrame(openpyxl.load_workbook(filename, data_only = True)[sheetname].values)
    df.columns = df.iloc[0]
    return df.drop([0])
