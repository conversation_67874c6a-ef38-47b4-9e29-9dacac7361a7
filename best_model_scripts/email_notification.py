"""
Best Model Scripts - Email Notification

Refactored email notification system with improved error handling,
standardized utilities, and better maintainability.
"""

import sys
import os
import base64
import numpy as np
import pandas as pd
import traceback
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from email.mime.text import MIMEText
from email.message import EmailMessage
from pandas.tseries.offsets import BDay
import logging

# Import shared utilities package (installed from Git)
from shared_utils import (
    load_config, create_email_sender, create_s3_manager,
    create_error_handler, ErrorSeverity, EmailError, DataError, safe_execute
)

from helper_functions import Helpers

logger = logging.getLogger(__name__)


class AnalysisNotification:
    """
    Improved analysis notification system with standardized email utilities.
    """

    def __init__(self,
                 date: Union[str, pd.Timestamp],
                 scheduler: str,
                 tag: str,
                 sender_name: Optional[str] = None):
        """
        Initialize analysis notification system.

        Args:
            date: Run date
            scheduler: Scheduler type
            tag: Model tag
            sender_name: Name of the sender
        """
        # Initialize helper and configuration
        self.hf = Helpers(date, scheduler, tag)
        self.config = load_config('config.yaml')

        # Setup utilities
        from eq_common_utils.utils.config.s3_config import s3_config
        s3_client = s3_config()
        self.email_sender = create_email_sender(self.config.to_dict(), s3_client)
        self.error_handler = create_error_handler(logger, self.email_sender)

        # Configuration shortcuts
        self.gmail_creds = self.config.get('common.gmail_creds', {})
        self.temp_folder = self.config.get('common.temporary_folder', 'static')
        self.max_forward_filled_limit = self.config.get('common.max_forward_filled_limit', 2)
        self.urgent_forward_filled_limit = self.config.get('common.urgent_forward_filled_limit', 5)
        self.urgent_forward_filled_close_limit = self.config.get('common.urgent_forward_filled_close_limit', 2)

        # Email content setup
        self._setup_email_content(sender_name)

        logger.info(f"AnalysisNotification initialized for {tag.upper()}-{scheduler.capitalize()} on {date}")

    def _setup_email_content(self, sender_name: Optional[str]) -> None:
        """Setup email content templates."""
        lookback_window = self.config.get('common.lookback_window', 22)

        # Email headers and footers
        self.mail_header = (
            f"Daily run script status: Complete.\n"
            f"Please find attached, the data quality report file on Best Model daily run for "
            f"{self.hf.tag.upper()}-{self.hf.scheduler.capitalize()} companies for date {self.hf.run_date} "
            f"(as observed over a lookback window of {lookback_window} business days).\n"
            f"Report file summary:\n"
        )

        sender_details = (
            f"Best Regards,\n{' '.join([x.capitalize() for x in sender_name.split()])}"
            if sender_name else "Thanks"
        )

        self.mail_footer = (
            "Downstream consumers, kindly review and take necessary actions.\n"
            "Feel free to reach out in case of any help or clarification.\n\n"
            f"{sender_details}"
        )

        self.error_mail_footer = (
            "The team will look into the issue and get back once the error is fixed.\n\n"
            f"{sender_details}"
        )

        # Email subjects
        self.mail_subject = (
            f"Data Quality Report : Best-Model {self.hf.tag.upper()}-{self.hf.scheduler.capitalize()} "
            f"daily run : Date {self.hf.run_date}"
        )

        self.error_mail_subject = (
            f"Scheduled daily run failed: Best-Model {self.hf.tag.upper()}-{self.hf.scheduler.capitalize()} "
            f"daily run : Date {self.hf.run_date}"
        )

        self.urgent_tag = "URGENT - HIGH PRIORITY: "


    def get_credentials(self, path_name: str) -> Optional[Credentials]:
        """
        DEPRECATED: Use EmailSender from shared utilities instead.
        Method to get the credentials from local temporary credential file.
        """
        logger.warning("get_credentials is deprecated. Use EmailSender from shared utilities instead.")

        try:
            creds = None
            SCOPES = self.gmail_creds.get('scopes', [])

            if os.path.exists(path_name):
                creds = Credentials.from_authorized_user_file(path_name, SCOPES)

            return creds

        except Exception as e:
            self.error_handler.handle_error(
                EmailError(f"Failed to get credentials: {e}", ErrorSeverity.MEDIUM),
                context={"path": path_name}
            )
            return None

    def download_temp_json(self) -> Optional[str]:
        '''
        Method to download the temporary credential json file
        '''
        if not os.path.exists(self.temp_folder):
            os.mkdir(self.temp_folder)
        bucketname = self.hf.config['common']['gmail_creds']['bucket']
        s3_filepath = self.hf.config['common']['gmail_creds']['folder']
        local_folder = self.temp_folder
        local_path = download_s3_object(self.hf.s3conf._s3Client, bucketname, s3_filepath, local_folder)

        return local_path


    def delete_temp_data(self, filepaths):
        '''
        Method to delete all the temporary files created
        '''
        for filepath in filepaths:
            if os.path.exists(filepath):
                os.remove(filepath)
                print(f'Deleted file "{filepath.split('/')[-1]}".')
        extra_data = os.path.join(self.temp_folder, '.ipynb_checkpoints')
        if os.path.exists(extra_data):
            os.rmdir(extra_data)
        # Delete the temp folder if empty
        print(os.listdir(self.temp_folder))
        if os.path.exists(self.temp_folder) and len(os.listdir(self.temp_folder)) == 0:
            os.rmdir(self.temp_folder)
            print(f'"{self.temp_folder}" folder deleted since found empty.')


    def send_mail(self, subject: str, message_text: str, attachments: list = None, to_recipient: str = None):
        '''
        Method to send the data and files over mail.
        '''
        credential_filepath = self.download_temp_json()

        credentials = self.get_credentials(credential_filepath)
        filenames = [attachment.split('/')[-1] for attachment in attachments]
        
        if to_recipient is None:
            to_recipient = 'success_mail'
        to = self.hf.config['common']['gmail_creds']['to_mail_ids'][to_recipient]

        if len(to) == 0:
            print('No recipient mail id found. Mail not sent.')
            return
        
        if credentials == None:
            return print("Credentials not found.")
        try:
            service = build('gmail', 'v1', credentials=credentials)
            message = EmailMessage()
            tab_spaces = self.hf.config['common']['tab_spaces']
            html = f"<html><head></head><body>{message_text.replace('\n', '<br>').replace('\t', '&nbsp;' * tab_spaces)}</body></html>"
            body = MIMEText(html, 'html')
            message.set_content(body)
            if attachments:
                for i, attachment in enumerate(attachments):
                    with open(attachment, 'rb') as content_file:
                        content = content_file.read()
                        message.add_attachment(content, maintype='application', subtype=(
                            attachment.split('.')[1]), filename=filenames[i])
            message['To'] = to
            message['Subject'] = subject
    
            # encoded message
            encoded_message = base64.urlsafe_b64encode(message.as_bytes()) \
                .decode()
    
            create_message = {
                'raw': encoded_message
            }
            send_message = (service.users().messages().send(userId="me", body=create_message).execute())
            return send_message["id"]
        except HttpError as error:
            print(F'An error occurred while sending mail: {error}')
            send_message = None


    def do_data_analysis(self):
        '''
        Method to fetch the data for Elastic Search and do required analysis.
        '''
        index_prefix = self.hf.properties['metrics_es_index_prefix']
        lookback_window = self.hf.config['common']['lookback_window']
        start_date = (self.hf.run_date - BDay(lookback_window)).strftime('%Y-%m-%d')
        end_date = (self.hf.run_date - BDay(1)).strftime('%Y-%m-%d')

        
        # Traded and Non-traded ISINs
        traded_isins = self.hf.security_traded_last()
        if traded_isins is None or str(traded_isins) == str(np.nan): # Some issue fetching traded isins, consider all isins as traded
            isin_not_traded = []
        else:
            isin_not_traded = list(set(self.hf.isin_list) - set(traded_isins))
            self.hf.isin_list = traded_isins # Update the isin list to traded isins only

        data_df = self.hf.get_es_data_datewise(index_prefix)
        
        # NaN or 0 ER ISINs
        nan_isins_list = data_df.loc[data_df['raw_er'].isna() | data_df['predicted_er'].isna() | (data_df['raw_er'] == 0) | (data_df['predicted_er'] == 0), 'isin'].to_list()
        nan_isins = {}
        if nan_isins_list:
            data_dict = self.hf.get_es_data_in_range(nan_isins_list, start_date, end_date, index_prefix)
            for isin, data in data_dict.items():
                if data.loc[data['raw_er'].isna() | data['predicted_er'].isna() | (data_df['raw_er'] == 0) | (data_df['predicted_er'] == 0)].shape[0] == data.shape[0]:
                    nan_isins[isin] = data.shape[0]
                else:
                    not_nan_index = data.loc[data['raw_er'].notna() & data['predicted_er'].notna() * (data_df['raw_er'] != 0) & (data_df['predicted_er'] != 0)].index[-1]
                    nan_isins[isin] = len(data[not_nan_index: ]) - 1
        
        # Not NaN ER ISINs
        remaining_isins = list(set(self.hf.isin_list) - set(nan_isins_list)) # Remaining companies
        if len(remaining_isins) != 0:
            data_dict = self.hf.get_es_data_in_range(remaining_isins, start_date, end_date, index_prefix)
            if data_dict is None or len(data_dict) == 0:
                print('Not able to fetch any data.')
                return
            forward_filled_close_isins = {}
            missing_isins = {}
            forward_filled_isins = {}
            min_constraint_isins, max_constraint_isins = {}, {}
            matrix_failure_list = []
            forward_filled_metrics_isins = {}

            # Missing ISINs
            for isin, data in data_dict.items():
                if len(data_df[data_df['isin'] == isin]) == 0:
                    last_occurace = str(data.loc[data['closeprice'].notna(), ['date']].max().values[0]).split('T')[0]
                    run_date = str(self.hf.run_date)
                    last_occurance_days = np.busday_count(last_occurace, run_date)
                    if last_occurance_days != 0:
                        missing_isins[isin] = last_occurance_days
            for isins in set(remaining_isins) - set(data_dict.keys()):
                missing_isins[isin] = lookback_window

            # Forward-Filled Close-price ISINs
            if self.hf.properties['forward_fill_in_es']: # In this case, check the same value of close prices
                _, missed_close_isins = self.hf.get_closes_waterfall(self.hf.properties['close_fetch_model']['first_index'], self.hf.properties['close_fetch_model']['second_index'])
                for isin in missed_close_isins:
                    data = data_dict.get(isin)
                    if data is None or isin in missing_isins.keys():
                        continue
                    last_value = data_df.loc[data_df['isin'] == isin, 'closeprice'].values[0]
                    not_same_df = data[abs(data['closeprice'] - last_value) > self.hf.config['common']['diff']]
                    if len(not_same_df) == 0:
                        forward_filled_close_isins[isin] = len(data)
                    else:
                        slice_index = not_same_df.index[-1]
                        if len(data.loc[slice_index: ]) > 1:
                            forward_filled_close_isins[isin] = len(data.loc[slice_index: ]) - 1
            else: # In this case, check the NaN occurances of close prices
                nan_closes_today = data_df[data_df['closeprice'].isna(), 'isin'].to_list()
                if nan_closes_today:
                    data_dict = self.hf.get_es_data_in_range(nan_closes_today, start_date, end_date, index_prefix)
                    for isin, data in data_dict.items():
                        if data.loc[data['closeprice'].isna()].shape[0] == data.shape[0]:
                            forward_filled_close_isins[isin] = data.shape[0]
                        else:
                            not_nan_index = data.loc[data['closeprice'].notna()].index[-1]
                            forward_filled_close_isins[isin] = len(data[not_nan_index: ]) - 1

            # Forward-Filled ER ISINs
            for isin, data in data_dict.items():
                if isin in missing_isins.keys():
                    continue
                last_value = data_df.loc[data_df['isin'] == isin, 'raw_er'].values[0]
                not_same_df = data[abs(data['raw_er'] - last_value) > self.hf.config['common']['diff']]
                if len(not_same_df) == 0:
                    forward_filled_isins[isin] = len(data)
                else:
                    slice_index = not_same_df.index[-1]
                    if len(data.loc[slice_index: ]) > 1:
                        forward_filled_isins[isin] = len(data.loc[slice_index: ]) - 1
            forward_filled_isins = {x: y for x, y in forward_filled_isins.items() if y > self.max_forward_filled_limit}
            
            # MIN-MAX constraint violation ISINs
            isins_to_check = list(set(remaining_isins) - set(missing_isins.keys()))
            try:
                actual_min_max = self.hf.get_diff_min_max(isins_to_check).rename(columns={f'dif_min_{self.hf.schedular.lower()}': 'dif_min', f'dif_max_{self.hf.schedular.lower()}': 'dif_max'})
                if actual_min_max.empty:
                    raise Exception('Unable to fetch historical min-max values.')
                dif_check_df = pd.merge(data_df[['isin', 'raw_er']], actual_min_max, on='isin', how='inner')
                dif_max_isins = dif_check_df.loc[abs(dif_check_df['raw_er'] - dif_check_df['dif_max']) <= self.hf.config['common']['diff'], 'isin'].to_list()
                dif_min_isins = dif_check_df.loc[abs(dif_check_df['raw_er'] - dif_check_df['dif_min']) <= self.hf.config['common']['diff'], 'isin'].to_list()
                for isin in dif_min_isins:
                    if isin in forward_filled_isins.keys():
                        min_constraint_isins[isin] = forward_filled_isins[isin] + 1
                        del forward_filled_isins[isin]
                    else:
                        min_constraint_isins[isin] = 1
                for isin in dif_max_isins:
                    if isin in forward_filled_isins.keys():
                        max_constraint_isins[isin] = forward_filled_isins[isin] + 1
                        del forward_filled_isins[isin]
                    else:
                        max_constraint_isins[isin] = 1
            except Exception as e:
                print(f'Unable to get actual min-max for {self.hf.tag.upper()}-{self.hf.schedular.capitalize()}. Error: {e}')

            # Missing metrics
            metrics_to_track = self.hf.config['common']['mail_file']['metrics_to_track']
            temp_list = []
            for metric_column in metrics_to_track:
                temp_list += data_df.loc[(data_df[metric_column] == 0) | data_df[metric_column].isna(), 'isin'].to_list()
            matrix_failure_list = list(set(temp_list))

            # Forward-Filled metrics
            for isin, data in data_dict.items():
                if isin in matrix_failure_list or isin in missing_isins.keys():
                    continue
                last_values = data_df[data_df['isin'] == isin][metrics_to_track].values
                not_same_df = data[(abs(data[metrics_to_track].values - last_values) > self.hf.config['common']['diff']).any(axis=1)]
                if len(not_same_df) == 0:
                    forward_filled_metrics_isins[isin] = len(data)
                else:
                    slice_index = not_same_df.index[-1]
                    if len(data.loc[slice_index: ]) > 1:
                        forward_filled_metrics_isins[isin] = len(data.loc[slice_index: ]) - 1    
            forward_filled_metrics_isins = {x: y for x, y in forward_filled_metrics_isins.items() if y > self.max_forward_filled_limit}
                    
        return nan_isins, missing_isins, isin_not_traded, forward_filled_close_isins, forward_filled_isins, max_constraint_isins, min_constraint_isins, matrix_failure_list, forward_filled_metrics_isins


    def prepare_mail_content(self):
        '''
        Method to prepare the subject, the body and the attachment files for the mail.
        '''
        # Get the required metadata after data analysis
        files = {}
        try:
            nan_isins, missing_isins, isin_not_traded, forward_filled_close_isins, forward_filled_isins, max_constraint_isins, min_constraint_isins, matrix_failure_list, forward_filled_metrics_isins = self.do_data_analysis()
        except Exception as e:
            traceback_error = traceback.format_exc()
            print('Issue generating data quality report. Error:', e)
            subject = self.mail_subject
            error_message = f"Daily run script status: Complete. \n\nReport file generation failed. \nError: \n{e} \n\nTraceback message: \n{traceback_error}"
            message = error_message + '\n\n' + self.error_mail_footer
            to_recipient = 'error_mail'
            return subject, message, files, to_recipient

        urgent_mail_check = False
        if len(forward_filled_isins) > 0 and max(forward_filled_isins.values()) > self.urgent_forward_filled_limit:
            urgent_mail_check = True
        if len(forward_filled_close_isins) > 0 and max(forward_filled_close_isins.values()) > self.urgent_forward_filled_close_limit:
            urgent_mail_check = True
        
        counter = 0
        message = ""
        message += self.mail_header

        counter += 1
        message += f"\t{counter}. Number of companies worked upon (traded companies): {len(self.hf.isin_list)}\n"

        counter += 1
        if len(forward_filled_close_isins) != 0:
            message += f"\t{counter}. Number of companies with forward-filled close prices: {len(forward_filled_close_isins)}\n"
        else:
            message += f"\t{counter}. No company with with forward-filled close price.\n"
        
        counter += 1
        if len(nan_isins) != 0:
            message += f"\t{counter}. Number of companies with 0 or NaN ERs: {len(nan_isins)}\n"
        else:
            message += f"\t{counter}. No company with 0 or NaN ER.\n"
        
        counter += 1
        if len(missing_isins) != 0:
            message += f"\t{counter}. Number of companies with missing data: {len(missing_isins)}\n"
        else:
            message += f"\t{counter}. No company with missing data.\n"
        
        counter += 1
        if len(isin_not_traded) != 0:
            message += f"\t{counter}. Number of companies not traded: {len(isin_not_traded)}\n"
        else:
            message += f"\t{counter}. All the companies traded.\n"
        
        counter += 1
        if len(forward_filled_isins) != 0:
            message += f"\t{counter}. Number of companies with forward-filled ER for more than {self.max_forward_filled_limit} days: {len(forward_filled_isins)}\n"
        else:
            message += f"\t{counter}. No company with forward-filled ER for more than {self.max_forward_filled_limit} days.\n"
        
        counter += 1
        if len(max_constraint_isins) != 0:
            message += f"\t{counter}. Number of companies with max-diff constraint violation: {len(max_constraint_isins)}\n"
        else:
            message += f"\t{counter}. No company with max-diff constraint violation.\n"
        
        counter += 1
        if len(min_constraint_isins) != 0:
            message += f"\t{counter}. Number of companies with min-diff constraint violation: {len(min_constraint_isins)}\n"
        else:
            message += f"\t{counter}. No company with min-diff constraint violation.\n"

        counter += 1
        if len(matrix_failure_list) != 0:
            message += f"\t{counter}. Number of companies with matrix caluculation failure: {len(matrix_failure_list)}\n"
        else:
            message += f"\t{counter}. No company with matrix caluculation failure.\n"

        counter += 1
        if len(forward_filled_metrics_isins) != 0:
            message += f"\t{counter}. Number of companies with forward-filled matrix for more than {self.max_forward_filled_limit} days: {len(forward_filled_metrics_isins)}\n"
        else:
            message += f"\t{counter}. No company with forward-filled matrix for more than {self.max_forward_filled_limit} days.\n"

        message += '\n' + self.mail_footer
        subject = self.urgent_tag + self.mail_subject if urgent_mail_check else self.mail_subject
        
        # Report file preparation
        report_file_cols = self.hf.config['common']['mail_file']['generated_numeric_columns']
        
        all_isins = set(nan_isins.keys()
                       ).union(
                    set(forward_filled_close_isins.keys())
                ).union(
                    set(forward_filled_isins.keys())
                ).union(
                    set(max_constraint_isins.keys())
                ).union(
                    set(min_constraint_isins.keys())
                ).union(
                    set(missing_isins.keys())
                ).union(
                    set(matrix_failure_list)
                ).union(
                    set(forward_filled_metrics_isins.keys())
                )
            
        if len(all_isins) != 0:
            report_file = self.hf.all_df[self.hf.all_df['isin'].isin(all_isins)][self.hf.config['common']['mail_file']['columns']].rename(columns=self.hf.config['common']['mail_file']['rename']).reset_index(drop=True) 
            
            for isin in report_file['isin'].values:
                # Missing companies ERs
                if len(missing_isins) != 0:
                    value = missing_isins.get(isin)
                    value = value if value is not None else 0
                    report_file.loc[report_file['isin'] == isin, 'missing_records'] = value
                # Forward-filled close prices
                if len(forward_filled_close_isins) != 0:
                    value = forward_filled_close_isins.get(isin)
                    value = value if value is not None else 0
                    report_file.loc[report_file['isin'] == isin, 'forward_filled_closeprices'] = value
                # Forward-filled ERs
                if len(forward_filled_isins) != 0:
                    value = forward_filled_isins.get(isin)
                    value = value if value is not None else 0
                    report_file.loc[report_file['isin'] == isin, 'forward_filled_er'] = value
                # NaN companies
                if len(nan_isins) != 0:
                    value = nan_isins.get(isin)
                    value = value if value is not None else 0
                    report_file.loc[report_file['isin'] == isin, 'nan_or_zero_er'] = value
                # Max constraint er voilation
                if len(max_constraint_isins) != 0:
                    value = max_constraint_isins.get(isin)
                    value = value if value is not None else 0
                    report_file.loc[report_file['isin'] == isin, 'max_constraint_violation'] = value
                # Min constraint er voilation
                if len(min_constraint_isins) != 0:
                    value = min_constraint_isins.get(isin)
                    value = value if value is not None else 0
                    report_file.loc[report_file['isin'] == isin, 'min_constraint_violation'] = value
                # Matrix calculation failure
                if len(matrix_failure_list) != 0: 
                    value = 'yes' if isin in matrix_failure_list else 'no'
                    report_file.loc[report_file['isin'] == isin, 'matrix_failed'] = value
                # Forward-filled Matrix
                if len(forward_filled_metrics_isins) != 0: 
                    value = forward_filled_metrics_isins.get(isin)
                    value = value if value is not None else 0
                    report_file.loc[report_file['isin'] == isin, 'forward_filled_metrics'] = value
            report_file[[col for col in report_file.columns if col in report_file_cols]] = report_file[[col for col in report_file.columns if col in report_file_cols]].astype(int)
            files[f'report_file_{self.hf.tag.lower()}_{self.hf.schedular.lower()}_{self.hf.run_date}'] = report_file

        if isin_not_traded != 0:
            untraded_file = self.hf.all_df[self.hf.all_df['isin'].isin(isin_not_traded)][self.hf.config['common']['mail_file']['columns']].rename(columns=self.hf.config['common']['mail_file']['rename']).reset_index(drop=True) 
            files[f'untraded_companies_{self.hf.tag.lower()}_{self.hf.schedular.lower()}_{self.hf.run_date}'] = untraded_file

        to_recipient = 'success_mail'
        
        return subject, message, files, to_recipient


    def prepare_data_and_send_mail(self):
        # Prepare the subject, mail body and the attchment files
        subject, mail_body, files, to_recipient = self.prepare_mail_content()
        
        attachments = []
        for key in files.keys():
            file = files[key]
            if not os.path.exists(self.temp_folder):
                os.mkdir(self.temp_folder)
            filepath = os.path.join(self.temp_folder, key+'.csv')
            attachments.append(filepath)
            file.to_csv(filepath, index=False)
            print(f'File "{key}" saved locally.')
        
        print(f'Subject: {subject}\n')
        print(f'Body: \n{mail_body}\n')
        print(f'Attachments: {attachments}')

        # Download the latest credentials from s3
        credential_filepath = self.download_temp_json()
        
        # Send the mail
        mail_id = self.send_mail(subject, mail_body, attachments, to_recipient)
        if mail_id is not None:
            print('Successfully send mail, ID:', mail_id)
        else:
            print('E-mail not sent.')
        
        # Delete static files
        files_to_delete = [credential_filepath, *attachments]
        self.delete_temp_data(files_to_delete)


    def send_error_mail(self, error_message):
        # Download the latest credentials from s3
        credential_filepath = self.download_temp_json()

        subject = self.error_mail_subject
        mail_body = error_message + '\n\n' + self.error_mail_footer
        attachments = []

        mail_id = self.send_mail(subject, mail_body, attachments, 'error_mail')
        if mail_id is not None:
            print('Successfully send error mail, ID:', mail_id)
        else:
            print('E-mail not sent.')
