import numpy as np
import datetime
import pandas as pd
from tqdm import tqdm
import requests
from pandas.tseries.offsets import BDay
from helper_functions import Helpers

def main(today, schedular, tag):
    hf = Helpers(today, schedular, tag)
    year = today.year

    bucket_name = hf.properties['bucket_name']
    best_model_filename = hf.properties['best_model_filename']
    daily_metrics_filename = hf.properties['daily_metrics_filename']
    es_index_prefix = hf.properties['es_index_prefix']
    metrics_es_index_prefix = hf.properties['metrics_es_index_prefix']

    save_files = hf.properties['save_files']
    fetch_period = int(hf.config['common']['lookback_window'])
    # Start and end days based on lookback window
    start_date = (today - BDay(1 + fetch_period)).strftime('%Y-%m-%d')
    end_date = (today - BDay(1)).strftime('%Y-%m-%d')

    forward_fill_in_es = hf.properties['forward_fill_in_es']

    
    # Daily Prediction Run


    # Check for companies traded today
    traded_isins = hf.security_traded_last()
    if str(traded_isins) == str(np.nan): # Not able to fetch the list of traded isins
        print('Unable to fetch the list of traded isins.')
    else:
        isin_not_traded = list(set(hf.isin_list) - set(traded_isins))
        print(f'Isins not traded on {str(hf.run_date)} are ({len(isin_not_traded)}): \n{isin_not_traded}')
        # Update the isin list to traded isins only
        hf.isin_list = traded_isins

    gradient_size = hf.properties['gradient_size']
    multiplier = 1 / (np.e * (np.e ** gradient_size - 1) / (np.e - 1))
    smoothening_gradient = [multiplier * (np.e ** (i + 1)) for i in range(gradient_size)]

    es_index_mapping = hf.properties['es_index_mapping']
    all_models = list(es_index_mapping.keys())
    base_model = hf.properties['base_model']


    # 2 year historical min-max
    actual_min_max = hf.get_diff_min_max()


    # Collect the close data for all models (waterfall mechanism)
    closes, remaining_isins = hf.get_closes_waterfall(hf.properties['close_fetch_model']['first_index'], hf.properties['close_fetch_model']['second_index'])
    if len(remaining_isins) != 0:
        print('Number of isins with close prices not available', len(remaining_isins))
        print("Couldn't fetch prices for isin(s):", remaining_isins)
                
        # Forward fill close price in the lookback period if required
        if forward_fill_in_es: 
            data_dict_first = hf.get_es_data_in_range(remaining_isins, start_date, end_date, es_index_mapping[hf.properties['close_fetch_model']['first_index']]['predictions'])
            data_dict_second = hf.get_es_data_in_range(remaining_isins, start_date, end_date, es_index_mapping[hf.properties['close_fetch_model']['second_index']]['predictions'])

            for isin in remaining_isins:
                try:
                    value = data_dict_first['closeprice'].ffill().tail(1).values[0]
                except:
                    value = np.nan
                if str(value) == str(np.nan):
                    try:
                        value = data_dict_second[isin]['closeprice'].ffill().tail(1).values[0]
                    except:
                        value = np.nan
                if isin not in closes['isin'].values:
                    closes = pd.concat([closes, pd.DataFrame([[closes['date'].unique()[0], isin, value]], columns = closes.columns.values)], axis=0)
                else:
                    closes.loc[closes['isin'] == isin, ['closeprice']] = value
            if closes['closeprice'].isna().sum() == 0:
                print('Close price forward-filling successful. Still remaining isins:', closes.loc[closes['closeprice'].isna(), 'isin'].tolist())
        else:
            print('Close price forward-filling not required.')
    else:
        print('All traded isins close price available.')


    # Combine the data
    df = closes
    for model in all_models:
        print('Fetching data for', model)
        try:
            if model != base_model:
                model_data = hf.get_es_data_datewise(es_index_mapping[model]['predictions'])[['isin', es_index_mapping[model]['column']]].rename(columns={es_index_mapping[model]['column']: f'{hf.schedular.lower()}_predictions_{model}'})
            else:
                all_model_data = hf.get_es_data_datewise(es_index_mapping[model]['predictions'])
                model_data = all_model_data[['isin', es_index_mapping[model]['column']]].rename(columns={es_index_mapping[model]['column']: f'{hf.schedular.lower()}_predictions_{model}'})
            df = pd.merge(df, model_data, on='isin', how='left')
        except:
            df[f'{hf.schedular.lower()}_predictions_{model}'] = np.nan
    # Convert information model predictions from ratios to percentages
    if f'{hf.schedular.lower()}_predictions_inf' in df.columns:
        df[f'{hf.schedular.lower()}_predictions_inf'] = df[f'{hf.schedular.lower()}_predictions_inf'] * 100


    # Set the base model and the raw er values
    df['model'] = base_model
    df['raw_er'] = df[f'{hf.schedular.lower()}_predictions_{base_model}']


    # Handle the missing predictions
    nan_data_isins = df.loc[df['raw_er'].isna(), 'isin'].to_list()
    if len(nan_data_isins) > 0:
        print('Number of isins for which data is not present are', len(nan_data_isins))
        data_dict = hf.get_es_data_in_range(nan_data_isins, start_date, end_date, es_index_mapping[base_model]['predictions']) # Fetch given period data for missing isins
        if data_dict is not None:
            temp_all_data = all_model_data.copy()
            all_model_data = []
            for isin, data in data_dict.items():
                data = data.ffill().tail(1)
                value = data[[es_index_mapping[model]['column']]].values[0][0]
                df.loc[df['isin'] == isin, 'raw_er'] = value
                # Update the input data as well
                temp_model_data = pd.concat([data, temp_all_data[temp_all_data['isin'] == isin]], axis=0)
                temp_model_data = temp_model_data.ffill().tail(1)
                all_model_data.append(temp_model_data)
            all_model_data = pd.concat(all_model_data, axis=0)
            all_model_data = pd.concat([all_model_data, temp_all_data[~temp_all_data['isin'].isin(all_model_data['isin'].to_list())]]).reset_index(drop=True)
            print('Data forward filled successful.')
        else:
            print(f'No data found for forward filling in the window {hf.config['common']['lookback_window']} business days.')


    # Min-Max capping
    df = pd.merge(df, actual_min_max, on='isin', how='left')
    df.loc[(df[f'dif_max_{hf.schedular.lower()}'].notna()) & (df[f'dif_max_{hf.schedular.lower()}'] < df['raw_er']), 'raw_er'] = df.loc[(df[f'dif_max_{hf.schedular.lower()}'].notna()) & (df[f'dif_max_{hf.schedular.lower()}'] < df['raw_er']), f'dif_max_{hf.schedular.lower()}']
    df.loc[(df[f'dif_min_{hf.schedular.lower()}'].notna()) & (df[f'dif_min_{hf.schedular.lower()}'] > df['raw_er']), 'raw_er'] = df.loc[(df[f'dif_min_{hf.schedular.lower()}'].notna()) & (df[f'dif_min_{hf.schedular.lower()}'] > df['raw_er']), f'dif_min_{hf.schedular.lower()}']
    df.drop(columns=[f'dif_min_{hf.schedular.lower()}', f'dif_max_{hf.schedular.lower()}'], inplace=True)
    if forward_fill_in_es:
        df = df[df['closeprice'].notna()].reset_index(drop=True)


    # Forward fill and smoothen
    past_data = pd.DataFrame()
    all_past_data = pd.DataFrame()
    for day in tqdm(range(len(smoothening_gradient) + 2)):
        past_data_df = hf.get_es_data_datewise(es_index_prefix, date=(today - BDay(day + 1)).strftime('%Y-%m-%d'))
        if past_data_df.empty:
            continue
        else:
            all_past_data = pd.concat([all_past_data, past_data_df], axis=0) # For input data file
        past_data = pd.concat([past_data, past_data_df[hf.properties['past_data_cols']]], axis=0)
    past_data = pd.concat([past_data, df[hf.properties['past_data_cols']]], axis=0)
    print('Past data (used for smoothenining) shape:', past_data.shape)

    forward_filled_isins = []
    for isin, data in tqdm(past_data.groupby('isin')):
        data = data.drop_duplicates(subset='date').sort_values(by='date').ffill()
        data['raw_er'] = data['raw_er'].replace({0: np.nan})
        if data['raw_er'].notna().sum() == 0:
            forward_filled_isins.append(isin)
        df.loc[df['isin'] == isin, 'predicted_er'] = (data[-len(smoothening_gradient):]['raw_er'].values * smoothening_gradient).sum() if len(data) >= len(smoothening_gradient) else data.iloc[-1]['raw_er']

    # Update the input data as well
    cols_to_update = [col for col in all_model_data if col not in ['date', 'isin']]
    for isin in forward_filled_isins:
        data = all_past_data[all_past_data['isin'] == isin].drop_duplicates(subset='date').sort_values(by='date').ffill()
        temp_model_data = pd.concat([data, all_model_data[all_model_data['isin'] == isin]], axis=0).ffill().tail(1)
        all_model_data.loc[all_model_data['isin'] == isin, cols_to_update] = temp_model_data[cols_to_update].values[0]

    print('Number of records in the file:', len(df))

    # Save the input data
    if save_files:
        hf.s3conf.write_advanced_as_df(all_model_data, hf.properties['explainability_s3']['bucket_name'], hf.properties['explainability_s3']['folder_name'])

    # Base model metrics
    try:
        metrics = hf.get_es_data_datewise(es_index_mapping[base_model]['metrics'])[hf.properties['prediction_columns']['column_list']].rename(columns=hf.properties['prediction_columns']['rename'])
    except:
        metrics = pd.DataFrame(columns=hf.properties['prediction_columns']['column_list']).rename(columns=hf.properties['prediction_columns']['rename'])


    # Forward fill metrics data in case missing
    nan_metrics_data_isins = metrics.loc[metrics[[col for col in metrics.columns if col != 'isin']].isna().any(axis=1), 'isin'].to_list()
    if forward_fill_in_es and len(nan_metrics_data_isins) != 0:
        data_dict = hf.get_es_data_in_range(nan_metrics_data_isins, start_date, end_date, es_index_mapping[base_model]['metrics']) # Fetch given period data for missing isins
        if data_dict is not None:
            for isin, data in data_dict.items():
                last_row_data = data[hf.properties['prediction_columns']['column_list']].rename(columns=hf.properties['prediction_columns']['rename']).replace({0: np.nan}).ffill().tail(1)
                metrics = pd.concat([last_row_data, metrics], axis=0)
            isins = metrics['isin']
            metrics = metrics.groupby('isin').ffill()
            metrics['isin'] = isins
            metrics = metrics.drop_duplicates(subset=['isin'], keep='last').reset_index(drop=True)
            print('Metrics data forward filled successful.')
        else:
            print(f'No data found for forward filling in the window {hf.config['common']['lookback_window']} business days.')

    df = pd.merge(df, metrics, on='isin', how='left')


    # Additional columns
    additional_columns = hf.properties['additional_columns']
    additional_data = hf.get_es_data_datewise(es_index_mapping[base_model]['predictions'])[['isin'] + additional_columns]
    df = pd.merge(df, additional_data, on='isin')


    # Save the data
    if save_files:
        save_params = {
            's3': {
                'bucket_name': bucket_name,
                'folder_name': best_model_filename
            },
            'es': {
                'year': year,
                'index_prefix': es_index_prefix
            },
            'versioned_s3': {
                'model_year': f'{year}_Q4',
                'type': 'predictions'
            }
        }
        hf.save_data(df, save_params)


    # Daily Metrics Run


    # ER model data for comparison
    try:
        base_metrics_file_today = hf.get_es_data_datewise(es_index_mapping['er']['metrics'])
        base_metrics_file_today = base_metrics_file_today[hf.properties['prediction_columns']['column_list']].rename(columns = hf.properties['base_metrics_columns'])
    except:
        base_metrics_file_today = pd.DataFrame(columns=hf.properties['prediction_columns']['column_list']).rename(columns = hf.properties['base_metrics_columns'])
        print('Base model metrics not found.')

    try:
        daily_metrics = hf.get_es_data_datewise(es_index_mapping[base_model]['metrics'])
        daily_metrics = daily_metrics[hf.properties['metrics_columns']['get_columns']].rename(columns = hf.properties['metrics_columns']['rename'])
    except:
        daily_metrics = pd.DataFrame(columns=hf.properties['metrics_columns']['get_columns'])

    try:
        best_file_today = hf.s3conf.read_advanced_as_df(bucket_name, best_model_filename)[hf.properties['best_file_columns']['column_list']].rename(columns = hf.properties['best_file_columns']['rename'])
    except:
        best_file_today = hf.s3conf.read_advanced_as_df(bucket_name, best_model_filename)[[col for col in hf.properties['best_file_columns']['column_list'] if col not in hf.properties['best_file_columns']['rename'].keys()]]
        best_file_today[list(hf.properties['best_file_columns']['rename'].values())] = np.nan


    daily_metrics = pd.merge(daily_metrics, best_file_today, how = 'right', on = 'isin')
    daily_metrics = pd.merge(daily_metrics, base_metrics_file_today, how = 'left', on = 'isin')


    # Forward fill metrics data in case missing
    nan_metrics_isins = daily_metrics.loc[daily_metrics[[col for col in daily_metrics.columns if col != 'isin']].isna().any(axis=1), 'isin'].to_list()
    if forward_fill_in_es and len(nan_metrics_isins) != 0:
        data_dict = hf.get_es_data_in_range(nan_metrics_isins, start_date, end_date, metrics_es_index_prefix) # Fetch given period data for missing isins
        if data_dict is not None:
            for isin, data in data_dict.items():
                last_row_data = data[daily_metrics.columns].replace({0: np.nan}).ffill().tail(1)
                daily_metrics = pd.concat([last_row_data, daily_metrics], axis=0)
            isins = daily_metrics['isin']
            daily_metrics = daily_metrics.groupby('isin').ffill()
            daily_metrics['isin'] = isins
            daily_metrics = daily_metrics.drop_duplicates(subset=['isin'], keep='last').reset_index(drop=True)
            print('Metrics data forward filled successful.')
        else:
            print(f'No data found for forward filling in the window {hf.config['common']['lookback_window']} business days.')

    daily_metrics['schedular'] = schedular.capitalize()
    daily_metrics['date'] = pd.to_datetime(today)

    
    daily_metrics = daily_metrics[hf.properties['metrics_columns']['column_list']]
    print('Metrics shape:', daily_metrics.shape)

    # Save the data
    if save_files:
        save_params_metrics = {
            's3': {
                'bucket_name': bucket_name,
                'folder_name': daily_metrics_filename
            },
            'es': {
                'year': year,
                'index_prefix': metrics_es_index_prefix
            },
            'versioned_s3': {
                'model_year': f'{year}_Q4',
                'type': 'metrics'
            }
        }
        hf.save_data(daily_metrics, save_params_metrics)   


        # Pre-portfolio trigger
        preportfolio_url = hf.properties.get('preportfolio_url')
        if preportfolio_url is not None and preportfolio_url != '':
            response = requests.get(preportfolio_url)
            print('Preportfolio trigger response:', response)