"""
Best Model Scripts - Main Driver

Refactored main driver for best model analysis with improved error handling,
logging, and configuration management using shared utilities.

Usage:
    python main.py <tag> <scheduler> <run_date>

Args:
    tag: Universe tag (e.g., 'aieq', 'aigo')
    scheduler: Run frequency ('daily', 'monthly', 'quarterly')
    run_date: Date in 'yyyy-mm-dd' format (optional, defaults to last business day)
"""

import sys
import datetime
from pathlib import Path
from typing import Dict, Callable, Optional
from pandas.tseries.offsets import BDay

# Import shared utilities package (installed from Git)
from shared_utils import (
    load_config, validate_common_config, create_model_logger, create_email_sender,
    create_error_handler, ErrorSeverity, ModelError, ConfigurationError, ErrorContext
)


# Import model functions
from aieq_monthly import main as aieq_monthly
from aieq_quarterly import main as aieq_quarterly
from aieq_daily import main as aieq_daily
from aigo_monthly import main as aigo_monthly
from email_notification import AnalysisNotification

class BestModelRunner:
    """Best Model execution orchestrator with improved error handling."""

    def __init__(self, config_path: str = 'config.yaml'):
        """Initialize the Best Model Runner."""
        self.config = load_config(config_path)
        self._validate_config()

        # Function mapping for different model combinations
        self.function_mapping: Dict[str, Callable] = {
            'aieq_monthly': aieq_monthly,
            'aieq_quarterly': aieq_quarterly,
            'aieq_daily': aieq_daily,
            'aigo_monthly': aigo_monthly
        }

        # Initialize utilities (will be set per run)
        self.logger = None
        self.error_handler = None
        self.email_sender = None
        self.analysis_notification = None

    def _validate_config(self) -> None:
        """Validate configuration requirements."""
        if not validate_common_config(self.config):
            raise ConfigurationError("Configuration validation failed")

    def _setup_run_context(self, tag: str, scheduler: str, date: datetime.date) -> None:
        """Setup logging and utilities for a specific run."""
        # Setup logger
        self.logger = create_model_logger(
            model_name='best_model',
            tag=tag,
            scheduler=scheduler,
            run_date=date.strftime('%Y-%m-%d'),
            log_dir=self.config.get('common.temporary_folder', 'logs')
        )

        # Setup error handler
        self.error_handler = create_error_handler(self.logger.get_logger())

        # Setup email sender
        from eq_common_utils.utils.config.s3_config import s3_config
        s3_client = s3_config()
        self.email_sender = create_email_sender(self.config.to_dict(), s3_client)

        # Setup analysis notification (backward compatibility)
        try:
            sender_name = 'Best Model System'
            self.analysis_notification = AnalysisNotification(date, scheduler, tag, sender_name)
        except Exception as e:
            self.logger.warning(f"Could not initialize AnalysisNotification: {e}")

    def execute_model(self, date: datetime.date, scheduler: str, tag: str) -> bool:
        """
        Execute the appropriate model function.

        Args:
            date: Run date
            scheduler: Scheduler type
            tag: Model tag

        Returns:
            True if successful, False otherwise
        """
        function_key = f'{tag}_{scheduler.lower()}'

        if function_key not in self.function_mapping:
            raise ModelError(
                f"No model function found for {function_key}",
                ErrorSeverity.HIGH,
                "INVALID_MODEL_COMBINATION",
                {"tag": tag, "scheduler": scheduler, "available_functions": list(self.function_mapping.keys())}
            )

        model_function = self.function_mapping[function_key]

        try:
            self.logger.info(f"Executing model function: {function_key}")
            model_function(date, scheduler, tag)
            self.logger.info(f"Model function completed successfully: {function_key}")
            return True

        except Exception as e:
            self.error_handler.handle_error(
                e,
                ErrorSeverity.HIGH,
                {"function": function_key, "date": str(date), "scheduler": scheduler, "tag": tag}
            )
            return False


    def send_trigger_notification(self, tag: str, scheduler: str, date: datetime.date) -> bool:
        """Send trigger notification email."""
        try:
            subject = f"[Best-ER Model] Script run started for {tag.upper()}-{scheduler.capitalize()} for date {date} [EOM]"
            recipients = self.config.get('common.gmail_creds.to_mail_ids.trigger_mail', [])

            if not recipients:
                self.logger.warning("No trigger mail recipients configured")
                return False

            success = self.email_sender.send_email(subject, recipients, "Best Model run initiated.")

            if success:
                self.logger.info(f"Trigger notification sent successfully")
            else:
                self.logger.warning("Failed to send trigger notification")

            return success

        except Exception as e:
            self.error_handler.handle_error(e, ErrorSeverity.LOW, {"operation": "send_trigger_notification"})
            return False

    def send_completion_notification(self, success: bool, tag: str, scheduler: str, date: datetime.date) -> None:
        """Send completion notification email."""
        try:
            if self.analysis_notification:
                if success:
                    self.analysis_notification.prepare_data_and_send_mail()
                else:
                    error_message = f"Best Model run failed for {tag.upper()}-{scheduler.capitalize()} on {date}"
                    self.analysis_notification.send_error_mail(error_message)
            else:
                # Fallback to direct email
                status = "COMPLETED" if success else "FAILED"
                subject = f"[Best-ER Model] Run {status} for {tag.upper()}-{scheduler.capitalize()} for date {date}"
                recipients = self.config.get(f'common.gmail_creds.to_mail_ids.{"success" if success else "error"}_mail', [])

                if recipients:
                    body = f"Best Model run {status.lower()} for {tag} {scheduler} on {date}"
                    self.email_sender.send_email(subject, recipients, body)

        except Exception as e:
            self.error_handler.handle_error(e, ErrorSeverity.LOW, {"operation": "send_completion_notification"})

    def run(self, tag: str, scheduler: str, date: Optional[datetime.date] = None) -> bool:
        """
        Execute the complete best model run.

        Args:
            tag: Model tag
            scheduler: Scheduler type
            date: Run date (defaults to last business day)

        Returns:
            True if successful, False otherwise
        """
        # Set default date if not provided
        if date is None:
            date = (datetime.date.today() - BDay(1)).date()

        # Setup run context
        self._setup_run_context(tag, scheduler, date)

        # Log run start
        self.logger.log_model_start({
            'tag': tag,
            'scheduler': scheduler,
            'date': str(date)
        })

        success = False

        try:
            # Send trigger notification
            self.send_trigger_notification(tag, scheduler, date)

            # Execute model
            with ErrorContext(self.error_handler, f"model_execution_{tag}_{scheduler}"):
                success = self.execute_model(date, scheduler, tag)

            # Log completion
            self.logger.log_model_end(success, {
                'tag': tag,
                'scheduler': scheduler,
                'date': str(date)
            })

        except Exception as e:
            self.error_handler.handle_error(e, ErrorSeverity.CRITICAL, {
                "operation": "best_model_run",
                "tag": tag,
                "scheduler": scheduler,
                "date": str(date)
            })
        finally:
            # Send completion notification
            self.send_completion_notification(success, tag, scheduler, date)

        return success


def parse_arguments() -> tuple:
    """Parse command line arguments."""
    if len(sys.argv) < 3:
        raise ValueError("Usage: python main.py <tag> <scheduler> [run_date]")

    tag = sys.argv[1].lower()
    scheduler = sys.argv[2].capitalize()

    # Parse date
    date = None
    if len(sys.argv) > 3:
        try:
            date = datetime.date(*map(int, sys.argv[3].split('-')))
        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid date format. Use YYYY-MM-DD: {e}")

    return tag, scheduler, date


def main():
    """Main execution function."""
    try:
        # Parse arguments
        tag, scheduler, date = parse_arguments()

        # Initialize and run
        runner = BestModelRunner()
        success = runner.run(tag, scheduler, date)

        return 0 if success else 1

    except Exception as e:
        print(f"Critical error in best model execution: {e}")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
