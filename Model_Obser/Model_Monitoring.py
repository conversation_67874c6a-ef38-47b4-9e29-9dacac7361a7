import pandas as pd
import numpy as np
import requests
import json
import io
from aws_requests_auth.aws_auth import AWSRe<PERSON>sAuth
from datetime import datetime, timedelta
import argparse
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime
import boto3
from io import StringIO

yesterday = datetime.today() - timedelta(days=1)
date=yesterday.strftime('%Y-%m-%d')
print(date)

#if __name__ == "__main__":

parser = argparse.ArgumentParser(prog='Model-Monitoring', description="Script for Daily Model Monitoring Observations.")

parser.add_argument('-t', '--tag')

args = parser.parse_args()

tag = args.tag


selected_tag =tag

smtp_server = "smtp.gmail.com"
smtp_port = 587
username = "<EMAIL>"
password = "vies rtlt mxwb qyap"

auth_es = AWSRequestsAuth(aws_access_key='********************',
                               aws_secret_access_key='xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV',
                               aws_host = 'search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com', 
                               aws_region='us-east-1',
                               aws_service='es') #------ Elastic search Credentials





auth_es_tr = AWSRequestsAuth(aws_access_key='********************',
                                aws_secret_access_key='oVD9ZhQRCGz74RzQpIqqiyIvyIXIpRIElO+8ftvb',
                                aws_host='search-training-data-7llze3ehbf3ry4hu5rwlu662ye.us-east-1.es.amazonaws.com',
                                aws_region='us-east-1',
                                aws_service='es')       #---- training elastic search Credentials



s3 = boto3.client('s3', 
                  aws_access_key_id= "********************",
                  aws_secret_access_key= "bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/", 
                    region_name='us-east-1') #-----------------S3 Credentials



aieq_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aieq').json()
aieq = pd.DataFrame(aieq_json["data"]["masteractivefirms_aieq"])

if 'isin' in aieq.columns:
    isin_data = aieq[['isin']]
    #print(isin_data)
else:
    print("Column 'isin' not found in the DataFrame")
real_aieq_isin = pd.DataFrame(isin_data)


tier1_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aipex').json()
tier1 = pd.DataFrame(tier1_json["data"]["masteractivefirms_aipex"])
if 'isin' in aieq.columns:
    isin_data = tier1[['isin']]
    #print(isin_data)
else:
    print("Column 'isin' not found in the DataFrame")
real_tier1_isin = pd.DataFrame(isin_data)


aigo_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aigo').json()
aigo_df = pd.DataFrame(aigo_json["data"]["masteractivefirms_aigo"])
aigo_filtered_df = aigo_df[aigo_df['tags'].str.contains('aigo') &~aigo_df['tags'].str.contains('aifint|indsec|aieq')]
real_aigo_isin = aigo_filtered_df[['isin']]



indt1_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=indt1').json()
indt1 = pd.DataFrame(indt1_json["data"]["masteractivefirms_indt1"])
if 'isin' in aieq.columns:
    isin_data = indt1[['isin']]
    #print(isin_data)
else:
    print("Column 'isin' not found in the DataFrame")
real_indt1_isin = pd.DataFrame(isin_data)


count_tier1_isin = len(real_tier1_isin)
count_aieq_isin = len(real_aieq_isin)
count_aigo_isin =len(real_aigo_isin)
count_indt1_isin =len(real_indt1_isin)
#print(count_aigo_isin)


macro_df = pd.read_csv("macro_deployment.csv")
if selected_tag == "aieq":
    filtered_df = macro_df[macro_df["Country_Code"] == "USA"]
elif selected_tag == "indt1":
    filtered_df = macro_df[macro_df["Country_Code"] == "IND"]
elif selected_tag == "aigo":
    filtered_df = macro_df[~macro_df["Country_Code"].isin(["USA", "IND"])]
else:
    raise ValueError("Invalid tag! Use 'aieq', 'indt1', or 'aigo'.")
isin_df_macro = pd.DataFrame({"isin": filtered_df["Country_Code"] + "_" + filtered_df["Type"].astype(str)})
print(f"ISINs for tag '{selected_tag}':")
print(isin_df_macro.head())
print(f"Total ISINs: {len(isin_df_macro)}")




## ------------------------------------------------phase model total hits-----------------
# Fetch and read data
bucket_name = "phase-model"
file_path = "adhoc/country_identifier_mapping_new.csv"
obj = s3.get_object(Bucket=bucket_name, Key=file_path)
df = pd.read_csv(StringIO(obj['Body'].read().decode('utf-8')))

# Strategy to word2 mapping (expanded to include all possible words)
strategy_map = {
    'iwf': 'G',
    'iwf_corr': 'G_B',
    'mtum': 'M',
    'mtum_corr': 'M_B',
    'sphq': 'Q',
    'sphq_corr': 'Q_B',
    'vlue': 'V',
    'vlue_corr': 'V_B',
}



# Filter countries based on selected tag
if selected_tag == 'aieq':
    countries = ['USA']
elif selected_tag == 'indt1':
    countries = ['IND']
elif selected_tag == 'aigo':
    # Get all unique countries except USA and IND
    all_countries = df['country_code'].unique()
    countries = [c for c in all_countries if c not in ['USA', 'IND']]
else:
    raise ValueError("Invalid tag. Must be 'aieq', 'indt1', or 'aigo'")

# Filter dataframe for selected countries
filtered_df = df[df['country_code'].isin(countries)]

valid_isins = set()

for _, row in filtered_df.iterrows():
    country = row['country_code']
    identifier = str(row['identifiers'])
    
    # Find ALL strategy words in each identifier
    found_strategies = set()
    for word in strategy_map.keys():
        if word in identifier.lower():
            found_strategies.add(word)
    
    # Generate ISINs for each found strategy
    for strategy in found_strategies:
        if strategy in strategy_map:
            # Add both base and _corr versions if applicable
            if '_corr' in strategy:
                valid_isins.add(f"{country}_{strategy_map[strategy]}")
            else:
                # Add base version
                valid_isins.add(f"{country}_{strategy_map[strategy]}")
                # Add _corr version if base exists
                corr_strategy = f"{strategy}_corr"
                if corr_strategy in strategy_map:
                    valid_isins.add(f"{country}_{strategy_map[corr_strategy]}")

isin_df_phase = pd.DataFrame({'isin': sorted(valid_isins)})

print(f"Total ISINs generated for {selected_tag}: {len(valid_isins)}")
print(isin_df_phase.head())

##fetch data----

def fetch_data_es(start_date, end_date, schedular, auth_es, model_name=None, functionality_type=None):

    # Convert dates to string format
    start_date = pd.to_datetime(start_date).strftime('%Y-%m-%d')
    end_date = pd.to_datetime(end_date).strftime('%Y-%m-%d')
    year = pd.to_datetime(start_date).year

    # Special handling for specific models
    if model_name =="dcf" and selected_tag =="indt1":
        index_name ="training_eq_dcf*"
        isin_key="Isin"
    elif model_name == "dcf":
        index_name = "eq_dcf*"  # Wildcard pattern for dcf
        isin_key = "Isin"
    elif model_name == "momentum":
        index_name = f"eq_momentum_{year}"
        isin_key = "Isin"
    elif model_name == "indicators":
        index_name = f"eq_technical_indicators_{year}"
        isin_key = "isin"
    elif model_name =="phase" and functionality_type=="model":
        index_name =f"eq_phase_model_{year}"
        isin_key ="id"
    elif model_name =="phase" and functionality_type=="metric":
        index_name =f'eq_phase_model_metrics_{year}'
        isin_key ="id"
    elif model_name =="lstm" and functionality_type =="model":
        index_name =f'eq_lstm_model_{year}'
        isin_key ='isin'
    elif model_name =="lstm" and functionality_type =="metric":
        index_name =f'eq_lstm_model_metrics_{year}'
        isin_key ='isin'
    elif model_name =="macro" and functionality_type =="metric":
        index_name =f'eq_macro_model_metrics_{year}'
        isin_key ='isin'
    elif model_name =="macro" and functionality_type =="model":
        index_name =f'eq_macro_model_{year}'
        isin_key ='isin'
    else:
        # Default pattern for other models
        index_name = f"eq_{model_name}_{'model' if functionality_type == 'model' else 'model_metrics'}_{year}"
        isin_key = "isin"

    print(f"Searching index: {index_name} with isin_key: {isin_key}")

    # Build the query
    must_conditions = [
        {"range": {"date": {"gte": start_date, "lte": end_date}}}
    ]
    
    # Only add schedular filter if it's not None and not "None"
    if schedular and schedular != "None":
        must_conditions.append({"term": {"schedular.keyword": schedular}})

    query = {
        "_source": [isin_key, "date"],
        "size": 10000,
        "query": {
            "bool": {
                "must": must_conditions
            }
        },
        "sort": [{"_id": "asc"}]
    }

    # Execute the query
    try:
        url = f"https://{auth_es.aws_host}/{index_name}/_search"
        response = requests.post(
            url,
            auth=auth_es,
            json=query,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        hits = response.json()['hits']['hits']
        if not hits:
            print(f"No documents found in index: {index_name}")
            print("Query used:", query)
            return None
            
        # Create DataFrame
        data = []
        for hit in hits:
            source = hit.get('_source', {})
            data.append({
                'isin': source.get(isin_key),
                'date': source.get('date')
            })
            
        return pd.DataFrame(data)
        
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            print(f"Index not found: {index_name}")
        else:
            print(f"HTTP Error: {str(e)}")
        return None
    except Exception as e:
        print(f"Error fetching data from ES: {str(e)}")
        return None

def fetch_s3_data(start_date, end_date, bucket_name, filepath, s3, model_name=None, functionality_type=None, deploy_df=None, isin_df=None):
    try:
        # Fetch the object from S3 using the provided filepath
        s3_response_object = s3.get_object(Bucket=bucket_name, Key=filepath)
        object_content = s3_response_object['Body'].read()

        # Determine the file type and read content
        if filepath.endswith(".xlsx"):
            try:
                df = pd.read_excel(io.BytesIO(object_content), engine='openpyxl')
                #print(f"Successfully read Excel (.xlsx) file: {filepath}")
            except Exception as e:
                print(f"Error reading .xlsx file: {e}. Trying .xls format...")
                try:
                    df = pd.read_excel(io.BytesIO(object_content), engine='xlrd')
                    #print(f"Successfully read Excel (.xls) file: {filepath}")
                except Exception as e:
                    print(f"Error reading .xls file: {e}")
                    return None
        elif filepath.endswith(".xls"):
            try:
                df = pd.read_excel(io.BytesIO(object_content), engine='xlrd')
                #print(f"Successfully read Excel (.xls) file: {filepath}")
            except Exception as e:
                print(f"Error reading .xls file: {e}")
                return None
        else:
            try:
                df = pd.read_csv(io.BytesIO(object_content))
                #print(f"Successfully read CSV file: {filepath}")
            except Exception as e:
                print(f"Error reading CSV file: {e}")
                return None

        # Special handling for phase model metrics
        if model_name and model_name.lower() == 'phase' and functionality_type == 'metric':
            return transform_phase_metrics(df, filepath, start_date, isin_df)
        
        # Handle different model types
        if model_name and model_name.lower() == 'macro':
            # Transform data for macro model with country-code mapping
            if deploy_df is None:
                raise ValueError("For macro model, deploy_df (macro_deployment.csv) must be provided")
            df = transform_macro_data(df, filepath, start_date, deploy_df)
        else:
            # Handle column renaming for different models
            if model_name and model_name.lower() in ['dcf', 'momentum']:
                if 'Isin' in df.columns:
                    df.rename(columns={'Isin': 'isin'}, inplace=True)
            elif model_name and model_name.lower() =="macro":
                if 'country_data_type' in df.columns:
                    df.rename(columns={'country_data_type': 'isin'},inplace =True)
            elif model_name and model_name.lower() == 'phase':
                # For phase model, rename model_identifier to isin if it exists
                if 'id' in df.columns:
                    df.rename(columns={'id': 'isin'}, inplace=True)
                elif 'Isin' in df.columns:
                    df.rename(columns={'Isin': 'isin'}, inplace=True)
            elif 'isin' not in df.columns and 'Isin' in df.columns:
                df.rename(columns={'Isin': 'isin'}, inplace=True)
        
        #print(df.head())
        return df

    except Exception as e:
        print(f'Error in reading file from S3: {e}')
        return None
def process_phase_metrics(date, selected_tag, s3, file_path):
    try:
        final_metrics_df = pd.DataFrame(columns=['countrywise', 'isin', 'date'])
        
        # Get the country mapping data
        mapping_bucket = "phase-model"
        mapping_path = "adhoc/country_identifier_mapping_new.csv"
        obj = s3.get_object(Bucket=mapping_bucket, Key=mapping_path)
        country_mapping_df = pd.read_csv(StringIO(obj['Body'].read().decode('utf-8')))
        
        path_parts = file_path.split('/')
        country_part = path_parts[-2].lower()  
        data_type = path_parts[-1].split('.')[0]  
        
        # Skip processing if this is a correlation file (we'll handle it when processing the main file)
        if data_type.endswith('_corr'):
            return final_metrics_df
        
        try:
            country_row = country_mapping_df[
                country_mapping_df['country_name'].str.lower().str.replace('usa', 'us').str.replace(' ', '_') == country_part
            ].iloc[0]
            
            country_code = country_row.get('country_code', country_part.upper())
        except IndexError:
            #print(f"Country {country_part} not found in mapping file, using default code")
            country_code = country_part.upper()
        
        try:
            # Fetch the metrics file from the provided file_path
            bucket = file_path.split('/')[2]
            key = '/'.join(file_path.split('/')[3:])
            s3_response = s3.get_object(Bucket=bucket, Key=key)
            df = pd.read_csv(io.BytesIO(s3_response['Body'].read()))
            
            # Convert date column if present
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df = df[df['date'].dt.date == date.date()]
            
            # Skip if no data for this date
            if df.empty:
                return final_metrics_df
            
            # Add main data type to final DataFrame
            id_column = 'id' if 'id' in df.columns else 'isin'
            if id_column in df.columns:
                temp_df = pd.DataFrame({
                    'countrywise': [f"{country_code}_{data_type}"] * len(df),
                    'isin': df[id_column],
                    'date': df['date'] if 'date' in df.columns else date
                })
                final_metrics_df = pd.concat([final_metrics_df, temp_df])
            
            # Check if correlation data exists for this data type
            corr_data_type = f"{data_type}_corr"
            corr_file_path = file_path.replace(data_type, corr_data_type)
            
            try:
                # Try to fetch the correlation file
                corr_bucket = corr_file_path.split('/')[2]
                corr_key = '/'.join(corr_file_path.split('/')[3:])
                corr_response = s3.get_object(Bucket=corr_bucket, Key=corr_key)
                corr_df = pd.read_csv(io.BytesIO(corr_response['Body'].read()))
                
                # Convert date column if present
                if 'date' in corr_df.columns:
                    corr_df['date'] = pd.to_datetime(corr_df['date'])
                    corr_df = corr_df[corr_df['date'].dt.date == date.date()]
                
                if not corr_df.empty and id_column in corr_df.columns:
                    temp_corr_df = pd.DataFrame({
                        'countrywise': [f"{country_code}_{corr_data_type}"] * len(corr_df),
                        'isin': corr_df[id_column],
                        'date': corr_df['date'] if 'date' in corr_df.columns else date
                    })
                    final_metrics_df = pd.concat([final_metrics_df, temp_corr_df])
                    
            except Exception as corr_e:
                # Correlation file doesn't exist or error reading it - that's okay
                pass
            
            return final_metrics_df.reset_index(drop=True)
            
        except Exception as e:
            #print(f"Error processing file {file_path}: {str(e)}")
            return pd.DataFrame(columns=['countrywise', 'isin', 'date'])
    
    except Exception as e:
        #print(f"Error in process_phase_metrics: {str(e)}")
        return pd.DataFrame(columns=['countrywise', 'isin', 'date'])

def transform_macro_data(df, filepath, date, deploy_df):
    """Transform macro data into the required format with country_code_type mapping."""
    try:
        path_parts = filepath.split('/')
        country_index = -4 
        type_index = -3
        
        if len(path_parts) >= abs(country_index):
            country = path_parts[country_index]
            data_type = path_parts[type_index]
            country = country.replace('_', ' ').strip()
            matched_row = deploy_df[(deploy_df['Country'].str.strip().str.lower() == country.lower()) | 
                                   (deploy_df['Mapped_Country'].str.strip().str.lower() == country.lower())]
            
            if not matched_row.empty:
                country_code = matched_row.iloc[0]['Country_Code']  # Assuming column exists
                country_data_type = f"{country_code}_{data_type}"
            else:
                #print(f"No country code found for {country} in deployment file")
                country_data_type = f"{country}_{data_type}"
            
            # Create the output DataFrame
            result_df = pd.DataFrame({
                'country_data_type': [country_data_type],
                'date': [date]
            })
            
            # Add all other columns from original DataFrame
            for col in df.columns:
                result_df[col] = df[col].values[0] if len(df) > 0 else None
                
            return result_df
        else:
            #print(f"Could not extract country/type from path: {filepath}")
            return None
            
    except Exception as e:
        #print(f"Error transforming macro data: {e}")
        return None


def fetch_s3_vc_data(start_date, end_date, bucket_name_vc, base_filepath, s3,functionality_type,is_macro_model=False):
    try:
        records = []

        # Generate date range
        date_range = pd.date_range(start=start_date, end=end_date).strftime('%Y-%m-%d')
        
        for date in date_range:
            if functionality_type=="model":
                full_prefix = f"{base_filepath}/{date}/predictions/"
                continuation_token = None
            else:
                full_prefix = f"{base_filepath}/{date}/metrics/"
                continuation_token = None

            while True:
                # Fetch objects from S3 using list_objects_v2
                if continuation_token:
                    response = s3.list_objects_v2(
                        Bucket=bucket_name_vc,
                        Prefix=full_prefix,
                        ContinuationToken=continuation_token
                    )
                else:
                    response = s3.list_objects_v2(
                        Bucket=bucket_name_vc,
                        Prefix=full_prefix
                    )

                # Check if "Contents" is in the response, meaning objects are found
                if "Contents" in response:
                    for obj in response["Contents"]:
                        file_key = obj["Key"]
                        
                        # Only process CSV files
                        if file_key.endswith(".csv"):
                            # Extract ISIN from file name
                            isin = file_key.split("/")[-1].replace(".csv", "")
                            
                            # Append data to records list
                            records.append({'date': date, 'isin': isin})

                # If there are more objects, continue fetching
                if response.get("IsTruncated"):
                    continuation_token = response.get("NextContinuationToken")
                else:
                    break

        # Create DataFrame from records
        records_df = pd.DataFrame(records)
        
        # Debugging: print the first few records to confirm 'isin' column exists
        print(records_df.head())
        print(len(records_df))
        
        return records_df

    except Exception as e:
        print(f"Error fetching data from S3: {e}")
        return None

# Final Summary:
def build_final_summary(
    es_df, s3_df, s3_df_vc,
    model_variable_name,
    final_df_model=None,
    final_df_metrics=None,
    functionality_type=None,
    tag=selected_tag):

    # Rename model variable names if applicable
    rename_map = {
        "er_Daily": "lgbm_er_Daily",
        "er_Monthly": "lgbm_er_Monthly",
        "cat_er_Monthly": "catboost_Monthly",
        "indicators_None": "indicators",
        "dcf_None": "dcf",
        "momentum_None": "momentum",
        "macro_None": "macro_monthly"
    }
    model_variable_name = rename_map.get(model_variable_name, model_variable_name)

    # Initialize default values
    tag_es = tag_s3 = tag_s3_vc = "Data is not available"
    tier1_es = tier1_s3 = tier1_s3_vc = "Data is not available"
    usa_isins = isin_df_phase['isin'].astype(str).unique() if isin_df_phase is not None else []
    macro_isins = isin_df_macro['isin'].astype(str).unique() if isin_df_macro is not None else []

    # Always show "-" for phase and macro model Tier-1 columns
    if "phase" in model_variable_name or "macro" in model_variable_name:
        tier1_es = tier1_s3 = tier1_s3_vc = "-"

    try:
        if tag == "aieq":
            tier1_isin = real_tier1_isin['isin'].astype(str).unique()
            tag_isin = real_aieq_isin['isin'].astype(str).unique()
            all_tag_isins = tag_isin
        else:
            tag_file_map = {
                "aigo": real_aigo_isin,
                "indt1": real_indt1_isin
            }
            tag_isin = tag_file_map[tag]['isin'].astype(str).unique()
            all_tag_isins = tag_isin
            tier1_isin = None

        # Process ES
        if es_df is not None and not es_df.empty:
            es_isins = es_df['isin'].astype(str).unique()
            if "phase" in model_variable_name:
                #tag_es = len(set(usa_isins).lower() - set(es_isins).lower())
                tag_es = len(set(map(str.upper, usa_isins)) - set(map(str.upper, es_isins)))
            elif "macro" in model_variable_name:
                #tag_es = len(set(macro_isins) - set(es_isins))
                tag_es =len(set(map(str.upper, macro_isins))- set(map(str.upper, es_isins)))
            else:
                tag_es = len(set(all_tag_isins) - set(es_isins))

            if tag == "aieq" and not ("phase" in model_variable_name or "macro" in model_variable_name):
                tier1_es = len(set(tier1_isin) - set(es_isins))

        # Process S3
        if s3_df is not None and not s3_df.empty:
            s3_isins = s3_df['isin'].astype(str).unique()
            if "phase" in model_variable_name:
                tag_s3 = len(set(map(str.upper, usa_isins)) - set(map(str.upper, s3_isins)))
                #tag_s3 = len(set(usa_isins) - set(s3_isins))
            elif "macro" in model_variable_name:
                tag_s3 = len(set(map(str.upper, macro_isins))- set(map(str.upper, s3_isins)))
                #len(set(macro_isins) - set(s3_isins))
            else:
                tag_s3 = len(set(all_tag_isins) - set(s3_isins))

            if tag == "aieq" and not ("phase" in model_variable_name or "macro" in model_variable_name):
                tier1_s3 = len(set(tier1_isin) - set(s3_isins))
        else:
            if "ttm" in model_variable_name:
                tag_s3 ="-"
                tier1_s3 ="-"
        

        # Process S3 VC
        if s3_df_vc is not None and not s3_df_vc.empty:
            s3_vc_isins = s3_df_vc['isin'].astype(str).unique()
            if "phase" in model_variable_name:
                tag_s3_vc =len(set(map(str.upper, usa_isins)) - set(map(str.upper, s3_vc_isins))) #len(set(usa_isins) - set(s3_vc_isins))
            elif "macro" in model_variable_name:
                tag_s3_vc =len(set(map(str.upper, macro_isins))- set(map(str.upper, s3_vc_isins))) #len(set(macro_isins) - set(s3_vc_isins))
            else:
                tag_s3_vc = len(set(all_tag_isins) - set(s3_vc_isins))

            if tag == "aieq" and not ("phase" in model_variable_name or "macro" in model_variable_name):
                tier1_s3_vc = len(set(tier1_isin) - set(s3_vc_isins))
        elif model_variable_name in ['dcf', 'indicators', 'momentum']:
            tag_s3_vc = "-"
            if tag == "aieq":
                tier1_s3_vc = "-"

    except Exception as e:
        raise ValueError(f"Error reading ISIN dataframes for tag '{tag}': {e}")

    # Format tag label
    tag_display = tag.upper()

    # Build summary row
    summary_data = {
        "Model_Name": model_variable_name,
    }

    # For aieq tag, put Tier-1 columns first
    if tag == "aieq":
        summary_data.update({
            "Tier-1 (ES)": tier1_es,
            "Tier-1 (S3)": tier1_s3,
            "Tier-1 (VC)": tier1_s3_vc,
            f"{tag_display} (ES)": tag_es,
            f"{tag_display} (S3)": tag_s3,
            f"{tag_display} (VC)": tag_s3_vc
        })
    else:
        summary_data.update({
            f"{tag_display} (ES)": tag_es,
            f"{tag_display} (S3)": tag_s3,
            f"{tag_display} (VC)": tag_s3_vc
        })

    summary_row = pd.DataFrame([summary_data])

    # Append to final DataFrames
    if functionality_type == "model":
        final_df_model = pd.concat([final_df_model, summary_row], ignore_index=True) if final_df_model is not None else summary_row
    else:
        final_df_metrics = pd.concat([final_df_metrics, summary_row], ignore_index=True) if final_df_metrics is not None else summary_row

    return final_df_model, final_df_metrics

full_config = {
     "lstm": {
        "aieq": {
            # "Daily": {
            #     "metric": {
            #         "bucket": "historical-prediction-data-15yrs", 
            #         "filepath": "lstm/aieq/metrics/1_Day/2025/metrics_{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": "lstm_model/daily"
            #     },
            #     "model": {
            #         "bucket": "historical-prediction-data-15yrs",
            #         "filepath": "lstm/aieq/predictions/1_Day/2025/predictionresult_{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": "lstm_model/daily"
            #     }
            # },
            "Monthly": {
                "metric": {
                    "bucket": "historical-prediction-data-15yrs", 
                    "filepath": "lstm/aieq/metrics/7_Day/2025/metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "lstm_model/monthly"
                },
                "model": {
                    "bucket": "historical-prediction-data-15yrs",
                    "filepath": "lstm/aieq/predictions/7_Day/2025/predictionresult_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "lstm_model/monthly"
                }
            }
        },
        "aigo": {
            "Monthly": {
                "metric": {
                    "bucket": "historical-prediction-data-15yrs", 
                    "filepath": "lstm/aigo/metrics/7_Day/2025/metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "lstm_model/monthly"
                },
                "model": {
                    "bucket": "historical-prediction-data-15yrs",
                    "filepath": "lstm/aigo/7_Day/2025/predictionresult_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "lstm_model/monthly"
                }
            }
        #     "Daily": {
        #          "metric": {
        #             "bucket": "historical-prediction-data-15yrs", 
        #             "filepath": "lstm/aigo/metrics/1_Day/2025/metrics_{date}.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "lstm_model/monthly"
        #         },
        #         "model": {
        #             "bucket": "historical-prediction-data-15yrs",
        #             "filepath": "lstm/aigo/1_Day/2025/predictionresult_{date}.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "lstm_model/daily"
        #         }
        #     }
        },
        "indt1": {
            "Monthly": {
                "metric": {
                    "bucket": "historical-prediction-data-15yrs", 
                    "filepath": "lstm/niftyind/metrics/7_Day/2025/metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "lstm_model/monthly"
                },
                "model": {
                    "bucket": "historical-prediction-data-15yrs",
                    "filepath": "lstm/niftyind/predictions/7_Day/2025/predictionresult_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "lstm_model/monthly"
                }
            }
            # "Daily": {
            #     "metric": {
            #         "bucket": "historical-prediction-data-15yrs", 
            #         "filepath": "lstm/niftyind/metrics/1_Day/2025/metrics_{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": "lstm_model/monthly"
            #     },
            #     "model": {
            #         "bucket": "historical-prediction-data-15yrs",
            #         "filepath": "lstm/niftyind/predictions/1_Day/2025/predictionresult_{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": "lstm_model/monthly"
            #     }
            # }
        }
    },
    "financial": {
        "aieq": {
            # "Daily": {
            #     "metric": {
            #         "bucket": "financial-model-data-collection",
            #         "filepath": "aieq/daily_metrics/daily_prediction_metrics_{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": 'financial_model/daily'
            #     },
            #     "model": {
            #         "bucket": "financial-model-data-collection",
            #         "filepath": "aieq/daily_predictions/daily_prediction_{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": 'financial_model/daily'
            #     }
            # },
            "Monthly": {
                "metric": {
                    "bucket": "financial-model-data-collection",
                    "filepath": "aieq/monthly_metrics/monthly_prediction_metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'financial_model/monthly'
                },
                "model": {
                    "bucket": "financial-model-data-collection",
                    "filepath": "aieq/monthly_predictions/monthly_prediction_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'financial_model/monthly'
                }
            }
        },
        "aigo": {
            "Monthly": {
                "metric": {
                    "bucket": "financial-model",
                    "filepath": "all_data/aigo/monthly/daily_run/metrics/monthly_prediction_metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'financial_model/monthly'
                },
                "model": {
                    "bucket": "financial-model",
                    "filepath": "aigo/daily_predictions/prediction_data/monthly_prediction_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'financial_model/monthly'
                }
            }
        },
        "indt1": {
            "Monthly": {
                "metric": {
                    "bucket": "financial-model",
                    "filepath": "all_data/india/monthly/daily_run/metrics/monthly_prediction_metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'financial_model/monthly'
                },
                "model": {
                    "bucket": "financial-model",
                    "filepath": "india/daily_predictions/prediction_data/monthly_prediction_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'financial_model/monthly'
                }
            }
        }
    },
    "management": {
        "aieq": {
            "Monthly": {
                "metric": {
                    "bucket": "management-model-retraining-bucket",
                    "filepath": "Feb_2024_MGMT_Model/metrics/AIEQ/monthly/monthly_prediction_metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'management_model/monthly'
                },
                "model": {
                    "bucket": "management-model-retraining-bucket",
                    "filepath": "Feb_2024_MGMT_Model/AIEQ_daily_run_monthly_predictions/monthly_prediction_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'management_model/monthly'
                }
            }
        },
        "aigo": {
            "Monthly": {
                "metric": {
                    "bucket": "management-model-retraining-bucket",
                    "filepath": "Feb_2024_MGMT_Model/metrics/AIGO/monthly/monthly_prediction_metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'management_model/monthly'
                },
                "model": {
                    "bucket": "management-model-retraining-bucket",
                    "filepath": "Feb_2024_MGMT_Model/AIGO_daily_run_monthly_predictions/monthly_prediction_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'management_model/monthly'
                }
            }
        },
        "indt1": {
            "Monthly": {
                "metric": {
                    "bucket": "management-model-retraining-bucket",
                    "filepath": "Feb_2024_MGMT_Model/metrics/INDT1/monthly/monthly_prediction_metrics_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'management_model/monthly'
                },
                "model": {
                    "bucket": "management-model-retraining-bucket",
                    "filepath": "Feb_2024_MGMT_Model/INDT1_daily_run_monthly_predictions/monthly_prediction_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'management_model/monthly'
                }
            }
        }
    },
    "er": {
        "aieq": {
            # "Daily": {
            #     "metric": {
            #         "bucket": "portfolio-experiments-test",
            #         "filepath": "aieq_training/Daily/daily_run_metrics/{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": 'er_model_lgb/daily'
            #     },
            #     "model": {
            #         "bucket": "portfolio-experiments-test",
            #         "filepath": "aieq_training/Daily/daily_run/{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": 'er_model_lgb/daily'
            #     }
            # },
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aieq_training/Monthly/daily_run_metrics/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_lgb/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aieq_training/Monthly/daily_run/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_lgb/monthly'
                }
            }
        },
        "aigo": {
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aigo_training/Monthly/daily_run_metrics/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_lgb/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aigo_training/Monthly/daily_run/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_lgb/monthly'
                }
            }
        },
        "indt1": {
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "india_training/Monthly/daily_run_metrics/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_lgb/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "india_training/Monthly/daily_run/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_lgb/monthly'
                }
            }
        }
    },
    "cat_er": {
        "aieq": {
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aieq_training_catboost/Monthly/daily_run_metrics/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_cat/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aieq_training_catboost/Monthly/daily_run/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_cat/monthly'
                }
            }
        },
        # "aigo": {
        #     "Monthly": {
        #         "metric": {
        #             "bucket": "portfolio-experiments-test",
        #             "filepath": "aigo_training_catboost/Monthly/daily_run_metrics/{date}.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": 'er_model_cat/monthly'
        #         },
        #         "model": {
        #             "bucket": "portfolio-experiments-test",
        #             "filepath": "aigo_training_catboost/Monthly/daily_run/{date}.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": 'er_model_cat/monthly'
        #         }
        #     }
        # },
        "indt1": {
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "india_training_catboost/Monthly/daily_run_metrics/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_cat/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "india_training_catboost/Monthly/daily_run/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'er_model_cat/monthly'
                }
            }
        }
    },
    "information": {
        "aieq": {
            # "Daily": {
            #     "metric": [
            #         {
            #             "bucket": "micro-ops-output",
            #             "filepath": "new_info_model/daily_models/tier1_isinwise/v1/daily_run/daily_model_metrics/data_{date}.csv"
            #         },
            #         {
            #             "bucket": "micro-ops-output",
            #             "filepath": "new_info_model/daily_models/tier1_isinwise/v1/daily_run/daily_model_metrics/data_{date}.csv"
            #         },
            #         {
            #             "base_filepath": 'eq_information_model/daily',
            #             "bucket_name_vc": "eq-model-output"
            #         }
            #     ],
            #     "model": [
            #         {
            #             "bucket": "micro-ops-output",
            #             "filepath": "new_info_model/daily_models/tier1_isinwise/v1/daily_run/daily_model_final_predictions/data_{date}.csv"
            #         },
            #         {
            #             "bucket": "micro-ops-output",
            #             "filepath": "new_info_model/daily_models/aieq_sectorwise/v1/daily_run/daily_model_final_predictions/data_{date}.csv"
            #         },
            #         {
            #             "base_filepath": 'eq_information_model/daily',
            #             "bucket_name_vc": "eq-model-output"
            #         }
            #     ]
            # },
            "Monthly": {
                "metric": [
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/new_info_model_evidence_mention/tier1/daily_run/monthly_model_metrics/data_{date}.csv"
                    },
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/new_info_model_evidence_mention/tier1/daily_run/monthly_model_metrics/data_{date}.csv"
                    },
                    {
                        "bucket_name_vc": "eq-model-output",
                        "base_filepath": 'eq_information_model/monthly'
                    }
                ],
                "model": [
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/new_info_model_evidence_mention/tier1/daily_run/monthly_model_final_predictions/data_{date}.csv"
                    },
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/aieq_sectorwise/v1/daily_run/monthly_model_final_predictions/data_{date}.csv"
                    },
                    {
                        "bucket_name_vc": "eq-model-output",
                        "base_filepath": 'eq_information_model/monthly'
                    }
                ]
            }
        },
        "aigo": {
            "Monthly": {
                "metric": [
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/aigo_sectorwise/daily_run/monthly_model_metrics/data_{date}.csv"
                    },
                    {
                        "bucket_name_vc": "eq-model-output",
                        "base_filepath": 'eq_information_model/monthly'
                    }
                ],
                "model": [
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/aigo_sectorwise/daily_run/monthly_model_final_predictions/data_{date}.csv"
                    },
                    {
                        "bucket_name_vc": "eq-model-output",
                        "base_filepath": 'eq_information_model/monthly'
                    }
                ]
            }
        },
        "indt1": {
            "Monthly": {
                "metric": [
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/indt1/daily_run/monthly_model_metrics/data_{date}.csv"
                    },
                    {
                        "bucket_name_vc": "eq-model-output",
                        "base_filepath": 'eq_information_model/monthly'
                    }
                ],
                "model": [
                    {
                        "bucket": "micro-ops-output",
                        "filepath": "new_info_model/indt1/daily_run/monthly_model_final_predictions/data_{date}.csv"
                    },
                    {
                        "bucket_name_vc": "eq-model-output",
                        "base_filepath": 'eq_information_model/monthly'
                    }
                ]
            }
        }
    },
    "xgb_er": {
        "aieq": {
            "Quarterly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aieq_training_xgboost/Quarterly/daily_run_metrics/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "er_model_xgb/quarterly"
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "aieq_training_xgboost/Quarterly/daily_run/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "er_model_xgb/quarterly"
                }
            }
        },
        # "aigo": {
        #     "Quarterly": {
        #         "metric": {
        #             "bucket": "portfolio-experiments-test",
        #             "filepath": "aieq_training_xgboost/Quarterly/aigo/daily_run_metrics/{date}.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "er_model_xgb/quarterly"
        #         },
        #         "model": {
        #             "bucket": "portfolio-experiments-test",
        #             "filepath": "aieq_training_xgboost/Quarterly/aigo/daily_run/{date}.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "er_model_xgb/quarterly"
        #         }
        #     }
        # },
        "indt1": {
            "Quarterly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "india_training_xgboost/Quarterly/daily_run_metrics/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "er_model_xgb/quarterly"
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "india_training_xgboost/Quarterly/daily_run/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "er_model_xgb/quarterly"
                }
            }
        }
    },
    "ttm": {
        "aieq": {
            # "Daily": {
            #     "metric": {
            #         "bucket": "portfolio-experiments-test",
            #         "filepath": "consolidated_er_best_model/v1/daily_run/ttm/metrics/all-data/aieq/Daily/{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": 'ttm_model/daily'
            #     },
            #     "model": {
            #         "bucket": "portfolio-experiments-test",
            #         "filepath": "consolidated_er_best_model/v1/daily_run/ttm/predictions/all-data/aieq/Daily/{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": 'ttm_model/daily'
            #     }
            # },
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/metrics/all-data/aieq/Monthly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/predictions/all-data/aieq/Monthly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/monthly'
                }
            },
            "Quarterly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/metrics/all-data/aieq/Quarterly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/quarterly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/predictions/all-data/aieq/Quarterly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/quarterly'
                }
            }
        },
        "indt1": {
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/metrics/all-data/indeq/Monthly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/predictions/all-data/indeq/Monthly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/monthly'
                }
            },
            "Quarterly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/metrics/all-data/indeq/Quarterly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/quarterly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/predictions/all-data/indeq/Quarterly/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/quarterly'
                }
            }
        },
        "aigo": {
            "Monthly": {
                "metric": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/metrics/all-data/indeq/Monthly/{date}.csv",#### not there
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/monthly'
                },
                "model": {
                    "bucket": "portfolio-experiments-test",
                    "filepath": "consolidated_er_best_model/v1/daily_run/ttm/predictions/all-data/indeq/Monthly/{date}.csv", # not there
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": 'ttm_model/monthly'
                }
            }
        }
    },
    "best": {
        "aieq": {
            # "Daily": {
            #     "metric": {
            #         "bucket": "micro-ops-output",
            #         "filepath": "er_comparison_daily/daily_metrics/{date}_best_model_metrics.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": "best_er_model/daily"
            #     },
            #     "model": {
            #         "bucket": "micro-ops-output",
            #         "filepath": "er_comparison_daily/daily_data/{date}_best_model.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": "best_er_model/daily"
            #     }
            # },
            "Monthly": {
                "metric": {
                    "bucket": "micro-ops-output",
                    "filepath": "er_comparison/daily_metrics/{date}_best_model_metrics.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "best_er_model/monthly"
                },
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "er_comparison/daily_data/{date}_best_model.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "best_er_model/monthly"
                }
            },
            "Quarterly": {
                "metric": {
                    "bucket": "micro-ops-output",
                    "filepath": "er_comparison_quarterly/daily_metrics/{date}_best_model_metrics.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "best_er_model/quarterly"
                },
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "er_comparison_quarterly/daily_data/{date}_best_model.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "best_er_model/quarterly"
                }
            }
        }#,
        # "aigo": {
        #     "Monthly": {
        #         "metric": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison_aigo/daily_metrics/{date}_best_model_metrics.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/monthly"
        #         },
        #         "model": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison_aigo/daily_data/{date}_best_model.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/monthly"
        #         }
        #     },
        #     "Quarterly": {
        #         "metric": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison_quarterly/aigo/daily_metrics/{date}_best_model_metrics.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/quarterly"
        #         },
        #         "model": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison_quarterly/aigo/daily_data/{date}_best_model.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/quarterly"
        #         }
        #     }
        # }
        # "indt1": {
        #     "Monthly": {
        #         "metric": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison/indt1/daily_metrics/{date}_best_model_metrics.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/monthly"
        #         },
        #         "model": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison/indt1/daily_data/{date}_best_model.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/monthly"
        #         }
        #     },
        #     "Quarterly": {
        #         "metric": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison_quarterly/indt1/daily_metrics/{date}_best_model_metrics.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/quarterly"
        #         },
        #         "model": {
        #             "bucket": "micro-ops-output",
        #             "filepath": "er_comparison_quarterly/indt1/daily_data/{date}_best_model.csv",
        #             "bucket_name_vc": "eq-model-output",
        #             "base_filepath": "best_er_model/quarterly"
        #         }
        #     }
        # }
    },
    "indicators": {
        "aieq": {
            "None": {
                "model": {
                    "bucket": "portfolio-model",
                    "filepath": "daily_technical_indicators/technical_indicators_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "indicators_model"
                }
            }
        },
        "aigo": {
            "None": {
                "model": {
                    "bucket": "portfolio-model",
                    "filepath": "daily_technical_indicators/technical_indicators_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "indicators_model"
                }
            }
        },
        "indt1": {
            "None": {
                "model": {
                    "bucket": "portfolio-model",
                    "filepath": "daily_technical_indicators/technical_indicators_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "indicators_model"
                }
            }
        }        
    },
    "dcf": {
        "aieq": {
            "None": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "eq_dcf/aieq/results_{date}.xlsx",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "dcf_model/monthly"
                }
            }
        },
        "aigo": {
            "None": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "eq_dcf/aigo/results_{date}.xlsx",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "dcf_model/monthly"
                }
            }
        },
        "indt1": {
            "None": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "eq_dcf/indeq/results_{date}.xlsx",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "dcf_model/monthly"
                }
            }
        }
    },
    "momentum": {
        "aieq": {
            "None": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "momentum_close_price/aieq/momentum_{date}.xlsx",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "momentum_model/monthly"
                }
            }
        },
        "aigo": {
            "None": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "momentum_close_price/aigo/momentum_{date}.xlsx",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "momentum_model/monthly"
                }
            }
        },
        "indt1": {
            "None": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "momentum_close_price/indeq/momentum_{date}.xlsx",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "momentum_model/monthly"
                }
            }
        }
    },
    "phase": {
        "aieq": {
            "Monthly": {
                "model": {
                    "bucket": "phase-model",
                    "filepath": "daily_runs/daily_run_monthly/phase_predictions_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "phase_model/monthly"
                },
                "metric": {
                     "bucket": "financial-model-data-collection",
                     "filepath": "phase_data/historical_metrics/aieq/monthly/{country}/{data_type}.csv",
                     "bucket_name_vc": "eq-model-output",
                    "base_filepath": "phase_model/monthly"
                  }
            }
            # "Daily": {
            #     "model": {
            #         "bucket": "phase-model",
            #         "filepath": "daily_runs/daily_run_daily/phase_predictions_{date}.csv",
            #         "bucket_name_vc": "eq-model-output",
            #         "base_filepath": "phase_model/monthly"
            #     },
            #     "metric": {
            #          "bucket": "financial-model-data-collection",
            #          "filepath": "phase_data/historical_metrics/aieq/daily/{country}/{data_type}.csv",
            #          "bucket_name_vc": "eq-model-output",
            #          "base_filepath": "phase_model/monthly"
            #       }
            #  }
            
        },
        "aigo": {
            "Monthly": {
                "model": {
                    "bucket": "phase-model",
                    "filepath": "daily_runs/daily_run_monthly/phase_predictions_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "phase_model/monthly"
                },
                "metric": {
                     "bucket": "financial-model-data-collection",
                     "filepath": "phase_data/historical_metrics/aigo/monthly/{country}/{data_type}.csv",
                     "bucket_name_vc": "eq-model-output",
                    "base_filepath": "phase_model/monthly"
                 }
            }
        },
        "indt1": {
            "Monthly": {
                "model": {
                    "bucket": "phase-model",
                    "filepath": "daily_runs/daily_run_monthly/phase_predictions_{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "phase_model/monthly"
                },
                "metric": {
                     "bucket": "financial-model-data-collection",
                     "filepath": "phase_data/historical_metrics/aigo/monthly/{country}/{data_type}.csv",
                     "bucket_name_vc": "eq-model-output",
                     "base_filepath": "phase_model/monthly"
                 }
            }
            
        }
    },
    "macro": {
        "aieq": {
            "monthly": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "macro-historical-data/final-predictions/daily/{country}/{data_type}/daily/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "macro_model/monthly"
                },
                "metric": {
                    "bucket": "micro-ops-output",
                    "filepath": "macro-historical-data/final-metrics/daily/{country}/{data_type}/daily/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "macro_model/monthly"
                }
            }
        },
        "aigo": {
            "monthly": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "macro-historical-data/final-predictions/daily/{country}/{data_type}/daily/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "macro_model/monthly"
                },
                "metric": {
                    "bucket": "micro-ops-output",
                    "filepath": "macro-historical-data/final-metrics/daily/{country}/{data_type}/daily/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "macro_model/monthly"
                }
            }
            
        },
        "indt1": {
            "monthly": {
                "model": {
                    "bucket": "micro-ops-output",
                    "filepath": "macro-historical-data/final-predictions/daily/{country}/{data_type}/daily/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "macro_model/monthly"
                },
                "metric": {
                    "bucket": "micro-ops-output",
                    "filepath": "macro-historical-data/final-metrics/daily/{country}/{data_type}/daily/{date}.csv",
                    "bucket_name_vc": "eq-model-output",
                    "base_filepath": "macro_model/monthly"
                }
            }
        }
    }
}
#comment

final_df_model = pd.DataFrame()
final_df_metrics = pd.DataFrame()

if isinstance(date, str):
    date = datetime.strptime(date, '%Y-%m-%d')

formatted_date = date.strftime('%Y_%m_%d')

# Strategy to word2 mapping
strategy_map = {
    'iwf': 'G',
    'iwf_corr': 'G_B',
    'mtum': 'M',
    'mtum_corr': 'M_B',
    'sphq': 'Q',
    'sphq_corr': 'Q_B',
    'vlue': 'V',
    'vlue_corr': 'V_B',
}


for model_category, tags in full_config.items():
    if selected_tag not in tags:
        continue
    tag_config = tags[selected_tag]

    for schedular, functionality_types in tag_config.items():
        print(f"\nProcessing {model_category} with {schedular} schedular for tag {selected_tag}...")

        if model_category in ["dcf", "momentum"]:
            auth_es_used = auth_es_tr
        elif model_category in ['lstm']:
            auth_es_used = auth_es
        else:
            auth_es_used = auth_es

        for functionality_type, config in functionality_types.items():
            if functionality_type not in ["model", "metric"]:
                continue
                
            print(f"\nProcessing {functionality_type} data...")

            if model_category in ["dcf", "momentum", "lstm"]:
                if model_category == "lstm" and functionality_type == "metric":
                    date_format = date.strftime('%Y-%m-%d')
                else:
                    date_format = formatted_date
            else:
                if model_category == "macro" and functionality_type == "metric":
                    schedular ="None"
                    date_format = f"{date.strftime('%Y-%m-%d')}T00:00:00.000000000"
                else:
                    date_format = date.strftime('%Y-%m-%d')

            # Fetch ES data
            data_es = fetch_data_es(
                model_name=model_category,
                start_date=date,
                end_date=date,
                schedular=schedular,
                auth_es=auth_es_used,
                functionality_type=functionality_type
            )
            es_count = len(data_es) if data_es is not None else 0
            print(f"Retrieved {es_count} {functionality_type} records from ES")

    
            s3_df_vc = None
            s3_df = None

            if isinstance(config, dict):
                # Handle VC data if present
                if "bucket_name_vc" in config:
                    if model_category == "macro":
                        s3_df_vc = fetch_s3_vc_data(
                            start_date=date,
                            end_date=date,
                            bucket_name_vc=config["bucket_name_vc"],
                            base_filepath=config["base_filepath"],
                            s3=s3,
                            functionality_type=functionality_type,
                            is_macro_model=True
                        )
                    else:
                        s3_df_vc = fetch_s3_vc_data(
                            start_date=date,
                            end_date=date,
                            bucket_name_vc=config["bucket_name_vc"],
                            base_filepath=config["base_filepath"],
                            s3=s3,
                            functionality_type=functionality_type
                        )
                    print(f"VC data: {len(s3_df_vc) if s3_df_vc is not None else 0} records")

               #phase_model
                if model_category == "phase" and functionality_type == "metric":
                    try:
                        bucket_name = "phase-model"
                        file_path = "adhoc/country_identifier_mapping_new.csv"
                        obj = s3.get_object(Bucket=bucket_name, Key=file_path)
                        country_mapping_df = pd.read_csv(StringIO(obj['Body'].read().decode('utf-8')))
                        
                        combined_s3_df = pd.DataFrame()
                        
                        for _, country_row in country_mapping_df.iterrows():
                            country_name = country_row['country_name']
                            identifiers = str(country_row['identifiers']).lower()
                            
                            normalized_country = country_name.lower().replace("usa", "us").replace(" ", "_")
                            
                            data_types = ['iwf', 'mtum', 'sphq', 'vlue']
                            found_data_types = []
                            
                            for dt in data_types:
                                if dt in identifiers:
                                    found_data_types.append(dt)
                                    if f"{dt}_corr" in identifiers:
                                        found_data_types.append(f"{dt}_corr")
                            
                           
                            for data_type in found_data_types:
                                try:
                                    
                                    full_filepath = f"s3://{config['bucket']}/{config['filepath'].format(
                                        country=normalized_country,
                                        data_type=data_type,
                                        date=date_format
                                    )}"
                                    
                                    #print(f"Processing phase metrics from {full_filepath}")
                                    
                                    
                                    part_df = process_phase_metrics(
                                        date=date,
                                        selected_tag=selected_tag,
                                        s3=s3,
                                        file_path=full_filepath
                                    )
                                    if not part_df.empty:  
                                        part_df['strategy'] = part_df['countrywise'].apply(
                                            lambda x: strategy_map.get(x.split('_')[-1], '')
                                        )
                                        combined_s3_df = pd.concat([combined_s3_df, part_df])
                                        
                                except Exception as e:
                                    #print(f"Error processing {normalized_country}/{data_type}: {str(e)}")
                                    continue
                        
                        s3_df = combined_s3_df if not combined_s3_df.empty else None
                    
                    except Exception as e:
                        #print(f"Error processing phase metrics: {str(e)}")
                        s3_df = None
                
                # Special handling for macro model
                elif model_category == "macro":
                    try:
                        # Read deployment file for country mapping
                        deploy_df = pd.read_csv("macro_deployment.csv")
                        
                        if not all(col in deploy_df.columns for col in ["Country", "Type"]):
                            raise ValueError("macro_deployment.csv missing required columns (Country, Type)")
                        
                        deploy_df["Mapped_Country"] = deploy_df.apply(
                            lambda row: "Usa" if row["Type"] == "aieq" 
                                        else "India" if row["Type"] == "indt1" 
                                        else row["Country"],
                            axis=1
                        )
                        
                        combined_s3_df = pd.DataFrame()
                        for _, row in deploy_df.iterrows():
                            country = row["Mapped_Country"]
                            data_type = row["Type"]
                            
                            try:
                                filepath = config["filepath"].format(
                                    country=country,
                                    data_type=data_type,
                                    date=date_format
                                )
                                
                                #print(f"Fetching macro data for {country}/{data_type} from {filepath}")
                                
                                part_df = fetch_s3_data(
                                    start_date=date,
                                    end_date=date,
                                    bucket_name=config["bucket"],
                                    filepath=filepath,
                                    s3=s3,
                                    model_name=model_category,
                                    functionality_type=functionality_type,
                                    deploy_df=deploy_df
                                )
                                
                                if part_df is not None:
                                    part_df['macro_country'] = country
                                    part_df['macro_type'] = data_type
                                    combined_s3_df = pd.concat([combined_s3_df, part_df])
                            except Exception as e:
                                #print(f"Error processing {country}/{data_type}: {str(e)}")
                                continue
                        
                        s3_df = combined_s3_df if not combined_s3_df.empty else None
                        
                    except Exception as e:
                        #print(f"Error processing macro model: {str(e)}")
                        s3_df = None
                
                # Normal S3 fetch for other models
                else:
                    s3_df = fetch_s3_data(
                        start_date=date,
                        end_date=date,
                        bucket_name=config["bucket"],
                        filepath=config["filepath"].format(date=date_format),
                        s3=s3,
                        model_name=model_category,
                        functionality_type=functionality_type
                    )
                print(f"S3 data: {len(s3_df) if s3_df is not None else 0} records")

            elif isinstance(config, list):
                # Handle list configurations (information model)
                combined_s3_df = pd.DataFrame()
                for s3_conf in config:
                    if "bucket" in s3_conf:
                        part_df = fetch_s3_data(
                            start_date=date,
                            end_date=date,
                            bucket_name=s3_conf["bucket"],
                            filepath=s3_conf["filepath"].format(date=date_format),
                            s3=s3,
                            model_name=model_category,
                            functionality_type=functionality_type
                        )
                        if part_df is not None:
                            combined_s3_df = pd.concat([combined_s3_df, part_df])
                    elif "bucket_name_vc" in s3_conf:
                        s3_df_vc = fetch_s3_vc_data(
                            start_date=date,
                            end_date=date,
                            bucket_name_vc=s3_conf["bucket_name_vc"],
                            base_filepath=s3_conf["base_filepath"],
                            s3=s3,
                            functionality_type=functionality_type
                        )
                s3_df = combined_s3_df if not combined_s3_df.empty else None

            # Compare data sources
            print(f"\nData Comparison for {model_category}_{schedular}_{functionality_type}:")

            if data_es is not None:
                print(f"ES records: {len(data_es)}\n{data_es.head()}")
            else:
                print("ES records: NULL")

            if s3_df is not None:
                print(f"S3 records: {len(s3_df)}\n{s3_df.head()}")
            else:
                print("S3 records: NULL")

            if s3_df_vc is not None:
                print(f"VC records: {len(s3_df_vc)}\n{s3_df_vc.head()}")
            else:
                print("VC records: NULL")

            # Build summary
            if functionality_type == "model":
                final_df_model, _ = build_final_summary(
                    es_df=data_es,
                    s3_df=s3_df,
                    s3_df_vc=s3_df_vc,
                    model_variable_name=f"{model_category}_{schedular}",
                    final_df_model=final_df_model,
                    final_df_metrics=None,
                    functionality_type=functionality_type,
                    tag=selected_tag
                )
            else:
                _, final_df_metrics = build_final_summary(
                    es_df=data_es,
                    s3_df=s3_df,
                    s3_df_vc=s3_df_vc,
                    model_variable_name=f"{model_category}_{schedular}",
                    final_df_model=None,
                    final_df_metrics=final_df_metrics,
                    functionality_type=functionality_type,
                    tag=selected_tag
                )

print("\nFinal Model Summary:")
print(final_df_model.head(20))

print("\nFinal Metrics Summary:")
print(final_df_metrics.head(20))

tag = selected_tag

sender_email = "<EMAIL>"
#receiver_email = ["<EMAIL>","<EMAIL>"]

# all the mails
receiver_email=["<EMAIL>", "<EMAIL>", '<EMAIL>','<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']

subject = f"Daily Model Monitoring Summary Report - {tag.upper()}"
 

# Compose the email
msg = MIMEMultipart("alternative")
msg["Subject"] = subject
msg["From"] = sender_email
msg["To"] = ", ".join(receiver_email)

# Format the date
formatted_date = date.strftime("%B %d, %Y")

# Create HTML tables
html_table_model = final_df_model.to_html(index=False, classes="dataframe", header=True, border=1)
html_table_metrics = final_df_metrics.to_html(index=False, classes="dataframe", header=True, border=1)

# Dynamic ISIN count section
if tag == "aieq":
    isin_section = f"""
    <p><span class="bold">Count of Tier1 ISIN in MasterActive Firm</span> = {count_tier1_isin}</p>
    <p><span class="bold">Count of AIEQ ISIN in MasterActive Firm</span> = {count_aieq_isin}</p>
    """
elif tag =="indt1":
    isin_section = f"""
    <p><span class="bold">Count of {tag.upper()} ISIN in MasterActive Firm</span> = {count_indt1_isin}</p>
    """
else:
    isin_section = f"""
    <p><span class="bold">Count of {tag.upper()} ISIN in MasterActive Firm</span> = {count_aigo_isin}</p>
    """

# Full HTML email content
html_content = f"""
<html>
<head>
  <style>
    table {{
        border-collapse: collapse;
        width: 100%;
        font-family: Arial, sans-serif;
    }}
    th, td {{
        border: 1px solid #dddddd;
        text-align: center;
        padding: 8px;
    }}
    th {{
        background-color: #4CAF50;
        color: white;
    }}
    .bold {{
        font-weight: bold;
    }}
    h3 {{
        font-weight: normal;
    }}
  </style>
</head>
<body>
  <p>Hi Team,</p>
  <p>Below is the table for the count of <b>missing ISINs</b> for tag <b>{tag.upper()}</b> in S3, ES, and Version Control (VC):</p>
  {isin_section}

  <h3>Model Summary Table for {formatted_date}</h3>
  <p><b>Predictions</b></p>
  {html_table_model}

  <h3>Metrics Summary Table for {formatted_date}</h3>
  <p><b>Metrics</b></p>
  {html_table_metrics}

  <p>Regards,</p>
  <p>Garima Yadav</p>
</body>
</html>
"""

msg.attach(MIMEText(html_content, "html"))

# Send the email
with smtplib.SMTP(smtp_server, smtp_port) as server:
    server.starttls()
    server.login(username, password)
    server.sendmail(sender_email, receiver_email, msg.as_string())

print("Email sent successfully.")
