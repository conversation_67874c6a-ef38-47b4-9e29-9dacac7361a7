import pandas as pd
import numpy as np
import requests
import json
from requests_aws4auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime
import os
import argparse

aws_access_key = '********************'
aws_secret_key = 'xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV'
region = 'us-east-1'
service = 'es'  
auth = AWS4Auth(aws_access_key, aws_secret_key, region, service)


sender_email = "<EMAIL>"
receiver_email=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" ,"<EMAIL>"]

username = "<EMAIL>"
password = "vies rtlt mxwb qyap" 



parser = argparse.ArgumentParser(prog='Model', description="Script for Daily Observations.")

parser.add_argument('-m', '--model')

args = parser.parse_args()

model = args.model


yesterday = datetime.today() - timedelta(days=1)
date = yesterday.strftime('%Y-%m-%d')
year = yesterday.year
schedular = "Monthly"
host = 'search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com'


# Fetch AIEQ ISINs
aieq_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=aieq').json()
real_aieq_isins = pd.DataFrame(aieq_json["data"]["masteractivefirms_aieq"])[['isin']].dropna()



model_columns = {
    "best": {"predictions": "predicted_er"},
    "ttm": {"predictions": "actual_monthly_return_predictions"},
    "cat_er": {"predictions": "actual_monthly_return_predictions"}
}

metrics_columns = {
    "best": {
        "avg_confidence": "mean_conf",
        "directionality": "mean_dir",
        "rmse": "rmse"
    },
    "ttm": {
        "avg_confidence": "avg_confidence_score",
        "directionality": "mean_directionality",
        "rmse": "root_mean_squared_error"
    },
    "cat_er": {
        "avg_confidence": "avg_confidence_score",
        "directionality": "mean_directionality",
        "rmse": "root_mean_squared_error"
    }
}

def fetch_es_data(index_type):
    index_name = f"eq_{model}_model_{year}" if index_type == "model" else f"eq_{model}_model_metrics_{year}"
    columns = model_columns[model] if index_type == "model" else metrics_columns[model]
    
    
    url = f'https://{host}/{index_name}/_search?scroll=1m'  
    query = {
        "_source": list(columns.values()) + ["isin", "date"],
        "size": 10000, 
        "query": {
            "bool": {
                "must": [
                    {"match": {"schedular.keyword": schedular}},
                    {"term": {"date": date}}
                ]
            }
        },
        "sort": ["_doc"]  
    }
    
    try:
        response = requests.post(url, auth=auth, json=query, headers={"Content-Type": "application/json"}, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        hits = data.get('hits', {}).get('hits', [])
        if not hits:
            print(f"No data found for {index_name} on {date}")
            return pd.DataFrame()
        
        scroll_id = data.get('_scroll_id')
        records = []
        

        for hit in hits:
            source = hit.get('_source', {})
            record = {"isin": source.get("isin"), "date": source.get("date")}
            for local_field, es_field in columns.items():
                record[local_field] = source.get(es_field)
            records.append(record)
        
        
        while True:
            scroll_url = f'https://{host}/_search/scroll'
            scroll_query = {
                "scroll": "1m",
                "scroll_id": scroll_id
            }
            
            scroll_response = requests.post(scroll_url, auth=auth, json=scroll_query, 
                                          headers={"Content-Type": "application/json"}, timeout=30)
            scroll_response.raise_for_status()
            scroll_data = scroll_response.json()
            
            new_hits = scroll_data.get('hits', {}).get('hits', [])
            if not new_hits:
                break  
                
           
            for hit in new_hits:
                source = hit.get('_source', {})
                record = {"isin": source.get("isin"), "date": source.get("date")}
                for local_field, es_field in columns.items():
                    record[local_field] = source.get(es_field)
                records.append(record)
            
            
            scroll_id = scroll_data.get('_scroll_id')
        
        
        if scroll_id:
            clear_url = f'https://{host}/_search/scroll'
            clear_body = {"scroll_id": scroll_id}
            requests.delete(clear_url, auth=auth, json=clear_body, headers={"Content-Type": "application/json"})
        
        return pd.DataFrame(records)
    
    except Exception as e:
        print(f"Error fetching {index_type} data: {str(e)}")
        return pd.DataFrame()

def analyze_data(df, index_type):
    if df.empty:
        print(f"No {index_type} data to analyze")
        return {}
    
    df['isin'] = df['isin'].astype(str).str.strip().str.upper()
    aieq_isins = real_aieq_isins['isin'].astype(str).str.strip().str.upper().unique()
    
    stats = {
        "aieq_hits": len(set(df['isin']).intersection(set(aieq_isins))),
        "null_fields": {}
    }
    
    for col in df.columns:
        if col not in ["isin", "date"]:
            null_count = df[col].isnull().sum()
            aieq_null_count = df[df['isin'].isin(aieq_isins)][col].isnull().sum()
            stats["null_fields"][col] = {
                "null_count": int(null_count),
                "aieq_null_count": int(aieq_null_count)
            }
    
    return stats


print(f"Fetching data for model={model}, date={date}")
model_df = fetch_es_data("model")
metrics_df = fetch_es_data("metrics")

print("\n=== Model Data ===")
if not model_df.empty:
    model_stats = analyze_data(model_df, "model")
    print(f"AIEQ hits: {model_stats['aieq_hits']}")
    for field, counts in model_stats['null_fields'].items():
        print(f"{field}: {counts['null_count']} nulls ({counts['aieq_null_count']} in AIEQ)")
else:
    print("No model data retrieved.")

print("\n=== Metrics Data ===")
if not metrics_df.empty:
    metrics_stats = analyze_data(metrics_df, "metrics")
    print(f"AIEQ hits: {metrics_stats['aieq_hits']}")
    for field, counts in metrics_stats['null_fields'].items():
        print(f"{field}: {counts['null_count']} nulls ({counts['aieq_null_count']} in AIEQ)")
else:
    print("No metrics data retrieved.")

formatted_date = date 

email_content = f"""
<html>
<head>
  <style>
    .bold {{
        font-weight: bold;
    }}
    body {{
        font-family: Arial, sans-serif;
        line-height: 1.6;
    }}
    .section {{
        margin-bottom: 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
    }}
    h3 {{
        color: #2c3e50;
        margin-bottom: 10px;
    }}
  </style>
</head>
<body>
  <p>Hi Team,</p>
  <p>Below are the counts for <b>AIEQ</b> as of {formatted_date}:</p>
  
  <div class="section">
    <h3>Model Data ({model})</h3>
    <p>Count of AIEQ ISINs in MasterActive Firm: {len(real_aieq_isins)}</p>
    <p>Count of AIEQ Hits: {model_stats['aieq_hits']}</p>
    <p>AIEQ Hits with null predictions: {model_stats['null_fields']['predictions']['aieq_null_count']}</p>
  </div>

  <div class="section">
    <h3>Metrics Data ({model})</h3>
    <p>Count of AIEQ Hits: {metrics_stats['aieq_hits']}</p>
    <p>AIEQ Hits with null RMSE: {metrics_stats['null_fields']['rmse']['aieq_null_count']}</p>
    <p>AIEQ Hits with null Directionality: {metrics_stats['null_fields']['directionality']['aieq_null_count']}</p>
    <p>AIEQ Hits with null Avg Confidence: {metrics_stats['null_fields']['avg_confidence']['aieq_null_count']}</p>
  </div>

  <p>Regards,</p>
  <p>Garima Yadav</p>
</body>
</html>
"""

# Compose the email
msg = MIMEMultipart("alternative")
msg["From"] = sender_email
msg["To"] = ", ".join(receiver_email)
msg["Subject"] = f"AIEQ ISIN Counts Run Summary: {model} - {formatted_date}-{schedular}"

# Attach the HTML content
msg.attach(MIMEText(email_content, "html"))

# Send the email
try:
    with smtplib.SMTP("smtp.gmail.com", 587) as server:
        server.starttls()
        server.login(username, password)
        server.sendmail(sender_email, receiver_email, msg.as_string())
    print("Email sent successfully.")
except Exception as e:
    print(f"Error sending email: {e}")



