import pandas as pd
import numpy as np
import requests
import json
import io
import os
from aws_requests_auth.aws_auth import AWSRequestsAuth
from datetime import datetime, timedelta
import argparse
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime
import boto3
from io import StringIO



yesterday = datetime.today() - timedelta(days=1)
date=yesterday.strftime('%Y-%m-%d')
print(date)

smtp_server = "smtp.gmail.com"
smtp_port = 587
username = "<EMAIL>"
password = "vies rtlt mxwb qyap"
sender_email = "<EMAIL>"
#receiver_email = ["<EMAIL>","<EMAIL>"]

receiver_email=["<EMAIL>", "<EMAIL>", '<EMAIL>','<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']


auth_es = AWSRequestsAuth(aws_access_key='********************',
                               aws_secret_access_key='xBm4YAAiH/OgddKUbgKJUT6DK34i1zW0Ikf6LPcV',
                               aws_host = 'search-eq-data-es-77f45yx7k36ad4mj2uxk5lzk3y.us-east-1.es.amazonaws.com', 
                               aws_region='us-east-1',
                               aws_service='es') #------ Elastic search Credentials
s3 = boto3.client('s3', 
                  aws_access_key_id= "********************",
                  aws_secret_access_key= "bNrlV4j7SvirTnHBBdEJ/5XTaIaaoRm/QmfUTsL/", 
                    region_name='us-east-1') #-----------------S3 Credentials



parser = argparse.ArgumentParser(prog='ETFs-Monitoring', description="Script for Daily ETFs Monitoring.")
parser.add_argument('-m', '--model')
args = parser.parse_args()
model = args.model





dbetf_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=dbetf').json()
db = pd.DataFrame(dbetf_json["data"]["masteractivefirms_dbetf"])

if 'isin' in db.columns:
    isin_data = db[['isin']]
    #print(isin_data)
else:
    print("Column 'isin' not found in the DataFrame")
real_db_isin = pd.DataFrame(isin_data)





bnpetf_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=bnpetf').json()
bnp= pd.DataFrame(bnpetf_json["data"]["masteractivefirms_bnpetf"])
if 'isin' in bnp.columns:
    isin_data = bnp[['isin']] 
else:
    print("Column 'isin' not found in the DataFrame")
real_bnp_isin=pd.DataFrame(isin_data)




multietf_json = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=ben_etf').json()
multi = pd.DataFrame(multietf_json["data"]["masteractivefirms_ben_etf"])
if {'isin', 'tic'}.issubset(multi.columns):
    isin_data = multi[['isin', 'tic']]
else:
    print("Required columns 'isin' and/or 'tic' not found in the DataFrame")
    isin_data = pd.DataFrame() 
real_multi_isin = pd.DataFrame(isin_data)
#print(len(real_multi_isin))
#print(real_multi_isin.head())





aigo_etf_json_1 = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=goetf').json()
aigo_etf1 = pd.DataFrame(aigo_etf_json_1["data"]["masteractivefirms_goetf"])

if {'isin', 'tic'}.issubset(aigo_etf1.columns):
    isin_data_1 = aigo_etf1[['isin', 'tic']]
else:
    print("Required columns 'isin' and/or 'tic' not found in the first DataFrame")
    isin_data_1 = pd.DataFrame()


aigo_etf_json_2 = requests.get('http://alb-master-1830456747.us-east-1.elb.amazonaws.com:8080/masteractivefirms/getmasterbytag?tag=sectors').json()
aigo_etf2 = pd.DataFrame(aigo_etf_json_2["data"]["masteractivefirms_sectors"])
if {'isin', 'tic'}.issubset(aigo_etf2.columns):
    isin_data_2 = aigo_etf2[['isin', 'tic']]
else:
    print("Required columns 'isin' and/or 'tic' not found in the second DataFrame")
    isin_data_2 = pd.DataFrame()


real_aigoetf_isin = pd.concat([isin_data_1, isin_data_2]).drop_duplicates()
real_aigoetf_isin = real_aigoetf_isin[real_aigoetf_isin['isin'] != 'HSMETYB8'].reset_index(drop=True)
print(len(real_aigoetf_isin))

def fetch_data_es(start_date, end_date, schedular, auth_es, model_name=None, functionality_type=None):

    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    year = start_date.year
    print(model_name)
    index_suffix = 'model' if functionality_type == 'model' else 'model_metrics'
    index_name = f"eq_{model_name}_{index_suffix}_{year}"
    
    print(f"Searching index: {index_name} for {functionality_type} data")

    # Build the Elasticsearch query
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "range": {
                            "date": {
                                "gte": start_date.strftime('%Y-%m-%d'),
                                "lte": end_date.strftime('%Y-%m-%d')
                            }
                        }
                    },
                    {
                        "term": {
                            "schedular.keyword": schedular
                        }
                    }
                ]
            }
        },
        "_source": ["isin", "date", "prediction", "score","tic"],  # Include relevant fields
        "size": 10000,
        "sort": [{"date": "asc"}]
    }

    try:
        # Make the request to Elasticsearch
        url = f"https://{auth_es.aws_host}/{index_name}/_search"
        response = requests.post(
            url,
            auth=auth_es,
            json=query,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        response.raise_for_status()
        
        # Process the response
        hits = response.json().get('hits', {}).get('hits', [])
        if not hits:
            print(f"No documents found in index: {index_name}")
            return None
            
        # Extract data from hits
        data = []
        for hit in hits:
            source = hit.get('_source', {})
            record = {
                'isin': source.get('isin'),
                'date': source.get('date'),
                'tic':source.get('tic'),
                'schedular': schedular,
                'model_name': model_name,
                'functionality_type': functionality_type
            }
            
            # Add model-specific fields
            if functionality_type == 'model':
                record['prediction'] = source.get('prediction')
            else:  # metrics
                record['score'] = source.get('score')
            
            data.append(record)
        
        df = pd.DataFrame(data)
        df['date'] = pd.to_datetime(df['date'])
        return df
        
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            print(f"Index not found: {index_name}")
        else:
            print(f"HTTP Error ({e.response.status_code}): {e.response.text}")
        return None
    except Exception as e:
        print(f"Error fetching ETF data from ES: {str(e)}")
        return None


def fetch_s3_data(start_date, end_date, bucket_name, filepath, s3, data_type=None, functionality_type=None, isin_column=None, model_category=None):
    try:
        valid_data_types = ['aigo_etf', 'db', 'bnp', 'multi_asset']
        if data_type and data_type not in valid_data_types and model_category != 'lstm':
            raise ValueError(f"Invalid data_type. Must be one of {valid_data_types}")

        valid_functionality_types = ['model', 'metric']
        if functionality_type not in valid_functionality_types:
            raise ValueError(f"Invalid functionality_type. Must be one of {valid_functionality_types}")

        try:
            if isinstance(start_date, str):
                start_dt = pd.to_datetime(start_date, format='%d-%m-%Y').normalize()
            else:
                start_dt = pd.to_datetime(start_date).normalize()
                
            if isinstance(end_date, str):
                end_dt = pd.to_datetime(end_date, format='%d-%m-%Y').normalize()
            else:
                end_dt = pd.to_datetime(end_date).normalize()
        except Exception as e:
            print(f"Invalid date format: {e}")
            return pd.DataFrame(columns=['date', 'isin', 'data_type', 'functionality_type', 'file_path'])

        if data_type == 'bnp' and model_category != 'lstm':
            formatted_path = filepath.format(date=end_dt.strftime('%Y-%m-%d'))
            filename = os.path.basename(formatted_path)
            isin = filename.split('_')[2]
            
            try:
                s3_obj = s3.get_object(Bucket=bucket_name, Key=formatted_path)
                result_df = pd.DataFrame({
                    'isin': [isin],
                    'date': [end_dt.strftime('%Y-%m-%d')],
                    'data_type': [data_type],
                    'functionality_type': [functionality_type],
                    'file_path': [formatted_path]
                })
                print(f"Found BNP file for date {end_dt.date()} with ISIN {isin}")
                return result_df
            except Exception as e:
                print(f"BNP file not found for date {end_dt.date()}: {str(e)}")
                return pd.DataFrame(columns=['date', 'isin', 'data_type', 'functionality_type', 'file_path'])

        if model_category == 'lstm':
            try:
                year = end_dt.strftime('%Y')
                formatted_path = filepath.format(year=year, date=end_dt.strftime('%Y-%m-%d'))
                
                s3_obj = s3.get_object(Bucket=bucket_name, Key=formatted_path)
                
                file_content = s3_obj['Body'].read()
                
                if formatted_path.endswith('.csv'):
                    df = pd.read_csv(io.BytesIO(file_content))
                else:
                    try:
                        df = pd.read_csv(io.BytesIO(file_content))
                    except:
                        print(f"Unsupported file format: {formatted_path}")
                        return pd.DataFrame(columns=['date', 'tic', 'data_type', 'functionality_type', 'file_path'])
                
                df.columns = df.columns.str.lower().str.strip()
                
                id_col = isin_column.lower()
                if id_col not in df.columns:
                    print(f"Identifier column {id_col} not found in {formatted_path}")
                    return pd.DataFrame(columns=['date', 'tic', 'data_type', 'functionality_type', 'file_path'])
                
                date_col = next((col for col in df.columns if 'date' in col), None)
                if not date_col:
                    print(f"No date column found in {formatted_path}")
                    return pd.DataFrame(columns=['date', 'tic', 'data_type', 'functionality_type', 'file_path'])
                
                df[date_col] = pd.to_datetime(df[date_col], errors='coerce').dt.normalize()
               
                if end_dt in df[date_col].values:
                    print(f"End date {end_dt.date()} found in {formatted_path}")
                    results = []
                    for _, row in df[df[date_col] == end_dt].iterrows():
                        results.append({
                            'date': end_dt.strftime('%Y-%m-%d'),
                            'isin': row[id_col],
                            'data_type': 'lstm',  # Always use 'lstm' as data_type for LSTM models
                            'functionality_type': functionality_type,
                            'file_path': formatted_path
                        })
                    return pd.DataFrame(results)
                else:
                    print(f"End date {end_dt.date()} not found in {formatted_path}")
                    return pd.DataFrame(columns=['date', 'isin', 'data_type', 'functionality_type', 'file_path'])
                    
            except Exception as e:
                print(f"Error processing LSTM file: {str(e)}")
                return pd.DataFrame(columns=['date', 'isin', 'data_type', 'functionality_type', 'file_path'])

        else:
            isin_df_map = {
                'aigo_etf': real_aigoetf_isin,
                'db': real_db_isin,
                'bnp': real_bnp_isin,
                'multi_asset': real_multi_isin
            }
            isin_df = isin_df_map[data_type]
            
            key_column = isin_column
            isin_df[key_column] = isin_df[key_column].astype(str).str.strip().str.upper()

            path_identifier_map = {}
            for identifier in isin_df[key_column].dropna().unique():
                try:
                    formatted_path = filepath.format(**{key_column: identifier})
                    path_identifier_map[formatted_path] = identifier
                except KeyError as e:
                    print(f"Error formatting path for {identifier}: {e}")
                    continue

            existing_keys = set()
            paginator = s3.get_paginator('list_objects_v2')
            
            try:
                for page in paginator.paginate(Bucket=bucket_name, Prefix=filepath.split('{')[0]):
                    for obj in page.get('Contents', []):
                        existing_keys.add(obj['Key'])
            except Exception as e:
                print(f"Error listing S3 objects: {e}")
                return pd.DataFrame(columns=['date', 'isin', 'data_type', 'functionality_type'])

            results = []
            
            for file_path, identifier in path_identifier_map.items():
                if file_path not in existing_keys:
                    print(f"File not found in S3: {file_path}")
                    continue

                try:
                    s3_obj = s3.get_object(Bucket=bucket_name, Key=file_path)
                    file_content = s3_obj['Body'].read()
                    
                    if file_path.endswith('.csv'):
                        df = pd.read_csv(io.BytesIO(file_content))
                    elif file_path.endswith(('.xlsx', '.xls')):
                        df = pd.read_excel(io.BytesIO(file_content))
                    else:
                        try:
                            df = pd.read_csv(io.BytesIO(file_content))
                        except:
                            print(f"Unsupported file format: {file_path}")
                            continue

                    df.columns = df.columns.str.lower().str.strip()
                    date_col = next((col for col in df.columns if 'date' in col), None)
                    if not date_col:
                        print(f"No date column found in {file_path}")
                        continue
                    
                    df[date_col] = pd.to_datetime(df[date_col], errors='coerce').dt.normalize()
                    
                    if end_dt in df[date_col].values:
                        print(f"End date {end_dt.date()} found in {file_path}")
                        row = df[df[date_col] == end_dt].iloc[0]
                        actual_identifier = row.get(key_column, identifier)
                        
                        results.append({
                            'date': end_dt.strftime('%Y-%m-%d'),
                            'isin': actual_identifier,
                            'data_type': data_type,
                            'functionality_type': functionality_type,
                            'file_path': file_path
                        })
                    else:
                        print(f"End date {end_dt.date()} not found in {file_path}")

                except Exception as e:
                    print(f"Error processing {file_path}: {str(e)}")
                    continue

            if results:
                return pd.DataFrame(results)
            else:
                print("No files contained the end date")
                return pd.DataFrame(columns=['date', 'isin', 'data_type', 'functionality_type', 'file_path'])

    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return pd.DataFrame(columns=['date', 'isin', 'data_type', 'functionality_type', 'file_path'])

def build_final_summary(es_df, s3_df, s3_df_vc, model_variable_name, final_df_model=None, final_df_metrics=None, functionality_type=None, tag=None, model_category=None):  
    es_missing = s3_missing = "Data not available"
    is_quarterly_aigo = (tag == "aigo_etf" and "quarterly" in model_variable_name.lower())
    
    try:
        provider_isins_for_es, provider_isins_for_s3 = get_provider_isins_updated(tag, is_quarterly_aigo)
            
    except Exception as e:
        print(f"Error getting ISINs for provider {tag}: {e}")
        provider_isins_for_es = set()
        provider_isins_for_s3 = set()

    try:
        if es_df is not None and not es_df.empty:
            if tag in ["aigo_etf", "multi_asset"]:  
                es_ids = set(es_df['tic'].astype(str).str.upper())
            else:  
                es_ids = set(es_df['isin'].astype(str).str.upper())
            es_missing = len(provider_isins_for_es - es_ids)
        else:
            es_missing = "No ES data"
        if s3_df is not None and not s3_df.empty:
            s3_isins = set(s3_df['isin'].astype(str).str.upper())
            s3_missing = len(provider_isins_for_s3 - s3_isins)
        else:
            s3_missing = "No S3 data"
            
    except Exception as e:
        print(f"Error processing data for {tag}: {e}")
        es_missing = s3_missing = "Error"
    display_name = format_model_name_updated(model_variable_name, tag, is_quarterly_aigo, model_category)
 
    if functionality_type == "model":
        summary_data = {
            "ETFs Model": display_name,
            "ES ": es_missing,
            "S3 ": s3_missing
        }
    else:
        summary_data = {
            "ETFs Metrics": display_name,
            "ES ": es_missing,
            "S3 ": s3_missing
        }

    summary_row = pd.DataFrame([summary_data])

    if functionality_type == "model":
        final_df_model = pd.concat([final_df_model, summary_row], ignore_index=True) if final_df_model is not None else summary_row
        final_df_model = sort_dataframe_lstm_bottom(final_df_model, "ETFs Model")
        return final_df_model, final_df_metrics
    else:
        final_df_metrics = pd.concat([final_df_metrics, summary_row], ignore_index=True) if final_df_metrics is not None else summary_row
        final_df_metrics = sort_dataframe_lstm_bottom(final_df_metrics, "ETFs Metrics")
        return final_df_model, final_df_metrics


def get_provider_isins_updated(provider, is_quarterly_aigo=False):
    
    if provider == "aigo_etf":
        if is_quarterly_aigo:
            temp_df = real_aigoetf_isin.copy()
            temp_df['tic'] = temp_df['tic'].astype(str).str.upper()
            temp_df['isin'] = temp_df['isin'].astype(str).str.upper()
            filtered_tics = temp_df[temp_df['isin'] != temp_df['tic']]['tic']
            provider_isins_for_es = set(filtered_tics)
            provider_isins_for_s3 = set(filtered_tics)
        else:
            provider_isins_for_es = set(real_aigoetf_isin['tic'].astype(str).str.upper())
            provider_isins_for_s3 = set(real_aigoetf_isin['tic'].astype(str).str.upper())
            
    elif provider == "multi_asset":
        provider_isins_for_es = set(real_multi_isin['tic'].astype(str).str.upper())
        provider_isins_for_s3 = set(real_multi_isin['tic'].astype(str).str.upper())
        
    elif provider == "bnp":
        provider_isins_for_es = set(real_bnp_isin['isin'].astype(str).str.upper())
        provider_isins_for_s3 = set(real_bnp_isin['isin'].astype(str).str.upper())
        
    elif provider == "db":
        provider_isins_for_es = set(real_db_isin['isin'].astype(str).str.upper())
        provider_isins_for_s3 = set(real_db_isin['isin'].astype(str).str.upper())
        
    else:
        provider_isins_for_es = set()
        provider_isins_for_s3 = set()
    
    return provider_isins_for_es, provider_isins_for_s3


def format_model_name_updated(model_variable_name, provider, is_quarterly_aigo=False, model_category=None):
    if model_category == "lstm":
        provider_map = {
            "aigo_etf": "aigo",
            "aigo": "aigo",
            "bnp": "bnp", 
            "db": "db",
            "multi_asset": "multiasset"
        }
        
        provider_display = provider_map.get(provider, provider)
        if "quarterly" in model_variable_name.lower():
            frequency = "quarterly"
        else:
            frequency = "monthly"
            
        return f"lstm_{provider_display}_{frequency}"
    
    else:
        provider_map = {
            "aigo_etf": "aigo",
            "aigo": "aigo",  
            "bnp": "bnp",
            "db": "db",
            "multi_asset": "multiasset"
        }
        
        provider_display = provider_map.get(provider, provider)
        
        if provider in ["aigo_etf", "aigo"]:
            frequency = "quarterly" if (is_quarterly_aigo or provider == "aigo_quarterly") else "monthly"
        elif provider == "multi_asset":
            parts = model_variable_name.lower().split("_")
            frequency = "quarterly" if "quarterly" in parts else "monthly"
        else:
            frequency = "monthly"
        
        return f"{provider_display}_{frequency}"


def sort_dataframe_lstm_bottom(df, column_name):
    if df is None or df.empty:
        return df
    df['_sort_key'] = df[column_name].str.startswith('lstm_').astype(int)
    
    df_sorted = df.sort_values(['_sort_key', column_name]).drop(columns=['_sort_key'])
    
    return df_sorted.reset_index(drop=True)


def get_provider_isins(provider):
    provider_isin_map = {
        "aigo_etf": real_aigoetf_isin['tic'],
        "bnp": real_bnp_isin['isin'],
        "db": real_db_isin['isin'],
        "multi_asset": real_multi_isin['tic']
    }
    isin_series = provider_isin_map.get(provider, pd.Series([]))
    return set(isin_series.astype(str).str.upper())


def format_model_name(model_variable_name, provider, is_quarterly_aigo=False):
    return format_model_name_updated(model_variable_name, provider, is_quarterly_aigo, model_category=None)

config = {
    "etf" :{
        "Monthly": {
            "aigo_etf": {
                "model": {
                    "bucket_name": "etf-predictions",
                    "filepath_1": "Monthly/aigo/predictions/{tic}_predictions.csv",
                    "filepath_2": "Monthly/sector/predictions/{tic}_predictions.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "etf-predictions",
                    "filepath_1": "Monthly/aigo/metrics/{tic}_metrics.csv",
                    "filepath_2": "Monthly/sector/metrics/{tic}_metrics.csv",
                    "isin_column": "tic"
                }
            },
            "db": {
                "model": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Monthly/db/predictions/{isin}_predictions.csv",
                    "isin_column": "isin"
                },
                "metric": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Monthly/db/metrics/{isin}_metrics.csv",
                    "isin_column": "isin"
                }
            },
            "bnp": {
                "model": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Monthly/new_BNP_Paribas/BNP_retrained/Daily_run_predictions/{isin}/daily_pred_{isin}_{date}.csv",
                    "isin_column": "isin"
                },
                "metric": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Monthly/new_BNP_Paribas/Daily_Run_metrics/{isin}/daily_metrics_{isin}_{date}.csv",
                    "isin_column": "isin"
                }
            },
            "multi_asset": {
                "model": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Monthly/multiasset/predictions/{tic}_predictions.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Monthly/multiasset/metrics/{tic}_metrics.csv",
                    "isin_column": "tic"
                }
            }
        },
        "Quarterly": {
            "aigo_etf": {
                "model": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Quarterly/public_etfs/transformed_data/predictions_with_raw_features/{tic}_predictions.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Quarterly/public_etfs/transformed_data/metrics/{tic}_metrics.csv",
                    "isin_column": "tic"
                }
            },
            "multi_asset": {
                "model": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Quarterly/multiasset/predictions/{tic}_predictions.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "etf-predictions",
                    "filepath": "Quarterly/multiasset/metrics/{tic}_metrics.csv",
                    "isin_column": "tic"
                }
            }
        }
    },
    "lstm" :{
        "Monthly": {
            "aigo_etf": {
                "model": {
                    "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/goetf/predictions/7_Day/{year}/predictionresult_{date}.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/goetf/metrics/7_Day/{year}/metrics_{date}.csv",
                    "isin_column": "tic"
                }
            },
            "db": {
                "model": {
                   "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/dbetf/predictions/7_Day/{year}/predictionresult_{date}.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/dbetf/metrics/7_Day/{year}/metrics_{date}.csv",
                    "isin_column": "tic"
                }
            },
            "bnp": {
                "model": {
                    "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/bnpetf/predictions/7_Day/{year}/predictionresult_{date}.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/bnpetf/metrics/7_Day/{year}/metrics_{date}.csv",
                    "isin_column": "tic"
                }
            },
            "multi_asset": {
                "model": {
                    "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/ben_etf/predictions/7_Day/{year}/predictionresult_{date}.csv",
                    "isin_column": "tic"
                },
                "metric": {
                    "bucket_name": "historical-prediction-data-15yrs",
                    "filepath": "lstm/ben_etf/metrics/7_Day/{year}/metrics_{date}.csv",
                    "isin_column": "tic"
                }
            }
        }
    }
}


final_df_model = pd.DataFrame()
final_df_metrics = pd.DataFrame()

if isinstance(date, str):
    date = datetime.strptime(date, '%Y-%m-%d')

formatted_date = date.strftime('%Y-%m-%d')
formatted_date_lstm = date.strftime('%Y_%m_%d')

for model_category, frequencies in config.items():
    print(f"\nProcessing {model_category.upper()} data...")
    
    for frequency, providers in frequencies.items():
        print(f"\nProcessing {model_category} data with {frequency} frequency...")

        for provider, functionality_types in providers.items():
            print(f"\nProcessing {provider} provider data...")

            for functionality_type, config_details in functionality_types.items():
                if functionality_type not in ["model", "metric"]:
                    continue

                print(f"\nProcessing {functionality_type} data for {provider}...")

                data_es = fetch_data_es(
                    model_name=model_category,
                    start_date=date,
                    end_date=date,
                    schedular=frequency,
                    auth_es=auth_es,
                    functionality_type=functionality_type
                )

                es_count = len(data_es) if data_es is not None else 0
                print(f"Retrieved {es_count} {functionality_type} records from ES for {model_category} {frequency}")

                try:
                    if model_category == "lstm":
                        date_format = formatted_date_lstm if functionality_type == "model" else formatted_date
                        year = date.strftime('%Y')
                        
                        filepath = config_details["filepath"].format(year=year, date=date_format)
                        s3_df = fetch_s3_data(
                            start_date=date,
                            end_date=date,
                            bucket_name=config_details["bucket_name"],
                            filepath=filepath,
                            s3=s3,
                            data_type=provider,
                            functionality_type=functionality_type,
                            model_category=model_category,
                            isin_column=config_details["isin_column"]
                        )
                    else:
                        if provider == "aigo_etf":
                            identifiers = real_aigoetf_isin["tic"].unique()
                            id_param = "tic"
                        elif provider == "db":
                            identifiers = real_db_isin["isin"].unique()
                            id_param = "isin"
                        elif provider == "bnp":
                            identifiers = real_bnp_isin["isin"].unique()
                            id_param = "isin"
                        elif provider == "multi_asset":
                            identifiers = real_multi_isin["tic"].unique()
                            id_param = "tic"
                        else:
                            print(f"Unsupported provider: {provider}")
                            continue

                        combined_s3_df = pd.DataFrame()

                        for identifier in identifiers:
                            format_params = {id_param: identifier}
                            if provider == "bnp":
                                format_params.update({"bnp": "bnp", "date": formatted_date})

                            if provider == "aigo_etf" and functionality_type in ["model", "metric"]:
                                filepaths_to_try = []
                                
                                if "filepath_1" in config_details and "filepath_2" in config_details:
                                    filepaths_to_try = [config_details["filepath_1"], config_details["filepath_2"]]
                                elif "filepath" in config_details:
                                    filepaths_to_try = [config_details["filepath"]]
                                
                                for filepath_template in filepaths_to_try:
                                    filepath = filepath_template.format(**format_params)
                                    s3_df_part = fetch_s3_data(
                                        start_date=date,
                                        end_date=date,
                                        bucket_name=config_details["bucket_name"],
                                        filepath=filepath,
                                        s3=s3,
                                        data_type=provider,
                                        functionality_type=functionality_type,
                                        model_category=model_category,
                                        isin_column=config_details["isin_column"]
                                    )
                                    
                                    if s3_df_part is not None and not s3_df_part.empty:
                                        combined_s3_df = pd.concat([combined_s3_df, s3_df_part])
                                        break
                            else:
                                filepath = config_details["filepath"].format(**format_params)
                                s3_df_part = fetch_s3_data(
                                    start_date=date,
                                    end_date=date,
                                    bucket_name=config_details["bucket_name"],
                                    filepath=filepath,
                                    s3=s3,
                                    data_type=provider,
                                    functionality_type=functionality_type,
                                    model_category=model_category,
                                    isin_column=config_details["isin_column"]
                                )
                                if s3_df_part is not None and not s3_df_part.empty:
                                    combined_s3_df = pd.concat([combined_s3_df, s3_df_part])

                        if not combined_s3_df.empty:
                            combined_s3_df = combined_s3_df.drop_duplicates(subset=['date', 'isin', 'data_type', 'functionality_type'])
                        
                        s3_df = combined_s3_df if not combined_s3_df.empty else None

                    print(f"S3 data: {len(s3_df) if s3_df is not None else 0} records")

                except Exception as e:
                    print(f"Error processing identifiers or reading S3 for {provider}: {str(e)}")
                    s3_df = None
                print("s3 data: ")
                print(s3_df.head())
                print("es data: ")
                print(data_es.head())
                display_name = f"{provider}_{frequency.lower()}"

                if functionality_type == "model":
                    final_df_model, _ = build_final_summary(
                        es_df=data_es,
                        s3_df=s3_df,
                        s3_df_vc=None,
                        model_variable_name=display_name,
                        final_df_model=final_df_model,
                        final_df_metrics=None,
                        functionality_type=functionality_type,
                        tag=provider,
                        model_category=model_category
                    )
                else:
                    _, final_df_metrics = build_final_summary(
                        es_df=data_es,
                        s3_df=s3_df,
                        s3_df_vc=None,
                        model_variable_name=display_name,
                        final_df_model=None,
                        final_df_metrics=final_df_metrics,
                        functionality_type=functionality_type,
                        tag=provider,
                        model_category=model_category
                    )

print("\nFinal Model Summary:")
print(final_df_model.head(20))

print("\nFinal Metrics Summary:")
print(final_df_metrics.head(20))


config_etf = {
    "bucket_name": "eq-model-output",
    "model": "etf_model",
    "tag": ["aigo", "bnp", "db", "multiasset"],
    "delivery": {
        "scheduler": {
            "monthly": {
                "tag": ["aigo", "bnp", "db", "multiasset"],
                "file": {
                    "predictions": "{tag}.csv",
                    "metrics": "{tag}_metrics.csv"
                }
            },
            "quarterly": {
                "tag": ["aigo", "multiasset"],
                "file": {
                    "predictions": "{tag}.csv",
                    "metrics": "{tag}_metrics.csv"
                }
            }
        },
        "path": "{tag}/delivery/{scheduler}/{date}/"
    },
    "pipeline_mapped": {
        "scheduler": "monthly",
        "path": "{tag}/etf_pipeline_mapped/monthly/{date}/predictions/{isin}.csv",
        "tag": ["aigo", "bnp", "db", "multiasset"]
    },
    "correlated_consolidated": {
        "scheduler": "monthly",
        "path": "{tag}/etf_correlated_consolidated/monthly/{date}/predictions/{isin}.csv",
        "tag": ["aigo", "bnp", "db", "multiasset"]
    },
    "post_processed": {
        "scheduler": {
            "monthly": {
                "tag": ["aigo", "bnp", "db", "multiasset"],
                "path": {
                    "predictions": "{tag}/etf_post_processed/monthly/{date}/predictions/{isin}.csv",
                    "metrics": "{tag}/etf_post_processed/monthly/{date}/metrics/{isin}.csv"
                }
            },
            "quarterly": {
                "tag": ["aigo", "multiasset"],
                "path": {
                    "predictions": "{tag}/etf_post_processed/quarterly/{date}/predictions/{isin}.csv",
                    "metrics": "{tag}/etf_post_processed/quarterly/{date}/metrics/{isin}.csv"
                }
            }
        }
    },
    "initial_pipeline": {
        "scheduler": {
            "monthly": {
                "tag": ["aigo", "bnp", "db", "multiasset"],
                "path": {
                    "predictions": "{tag}/etf_initial_pipeline/monthly/{date}/predictions/{isin}.csv",
                    "metrics": "{tag}/etf_initial_pipeline/monthly/{date}/metrics/{isin}.csv"
                }
            },
            "quarterly": {
                "tag": ["aigo", "multiasset"],
                "path": {
                    "predictions": "{tag}/etf_initial_pipeline/quarterly/{date}/predictions/{isin}.csv",
                    "metrics": "{tag}/etf_initial_pipeline/quarterly/{date}/metrics/{isin}.csv"
                }
            }
        }
    },
    "lstm": {
        "Monthly": {
            "metric": {
                "bucket_name_vc": "eq-model-output",
                "base_filepath": "lstm_model/monthly"
            },
            "model": {
                "bucket_name_vc": "eq-model-output",
                "base_filepath": "lstm_model/monthly"
            }
        }
    }
}

def get_s3_file(bucket, key):
    try:
        obj = s3.get_object(Bucket=bucket, Key=key)
        return pd.read_csv(StringIO(obj['Body'].read().decode('utf-8')))
    except Exception as e:
        print(f"File not found or error reading {key}: {str(e)}")
        return pd.DataFrame()

def get_isins_from_delivery(df, tag):
    if df.empty:
        return set()
    column_options = {
        'bnp': ['Ticker', 'ticker', 'ISIN', 'isin'],
        'aigo': ['Equity', 'equity', 'ISIN', 'isin'],
        'multiasset': ['Equity', 'equity', 'ISIN', 'isin'],
        'db': ['isin', 'ISIN']}
    
    for col in column_options.get(tag, []):
        if col in df.columns:
            return set(df[col].dropna().unique())
    
    print(f"No valid identifier column found for {tag} in delivery file")
    return set()

def get_isins_from_metrics(df):
    if df.empty:
        return set()
    
    for col in ['isin', 'ISIN', 'identifier', 'Identifier']:
        if col in df.columns:
            return set(df[col].dropna().unique())
    
    print("No valid ISIN column found in metrics file")
    return set()

def get_isins_from_path(bucket, base_path, date, tag, file_prefix=""):
    try:
        formatted_date = pd.to_datetime(date).strftime('%Y-%m-%d')
        path = base_path.format(tag=tag, date=formatted_date)
        path = path.rstrip('/')
        if file_prefix:
            path = f"{file_prefix}/{path}"
        
        print(f"Listing objects in path: {path}")
        
        isins = set()
        continuation_token = None
        total_objects = 0
        while True:
            if continuation_token:
                response = s3.list_objects_v2(
                    Bucket=bucket, 
                    Prefix=path,
                    ContinuationToken=continuation_token
                )
            else:
                response = s3.list_objects_v2(Bucket=bucket, Prefix=path)
            
            if 'Contents' in response:
                batch_count = len(response['Contents'])
                total_objects += batch_count
                print(f"Processing batch of {batch_count} objects (total so far: {total_objects})")
                
                for obj in response['Contents']:
                    key = obj['Key']
                    
                    if key.endswith('.csv'):
                        filename = key.split('/')[-1]
                        isin = filename.replace('.csv', '')
                        
                        if isin and not any(skip in isin.lower() for skip in ['summary', 'aggregate', 'all']):
                            isins.add(isin)
            if response.get('IsTruncated', False):
                continuation_token = response.get('NextContinuationToken')
                print(f"More objects available, continuing with token...")
            else:
                break
        
        print(f"Found total {total_objects} objects, extracted {len(isins)} ISINs from path: {path}")
        return isins
        
    except Exception as e:
        print(f"Error listing objects in {path}: {str(e)}")
        return set()

def fetch_lstm_data(date, functionality_type):
    try:
        formatted_date = pd.to_datetime(date).strftime('%Y-%m-%d')
        
        if functionality_type == "model":
            bucket = config_etf['lstm']['Monthly']['model']['bucket_name_vc']
            base_path = config_etf['lstm']['Monthly']['model']['base_filepath']
            subfolder = "predictions"
        else:
            bucket = config_etf['lstm']['Monthly']['metric']['bucket_name_vc']
            base_path = config_etf['lstm']['Monthly']['metric']['base_filepath']
            subfolder = "metrics"
        
        prefix = f"{base_path}/{formatted_date}/{subfolder}/"
        print(f"Fetching LSTM {functionality_type} from path: {prefix}")
        
        isins = set()
        continuation_token = None
        total_objects = 0
        while True:
            if continuation_token:
                response = s3.list_objects_v2(
                    Bucket=bucket, 
                    Prefix=prefix,
                    ContinuationToken=continuation_token
                )
            else:
                response = s3.list_objects_v2(Bucket=bucket, Prefix=prefix)
            
            if 'Contents' in response:
                batch_count = len(response['Contents'])
                total_objects += batch_count
                print(f"Processing LSTM {functionality_type} batch of {batch_count} objects (total so far: {total_objects})")
                
                for obj in response['Contents']:
                    key = obj['Key']
                    if key.endswith('.csv'):
                        filename = key.split('/')[-1]
                        isin = filename.replace('.csv', '')
                        if isin and not any(skip in isin.lower() for skip in ['summary', 'aggregate', 'all']):
                            isins.add(isin)
            if response.get('IsTruncated', False):
                continuation_token = response.get('NextContinuationToken')
                print(f"More LSTM {functionality_type} objects available, continuing...")
            else:
                break
        
        print(f"Found total {total_objects} LSTM {functionality_type} objects, extracted {len(isins)} ISINs")
        return isins
        
    except Exception as e:
        print(f"Error fetching LSTM {functionality_type} data: {str(e)}")
        return set()

def get_reference_isins_from_dataframes(**reference_dfs):
    reference_isins = {}
    dataframe_mapping = {
        'real_aigoetf_isin': 'aigo',
        'real_bnp_isin': 'bnp', 
        'real_db_isin': 'db',
        'real_multi_isin': 'multiasset'
    }
    
    for df_name, tag in dataframe_mapping.items():
        try:
            if df_name in reference_dfs:
                df = reference_dfs[df_name]
                print(f"Using passed dataframe for {df_name}")
            elif df_name in globals():
                df = globals()[df_name]
                print(f"Using global dataframe for {df_name}")
            else:
                print(f"Dataframe {df_name} not found - skipping {tag}")
                reference_isins[tag] = set()
                continue
            
            if df is not None and not df.empty:
                isin_columns = ['ISIN', 'isin', 'Equity', 'equity', 'Ticker', 'ticker', 'identifier', 'Identifier']
                isins = set()
                
                print(f"Available columns in {df_name}: {list(df.columns)}")
                
                for col in isin_columns:
                    if col in df.columns:
                        isins = set(df[col].dropna().unique())
                        print(f"Found {len(isins)} ISINs in column '{col}' for {tag}")
                        break
                
                if not isins:
                    print(f"No ISIN column found in {df_name} - tried: {isin_columns}")
                
                reference_isins[tag] = isins
            else:
                print(f"Dataframe {df_name} is None or empty")
                reference_isins[tag] = set()
                
        except Exception as e:
            print(f"Error reading {df_name}: {str(e)}")
            reference_isins[tag] = set()
    
    return reference_isins

def compare_isins(ref_date, current_date):
    results = []
    bucket = config_etf['bucket_name']
    base_model = config_etf['model']
    
    ref_date_fmt = pd.to_datetime(ref_date).strftime('%Y-%m-%d')
    current_date_fmt = pd.to_datetime(current_date).strftime('%Y-%m-%d')
    
    print(f"Comparing data between {ref_date_fmt} and {current_date_fmt}")
    
    # Get reference ISINs from dataframes
    print("\n=== Reference ISINs from Dataframes ===")
    reference_isins = get_reference_isins_from_dataframes()
    
    # Get LSTM data for current date only
    print("\n=== LSTM Data ===")
    current_lstm_predictions = fetch_lstm_data(current_date_fmt, "model")
    current_lstm_metrics = fetch_lstm_data(current_date_fmt, "metric")
    
    print(f"Current LSTM predictions: {len(current_lstm_predictions)} ISINs")
    print(f"Current LSTM metrics: {len(current_lstm_metrics)} ISINs")

    # Process delivery files
    print("\n=== Delivery Files ===")
    for scheduler in ['monthly', 'quarterly']:
        if scheduler not in config_etf['delivery']['scheduler']:
            continue
            
        for tag in config_etf['delivery']['scheduler'][scheduler]['tag']:
            print(f"\nProcessing {tag} {scheduler} delivery files...")

            pred_path = config_etf['delivery']['path'].format(tag=tag, scheduler=scheduler, date=ref_date_fmt)
            pred_file = config_etf['delivery']['scheduler'][scheduler]['file']['predictions'].format(tag=tag)
            ref_key = f"{base_model}/{pred_path}{pred_file}"
            
            ref_df = get_s3_file(bucket, ref_key)
            ref_isins = get_isins_from_delivery(ref_df, tag)
            
            current_key = f"{base_model}/{pred_path.replace(ref_date_fmt, current_date_fmt)}{pred_file}"
            current_df = get_s3_file(bucket, current_key)
            current_isins = get_isins_from_delivery(current_df, tag)
            
            missing = ref_isins - current_isins
            results.append({
                'etfs': 'delivery_predictions',
                f"{tag}_{scheduler}": int(len(missing))
            })
            metrics_file = config_etf['delivery']['scheduler'][scheduler]['file']['metrics'].format(tag=tag)
            ref_metrics_key = f"{base_model}/{pred_path}{metrics_file}"
            ref_metrics_df = get_s3_file(bucket, ref_metrics_key)
            ref_metrics_isins = get_isins_from_metrics(ref_metrics_df)
            
            current_metrics_key = f"{base_model}/{pred_path.replace(ref_date_fmt, current_date_fmt)}{metrics_file}"
            current_metrics_df = get_s3_file(bucket, current_metrics_key)
            current_metrics_isins = get_isins_from_metrics(current_metrics_df)
            
            missing_metrics = ref_metrics_isins - current_metrics_isins
            results.append({
                'etfs': 'delivery_metrics',
                f"{tag}_{scheduler}": int(len(missing_metrics))
            })

    print("\n=== Processing File-based Data ===")
    file_types = [
        ('initial_pipeline', 'predictions'),
        ('initial_pipeline', 'metrics'),
        ('pipeline_mapped', 'predictions'),
        ('correlated_consolidated', 'predictions'),
        ('post_processed', 'predictions'),
        ('post_processed', 'metrics')
    ]
    
    for file_type, file_subtype in file_types:
        #print(f"\nProcessing {file_type} {file_subtype}...")
        if file_type in ['post_processed', 'initial_pipeline']:
            schedulers = ['monthly', 'quarterly']
        else:
            schedulers = ['monthly']
        
        for scheduler in schedulers:
            if file_type in ['post_processed', 'initial_pipeline']:
                if scheduler not in config_etf[file_type]['scheduler']:
                    continue
                tags = config_etf[file_type]['scheduler'][scheduler]['tag']
                path_template = config_etf[file_type]['scheduler'][scheduler]['path'][file_subtype]
            else:
                tags = config_etf[file_type]['tag']
                path_template = config_etf[file_type]['path']
            
            for tag in tags:
                print(f"  Processing {tag} {scheduler}...")
                base_path = path_template.split('{isin}')[0]
                ref_isins = get_isins_from_path(bucket, base_path, ref_date, tag, base_model)
                current_isins = get_isins_from_path(bucket, base_path, current_date, tag, base_model)
                
                missing = len(ref_isins - current_isins)
                print(f"    Reference ISINs: {len(ref_isins)}, Current ISINs: {len(current_isins)}, Missing: {missing}")
                
                results.append({
                    'etfs': f"{file_type}_{file_subtype}",
                    f"{tag}_{scheduler}": int(missing)
                })
    
    #print("\n=== lstm part ===")
    for tag in config_etf['tag']:
        if tag in reference_isins:
            ref_isins = reference_isins[tag]
            missing_lstm_pred = len(ref_isins - current_lstm_predictions)
            results.append({
                'etfs': 'lstm_predictions',
                f"{tag}_monthly": int(missing_lstm_pred)
            })
            #print(f"LSTM predictions - {tag}: Reference={len(ref_isins)}, Current={len(current_lstm_predictions)}, Missing={missing_lstm_pred}")  
            missing_lstm_metrics = len(ref_isins - current_lstm_metrics)
            results.append({
                'etfs': 'lstm_metrics',
                f"{tag}_monthly": int(missing_lstm_metrics)
            })
            #print(f"LSTM metrics - {tag}: Reference={len(ref_isins)}, Current={len(current_lstm_metrics)}, Missing={missing_lstm_metrics}")
        else:
            print(f"No reference ISINs found for {tag}")
            results.append({
                'etfs': 'lstm_predictions',
                f"{tag}_monthly": 0
            })
            results.append({
                'etfs': 'lstm_metrics', 
                f"{tag}_monthly": 0
            })
    
    if not results:
        print("No results generated - check if files exist in S3")
        return pd.DataFrame(columns=['etfs'] + [f"{tag}_{freq}" for tag in config_etf['tag'] 
                                              for freq in (['monthly'] if tag in ['bnp', 'db'] else ['monthly', 'quarterly'])])
    
    try:
        all_columns = ['etfs']
        for tag in config_etf['tag']:
            if tag in ['bnp', 'db']:
                all_columns.append(f"{tag}_monthly")
            else:
                all_columns.append(f"{tag}_monthly")
                all_columns.append(f"{tag}_quarterly")
        
        rows = []
        row_order = [
            'initial_pipeline_predictions',
            'initial_pipeline_metrics',
            'pipeline_mapped_predictions',
            'correlated_consolidated_predictions',
            'post_processed_predictions',
            'post_processed_metrics',
            'delivery_predictions',
            'delivery_metrics',
            'lstm_predictions',
            'lstm_metrics'
        ]
        for etfs in row_order:
            row_data = {'etfs': etfs}
            for col in all_columns[1:]:
                row_data[col] = 0
            for result in results:
                if result['etfs'] == etfs:
                    for key, value in result.items():
                        if key != 'etfs':
                            row_data[key] = value
            
            rows.append(row_data)
        final_df = pd.DataFrame(rows, columns=all_columns)
        for col in all_columns[1:]:
            final_df[col] = final_df[col].fillna(0).astype(int)
        
        return final_df
    
    except Exception as e:
        print(f"Error creating result DataFrame: {str(e)}")
        return pd.DataFrame(columns=['etfs'] + [f"{tag}_{freq}" for tag in config_etf['tag'] 
                                              for freq in (['monthly'] if tag in ['bnp', 'db'] else ['monthly', 'quarterly'])])

reference_date = '2025-05-15'  
current_date = date  
result_df = compare_isins(reference_date, current_date)
print("\n Final Result ")
print(result_df)


#Mail part
subject = "Daily ETFs Monitoring Summary Report "
 


msg = MIMEMultipart("alternative")
msg["Subject"] = subject
msg["From"] = sender_email
msg["To"] = ", ".join(receiver_email)


formatted_date = date.strftime("%B %d, %Y")


html_table_model = final_df_model.to_html(index=False, classes="dataframe", header=True, border=1)
html_table_metrics = final_df_metrics.to_html(index=False, classes="dataframe", header=True, border=1)
html_table_Version_Control = result_df.to_html(index=False, classes="dataframe", header=True, border=1)


isin_section = f"""
    <p><span class="bold">Count of AIGO in MasterActive Firm</span> = {len(real_aigoetf_isin)}</p>
    <p><span class="bold">Count of BNP in MasterActive Firm</span> = {len(real_bnp_isin)}</p>
    <p><span class="bold">Count of DB in MasterActive Firm</span> = {len(real_db_isin)}</p>
    <p><span class="bold">Count of Multi Asset in MasterActive Firm</span> = {len(real_multi_isin)}</p>
    """


html_content = f"""
<html>
<head>
  <style>
    table {{
        border-collapse: collapse;
        width: 80%;
        font-family: Arial, sans-serif;
    }}
    th, td {{
        border: 1px solid #dddddd;
        text-align: center;
        padding: 8px;
    }}
    th {{
        background-color: #4CAF50;
        color: white;
    }}
    .bold {{
        font-weight: bold;
    }}
    h3 {{
        font-weight: normal;
    }}
  </style>
</head>
<body>
  <p>Hi Team,</p>
  <p>Below is the table for the count of <b>missing ETFs</b> in S3 ,ES and Version Control:</p>
  {isin_section}

  <h3>Model Summary Table for {formatted_date}</h3>
  <p><b>Predictions</b></p>
  {html_table_model}

  <h3>Metrics Summary Table for {formatted_date}</h3>
  <p><b>Metrics</b></p>
  {html_table_metrics}

  <p><b>S3 Version Predictions and Metrics</b></p>
  {html_table_Version_Control}

  <p>Regards,</p>
  <p>Garima Yadav</p>
</body>
</html>
"""

msg.attach(MIMEText(html_content, "html"))

with smtplib.SMTP(smtp_server, smtp_port) as server:
    server.starttls()
    server.login(username, password)
    server.sendmail(sender_email, receiver_email, msg.as_string())

print("Email sent successfully.")

