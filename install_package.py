#!/usr/bin/env python3
"""
Installation script for DS-Inference-Scripts with Git-based shared utilities.
"""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description, check=True):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
        else:
            print(f"❌ {description} failed")
            if result.stderr.strip():
                print(f"   Error: {result.stderr.strip()}")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 9:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Requires Python 3.9+")
        return False


def install_shared_utils():
    """Install the shared utilities package from Git."""
    print("\n📦 Installing shared-utils package from Git...")

    git_url = "git+https://github.com/EqubotAI/DS_Utils.git"

    print(f"Attempting to install shared-utils from: {git_url}")

    # Try to install shared-utils directly from Git
    success = run_command(f"pip install {git_url}", "Installing shared-utils from Git", check=False)

    if success:
        print("✅ shared-utils installed successfully from Git!")
    else:
        print("⚠️  Git repository is not yet configured as installable package")
        print("   The repository needs a setup.py file to be pip-installable")
        print("   See GIT_REPOSITORY_SETUP_GUIDE.md for setup instructions")
        print("\n💡 Manual installation option:")
        print("   1. git clone https://github.com/EqubotAI/DS_Utils.git")
        print("   2. cd DS_Utils")
        print("   3. Add setup.py file (see setup guide)")
        print("   4. pip install -e .")

    # Install other dependencies
    print("\n📋 Installing other project dependencies...")
    deps_success = run_command("pip install -r requirements.txt", "Installing project dependencies")

    return deps_success


def install_project():
    """Install the main project in development mode."""
    print("\n📦 Installing DS-Inference-Scripts project...")

    # Check if we're in the right directory
    if not os.path.exists('setup.py'):
        print("❌ setup.py not found. Please run this script from the project root directory.")
        return False

    # Install in development mode
    success = run_command("pip install -e .", "Installing project in development mode")

    if success:
        print("✅ Project installed successfully!")
        print("   All model scripts are now available as a package.")

    return success


def test_installation():
    """Test the installation."""
    print("\n🧪 Testing installation...")

    try:
        # Test shared_utils import
        print("Testing shared_utils import...")
        try:
            import shared_utils
            print(f"✅ shared_utils imported successfully")

            # Test specific imports
            from shared_utils import (
                load_config, create_model_logger, create_email_sender,
                create_s3_manager, create_error_handler, ErrorSeverity
            )
            print("✅ All main functions imported successfully")

        except ImportError as e:
            print(f"⚠️  shared_utils not available: {e}")
            print("   The Git repository needs setup.py to be installable")
            print("   See GIT_REPOSITORY_SETUP_GUIDE.md for instructions")

        # Test project imports
        print("Testing project imports...")
        try:
            from best_model_scripts.main import BestModelRunner
            print("✅ Best model scripts importable")
        except ImportError as e:
            print(f"⚠️  Some model scripts may have import issues: {e}")

        return True

    except Exception as e:
        print(f"❌ Testing failed: {e}")
        return False


def create_test_script():
    """Create a test script to verify the package works."""
    test_script_content = '''#!/usr/bin/env python3
"""
Test script to verify DS-Inference-Utils package functionality.
"""

def test_package_functionality():
    """Test basic package functionality."""
    print("🧪 Testing DS-Inference-Utils package functionality...")
    
    try:
        # Test configuration utilities
        from shared_utils import load_config, ConfigManager
        print("✅ Configuration utilities imported successfully")
        
        # Test logging utilities
        from shared_utils import create_model_logger, create_logger
        logger = create_logger('test_logger')
        print("✅ Logging utilities working")
        
        # Test error handling
        from shared_utils import ErrorSeverity, ModelError, create_error_handler
        error_handler = create_error_handler(logger.get_logger())
        print("✅ Error handling utilities working")
        
        # Test email utilities (basic)
        from shared_utils import create_email_sender
        print("✅ Email utilities imported successfully")
        
        # Test S3 utilities (basic)
        from shared_utils import create_s3_manager
        print("✅ S3 utilities imported successfully")
        
        print("\\n🎉 All package components are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Package test failed: {e}")
        return False

if __name__ == '__main__':
    success = test_package_functionality()
    exit(0 if success else 1)
'''
    
    with open('test_package.py', 'w') as f:
        f.write(test_script_content)
    
    print("✅ Created test_package.py script")


def show_usage_examples():
    """Show usage examples."""
    print("\n📚 Usage Examples:")
    print("""
# Basic usage in your scripts:
from shared_utils import (
    load_config, create_model_logger, create_email_sender,
    create_s3_manager, create_error_handler
)

# Load configuration
config = load_config('config.yaml')

# Create logger
logger = create_model_logger('my_model', 'aieq', 'daily', '2024-01-15')

# Create utilities
email_sender = create_email_sender(config.to_dict())
s3_manager = create_s3_manager()
error_handler = create_error_handler(logger.get_logger())

# Use in your model scripts
logger.log_model_start({'param': 'value'})
df = s3_manager.read_dataframe('bucket', 'file.csv')
email_sender.send_email('Subject', '<EMAIL>', 'Body')
logger.log_model_end(success=True)
""")


def main():
    """Main installation and testing function."""
    print("🚀 DS-Inference-Scripts Installation with Git-based Shared Utils")
    print("=" * 70)

    # Check Python version
    if not check_python_version():
        return 1

    # Install shared utilities
    if not install_shared_utils():
        print("⚠️  Dependencies installed but shared-utils needs manual installation")

    # Install main project
    if not install_project():
        return 1

    # Test installation
    if not test_installation():
        print("⚠️  Installation completed but some tests failed. Check the setup.")

    # Create test script
    create_test_script()

    # Show usage examples
    show_usage_examples()

    print("\n🎉 Installation complete!")
    print("\nNext steps:")
    print("1. Set up Git repository with setup.py (see GIT_REPOSITORY_SETUP_GUIDE.md)")
    print("2. Once configured, install: pip install git+https://github.com/EqubotAI/DS_Utils.git")
    print("3. Run 'python test_git_package.py' to verify functionality")
    print("4. Test your model scripts")
    print("\nAll scripts are ready for clean imports from the shared-utils package!")

    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
